package com.xunw.jxjy.common.utils;

import com.google.gson.Gson;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import com.qiniu.util.UrlSafeBase64;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.config.QiniuConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.docx.DefaultContentType;
import com.xunw.jxjy.common.utils.docx.DocxReader;
import com.xunw.jxjy.common.utils.docx.ResourceInfo;
import net.sf.saxon.TransformerFactoryImpl;
import net.sourceforge.jeuclid.LayoutContext;
import net.sourceforge.jeuclid.context.LayoutContextImpl;
import net.sourceforge.jeuclid.context.Parameter;
import net.sourceforge.jeuclid.context.StyleAttributeLayoutContext;
import net.sourceforge.jeuclid.converter.Converter;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.awt.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class FileHelper {

	private static  final Logger LOGGER = LoggerFactory.getLogger(FileHelper.class);
	private static String outFileType = "image/png";
	private static String encoding = "utf-8";

	private static final String[] imgExt = new String[] {
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
				".tiff", ".tif", ".svg", ".ico", ".heic", ".cr2", ".nef"
	};

	private static LayoutContext defalutLc = null;
	static{
		LayoutContextImpl myLc = new LayoutContextImpl(LayoutContextImpl.getDefaultLayoutContext());
		myLc.setParameter(Parameter.SCRIPTLEVEL, -1);
		defalutLc = new StyleAttributeLayoutContext(myLc, "16pt", Color.BLACK);
	}
	
	public static void delFile(File file){
		try {
			if(file != null && file.exists()){
				FileUtils.forceDelete(file);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 写文件
	 * @param path
	 * @param content
	 * @return
	 */
	public static boolean doWriteFile(String path, String content){
		try {
			/**
			 * FileOutputStream out = new FileOutputStream(path);
			 * out.write(content == null ? "".getBytes() : content.getBytes());
			 * out.close();
			 **/
			FileOutputStream fos = new FileOutputStream(path);
			OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8");
			osw.write(content);
			osw.flush();
			osw.close();
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	 /**
     * 获取文件的后缀,返回的后缀中带有.号,返回示例:.doc
     */
    public static String getExtension(String fileName) {
        int k = fileName.lastIndexOf(".");
        String ext = "";
        if (k > 0) {
            ext = fileName.substring(k, fileName.length());
        }
        return ext;
    }
	
	/**
	 * 读取文件
	 * @param filePath
	 * @return
	 * @throws Exception
	 */
	public static String readFileByLines(String filePath) throws Exception {
		File file = new File(filePath);
		BufferedReader reader = null;
		StringBuffer sb = new StringBuffer();
		String linesepartor = System.getProperty("line.separator", "\n");
		try {
			reader = new BufferedReader(new FileReader(file));
			String tempString = null;
			while ((tempString = reader.readLine()) != null) {
				sb.append(tempString + linesepartor);
			}
			reader.close();
		} catch (IOException e) {
			throw new Exception("读取文件发生异常，文件可能不存在");
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e1) {
				}
			}
		}
		return sb.toString();
	}
	
	/**
	 * 创建一个临时文件（夹），文件（夹）名字是随机不重复的
	 * @return
	 */
	public static File createTmpFile(){
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String storepath = attConfig.getTempdir();//本地临时存储路径
		File dir= new File(storepath);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		return new File(dir,BaseUtil.generateId2());
	}
	
	/**
	 * 获取系统配置的文件存储临时目录
	 * @return
	 */
	public static File getTmpFolder(){
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String storepath = attConfig.getTempdir();
		File dir= new File(storepath);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		return dir;
	}

	/**
	 * 存储文件，并返回存储之后的链接地址。根据系统配置的文件存储方式来决定存储文件到哪个地方，
	 * 目前支持：CLOUD：七牛云；FILESYSTEM：文件系统
	 * @param basepath 		存储的相对文件目录路径，首尾都不能带有文件分隔符，且中间的文件分隔符必须为“/”,该路径必须使用系统配置的att.root-dir作为根路径
	 * @param inputStream 	流 	注意：上传结束之后流会被关闭
	 * @param fileName 		新的文件名称
	 * @return 可访问的存储之后的链接地址
	 * @throws IOException
	 */
	public static String storeFile(String basepath, InputStream inputStream, String fileName) throws IOException{
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		if(!basepath.startsWith(attConfig.getRootDir())){
			throw BizException.withMessage("文件存储的相对路径必须以"+attConfig.getRootDir()+"作为根路径");
		}
		try {
			String fullpath = null;
			String storeType = attConfig.getStoreType();//图片存储路径
			if("FILESYSTEM".equals(storeType)){
				/******* 参数定义 *******/
				String storepath = (String) attConfig.getStoreDir();//图片存储路径

				/******* 按日期创建文件夹 ********/
				File dir= null;
				if (!(dir= new File(storepath,basepath)).exists()) {
					dir.mkdirs();
				}

				/******* 存储文件 ********/
				File uploadedFile = new File(dir, fileName);
				FileCopyUtils.copy(inputStream, new FileOutputStream(uploadedFile));

				/******* 输出结果 ********/
				String prefixUrl = attConfig.getUrlPrefix();
				if(!prefixUrl.endsWith("/")){
					prefixUrl = prefixUrl + "/";
				}
				fullpath = prefixUrl + basepath + "/" + fileName;
			}else if("CLOUD".equals(storeType)){
				/****************上传到七牛服务器上****************/
				String key = basepath + "/" + fileName;
				Configuration cfg = new Configuration(Zone.zone0());//华东
				UploadManager uploadManager = new UploadManager(cfg);
				QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
				Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
				String upToken = auth.uploadToken(qiniuConfig.getBucket());
				Response response = uploadManager.put(inputStream, key, upToken, null, null);
				//解析上传成功的结果
				DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
				String entry = qiniuConfig.getBucket() + ":" + key;
				HttpURLConnection conn = null;
				try {//设置文件为低频存储
					String encodedEntryURI = UrlSafeBase64.encodeToString(entry);
					String host = "http://rs.qiniu.com";
					String url = "/chtype/" + encodedEntryURI + "/type/1";
					String authorization = (String)auth.authorization(url, null, "application/x-www-form-urlencoded").get("Authorization");
					URL u = new URL(host + url);
					conn = (HttpURLConnection) u.openConnection();
					conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
					conn.setRequestProperty("Authorization", authorization);
					conn.setConnectTimeout(180000);
					conn.setReadTimeout(180000);
					conn.setDoInput(true);
					// 设置请求方式，默认为GET
					conn.setRequestMethod("GET");

					InputStream is = conn.getInputStream();
					String returnStr = IOUtils.toString(is, "UTF-8");
					if(BaseUtil.isNotEmpty(returnStr)){
						LOGGER.info("returnStr=" + returnStr);
					}
					BaseUtil.close(is);
				} catch (Exception e) {
					LOGGER.info("把[" + entry + "]设为低频存储失败，原因：" , e);
					if(conn != null){
						try {
							InputStream is = conn.getErrorStream();
							String returnStr = IOUtils.toString(is, "UTF-8");
							LOGGER.info("returnStr=" + returnStr);
							BaseUtil.close(is);
						} catch (Exception e2) {
							LOGGER.error("解析接口返回结果失败", e2);
							e2.printStackTrace();
						}
					}
				}
				fullpath = qiniuConfig.getUrlprefix() + putRet.key;
			}else{
				throw new BizException("未正确配置att.store-type");
			}
			return fullpath;
		}
		finally {
			if(inputStream!=null){
				IOUtils.closeQuietly(inputStream);
			}
		}
	}
	
	/**
	 * 存储文件，并返回存储之后的链接地址。根据系统配置的文件存储方式来决定存储文件到哪个地方，
	 * 目前支持：CLOUD：七牛云；FILESYSTEM：文件系统
	 * @param basepath 	存储的相对文件目录路径，首尾都不能带有文件分隔符，且中间的文件分隔符必须为“/”,该路径必须使用系统配置的att.root-dir作为根路径
	 * @param bytes 要存储的文件内容
	 * @param fileName 新的文件名称
	 * @return 可访问的存储之后的链接地址
	 * @throws IOException
	 */
	public static String storeFile(String basepath, byte[] bytes, String fileName) throws IOException{
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		if(!basepath.startsWith(attConfig.getRootDir())){
			throw BizException.withMessage("文件存储的相对路径必须以"+attConfig.getRootDir()+"作为根路径");
		}
		String fullpath = null;
		String storeType = attConfig.getStoreType();//图片存储路径
		if("FILESYSTEM".equals(storeType)){
			/******* 参数定义 *******/
			String storepath = (String) attConfig.getStoreDir();//图片存储路径
			
			/******* 按日期创建文件夹 ********/
			File dir= null;
			if (!(dir= new File(storepath,basepath)).exists()) {
				dir.mkdirs();
			}
			
			/******* 存储文件 ********/
			File uploadedFile = new File(dir, fileName);
			FileCopyUtils.copy(bytes, uploadedFile);
				
			/******* 输出结果 ********/
			String prefixUrl = attConfig.getUrlPrefix();
			if(!prefixUrl.endsWith("/")){
				prefixUrl = prefixUrl + "/";
			}
			fullpath = prefixUrl + basepath + "/" + fileName;
		}else if("CLOUD".equals(storeType)){
			/****************上传到七牛服务器上****************/
			String key = basepath + "/" + fileName;
//			LOGGER.info("--开始上传文件：" + fileName + "到七牛存储...");
//			LOGGER.info("--七牛存储路径：" + key);
			
			Configuration cfg = new Configuration(Zone.zone0());//华东
			UploadManager uploadManager = new UploadManager(cfg);
			QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
			Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
			String upToken = auth.uploadToken(qiniuConfig.getBucket());
			Response response = uploadManager.put(bytes, key, upToken);
			System.out.println("----upload response:"+response.bodyString());
		    //解析上传成功的结果
		    DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        	String entry = qiniuConfig.getBucket() + ":" + key;
        	HttpURLConnection conn = null;
		    try {//设置文件为低频存储
	        	String encodedEntryURI = UrlSafeBase64.encodeToString(entry);
	        	String host = "http://rs.qiniu.com";
	        	String url = "/chtype/" + encodedEntryURI + "/type/1";
	        	String authorization = (String)auth.authorization(url, null, "application/x-www-form-urlencoded").get("Authorization");
	        	URL u = new URL(host + url);
	        	conn = (HttpURLConnection) u.openConnection();
	    		conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
	    		conn.setRequestProperty("Authorization", authorization);
	    		conn.setConnectTimeout(180000);
	    		conn.setReadTimeout(180000);
	    		conn.setDoInput(true);
	    		// 设置请求方式，默认为GET
	    		conn.setRequestMethod("GET");

	    		InputStream is = conn.getInputStream();
	    		String returnStr = IOUtils.toString(is, "UTF-8");
	    		if(BaseUtil.isNotEmpty(returnStr)){
	    			LOGGER.info("returnStr=" + returnStr);
	    		}
	    		BaseUtil.close(is);
			} catch (Exception e) {
				LOGGER.info("把[" + entry + "]设为低频存储失败，原因：" , e);
				if(conn != null){
					try {
						InputStream is = conn.getErrorStream();
			    		String returnStr = IOUtils.toString(is, "UTF-8");
			    		LOGGER.info("returnStr=" + returnStr);
			    		BaseUtil.close(is);
					} catch (Exception e2) {
						LOGGER.error("解析接口返回结果失败", e2);
						e2.printStackTrace();
					}
				}
			}
		    fullpath = qiniuConfig.getUrlprefix() + putRet.key;
		}else{
			throw new BizException("未正确配置att.store-type");
		}
		return fullpath;
	}
	
	public static void delFileFromQiniu(String bucket, String key) {
		QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
		Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
		String entry = bucket + ":" + key;
    	HttpURLConnection conn = null;
	    try {//设置文件为低频存储
        	String encodedEntryURI = UrlSafeBase64.encodeToString(entry);
        	String host = "http://rs.qiniu.com";
        	String url = "/delete/" + encodedEntryURI;
        	String authorization = (String)auth.authorization(url, null, "application/x-www-form-urlencoded").get("Authorization");
        	URL u = new URL(host + url);
        	conn = (HttpURLConnection) u.openConnection();
    		conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
    		conn.setRequestProperty("Authorization", authorization);
    		conn.setConnectTimeout(5000);
    		conn.setReadTimeout(5000);
    		conn.setDoInput(true);
    		// 设置请求方式，默认为GET
    		conn.setRequestMethod("GET");

    		InputStream is = conn.getInputStream();
    		String returnStr = IOUtils.toString(is, "UTF-8");
    		if(BaseUtil.isNotEmpty(returnStr)) {
    			LOGGER.info("returnStr=" + returnStr);
    		}
    		BaseUtil.close(is);
		} catch (Exception e) {
			LOGGER.error("delFileFromQiniu error", e);
			String failedReturnStr = null;
			if(conn != null){
				try {
					InputStream is = conn.getErrorStream();
					failedReturnStr = IOUtils.toString(is, "UTF-8");
		    		LOGGER.info("failedReturnStr=" + failedReturnStr);
		    		BaseUtil.close(is);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
			}
			throw new BizException("删除七牛云上面的文件失败[bucket='" + bucket + "'；key='" + key + "']，原因：" + (BaseUtil.isNotEmpty(failedReturnStr) ? failedReturnStr : e.getMessage()));
		}
	}

	/**
	 *	田军 于 2020年 0306 修改
	 * 	图片Src中base64格式的字符串转换成可以直接访问的URL，图片存储在系统配置的存储方式中。
	 * @param  base64ImgSrcStr 只能够是Base64格式的Src字符串,不能够包含其他HTML字符
	 */
	public static String convertImgBase64SrcStrToUrl(String imgBase64SrcStr) throws Exception {
		String newHtml = imgBase64SrcStr;
		String base64_flag = ";base64,";
		String dataimg_flag = "data:image/";
		DateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String basepath = attConfig.getRootDir()+"/upload/editor/" + sdf.format(System.currentTimeMillis());
		int index0 = newHtml.indexOf(base64_flag);
		if(index0 < 0){
			throw BizException.withMessage("传入的图片不是base64图片");
		}
		String strLeft = newHtml.substring(0, index0);
		int index1 = strLeft.lastIndexOf(dataimg_flag);
		String fileType = strLeft.substring(index1 + dataimg_flag.length());
		//判断文件类型是否是图片格式
        if (Arrays.stream(imgExt).noneMatch(ext -> ext.equals("."+fileType))) {
			throw BizException.withMessage("传入的文件不是图片类型，暂不支持");
        }
		String fileName =  BaseUtil.generateId2() + "." + fileType;
		byte[] bytes = Base64Img.base64StrToByteArray(imgBase64SrcStr);
		String url = FileHelper.storeFile(basepath, bytes, fileName);
		return url;
	}

	/**
	 * 把html代码里面base64位img内容转换成普通img url，base64位内容转存到系统配置的存储里面
	 * @param htmlStr
	 * @return
	 * @throws IOException 
	 */
	public static String convertQuestionBase64ImgToFile(String htmlStr) throws IOException {
		String newHtml = htmlStr;
		String base64_flag = ";base64,";
		String dataimg_flag = "data:image/";
		DateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String basepath = "upload/editor/" + sdf.format(System.currentTimeMillis());
		while(newHtml.contains(base64_flag)) {
			int index0 = newHtml.indexOf(base64_flag);
			String strLeft = newHtml.substring(0, index0);
			String strRight = newHtml.substring(index0 + base64_flag.length());
			int index1 = strLeft.lastIndexOf(dataimg_flag);
			String fileType = strLeft.substring(index1 + dataimg_flag.length());
			strLeft = strLeft.substring(0, index1);
			
			int index2 = strRight.indexOf('"');
			String base64Str = strRight.substring(0, index2);
			strRight = strRight.substring(index2);
			
			byte[] b = Base64.decodeBase64(base64Str);
			String new_filename = BaseUtil.generateId2() + "." + fileType;
			String fullpath = storeFile(basepath, b, new_filename);
			
			newHtml = strLeft + fullpath + strRight;
		}
		return newHtml;
	
	}
	
	public static String dealWord(byte[] word, DocxReader paperDocxReader, Map<String, ResourceInfo> needToUpdate)
            throws IOException, DocumentException {
        String content = "";
        File newDocx = null;
        DocxReader xtReader = null;
        try {
            {// 把内存的数据写入到临时文件里面，方便后面的逻辑使用
                FileOutputStream fos = null;
                try {
                    newDocx = FileHelper.createTmpFile();
                    fos = new FileOutputStream(newDocx);
                    IOUtils.write(word, fos);
                    fos.flush();
                } finally {
                	BaseUtil.close(fos);
                }
            }
            xtReader = new DocxReader(newDocx);
            xtReader.doRead();

            {// 处理DefaultContentType
                Map<String, DefaultContentType> xtDctMap = xtReader.getDefaultContentTypeMap();
                Map<String, DefaultContentType> paperDctMap = paperDocxReader.getDefaultContentTypeMap();

                SAXReader sax = new SAXReader();
                Document xmlDoc = sax.read(paperDocxReader.getContentTypesInfo().getFile());
                Element root = xmlDoc.getRootElement();// 根节点
                boolean hasDiff = false;
                for (Map.Entry<String, DefaultContentType> dct : xtDctMap.entrySet()) {
                    if (!paperDctMap.containsKey(dct.getKey())) {
                        hasDiff = true;
                        Element newDctEle = root.addElement("Default");
                        newDctEle.addAttribute("ContentType", dct.getValue().getContentType());
                        newDctEle.addAttribute("Extension", dct.getValue().getExtension());
                        paperDctMap.put(dct.getKey(), dct.getValue());
                    }
                }
                if (hasDiff) {
                    OutputFormat format = OutputFormat.createCompactFormat();
                    format.setEncoding("UTF-8");
                    XMLWriter writer = new XMLWriter(new FileWriter(paperDocxReader.getContentTypesInfo().getFile()),
                            format);
                    writer.write(xmlDoc); // 输出到文件
                    writer.close();
                }
                needToUpdate.put(paperDocxReader.getContentTypesInfo().getPathInZip(),
                        paperDocxReader.getContentTypesInfo());
            }

            String docStr = null;
            Set<String> embedIdSet = new HashSet<String>();
            {// 处理document.xml里面的内容
                File document = xtReader.getDocumentInfo().getFile();
                InputStream is = null;
                try {
                    is = new FileInputStream(document);
                    docStr = IOUtils.toString(is, "UTF-8");
                    {
	                    Pattern pattern = Pattern.compile("r:embed=\"(.+?)\"");
	        			Matcher matcher = pattern.matcher(docStr);
	        			while(matcher.find()){
	        				embedIdSet.add(matcher.group(1));
	        			}
                    }

                    {
	                    Pattern pattern = Pattern.compile("r:id=\"(.+?)\"");
	        			Matcher matcher = pattern.matcher(docStr);
	        			while(matcher.find()){
	        				embedIdSet.add(matcher.group(1));
	        			}
                    }
                }finally{
                	BaseUtil.close(is);
                }
            }
            
            Map<String, String> imgIdMap = new HashMap<String, String>();
            {// 处理图片资源
                List<ResourceInfo> imgs = xtReader.getImgs();
                if (imgs != null && imgs.size() > 0) {
                    SAXReader sax = new SAXReader();
                    Document xmlDoc = sax.read(paperDocxReader.getRelsInfo().getFile());
                    Element root = xmlDoc.getRootElement();// 根节点
                    for (ResourceInfo img : imgs) {
                    	if(!embedIdSet.contains(img.getId())){
                    		continue;
                    	}
                        ResourceInfo newImg = ResourceInfo.copyResourceInfo(img, paperDocxReader.getWordDir());
                        imgIdMap.put(img.getId(), newImg.getId());
                        Element newDctEle = root.addElement("Relationship");
                        newDctEle.addAttribute("Id", newImg.getId());
                        newDctEle.addAttribute("Type", newImg.getType());
                        newDctEle.addAttribute("Target", newImg.getTarget());
                        needToUpdate.put(newImg.getPathInZip(), newImg);
                    }
                    OutputFormat format = OutputFormat.createCompactFormat();
                    format.setEncoding("UTF-8");
                    XMLWriter writer = new XMLWriter(new FileWriter(paperDocxReader.getRelsInfo().getFile()), format);
                    writer.write(xmlDoc); // 输出到文件
                    writer.close();
                    needToUpdate.put(paperDocxReader.getRelsInfo().getPathInZip(), paperDocxReader.getRelsInfo());
                }
            }
            {// 处理document.xml里面的内容
            	int sectPrIndex = docStr.indexOf("<w:sectPr");
                if(sectPrIndex < 0){
                	sectPrIndex = docStr.indexOf("</w:body>");
                }
                content = docStr.substring((docStr.indexOf("<w:body>") + "<w:body>".length()),sectPrIndex);
                for (Map.Entry<String, String> entry : imgIdMap.entrySet()) {
                    content = content.replace("r:embed=\"" + entry.getKey() + "\"", "r:embed=\"" + entry.getValue()
                            + "\"").replace("r:id=\"" + entry.getKey() + "\"", "r:id=\"" + entry.getValue()
                                    + "\"");
                }
            }
        } finally {
            FileHelper.delFile(newDocx);
            DocxReader.release(xtReader);
        }
        return content;
    }
	
	/**
	 * 把图片文件存储在word目录下面，返回对应的可访问URL
	 * @param imgFile
	 * @param fileType 图片文件类型，直接用来命名后缀名
	 * @return
	 * @throws IOException
	 */
	public static String getImageUrlForWord(File imgFile,String imgType) throws IOException {
		InputStream is = null;
		try {
			is = new FileInputStream(imgFile);
			DateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
			String basepath = attConfig.getRootDir()+"/upload/word/" + sdf.format(System.currentTimeMillis());
			byte[] b = IOUtils.toByteArray(is);
			String new_filename = BaseUtil.generateId2() + "." + imgType;
			String fullpath = FileHelper.storeFile(basepath, b, new_filename);
			return fullpath;
		} finally {
			BaseUtil.close(is);
		}
	}
	
	// 图片转化成base64字符串
	public static byte[] getImageByteArr(File imgFile) throws IOException {// 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
		InputStream is = null;
		try {
			is = new FileInputStream(imgFile);
			return Base64.encodeBase64(IOUtils.toByteArray(is));
		} finally {
			BaseUtil.close(is);
		}
	}
	
	/**
	 * 把MathML表达的公式打印成png，然后转成base64数据
	 * 
	 * @param mathMlStr
	 * @return
	 * @throws IOException
	 */
	public static byte[] convertToBase64Img(StringBuffer mathMlStr) throws IOException {
		File xmlFile = null;
		File imgFile = null;
		FileOutputStream xmlFos = null;
		try {
			xmlFile = new File(FileHelper.getTmpFolder(),BaseUtil.generateId2());
			imgFile = new File(FileHelper.getTmpFolder(),BaseUtil.generateId2());
			xmlFos = new FileOutputStream(xmlFile);
			IOUtils.write(mathMlStr, xmlFos, encoding);
			xmlFos.flush();
			Converter.getInstance().convert(xmlFile, imgFile, outFileType, defalutLc);
			return getImageByteArr(imgFile);
		} finally {
			BaseUtil.close(xmlFos);
			FileHelper.delFile(xmlFile);
			FileHelper.delFile(imgFile);
		}
	}
	
	public static StringBuffer omml2mml(Element oMath){
		String omml = oMath.asXML();
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		InputStream stylesheet = BaseUtil.class.getResourceAsStream("/docxTmps/OMML2MML.XSL");
		StringReader sr = new StringReader(omml);
		InputSource ommldata = new InputSource(sr);
		DocumentBuilder builder = null;
		StringWriter writer = new StringWriter();
		try {
			builder = factory.newDocumentBuilder();
			org.w3c.dom.Document document = builder.parse(ommldata);
			// Use a Transformer for output
			TransformerFactory tFactory = new TransformerFactoryImpl();
			StreamSource stylesource = new StreamSource(stylesheet);
			Transformer transformer = null;
			transformer = tFactory.newTransformer(stylesource);
			DOMSource source = new DOMSource(document);
			StreamResult result = new StreamResult(writer);
			transformer.transform(source, result);
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			BaseUtil.close(sr);
			BaseUtil.close(writer);
		}
		String mml = writer.toString();
		if(BaseUtil.isNotEmpty(mml)){
			mml = ("<mml:math" + StringUtils.substringAfter(mml, "<mml:math")).replace("mml:", "");
			System.out.println("mml:" + mml);
		}
		return new StringBuffer(mml);
	}
	
}
