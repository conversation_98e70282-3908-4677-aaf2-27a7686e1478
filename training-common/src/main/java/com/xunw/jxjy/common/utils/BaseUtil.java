package com.xunw.jxjy.common.utils;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.tools.imageio.ImageIOUtil;

import com.alibaba.druid.sql.parser.ParserException;
import com.xunw.jxjy.common.core.ScoreStandard;
import com.xunw.jxjy.common.exception.BizException;

import jxl.Cell;
import jxl.CellType;
import jxl.DateCell;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class BaseUtil {

	private static final Logger logger = Logger.getLogger(BaseUtil.class);

	public static HanyuPinyinOutputFormat hanyuPinyinOutputFormat = new HanyuPinyinOutputFormat();
	static {
		hanyuPinyinOutputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		hanyuPinyinOutputFormat.setVCharType(HanyuPinyinVCharType.WITH_U_UNICODE);
		hanyuPinyinOutputFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
	}

	/**
	 * 是否NULL
	 *
	 * @param obj
	 * @return
	 */
	public static boolean isNull(Object obj) {
		return obj == null;
	}

	/**
	 * 是否NULL或者空
	 *
	 * @param s
	 * @return
	 */
	public static boolean isEmpty(String s) {
		return (s == null || s.trim().length() < 1 || "null".equalsIgnoreCase(s) || "undefined".equalsIgnoreCase(s));
	}

	public static boolean isEmpty(Object o) {
		return o == null || isEmpty(String.valueOf(o));
	}

	/**
	 * 不为NULL或空
	 *
	 * @param s
	 * @return
	 */
	public static boolean isNotEmpty(String s) {
		return !isEmpty(s);
	}

	public static boolean isNotEmpty(Object o) {
		return o != null && !isEmpty(String.valueOf(o));
	}

	public static String trimBlank(String str) {
		if (str == null) {
			return null;
		}
		return str.trim().replace((char) 160 + "", "").replace((char) 194 + "", "");
	}

	/**
	 * 判断18位身份证号的性别,M 男 F: 女
	 */
	public static String parseGender(String cid) {
		try {
			if (StringUtils.isEmpty(cid)) {
				return null;
			}
			if (cid.length() != 18) {
				return null;
			}
			String gender = null;
			char c = cid.charAt(cid.length() - 2);
			int sex = Integer.parseInt(String.valueOf(c));
			if (sex % 2 == 0) {
				gender = "F";
			} else {
				gender = "M";
			}
			return gender;
		} catch (NumberFormatException e) {
			return null;
		}
	}

	/**
	 * 获取文件的后缀，不带.号，比如:doc
	 */
	public static String getFileExtension(String fileName) {
		int k = fileName.lastIndexOf(".");
		String ext = "";
		if (k > 0) {
			ext = fileName.substring(k + 1, fileName.length());
		}
		return ext;
	}

	/**
	 * 从json对象里面取出某个key对应的字符串数组
	 *
	 * @param json
	 * @param key
	 * @return
	 */
	public static String[] getValuesFromJson(JSONObject json, String key) {
		if (!json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		if (valueObj instanceof JSONArray) {
			JSONArray valueArr = (JSONArray) valueObj;
			String[] values = new String[valueArr.size()];
			for (int i = 0; i < values.length; i++) {
				values[i] = valueArr.getString(i);
			}
			return values;
		} else {
			return new String[] { (String) valueObj };
		}
	}

	/**
	 * 返回HashMap中的string value,若为空，则返回null
	 */
	public static String getStringValueFromMap(Map<String, ?> map, String key) {
		if (map == null || StringUtils.isEmpty(key)) {
			return null;
		} else {
			Object object = map.get(key);
			return object != null ? object.toString() : null;
		}
	}

	/**
	 * 返回HashMap中的string value,若为空，则返回指定的默认值
	 */
	public static String getStringValueFromMap(Map<String, ?> map, String key, String defaultValue) {
		if (map == null || StringUtils.isEmpty(key)) {
			return defaultValue;
		} else {
			Object object = map.get(key);
			return object != null ? object.toString() : defaultValue;
		}
	}

	/**
	 * 返回HashMap中的int value,若为空，则返回null
	 */
	public static Integer getIntValueFromMap(Map<String, ?> map, String key) {
		if (map == null || StringUtils.isEmpty(key)) {
			return null;
		} else {
			Object object = map.get(key);
			return object != null ? Integer.valueOf(object.toString()) : null;
		}
	}

	/**
	 * 返回HashMap中的int value,若为空，则返回指定的默认值
	 */
	public static Integer getIntValueFromMap(Map<String, ?> map, String key, int defaultValue) {
		if (map == null || StringUtils.isEmpty(key)) {
			return defaultValue;
		} else {
			Object object = map.get(key);
			return object != null ? Integer.valueOf(object.toString()) : defaultValue;
		}
	}

	/**
	 * 返回HashMap中的float value,若为空，则返回null
	 */
	public static Float getFloatValueFromMap(Map<String, ?> map, String key) {
		if (map == null || StringUtils.isEmpty(key)) {
			return null;
		} else {
			Object object = map.get(key);
			return object != null ? Float.valueOf(object.toString()) : null;
		}
	}

	/**
	 * 返回HashMap中的float value,若为空，则返回指定的默认值
	 */
	public static Float getFloatValueFromMap(Map<String, ?> map, String key, float defaultValue) {
		if (map == null || StringUtils.isEmpty(key)) {
			return defaultValue;
		} else {
			Object object = map.get(key);
			return object != null ? Float.valueOf(object.toString()) : defaultValue;
		}
	}

	/**
	 * 返回HashMap中的double value,若为空，则返回null
	 */
	public static Double getDoubleValueFromMap(Map<String, ?> map, String key) {
		if (map == null || StringUtils.isEmpty(key)) {
			return null;
		} else {
			Object object = map.get(key);
			return object != null ? Double.valueOf(object.toString()) : null;
		}
	}

	/**
	 * 返回HashMap中的double value,若为空，则返回指定的默认值
	 */
	public static Double getDoubleValueFromMap(Map<String, ?> map, String key, double defaultValue) {
		if (map == null || StringUtils.isEmpty(key)) {
			return defaultValue;
		} else {
			Object object = map.get(key);
			return object != null ? Double.valueOf(object.toString()) : defaultValue;
		}
	}

	/**
	 * 返回HashMap中的date value,若为空，则返回null
	 */
	public static Date getDateValueFromMap(Map<String, ?> map, String key) {
		if (map == null || StringUtils.isEmpty(key)) {
			return null;
		} else {
			Object object = map.get(key);
			return object != null ? (Date) object : null;
		}
	}

	/**
	 * 返回HashMap中的date value,若为空，则返回指定的默认值
	 */
	public static Date getDateValueFromMap(Map<String, ?> map, String key, Date defaultValue) {
		if (map == null || StringUtils.isEmpty(key)) {
			return defaultValue;
		} else {
			Object object = map.get(key);
			return object != null ? (Date) object : defaultValue;
		}
	}

	/**
	 * 返回JSON中的string value,若为空，则返回null
	 */
	public static String getStringValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? valueObj.toString() : null;
	}

	/**
	 * 返回JSON中的string value,若为空，则返回指定的默认值
	 */
	public static String getStringValueFromJson(JSONObject json, String key, String defaultValue) {
		if (json == null || !json.containsKey(key)) {
			return defaultValue;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? valueObj.toString() : defaultValue;
	}

	/**
	 * 返回JSON中的double value,若为空，则返回null
	 */
	public static Double getDoubleValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return BaseUtil.isNotEmpty(valueObj) ? Double.valueOf(valueObj.toString()) : null;
	}

	/**
	 * 返回JSON中的double value,若为空，则返回指定的默认值
	 */
	public static Double getDoubleValueFromJson(JSONObject json, String key, double defaultValue) {
		if (json == null || !json.containsKey(key)) {
			return defaultValue;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? Double.valueOf(valueObj.toString()) : defaultValue;
	}

	/**
	 * 返回JSON中的float value,若为空，则返回null
	 */
	public static Float getFloatValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? Float.valueOf(valueObj.toString()) : null;
	}

	/**
	 * 返回JSON中的float value,若为空，则返回指定的默认值
	 */
	public static Float getFloatValueFromJson(JSONObject json, String key, float defaultValue) {
		if (json == null || !json.containsKey(key)) {
			return defaultValue;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? Float.valueOf(valueObj.toString()) : defaultValue;
	}

	/**
	 * 返回JSON中的int value,若为空，则返回null
	 */
	public static Integer getIntegerValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return BaseUtil.isNotEmpty(valueObj) ? Integer.valueOf(valueObj.toString()) : null;
	}

	/**
	 * 返回JSON中的float value,若为空，则返回指定的默认值
	 */
	public static Integer getIntegerValueFromJson(JSONObject json, String key, int defaultValue) {
		if (json == null || !json.containsKey(key)) {
			return defaultValue;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? Integer.valueOf(valueObj.toString()) : defaultValue;
	}

	/**
	 * 采用JSON的数据结构返回JSON对象中指定的key值
	 */
	public static JSONObject getJSONValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		JSONObject valueObj = json.getJSONObject(key);
		return valueObj != null ? valueObj : null;
	}

	/**
	 * 返回JSON中的date value,若为空，则返回null
	 */
	public static Date getDateValueFromJson(JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? (Date) valueObj : null;
	}

	/**
	 * 返回JSON中的double value,若为空，则返回指定的默认值
	 */
	public static Date getDateValueFromJson(JSONObject json, String key, Date defaultValue) {
		if (json == null || !json.containsKey(key)) {
			return defaultValue;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? (Date) valueObj : defaultValue;
	}

	/**
	 * 获取整数，如果转换失败，返回：-9999
	 *
	 * @param s
	 * @return
	 */
	public static int getInt(Object s) {
		return getInt(s, -9999);
	}

	/**
	 * 获取整数，如果转换失败，返回：-9999
	 *
	 * @param s
	 * @return
	 */
	public static int getInt(String s) {
		return getInt(s, -9999);
	}

	/**
	 * 获取整数
	 *
	 * @param s
	 * @return
	 */
	public static int getInt(Object s, int defaultValue) {
		Integer value = parseInteger(s);
		return value == null ? defaultValue : value;
	}

	/**
	 * 对象转换成数字，如果转换不成功，则返回null
	 *
	 * @param s
	 * @return
	 */
	public static Integer parseInteger(Object s) {
		if (s == null) {
			return null;
		}
		try {
			if (s instanceof Double) {
				return (int) ((Double) s).doubleValue();
			}
			if (s instanceof Float) {
				return (int) ((Float) s).floatValue();
			}
			if (s instanceof Long) {
				return (int) ((Long) s).longValue();
			}
			if (s instanceof Integer) {
				return (Integer) s;
			}
			if (s instanceof BigDecimal) {
				return ((BigDecimal) s).intValue();
			}
			if (!s.toString().matches("^(-?\\d+)(\\.\\d+)?$")) {
				return null;
			}
			return (new BigDecimal(s.toString())).intValue();
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 对象转换成长整型数值，如果转换不成功，则返回null
	 *
	 * @param s
	 * @return
	 */
	public static Long parseLong(Object s) {
		if (s == null) {
			return null;
		}
		try {
			if (s instanceof Double) {
				return (long) ((Double) s).doubleValue();
			}
			if (s instanceof Float) {
				return (long) ((Float) s).floatValue();
			}
			if (s instanceof Long) {
				return (Long) s;
			}
			if (s instanceof Integer) {
				return (long) ((Integer) s).intValue();
			}
			if (s instanceof BigDecimal) {
				return ((BigDecimal) s).longValue();
			}
			if (!s.toString().matches("^(-?\\d+)(\\.\\d+)?$")) {
				return null;
			}
			return (new BigDecimal(s.toString())).longValue();
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 对象转换成长整型数值，如果转换不成功，则返回默认值
	 *
	 * @param s
	 * @return
	 */
	public static long getLong(Object s, long defaultValue) {
		Long value = parseLong(s);
		return value == null ? defaultValue : value;
	}

	/**
	 * 对象转换成数字，如果转换不成功，则返回null
	 *
	 * @param s
	 * @return
	 */
	public static Double parseDouble(Object s) {
		if (s == null) {
			return null;
		}
		try {
			if (s instanceof Double) {
				return (Double) s;
			}
			if (s instanceof Float) {
				return ((Float) s).floatValue() + 0.0;
			}
			if (s instanceof Long) {
				return ((Long) s).longValue() + 0.0;
			}
			if (s instanceof Integer) {
				return ((Integer) s).intValue() + 0.0;
			}
			if (s instanceof BigDecimal) {
				return ((BigDecimal) s).doubleValue();
			}
			if (!s.toString().matches("^(-?\\d+)(\\.\\d+)?$")) {
				return null;
			}
			return (new BigDecimal(s.toString())).doubleValue();
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 对象转换成数字，如果转换不成功，则返回defaultValue
	 *
	 * @param s
	 * @return
	 */
	public static double parseDouble(Object s, double defaultValue) {
		if (s == null) {
			return defaultValue;
		}
		try {
			if (s instanceof Double) {
				return (Double) s;
			}
			if (s instanceof Float) {
				return ((Float) s).floatValue() + 0.0;
			}
			if (s instanceof Long) {
				return ((Long) s).longValue() + 0.0;
			}
			if (s instanceof Integer) {
				return ((Integer) s).intValue() + 0.0;
			}
			if (s instanceof BigDecimal) {
				return ((BigDecimal) s).doubleValue();
			}
			if (!s.toString().matches("^(-?\\d+)(\\.\\d+)?$")) {
				return defaultValue;
			}
			return (new BigDecimal(s.toString())).doubleValue();
		} catch (Exception e) {
			return defaultValue;
		}
	}

	/**
	 * NULL转换为空字符串
	 *
	 * @param s
	 * @return
	 */
	public static String convertNullToEmpty(Object s) {
		if (s != null && s instanceof Date) {
			return DateUtils.format((Date) s);
		}
		return s == null ? "" : String.valueOf(s);
	}

	/**
	 * NULL转换为空字符串
	 *
	 * @param s
	 * @return
	 */
	public static String convertNullToEmpty(String s) {
		return s == null ? "" : s;
	}

	/**
	 * 空转换为默认值
	 *
	 * @param s
	 * @return
	 */
	public static String convertEmptyToSome(String s, String t) {
		return isEmpty(s) ? t : s;
	}

	/**
	 * 空字符串转换为NULL
	 *
	 * @param s
	 * @return
	 */
	public static String converEmptyToNull(String s) {
		return "".equals(s) ? null : s;
	}

	/**
	 * 特殊字符处理
	 *
	 * @param str
	 * @return
	 */
	public static String characterConversion(String str) {
		if (str != null && !"".equals(str)) {
			str = str.replace("'", "&#39;");
			str = str.replace("\"", "&#34;");
			str = str.replace("<", "&lt;");
			str = str.replace(">", "&gt;");
		}
		return str;
	}

	/**
	 * 获取中文
	 *
	 * @param s
	 * @return
	 */
	public static String getChinese(String s) {
		try {
			return s;
//			return new String(s.getBytes("ISO8859-1"),"UTF-8");
		} catch (Exception e) {
			return null;
		}
	}

	public static String subString(String s, int len) {
		if (BaseUtil.isEmpty(s)) {
			return "";
		} else {
			if (s.length() >= len) {
				return s.substring(0, len);
			}
		}
		return s;
	}

	public static String subStringByFrom(String s, int from) {
		if (BaseUtil.isEmpty(s)) {
			return "";
		} else {
			try {
				return s.substring(from);
			} catch (Exception e) {
				return "";
			}
		}
	}

	/**
	 * 生成主键编号 带横杠"-"
	 *
	 * @return
	 */
	public static String generateId() {
		return UUID.randomUUID().toString();
	}

	/**
	 * 生成全大写的且中间没有横杠的主键编号
	 *
	 * @return
	 */
	public static String generateId2() {
		return BaseUtil.generateId().toUpperCase().replace("-", "");
	}

	/**
	 * 获得任意长度的随机字符串
	 *
	 * @param length
	 * @return
	 */
	public static String generateRandomString(int length) {
		String base = "abcdefghijklmnopqrstuvwxyz0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	/**
	 * 获得任意长度的英文随机字符串
	 */
	public static String generateRandomEnglishLetterString(int length) {
		String base = "abcdefghijklmnopqrstuvwxyz";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	public static String getClientIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("X-Real-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if(ip.contains(",")) {
			ip = ip.split(",")[0];
		}
		return ip;
	}

	/**
	 * 验证客户端ip是否是在允许的ip段内
	 *
	 * @param request
	 * @param ipRuleStr 限制的IP段，IP段的2个ip用“-”隔开，多个IP段中间用“|”隔开，如果为空，表示不限制，直接返回true
	 * @return
	 */
	public static boolean isIpAllow(HttpServletRequest request, String ipRuleStr) {
		return isIpAllow(getClientIpAddr(request), ipRuleStr);
	}

	/**
	 * 验证客户端ip是否是在允许的ip段内
	 *
	 * @param ipStr     客户端ip地址，可能出现中间用“,”隔开的IP地址，例如：**************,**************
	 * @param ipRuleStr 限制的IP段，IP段的2个ip用“-”隔开，多个IP段中间用“|”隔开，如果为空，表示不限制，直接返回true
	 * @return
	 */
	public static boolean isIpAllow(String ipStr, String ipRuleStr) {
		if (isEmpty(ipRuleStr)) {
			return true;
		}
		boolean isAllow = false;
		for (String ip : ipStr.split(",")) {
			ip = ip.trim();
			if ("0:0:0:0:0:0:0:1".equals(ip)) {
				ip = "127.0.0.1";
			}
			for (String ipSection : ipRuleStr.split("\\|")) {
				ipSection = ipSection.trim();
				int idx = ipSection.indexOf('-');
				String beginIP = ipSection.substring(0, idx);
				String endIP = ipSection.substring(idx + 1);
				isAllow = getIp2long(beginIP) <= getIp2long(ip) && getIp2long(ip) <= getIp2long(endIP);
				if (isAllow) {
					return isAllow;
				}
			}
		}
		return isAllow;
	}

	private static long getIp2long(String ip) {
		ip = ip.trim();
		String[] ips = ip.split("\\.");
		long ip2long = 0L;
		for (int i = 0; i < 4; ++i) {
			ip2long = ip2long << 8 | Integer.parseInt(ips[i]);
		}
		return ip2long;
	}

	/**
	 * 将数字转化为汉字的数组,因为各个实例都要使用所以设为静态
	 */
	private static final char[] cnUpperNumbers = { '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖' };

	/**
	 * 将数字转化为汉字的数组,因为各个实例都要使用所以设为静态
	 */
	private static final char[] cnLowerNumbers = { '零', '一', '二', '三', '四', '五', '六', '七', '八', '九' };

	/**
	 * 供分级转化的数组,因为各个实例都要使用所以设为静态
	 */
	private static final char[] series = { '十', '百', '千', '万', '十', '百', '千', '亿' };

	/**
	 * 阿拉伯数字转成大写中文的数字。
	 *
	 * @param num
	 * @return
	 */
	public static String toCNUpperNum(Integer num) {
		return toCNNum(num, cnUpperNumbers);
	}

	/**
	 * 阿拉伯数字转成小写中文的数字。
	 *
	 * @param num
	 * @return
	 */
	public static String toCNLowerNum(Integer num) {
		return toCNNum(num, cnLowerNumbers);
	}

	/**
	 * 阿拉伯数字转成中文的数字，不涉及金额数字的处理。TODO 有一些Bug，对100,1001类似的特殊数字没有处理好。
	 * 现在100以内的数字或者中间不带0的数字转换没有问题。 <b>*不支持负数</b>
	 *
	 * @param num 阿拉伯数据
	 * @return 中文数字
	 */
	private static String toCNNum(int num, char[] cnNumbers) {
		if (num == 0) {
			return cnNumbers[0] + "";
		}
		if (num < 0) {
			throw new RuntimeException("不支持负数");
		}
		// 成员变量初始化
		String integerPart = "" + num;

		// 因为是累加所以用StringBuffer
		StringBuffer sb = new StringBuffer();

		// 整数部分处理
		for (int i = 0; i < integerPart.length(); i++) {
			int number = getNumber(integerPart.charAt(i));

			sb.append(cnNumbers[number]);
			if (integerPart.length() - 2 - i >= 0) {
				sb.append(series[integerPart.length() - 2 - i]);
			}
		}
		String cnNum = sb.toString();
		if (cnNum.startsWith("一十")) {
			cnNum = cnNum.substring(1);
		}
		while (cnNum.endsWith("" + cnNumbers[0])) {
			cnNum = cnNum.substring(0, cnNum.length() - 1);
		}

		// 返回拼接好的字符串
		return cnNum;
	}

	/**
	 * 将字符形式的数字转化为整形数字
	 *
	 * @param c
	 * @return
	 */
	private static int getNumber(char c) {
		String str = String.valueOf(c);
		return Integer.parseInt(str);
	}

	/**
	 * 从excel中提取cell中的数字部分
	 *
	 * @param str
	 * @return
	 */
	public static String getNumFromCell(String str) {
		Pattern p = Pattern.compile("[\\d]+");
		Matcher m = p.matcher(str);
		if (m.find()) {
			return m.group();
		}
		return "";
	}

	/**
	 * 字符串的list转成字符串的逗号拼接形式
	 *
	 * @param list
	 * @return
	 */
	public static String getStringFromList(List<String> list) {
		String res = "";
		if (null == list || list.isEmpty()) {
			return "";
		} else {
			for (Object ob : list) {
				String str = convertNullToEmpty(ob);
				res += "," + str;
			}
		}
		if (res.contains(",")) {
			res = res.replaceFirst(",", "");
		}
		return res;
	}

	/**
	 * 把byte转化可识别的B、KB、MB、GB、TB
	 *
	 * @param size
	 * @return
	 */
	public static String getFileShowSize(long size) {
		StringBuffer bytes = new StringBuffer();
		DecimalFormat format = new DecimalFormat("###.#");
		if (size >= 1024 * 1024 * 1024 * 1024.0) {
			double i = (size / (1024.0 * 1024.0 * 1024.0 * 1024.0));
			bytes.append(format.format(i)).append("TB");
		} else if (size >= 1024 * 1024 * 1024) {
			double i = (size / (1024.0 * 1024.0 * 1024.0));
			bytes.append(format.format(i)).append("GB");
		} else if (size >= 1024 * 1024) {
			double i = (size / (1024.0 * 1024.0));
			bytes.append(format.format(i)).append("MB");
		} else if (size >= 1024) {
			double i = (size / (1024.0));
			bytes.append(format.format(i)).append("KB");
		} else if (size < 1024) {
			if (size <= 0) {
				bytes.append("0B");
			} else {
				bytes.append((int) size).append("B");
			}
		}
		return bytes.toString();
	}

	/**
	 * 准考证号验证
	 *
	 * @param zkz
	 * @return
	 */
	public static boolean testZkz(String zkz) {
		if (StringUtils.isEmpty(zkz)) {
			return false;
		}
		Pattern pattern = Pattern.compile("\\d{12}");
		Matcher matcher = pattern.matcher(zkz);
		if (matcher.find()) {
			return true;
		}
		return false;
	}

	/**
	 * 生成指定位数的数字随机码
	 *
	 * @param len
	 * @return
	 */
	public static String genCheckCode(int len) {
		String code = "";
		for (int i = 0; i < len; i++) {
			Random random = new Random();
			code = code + random.nextInt(10);
		}
		return code;
	}

	public static void c(Closeable clo) {
		if (clo != null) {
			try {
				clo.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public static void close(AutoCloseable clo) {
		if (clo != null) {
			try {
				clo.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public static String toUnicode(char c) {
		return "&#" + (c - 0) + ";";
	}

	public static String toUnicode(String s) {
		StringBuffer uc = new StringBuffer();
		for (int i = 0; i < s.length(); i++) {
			uc.append(toUnicode(s.charAt(i)));
		}
		return uc.toString();
	}

	public static String toJavaFormat(String wordFormat) {
		StringBuffer javaFormat = new StringBuffer();
		boolean flag = false;// 下一位字符是否是数字
		for (int i = 0; i < wordFormat.length(); i++) {
			char c = wordFormat.charAt(i);
			javaFormat.append(c);
			char nc = 0;// 下一个字符
			if (i < wordFormat.length() - 1) {
				nc = wordFormat.charAt(i + 1);
			}
			int cha = nc - '0';// 与字符'0'的差值
			if (c == '%') {
				if (cha >= 0 && cha <= 9) {
					flag = true;
				} else {
					javaFormat.append('%');
				}
			}
			if (flag && ((i == wordFormat.length() - 1) || (cha < 0 || cha > 9))) {
				javaFormat.append("$s");
				flag = false;
			}
		}
		return javaFormat.toString();
	}

	/**
	 * 把行、列的index值转成Excel坐标
	 *
	 * @param rowIndex 行序列值，从0开始计数
	 * @param colIndex 列序列值，从0开始计数
	 * @return
	 */
	public static String toExcelCoordinate(int rowIndex, int colIndex) {
		char A = 'A';
		int m = colIndex / 26;
		String priStr = m == 0 ? "" : (((char) (A + (m - 1))) + "");
		colIndex = colIndex % 26;
		String colStr = ((char) (A + colIndex)) + "";
		return priStr + colStr + (rowIndex + 1);
	}

	/**
	 * 把整数序列值转成大写字母序列A、B、C、D…… TODO 现仅支持到Z
	 *
	 * @param num 1、2、3、4……
	 * @return
	 */
	public static String toUpperLetter(Integer num) {
		char letter = 'A';
		letter += (num - 1);
		return String.valueOf(letter);
	}

	/**
	 * 把整数序列值转成小写字母序列a、b、c、d…… TODO 现仅支持到z
	 *
	 * @param num 1、2、3、4……
	 * @return
	 */
	public static String toLowerLetter(Integer num) {
		return toUpperLetter(num).toLowerCase();
	}

	private final static String rnums[] = { "m", "cm", "d", "cd", "c", "xc", "l", "xl", "x", "Mx", "v", "Mv", "M", "CM",
			"D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I" }; // 儲存所有羅馬數字
	private final static int anums[] = { 1000000, 900000, 500000, 400000, 100000, 90000, 50000, 40000, 10000, 9000,
			5000, 4000, 1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1 }; // 儲存羅馬數字表示的值

	/**
	 * 將整數轉成以字串表示的大写羅馬數字
	 *
	 * @param num
	 * @return
	 */
	public static String toUpperRoman(Integer num) {
		if (num == 0) { // 因為羅馬數字裡並沒有零，所以輸出ZERO！
			return "ZERO";
		} else if (num < 0 || num > 3999999) { // 溢位判斷
			return "OVERFLOW";
		}

		StringBuilder output = new StringBuilder(); // 儲存羅馬數字字串
		for (int i = 0; num > 0 && i < anums.length; i++) { // 尋找對應的羅馬數字
			while (num >= anums[i]) { // 將羅馬數字加到output物件內
				num -= anums[i];
				output.append(rnums[i]);
			}
		}
		return output.toString().toUpperCase(); // 傳回羅馬數字字串
	}

	/**
	 * 將整數轉成以字串表示的小写羅馬數字
	 *
	 * @param num
	 * @return
	 */
	public static String toLowerRoman(Integer num) {
		return toUpperRoman(num).toLowerCase();
	}

	public static boolean isRgbColor(String color) {
		color = color.toUpperCase();
		if (color.length() == 3 || color.length() == 6) {
			for (int i = 0; i < color.length(); i++) {
				char flag = color.charAt(i);
				if (!((flag >= '0' && flag <= '9') || (flag >= 'A' && flag <= 'F'))) {
					return false;
				}
			}
			return true;
		} else {
			return false;
		}
	}

	public static String getBorderWidth(String sz) {
		if (sz == null || sz.equals("")) {
			return "0pt";
		}
		int width = Integer.parseInt(sz);
		double pxW = width / 8.0;
		if (pxW < 1) {
			pxW = 1;
		}
		return pxW + "pt";
	}

	public static String getBorderStyle(String style) {
		if (style == null || (!style.equals("dotted") && !style.equals("dashed") && !style.equals("double"))) {
			style = "solid";
		}
		return style;
	}

	public static boolean toBoolean(Object obj, boolean defaultValue) {
		if (obj == null) {
			return defaultValue;
		}
		String v = obj.toString().toLowerCase();
		return !(v.equals("0") || v.equals("false") || v.equals("no") || v.equals("none") || v.equals("f")
				|| v.equals("n"));
	}

	/**
	 * 将单页PDF文件转换为图片 基于apache pdfbox工具实现
	 *
	 * @param pdf   PDF文件绝对路径
	 * @param image 图片文件绝对路径
	 */
	public static void singlePagePdf2Image(File pdf, File image) throws InvalidPasswordException, IOException {
		PDDocument document = PDDocument.load(pdf);
		PDFRenderer pdfRenderer = new PDFRenderer(document);
		BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(0, 100);
		ImageIOUtil.writeImage(bufferedImage, image.getAbsolutePath(), 100);
		document.close();
	}

	/**
	 * 获取身份证的出生日期
	 */
	public static String getBirthday(String IDCard) {
		String birthday = null;
		String year = "";
		String month = "";
		String day = "";
		if (StringUtils.isNotEmpty(IDCard)) {
			// 身份证上的年份
			year = IDCard.substring(6).substring(0, 4);
			// 身份证上的月份
			month = IDCard.substring(10).substring(0, 2);
			// 身份证上的日期
			day = IDCard.substring(12).substring(0, 2);
			birthday = year + "-" + month + "-" + day;
		}
		return birthday;
	}

	/**
	 * XML字符串编码，把转义字符转义成对应的字符串
	 *
	 * @param str
	 * @return
	 */
	public static String XMLEncode(String str) {
		if (str != null) {
			return str.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace("’", "&apos;")
					.replace("\"", "&quot;");
		} else {
			return "";
		}
	}

	/**
	 * 生成随机颜色
	 *
	 * @param fc
	 * @param bc
	 * @return
	 */
	public static Color getRandColor(int fc, int bc) {
		Random random = new Random();
		if (fc > 255)
			fc = 255;
		if (bc > 255)
			bc = 255;
		int r = fc + random.nextInt(bc - fc);
		int g = fc + random.nextInt(bc - fc);
		int b = fc + random.nextInt(bc - fc);
		return new Color(r, g, b);
	}

	/**
	 * 提取Html里面的文本信息，并把一般的转义字符串转换回来
	 *
	 * @param htmlStr
	 * @return
	 * @throws ParserException
	 */
	public static String extractText(String htmlStr) {
		if (isEmpty(htmlStr)) {
			return "";
		}
		if (htmlStr.toLowerCase().contains("<!doctype")) {
			int index1 = htmlStr.toLowerCase().indexOf("<!doctype");
			int index2 = htmlStr.indexOf('>', index1 + 1);
			htmlStr = htmlStr.substring(0, index1) + htmlStr.substring(index2 + 1);
		}
		String regEx_head = "<[\\s]*?head[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?head[\\s]*?>"; // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
																							// }
		String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
																									// }
		String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
																								// }
		String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式

		Pattern p_head = Pattern.compile(regEx_head, Pattern.CASE_INSENSITIVE);
		Matcher m_head = p_head.matcher(htmlStr);
		htmlStr = m_head.replaceAll(""); // 过滤script标签

		Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
		Matcher m_script = p_script.matcher(htmlStr);
		htmlStr = m_script.replaceAll(""); // 过滤script标签

		Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
		Matcher m_style = p_style.matcher(htmlStr);
		htmlStr = m_style.replaceAll(""); // 过滤style标签

		htmlStr = htmlStr.replace("<div", " <div").replace("<DIV", " <DIV").replace("<p", " <p").replace("<P", " <P")
				.replace("<h", " <h").replace("<H", " <H").replace("<br", " <br").replace("<BR", " <BR")
				.replace("<td", " <td").replace("<TD", " <TD").replace("<th", " <th").replace("<TH", " <TH");
		Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
		Matcher m_html = p_html.matcher(htmlStr);
		htmlStr = m_html.replaceAll(""); // 过滤html标签

		return HTMLDecode(htmlStr.trim());
	}

	/**
	 * 把HTML字符串解码成普通字符串，例如：把&nbsp;转换成空格
	 *
	 * @param str
	 * @return
	 */
	public static String HTMLDecode(String str) {
		if (str != null) {
			Set<Entry<String, String>> entrySet = Constants.HtmlSymbolWholeMap.entrySet();
			for (Entry<String, String> entry : entrySet) {
				if (!"&amp;".equals(entry.getKey())) {
					str = str.replace(entry.getKey(), entry.getValue());
				}
			}
			str = str.replace("&amp;", "&").replace("<br/>", "\n");
		}
		return str;
	}

	/**
	 * 把普通字符串编码成HTML字符串，例如：把空格转换成&nbsp;
	 *
	 * @param str
	 * @param ignoreEnter 是否忽略回车
	 * @return
	 */
	public static String HTMLEncode(String str, boolean ignoreEnter) {
		if (str != null) {
			str = str.replace("&", "&amp;");
			for (Entry<String, String> entry : Constants.HtmlSymbolWholeMap.entrySet()) {
				if (!"&amp;".equals(entry.getKey()) && !"&shy;".equals(entry.getKey())) {
					str = str.replace(entry.getValue(), entry.getKey());
				}
			}
			if (!ignoreEnter) {
				str = str.replace("\r\n", "<br/>").replace("\n", "<br/>");
			}
		}
		return str;
	}

	/**
	 * 返回HTML支持的颜色格式
	 *
	 * @param color 如果为null或者auto，返回空字符串
	 * @return
	 */
	public static String getRealColor(String color) {
		if (color == null || color.equals("auto")) {
			color = "";
		} else if (isRgbColor(color)) {
			color = "#" + color;
		}
		return color;
	}

	// 格式化填空题
	public static String FormatBlankQuestions(String content, String input) {
		if (null == content || "".equals(content))
			return "";

		String result = "";
		Pattern p = Pattern.compile("\\[BlankArea.+?]");
		Matcher m = null;
		m = p.matcher(content);
		result = m.replaceAll(input);

		// System.out.println(result);
		return result;
	}

	/**
	 * 解析学习评分标准字符串，并返回学习评分标准对象集合
	 *
	 * @param cfgStr
	 * @return
	 */
	public static List<ScoreStandard> convertScoreStandards(String cfgStr) {
		if (cfgStr == null || cfgStr.trim().length() == 0) {
			throw new RuntimeException("学习评分标准为空");
		}
		cfgStr = cfgStr.trim();
		List<ScoreStandard> stList = new ArrayList<ScoreStandard>();
		ScoreStandard tmp = null;
		for (String c : cfgStr.split("\n")) {
			c = c.trim();
			if (c.length() > 0) {
				String[] arr = c.split("\\s+");
				if (arr.length != 2) {
					throw new RuntimeException("不可识别的学习评分标准：" + c);
				}
				Double percent = parseDouble(arr[0]);
				Double score = parseDouble(arr[1]);
				if (percent == null || percent < 0 || percent > 100 || score == null || score < 0) {
					throw new RuntimeException("不可识别的学习评分标准：" + c);
				}
				if (tmp != null) {
					if (percent <= tmp.getPercent() || score <= tmp.getScore()) {
						throw new RuntimeException("不合理的学习评分标准：" + c);
					}
				}
				ScoreStandard st = new ScoreStandard(percent, score);
				stList.add(st);
				tmp = st;
			}
		}
		if (stList.size() == 0) {
			throw new RuntimeException("学习评分标准为空");
		}
		if (stList.get(0).getPercent() != 0) {
			stList.add(0, new ScoreStandard(0, 0));
		}
		return stList;
	}

	public static String formatFileSize(long size) {
		DecimalFormat formater = new DecimalFormat("####.00");
		if (size < 1024) {
			return size + "bytes";
		} else if (size < 1024 * 1024) {
			float kbsize = size / 1024f;
			return formater.format(kbsize) + "KB";
		} else if (size < 1024 * 1024 * 1024) {
			float mbsize = size / 1024f / 1024f;
			return formater.format(mbsize) + "MB";
		} else if (size < 1024 * 1024 * 1024 * 1024) {
			float gbsize = size / 1024f / 1024f / 1024f;
			return formater.format(gbsize) + "GB";
		} else {
			return "size: error";
		}
	}

	/**
	 * 采用JavaScript的escape方法对字符串进行编码
	 */
	public static String escapeByJS(String src) throws ScriptException {
		ScriptEngineManager sem = new ScriptEngineManager();
		ScriptEngine engine = sem.getEngineByExtension("js");
		Object res = engine.eval("escape('" + src + "')");
		return res == null ? null : res.toString();
	}

	/**
	 * @Description 中文转拼音, 首字母大写 如 Li Shiming
	 * <AUTHOR>
	 * @Time 2020/9/29 16:58
	 * @Version 1.0
	 */
	public static String toPinYinFirstUp(Object text) {
		hanyuPinyinOutputFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		if (text == null || StringUtils.isEmpty(text.toString())) {
			return "";
		}
		char[] chars = text.toString().toCharArray();
		StringBuilder buffer = new StringBuilder();
		for (int i = 0; i < chars.length; i++) {
			if (Character.toString(chars[i]).matches("[\\u4E00-\\u9FA5]")) {
				try {
					String name = PinyinHelper.toHanyuPinyinStringArray(chars[i], BaseUtil.hanyuPinyinOutputFormat)[0];
					if (i < 2) {
						name = String.valueOf(name.toCharArray()[0]).toUpperCase().concat(name.substring(1));
					}
					buffer.append(name);
					if (i == 0) {
						buffer.append(" ");
					}
				} catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
					badHanyuPinyinOutputFormatCombination.printStackTrace();
				} catch (Exception e) {
					logger.error(text, e);
				}
			} else {
				buffer.append(chars[i]);
				if (i == 0) {
					buffer.append(" ");
				}
			}
		}
		return buffer.toString().trim();
	}

	/**
	 * 返回JSON中的string value,若为空，则返回null
	 */
	public static String getStringValueFromJson(com.alibaba.fastjson.JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return valueObj != null ? valueObj.toString() : null;
	}

	/**
	 * 返回JSON中的int value,若为空，则返回null
	 */
	public static Integer getIntegerValueFromJson(com.alibaba.fastjson.JSONObject json, String key) {
		if (json == null || !json.containsKey(key)) {
			return null;
		}
		Object valueObj = json.get(key);
		return BaseUtil.isNotEmpty(valueObj) ? Integer.valueOf(valueObj.toString()) : null;
	}

	/**
	 * 根据固定的前缀和已有流水号，生成下一位流水号 示例：根据 SYXH000001 生成 SYXH000002
	 */
	public static String nextSerialNumber(String prefix, String serialNumber) {
		if (StringUtils.isEmpty(prefix) || StringUtils.isEmpty(serialNumber)) {
			throw BizException.withMessage("参数错误");
		}
		Integer sequence = Integer.valueOf(serialNumber);
		sequence++;
		return String.format(prefix + "%0" + serialNumber.length() + "d", sequence);
	}

	public static String getCellText(Cell cell) {
		if (cell == null || "".equals(cell.getContents())) {
			return "";
		}
		if (cell.getType().equals(CellType.EMPTY)) {
			return "";
		}
		if (cell.getType() == CellType.DATE) {// 手动填写模板文件时为 date 类型，其他情况有可能不是date类型
			DateCell dc = (DateCell) cell;
			Date date = dc.getDate();
			TimeZone zone = TimeZone.getTimeZone("GMT");
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			sdf.setTimeZone(zone);
			String sDate = sdf.format(date);
			return sDate;
		}
		return trimBlank(converEmptyToNull(cell.getContents()));
	}

	/**
	 * 文本最大相似度
	 * 
	 * @param baseList 基础文本库
	 * @param text     需要查询相似度的文本
	 */
	public static double cleanRepetition(List<String> baseList, String text) {
		// 最高相似度
		double similarityMax = 0;

		// 正则表达式，用于检测<img>标签的存在，不关心内部细节
		String imgPattern = "<img\\b[^>]*?>";
		Pattern pattern = Pattern.compile(imgPattern, Pattern.CASE_INSENSITIVE);
		Matcher matcher = pattern.matcher(text);
		if(matcher.find()) {
			return similarityMax;
		}

		List<String> list = baseList.stream().map(e -> e.replaceAll("<[^<]+?>", "")).collect(Collectors.toList());
		String replaceText = text.replaceAll("<[^<]+?>", "");
		if (CollectionUtils.isEmpty(list)) {
			return similarityMax;
		}
		for (String s : list) {
			// 相似度
			double similarity = 0;
			s = delHTMLTag(s);
			text = delHTMLTag(text);
			if (s.equals(replaceText)) {
				similarity = 100;
			}
			similarityMax = Math.max(similarityMax, similarity);
		}
		return similarityMax;
	}

	/**
	 * 去除字符串中的HTML标签
	 * 
	 * @param htmlStr 包含html标签的字符串
	 * @return
	 */
	public static String delHTMLTag(String htmlStr) {
		// 定义script的正则表达式
		String regExScript = "<script[^>]*?>[\\s\\S]*?</script>";
		// 定义style的正则表达式
		String regExStyle = "<style[^>]*?>[\\s\\S]*?</style>";
		// 定义HTML标签的正则表达式
		String regExHtml = "<[^>]+>";

		Pattern pScript = Pattern.compile(regExScript, Pattern.CASE_INSENSITIVE);
		Matcher mScript = pScript.matcher(htmlStr);
		// 过滤script标签
		htmlStr = mScript.replaceAll("");

		Pattern pStyle = Pattern.compile(regExStyle, Pattern.CASE_INSENSITIVE);
		Matcher mStyle = pStyle.matcher(htmlStr);
		// 过滤style标签
		htmlStr = mStyle.replaceAll("");

		Pattern pHtml = Pattern.compile(regExHtml, Pattern.CASE_INSENSITIVE);
		Matcher mHtml = pHtml.matcher(htmlStr);
		// 过滤html标签
		htmlStr = mHtml.replaceAll("");

		// 返回文本字符串
		return htmlStr.trim();
	}

	/**
	 * 管理用户弱密码检测 密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合，否则即为弱密码 弱密码返回true 非弱密码
	 */
	public static boolean isRawPassword(String password) {
		if (StringUtils.isEmpty(password)) {
			return true;
		} else {
			String patternStr = "^(?![A-Za-z0-9]+$)(?![a-z0-9#?!@$%^&*-.]+$)(?![A-Za-z#?!@$%^&*-.]+$)(?![A-Z0-9#?!@$%^&*-.]+$)[a-zA-Z0-9#?!@$%^&*-.]{8,32}$";
			return password.matches(patternStr) == false;
		}
	}
	
	/**
	 * 学员用户弱密码检测，与管理端不要公用一个方法
	 */
	public static boolean isRawPasswordForStudent(String password) {
		if (StringUtils.isEmpty(password)) {
			return true;
		} else {
			String patternStr = "^(?![A-Za-z0-9]+$)(?![a-z0-9#?!@$%^&*-.]+$)(?![A-Za-z#?!@$%^&*-.]+$)(?![A-Z0-9#?!@$%^&*-.]+$)[a-zA-Z0-9#?!@$%^&*-.]{8,32}$";
			return password.matches(patternStr) == false;
		}
	}
	


	/**
	 * 身份证号码有效性校验
	 */
	public static boolean isIDCardNumber(String IDCardNumber) {
		if (StringUtils.isEmpty(IDCardNumber)) {
			return false;
		}
		// 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
		String regularExpression = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|"
				+ "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
		boolean matches = IDCardNumber.matches(regularExpression);
		// 判断第18位校验值
		if (matches) {
			if (IDCardNumber.length() == 18) {
				try {
					char[] charArray = IDCardNumber.toCharArray();
					// 前十七位加权因子
					int[] idCardWi = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };
					// 这是除以11后，可能产生的11位余数对应的验证码
					String[] idCardY = { "1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2" };
					int sum = 0;
					for (int i = 0; i < idCardWi.length; i++) {
						int current = Integer.parseInt(String.valueOf(charArray[i]));
						int count = current * idCardWi[i];
						sum += count;
					}
					char idCardLast = charArray[17];
					int idCardMod = sum % 11;
					if (idCardY[idCardMod].toUpperCase().equals(String.valueOf(idCardLast).toUpperCase())) {
						return true;
					} else {
						return false;
					}
				} catch (Exception e) {
					e.printStackTrace();
					System.out.println("异常:" + IDCardNumber);
					return false;
				}
			}
		}
		return matches;
	}

	/**
	 * 手机号有效性校验
	 */
	public static boolean isMobile(String mobile) {
		String regex = "^(1[3456789]\\d{9})$";
		if (mobile.length() != 11) {
			return false;
		} else {
			Pattern p = Pattern.compile(regex);
			Matcher m = p.matcher(mobile);
			return m.matches();
		}
	}
	
	   /**
     * 文件下载时对中文文件名进行编码，防止下载文件后出现乱码的情况
     * @throws UnsupportedEncodingException 
     */
    public static String encodeDownloadFileName(HttpServletRequest request, String fileName) throws UnsupportedEncodingException {
		fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
		return fileName;
    }
    
	// 根据IP获取所在城市的名称
	public static String getCityByIP(String ip) {
		String city = "";
		String jsonStr = "";
		String strUrl = "http://apis.juhe.cn/ip/ipNew";
		String srtVlaue = "ip=" + ip + "&key=022287affefd4e498740d2d107efbe4c";
		jsonStr = HttpKit.get(strUrl, srtVlaue);
		System.out.println(jsonStr);
		if (jsonStr.equals("请求超时")) {
			return "获取城市名称失败";
		}
		JSONObject json = JSONObject.fromObject(jsonStr);
		String resultcode = (String) json.get("resultcode");
		if (resultcode.equals("200")) {// 接口請求成功
			JSONObject result = (JSONObject) json.get("result");
			city = (String) result.get("City");
		} else {
			city = "所在城市获取失败";
		}
		if (city.contains("市")) {
			city = city.substring(0, 2);// 只截取前两位汉字
		}
		System.out.println("根据IP:" + ip + "获取城市名称为:" + city);
		return city;
	}

	// 根据城市名称获取该城市的天气状况
	public static Map<String, Object> getWeatherByCity(String city) {
		Map<String, Object> hashmap = new HashMap<String, Object>();
		String jsonStr = "";
		String strUrl = "http://apis.juhe.cn/simpleWeather/query";
		String srtVlaue = "city=" + city + "&key=9bcd97b364477e29c8b80aaff5f75da0";
		jsonStr = HttpKit.get(strUrl, srtVlaue);
		System.out.println(jsonStr);
		JSONObject json = JSONObject.fromObject(jsonStr);
		String reason = (String) json.get("reason");
		if (reason.equals("查询成功!")) {
			for (Object key : json.keySet()) {
				hashmap.put((String) key, json.get(key));
			}
		} else {
			hashmap.put("error_code", "获取" + city + "天气失败");
		}
		return hashmap;
	}

	public static void main(String[] args) {
//		System.out.println(isIDCardNumber("******************"));
//		System.out.println(isMobile("18711112222"));
		System.out.println(BaseUtil.isRawPassword("Abc@123456"));
//		System.out.println(isRawPasswordForStudent("9921l000000"));
//		System.out.println(DigestUtils.md5Hex("123456"));
//		System.out.println(DigestUtils.md5Hex("111111"));
//		System.out.println(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
//		System.out.println(BaseUtil.isRawPassword("Px!@123456"));
	}

}
