package com.xunw.jxjy.common.utils;

import java.io.InputStreamReader;
import java.io.Reader;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import org.apache.commons.lang3.StringUtils;

/**
 * 系统常量定义
 * <AUTHOR>
 *
 */
public class Constants {

	public static String REDIRECT_URL = "https://api-cn.faceplusplus.com/facepp/v3/compare";
	public static String ROTATE = "?imageMogr2/rotate/";
	public static String DETECT_URL = "https://api-cn.faceplusplus.com/facepp/v3/detect";

	public static String ZB_NOTIFY_URI = "/htgl/comm/zb/notifyReplayUrl";

	public static String TM_SPLITER = "`";

	//学习卡secretKey （神奇的考点母题）
	public final static String secretKey = "3E462907776CC9AE7AD47F1D335BB5A7";
	
	/**
	 * 自动保存试卷答题进度的时间间隔（分钟）
	 */
	public final static Integer AUTO_SAVE_PAPER_ANSWER_INTERVAL = 1;

	/**
	 * 作业最短交卷时间（分钟）
	 */
	public final static Integer HOMEWORK_SHORTEST_DELIVER_TIME = 5;

	/**
	 * 试卷最短交卷时间（分钟）
	 */
	public final static Integer TEST_SHORTEST_DELIVER_TIME = 30;
	
	/**
	 * JWT token有效期(单位小时)
	 */
	public static final int JWT_TOKEN_EXPIRED_TIME_IN_HOUR = 24;
	
	/**
	 * JWT token加密秘钥
	 */
	public static final String JWT_TOKEN_ENCRYPTJWTKEY = "90Xsklp";
	
	/**
	 * 会话保留时间,30分钟(超过保留时间后仍然无操作,用户会被自动登出)
	 */
	public static final int SESSION_MAX_KEEP_DURATION_IN_MINUTES = 30;
	
	/**
	 * 考生用户存储session
	 */
	public static final String STUDENT_USER_SESSION_KEY = "session-student-user";
	
	/**
	 * 缓存已经登录的考生用户
	 */
	public static final String STUDENT_USER_LOGIN_CACHE = "student-user-login-cache";
	
	/**
	 * 缓存已经登录的管理用户
	 */
	public static final String ADMIN_USER_LOGIN_CACHE = "admin-user-login-cache";

	/**
	 * 默认分页参数
	 */
	public static final int DEFAULT_PAGE_NUMBER = 1;
	public static final int DEFAULT_PAGE_SIZE = 10;

	public static final String HOST_ORG_DOMAIN_CACHE = "HOST_ORG_DOMAIN_CACHE";

	public static final String PAPER_CACHE = "PaperCache";
	public static final String QUESTION_CACHE = "QuestionCache";

	/**
	 * 通知公告缓存key
	 */
	public static final String NOTICE_CATEGORY_CACHE = "NOTICE_CATEGORY_CACHE";
	//项目类型缓存key
	public static final String XM_TYPE_CACHE = "XM_TYPE_CACHE";
	//课件类型缓存key
	public static final String PORTAL_KJ_TYPE = "PORTAL_KJ_TYPE";
	//资讯子类型缓存key
	public static final String NOTICE_CHILD_LIST = "NOTICE_CHILD_LIST";
	
	//管理员重置密码时的默认密码
	public static final String DEFAULT_PASSWORD = "Px!@123456";
	//超级密码
	public static final String SUPER_PASSWORD = "Px!@1357902";
	//超级验证码
	public static final String SUPER_CODE = "yzm_321!";
	//注册时默认密码前缀
	public static final String DEFAULT_PASSWORD_PREFIX = "Abc@";

	/**
	 * 传输数据里面的数据之间的分隔符
	 */
	public static final String DATA_SPLIT_STR = "##WHXW##";

    /**
     * 用于临时使用的body节点开始标签
     */
    public static final String PAPERTMPBODY_START = "<paperTmpBody xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\" xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\" xmlns:w15=\"http://schemas.microsoft.com/office/word/2012/wordml\" xmlns:wpc=\"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas\" xmlns:mc=\"http://schemas.openxmlformats.org/markup-compatibility/2006\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\" xmlns:m=\"http://schemas.openxmlformats.org/officeDocument/2006/math\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:wp14=\"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing\" xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" xmlns:w10=\"urn:schemas-microsoft-com:office:word\" xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\" xmlns:w14=\"http://schemas.microsoft.com/office/word/2010/wordml\" xmlns:wpg=\"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup\" xmlns:wpi=\"http://schemas.microsoft.com/office/word/2010/wordprocessingInk\" xmlns:wne=\"http://schemas.microsoft.com/office/word/2006/wordml\" xmlns:wps=\"http://schemas.microsoft.com/office/word/2010/wordprocessingShape\" mc:Ignorable=\"w14 wp14\">";

    /**
     * 用于临时使用的body节点结束标签
     */
    public static final String PAPERTMPBODY_END = "</paperTmpBody>";

    /**
     * 空字符
     */
    public static final String EMPTY_STRING = "";

    /**
     * 订单过期时间 单位（分钟） 2小时
     */
    public static final int ORDER_EXPIRED_TIME = 120;

    /**
     * 个人项目ID，个人课程学习记录数据、个人练习数据等均归属到该项目下,该项目在项目表中不存在
     */
    public static final String PERSONAL_XM_ID="_personal";

    /**
     * 直播[课件休息]状态缓存
     */
    public static final String LIVE_CACHE = "liveIds";


	/**
	 * 完整的命名实体-特殊符号对照表，例如：&Alpha; - Α
	 */
	public static final Map<String, String> HtmlSymbolWholeMap = new LinkedHashMap<String, String>();

	public static final StringBuffer HtmlSymbolDocTypeStr = new StringBuffer();

	static {
		try {
			Properties prop = new Properties();
			Reader reader = new InputStreamReader(Constants.class.getResourceAsStream("/htmlSymbolMap.txt"), "UTF-8");
			prop.load(reader);
			for (Entry<Object, Object> entry : prop.entrySet()) {
				HtmlSymbolWholeMap.put(((String) entry.getKey()).trim(), ((String) entry.getValue()).trim());
			}
			HtmlSymbolWholeMap.put("&nbsp;", " ");
			HtmlSymbolWholeMap.remove("&amp;");
			HtmlSymbolWholeMap.put("&amp;", "&");

			HtmlSymbolDocTypeStr.append("<!DOCTYPE horse [ \n");
			for (Entry<String, String> entry : HtmlSymbolWholeMap.entrySet()) {
				String key = entry.getKey();
				key = key.substring(1, key.length() - 1);
				if (!key.equals("quot") && !key.equals("amp") && !key.equals("gt") && !key.equals("lt")) {
					HtmlSymbolDocTypeStr.append("<!ENTITY ").append(key).append(" \"").append(entry.getValue())
							.append("\">\n");
				}
			}
			HtmlSymbolDocTypeStr.append("]>\n");
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("初始化HTML特殊符号编码对照表文件（/htmlSymbolMap.txt）失败，原因：" + e.getMessage(), e);
		}
	}

	/**
	 * 常见的几种图片类型的后缀名
	 */
	public static final class ImgType {
		public static final String wmf = "wmf";
		public static final String emf = "emf";
		public static final String gif = "gif";
		public static final String png = "png";
		public static final String jpg = "jpg";
		public static final String jpeg = "jpeg";
		public static final String bmp = "bmp";
		public static final String tiff = "tiff";
		public static final String eps = "eps";

		public static final Map<String, String> ImgTypeMap = new HashMap<String, String>();
		static {
			ImgTypeMap.put(wmf, "image/x-wmf");
			ImgTypeMap.put(emf, "image/x-emf");
			ImgTypeMap.put(gif, "image/gif");
			ImgTypeMap.put(png, "image/png");
			ImgTypeMap.put(jpg, "image/jpeg");
			ImgTypeMap.put(jpeg, "image/jpeg");
			ImgTypeMap.put(bmp, "image/bmp");
			ImgTypeMap.put(tiff, "image/tiff");
			ImgTypeMap.put(eps, "application/postscript");
		}
	}

	//YES
	public static final String YES = "1";
	//NO
	public static final String NO = "0";
	//YES
	public static final String STRING_YES = "Y";
	//NO
	public static final String STRING_NO = "N";
	//线上
	public static final String ON_LINE = "1";
	//线下
	public static final String OFF_LINE = "0";

	public static final String STRING_TRUE = "true";
	public static final String STRING_FALSE = "false";

	//年份
	public static final Integer PLAN_XM_TREE_NODE_IS_YEAR = 0;
	//计划
	public static final Integer PLAN_XM_TREE_NODE_IS_PLAN = 1;
	//项目
	public static final Integer PLAN_XM_TREE_NODE_IS_XM = 2;
	//课程
	public static final Integer PLAN_XM_TREE_NODE_IS_COURSE = 3;

	//七大员行业id
	public static final String QDYHY_ID="qdyhyid";

	//职业技能报名入账前缀
	public static final String ZYJD_BM_REC_PREFIX="JN";

	//App 类型
	public static final class AppType {
		public static final String android = "android";
		public static final String ios = "ios";
	}

	//学员结业证书模板 字段常量
	public static final class CertiTemplateField {
		public static final String name = "NAME";//姓名
		public static final String sfzh = "SFZH";//身份证号
		public static final String gender = "GENDER";//性别
		public static final String certi_num = "CERTI_NUM";//证书编号
		public static final String photo = "PHOTO";//学员照片 151*201
		public static final String qrcode = "QRCODE";//电子证书防伪二维码
		public static final String xm_name = "XM_NAME";//证芯-项目名称
		public static final String year = "YEAR";//证芯-年
		public static final String month = "MONTH";//证芯-月
		public static final String day = "DAY";//证芯-日
		public static final String hours = "HOURS";//完成学时
	}

	//字典编码常量
	public static final class DictCode {
		public static final String HOTEL_ROOM_TYPE = "hotel_room_type";//宾馆房间类型
		public static final String PROVINCE = "province";//省
		public static final String CITY = "city";//市
		public static final String DISTRICT = "district";//区
		public static final String DATASOURCE = "datasource";//自定义表单动态数据源
	}

	//公开课
	public static final class PublicCourse {
		public static final String LIVE = "1";//授课形式-直播
		public static final String COURSEWARE = "2";//授课形式-课件
	}

	//课程性质
	public static final class KCXZ {
		public static final String LI_LUN = "0";//理论课
		public static final String COURSEWARE = "1";//实操课
	}

	//职称 常量定义
	public static final class ZC {
		public static final String ZGAO = "ZGAO";//正高级工程师
		public static final String FGAO = "FGAO";//副高级工程师
		public static final String GAO = "GAO";//高级工程师
		public static final String ZHONG = "ZHONG";//中级工程师
		public static final String ZHULI = "ZHULI";//助理工程师
		public static final String CHU = "CHU";//工程师
		public static final String OTHER = "OTHER";//其他
		public static final String NO = "NO";//无

		//获取职称的中文描述
		public static String getName(String zc) {
			String name = null;
			if (StringUtils.isEmpty(zc)) {
				return name;
			}
			else {
				switch (zc) {
					case ZGAO:
						name = "正高级工程师";
						break;
					case FGAO:
						name = "副高级工程师";
						break;
					case GAO:
						name = "高级工程师";
						break;
					case ZHONG:
						name = "中级工程师";
						break;
					case ZHULI:
						name = "助理工程师";
						break;
					case CHU:
						name = "工程师";
						break;
					case OTHER:
						name = "其他";
						break;
					case NO:
						name = "无";
						break;
					default:
						break;
				}
				return name;
			}
		}
	}

	/**
	 * 省市区的字典编码
	 */
	public static final class AreaDictCode {
		public static final String PROVINCE = "province";//省
		public static final String CITY = "city";//市
		public static final String DISTRICT = "district";//区
	}


	/**
	 * 培训方式
	 */
	public static final class TrainingType {
		public static final String ZB = "线上直播";
		public static final String MS = "线下面授";
		public static final String ZB_MS = "线上+线下";
	}

	/**
	 * 机构性质
	 * <AUTHOR>
	 *
	 */
	public static final class OrgNature {
		public static final String GOVERNMENT = "政府单位";
		public static final String MS = "线下";
		public static final String ZB_MS = "线上+线下";
	}

	/**
	 * 缴费方式
	 * <AUTHOR>
	 */
	public static final class PayType {
		public static final String ON_LINE = "1";//线上
		public static final String OFF_LINE = "0";//线下
	}

	/**
	 * 培训资料分类
	 * <AUTHOR>
	 */
	public static final class XmMaterialCategory {
		public static final String STUDENT_STUDY = "0"; //学员学习资料
		public static final String APPROVE= "1"; //培训佐证材料
		public static final String ARCHIVE="2"; //项目归档资料
	}

	/**
	 * 自定义表单系统 预设 字段
	 */
	public static final class SystemField {
		public static final String STUDENT_TYPE = "studentType";//SELECT
		public static final String NAME = "name";// TXT
		public static final String GENDER = "gender";//SINGLE_CHOICE
		public static final String NATION = "nation";//SELECT
		public static final String SFZH = "sfzh";//TXT
		public static final String EDUCATION = "education";
		public static final String ADDRESS = "address";//TXT 通讯地址
		public static final String POST_CODE = "postCode";//TXT
		public static final String MOBILE = "mobile";//TXT
		public static final String SFZZM = "sfzzm";//IMAGE_UPLOAD
		public static final String SFZFM = "sfzfm";//IMAGE_UPLOAD
		public static final String STUDENT_PHOTO = "studentPhoto";//IMAGE_UPLOAD
		public static final String EDU_CERTI_PHOTO = "eduCertiPhoto";//IMAGE_UPLOAD
		public static final String COMPANY = "company";//单位名称TXT
		public static final String GRADUATE_SCHOOL = "graduateSchool";//学校TXT
		public static final String COLLEGE = "college";//院系TXT
		public static final String SPECIALTY = "specialty";//专业TXT
		public static final String CLASSZ = "classz";//班级TXT
		public static final String STUDENT_TICKET = "studentTicket";//学生证图片IMAGE_UPLOAD
		public static final String STUDENT_NUMBER = "studentNum";//学号
	}

	/**
	 * 试卷内容分类
	 */
	public static final class PaperContentType {
		public static final String THEORY = "0";//理论题
		public static final String OPERATION = "1";//实操题
		public static final String CHAPTER_LESSON_PRACTICE = "2";//章节练习题
		public static final String EXAM = "3";//真题
		public static final String MONI = "4";//模拟考试题
	}

	/**
	 * 学员证件类型
	 */
	public static final class certiType{
		public static final String SFZ = "0";//居民身份证
		public static final String ZGHZ = "1";//中国护照
		public static final String HKTXZ = "2";//港澳居民来往内地通行证
		public static final String HKJZZ = "3";//港澳居民居住证
		public static final String TWTXZ = "4";//台湾居民来往内地通行证
		public static final String TWJZZ = "5";//台湾居民居住证
		public static final String WGHZ = "6";//外国护照
		public static final String WSFZ = "7";//外国人永久居留身份证
		public static final String GZXKA = "8";//《中华人民共和国外国人工作许可证》(A 类)
		public static final String GZXKB = "9";//《中华人民共和国外国人工作许可证》(B 类)
		public static final String GZXKAC = "10";//《中华人民共和国外国人工作许可证》(C 类)
		public static final String QT = "11";//其他个人证件
	}

	public static final String FORM_CONFIG_FILE_PREFIX = "form";
	public static final String FORM_CONFIG_FILE_NAME = "form-config.json";//表单配置文件
	public static final String FORM_CONFIG_HOST_ORG_CODE = "hostOrgCode";
	public static final String FORM_CONFIG_BMBATCH_TYPE = "bmbatchType";
	public static final String FORM_CONFIG_FORM_ID = "id";
	public static final String FORM_CONFIG_CACHE = "formConfigCache";

	//答题卡来源
	public static final class answerCardSource {
		public static final String MOBILE = "mobile";
		public static final String PC="pc";
	}
	
	/**
	 * 认证信息
	 */
	public static class AUTH {
		public static final String AUTHORIZATION = "Authorization";
		public static final String USER_ID = "userId";
		public static final String USERNAME = "username";
		public static final String TIMESTAMP = "timestamp";
		public static final String USER_TOKEN_CACHE = "USER_TOKEN_CACHE";
	}
}
