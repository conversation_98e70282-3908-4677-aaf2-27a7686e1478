package com.xunw.jxjy.student.portal.controller;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.sys.entity.*;
import com.xunw.jxjy.model.sys.params.PortalXmQueryParams;
import com.xunw.jxjy.model.zypx.entity.ZypxSkillCerti;
import com.xunw.jxjy.model.zypx.service.ZypxSkillCertiService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.Chapter;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.Lesson;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.Courseware;
import com.xunw.jxjy.model.inf.entity.CoursewareComment;
import com.xunw.jxjy.model.inf.entity.CoursewareMaterial;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.params.CoursewareCommentQueryParams;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.inf.service.CoursewareCommentService;
import com.xunw.jxjy.model.inf.service.CoursewareMaterialService;
import com.xunw.jxjy.model.inf.service.CoursewareService;
import com.xunw.jxjy.model.personal.service.ZypxStudentCorePaperService;
import com.xunw.jxjy.model.personal.service.ZypxStudentPaperService;
import com.xunw.jxjy.model.portal.service.IndexService;
import com.xunw.jxjy.model.portal.service.LgdIndexService;
import com.xunw.jxjy.model.sys.service.NoticeService;
import com.xunw.jxjy.model.sys.service.PortalXmService;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.service.QuestionDB2CourseService;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.wdxx.entity.MyPracticeRecord;
import com.xunw.jxjy.model.wdxx.service.PortalPracticeRecordService;
import com.xunw.jxjy.model.zypx.service.ZypxExamDataService;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

/**
 * 理工大培训平台，定制化门户
 *
 * <AUTHOR>
 */
@Controller
public class LgdPortalControler extends BaseController {

    private static final Integer DEFAULT_PAGE_SIZE = 6;

    @Autowired
    private IndexService indexService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
	private PortalPracticeRecordService portalPracticeRecordService;
    @Autowired
    private CoursewareMaterialService coursewareMaterialService;
    @Autowired
    private CoursewareService coursewareService;
    @Autowired
	private ZypxExamDataService examDataService;
	@Autowired
	private QuestionDBService questionDBService;
	@Autowired
	private CourseService courseService;
    @Autowired
    private CoursewareCommentService coursewareCommentService;
    @Autowired
    private ZypxStudentCorePaperService studentCorePaperService;
    @Autowired
    private LgdIndexService lgdIndexService;
    @Autowired
    private PortalXmService portalXmService;
    @Autowired
    private QuestionDB2CourseService question2CourseService;
    @Autowired
    private ZypxSkillCertiService zypxSkillCertiService;
    @Autowired
    private PortalXmService service;

    /**
     * 理工大门户首页
     */
    @RequestMapping("/portal/{hostOrgCode}/index")
    public Object list(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        //获取子类
        List<Map<String, Object>> categoryList = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        mv.addObject("categoryList", categoryList);
        List<List<Map<String, Object>>> newsList = new ArrayList<List<Map<String, Object>>>();
        if (categoryList.size() > 0) {
            for (Map<String, Object> category : categoryList) {
                Map<String, Object> condition = new HashMap<String, Object>();
                condition.put("categoryId", BaseUtil.getStringValueFromMap(category, "id"));
                //获取新闻咨询信息
                Page<Map<String, Object>> pageInfo = indexService.getNotice(condition, Constants.DEFAULT_PAGE_NUMBER,
                        Constants.DEFAULT_PAGE_SIZE);
                newsList.add(pageInfo.getRecords());
            }
        }
        //获取父类
        List<NoticeCategory> list=lgdIndexService.getNoticeParent(getCurrentHostOrgId(request));
        List<List<Map<String, Object>>> parentList = new ArrayList<List<Map<String, Object>>>();
        if(list.size() > 0){
            for(NoticeCategory notice: list){
                Map<String, Object> condition = new HashMap<String, Object>();
                condition.put("categoryId", notice.getId());
                Page<Map<String, Object>> pageInfo = indexService.getNotice(condition, Constants.DEFAULT_PAGE_NUMBER,
                        Constants.DEFAULT_PAGE_SIZE);
                parentList.add(pageInfo.getRecords());
            }
        }
        Map<String, Object> bannerCondition = new HashMap<String, Object>();
        bannerCondition.put("hostOrgId", getCurrentHostOrgId(request));
        Page<Map<String, Object>> syslbts = indexService.getBanner(bannerCondition, Constants.DEFAULT_PAGE_NUMBER,
                Constants.DEFAULT_PAGE_SIZE);
        mv.addObject("bannerList", syslbts.getRecords());
        mv.addObject("parentList", parentList);
        mv.addObject("newsList", newsList);
        //获取左侧的新闻咨询(置顶以及最新的两条,右侧为7条)
        List<Map<String, Object>> notices = indexService.getNoticeByNew().getRecords();
        mv.addObject("leftNews",notices.size()<2?notices:notices.subList(0,2));
        mv.addObject("rightNews",notices.size()>2?notices.subList(2,notices.size()):null);

        //获取通知公告
        Map<String, Object> condition = new HashMap<String, Object>();
        List<Map<String, Object>> categorys = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        if (categorys.size() > 0) {
            condition.put("categoryId","TZGG");
            Page pageInfo = indexService.getNotice(condition, 1, 6);
            mv.addObject("noticeList", pageInfo.getRecords());
        }
        //获取热门项目
        PortalXmQueryParams portalXmQueryParams = new PortalXmQueryParams();
        portalXmQueryParams.setCurrent(1);
        portalXmQueryParams.setIsHot(Constants.YES);
        portalXmQueryParams.setSize(12);
        portalXmQueryParams.setHostOrgId(super.getCurrentHostOrgId(request));
        Page page = service.pageQuery(portalXmQueryParams);
        mv.addObject("projectList",page.getRecords());
        //获取推荐课程
        Map<String, Object> courseCondition = new HashMap<String, Object>();
        courseCondition.put("hostOrgId", super.getCurrentHostOrgId(request));
        Page<Map<String, Object>> pageByCourseList = lgdIndexService.getCourseByIndex(courseCondition,1, 4);
        mv.addObject("courseList", pageByCourseList.getRecords());
        //返回页面数据
        mv.setViewName("pages/portal/" + hostOrgCode + "/index");
        return mv;
    }

    /**
     * 中心概况
     */
    @RequestMapping("/portal/{hostOrgCode}/profileIndex")
    public Object profileIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/profileIndex");
        return mv;
    }

    /**
     * 组织结构
     */
    @RequestMapping("/portal/{hostOrgCode}/structureIndex")
    public Object structureIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/structureIndex");
        return mv;
    }

    /**
     * 培训基地
     */
    @RequestMapping("/portal/{hostOrgCode}/trainingBaseIndex")
    public Object trainingBaseIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingBaseIndex");
        return mv;
    }

    /**
     * 往期剪彩
     */
    @RequestMapping("/portal/{hostOrgCode}/trainingCaseIndex")
    public Object trainingCaseIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize, @PathVariable String hostOrgCode) throws Exception {
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("hostOrgId",getCurrentHostOrgId(request));
        Page<TrainingCase> pageInfo = lgdIndexService.getTrainingCase(condition, pageNum, pageSize);
        mv.addObject("caseList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingCaseIndex");
        return mv;
    }

    /**
     * 通知公告
     */
    @RequestMapping("/portal/{hostOrgCode}/noticeIndex")
    public Object noticeIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize, String categoryId, Integer sortType, @PathVariable String hostOrgCode) throws Exception {
        // 取出所有的类型
        List<Map<String, Object>> categorys = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        mv.addObject("categorys", categorys);
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : Constants.DEFAULT_PAGE_SIZE;
        if (categorys.size() > 0) {
            if (StringUtils.isEmpty(categoryId)) {
                mv.addObject("activeCategory", categorys.get(0));
            } else {
                List<Map<String, Object>> categoryList = categorys.stream()
                        .filter(x -> BaseUtil.getStringValueFromMap(x, "id").equals(categoryId))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    mv.addObject("activeCategory", categoryList.get(0));
                }
            }
            Map<String, Object> condition = new HashMap<String, Object>();
            condition.put("categoryId",
                    StringUtils.isEmpty(categoryId) ? BaseUtil.getStringValueFromMap(categorys.get(0), "id")
                            : categoryId);
            condition.put("sortType",sortType);
            Page pageInfo = indexService.getNotice(condition, pageNum, pageSize);
            mv.addObject("noticeList", pageInfo.getRecords());
            mv.addObject("pageSize", pageSize);
            mv.addObject("pageNum", pageNum);
            mv.addObject("count", pageInfo.getTotal());
            mv.addObject("categoryId",categoryId);
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/noticeIndex");
        return mv;
    }

    /**
     * 通知详情
     */
    @RequestMapping("/portal/{hostOrgCode}/noticeDetail")
    public Object noticeDetail(HttpServletRequest request, ModelAndView mv, String id, @PathVariable String hostOrgCode) throws Exception {
        // 所有类型
        List<Map<String, Object>> categorys = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        mv.addObject("categorys", categorys);
        Notice notice = noticeService.selectById(id);
        NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
        mv.addObject("activeCategory", noticeCategory);
        mv.addObject("notice", notice);
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", noticeCategory.getId());
        Page noticePages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
        //获取上一条通知的ID、下一条通知的ID
        List<Map<String, Object>> allNotice = noticePages.getRecords();
        int index = 0;
        for (int i = 0; i < allNotice.size(); i++) {
            Map map = allNotice.get(i);
            if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Map<String, Object> lastNotice = allNotice.get(index - 1);
            mv.addObject("lastNoticeId", BaseUtil.getStringValueFromMap(lastNotice, "id"));
        }
        if (index < allNotice.size() - 1) {
            Map<String, Object> nextNotice = allNotice.get(index + 1);
            mv.addObject("nextNoticeId", BaseUtil.getStringValueFromMap(nextNotice, "id"));
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/noticeDetail");
        return mv;
    }

    /**
     * 中心新闻
     */
    @RequestMapping("/portal/{hostOrgCode}/newsIndex")
    public Object newsIndex(HttpServletRequest request, ModelAndView mv, String categoryId, Integer sortType, Integer pageNum, Integer pageSize,
                            @PathVariable String hostOrgCode) throws Exception {

        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : Constants.DEFAULT_PAGE_SIZE;
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", categoryId);
        condition.put("sortType", sortType);
        Page pageInfo = indexService.getNotice(condition, pageNum, pageSize);
        mv.addObject("newsList", pageInfo.getRecords());
        mv.addObject("count", pageInfo.getTotal());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("categoryId", categoryId);
        mv.setViewName("pages/portal/" + hostOrgCode + "/newsIndex");
        return mv;
    }

    /**
     * 新闻详情
     */
    @RequestMapping("/portal/{hostOrgCode}/newsDetail")
    public Object newsDetail(HttpServletRequest request, ModelAndView mv, String id, @PathVariable String hostOrgCode) throws Exception {
        Notice notice = noticeService.selectById(id);
        NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
        mv.addObject("activeCategory", noticeCategory);
        mv.addObject("notice", notice);
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", noticeCategory.getId());
        Page newsPages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
        //获取上一条新闻的ID、下一条新闻的ID
        List<Map<String, Object>> allNews = newsPages.getRecords();
        int index = 0;
        for (int i = 0; i < allNews.size(); i++) {
            Map map = allNews.get(i);
            if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Map<String, Object> lastNews = allNews.get(index - 1);
            mv.addObject("lastNewsId", BaseUtil.getStringValueFromMap(lastNews, "id"));
        }
        if (index < allNews.size() - 1) {
            Map<String, Object> nextNews = allNews.get(index + 1);
            mv.addObject("nextNewsId", BaseUtil.getStringValueFromMap(nextNews, "id"));
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/newsDetail");
        return mv;
    }

    /**
     * 师资力量
     */
    @RequestMapping("/portal/{hostOrgCode}/teachersIndex")
    public Object teachersIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize, @PathVariable String hostOrgCode) throws Exception {
        Map<String, Object> condition = new HashMap<String, Object>();
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        //获取主办单位id
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(condition, pageNum, pageSize);
        mv.addObject("teacherList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/" + hostOrgCode + "/teachersIndex");
        return mv;
    }

    /**
     * 员工风采
     */
    @RequestMapping("/portal/{hostOrgCode}/employeeIndex")
    public Object employeeIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/employeeIndex");
        return mv;
    }

    /**
     * 项目介绍
     */
    @RequestMapping("/portal/{hostOrgCode}/trainingIndex")
    public Object trainingIndex(HttpServletRequest request, ModelAndView mv, @RequestParam(required = false) String parentTypeId,String childId, @PathVariable String hostOrgCode) throws Exception {
        //如果当前类型为空默认取第一条
        if (StringUtils.isEmpty(parentTypeId)) {
            List<ZypxType> zypxTypeList = indexService.getTopTypeList(getCurrentHostOrgId(request));
            parentTypeId = zypxTypeList.get(0).getId();
        }
        List<Map<String, Object>> typeList = indexService.getChildType(parentTypeId);
        for (Map<String, Object> map : typeList) {
            List<Map<String, Object>> childTypes = indexService.getChildType(BaseUtil.getStringValueFromMap(map, "id"));
            map.put("child_types", childTypes);
        }
        // condition
        Map<String, Object> condition = new HashMap<String, Object>();
        if(StringUtils.isEmpty(childId)){
            condition.put("typeId", parentTypeId);
        }else{
            condition.put("typeId", childId);
        }
        //获取主办单位id
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        //项目列表
        List<Map<String, Object>> xmList = lgdIndexService.getPortalXmList(condition, Constants.DEFAULT_PAGE_NUMBER, Constants.DEFAULT_PAGE_SIZE).getRecords();
        ZypxType zypxType = indexService.getZypxTypeById(parentTypeId);
        mv.addObject("typeList",typeList);
        mv.addObject("curParentTypeId", parentTypeId);
        mv.addObject("curChildTypeId", childId);
        mv.addObject("curParentTypeName", zypxType.getName());
        mv.addObject("xmList", xmList);
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingIndex");
        return mv;
    }

    /**
     * 项目详情
     */
    @RequestMapping("/portal/{hostOrgCode}/trainingDetail")
    public Object trainingDetail(HttpServletRequest request, ModelAndView mv,
                                 @RequestParam(required = false) String parentTypeId,
                                 @RequestParam(required = false) String childId,
                                 @RequestParam(required = false) String id,
                                 @PathVariable String hostOrgCode){
        //如果当前类型为空默认取第一条
        if (StringUtils.isEmpty(parentTypeId)) {
            List<ZypxType> zypxTypeList = indexService.getTopTypeList(getCurrentHostOrgId(request));
            parentTypeId = zypxTypeList.get(0).getId();
        }
        List<Map<String, Object>> typeList = indexService.getChildType(parentTypeId);
        for (Map<String, Object> map : typeList) {
            List<Map<String, Object>> childTypes = indexService.getChildType(BaseUtil.getStringValueFromMap(map, "id"));
            map.put("child_types", childTypes);
        }
        if("".equals(childId)){
            mv.addObject("curChildTypeId", null);
        }else{
            mv.addObject("curChildTypeId", childId);
        }
        PortalXm portalXm = portalXmService.selectById(id);
        ZypxType zypxType = indexService.getZypxTypeById(parentTypeId);
        mv.addObject("typeList",typeList);
        mv.addObject("curParentTypeId", parentTypeId);
        mv.addObject("portalXm", portalXm);
        mv.addObject("curParentTypeName", zypxType.getName());
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingDetail");
        return mv;
    }

    /**
     * 课程列表
     */
    @RequestMapping("/portal/{hostOrgCode}/courseList")
    public Object courseList(HttpServletRequest request, ModelAndView mv,@RequestParam(required = false) String kjType, @PathVariable String hostOrgCode) throws Exception {
        //如果当前类型为空默认取第一条
        if (StringUtils.isEmpty(kjType)) {
            List<String> portalKjList = lgdIndexService.getPortakKjType(getCurrentHostOrgId(request));
            if(portalKjList.size()>0) {
                kjType = portalKjList.get(0);
            }
        }
        // condition
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("tag", kjType);
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        Page<Map<String, Object>> page = lgdIndexService.getCourse(condition, Constants.DEFAULT_PAGE_NUMBER, Constants.DEFAULT_PAGE_SIZE);
        mv.addObject("courseList", page.getRecords());
        mv.addObject("courseKjType",kjType);
        mv.setViewName("pages/portal/" + hostOrgCode + "/courseList");
        return mv;
    }

    /**
     * 学习课件
     */
    @RequestMapping("/portal/{hostOrgCode}/courseIndex")
    public Object courseIndex(HttpServletRequest request, ModelAndView mv,@RequestParam(required = false) String kjType,
                              @RequestParam(required = false) String courseId,
                              @PathVariable String hostOrgCode) throws Exception {
        //如果当前类型为空默认取第一条
        if (StringUtils.isEmpty(kjType)) {
            List<String> portalKjList = lgdIndexService.getPortakKjType(getCurrentHostOrgId(request));
            if(portalKjList.size()>0) {
                kjType = portalKjList.get(0);
            }
        }
        // condition
        mv.addObject("courselearn", indexService.getCourselearnByCourseId(courseId));
        Course course = courseService.selectById(courseId);
        //获取当前课程的题库
        LoginStudent loginStudent = super.getLoginStudent(request);
        // 根据课程获取题库列表
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (QuestionDBEntity questionDBEntity : questionDBService.getQuestionDBEntity(courseId)) {
            Map<String, Object> map = new HashMap<String, Object>();
            String questionDbId = questionDBEntity.getId();
            map.put("id", questionDbId);
            map.put("name", questionDBEntity.getName());
            if (loginStudent != null) {
                String studentId = loginStudent.getUser().getId();
                // 我的练习
                EntityWrapper<MyPracticeRecord> myPraceticeRecordWrapper = new EntityWrapper<MyPracticeRecord>();
                myPraceticeRecordWrapper.eq("student_id", studentId);
                myPraceticeRecordWrapper.eq("db_id", questionDbId);
                List<MyPracticeRecord> myPaperList = portalPracticeRecordService.selectList(myPraceticeRecordWrapper);
                if (myPaperList.size() > 0) {
                    String paperId = myPaperList.get(0).getPaperId();
                    // 考试记录
                    EntityWrapper<ExamData> examDataWrapper = new EntityWrapper<ExamData>();
                    examDataWrapper.eq("student_id", studentId);
                    examDataWrapper.eq("paper_id", paperId);
                    List<ExamData> examDatas = examDataService.selectList(examDataWrapper);
                    if (examDatas.size() > 0) {
                        ExamDataStatus examDataStatus = examDatas.get(0).getStatus();
                        map.put("examDataStatus", examDataStatus.name());
                    }
                }
            }
            list.add(map);
        }
        mv.addObject("courseKjType",kjType);
        mv.addObject("dbList", list);
        mv.addObject("course", course);
        mv.setViewName("pages/portal/" + hostOrgCode + "/courseIndex");
        return mv;
    }

    /**
     * 试题
     */
    @RequestMapping("/portal/{hostOrgCode}/doQuestionDBPaper")
    public Object courseQuestion(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode,
                                 @RequestParam String kjType,String id) throws Exception {
        String studentId = getLoginStudentId(request);
        QuestionDBEntity questionDBEntity = questionDBService.selectById(id);
        QuestionDB2CourseEntity question2CourseEntity = question2CourseService.getDetailsByDbId(id);
        Course course = courseService.selectById(question2CourseEntity.getCourseId());
        Paper paper = indexService.makePaperByQuestionDB(id, studentId);
        EntityWrapper<ExamData> wrapper = new EntityWrapper();
        wrapper.eq("student_id", studentId);
        wrapper.eq("paper_id", paper.getId());
        List<ExamData> examDataList = examDataService.selectList(wrapper);
        if (examDataList.size() > 0) {
            mv.addObject("examDataStatus", examDataList.get(0).getStatus());
            Map<String, Object> paperInitData = studentCorePaperService.getLastSubmit4PaperInit(paper, studentId, paper.getId());
            mv.addObject("paperInitData", paperInitData);
        }
        mv.addObject("courseKjType",kjType);
        mv.addObject("db", questionDBEntity);
        mv.addObject("course", course);
        mv.addObject("paper", paper);
        mv.setViewName("pages/portal/" + hostOrgCode + "/doQuestionDBPaper");
        return mv;
    }

    /**
     * 课件播放
     */
    @RequestMapping("/portal/{hostOrgCode}/coursePlay")
    public Object coursePlay(HttpServletRequest request, ModelAndView mv, @RequestParam(required = false) String kjType,
                             @PathVariable String hostOrgCode,String id,@RequestParam(required = false) String lessonId) throws Exception {
        Courselearn courselearn = indexService.getCourselearnById(id);
        Courseware courseware = coursewareService.selectById(id);
        Course course = courseService.selectById(courseware.getCourseId());
        Lesson activeLesson = null;
        Chapter activeChapter = null;
        //获取学习资料
        if (courselearn != null) {
            for (Chapter chapter : courselearn.getChapters()) {
                for (Lesson lesson : chapter.getLessons()) {
                    if (activeLesson == null) {
                        if (StringUtils.isEmpty(lessonId)) {
                            activeLesson = lesson;
                            activeChapter = chapter;
                        }
                        else if(lesson.getId().equals(lessonId)){
                            activeLesson = lesson;
                            activeChapter = chapter;
                        }
                    }
                    //获取课时的学习资料
                    EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper();
                    materialWrapper.eq("kj_id", id);
                    materialWrapper.eq("chapter_id", chapter.getId());
                    materialWrapper.eq("lesson_id", lesson.getId());
                    List<CoursewareMaterial> files = coursewareMaterialService.selectList(materialWrapper);
                    lesson.setFiles(files);
                }
            }
        }
        mv.addObject("courseKjType",kjType);
        mv.addObject("course", course);
        mv.addObject("courselearn", courselearn);
        mv.addObject("courseware", courseware);
        mv.addObject("activeLesson", activeLesson);
        mv.addObject("activeChapter", activeChapter);
        // 获取评论
        CoursewareCommentQueryParams params = new CoursewareCommentQueryParams();
        params.setKjId(id);
        params.setChapterId(activeChapter.getId());
        params.setLessonId(lessonId);
        mv.addObject("comments", coursewareCommentService.getList(params));
        mv.setViewName("pages/portal/" + hostOrgCode + "/coursePlay");
        return mv;
    }

    /**
     * 保存评论
     */
    @RequestMapping("/portal/{hostOrgCode}/saveComment")
    @ResponseBody
    public Object saveComment(HttpServletRequest request, @RequestParam(required = false) String kjId,
                              @RequestParam(required = false) String chapterId,
                              @RequestParam(required = false) String lessonId,
                              @RequestParam(required = false) String pl) {
        CoursewareComment coursewareNote =
                new CoursewareComment(BaseUtil.generateId2(), kjId, chapterId, lessonId, pl, super.getLoginStudentId(request), new Date());
        coursewareCommentService.save(coursewareNote);
        return true;
    }

    /**
     * 查询评论
     */
    @RequestMapping("/portal/{hostOrgCode}/comments")
    @ResponseBody
    public Object comments(HttpServletRequest request, CoursewareCommentQueryParams params) {
        return coursewareCommentService.getList(params);
    }

    /**
     * 班级风采
     */
    @RequestMapping("/portal/{hostOrgCode}/ourclassIndex")
    public Object ourcalssIndex(HttpServletRequest request, ModelAndView mv,String categoryId, Integer pageNum, Integer pageSize
            ,@PathVariable String hostOrgCode) throws Exception {
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : Constants.DEFAULT_PAGE_SIZE;
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", categoryId);
        Page pageInfo = indexService.getNotice(condition, pageNum, pageSize);
        mv.addObject("ourClassList", pageInfo.getRecords());
        mv.addObject("count", pageInfo.getTotal());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("categoryId", categoryId);
        mv.setViewName("pages/portal/" + hostOrgCode + "/ourclassIndex");
        return mv;
    }

    /**
     * 班级风采详情
     */
    @RequestMapping("/portal/{hostOrgCode}/ourclassDetail")
    public Object ourclassDetail(HttpServletRequest request, ModelAndView mv, String id, @PathVariable String hostOrgCode) throws Exception {
        Notice notice = noticeService.selectById(id);
        NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
        mv.addObject("activeCategory", noticeCategory);
        mv.addObject("notice", notice);
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", noticeCategory.getId());
        Page newsPages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
        //获取上一数据的ID、下一条数据的ID
        List<Map<String, Object>> allNews = newsPages.getRecords();
        int index = 0;
        for (int i = 0; i < allNews.size(); i++) {
            Map map = allNews.get(i);
            if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Map<String, Object> lastNews = allNews.get(index - 1);
            mv.addObject("lastNewsId", BaseUtil.getStringValueFromMap(lastNews, "id"));
        }
        if (index < allNews.size() - 1) {
            Map<String, Object> nextNews = allNews.get(index + 1);
            mv.addObject("nextNewsId", BaseUtil.getStringValueFromMap(nextNews, "id"));
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/ourclassDetail");
        return mv;
    }

    /**
     * 学员寄语
     */
    @RequestMapping("/portal/{hostOrgCode}/messageDetail")
    public Object messageDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/messageDetail");
        return mv;
    }

    /**
     * 证书查询
     */
    @RequestMapping("/portal/{hostOrgCode}/credentialQuery")
    public Object credentialQuery(HttpServletRequest request,
                                  ModelAndView mv,
                                  @PathVariable String hostOrgCode,
                                  @RequestParam(required = false) String certiNum,
                                  @RequestParam(required = false) String sfzh,
                                  @RequestParam(required = false) String name){
        if (StringUtils.isEmpty(sfzh) || StringUtils.isEmpty(name)) {
            mv.addObject("resultMsg", "请输入个人信息查询");
            mv.setViewName("pages/portal/" + hostOrgCode + "/credentialQuery");
            return mv;
        }
        List<Map<String, Object>> certiByStudent = indexService.getCertiByStudent(certiNum,sfzh,name,
        		super.getCurrentHostOrgId(request));
        EntityWrapper<ZypxSkillCerti> entityWrapper = new EntityWrapper<>();
        if (!StringUtils.isEmpty(certiNum)){
            entityWrapper.eq("certi_no",certiNum);
        }
        entityWrapper.eq("sfzh",sfzh);
        entityWrapper.eq("student_name",name);
        List<ZypxSkillCerti> skillCertis = zypxSkillCertiService.selectList(entityWrapper);
        ArrayList<Object> result = new ArrayList<>();
        if (certiByStudent!=null){
            result.addAll(certiByStudent);
        }
        if (skillCertis!=null){
            result.addAll(skillCertis);
        }
        if (CollectionUtils.isEmpty(result)) {
            mv.addObject("resultMsg", "查询无结果");
            mv.setViewName("pages/portal/" + hostOrgCode + "/credentialQuery");
        } else {
            mv.addObject("result", result);
            mv.setViewName("pages/portal/" + hostOrgCode + "/credentialResult");
        }
        return mv;
    }

    /**
     * 联系我们
     */
    @RequestMapping("/portal/{hostOrgCode}/contactus")
    public Object contactusDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/contactus");
        return mv;
    }

    /**
     * 培训项目列表
     */
    @RequestMapping("/portal/{hostOrgCode}/projectList")
    public Object courseList(HttpServletRequest request,
                       PortalXmQueryParams params,
                       Integer pageNum,
                       Integer pageSize,
                       String parentTypeId,
                       String typeId,
                       ModelAndView mv,
                       @PathVariable String hostOrgCode
                       ) throws Exception {
        //赋默认值
        pageNum = pageNum != null ? pageNum : 1;
        pageSize = pageSize != null ? pageSize : 12;
        //处理左侧的查询条件树
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.XM);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.XM));
        }
        //处理查询数据
        params.setCurrent(pageNum);
        params.setSize(pageSize);
        if (StringUtils.isNotEmpty(typeId)) {
            params.setTypeId(typeId);
        } else {
            params.setTypeId(StringUtils.isNotEmpty(parentTypeId) ? parentTypeId : null);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        Page page = service.pageQuery(params);
        mv.addObject("courseList",page.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", page.getTotal());
        mv.setViewName("pages/portal/"+hostOrgCode+"/projectList");
        return mv;
    }

    /**
     * 详情
     */
    @RequestMapping("/portal/{hostOrgCode}/getCourseDeatilById")
    public Object getCourseDeatilById(HttpServletRequest request,
                                      String id,
                                      ModelAndView mv,
                                      @PathVariable String hostOrgCode
                                      ) throws Exception {
        if(id==null){
            throw BizException.withMessage("培训项目id不能为空");
        }
        PortalXm portalXm = service.selectById(id);
        if (portalXm==null){
            throw BizException.withMessage("根据此项目id查询不到项目");
        }
        mv.addObject("PortalXm",portalXm );
        mv.setViewName("pages/portal/"+hostOrgCode+"/projectDeatil");
        return mv;
    }
}
