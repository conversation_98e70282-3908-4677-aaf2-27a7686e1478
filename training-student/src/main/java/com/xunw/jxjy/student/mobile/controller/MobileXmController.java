package com.xunw.jxjy.student.mobile.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.enums.ScheduleSign;
import com.xunw.jxjy.model.enums.XmNoticeStatus;
import com.xunw.jxjy.model.inf.entity.Form;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.inf.service.HotelService;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.mobile.service.ZypxMobileBmService;
import com.xunw.jxjy.model.mobile.service.ZypxMobileXmService;
import com.xunw.jxjy.model.personal.mapper.StudentBmMapper;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.personal.service.ZypxStudentCertiService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.zypx.entity.*;
import com.xunw.jxjy.model.zypx.params.ZypxXmCommonQueryParams;
import com.xunw.jxjy.model.zypx.service.*;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;
import net.sf.json.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职业培训---项目
 * <AUTHOR>
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/xm")
public class MobileXmController extends BaseController {

	@Autowired
	private ZypxMobileXmService service;
	@Autowired
	private ZypxQaService qaService;
	@Autowired
	private ZypxXmMaterialService xmMaterialService;
	@Autowired
	private ZypxTypeService typeService;
	@Autowired
	private ZypxMobileBmService mobileBmService;
	@Autowired
	private FormService formService;
	@Autowired
	private PxxmXmBmAdviceService pxxmXmBmAdviceService;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private ZypxXmScheduleService zypxXmScheduleService;
	@Autowired
	private ZypxXmNoticeService zypxXmNoticeService;
	@Autowired
	private StudentBmMapper studentBmMapper;
	@Autowired
	private UserService userService;
	@Autowired
	private ZypxXmPracticeAreaService zypxXmPracticeAreaService;
	@Autowired
	private ZypxStudentCertiService zypxStudentCertiService;
    @Autowired
    private ZypxXmCourseSettingService zypxXmCourseSettingService;
    @Autowired
    private ZypxXmService zypxXmService;
    @Autowired
    private HotelService hotelService;
    @Autowired
    private ZypxXmNoticeRecordService zypxXmNoticeRecordService;
    @Autowired
    private ZypxStudentBmService zypxStudentBmService;

	/**
	 * 此接口 在项目扫描二维码会用到，所以无需登录
	 */
	@RequestMapping("/getById")
    @Operation(desc = "项目详情查询", loginRequired = false)
	public Object getById(
					HttpServletRequest request,
					@RequestParam(required = false) String id) throws ParseException {
		ZypxXm zypxXm = service.selectById(id);
		if (StringUtils.isNotEmpty(zypxXm.getTypeId())) {
			ZypxType zypxType = typeService.selectById(zypxXm.getTypeId());
			zypxXm.setZypxType(zypxType);
		}
		String formId = formService.getFormByXmId(zypxXm.getId());
		if (StringUtils.isNotEmpty(formId)) {
			Form form = formService.selectById(formId);
			zypxXm.setForm(form);
		}
		String qaId = qaService.getQaByXmId(id);
		zypxXm.setQaId(qaId);
		String studentId = getLoginStudentId(request);
		zypxXm.setStudentId(studentId);
		if (StringUtils.isNotEmpty(studentId)) {
			ZypxBm zypxBm = mobileBmService.getBmInfo(id, studentId);
			if (zypxBm != null) {
				zypxXm.setBmId(zypxBm.getId());
			}
		}
		return zypxXm;
	}

	@RequestMapping("/arrive")
	@Operation(desc = "培训项目签到")
	public Object arrive(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {
		String xmId = BaseUtil.getStringValueFromJson(params, "xmId");
		String poiaddress = BaseUtil.getStringValueFromJson(params, "poiaddress");
		String poiname = BaseUtil.getStringValueFromJson(params, "poiname");
		String poilatlng = BaseUtil.getStringValueFromJson(params, "poilatlng");
		String base64Image = BaseUtil.getStringValueFromJson(params, "base64Image");
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("培训项目ID为空");
		}
		ZypxXm zypxXm = service.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("培训项目不存在");
		}
		LoginStudent loginStudent = super.getLoginStudent(request);
		String stuId = loginStudent.getUser().getId();
		service.arrive(xmId, stuId, poiaddress, poiname, poilatlng, base64Image);
		return true;
	}

	@RequestMapping("/getMaterinals")
	@Operation(desc = "获取培训项目培训资料")
	public Object getMaterinals(HttpServletRequest request, String xmId) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw  BizException.withMessage("请传入项目ID");
		}
		EntityWrapper<ZypxXmMaterial> wrapper = new EntityWrapper<>();
		wrapper.eq("category", "0");
		wrapper.eq("xm_id", xmId);
		wrapper.orderBy("xm_id", false);
		return xmMaterialService.selectList(wrapper);
	}

	/**
	 * 获取自定义表单,并自动回填已经填写的值
	 */
	@RequestMapping("/getForm")
	@Operation(desc = "获取自定义表单", loginRequired = false)
	public Object getFormByBatchId(HttpServletRequest request, @RequestParam(required = false) String xmId,
			@RequestParam(required = false) String formId) throws Exception {
		if(StringUtils.isEmpty(xmId)){
			throw BizException.withMessage("项目Id不能够为空");
		}
		if(StringUtils.isEmpty(formId)){
			throw BizException.withMessage("表单Id不能够为空");
		}
		return mobileBmService.getFormFilledData(xmId, formId, super.getLoginStudentId(request));
	}

	/**
	 * 提交自定义表单
	 */
	@RequestMapping("/submitForm")
	@Operation(desc = "保存项目自定义表单数据", loginRequired = false)
	public Object submitForm(HttpServletRequest request, @RequestBody JSONObject json) throws Exception {
		String studentId = super.getLoginStudentId(request);
		String newStudentId = mobileBmService.saveAnswerForm(json, studentId, request.getSession());
		if (BaseUtil.isNotEmpty(newStudentId)) {
			return cacheLoginStudent(studentUserService.selectById(newStudentId), request);
		}
		return true;
	}
	
	protected Map<String, Object> cacheLoginStudent(StudentUser studentUser, HttpServletRequest request) {
		Map<String, Object> userMap = new HashMap<>();
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
		LoginStudent loginStudent = new LoginStudent();
		loginStudent.setUser(studentUser);
		loginStudent.setInfo(studentInfo);
		loginStudent.setHostOrg(orgService.selectById(studentUser.getRegHostOrgId()));
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		// 用户信息
		userMap.put("userId", studentUser.getId());
		String account = StringUtils.isEmpty(studentInfo.getSfzh()) ? studentInfo.getMobile() : studentInfo.getSfzh();
		userMap.put("username", account);
		userMap.put("orgId", studentUser.getOrgId());
		userMap.put("avatar", studentUser.getAvatar());
		userMap.put("isBindMobile", studentUser.getIsBindMobile());
		userMap.put("isPersonalInfoCompleted",
				studentInfoService.isPersonalInfoCompleted(studentUser.getId()) == true ? Constants.YES : Constants.NO);
		// 个人信息
		userMap.put("info", studentInfo);
		Date now = new Date();
		userMap.put("lastLoginTime", now);
		userMap.put("logo", super.getCurrentHostOrgPortalWebUrl(request) + "/sas/logo/app/"
				+ super.getCurrentHostOrg(request).getCode() + ".png");
		studentUserService.insertStudentLoginLog(studentUser.getId(), now, "APP");
		// 分主办单位统计在线用户
		CacheHelper.setCache(Constants.STUDENT_USER_LOGIN_CACHE + studentUser.getRegHostOrgId(),
				studentInfo.getStudentId(), request.getSession().getId());
		return userMap;
	}


	@RequestMapping("/getAllZypxType")
	@Operation(desc = "获取所有项目类别", loginRequired = false)
	public Object getAllZypxType(HttpServletRequest request){
		String currentHostOrgId = super.getCurrentHostOrgId(request);
		return typeService.getAllZypeType(currentHostOrgId);
	}

	@RequestMapping("/getXmByType")
	@Operation(desc = "通过项目类别获取项目", loginRequired = false)
	public Object getXmByType(String typeId,
							  HttpServletRequest request,
							  String name
							  ){
		String currentHostOrgId = super.getCurrentHostOrgId(request);
		return typeService.getXmByType(currentHostOrgId,typeId,name);
	}

	@RequestMapping("/getXmById")
	@Operation(desc = "获取所有项目类别", loginRequired = false)
	public Object getXmById(
							@RequestParam(required = true) String id
	){
		return typeService.getXmById(id);
	}

	@RequestMapping("/addAdvice")
	@Operation(desc = "添加新闻咨询", loginRequired = false)
	public Object addAdvice(HttpServletRequest request,
							HttpServletResponse response,
							@RequestParam(required = false) String name,
							@RequestParam(required = false) String mobile,
							@RequestParam(required = false) String xmId,
							@RequestParam(required = false) String content) {
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("电话号码不能为空");
		}
		if (StringUtils.isEmpty(content)) {
			throw BizException.withMessage("咨询内容不能为空");
		}
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		String studentId = super.getLoginStudentId(request);
		pxxmXmBmAdviceService.addAdvice(name,mobile,content,studentId,xmId);
		return true;
	}
	
	/********************** 新版功能 **********************/
	@RequestMapping("/detail")
	@Operation(desc = "项目详情")
	public Object detail(HttpServletRequest request, @RequestParam(required = false) String id) throws ParseException {
		Map<String, Object> data = new HashMap<String, Object>();
		String studentId = this.getLoginStudentId(request);
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		// 项目信息
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		// 项目信息，含须知
		data.put("zypxXm", zypxXm);

		// 当前项目的日程信息
		Date atDate = new Date();
		List<ZypxXmSchedule> schedules = zypxXmScheduleService.list(new ZypxXmCommonQueryParams(id));
		schedules.forEach(x -> x
				.setScheduleSign(x.getScheduleTime().before(atDate) ? ScheduleSign.DONE : ScheduleSign.NOT_START));
		schedules.stream().filter(x -> x.getScheduleSign() == ScheduleSign.DONE).reduce((first, second) -> second)
				.ifPresent(y -> y.setScheduleSign(ScheduleSign.AT));
		data.put("schedules", schedules.stream().filter(x->x.getScheduleSign() != ScheduleSign.DONE).collect(Collectors.toList()));

		// 获取最近的第一个通知
		ZypxBm zypxBm = mobileBmService.getBmInfo(id, studentId);
		List<ZypxXmNotice> notices = zypxXmNoticeService
				.pageQuery(new ZypxXmCommonQueryParams(id, XmNoticeStatus.PUBLISH, zypxBm.getClassId())).getRecords();
		data.put("notice", CollectionUtils.isNotEmpty(notices) ? notices.get(0) : new ZypxXmNotice());

		//获取公告未读数量
		List<ZypxXmNoticeRecord> zypxXmNoticeRecords = zypxXmNoticeRecordService.selectList((EntityWrapper<ZypxXmNoticeRecord>) new EntityWrapper<ZypxXmNoticeRecord>()
				.eq("xm_id", id).eq("student_id", super.getLoginStudentId(request)));
		long readCount = zypxXmNoticeRecords.stream().map(ZypxXmNoticeRecord::getXmNoticeId).distinct().count();
		data.put("notReadNoticeCount", notices.size() - readCount);

		// 培训课程
		List<Map<String, Object>> courses = studentBmMapper.getStudentBmCourseList(id, studentId);
		data.put("courses", courses);

		data.put("bmXms", zypxStudentBmService.getStudentBMCGXmList(studentId));
		return data;
	}

	@RequestMapping("/studentNotice")
	@Operation(desc = "项目须知")
	public Object studentNotice(HttpServletRequest request, @RequestParam(required = false) String id)
			throws ParseException {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		// 项目信息
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		return zypxXm.getStudentNotice();
	}

	@RequestMapping("/studentList")
	@Operation(desc = "学员名单")
	public Object studentList(HttpServletRequest request, @RequestParam(required = false) String id) {
		Map<String, Object> data = new HashMap<String, Object>();
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		String studentId = this.getLoginStudentId(request);
		// 项目信息，项目负责人信息
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
        if (!zypxStudentBmService.existsBmByStudentIdAndXmId(studentId, id)) {
			throw BizException.withMessage("暂未报名该项目");
        }
		// 项目负责人
		Map<String, Object> leader = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(zypxXm.getLeaderId())) {
			leader = userService.getById(zypxXm.getLeaderId());
		}
		data.put("leader", leader);

		// 项目报名的学员名单，找到当前
		List<Map<String, Object>> students = service.getStudentByXmId(id);
		students.stream().filter(x -> x.get("studentId").equals(studentId)).findFirst()
				.ifPresent(y -> y.put("isAt", 1));
		data.put("students", students);
		return data;
	}

	@RequestMapping("/arriveList")
	@Operation(desc = "培训报道")
	public Object arriveList(HttpServletRequest request, @RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		Map<String, Object> result = new HashMap<>();
		ZypxXm zypxXm = zypxXmService.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		result.put("poiaddress", zypxXm.getPoiaddress());//地址
		result.put("poilatlng", zypxXm.getPoilatlng());//坐标
		result.put("reportIntroduction", zypxXm.getReportIntroduction());//详情-富文本
		List<Map<String, Object>> students = service.arriveList(id);
		students.stream().filter(x -> x.get("studentId").equals(this.getLoginStudentId(request))).findFirst()
				.ifPresent(y -> y.put("isAt", 1));
		result.put("students", students);
		return result;
	}
	
	@RequestMapping("/scheduleList")
	@Operation(desc = "项目日程")
	public Object scheduleList(HttpServletRequest request, @RequestParam(required = false) String id)
			throws ParseException {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		Date atDate = new Date();
		List<ZypxXmSchedule> schedules = zypxXmScheduleService.list(new ZypxXmCommonQueryParams(id));
		schedules.forEach(x -> {
			Date scheduleTime = x.getScheduleTime();
			x.setScheduleYMD(DateUtils.format(scheduleTime, "yyyy-MM-dd"));
			x.setScheduleHM(DateUtils.format(scheduleTime, "HH:mm"));
			// 在当前时间之前：已结束，业务层计算当前
			x.setScheduleSign(scheduleTime.before(atDate) ? ScheduleSign.DONE : ScheduleSign.NOT_START);
		});
		schedules.stream().filter(x -> x.getScheduleSign() == ScheduleSign.DONE).reduce((first, second) -> second)
				.ifPresent(y -> y.setScheduleSign(ScheduleSign.AT));
		return schedules.stream().collect(Collectors.groupingBy(ZypxXmSchedule::getScheduleYMD));
	}

	@RequestMapping("/practiceAreaList")
	@Operation(desc = "实践区域")
	public Object practiceAreaList(HttpServletRequest request, @RequestParam(required = false) String id)
			throws ParseException {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return zypxXmPracticeAreaService.list(new ZypxXmCommonQueryParams(id));
	}

	@RequestMapping("/qaList")
	@Operation(desc = "调查问卷")
	public Object qaList(HttpServletRequest request, @RequestParam(required = false) String xmId)
			throws ParseException {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		String studentId = this.getLoginStudentId(request);
		List<Map<String, Object>> qaList = qaService.getQasByXmId(xmId);
		qaList.forEach(x -> {
			x.put("isRespond", zypxStudentCertiService.isQaAnswered(xmId, (String) x.get("id"), studentId));
		});
		return qaList;
	}

	@RequestMapping("/noticeList")
	@Operation(desc = "培训通知")
	public Object noticeList(HttpServletRequest request, @RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		// 获取报名
		ZypxBm zypxBm = mobileBmService.getBmInfo(id, this.getLoginStudentId(request));
		return zypxXmNoticeService.pageQuery(new ZypxXmCommonQueryParams(id, XmNoticeStatus.PUBLISH, zypxBm.getClassId())).getRecords();
	}

	@RequestMapping("/noticeDetail")
	@Operation(desc = "培训通知详情")
	public Object noticeDetail(HttpServletRequest request, @RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("通知id不能为空");
		}
		ZypxXmNotice zypxXmNotice = zypxXmNoticeService.selectById(id);
		if (BaseUtil.isEmpty(zypxXmNotice)) {
			throw BizException.withMessage("通知公告不存在");
		}
		//存已读记录
		Integer count = zypxXmNoticeRecordService.selectCount((EntityWrapper<ZypxXmNoticeRecord>) new EntityWrapper<ZypxXmNoticeRecord>()
				.eq("xm_id", zypxXmNotice.getXmId())
				.eq("student_id", super.getLoginStudentId(request))
				.eq("xm_notice_id", id));
		if (count == 0) {
			zypxXmNoticeRecordService.insert(new ZypxXmNoticeRecord(BaseUtil.generateId(), zypxXmNotice.getXmId(), id, super.getLoginStudentId(request), new Date()));
		}
		return zypxXmNotice;
	}

	@RequestMapping("/doTargetCourseList")
	@Operation(desc = "待评价面授课程列表")
	public Object doTargetCourseList(HttpServletRequest request, @RequestParam(required = false) String id) {
		if (StringUtils.isBlank(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return zypxXmCourseSettingService.doTargetCourseList(super.getLoginStudentId(request), id);
	}

	@RequestMapping("/getTeacherIntroduction")
	@Operation(desc = "获取师资简介")
	public Object getTeacherIntroduction(@RequestParam(required = false) String id) {
		if (StringUtils.isBlank(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = zypxXmService.selectById(id);
		return zypxXm.getTeacherIntroduction();
	}

	@RequestMapping("/getClassWork")
	@Operation(desc = "获取班级班委职责和小组工作内容")
	public Object getClassWork(@RequestParam(required = false) String id, @RequestParam(required = false) Integer type) {
		if (StringUtils.isBlank(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = zypxXmService.selectById(id);
		String result = null;
		switch (type) {
			case 0:
				result = zypxXm.getClassCommitteeWork();
				break;
			case 1:
				result = zypxXm.getGroupWork();
				break;
		}
		return result;
	}

	@RequestMapping("/getManagementTeam")
	@Operation(desc = "获取管理团队及联系方式")
	public Object getManagementTeam(@RequestParam(required = false) String id) {
		if (StringUtils.isBlank(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = zypxXmService.selectById(id);
		return zypxXm.getManagementTeam();
	}

	@RequestMapping("/getHotels")
	@Operation(desc = "获取食宿安排")
	public Object getHotels(@RequestParam(required = false) String id) {
		if (StringUtils.isBlank(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = zypxXmService.selectById(id);
		Map<String, Object> result = new HashMap<>();
		List<Map<String, Object>> hotels = hotelService.getByXmId(id);
		result.put("hotels", hotels);
		result.put("stayPlan", zypxXm.getStayPlan());
		Map<String, Object> eatPlan = new HashMap<>();
		eatPlan.put("plan", zypxXm.getEatPlan());
		eatPlan.put("point", zypxXm.getEatPoint());
		result.put("eatPlan", eatPlan);
		return result;
	}
}