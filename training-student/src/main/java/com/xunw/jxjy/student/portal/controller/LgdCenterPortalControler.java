package com.xunw.jxjy.student.portal.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.portal.service.IndexService;
import com.xunw.jxjy.model.portal.service.LgdIndexService;
import com.xunw.jxjy.model.portal.service.ZkService;
import com.xunw.jxjy.model.sys.entity.Notice;
import com.xunw.jxjy.model.sys.entity.NoticeCategory;
import com.xunw.jxjy.model.sys.entity.PortalXm;
import com.xunw.jxjy.model.sys.params.NoticeCategoryQueryParams;
import com.xunw.jxjy.model.sys.params.PortalXmQueryParams;
import com.xunw.jxjy.model.sys.service.NoticeCategoryService;
import com.xunw.jxjy.model.sys.service.NoticeService;
import com.xunw.jxjy.model.sys.service.PortalXmService;
import com.xunw.jxjy.student.core.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 理工大自考培训平台，统一登录门户
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/portal/center")
public class LgdCenterPortalControler extends BaseController {

    @Autowired
    private IndexService indexService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private LgdIndexService lgdIndexService;
    @Autowired
    private PortalXmService service;
    @Autowired
    private NoticeCategoryService noticeCategoryService;
    @Autowired
    private ZkService zkService;
     
    /**
     * 通知公告
     */
    @SuppressWarnings("rawtypes")
	@RequestMapping("/{hostOrgCode}/noticeIndex")
    public Object noticeIndex(HttpServletRequest request,
                              ModelAndView mv,
                              @RequestParam(defaultValue = "1") Integer pageNum,
                              @RequestParam(defaultValue = "10") Integer pageSize,
                              @RequestParam(required = false) String categoryName,
                              @RequestParam(required = false) String categoryId,
                              @RequestParam(required = false) String isZk,
                              @PathVariable String hostOrgCode) throws Exception {
        // 取出所有的类型
        List<Map<String, Object>> categorys = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        List<Map<String, Object>> zkCateogrys = zkService.getCateogrys();
        categorys.addAll(zkCateogrys);
        mv.addObject("categorys", categorys);

        Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
		if (StringUtils.isNotEmpty(categoryId) || StringUtils.isNotEmpty(categoryName)) {
			if (BaseUtil.isEmpty(isZk)) {
				NoticeCategoryQueryParams params = new NoticeCategoryQueryParams();
				params.setId(categoryId);
				params.setName(categoryName);
				params.setHostOrgId(super.getCurrentHostOrgId(request));
				params.setCurrent(1);
				params.setSize(Integer.MAX_VALUE);
				page = noticeCategoryService.pageQuery(params);
			} else {
				String newCategoryId = categoryId;
				List<Map<String, Object>> collect = zkCateogrys.stream()
						.filter(x -> (newCategoryId.equals(x.get("id")) || categoryName.equals(x.get("name"))))
						.collect(Collectors.toList());
				page = page.setRecords(collect);
			}
		}
        if (!page.getRecords().isEmpty()) {
            mv.addObject("activeCategory", page.getRecords().get(0));
            categoryId = page.getRecords().get(0).get("id").toString();
        } else {
            mv.addObject("activeCategory", categorys.get(0));
            categoryId = categorys.get(0).get("id").toString();
        }
        if (categorys.size() > 0) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("categoryId", categoryId);
			Page pageInfo = new Page();
			if (BaseUtil.isEmpty(isZk)) {
				pageInfo = indexService.getNotice(condition, pageNum, pageSize);
			} else {
				pageInfo = zkService.getNews(condition, pageNum, pageSize);
			}
            mv.addObject("noticeList", pageInfo.getRecords());
            mv.addObject("pageSize", pageSize);
            mv.addObject("pageNum", pageNum);
            mv.addObject("pages", pageInfo.getPages());
            mv.addObject("count", pageInfo.getTotal());
            mv.addObject("categoryName", categoryName);
        }
        mv.setViewName("pages/portal/center/news");
        return mv;
    }

    /**
     * 通知详情
     */
    @SuppressWarnings("unchecked")
	@RequestMapping("/{hostOrgCode}/noticeDetail")
    public Object noticeDetail(HttpServletRequest request,
                               ModelAndView mv,
                               String id,
                               @RequestParam(required = false) String isZk,
                               @PathVariable String hostOrgCode) throws Exception {
        // 所有类型
        List<Map<String, Object>> categorys = lgdIndexService.getNoticeSubType(getCurrentHostOrgId(request));
        List<Map<String, Object>> zkCateogrys = zkService.getCateogrys();
        categorys.addAll(zkCateogrys);
        mv.addObject("categorys", categorys);
        
        Page<Map<String, Object>> noticePages =new Page<Map<String, Object>>();
        if(BaseUtil.isEmpty(isZk)) {
        	Notice notice = noticeService.selectById(id);
            NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
            mv.addObject("activeCategory", noticeCategory);
            mv.addObject("notice", notice);
            Map<String, Object> condition = new HashMap<String, Object>();
            condition.put("categoryId", noticeCategory.getId());
            noticePages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
        }else {
        	Map<String, Object> notice = zkService.getNew(id);
        	Map<String, Object> noticeCategory = zkService.getCateogry((String)notice.get("categoryId"));
            mv.addObject("activeCategory", noticeCategory);
            mv.addObject("notice", notice);
            Map<String, Object> condition = new HashMap<String, Object>();
            condition.put("categoryId", (String)notice.get("categoryId"));
            noticePages = zkService.getNews(condition, 1, Integer.MAX_VALUE);
        }
        
        //获取上一条通知的ID、下一条通知的ID
        List<Map<String, Object>> allNotice = noticePages.getRecords();
        int index = 0;
        for (int i = 0; i < allNotice.size(); i++) {
            Map map = allNotice.get(i);
            if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Map<String, Object> lastNotice = allNotice.get(index - 1);
            mv.addObject("lastNoticeId", BaseUtil.getStringValueFromMap(lastNotice, "id"));
        }
        if (index < allNotice.size() - 1) {
            Map<String, Object> nextNotice = allNotice.get(index + 1);
            mv.addObject("nextNoticeId", BaseUtil.getStringValueFromMap(nextNotice, "id"));
        }
        mv.setViewName("pages/portal/center/newsDetail");
        return mv;
    }

    /**
     * 培训项目列表
     */
    @RequestMapping("/{hostOrgCode}/projectList")
    public Object projectList(HttpServletRequest request,
                              PortalXmQueryParams params,
                              @RequestParam(defaultValue = "1") Integer pageNum,
                              @RequestParam(defaultValue = "8") Integer pageSize,
                              String parentTypeId,
                              String typeId,
                              ModelAndView mv,
                              @PathVariable String hostOrgCode) {
        //处理左侧的查询条件树
        List<Map<String, Object>> level1Array = new ArrayList<>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.XM);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.XM));
        }
        //处理查询数据
        params.setCurrent(pageNum);
        params.setSize(pageSize);
        if (StringUtils.isNotEmpty(typeId)) {
            params.setTypeId(typeId);
        } else {
            params.setTypeId(StringUtils.isNotEmpty(parentTypeId) ? parentTypeId : null);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        Page page = service.pageQuery(params);
        mv.addObject("xmList", page.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("pages", page.getPages());
        mv.addObject("count", page.getTotal());
        mv.setViewName("pages/portal/center/project");
        return mv;
    }

    /**
     * 详情
     */
    @RequestMapping("/{hostOrgCode}/projectDetail")
    public Object getCourseDeatilById(@RequestParam String id,
                                      ModelAndView mv,
                                      @PathVariable String hostOrgCode) throws Exception {
        if (id == null) {
            throw BizException.withMessage("培训项目id不能为空");
        }
        PortalXm portalXm = service.selectById(id);
        if (portalXm == null) {
            throw BizException.withMessage("根据此项目id查询不到项目");
        }
        mv.addObject("portalXm", portalXm);
        mv.setViewName("pages/portal/center/projectDetail");
        return mv;
    }

    /**
     * 联系我们
     */
    @RequestMapping("/{hostOrgCode}/contactus")
    public Object contactusDetail(ModelAndView mv,
                                  @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/center/contactus");
        return mv;
    }

	/******** 自考相关 ***********/
    /**
     * @param keyword	专业关键字
     * @param s_edulevel	专业层次：A-专科、B-本科
     * @param s_subject_id	学科id
     */
	@SuppressWarnings("serial")
	@RequestMapping("/{hostOrgCode}/specialtys")
	public Object specialtys(String keyword, String s_edulevel, String s_subject_id,
			@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "8") Integer pageSize,
			ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
		Page<Map<String, Object>> page = zkService.getSpecialtys(new HashMap<String, Object>() {
			{
				put("keyword", keyword);
				put("s_edulevel", s_edulevel);
				put("s_subject_id", s_subject_id);
			}
		}, pageNum, pageSize);
		mv.addObject("specialtys", page.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("pages", page.getPages());
		mv.addObject("count", page.getTotal());
		mv.addObject("activeEdulevel", s_edulevel);
		mv.addObject("activeSubjectId", s_subject_id);
		mv.addObject("keyword", keyword);
		// 学科
		mv.addObject("subjects", zkService.getSubjects());
		mv.setViewName("pages/portal/center/zkSpeciality");
		return mv;
	}

	@RequestMapping("/{hostOrgCode}/specialtyDetail")
	public Object specialtyDetail(@RequestParam String s_id, ModelAndView mv, @PathVariable String hostOrgCode) {
		if (BaseUtil.isEmpty(s_id)) {
			throw BizException.withMessage("专业id不能为空");
		}
		Map<String, Object> specialty = zkService.getSpecialty(s_id);
		if (specialty == null) {
			throw BizException.withMessage("专业不存在");
		}
		mv.addObject("specialty", specialty);
		mv.setViewName("pages/portal/center/zkSpecialityDetail");
		return mv;
	}

}