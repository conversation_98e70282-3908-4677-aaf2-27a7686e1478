package com.xunw.jxjy.student.mobile.controller;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.tk.service.DbPracticeService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 职业培训---题库练习
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/dbPractice")
public class MobileDbPracticeController extends BaseController {

    @Autowired
    private DbPracticeService dbPracticeService;

    @RequestMapping("/getDbPractice")
    @Operation(desc = "获取题库练习的题库列表")
    public Object getDbPractice(HttpServletRequest request,  @RequestParam(required = false) String xmId) {
        return dbPracticeService.getDbPractice(xmId, super.getLoginStudentId(request));
    }

    /**
     * 保存刷题记录
     */
    @RequestMapping("/save")
    @Operation(desc = "保存刷题记录")
    public Object save(HttpServletRequest request,
                       @RequestParam(required = false) String dbPracticeId,
                       @RequestParam(required = false) String questionId,
                       @RequestParam(required = false) String answer) {
        if (StringUtils.isEmpty(dbPracticeId)) {
            throw BizException.withMessage("练习id不能为空");
        }
        if (StringUtils.isEmpty(questionId)) {
            throw BizException.withMessage("题目id不能为空");
        }
        if (StringUtils.isEmpty(answer)) {
            throw BizException.withMessage("答案不能为空");
        }
        return dbPracticeService.save(dbPracticeId, questionId, answer, super.getLoginStudentId(request));
    }

    /**
     * 获取题库练习试题
     */
    @RequestMapping("/getPracticeQuestions")
    @Operation(desc = "获取题库练习试题")
    public Object getPracticeQuestion(HttpServletRequest request,
                                      @RequestParam(required = false) String dbPracticeId,
                                      @RequestParam(required = false, defaultValue = "1") Integer current,
                                      @RequestParam(required = false, defaultValue = "10") Integer size) {
        if (StringUtils.isEmpty(dbPracticeId)) {
            throw BizException.withMessage("练习id不能为空");
        }
        return dbPracticeService.getPracticeQuestions(dbPracticeId, current, size, super.getLoginStudentId(request));
    }

    /**
     * 题库练习重做
     */
    @RequestMapping("/redo")
    @Operation(desc = "题库练习重做")
    public Object redo(HttpServletRequest request,
                       @RequestParam(required = false) String dbPracticeId) {
        if (StringUtils.isEmpty(dbPracticeId)) {
            throw BizException.withMessage("练习id不能为空");
        }
        dbPracticeService.redo(dbPracticeId, super.getLoginStudentId(request));
        return true;
    }
}
