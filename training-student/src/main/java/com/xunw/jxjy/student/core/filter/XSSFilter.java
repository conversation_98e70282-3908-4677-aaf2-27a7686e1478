package com.xunw.jxjy.student.core.filter;

import cn.hutool.http.HtmlUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.regex.Pattern;

public class XSSFilter implements Filter {

    private static Pattern pattern = Pattern.compile("(<script>(.*?)</script>)|(<iframe>(.*?)</iframe>)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            chain.doFilter(new XSSRequestWrapper(httpServletRequest), response);
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {

    }

    private static class XSSRequestWrapper extends HttpServletRequestWrapper {

        private HttpServletRequest request;

        public XSSRequestWrapper(HttpServletRequest request) {
            super(request);
            this.request = request;
        }

        @Override
        public String getHeader(String name) {
            return cleanXSS(request.getHeader(name));
        }

        @Override
        public String getQueryString() {
            return cleanXSS(request.getQueryString());
        }

        @Override
        public String getParameter(String name) {
            return cleanXSS(request.getParameter(name));
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] values = request.getParameterValues(name);
            if (values != null) {
                int count = values.length;
                String[] encodedValues = new String[count];
                for (int i = 0; i < count; i++) {
                    encodedValues[i] = cleanXSS(values[i]);
                }
                return encodedValues;
            }
            return super.getParameterValues(name);
        }

        private String cleanXSS(String value) {
            if (value != null) {
                return HtmlUtil.cleanHtmlTag(value);
            }
            return value;
        }
    }
}