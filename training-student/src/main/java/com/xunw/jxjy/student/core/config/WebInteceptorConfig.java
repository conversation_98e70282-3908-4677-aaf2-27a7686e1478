package com.xunw.jxjy.student.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.xunw.jxjy.student.core.inteceptor.WebLoginInterceptor;
import com.xunw.jxjy.student.core.inteceptor.WebPropertyResolveInterceptor;

/**
 * 拦截器 配置
 * <AUTHOR>
 *
 */
@Configuration
public class WebInteceptorConfig extends WebMvcConfigurerAdapter {

	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/**")
        .allowedOrigins("*")
        .allowedMethods("GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "TRACE")
        .allowedHeaders("*")
        .allowCredentials(true).maxAge(3600);
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		
		/**
		 * 登录拦截器
		 */
		registry.addInterceptor(new WebLoginInterceptor()).
		addPathPatterns(
				"/kaosheng/**",
				"/personal/**"
		).
		excludePathPatterns(
				"/kaosheng/mobile/zyjd/**"
		);
		
		/**
		 * freemarker模板页面全局变量注入拦截器
		 */
		registry.addInterceptor(new WebPropertyResolveInterceptor()).
		addPathPatterns(
				"/**",
				"/portal/**",
				"/personal/**",
				"/portal/center/**",
				"/kaosheng/mobile/zyjd/**");
	}

}
