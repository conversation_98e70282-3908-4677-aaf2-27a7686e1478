package com.xunw.jxjy.student.comm.controller;

import java.awt.image.BufferedImage;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.sys.dto.RegionTree;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;

import net.sf.json.JSONObject;

/**
 * 通用接口
 */
@RestController
public class CommonController extends BaseController {

	@Autowired
	private DictService service;
	@Autowired
	private AttConfig attConfig;

	/**
	 * 通用文件上传接口
	 */
	@RequestMapping("/kaosheng/common/uploadFile")
	@Operation(desc = "通用文件上传接口")
	public Object uploadFile(HttpServletRequest request, @RequestParam(required = false) String type,
			@RequestParam(required = false, value = "file") MultipartFile file) throws Exception {
		if (file == null) {
			throw BizException.withMessage("在请求中没有检测到文件");
		}
		if ("xcpz".equals(type)) {
			BufferedImage image = ImageIO.read(file.getInputStream());
			if (image.getWidth() > 500) {
				throw BizException.withMessage("图片的宽度必须小于500像素");
			}
			if (image.getHeight() > 700) {
				throw BizException.withMessage("图片的高度必须小于700像素");
			}
		}
		String ext = FileHelper.getExtension(file.getOriginalFilename());
		String path = attConfig.getRootDir() + "/upload/file/"
				+ new SimpleDateFormat("yyyyMMddHH").format(new Date());
		String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
		String url = FileHelper.storeFile(path, file.getInputStream(), newFileName);
		Map<String, Object> rst = new HashMap<>();
		rst.put("url", url);
		return rst;
	}

	/**
	 * 通用文件上传接口 不登陆，限制最大50M
	 */
	@RequestMapping("/kaosheng/common/uploadFileNoLogin")
	@Operation(desc = "通用文件上传接口", loginRequired = false)
	public Object uploadFile(HttpServletRequest request, @RequestParam(required = false, value = "file") MultipartFile file) throws Exception {
		if (file == null) {
			throw BizException.withMessage("在请求中没有检测到文件");
		}
		if (file.getSize() > 50 * 1024 * 1024) {
			throw BizException.withMessage("文件大小不能超过50M");
		}
		String ext = FileHelper.getExtension(file.getOriginalFilename());
		String path = attConfig.getRootDir() + "/upload/file/"
				+ new SimpleDateFormat("yyyyMMddHH").format(new Date());
		String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
		String url = FileHelper.storeFile(path, file.getInputStream(), newFileName);
		Map<String, Object> rst = new HashMap<>();
		rst.put("url", url);
		return rst;
	}

	/**
	 * 通用图片上传接口
	 */
	@RequestMapping("/kaosheng/uploadImage")
    @Operation(desc = "通用图片文件上传接口(不支持Base64)")
    public Object uploadImage(
            HttpServletRequest request, @RequestParam(required = false,value = "image") MultipartFile file,
            @RequestParam(required = false) Boolean isXcpz) throws Exception {
        if(file== null){
            throw BizException.withMessage("在请求中没有检测到图片");
        }
        if(isXcpz !=null && isXcpz) {
        	  BufferedImage image = ImageIO.read(file.getInputStream());
              if (image.getWidth() != 151) {
                  throw BizException.withMessage("图片的宽度必须是151px");
              }
              if (image.getHeight() != 201) {
                  throw BizException.withMessage("图片的高度必须是201px");
              }
        }
        String ext = FileHelper.getExtension(file.getOriginalFilename());
        String path = attConfig.getRootDir()+"/upload/images/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext.toLowerCase();
        String url = FileHelper.storeFile(path, file.getInputStream(), newFileName);
        Map<String,Object> rst = new HashMap<>();
        rst.put("url", url);
        return rst;
	}
	
	/**
	 * 图片上传(base64),注意此接口不做登录拦截 田军
	 */
	@RequestMapping("/kaosheng/uploadBase64")
	@Operation(desc = "图片上传(base64)",loginRequired = false)
	public Object storageZP(HttpServletRequest request,
							@RequestBody JSONObject params) throws Exception{
		String base64Str = BaseUtil.getStringValueFromJson(params, "base64Str");
		if (StringUtils.isEmpty(base64Str)){
			throw BizException.withMessage("未获取到抓拍图片");
		}
		String url = FileHelper.convertImgBase64SrcStrToUrl(base64Str);
		return url;
	}
	
	/**
	 * 根据字典编码查询集合
	 */
	@RequestMapping("/kaosheng/common/dict/select")
	@Operation(desc = "根据字典编码查询集合",loginRequired = false)
	public Object select(HttpServletRequest request, @RequestParam(required = false) String dictCode) throws Exception {
		if (StringUtils.isEmpty(dictCode)) {
			throw BizException.withMessage("字典编码不能够为空");
		}
		return service.getStudentDictByDictCode(dictCode);
	}

	/**
	 * 字典查询所有省份
	 */
	@RequestMapping("/kaosheng/common/dict/selectProvince")
	@Operation(desc = "根据字典编码查询所有省",loginRequired = false)
	public Object selectProvince(HttpServletRequest request) {
		List<Dict> list = service.getProvinceByDictCode();
		List<Map<String, Object>> relist = new ArrayList<>();
		for (Dict dict : list) {
			Map<String, Object> map = new HashMap<>();
			if (dict != null) {
				map.put("code", dict.getDictValue());
				map.put("name", dict.getDictName());
				relist.add(map);
			}
		}
		return relist;
	}

	/**
	 * 根据省查市
	 */
	@RequestMapping("/kaosheng/common/dict/selectCity")
	@Operation(desc = "根据省查市",loginRequired = false)
	public Object selectCity(HttpServletRequest request, @RequestParam(required = false) String provinceId) {
		if (StringUtils.isEmpty(provinceId)) {
			throw BizException.withMessage("省级地区不能为空");
		}
		List<Dict> list = service.getChildrenByParent(provinceId);
		List<Map<String, Object>> relist = new ArrayList<>();
		for (Dict dict : list) {
			Map<String, Object> map = new HashMap<>();
			if (dict != null) {
				map.put("code", dict.getDictValue());
				map.put("name", dict.getDictName());
				relist.add(map);
			}
		}
		return relist;
	}

	/**
	 * 根据市查区
	 */
	@RequestMapping("/kaosheng/common/dict/selectDistrict")
	@Operation(desc = "根据市查区",loginRequired = false)
	public Object selectDistrict(HttpServletRequest request, @RequestParam(required = false) String cityId) {
		if (StringUtils.isEmpty(cityId)) {
			throw BizException.withMessage("市级地区不能为空");
		}
		List<Dict> list = service.getChildrenByParent(cityId);
		List<Map<String, Object>> relist = new ArrayList<>();
		for (Dict dict : list) {
			Map<String, Object> map = new HashMap<>();
			if (dict != null) {
				map.put("code", dict.getDictValue());
				map.put("name", dict.getDictName());
				relist.add(map);
			}
		}
		return relist;
	}

	/**
	 * 提供地区树给APP使用
	 */
	@RequestMapping("/kaosheng/common/dict/getRegionTree")
	@Operation(desc = "查询地区树APP使用",loginRequired = false)
	public Object getRegionTree(HttpServletRequest request) {
		List<RegionTree> list = service.getRegionTree();
		return list;
	}
}
