package com.xunw.jxjy.student.mobile.controller;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.coursems.Chapter;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.common.coursems.Lesson;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.learning.entity.MsArrive;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxBmCourse;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxMsMarkArriveService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.paper.utils.ModelHelper;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 考生端-面授
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/ms")
public class MobileMsController extends BaseController {

	@Autowired
	private ZypxXmCourseSettingService service;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private ZypxMsMarkArriveService msMarkArriveService;
	@Autowired
	private ZypxXmCourseSettingService xmCourseSettingService;
	@Autowired
	private ZypxBmService bmService;

	@RequestMapping("/getLearnContent")
	@Operation(desc = "获取面授学习章节的详细内容")
	public Object hq(HttpServletRequest request,
			@RequestParam(required = false) String courseSettingId) throws Exception {
		if (StringUtils.isEmpty(courseSettingId)) {
			throw BizException.withMessage("请选择一条数据");
		}
		return service.getMsLearnContent(courseSettingId, getLoginStudentId(request));
	}
	
	@RequestMapping("/doArrive")
	@Operation(desc = "面授线上签到")
	public Object markArrive(HttpServletRequest request,String poiaddress,String poiname,
			String poilng, String courseSettingId,String chapterId,String lessonId) {
		StudentUser studentUser= studentUserService.selectById(super.getLoginStudentId(request));
		if (studentUser == null) {
			throw BizException.withMessage("签到失败,因为学员用户在系统中不存在");
		}
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
		ZypxBmCourse zypxBmCourse = msMarkArriveService.getBmCourseByStudentIdAndCourseSettingId(studentUser.getId(), courseSettingId);
		if (zypxBmCourse == null) {
			throw BizException.withMessage("签到失败,因为您并未报名该课程");
		}
		ZypxBm zypxBm = bmService.selectById(zypxBmCourse.getBmId());
		if (zypxBm == null) {
			throw BizException.withMessage("签到失败,因为您并未报名该项目");
		}
		ZypxXmCourseSetting zypxXmCourseSetting = xmCourseSettingService.selectById(courseSettingId);
		String content = zypxXmCourseSetting.getMsContent();
		CourseMs courseMs = ModelHelper.convertObject(content);
		Chapter currentChapter = null;
		Lesson currentLesson = null;
		for (Chapter chapter : courseMs.getChapters()) {
			for (Lesson lesson : chapter.getLessons()) {
				if (lesson.getId().equals(lessonId)) {
					currentLesson = lesson;
					currentChapter = chapter;
					break;
				}
			}
			if (currentLesson != null) {
				break;
			}
		}
		//允许签到时间是前后30分钟
		Date currentTime = new Date();
		if (currentTime.getTime() >= (currentLesson.getStartTime().getTime()-30*60*1000) && 
				currentTime.getTime() <= (currentLesson.getStartTime().getTime()+30*60*1000)) {
			MsArrive msArrive = new MsArrive();
			msArrive.setId(BaseUtil.generateId2());
			msArrive.setXmId(zypxBm.getXmId());
			msArrive.setStudentId(studentUser.getId());
			msArrive.setCourseId(zypxXmCourseSetting.getCourseId());
			msArrive.setChapterId(chapterId);
			msArrive.setLessonId(lessonId);
			msArrive.setIsOnline(Constants.YES);
			msArrive.setPoiaddress(poiaddress);//签到地址
			msArrive.setPoilatlng(poilng);//签到的坐标
			msArrive.setPoiname(poiname);//签到地标
			msMarkArriveService.doArrive(msArrive);
			return "您好！"+studentInfo.getName()+"，您已经签到成功了哦！";
		}
		else {
			throw BizException.withMessage("不在签到的时间范围内，仅允许开课前后30分钟内可以签到");
		}
	}
}
