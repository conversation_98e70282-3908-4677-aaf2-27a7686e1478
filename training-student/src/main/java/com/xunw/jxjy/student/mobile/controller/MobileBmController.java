package com.xunw.jxjy.student.mobile.controller;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.xunw.jxjy.model.common.entity.CommClass;
import com.xunw.jxjy.model.common.service.CommClassService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.service.CommOrderService;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.enums.BmStatus;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.enums.XmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.service.FormDataService;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.mobile.service.ZypxMobileBmService;
import com.xunw.jxjy.model.personal.params.StudentBmQueryParams;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmMaterial;
import com.xunw.jxjy.model.zypx.service.ZypxBmCountLimitService;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxXmMaterialService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

/**
 * 职业培训---报名
 * <AUTHOR>
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/bm")
public class MobileBmController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(MobileBmController.class);

	@Autowired
	private ZypxMobileBmService service;
	@Autowired
	private ZypxTypeService typeService;
	@Autowired
	private ZypxXmService xmService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private ZypxBmService bmService;
	@Autowired
	private FormService formService;
	@Autowired
	private ZypxXmMaterialService xmMaterialService;
	@Autowired
	private ZypxStudentBmService studentBmService;
	@Autowired
	private CommClassService commClassService;

	/**
	 * 获取当前学员可以报名的培训项目
	 */
	@RequestMapping("/getChooseXmList")
    @Operation(desc = "获取当前学员可以报名的培训项目")
	public Object getChooseXmList(HttpServletRequest reqest,String typeId) throws ParseException {
		StudentBmQueryParams params = new StudentBmQueryParams();
		params.setTypeId(typeId);
		params.setHostOrgId(getCurrentHostOrgId(reqest));
		params.setStudentId(getLoginStudentId(reqest));
		List<Map<String, Object>> xmList = service.getChooseXmList(params);
		return xmList;
	}

	/**
	 * 查询学员已经报名的培训项目
	 */
	@RequestMapping("/list")
    @Operation(desc = "查询学员已经报名的培训项目")
	public Object query(HttpServletRequest reqest) throws ParseException {
		LoginStudent loginStudent = super.getLoginStudent(reqest);
		StudentBmQueryParams params = new StudentBmQueryParams();
		params.setStudentId(loginStudent.getUser().getId());
		params.setHostOrgId(super.getCurrentHostOrgId(reqest));
		return service.getStudentBmXmList(params);
	}

	@RequestMapping("/getTypeTree")
    @Operation(desc = "获取项目类型下拉列表",loginRequired = false)
	public Object pcList(HttpServletRequest request,
						 @RequestParam(required = false) TypeCategory category) {
		return typeService.generateTreeSelect(getCurrentHostOrgId(request),category);
	}

	@RequestMapping("/chooseCourse")
	@Operation(desc = "获取培训项目")
	public Object chooseCourse(HttpServletRequest request,String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目ID不能为空");
		}
		StudentBmQueryParams params = new StudentBmQueryParams();
		params.setXmId(id);
		//获取可以个人报名的项目
		List<Map<String, Object>> list = service.getChooseXmList(params);
		if (list.size() == 0) {
			throw BizException.withMessage("当前项目暂未开放个人报名,请检查管理端当前项目的报名设置是否正确");
		}
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> xmMap =  list.size() > 0 ? list.get(0) : null;
		result.put("xm", xmMap);
		List<Map<String, Object>> courseList = service.getCourseByXmId(id);
		List<String> courseIdList = service.getStudentBmCourseSettingIdList(id, super.getLoginStudentId(request));
		ZypxBm zypxBm = service.getBmInfo(id, getLoginStudentId(request));
		String isJtbm = zypxBm!=null?zypxBm.getIsJtbm():null;
		result.put("isJtbm",isJtbm);
		result.put("courseList", courseList);//项目下的所有的课程
		result.put("bmCourseIdList", courseIdList);//当前学员已报名的课程，此处是课程设置ID
		return result;
	}

	@RequestMapping("/getXmDetails")
    @Operation(desc = "获取项目详情")
	public Object getXmDetails(HttpServletRequest request,String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目ID不能为空");
		}
		Map<String, Object> result = new HashMap<String, Object>();
		ZypxXm zypxXm = xmService.selectById(id);
		EntityWrapper<ZypxXmMaterial> entityWrapper = new EntityWrapper();
		entityWrapper.eq("xm_id",id);
		entityWrapper.eq("category",Constants.XmMaterialCategory.STUDENT_STUDY);
		Integer bmCount= bmService.getBmCountByXmId(id);
		List zypxXmMaterialList = xmMaterialService.selectList(entityWrapper);
		List<Map<String, Object>> courseList = service.getCourseByXmId(id);
		ZypxType zypxType = typeService.selectById(zypxXm.getTypeId());
		result.put("id",zypxXm.getId());
		result.put("title",zypxXm.getTitle());
		result.put("serialNumber",zypxXm.getSerialNumber());
		result.put("trainees",zypxXm.getTrainees());
		result.put("typeId",zypxXm.getTypeId());
		if (zypxType != null) {
			result.put("typeName",zypxType.getName());
		}
		result.put("amount",zypxXm.getAmount());
		result.put("isMustFillPersonalInfo",zypxXm.getIsMustFillPersonalInfo());
		result.put("introduce",zypxXm.getNotice());
		result.put("formId",formService.getFormByXmId(id));
		String studentId = getLoginStudentId(request);
		//如果用户已经登录，判断是否报名了
		if(StringUtils.isNotEmpty(studentId)) {
			ZypxBm zypxBm = studentBmService.getBmInfo(id, studentId);
			result.put("bmId", zypxBm != null ? zypxBm.getId() : null);
		}
		result.put("count",bmCount);
		result.put("startTime",zypxXm.getStartTime());
		result.put("endTime",zypxXm.getEndTime());
		result.put("courses", courseList);//项目下的所有的课程
		result.put("materials",zypxXmMaterialList);//项目下的所有资料
		return result;
	}

	/**
	 * 报名
	 */
	@RequestMapping("/bmconfirm")
	@Operation(desc = "学员报名")
	public Object confirm(HttpServletRequest request, @RequestParam(required = false) String xmId,
						  @RequestParam(required = false) String courseSettingId,@RequestParam(required = false) String inviteCode) {
		String studentId = super.getLoginStudentId(request);
		//前置校验
		service.checkBmInfoCanBeSaved(xmId, studentId, inviteCode, request.getSession());
		service.createStudentBm(studentId, xmId, courseSettingId, request.getSession());
		return true;

	}

	/**
	 * 单独的选择课程接口
	 */
	@RequestMapping("/chooseBmCourse")
	@Operation(desc = " 单独的选择课程接口")
	public Object chooseBmCourse(HttpServletRequest request, @RequestParam(required = false) String xmId,
						  @RequestParam(required = false) String courseSettingId) {
		String studentId = super.getLoginStudentId(request);
		//前置校验
		service.chooseBmCourse(studentId, xmId, courseSettingId);
		return true;
	}


	/**
	 * 获取学员已报名培训项目的明细数据，如课程、学习方式、成绩等信息
	 */
	@RequestMapping("/bmXmDetail")
	@Operation(desc = "获取学员项目下的培训课程")
	public Object bmXmStudyDetail(HttpServletRequest request,
			@RequestParam(required = false) String xmId) throws Exception{
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请传入培训项目ID");
		}
		String loginUserId = super.getLoginStudentId(request);
		StudentBmQueryParams params = new StudentBmQueryParams();
		params.setStudentId(loginUserId);
		params.setXmId(xmId);
		Map<String, Object> result = service.getStudentBmCourseDetails(params);
		return result;
	}

	/**
	 * 移动端 检查学员的项目报名费的缴费状态
	 */
	@RequestMapping("/checkPay")
	@Operation(desc = "检查学员的缴费状态")
	public Object checkPay(HttpServletRequest request,String bmId) {
		Map<String, Object> result = new HashMap<String, Object>();
		ZypxBm zypxBm = service.selectById(bmId);
		if (zypxBm == null) {
			throw BizException.withMessage("报名信息不存在");
		}
		ZypxXm zypxXm = xmService.selectById(zypxBm.getXmId());
		result.put("isPayed", zypxBm.getIsPayed());
		String isAlllowStudyBeforePay = zypxXm.getIsAlllowStudyBeforePay();
		isAlllowStudyBeforePay = StringUtils.isEmpty(isAlllowStudyBeforePay) ? Constants.YES : isAlllowStudyBeforePay;
		result.put("isAlllowStudyBeforePay", isAlllowStudyBeforePay);

		String isAlllowExamBeforePay = zypxXm.getIsAlllowExamBeforePay();
		isAlllowExamBeforePay = StringUtils.isEmpty(isAlllowExamBeforePay) ? Constants.YES : isAlllowExamBeforePay;
		result.put("isAlllowExamBeforePay", isAlllowExamBeforePay);

		return result;
	}

	/**
	 * 移动端 申请发票信息
	 */
	@RequestMapping("/applyInvoice")
	@Operation(desc = "申请发票信息")
	public Object getInvoice(HttpServletRequest request,@RequestParam(required = false) String xmId) {
		String studentId = super.getLoginStudentId(request);
		ZypxBm zypxBm = service.getBmInfo(xmId,studentId);
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentId);
		if (StringUtils.isEmpty(studentInfo.getInvoiceTitle())) {
			throw new BizException(BizException.INVOICE_PERFECT_MESSAGE.getCode(), "请先完善发票信息");
		}
		if (!Constants.YES.equals(zypxBm.getIsPayed())) {
			throw new BizException("未缴费,无法申请发票");
		}
		if (Constants.YES.equals(zypxBm.getIsApplyedInvoice())) {
			throw new BizException("您已申请开票，请勿重复申请");
		}
		zypxBm.setIsApplyedInvoice(Constants.YES);
		zypxBm.setIsMarkInvoice(Constants.YES);
		zypxBm.setInvoiceApplyedTime(new Date());
		zypxBm.setInvoiceAmount(zypxBm.getPayedAmount());
		service.updateById(zypxBm);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("isApplyedInvoice", zypxBm.getIsApplyedInvoice());
		result.put("invoiceApplyedTime", zypxBm.getInvoiceApplyedTime());
		return result;
	}

	/**
     *  撤销项目报名
     */
    @RequestMapping("/revoke")
    @Operation(desc = "撤销项目报名")
    public Object revoke(HttpServletRequest request,
    		@RequestParam(required = false) String bmId){
    	service.revoke(bmId,super.getLoginStudentId(request));
    	return true;
    }

	@RequestMapping("/getClassWork")
	@Operation(desc = "获取小组工作内容")
	public Object getClassWork(@RequestParam(required = false) String classId) {
		if (StringUtils.isBlank(classId)) {
			throw BizException.withMessage("班级id不能为空");
		}
		CommClass commClass = commClassService.selectById(classId);
		if(commClass == null){
			throw BizException.withMessage("班级不存在");
		}
		return commClass.getGroupWork();
	}


}
