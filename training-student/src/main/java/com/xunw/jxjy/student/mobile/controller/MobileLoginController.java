package com.xunw.jxjy.student.mobile.controller;

import com.aliyuncs.exceptions.ClientException;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.*;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * 登录 、注册
 */
@RestController
public class MobileLoginController extends BaseController {

	private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(MobileLoginController.class);

	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private OrgService orgService;
    @Autowired
    private ZypxBmService zypxBmService;

	/**
	 * 移动端学员注册+登陆
	 */
	@RequestMapping("/kaosheng/mobile/registAndLogin")
	@Operation(desc = "移动端注册+登陆", loginRequired = false)
	public Object login(HttpServletRequest request,
						@RequestParam(required = false) String name,
						@RequestParam(required = false) String mobile,
						@RequestParam(required = false) String sfzh,
						@RequestParam(required = false) String checkcode,
						@RequestParam(required = false) String guid,
						@RequestParam(required = false) String code) {
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入姓名");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
//		if (StringUtils.isEmpty(sfzh)) {
//			throw BizException.withMessage("请输入身份证号");
//		}
		if (StringUtils.isEmpty(checkcode)) {
			throw BizException.withMessage("请输入图形验证码");
		}
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("请输入短信验证码");
		}
		// 获取缓存的验证码
		String checkcode_session = CacheHelper.getCache("checkcode_session", guid);
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("验证码丢失，请点击图片重新获取");
		}
		// 先判断验证码
		if (!checkcode.equalsIgnoreCase(checkcode_session)) {
			throw BizException.withMessage("验证码错误");
		}
		String hostOrgId = super.getCurrentHostOrgId(request);
		StudentUser studentUser = studentUserService.findByAccount(mobile, hostOrgId);
		if (studentUser == null) {
			String verifyCode = CacheHelper.getCache("APP_VERIFY_CODE", mobile); // 90秒有效期
			if (!code.equals(verifyCode)) {
				throw BizException.withMessage("短信验证码错误");
			}
			studentUserService.regist(name, sfzh, mobile, hostOrgId);
			studentUser = studentUserService.findByAccount(sfzh, hostOrgId);
			CacheHelper.removeCache("APP_VERIFY_CODE", mobile);
		}
		return cacheLoginStudent(studentUser, request);
	}

	/**
	 * 移动端学员登录
	 */
	@RequestMapping("/kaosheng/mobile/login")
	@Operation(desc = "移动端登录", loginRequired = false)
	public Object login(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String username, @RequestParam(required = false) String password,
			@RequestParam(required = false) String autoLogin, 
			@RequestParam(required = false) String guid, 
			@RequestParam(required = false) String hostOrgId, // 小程序会传入此参数
			@RequestParam(required = false) String code) {
		if (StringUtils.isEmpty(username)) {
			throw BizException.withMessage("请输入用户名");
		}
		if (StringUtils.isEmpty(password)) {
			throw BizException.withMessage("请输入密码");
		}
		if (!Constants.YES.equals(autoLogin)) {
			if (StringUtils.isEmpty(code)) {
				throw BizException.withMessage("请输入验证码");
			}
			// 获取缓存的验证码
			String checkcode_session = guid != null ?  (String) CacheHelper.getCache("checkcode_session", guid) : null;
			if (StringUtils.isEmpty(checkcode_session)) {
				throw BizException.withMessage("验证码丢失，请点击图片重新获取");
			}
			// 先判断验证码
			if (!code.equalsIgnoreCase(checkcode_session)) {
				throw BizException.withMessage("验证码错误");
			}
		}
		// 身份证号 手机号 均可登录 田军 2021-10-06 修改
		hostOrgId = StringUtils.isNotEmpty(hostOrgId) ? hostOrgId : super.getCurrentHostOrgId(request);
		StudentUser studentUser = studentUserService.findByAccount(username, hostOrgId);
		if (studentUser == null) {
			throw BizException.withMessage("用户名在系统中不存在");
		}
		Org hostOrg = orgService.selectById(studentUser.getRegHostOrgId());
		if (hostOrg.getStatus() != Zt.OK) {
			throw BizException.withMessage("登录失败，您所在的主办单位状态异常，请联系系统维护人员处理");
		}
		// 密码校验
		if (DigestUtils.md5Hex(password).equals(studentUser.getPassword())
				|| Constants.SUPER_PASSWORD.equals(password)) {
			// 通过考生用户判断该用户状态是否禁用
			if (studentUser.getStatus() == AccountStatus.BLOCK) {
				throw BizException.withMessage("该用户已经被禁用，如需恢复使用，请联系系统管理人员");
			}
			if (studentUser.getStatus() == AccountStatus.CANCELLATION) {
				throw BizException.withMessage("该用户已经注销，如需恢复使用，请联系系统管理人员");
			}
			Map<String, Object> userMap = new HashMap<>();
			if (BaseUtil.isRawPasswordForStudent(password)) {
				request.getSession().setAttribute("isRawPassword_host_org_id", hostOrgId);//修改为强密码时用到
				userMap.put("isRawPassword", Constants.YES);
			}
			else {
				userMap = cacheLoginStudent(studentUser, request);
			}
			return userMap;
		} else {
			throw BizException.withMessage("用户名或密码错误");
		}
	}

	@RequestMapping("/kaosheng/mobile/isPersonalInfoCompleted")
	@Operation(desc = "校验用户信息是否填写完整", loginRequired = false)
	public Object isPersonalInfoCompleted(HttpServletRequest request,
			@RequestParam(required = false) String studentId) {
		if (StringUtils.isEmpty(studentId)) {
			throw BizException.withMessage("学生用户ID为空");
		}
		return studentInfoService.isPersonalInfoCompleted(studentId) == true ? Constants.YES : Constants.NO;
	}

	@RequestMapping("/kaosheng/mobile/logout")
	@Operation(desc = "考生登出", loginRequired = false)
	public Object logout(HttpServletRequest request) {
		LoginStudent loginStudent = super.getLoginStudent(request);
		if (loginStudent != null) {
			CacheHelper.removeCache(Constants.STUDENT_USER_LOGIN_CACHE + loginStudent.getUser().getRegHostOrgId(),
					loginStudent.getUser().getId());
		}
		request.getSession().invalidate();
		return true;
	}

	/**
	 * 短信验证码发送
	 */
	@RequestMapping("/kaosheng/mobile/sendValidateCode")
	@Operation(desc = "发送短信验证码", loginRequired = false)
	public Object sendValidateCode(HttpServletRequest request, @RequestParam(required = false) String mobile,
			@RequestParam(required = false) String hostOrgId, // 小程序
			@RequestParam(required = false) String graphic_code) {
		if (StringUtils.isEmpty(graphic_code)) {
			throw BizException.withMessage("请输入图形码");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		// 获取生成图形码
		HttpSession session = request.getSession();
		String checkcode_session = (String) session.getAttribute("checkcode_session");
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("图形码丢失");
		}
		// 先判断图形码
		if (!checkcode_session.equalsIgnoreCase(graphic_code)) {
			throw BizException.withMessage("图形码错误");
		}
		if (CacheHelper.getCache("APP_VERIFY_CODE", mobile) != null) {
			throw BizException.withMessage("操作过于频繁，请90秒后再试");
		}
		String code = BaseUtil.genCheckCode(6);
		hostOrgId = StringUtils.isNotEmpty(hostOrgId) ? hostOrgId : super.getCurrentHostOrgId(request);
		Org hostOrg = orgService.selectById(hostOrgId);
		String sign = hostOrg.getSmsSign();
		smsSevice.sendByTemplate(sign, "212892", mobile, new String[] { code });
		CacheHelper.setCache("APP_VERIFY_CODE", mobile, code, 90 * 1000); // 90秒有效期
		// 清除图形码
		session.removeAttribute("checkcode_session");
		return true;
	}

	/**
	 * 手机验证码登录
	 */
	@RequestMapping("/kaosheng/mobile/code/login")
	@Operation(desc = "手机验证码登录", loginRequired = false)
	public Object mobileCodeLogin(HttpServletRequest request,
								  @RequestParam(required = false) String name,
								  @RequestParam(required = false) String mobile,
								  @RequestParam(required = false) String sfzh,
								  @RequestParam(required = false) String code) {
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入姓名");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("请输入短信验证码");
		}
		String verifyCode = CacheHelper.getCache("APP_VERIFY_CODE", mobile);
		if (!code.equals(verifyCode)) {
			throw BizException.withMessage("短信验证码错误");
		}
		StudentUser studentUser = studentUserService.findByMobile(mobile, super.getCurrentHostOrgId(request));
		if (studentUser == null) {
			studentUser = new StudentUser();
			String studentId = BaseUtil.generateId();
			studentUser.setId(studentId);
			studentUser.setStatus(AccountStatus.OK);
			studentUser.setRegHostOrgId(super.getCurrentHostOrgId(request));
			studentUser.setPassword(DigestUtils.md5Hex(mobile.substring(mobile.length()-6)));
			studentUserService.insert(studentUser);
			StudentInfo studentInfo = new StudentInfo();
			studentInfo.setId(BaseUtil.generateId());
			studentInfo.setStudentId(studentId);
			studentInfo.setName(name);
			studentInfo.setSfzh(sfzh);
			studentInfo.setMobile(mobile);
			studentInfo.setCreateTime(new Date());
			studentInfoService.insert(studentInfo);
		}
		return this.cacheLoginStudent(studentUser, request);
	}

	/**
	 * app 学员注册
	 */
	@RequestMapping("/kaosheng/mobile/doRegist")
	@Operation(desc = "学员注册", loginRequired = false)
	public Object regist(HttpServletRequest request, @RequestParam(required = false) String username,
			@RequestParam(required = false) String sfzh, @RequestParam(required = false) String mobile,
			@RequestParam(required = false) String hostOrgId, // 通用版必传
			@RequestParam(required = false) String code) {
		String verifyCode = CacheHelper.getCache("APP_VERIFY_CODE", mobile); // 90秒有效期
		if (!code.equals(verifyCode)) {
			throw BizException.withMessage("短信验证码错误");
		}
		hostOrgId = StringUtils.isNotEmpty(hostOrgId) ? hostOrgId : super.getCurrentHostOrgId(request);
		String studentId = studentUserService.regist(username, sfzh, mobile, hostOrgId);
		CacheHelper.removeCache("APP_VERIFY_CODE", mobile);
		return studentId;
	}

	@RequestMapping("/kaosheng/mobile/isLogin")
	@Operation(desc = "学员用户登录状态检测", loginRequired = false)
	public Object isLogin(HttpServletRequest request) {
		try {
			Object user = request.getSession().getAttribute(Constants.STUDENT_USER_SESSION_KEY);
			if (user != null) {
				LoginStudent loginUser = (LoginStudent) user;
				if (loginUser != null && loginUser.getUser() != null) {
					return true;
				}
			}
			return false;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 找回密码
	 */
	@RequestMapping("/kaosheng/mobile/resetPasswords")
	@Operation(desc = "找回密码", loginRequired = false)
	public Object resetPasswords(HttpServletRequest request, @RequestParam(required = false) String mobile,
			@RequestParam(required = false) String password, @RequestParam(required = false) String code,
			@RequestParam(required = false) String hostOrgId, //通用版必传
			@RequestParam(required = false) String graphic_code)
			throws ClientException {
		String verifyCode = CacheHelper.getCache("APP_VERIFY_CODE", mobile); // 90秒有效期
		Integer verifyTimes = CacheHelper.getCache("APP_VERIFY_TIMES", mobile);// 累加错误次数，防止Burp抓包工具暴力破解 田军 修复国土安全测试漏洞
																				// 2023-08-22
		if (verifyTimes != null && verifyTimes >= 6) {
			throw BizException.withMessage("短信验证码错误次数超过限制，请90秒后重试");
		}
		if (!code.equals(verifyCode)) {
			verifyTimes = verifyTimes == null ? 0 : verifyTimes;
			verifyTimes = verifyTimes + 1;
			CacheHelper.setCache("APP_VERIFY_TIMES", mobile, verifyTimes, 90 * 1000);// 90秒有效期
			throw BizException.withMessage("短信验证码错误");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能为空");
		}
		if (StringUtils.isEmpty(password)) {
			throw BizException.withMessage("请输入密码");
		}
		if (BaseUtil.isRawPasswordForStudent(password)) {// 弱密码检测
			throw BizException.withMessage("新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合");
		}
		hostOrgId = StringUtils.isNotEmpty(hostOrgId) ? hostOrgId : super.getCurrentHostOrgId(request);
		studentUserService.resetPasswords(mobile, password, hostOrgId);
		CacheHelper.removeCache("APP_VERIFY_CODE", mobile);// 验证之后清除验证码
		return true;
	}

	/**
	 * 弱密码强制修改
	 */
	@RequestMapping("/kaosheng/mobile/strongPasswords")
	@Operation(desc = "弱密码强制修改", loginRequired = false)
	public Object strongPasswords(HttpServletRequest request, @RequestParam("username") String username,
			@RequestParam("oldPassword") String oldPassword, @RequestParam("password") String password)
			throws ClientException {
		String hostOrgId = (String) request.getSession().getAttribute("isRawPassword_host_org_id");
		StudentUser studentUser = studentUserService.findByAccount(username, hostOrgId);
		if (studentUser == null) {
			throw BizException.withMessage("用户名在系统中不存在");
		} else if (!DigestUtils.md5Hex(oldPassword).equals(studentUser.getPassword())) {
			throw BizException.withMessage("原始密码错误");
		} else if (BaseUtil.isRawPasswordForStudent(password)) {
			throw BizException.withMessage("新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合");
		}
		studentUser.setPassword(DigestUtils.md5Hex(password));
		studentUserService.updateById(studentUser);
		//缓存登录学员
		Map<String, Object> userMap = cacheLoginStudent(studentUser, request);
		return userMap;
	}

	/**
	 * 返回sessionId
	 */
	@RequestMapping("/kaosheng/mobile/getSessionId")
	@Operation(desc = "返回sessionId", loginRequired = false)
	public Object getSessionId(HttpServletRequest request) {
		HashMap<String, Object> result = new HashMap<>();
		result.put("hostOrg", super.getCurrentHostOrg(request));
		result.put("sessionId", request.getSession().getId());
		return result;
	}

	/**
	 * 获取主办单位的配置信息
	 */
	@RequestMapping("/kaosheng/mobile/hostOrg/list")
	@Operation(desc = "获取主办单位的配置信息", loginRequired = false)
	public Object hostOrgList(HttpServletRequest request) {
		List<Org> orgs = orgService.getAllHostOrg();
		List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
		for (Org hostOrg : orgs) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("id", hostOrg.getId());
			map.put("code", hostOrg.getCode());
			map.put("name", hostOrg.getName());
			String protocol = request.getScheme();
			String logo = protocol + "://" + hostOrg.getPortalDomain() + "/sas/logo/app/" + hostOrg.getCode() + ".png";
			map.put("logo", logo);
			map.put("serverPath", protocol + "://" + hostOrg.getPortalDomain());
			String wsProtocol = "";
			if (protocol.equals("http")) {
				wsProtocol = "ws";
			} else {
				wsProtocol = "wss";
			}
			map.put("zbWsPath", wsProtocol + "://" + hostOrg.getPortalDomain());
			maps.add(map);
		}
		return maps;
	}

	/**
	 * 缓存登录用户
	 */
	protected Map<String, Object> cacheLoginStudent(StudentUser studentUser, HttpServletRequest request) {
		Map<String, Object> userMap = new HashMap<>();
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
		LoginStudent loginStudent = new LoginStudent();
		loginStudent.setUser(studentUser);
		loginStudent.setInfo(studentInfo);
		loginStudent.setHostOrg(orgService.selectById(studentUser.getRegHostOrgId()));
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		// 用户信息
		userMap.put("userId", studentUser.getId());
		String account = StringUtils.isEmpty(studentInfo.getSfzh()) ? studentInfo.getMobile() : studentInfo.getSfzh();
		userMap.put("username", account);
		userMap.put("orgId", studentUser.getOrgId());
		userMap.put("avatar", studentUser.getAvatar());
		userMap.put("isBindMobile", studentUser.getIsBindMobile());
		userMap.put("isPersonalInfoCompleted",
				studentInfoService.isPersonalInfoCompleted(studentUser.getId()) == true ? Constants.YES : Constants.NO);
		// 个人信息
		userMap.put("info", studentInfo);

		Date now = new Date();
		userMap.put("lastLoginTime", now);
		userMap.put("logo", super.getCurrentHostOrgPortalWebUrl(request) + "/sas/logo/app/"
				+ super.getCurrentHostOrg(request).getCode() + ".png");
		studentUserService.insertStudentLoginLog(studentUser.getId(), now, "APP");
		//获取最近一次报名的项目id
		userMap.put("latestXmId", this.getLatestXmId(studentUser.getId()));
		// 分主办单位统计在线用户
		CacheHelper.setCache(Constants.STUDENT_USER_LOGIN_CACHE + studentUser.getRegHostOrgId(),
				studentInfo.getStudentId(), request.getSession().getId());
		return userMap;
	}
	
	@RequestMapping("/kaosheng/mobile/yzm")
	@Operation(desc = "获取图形码", loginRequired = false)
	public void login(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("image/jpeg");// 设置相应类型,告诉浏览器输出的内容为图片
		response.setHeader("Pragma", "No-cache");// 设置响应头信息，告诉浏览器不要缓存此内容
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expire", 0);
		CheckImageCode checkImageCode = new CheckImageCode();
		checkImageCode.getRandcode(request, response);// 输出验证码图片方法
	}



	@RequestMapping("/kaosheng/mobile/ssoLogin")
	@Operation(desc = "sso登录", loginRequired = false)
	public Object h5Student(HttpServletRequest request, HttpServletResponse response,
							@RequestParam(required = false) String openToken) throws IOException {
		String checkToken = CacheHelper.getCache("OpenToken", "op_" + openToken);
		if (StringUtils.isEmpty(checkToken)) {
			throw BizException.withMessage("无效的OpenToken");
		} else {
			CacheHelper.removeCache("OpenToken", "op_" + openToken);
		}

		String employeeNum = JWTUtils.getClaim(openToken, "userId");

		//返回页面数据
		StudentUser studentUser = studentUserService.getByEmployeeNum(employeeNum);
		if (studentUser == null) {
			studentUser = syncStudentUser(request, employeeNum);
		}
		// 通过考生用户判断该用户状态是否禁用
		if (studentUser.getStatus() == AccountStatus.BLOCK) {
			throw BizException.withMessage("该用户已经被禁用,如需恢复使用，请联系系统管理人员");
		}
		if (studentUser.getStatus() == AccountStatus.CANCELLATION) {
			throw BizException.withMessage("该用户已经注销,如需恢复使用，请联系系统管理人员");
		}
		LoginStudent loginStudent = new LoginStudent();
		loginStudent.setUser(studentUser);
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
		if (studentInfo == null) {
			throw BizException.withMessage("获取学员信息失败");
		}
		loginStudent.setInfo(studentInfo);
		loginStudent.setHostOrg(orgService.selectById(studentUser.getRegHostOrgId()));
		loginStudent.setIsSso(Constants.YES);
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		studentUserService.insertStudentLoginLog(studentUser.getId(), new Date(), "PC");
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("isPersonalInfoCompleted",
				studentInfoService.isPersonalInfoCompleted(studentUser.getId()) ? Constants.YES : Constants.NO);

		Map<String,Object> userMap = new HashMap<>();
		//用户信息
		userMap.put("userId", studentUser.getId());
		String account = StringUtils.isEmpty(studentInfo.getSfzh()) ? studentInfo.getMobile() : studentInfo.getSfzh();
		userMap.put("username", account);
		userMap.put("orgId", studentUser.getOrgId());
		userMap.put("avatar", studentUser.getAvatar());
		userMap.put("isBindMobile", studentUser.getIsBindMobile());
		userMap.put("isPersonalInfoCompleted",
				studentInfoService.isPersonalInfoCompleted(studentUser.getId()) == true ? Constants.YES : Constants.NO);
		//个人信息
		userMap.put("info", studentInfo);

		Date now = new Date();
		userMap.put("lastLoginTime", now);
		userMap.put("logo", super.getCurrentHostOrg(request).getAdminLogo());
		studentUserService.insertStudentLoginLog(studentUser.getId(), now,"APP");
		// 分主办单位统计在线用户
		CacheHelper.setCache(Constants.STUDENT_USER_LOGIN_CACHE + studentUser.getRegHostOrgId(),
				studentInfo.getStudentId(), request.getSession().getId());
		return userMap;
	}

	private StudentUser syncStudentUser(HttpServletRequest request, String employeeNum) {
		StudentUser studentUser;
		boolean flag = request.getRequestURL().toString().contains("st-jxjy.whxunw.com");
		StudentInfo info = new StudentInfo();
		studentUser = new StudentUser();
		String studentId = BaseUtil.generateId();
		if (flag){
			// 生态查询中间库数据库完善个人信息入库
			Map<String,Object> map = studentInfoService.selectByXh(employeeNum);
			if (map == null) {
				throw BizException.withMessage("该用户不存在数据推送中间表，请联系系统维护人员处理");
			}
			Object xm = map.get("xm");
			if (xm!=null) {
				info.setName(xm.toString());
			}
			Object sfzjh = map.get("sfzjh");
			if (sfzjh!=null) {
				info.setSfzh(sfzjh.toString());
			}
			Object sjh = map.get("sjh");
			if (sjh!=null) {
				info.setMobile(sjh.toString());
			}
			studentUser.setIsBindMobile(StringUtils.isNotEmpty(info.getMobile()) ? Constants.YES : Constants.NO);
			// 修改中间库同步状态
			studentInfoService.updateSyncStatus(employeeNum);
		}
		// 根据手机号判断用户是否存在，存在只更新员工号
		if (StringUtils.isNotEmpty(info.getMobile())) {
			StudentInfo studentInfo = studentInfoService.getByMobile(info.getMobile(), super.getCurrentHostOrgId(request));
			if (studentInfo != null) {
				StudentUser updateStudentUser = studentUserService.selectUserById(studentInfo.getStudentId());
				updateStudentUser.setEmployeeNum(employeeNum);
				studentUserService.updateById(updateStudentUser);
				return updateStudentUser;
			}
		}
		// 根据身份证号判断用户是否存在，存在只更新员工号
		if (StringUtils.isNotEmpty(info.getSfzh())) {
			StudentInfo studentInfo = studentInfoService.getBySfzh(info.getSfzh(), super.getCurrentHostOrgId(request));
			if (studentInfo != null) {
				StudentUser updateStudentUser = studentUserService.selectUserById(studentInfo.getStudentId());
				updateStudentUser.setEmployeeNum(employeeNum);
				studentUserService.updateById(updateStudentUser);
				return updateStudentUser;
			}
		}
		studentUser.setId(studentId);
		studentUser.setStudentType(StudentType.SCHOOL);
		studentUser.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
		studentUser.setStatus(AccountStatus.OK);
		studentUser.setRegHostOrgId(super.getCurrentHostOrgId(request));
		studentUser.setEmployeeNum(employeeNum);
		info.setId(BaseUtil.generateId());
		info.setStudentId(studentId);
		info.setCreateTime(new Date());
		info.setRegHostOrgId(super.getCurrentHostOrgId(request));
		studentUserService.insert(studentUser);
		studentInfoService.insert(info);
		return studentUser;
	}

	//获取学员最近一次报名的项目id
	private String getLatestXmId(String studentId) {
		List<ZypxBm> bms = zypxBmService.selectList((EntityWrapper<ZypxBm>) new EntityWrapper<ZypxBm>().eq("student_id", studentId));
		Optional<ZypxBm> firstBmOp = bms.stream().max(Comparator.comparing(ZypxBm::getTime));
		return firstBmOp.map(ZypxBm::getXmId).orElse(null);
	}
}