package com.xunw.jxjy.student.portal.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.inf.params.CoursewareCommentQueryParams;
import com.xunw.jxjy.model.inf.params.LiveCommentQueryParams;
import com.xunw.jxjy.model.inf.service.*;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.mobile.service.ZypxMobileBmService;
import com.xunw.jxjy.model.portal.service.IndexService;
import com.xunw.jxjy.model.sys.entity.Notice;
import com.xunw.jxjy.model.sys.entity.NoticeCategory;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.service.NoticeService;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.service.QuestionDB2CourseService;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.wdxx.entity.MyPracticeRecord;
import com.xunw.jxjy.model.wdxx.service.PortalPracticeRecordService;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmEvaluate;
import com.xunw.jxjy.model.zypx.entity.ZypxXmMaterial;
import com.xunw.jxjy.model.zypx.service.*;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.utils.ModelHelper;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生态定制化门户
 */
@Controller
public class StPortalController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(StPortalController.class);
    private static final Integer DEFAULT_PAGE_SIZE = 12;

    @Autowired
    private IndexService indexService;
    @Autowired
    private CoursewareService coursewareService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private UserService userService;
    @Autowired
    private ZypxXmService xmService;
    @Autowired
    private CoursewareMaterialService coursewareMaterialService;
    @Autowired
    private QuestionDBService questionDBService;
    @Autowired
    private PortalPracticeRecordService portalPracticeRecordService;
    @Autowired
    private ZypxExamDataService examDataService;
    @Autowired
    private ZypxLiveMaterialService liveMaterialService;
    @Autowired
    private ZypxLiveService liveService;
    @Autowired
    private CourseLiveService courseLiveService;
    @Autowired
    private ZypxMobileBmService mobileBmService;
    @Autowired
    private QuestionDB2CourseService question2CourseService;
    @Autowired
    private PxxmXmBmAdviceService pxxmXmBmAdviceService;
    @Autowired
    private ZyjdBmBatchService zyjdBmBatchService;
    @Autowired
    private ZyjdProfessionConditionService zyjdProfessionConditionService;
    @Autowired
    private ZyjdProfessionService zyjdProfessionService;
    @Autowired
    private ZypxXmCourseSettingService zypxXmCourseSettingService;
    @Autowired
    private ZypxBmService zypxBmService;
    @Autowired
    private ZypxXmMaterialService zypxXmMaterialService;
    @Autowired
    private ZypxXmEvaluateService zypxXmEvaluateService;
    @Autowired
    private CoursewareTargetResultService coursewareTargetResultService;
    @Autowired
    private CourseLiveTargetResultService courseLiveTargetResultService;
    @Autowired
    private CoursewareCommentService coursewareCommentService;
    @Autowired
    private LiveCommentService liveCommentService;

    /**
     * 通用门户首页加载
     */
    @RequestMapping("/portal/st/{hostOrgCode}/index")
    public Object list(HttpServletRequest request, @PathVariable String hostOrgCode, ModelAndView mv) throws Exception {
        //获取主办单位id
        String hostOrgId = getCurrentHostOrgId(request);
        List<Map<String, Object>> categoryList = indexService.getNoticeCategory(hostOrgId);
        mv.addObject("categoryList", categoryList);
        List<List<Map<String, Object>>> newsList = new ArrayList<List<Map<String, Object>>>();
        if (categoryList.size() > 0) {
            for (Map<String, Object> category : categoryList) {
                Map<String, Object> condition = new HashMap<String, Object>();
                condition.put("categoryId", BaseUtil.getStringValueFromMap(category, "id"));
                condition.put("receiver", Receiver.STUDENT);
                Page<Map<String, Object>> pageInfo = indexService.getNotice(condition, Constants.DEFAULT_PAGE_NUMBER,
                        15);
                newsList.add(pageInfo.getRecords());
            }
        }
        mv.addObject("newsList", newsList);
        List<Map<String, Object>> hotXmList = indexService.getHotXm(hostOrgId);
        if (!hotXmList.isEmpty()) {
            for (Map<String, Object> map : hotXmList) {
                String id = BaseUtil.getStringValueFromMap(map, "id");
                List<Map<String, Object>> courseList = mobileBmService.getCourseByXmId(id);
                //项目的总课时
                Double totalHours = 0.0;
                for (Map<String, Object> course : courseList) {
                    if (BaseUtil.isNotEmpty(course.get("hours"))) {
                        totalHours += Double.parseDouble(course.get("hours").toString());
                    }
                }
                Integer bmCount = indexService.selectCountByXmId(id);
                map.put("bm_count", bmCount);
                map.put("total_hours", totalHours);
            }
        }
        mv.addObject("xmList", hotXmList);
        //课程
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("hostOrgId", hostOrgId);
        condition.put("type", "KJ");
        Page<Map<String, Object>> courseList = indexService.getCourse(condition, Constants.DEFAULT_PAGE_NUMBER,
                Constants.DEFAULT_PAGE_SIZE);
        //课件学习人数
        for (Map<String, Object> map : courseList.getRecords()) {
            String courseId = BaseUtil.getStringValueFromMap(map, "id");
            Integer count = indexService.getStudyCount(courseId);
            //教师姓名
            Course course = courseService.selectById(courseId);
            EntityWrapper<Courseware> wrapper = new EntityWrapper<Courseware>();
            wrapper.eq("course_id", course.getId());
            List<Courseware> coursewareList = coursewareService.selectList(wrapper);
            if (CollectionUtils.isEmpty(coursewareList)) {
                map.put("kj", new Courseware());
            }
            Courseware courseware = coursewareList.get(0);
            map.put("kj", courseware);
            map.put("count", count);
        }
        mv.addObject("courseList", courseList.getRecords());
        Map<String, Object> jiangshiCondition = new HashMap<String, Object>();
        jiangshiCondition.put("hostOrgId", hostOrgId);
        Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(jiangshiCondition, Constants.DEFAULT_PAGE_NUMBER,
                Constants.DEFAULT_PAGE_SIZE);
        mv.addObject("teacherList", pageInfo.getRecords());
        Map<String, Object> bannerCondition = new HashMap<String, Object>();
        bannerCondition.put("hostOrgId", hostOrgId);
        Page<Map<String, Object>> syslbts = indexService.getBanner(bannerCondition, Constants.DEFAULT_PAGE_NUMBER,
                Constants.DEFAULT_PAGE_SIZE);
        mv.addObject("bannerList", syslbts.getRecords());
        mv.setViewName("pages/portal/" + hostOrgCode + "/index");
        return mv;
    }

    /**
     * 新闻首页
     */
    @RequestMapping("/portal/st/{hostOrgCode}/newsIndex")
    public Object newsIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, Integer pageNum, Integer pageSize,
                            String categoryId) throws Exception {
        // 取出职业培训所有的新闻类型
        List<Map<String, Object>> categorys = indexService.getNoticeCategory(super.getCurrentHostOrgId(request));
        mv.addObject("categorys", categorys);
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : Constants.DEFAULT_PAGE_SIZE;
        if (categorys.size() > 0) {
            // 默认展示第一个类型
            if (StringUtils.isEmpty(categoryId)) {
                mv.addObject("activeCategory", categorys.get(0));
            } else {
                List<Map<String, Object>> categoryList = categorys.stream()
                        .filter(x -> BaseUtil.getStringValueFromMap(x, "id").equals(categoryId))
                        .collect(Collectors.toList());
                mv.addObject("activeCategory", categoryList.get(0));
            }
            Map<String, Object> condition = new HashMap<String, Object>();
            condition.put("categoryId",
                    StringUtils.isEmpty(categoryId) ? BaseUtil.getStringValueFromMap(categorys.get(0), "id")
                            : categoryId);
            condition.put("receiver", Receiver.STUDENT);
            Page pageInfo = indexService.getNotice(condition, pageNum, pageSize);
            pageInfo.getRecords().forEach(x->{
                Map<String, Object> n = (Map<String, Object>)x;
                n.put("content", BaseUtil.extractText(n.get("content") == null ? null : n.get("content").toString()));
            });
            mv.addObject("newsList", pageInfo.getRecords());
            mv.addObject("pageSize", pageSize);
            mv.addObject("pageNum", pageNum);
            mv.addObject("count", pageInfo.getTotal());
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/newsIndex");
        return mv;
    }

    /**
     * 培训首页
     */
    @RequestMapping("/portal/st/{hostOrgCode}/trainingIndex")
    public Object trainingIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String parentTypeId, String typeId, String keyword,
                                Integer pageNum, Integer pageSize) throws Exception {
        // 获取所有的项目类型，只返回一级类型、二级类型
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.XM);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.XM));
        }

        // condition
        Map<String, Object> condition = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(typeId)) {
            condition.put("typeId", typeId);
        } else {
            condition.put("typeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(keyword)) {
            condition.put("keyword", keyword);
        }
        //获取主办单位ID
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        condition.put("isSkill", Constants.NO);
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        Page pageInfo = indexService.getXmList(condition, pageNum, pageSize);
        pageInfo.getRecords().forEach(x -> {
            Map<String, Object> xm = (Map<String, Object>) x;
            List<Map<String, Object>> courseList = zypxXmCourseSettingService.getAllCourseByXmId(xm.get("id").toString());
            double totalHours = courseList.stream().mapToDouble(c -> Double.parseDouble(c.get("hours").toString())).sum();
            xm.put("totalHours", totalHours);
            List<ZypxBm> zypxBms = zypxBmService.getByXmId(xm.get("id").toString());
            xm.put("totalBms", zypxBms.size());
        });
        mv.addObject("xmList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.addObject("curKeyword", keyword);
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingIndex");
        return mv;
    }

    /**
     * 职业鉴定首页
     */
    @RequestMapping("/portal/st/{hostOrgCode}/zyjdIndex")
    public Object zyjdIndex(HttpServletRequest request, ModelAndView mv,
                            @PathVariable String hostOrgCode,
                            String parentTypeId,
                            String typeId,
                            String childTypeId,
                            String keyword,
                            Integer pageNum,
                            Integer pageSize) throws Exception {
        // 获取所有的工种类型，只返回一级类型、二级类型，三级类型
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(null, TypeCategory.ZYJD);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(null, TypeCategory.ZYJD));
        }

        if (StringUtils.isNotEmpty(typeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(typeId);
            mv.addObject("level3Array", childTypes);
        } else {
            mv.addObject("level3Array", indexService.getThirdLevelType(null, TypeCategory.ZYJD));
        }

        // condition
        Map<String, Object> condition = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(childTypeId)) {
            condition.put("typeId", childTypeId);
        } else if (StringUtils.isNotEmpty(typeId)) {
            condition.put("typeId", typeId);
        } else {
            condition.put("typeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(childTypeId)) {
            mv.addObject("curChildTypeId", childTypeId);
        }
        if (StringUtils.isNotEmpty(keyword)) {
            condition.put("keyword", keyword);
        }
        //获取主办单位ID
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        Page pageInfo = indexService.getProfessions(condition, pageNum, pageSize);
        mv.addObject("professions", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.addObject("curKeyword", keyword);
        mv.setViewName("pages/portal/" + hostOrgCode + "/zyjdIndex");
        return mv;
    }

    /**
     * 课程首页
     */
    @RequestMapping("/portal/st/{hostOrgCode}/courseIndex")
    public Object courseIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String parentTypeId, String typeId, String keyword,
                              Integer pageNum, Integer pageSize) throws Exception {
        // 获取所有的项目类型，只返回一级类型、二级类型
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
        }
        Map<String, Object> condition = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(typeId)) {
            condition.put("typeId", typeId);
        } else {
            condition.put("typeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        //获取主办单位ID
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        condition.put("type", "KJ");
        condition.put("keyword", keyword);
        Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
        //课件学习人数
        for (Map<String, Object> map : pageInfo.getRecords()) {
            String courseId = BaseUtil.getStringValueFromMap(map, "id");
            Integer count = indexService.getStudyCount(courseId);
            map.put("count", count);
        }
        mv.addObject("curKeyword", keyword);
        mv.addObject("courseList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/" + hostOrgCode + "/courseIndex");
        return mv;
    }

    /**
     * 师资首页
     */
    @RequestMapping("/portal/st/{hostOrgCode}/teacherIndex")
    public Object teacherIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize)
            throws Exception {
        Map<String, Object> condition = new HashMap<String, Object>();
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        //获取主办单位id
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(condition, pageNum, pageSize);
        mv.addObject("teacherList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/original/teacherIndex");
        return mv;
    }

    /**
     * 直播列表 在线课堂
     */
    @RequestMapping("/portal/st/{hostOrgCode}/zhiboIndex")
    public Object zhiboIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String parentTypeId, String typeId, String keyword,
                             Integer pageNum, Integer pageSize) throws Exception {
        // 获取所有的项目类型，只返回一级类型、二级类型
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
        for (Map<String, Object> typeMap : typeList) {
            level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
            level1Array.add(typeMap);
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
        }
        Map<String, Object> condition = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(typeId)) {
            condition.put("typeId", typeId);
        } else {
            condition.put("typeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        //获取主办单位ID
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        //获取主办单位Id
        condition.put("type", "ZB");
        if (StringUtils.isNotEmpty(keyword)) condition.put("keyword", keyword);
        mv.addObject("curKeyword", keyword);
        Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
        pageInfo.getRecords().forEach(x->{
            CourseLive courseLive = courseLiveService.getByCourseId(x.get("id").toString());
            Courselive live = ModelHelper.convertObject(courseLive.getContent());
            long count = live.getChapters().stream().flatMap(c -> c.getLessons().stream()).filter(l -> {
                return l.getZbzt() == Zbzt.PLAYING;
            }).count();
            if (count > 0) {
                x.put("isZbing", Constants.YES);
                return;
            }
            x.put("isZbing", Constants.NO);
        });
        Comparator<Map<String, Object>> comparator = Comparator.comparingInt(x ->
                Integer.parseInt(x.get("isZbing").toString()));
        List<Map<String, Object>> maps = pageInfo.getRecords().stream().sorted(comparator.reversed()).collect(Collectors.toList());
        pageInfo.setRecords(maps);
        mv.addObject("courseList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/" + hostOrgCode + "/zhiboIndex");
        return mv;
    }

    /**
     * 直播详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/zhiboDetail")
    public Object zhiboDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id, @RequestParam(required = false) String lessonId) throws Exception {
        CourseLive courseLive = courseLiveService.getByCourseId(id);
        Integer studyCount = indexService.getZbStudyCount(id);
        Integer bmCourseCount = indexService.getBmCourseCountByCourseId(id);
        String liveContent = courseLive != null ? courseLive.getContent() : null;
        Courselive courselive = ModelHelper.convertObject(liveContent);
        List<Map<String, Object>> comments = new ArrayList<>();
        if (courselive != null) {
            List<com.xunw.jxjy.model.common.courselive.Chapter> chapters = courselive.getChapters();
            com.xunw.jxjy.model.common.courselive.Chapter activeChapter = null;
            com.xunw.jxjy.model.common.courselive.Lesson activeLesson = null;
            if (BaseUtil.isNotEmpty(chapters)) {
                for (com.xunw.jxjy.model.common.courselive.Chapter chapter : chapters) {
                    List<com.xunw.jxjy.model.common.courselive.Lesson> lessons = chapter.getLessons();
                    for (com.xunw.jxjy.model.common.courselive.Lesson lesson : lessons) {
                        Live live = liveService.selectById(lesson.getId());
                        lesson.setZbzt(live.getZbzt());
                        User user = userService.selectById(live.getJsId());
                        lesson.setJsxm(user.getName());
                        //回放 直播状态已完成 并且 会话id为空
                        if (Zbzt.FINISHED == live.getZbzt() && live.getHkspsc() != null && live.getHkspsc() > 0) {
                            if (StringUtils.isEmpty(live.getHkhhida())) {
                                lesson.setLiveDocPath(live.getHkdza());
                            }
                            if (StringUtils.isEmpty(live.getHkhhidb())) {
                                lesson.setLiveTeacherPath(live.getHkdzb());
                            }
                        }
                        //直播推流
                        else if (Zbzt.PLAYING == live.getZbzt()) {
                            lesson.setLiveDocPath(live.getHlsdza());
                            lesson.setLiveTeacherPath(live.getHlsdzb());
                            if (request.getScheme().indexOf("https") > -1) {
                                lesson.setLiveDocPath(lesson.getLiveDocPath().replace("http://pili-live-hls.zb.whxunw.com", "https://pili-live-hls-zb.whxunw.com"));
                                lesson.setLiveTeacherPath(lesson.getLiveTeacherPath().replace("http://pili-live-hls.zb.whxunw.com", "https://pili-live-hls-zb.whxunw.com"));
                            }
                            if (StringUtils.isEmpty(lessonId)) {
                                activeLesson = lesson;
                                activeChapter = chapter;
                            }
                        }
                        if (StringUtils.isNotEmpty(lessonId) && lesson.getId().equals(lessonId)) {
                            activeLesson = lesson;
                            activeChapter = chapter;
                        }
                        LiveCommentQueryParams params = new LiveCommentQueryParams();
                        params.setLiveId(lesson.getId());
                        comments.addAll(liveCommentService.getList(params));
                    }
                }
                activeChapter = activeChapter != null ? activeChapter : chapters.get(0);
                activeLesson = activeLesson != null ? activeLesson : activeChapter.getLessons().get(0);
            }
            mv.addObject("comments", comments);
            mv.addObject("courselive", courselive);//直播对象
            mv.addObject("courseLive", courseLive);//直播课对象
            mv.addObject("activeLesson", activeLesson);
            mv.addObject("activeChapter", activeChapter);
            List<String> lessonIds = new ArrayList<String>();
            if (courselive != null) {
                courselive.getChapters().forEach(x -> x.getLessons().forEach(y -> {
                    lessonIds.add(y.getId());
                }));
            }
            if (lessonIds != null && lessonIds.size() > 0) {
                Map<String, Object> condition = new HashMap<String, Object>();
                condition.put("liveIds", lessonIds);
                List<Map<String, Object>> materials = liveMaterialService.getList(condition);
                materials.forEach(x -> {
                    //格式化文件大小显示
                    x.put("file_size", BaseUtil.formatFileSize(Long.valueOf(BaseUtil.getStringValueFromMap(x, "fileSize"))));
                });
                mv.addObject("materials", materials);//所有资料
            }
        }
        if (super.getLoginStudent(request) != null) {
            mv.addObject("star", courseLiveTargetResultService.selectList(new EntityWrapper<CourseLiveTargetResult>()
                    .eq("student_id", super.getLoginStudentId(request)).eq("course_live_id", courseLive.getId())));
        }
        mv.addObject("studyCount", studyCount);
        mv.addObject("bmCourseCount", bmCourseCount);
        mv.addObject("course", courseService.selectById(id));//课程对象
        mv.setViewName("pages/portal/" + hostOrgCode + "/zhiboPlay");
        return mv;
    }


    /**
     * 题库列表
     */
    @RequestMapping("/portal/st/{hostOrgCode}/questionDbIndex")
    public Object qrzkIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String parentTypeId, String typeId, Integer pageNum,
                            Integer pageSize) throws Exception {
        // 获取所有的项目类型，只返回一级类型、二级类型
        List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
        Set<String> level1Ids = new HashSet<String>();
        List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
        for (Map<String, Object> typeMap : typeList) {
            String pid = BaseUtil.getStringValueFromMap(typeMap, "parentId");
            if (StringUtils.isEmpty(pid)) {
                level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
                level1Array.add(typeMap);
            }
        }
        mv.addObject("level1Array", level1Array);
        if (StringUtils.isNotEmpty(parentTypeId)) {
            List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
            mv.addObject("level2Array", childTypes);
        } else {
            mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
        }
        Map<String, Object> condition = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(typeId)) {
            condition.put("typeId", typeId);
        } else {
            condition.put("typeId", parentTypeId);
        }
        if (StringUtils.isNotEmpty(typeId)) {
            mv.addObject("curTypeId", typeId);
        }
        if (StringUtils.isNotEmpty(parentTypeId)) {
            mv.addObject("curParentTypeId", parentTypeId);
        }
        pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
        pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        //获取主办单位ID
        condition.put("hostOrgId", getCurrentHostOrgId(request));
        condition.put("type", "TK");
        Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
        mv.addObject("courseList", pageInfo.getRecords());
        mv.addObject("pageSize", pageSize);
        mv.addObject("pageNum", pageNum);
        mv.addObject("count", pageInfo.getTotal());
        mv.setViewName("pages/portal/" + hostOrgCode + "/questionDbIndex");
        return mv;
    }

    /**
     * 成功案例
     */
    @RequestMapping("/portal/st/{hostOrgCode}/case")
    public Object caseIndex(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/case");
        return mv;
    }

    /**
     * 关于我们
     */
    @RequestMapping("/portal/st/{hostOrgCode}/aboutus")
    public Object aboutus(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) throws Exception {
        mv.setViewName("pages/portal/" + hostOrgCode + "/aboutus");
        return mv;
    }

    /**
     * 以下内容是非首页的内容
     */

    /**
     * 培训详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/trainingDetail")
    public Object trainingDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        ZypxXm xm = xmService.selectById(id);
        List<Map<String, Object>> courseSettingList = indexService.getXmCourseSetting(xm.getId());
        mv.addObject("pxxm", xm);
        mv.addObject("bmCount", xmService.getBmCountByXmId(id));
        mv.addObject("courseSettingList", courseSettingList);
        // 获取相关课程的课件学习资料
//        List<Map<String, Object>> materialList = indexService.getCoursewareMaterialByXmId(xm.getId());
        EntityWrapper<ZypxXmMaterial> wrapper = new EntityWrapper<>();
        wrapper.eq("xm_id", id);
        wrapper.eq("material_status", Constants.YES);
        wrapper.eq("category", Constants.NO);
        List<ZypxXmMaterial> xmMaterials = zypxXmMaterialService.selectList(wrapper);
        xmMaterials.forEach(x->x.setMaterialUrl(x.getMaterialUrl().replaceAll("http://", "https://")));
        mv.addObject("materialList", xmMaterials);

        List<Map<String, Object>> comments = zypxXmEvaluateService.getList(id);
        mv.addObject("comments", super.getLoginStudentId(request) != null ? comments : null);
        // 获取一个讲师
        Map<String, Object> teacherCondition = new HashMap<String, Object>();
        teacherCondition.put("hostOrgId", super.getCurrentHostOrgId(request));
        teacherCondition.put("id", null);
        Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(teacherCondition, 1, 1);
        List<Map<String, Object>> teacherList = pageInfo.getRecords();
        mv.addObject("teacherList", teacherList);
        mv.setViewName("pages/portal/" + hostOrgCode + "/trainingDetail");
        return mv;
    }

    /**
     * 职业鉴定详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/zyjdDetail")
    public Object zyjdDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) {
        mv.addObject("profession", zyjdProfessionService.selectById(id));
        List<Map<String, Object>> conditions = zyjdProfessionConditionService.getConditionsByProfessionId(id, super.getCurrentHostOrgId(request));
        Map<Object, List<Map<String, Object>>> techLevelMap = conditions.stream().collect(Collectors.groupingBy(x -> x.get("techLevel")));
        List<ZyjdBmBatch> bmBatches = zyjdBmBatchService.getOpenBmBatchByProfessionId(id, super.getCurrentHostOrgId(request));
        mv.addObject("conditions", techLevelMap);
        ZyjdBmBatch batch = null;
        if (CollectionUtils.isNotEmpty(bmBatches)) {
            batch = bmBatches.get(0);
        }
        mv.addObject("batch", batch);
        mv.setViewName("pages/portal/" + hostOrgCode + "/zyjdDetail");
        return mv;
    }

    /**
     * 师资详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/teacherDetail")
    public Object teacherDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        Map<String, Object> teacherCondition = new HashMap<String, Object>();
        teacherCondition.put("hostOrgId", super.getCurrentHostOrgId(request));
        teacherCondition.put("id", id);
        Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(teacherCondition, 1, 1);
        List<Map<String, Object>> teacherList = pageInfo.getRecords();
        mv.addObject("teacher", teacherList.get(0));
        mv.setViewName("pages/portal/" + hostOrgCode + "/teacherDetail");
        return mv;
    }

    /**
     * 新闻详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/newsDetail")
    public Object newsDetail(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        // 取出职业培训所有的新闻类型
        List<Map<String, Object>> categorys = indexService.getNoticeCategory(getCurrentHostOrgId(request));
        mv.addObject("categorys", categorys);
        Notice notice = noticeService.selectById(id);
        NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
        mv.addObject("activeCategory", noticeCategory);
        mv.addObject("notice", notice);
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("categoryId", noticeCategory.getId());
        Page newsPages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
        //获取上一条新闻的ID、下一条新闻的ID
        List<Map<String, Object>> allNews = newsPages.getRecords();
        int index = 0;
        for (int i = 0; i < allNews.size(); i++) {
            Map map = allNews.get(i);
            if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
                index = i;
                break;
            }
        }
        if (index > 0) {
            Map<String, Object> lastNews = allNews.get(index - 1);
            mv.addObject("lastNewsId", BaseUtil.getStringValueFromMap(lastNews, "id"));
        }
        if (index < allNews.size() - 1) {
            Map<String, Object> nextNews = allNews.get(index + 1);
            mv.addObject("nextNewsId", BaseUtil.getStringValueFromMap(nextNews, "id"));
        }
        mv.setViewName("pages/portal/" + hostOrgCode + "/newsDetail");
        return mv;
    }

    /**
     * 课件播放
     */
    @RequestMapping("/portal/st/{hostOrgCode}/coursePlay")
    public Object coursePlay(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        Course course = courseService.selectById(id);
        Integer studyCount = indexService.getStudyCount(id);
        Integer bmCourseCount = indexService.getBmCourseCountByCourseId(id);
        EntityWrapper<Courseware> wrapper = new EntityWrapper<Courseware>();
        wrapper.eq("course_id", course.getId());
        wrapper.eq("status", Zt.OK);
        List<Courseware> coursewareList = coursewareService.selectList(wrapper);
        if (CollectionUtils.isEmpty(coursewareList)) {
            mv.addObject("courseLearn", "");
            mv.addObject("xxzlList", new JSONArray());
            mv.addObject("kj", new Courseware());
            mv.addObject("course", new Course());
            mv.setViewName("pages/portal/" + hostOrgCode + "/coursePlay");
            return mv;
        }
        Courseware courseware = coursewareList.get(0);
        Courselearn course_learn = ModelHelper.convertObject(courseware.getContent());
        mv.addObject("courseLearn", course_learn);
        // 查询课件学习资料
        EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper<CoursewareMaterial>();
        materialWrapper.orderBy("create_time", false);
        List<CoursewareMaterial> materialList = coursewareMaterialService.selectList(materialWrapper);
        for (CoursewareMaterial coursewareMaterial : materialList) {
            coursewareMaterial.setDisplaySize(BaseUtil.formatFileSize(coursewareMaterial.getFileSize()));
        }
        if (super.getLoginStudent(request) != null) {
            mv.addObject("star", coursewareTargetResultService.selectList((EntityWrapper<CoursewareTargetResult>) new EntityWrapper<CoursewareTargetResult>()
                    .eq("student_id", super.getLoginStudentId(request)).eq("kj_id", courseware.getId())));
        }
        String studentId = super.getLoginStudentId(request);
        List<Map<String, Object>> comments = new ArrayList<>();
        if (StringUtils.isNotEmpty(studentId)) {
            CoursewareCommentQueryParams coursewareCommentQueryParams = new CoursewareCommentQueryParams();
            coursewareCommentQueryParams.setKjId(courseware.getId());
            coursewareCommentQueryParams.setCreatorId(super.getLoginStudentId(request));
            coursewareCommentQueryParams.setSize(Integer.MAX_VALUE);
            coursewareCommentQueryParams.setCurrent(1);
            comments = coursewareCommentService.getList(coursewareCommentQueryParams);
        }
        mv.addObject("comments", comments);
        mv.addObject("studyCount", studyCount);
        mv.addObject("bmCourseCount", bmCourseCount);
        mv.addObject("xxzlList", materialList);
        mv.addObject("kj", courseware);
        mv.addObject("course", course);
        mv.setViewName("pages/portal/" + hostOrgCode + "/coursePlay");
        return mv;
    }

    /**
     * 题库详情
     */
    @RequestMapping("/portal/st/{hostOrgCode}/coursePlayQuestionDb")
    public Object coursePlayQuestionDb(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        LoginStudent loginStudent = super.getLoginStudent(request);
        Course course = courseService.selectById(id);
        // 根据课程获取题库列表
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (QuestionDBEntity questionDBEntity : questionDBService.getQuestionDBEntity(course.getId())) {
            Map<String, Object> map = new HashMap<String, Object>();
            String questionDbId = questionDBEntity.getId();
            map.put("id", questionDbId);
            map.put("name", questionDBEntity.getName());
            if (loginStudent != null) {
                String studentId = loginStudent.getUser().getId();
                // 我的练习
                EntityWrapper<MyPracticeRecord> myPraceticeRecordWrapper = new EntityWrapper<MyPracticeRecord>();
                myPraceticeRecordWrapper.eq("student_id", studentId);
                myPraceticeRecordWrapper.eq("db_id", questionDbId);
                List<MyPracticeRecord> myPaperList = portalPracticeRecordService.selectList(myPraceticeRecordWrapper);
                if (myPaperList.size() > 0) {
                    String paperId = myPaperList.get(0).getPaperId();
                    // 考试记录
                    EntityWrapper<ExamData> examDataWrapper = new EntityWrapper<ExamData>();
                    examDataWrapper.eq("student_id", studentId);
                    examDataWrapper.eq("paper_id", paperId);
                    List<ExamData> examDatas = examDataService.selectList(examDataWrapper);
                    if (examDatas.size() > 0) {
                        ExamDataStatus examDataStatus = examDatas.get(0).getStatus();
                        map.put("examDataStatus", examDataStatus.name());
                    }
                }
            }
            list.add(map);
        }
        mv.addObject("course", course);
        mv.addObject("list", list);
        mv.setViewName("pages/portal/" + hostOrgCode + "/coursePlayQuestionDb");
        return mv;
    }

    /**
     * 考生注册功能
     */
    @RequestMapping("/portal/st/{hostOrgCode}/register")
    public ModelAndView register(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode) {
        mv.setViewName("pages/portal/" + hostOrgCode + "/register");
        return mv;
    }

    /**
     * 展示试题
     */
    @Transactional
    @RequestMapping("/portal/st/{hostOrgCode}/doQuestionDBPaper")
    public Object subjectPlay(HttpServletRequest request, ModelAndView mv, @PathVariable String hostOrgCode, String id) throws Exception {
        String studentId = getLoginStudentId(request);
        QuestionDBEntity questionDBEntity = questionDBService.selectById(id);
        QuestionDB2CourseEntity question2CourseEntity = question2CourseService.getDetailsByDbId(id);
        Course course = courseService.selectById(question2CourseEntity.getCourseId());
        Paper paper = indexService.makePaperByQuestionDB(id, studentId);
        EntityWrapper<ExamData> wrapper = new EntityWrapper();
        wrapper.eq("student_id", studentId);
        wrapper.eq("paper_id", paper.getId());
        List<ExamData> examDataList = examDataService.selectList(wrapper);
        if (examDataList.size() > 0) {
            mv.addObject("examDataStatus", examDataList.get(0).getStatus());
        }
        mv.addObject("db", questionDBEntity);
        mv.addObject("course", course);
        mv.addObject("paper", paper);
        mv.setViewName("pages/portal/" + hostOrgCode + "/doQuestionDBPaper");
        return mv;
    }

    /**
     * 交卷
     */
    @RequestMapping("/portal/st/{hostOrgCode}/submitPaper")
    @ResponseBody
    public Object submitPaper(HttpServletRequest request, @PathVariable String hostOrgCode, String paperId) throws Exception {
        EntityWrapper<ExamData> wrapper = new EntityWrapper<ExamData>();
        String studentId = super.getLoginStudentId(request);
        wrapper.eq("paper_id", paperId);
        wrapper.eq("student_id", studentId);
        wrapper.orderBy("save_time", false);
        List<ExamData> examDataList = examDataService.selectList(wrapper);
        ExamData examData = examDataList.size() > 0 ? examDataList.get(0) : null;
        if (BaseUtil.isNotEmpty(examData)) {
            if (ExamDataStatus.WJJ != examData.getStatus()) {
                throw BizException.withMessage("用户已提交过试卷，不允许重复提交!");
            }
            examData = examDataList.get(0);
        } else {
            examData = new ExamData();
        }
        // 为保障提交时时最新答案，也存一次答案
        Map<String, Object> examDataMap = new HashMap<>();
        Map<String, String[]> mapx = request.getParameterMap();
        for (Map.Entry<String, String[]> entry : mapx.entrySet()) {
            if (entry.getKey().startsWith("Q-")) {
                examDataMap.put(entry.getKey(), StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
            }
        }

        JSONObject userDataJson = JSONObject.fromObject(examData);
        String studentAnswer = userDataJson == null ? "" : userDataJson.toString();
        examData.setData(studentAnswer);
        examData.setScore(0);
        examData.setEndTime(new Date());
        examData.setStatus(ExamDataStatus.YJJDPG);
        examData.setClient(Jjkhd.PC);

        boolean boo = true;
        if (BaseUtil.isEmpty(examData.getId())) {
            examData.setId(BaseUtil.generateId());
            examData.setPaperId(paperId);
            examData.setStudentId(studentId);
            examData.setStartTime(new Date());
            examData.setSaveTime(new Date());
            examDataService.insert(examData);
        } else {
            examDataService.updateById(examData);
        }
        return true;
    }

    /**
     * 查询课件学习资料
     */
    @RequestMapping("/portal/st/CoursewareMaterial")
    @ResponseBody
    public Object getCoursewareMaterial(String kjId, String chapterId, String lessonId) {
        EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper<CoursewareMaterial>();
        materialWrapper.eq("kj_id", kjId);
        materialWrapper.eq("chapter_id", chapterId);
        materialWrapper.eq("lesson_id", lessonId);
        materialWrapper.orderBy("create_time", false);
        List<CoursewareMaterial> materialList = coursewareMaterialService.selectList(materialWrapper);
        for (CoursewareMaterial coursewareMaterial : materialList) {
            coursewareMaterial.setDisplaySize(BaseUtil.formatFileSize(coursewareMaterial.getFileSize()));
        }
        return materialList;
    }

    /**
     * 添加新闻咨询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/portal/st/{hostOrgCode}/addAdvice")
    @ResponseBody
    public Object addAdvice(HttpServletRequest request,
                            HttpServletResponse response,
                            @RequestParam(required = false) String name,
                            @RequestParam(required = false) String mobile,
                            @RequestParam(required = false) String xmId,
                            @RequestParam(required = false) String content) {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("姓名不能为空");
        }
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.withMessage("电话号码不能为空");
        }
        if (StringUtils.isEmpty(content)) {
            throw BizException.withMessage("咨询内容不能为空");
        }
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        String studentId = super.getLoginStudentId(request);
        pxxmXmBmAdviceService.addAdvice(name, mobile, content, studentId, xmId);
        return true;
    }

    /**
     * 保存项目评论
     *
     * @param request
     * @return
     */
    @RequestMapping("/portal/st/{hostOrgCode}/saveXmEvaluate")
    @ResponseBody
    public Object saveXmEvaluate(HttpServletRequest request,
                                 @RequestParam(required = false) String xmId,
                                 @RequestParam(required = false) String content) {
        if (StringUtils.isEmpty(xmId)) throw BizException.withMessage("项目id不能为空");
        if (StringUtils.isEmpty(content)) throw BizException.withMessage("评论不能为空");
        String studentId = super.getLoginStudentId(request);
        return zypxXmEvaluateService.saveXmEvaluate(xmId, content, studentId);
    }

    /**
     * 保存课件评分
     *
     * @param request
     * @return
     */
    @RequestMapping("/portal/st/{hostOrgCode}/saveCoursewareTargetResult")
    @ResponseBody
    public Object saveCoursewareTargetResult(HttpServletRequest request,
                                             @RequestParam(required = false) String coursewareId,
                                             @RequestParam(required = false) String starCount) {
        if (StringUtils.isEmpty(coursewareId)) throw BizException.withMessage("课件id不能为空");
        if (StringUtils.isEmpty(starCount)) throw BizException.withMessage("评分不能为空");
        String studentId = super.getLoginStudentId(request);
        coursewareTargetResultService.saveCoursewareTargetResult(studentId, coursewareId, starCount);
        return coursewareTargetResultService.selectList((EntityWrapper<CoursewareTargetResult>) new EntityWrapper<CoursewareTargetResult>()
                .eq("student_id", studentId).eq("kj_id", coursewareId));
    }

    /**
     * 保存直播评分
     *
     * @param request
     * @return
     */
    @RequestMapping("/portal/st/{hostOrgCode}/saveLiveTargetResult")
    @ResponseBody
    public Object saveLiveTargetResult(HttpServletRequest request,
                                       @RequestParam(required = false) String courseLiveId,
                                       @RequestParam(required = false) String starCount) {
        if (StringUtils.isEmpty(courseLiveId)) throw BizException.withMessage("直播课id不能为空");
        if (StringUtils.isEmpty(starCount)) throw BizException.withMessage("评分不能为空");
        String studentId = super.getLoginStudentId(request);
        courseLiveTargetResultService.saveCoursliveTargetResult(studentId, courseLiveId, starCount);
        return courseLiveTargetResultService.selectList(new EntityWrapper<CourseLiveTargetResult>()
                .eq("student_id", studentId).eq("course_live_id", courseLiveId));
    }
}
