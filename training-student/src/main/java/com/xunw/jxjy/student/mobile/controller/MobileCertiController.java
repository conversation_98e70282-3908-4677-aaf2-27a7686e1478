package com.xunw.jxjy.student.mobile.controller;

import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.personal.service.ZypxStudentCertiService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 职业培训-学员结业证书
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/certi")
public class MobileCertiController extends BaseController {

    @Autowired
    private ZypxStudentCertiService service;

	/**
	 * 我的证书
	 */
	@RequestMapping("/myCerti")
	@Operation(desc = "我的证书")
	public Object certi(HttpServletRequest request, @RequestParam(required = false) String xmId) {
		List<Map<String, Object>> certisByStudent = service.getCertiByStudent(super.getLoginStudentId(request),
				getCurrentHostOrgPortalWebUrl(request));
		if (BaseUtil.isNotEmpty(xmId)) {
			certisByStudent = certisByStudent.stream().filter(x -> x.get("xmId").equals(xmId))
					.collect(Collectors.toList());
		}
		return certisByStudent;
	}

    /**
     * 下载结业证书时 检查是否作答过调查问卷
     */
    @RequestMapping("/checkQa")
    @Operation(desc = "检查是否答过调查问卷")
    public Object checkQaWhenDowloadCerti(HttpServletRequest request, String certiId){
        Map<String, Object> checkQa = service.checkQaWhenDowloadCerti(certiId, super.getLoginStudentId(request));
        return checkQa;
    }

    /**
     * 检查培训项目的调查问卷是否被学员作答
     */
    @RequestMapping("/isQaAnswered")
    @Operation(desc = "检查培训项目的调查问卷是否被学员作答")
    public Object checkQaWhenDowloadCerti(HttpServletRequest request, String xmId, String qaId){
        boolean a = service.isQaAnswered(xmId, qaId, super.getLoginStudentId(request));
        return a;
    }
    
    
    /**
     * 做调查问卷
     */
    @RequestMapping("/toSurveyPaper")
    @Operation(desc = "做调查问卷")
    public Object toSurveyPaper(HttpServletRequest request,
                                @RequestParam(required = false) String xmId,
                                @RequestParam(required = false) String qaId) {
        Map<String, Object> map = service.toSurveyPaper(xmId, qaId);
        return map;
    }

    /**
     *  提交问卷
     */
    @RequestMapping("/submitSurveyPaper")
    @Operation(desc = "提交问卷")
    public Object submitSurveyPaper(HttpServletRequest request){
        return service.submitSurveyPaper(request, super.getLoginStudentId(request));
    }
}
