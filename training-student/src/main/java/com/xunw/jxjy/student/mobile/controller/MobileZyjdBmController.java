package com.xunw.jxjy.student.mobile.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.zyjd.entity.InfUnit;
import com.xunw.jxjy.model.zyjd.service.ZcpsDirectlyUnitsService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.common.entity.CommOrder;
import com.xunw.jxjy.model.common.service.CommOrderService;
import com.xunw.jxjy.model.enums.BmOpenStatus;
import com.xunw.jxjy.model.enums.FeeCategory;
import com.xunw.jxjy.model.enums.FeeType;
import com.xunw.jxjy.model.enums.PayMethod;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZyjdIndustry;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZyjdIndustryService;
import com.xunw.jxjy.model.sys.entity.Bank;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.BankConfigService;
import com.xunw.jxjy.model.sys.service.BankService;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.utils.FormLoader;
import com.xunw.jxjy.model.zyjd.entity.Attachment;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.params.ZyjdStudentBmParams;
import com.xunw.jxjy.model.zyjd.service.AttachmentService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmScopeService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmService;
import com.xunw.jxjy.model.zyjd.service.ZyjdStudentBmService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

import net.sf.json.JSONObject;

/**
 * 职业技能等级认定报名
 */
@Controller
@RequestMapping("/kaosheng/mobile/zyjd/bm")
public class MobileZyjdBmController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(MobileZyjdBmController.class);
	private static final String PAGE_VIEW_PREFIX = "pages/mobile/views"; 

	@Autowired
	private ZyjdBmService service;
	@Autowired
	private ZyjdBmBatchService bmBatchService;
	@Autowired
	private ZyjdStudentBmService studentBmService;
	@Autowired
	private ZyjdBmScopeService bmScopeService;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private CommOrderService orderService;
	@Autowired
	private BankConfigService bankConfigService;
	@Autowired
	private BankService bankService;
	@Autowired
	private DictService dictService;
	@Autowired
	private OrgService orgService;
    @Autowired
    private ZyjdIndustryService zyjdIndustryService;
    @Autowired
    private ZyjdBmService zyjdBmService;
    @Autowired
    private AttachmentService attachmentService;
    @Autowired
    private ZyjdBmBatchService zyjdBmBatchService;
    @Autowired
    private CommOrderService commOrderService;
    @Autowired
	private ZcpsDirectlyUnitsService zcpsDirectlyUnitsService;

	/**
	 * 职业鉴定手机端报名入口
	 */
	@RequestMapping("/startPage/{bmbatchId}")
	public Object bmPage(HttpServletRequest request, ModelAndView mv, @PathVariable("bmbatchId") String bmbatchId,
			String id) throws Exception {
		Map<String, Object> bmInfo = null;
		ZyjdBmBatch bmbatch = null;
		if (StringUtils.isNotEmpty(id)) {//获取已有报名信息
			bmInfo = service.getDetailsById(id);
			bmbatchId = BaseUtil.getStringValueFromMap(bmInfo, "bmbatchId");
			bmbatch = bmBatchService.selectById(bmbatchId);
			if (bmbatch.getBmStartTime() != null && bmbatch.getBmStartTime().after(new Date())
					|| bmbatch.getBmEndTime() != null && bmbatch.getBmEndTime().before(new Date())) {
				throw BizException.withMessage("不在报名时间范围内，无法修改");
			}
			bmInfo.put("attachments", attachmentService.getByBmId(id));
			mv.addObject("result", bmInfo);
		}
		else {
			bmbatch = bmBatchService.selectById(bmbatchId);
			if (bmbatch.getStatus() == BmOpenStatus.BLOCK && Constants.YES.equals(bmbatch.getIsAutoNextBatch())) {
				bmbatch = bmBatchService.getLatestOpenBatch(bmbatch.getType(), super.getCurrentHostOrgId(request));
				if (bmbatch == null) {
					throw BizException.withMessage("未获取到同类型下已经开放的报名批次");
				}
			}
			if (bmbatch.getBmStartTime().after(new Date())) {
				mv.addObject("isHiddenForm",true);
				mv.addObject("errorMsg","当前报名未开始，无法报名！报名开始时间："+ DateUtils.format(bmbatch.getBmStartTime()));
			}
			if (bmbatch.getBmEndTime().before(new Date())) {
				mv.addObject("isHiddenForm",true);
				mv.addObject("errorMsg","当前报名已经结束，无法报名！报名截止时间：" + DateUtils.format(bmbatch.getBmEndTime()));
			}
		}
		List<Dict> provinceList = dictService.getProvinceByDictCode();
		mv.addObject("provinceList", provinceList);
		//查询当前主办单位ID的二级学院
		mv.addObject("colleges", orgService.getChildren(super.getCurrentHostOrgId(request)).stream().filter(x -> 
			Constants.YES.equals(x.getIsCollege())).collect(Collectors.toList()));
		if (bmbatch != null) {
			if (bmbatch.getType() == ZyjdBmBatchType.ZYJS) {
				mv.addObject("select", dictService.getZyjsbmDictList());
			}
			mv.addObject("bmbatch", bmbatch);
			List<ZyjdIndustry> industryList = studentBmService.getIndustryByBmbatchId(bmbatch.getId());
			mv.addObject("industryList", industryList);
			if (industryList.size() > 0) {
				String industryId = null;
				if (bmInfo != null) {
					industryId = BaseUtil.getStringValueFromMap(bmInfo, "industryId");
				} else {
					industryId = industryList.get(0).getId();
				}
				mv.addObject("industryId", industryId);
				List<ZyjdProfession> professionList = studentBmService.getOpenedProfession(bmbatch.getId(),
						industryId);
				mv.addObject("professionList", professionList);
			}
		} else {
			throw BizException.withMessage("报名批次不存在");
		}
		String bmbatchType = bmbatch != null ? bmbatch.getType().name() : ZyjdBmBatchType.ZYJD.name();
		if (bmbatch.getType() == ZyjdBmBatchType.ZYJD || bmbatch.getType() == ZyjdBmBatchType.ZCPS) {
			JSONObject formConfig = CacheHelper.getCache(Constants.FORM_CONFIG_CACHE, bmbatch.getId());
			if (formConfig == null) {
				formConfig = FormLoader.load(bmbatch.getType(), orgService.selectById(bmbatch.getHostOrgId()).getCode(), bmbatch.getFormId());
				CacheHelper.setCache(Constants.FORM_CONFIG_CACHE, bmbatch.getId(),formConfig);
			}
			if (formConfig != null) {
				mv.addObject("formConfig", formConfig);
				mv.addObject("formConfigJson", formConfig.toString());
			}
			else {
				throw BizException.withMessage("获取表单模板配置失败，请检查表单模板是否已经配置");
			}
		}
		String pageViewSuffix = null;
		if (StringUtils.isEmpty(id)) {
			pageViewSuffix = "startPage";
		} else {
			pageViewSuffix = "editPage";
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/" + bmbatchType.toLowerCase() + "/" + pageViewSuffix);
		return mv;
	}

	/**
	 * 获取批次下开放报名的职业
	 */
	@RequestMapping("/getOpenedProfession")
	@ResponseBody
	public Object getOpenedProfession(HttpServletRequest request, String bmbatchId, String industryId) {
		if (StringUtils.isEmpty(bmbatchId) || StringUtils.isEmpty(industryId)) {
			return Collections.EMPTY_LIST;
		}
		return studentBmService.getOpenedProfession(bmbatchId, industryId);
	}

	/**
	 * 获取职业等级、职业方向
	 */
	@RequestMapping("/getLevelAndDirection")
	@ResponseBody
	public Object getLevelAndDirection(HttpServletRequest request, String bmbatchId, String industryId,
			String professionId) {
		if (StringUtils.isEmpty(bmbatchId) || StringUtils.isEmpty(industryId) || StringUtils.isEmpty(professionId)) {
			return Collections.EMPTY_LIST;
		}
		EntityWrapper<ZyjdBmScope> wrapper = new EntityWrapper<ZyjdBmScope>();
		wrapper.eq("bmbatch_id", bmbatchId);
		wrapper.eq("industry_id", industryId);
		wrapper.eq("profession_id", professionId);
		List<ZyjdBmScope> list = bmScopeService.selectList(wrapper);
		if (list.size() == 0) {
			return Collections.EMPTY_LIST;
		}
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map<String, Object>> levelList = new ArrayList<Map<String, Object>>();
		String[] levelArray = StringUtils.split(list.get(0).getTechLevel(), ",");
		for (String a : levelArray) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("id", TechLevel.findByEnumName(a).getId());
			map.put("text", TechLevel.findByEnumName(a).getName());
			map.put("desc", TechLevel.findByEnumName(a).getDesc());
			map.put("value", a);
			levelList.add(map);
		}
		result.put("levelList", levelList);
		result.put("directionList", studentBmService.getDirectionByProfessionId(professionId));
		return result;
	}

	/**
	 * 七大员 由职业获取职业方向
	 */
	@RequestMapping("/getQdyDirection")
	@ResponseBody
	public Object getQdyDirection(HttpServletRequest request, String professionId) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("directionList", studentBmService.getDirectionByProfessionId(professionId));
		return result;
	}

	/**
	 * 报名信息提交
	 */
	@RequestMapping("/create")
	@ResponseBody
	@JsonIgnoreProperties(ignoreUnknown = true)
	public Object bm(HttpServletRequest request, @RequestBody ZyjdStudentBmParams params) throws Exception {
		params.setHostOrgId(getCurrentHostOrgId(request));
		String id = studentBmService.createBmInfo(params);
		return id;
	}

	/**
	 * 地质测绘 下一步
	 */
	@RequestMapping("/nextStep")
	public Object nextStep(ModelAndView mv, @RequestParam(required = false) String id) {
		ZyjdBm zyjdBm = zyjdBmService.selectById(id);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名信息不存在");
		}
		ZyjdBmBatch zyjdBmBatch = zyjdBmBatchService.selectById(zyjdBm.getBmbatchId());
		mv.addObject("bm", zyjdBm);
		mv.addObject("batch", zyjdBmBatch);
		mv.setViewName(PAGE_VIEW_PREFIX + "/tddzcl/regForm");
		return mv;
	}

	/**
	 * 地质测绘 提交报名信息
	 */
	@RequestMapping("/dzclSubmit")
	@ResponseBody
	@JsonIgnoreProperties(ignoreUnknown = true)
	public Object dzclSubmit(HttpServletRequest request, @RequestParam(required = false) String id,
						   @RequestParam(required = false) String yqrbmb) throws Exception {
		ZyjdBm zyjdBm = zyjdBmService.selectById(id);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名信息不存在");
		}
		if (zyjdBm.getStatus() != ZyjdBmStatus.DTJ) {
			throw BizException.withMessage("报名信息已经提交审核，不能重复提交");
		}
		zyjdBm.setYqrbmb(yqrbmb);
		zyjdBm.setStatus(ZyjdBmStatus.YTJ);
		zyjdBmService.updateById(zyjdBm);
		return true;
	}

	/**
	 * 修改报名信息
	 */
	@RequestMapping("/edit")
	@ResponseBody
	@JsonIgnoreProperties(ignoreUnknown = true)
	public Object edit(HttpServletRequest request, @RequestBody ZyjdStudentBmParams params)
			throws Exception {
		// 判断该考生报名是否通过审核,审核后不能修改
		ZyjdBm zyjdBm = studentBmService.selectById(params.getId());
		if (zyjdBm == null) {
			throw BizException.withMessage("该报名信息不存在");
		}
		if (zyjdBm.getStatus() == ZyjdBmStatus.SHTG) {
			throw BizException.withMessage("您的报名信息已经审核通过,不能修改!");
		}
		params.setHostOrgId(getCurrentHostOrgId(request));
		studentBmService.editBmInfo(params, zyjdBm);
		return params.getId();
	}

	/**
	 * 七大员新资格证书上传
	 */
	@RequestMapping("/qdyNewCertiUpload")
	@ResponseBody
	@JsonIgnoreProperties(ignoreUnknown = true)
	public Object qdyNewCertiUpload(HttpServletRequest request, @RequestBody ZyjdStudentBmParams params)
			throws Exception {
		ZyjdBm zyjdBm = studentBmService.selectById(params.getId());
		if (zyjdBm == null) {
			throw BizException.withMessage("该报名信息不存在");
		}
		zyjdBm.setNewCertificate(params.getNewCertificate());
		studentBmService.updateById(zyjdBm);
		return params.getId();
	}

	/**
	 * 土地地质测绘上传其他附件：报名表、健康码、行程卡
	 */
	@RequestMapping("/tddzcUploadAttr")
	@ResponseBody
	@JsonIgnoreProperties(ignoreUnknown = true)
	public Object tddzcUploadAttr(HttpServletRequest request, @RequestBody ZyjdStudentBmParams params)
			throws Exception {
		ZyjdBm zyjdBm = studentBmService.selectById(params.getId());
		if (zyjdBm == null) {
			throw BizException.withMessage("该报名信息不存在");
		}
		if (StringUtils.isNotEmpty(params.getConfirmedBmb())) {
			zyjdBm.setYqrbmb(params.getConfirmedBmb());
		}
		if (StringUtils.isNotEmpty(params.getHealthyCode())) {
			zyjdBm.setHealthyCode(params.getHealthyCode());
		}
		if (StringUtils.isNotEmpty(params.getTripCard())) {
			zyjdBm.setTripCard(params.getTripCard());
		}
		studentBmService.updateById(zyjdBm);
		return params.getId();
	}

	/**
	 * 登录页面
	 */
	@RequestMapping("/loginPage")
	public Object loginPage(HttpServletRequest request, ModelAndView mv) {
		String bmbatchId = request.getParameter("bmbatchId");
		ZyjdBmBatch batch = bmBatchService.selectById(bmbatchId);
		String type = request.getParameter("lx");
		if (batch == null && StringUtils.isNotEmpty(type)) {//根据类型获取当前最近开放的批次,国土水平能力测试用得到
			batch = bmBatchService.getLatestOpenBatch(ZyjdBmBatchType.valueOf(type), super.getCurrentHostOrgId(request));
		}
		mv.addObject("batch", batch);
		String str = "login";
		if (batch.getType() == ZyjdBmBatchType.ZYJS || batch.getType() == ZyjdBmBatchType.ZCPS) {
			// 判断是否是移动端跳转
			String url = "";
			if (batch.getType() == ZyjdBmBatchType.ZCPS && isMobile(request.getHeader("User-Agent"))) {
				url = "mobile-";
			}
			str = batch.getType().name().toLowerCase() + "/" + url + str;
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/" + str);
		return mv;
	}

	/**
	 * 报名系统登录接口
	 */
	@RequestMapping("/dologin")
	@ResponseBody
	public Object dologin(HttpServletRequest request, @RequestParam(required = false) String sfzh,
			@RequestParam(required = false) String password, @RequestParam(required = false) String mobile,@RequestParam(required = false) String code) {
		StudentUser studentUser = studentUserService.findBySfzh(sfzh, super.getCurrentHostOrgId(request));
		if (studentUser == null) {
			throw BizException.withMessage("身份证号在系统中不存在");
		}
		if (DigestUtils.md5Hex(password).equals(studentUser.getPassword())) {
			LoginStudent loginStudent = new LoginStudent();
			loginStudent.setUser(studentUser);
			StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
			if (studentInfo == null) {
				throw BizException.withMessage("获取考生信息失败");
			}
			//填写了手机号的 需要校验短信验证码
			if (StringUtils.isNotEmpty(mobile)) {
				if (StringUtils.isEmpty(code)) {
					throw BizException.withMessage("请输入短信验证码");
				}
				String checkCode = (String)request.getSession().getAttribute("REGISTER_VERIFY_CODE");
				if (!code.equals(checkCode)) {
					throw BizException.withMessage("短信验证码错误");
				}
			}
			loginStudent.setInfo(studentInfo);
			studentInfo.setMobile(mobile);
			studentInfoService.updateById(studentInfo);
			request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
			return true;
		}
		throw BizException.withMessage("用户名或密码错误");
	}

	/**
	 * 微信支付
	 */
	@RequestMapping("/wxJsApiPay/{id}")
	public ModelAndView wxJsApiPagy(HttpServletRequest request, ModelAndView mv, @PathVariable("id") String id,
			@RequestParam(required = false) String isPayUpgradeAmount) {
		try {
			String code = request.getParameter("code");
			Double money = studentBmService.getBmFee(id, getCurrentHostOrgId(request));
			Map<String, Object> bmInfo = studentBmService.getDetailsById(id);
			String bmbatchId = bmInfo.get("bmbatchId").toString();
			String studentId = bmInfo.get("studentId").toString();
			String name = bmInfo.get("name").toString();
			String sfzh = bmInfo.get("sfzh").toString();
			String professionCode = bmInfo.get("professionCode").toString();
			String professionName = bmInfo.get("professionName").toString();
			String applyTechLevel = bmInfo.get("applyTechLevel").toString();
			String description = name + "-" + sfzh + "-" + professionName + "(" + professionCode + ")-" + applyTechLevel
					+ "-继教平台-职业鉴定报名费用";
			if (Constants.YES.equals(isPayUpgradeAmount)) {
				description += "及培训费";
				Double upgradeMoney = studentBmService.getUpgradeFee(id, getCurrentHostOrgId(request));
				if (upgradeMoney != null) {
					money = money + upgradeMoney;
				}
			}
			FeeType[] feeTypeArr = new FeeType[] { FeeType.ZYJDZC };
			String[] ywIdArr = new String[] { id };
			Double[] amountArr = new Double[] { money };
			ZyjdBmBatch batch = bmBatchService.selectById(bmbatchId);
			CommOrder commDd = orderService.createOrderByStudent(FeeCategory.ZYJDZC, studentId,
					getCurrentHostOrgId(request), feeTypeArr, ywIdArr, amountArr, description, description,
					batch.getBankId());
			// 生成支付信息
			String paymentInfo = orderService.goPay(commDd.getId(), PayMethod.WX_JSAPI_PAY, code, null);
			LOGGER.info("支付信息：" + paymentInfo);
			JSONObject payJsonObject = JSONObject.fromObject(paymentInfo);
			String appId = payJsonObject.getString("appId");
			String nonceStr = payJsonObject.getString("nonceStr");
			String timeStamp = payJsonObject.getString("timeStamp");
			String packageValue = payJsonObject.getString("package");
			String signType = payJsonObject.getString("signType");
			String paySign = payJsonObject.getString("paySign");
			mv.addObject("appId", appId);
			mv.addObject("timeStamp", timeStamp);
			mv.addObject("nonceStr", nonceStr);
			mv.addObject("package", packageValue);
			mv.addObject("signType", signType);
			mv.addObject("paySign", paySign);
			mv.addObject("batch", batch);
		} catch (Exception e) {
			LOGGER.error("生成微信JSAPI支付信息失败:", e);
			mv.addObject("errorMesage", e.getMessage());
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/"+ "jsApiPay");
		return mv;
	}

	/**
	 * 微信扫码支付
	 */
	@RequestMapping("/wxPcPay/{id}")
	@ResponseBody
	public Object wxPcPay(HttpServletRequest request, ModelAndView mv, @PathVariable("id") String id,
									@RequestParam(required = false) String isPayUpgradeAmount) {
		try {
			Double money = studentBmService.getBmFee(id, getCurrentHostOrgId(request));
			Map<String, Object> bmInfo = studentBmService.getDetailsById(id);
			String bmbatchId = bmInfo.get("bmbatchId").toString();
			String studentId = bmInfo.get("studentId").toString();
			String name = bmInfo.get("name").toString();
			String sfzh = bmInfo.get("sfzh").toString();
			String professionCode = bmInfo.get("professionCode").toString();
			String professionName = bmInfo.get("professionName").toString();
			String applyTechLevel = bmInfo.get("applyTechLevel").toString();
			String description = name + "-" + sfzh + "-" + professionName + "(" + professionCode + ")-" + applyTechLevel
					+ "-继教平台-职业鉴定报名费用";
			if (Constants.YES.equals(isPayUpgradeAmount)) {
				description += "及培训费";
				Double upgradeMoney = studentBmService.getUpgradeFee(id, getCurrentHostOrgId(request));
				if (upgradeMoney != null) {
					money = money + upgradeMoney;
				}
			}
			FeeType[] feeTypeArr = new FeeType[] { FeeType.ZYJDZC };
			String[] ywIdArr = new String[] { id };
			Double[] amountArr = new Double[] { money };
			ZyjdBmBatch batch = bmBatchService.selectById(bmbatchId);
			CommOrder commDd = orderService.createOrderByStudent(FeeCategory.ZYJDZC, studentId,
					getCurrentHostOrgId(request), feeTypeArr, ywIdArr, amountArr, description, description,
					batch.getBankId());
			// 生成支付信息
			return orderService.goPay(commDd.getId(), PayMethod.WX_PC_PAY, null, null);
		} catch (Exception e) {
			LOGGER.error("生成微信pc支付信息失败:", e);
			mv.addObject("errorMesage", e.getMessage());
		}
		return null;
	}

	/**
	 * 支付宝支付
	 */
	@RequestMapping("/aliWapPay/{id}")
	public void aliWapPay(HttpServletRequest request, HttpServletResponse response, @PathVariable("id") String id,
			@RequestParam(required = false) String isPayUpgradeAmount) {
		try {
			// 获取职业鉴定的费用
			Double money = studentBmService.getBmFee(id, getCurrentHostOrgId(request));
			// 下单
			Map<String, Object> bmInfo = studentBmService.getDetailsById(id);
			String bmbatchId = bmInfo.get("bmbatchId").toString();
			String studentId = bmInfo.get("studentId").toString();
			String name = bmInfo.get("name").toString();
			String sfzh = bmInfo.get("sfzh").toString();
			String professionCode = bmInfo.get("professionCode").toString();
			String professionName = bmInfo.get("professionName").toString();
			String applyTechLevel = bmInfo.get("applyTechLevel").toString();
			String description = name + "-" + sfzh + "-" + professionName + "(" + professionCode + ")-" + applyTechLevel
					+ "-继教平台-职业鉴定报名费用";
			if (Constants.YES.equals(isPayUpgradeAmount)) {
				description += "及培训费";
				Double upgradeMoney = studentBmService.getUpgradeFee(id, getCurrentHostOrgId(request));
				if (upgradeMoney != null) {
					money = money + upgradeMoney;
				}
			}
			FeeType[] feeTypeArr = new FeeType[] { FeeType.ZYJDZC };
			String[] ywIdArr = new String[] { id };
			Double[] amountArr = new Double[] { money };
			ZyjdBmBatch batch = bmBatchService.selectById(bmbatchId);
			CommOrder commOrder = orderService.createOrderByStudent(FeeCategory.ZYJDZC, studentId,
					getCurrentHostOrgId(request), feeTypeArr, ywIdArr, amountArr, description, description,
					batch.getBankId());
			String form = orderService.goPay(commOrder.getId(), PayMethod.ALI_WAP_PAY, null, null);
			response.setContentType("text/html;charset=utf-8");
			response.getWriter().write(form);// 直接将完整的表单html输出到页面
			response.getWriter().flush();
			response.getWriter().close();
		} catch (Exception e) {
			LOGGER.error("生成支付宝缴费信息失败:", e);
		}
	}

	/**
	 * 聚合支付
	 */
	@RequestMapping("/juhePay/{id}")
	public ModelAndView wapPay(HttpServletRequest request, HttpServletResponse response, ModelAndView mv,
			@PathVariable("id") String id,@RequestParam(required = false) String isPayUpgradeAmount) {
		try {
			// 获取职业鉴定的费用
			Double money = studentBmService.getBmFee(id, getCurrentHostOrgId(request));
			// 下单
			Map<String, Object> bmInfo = studentBmService.getDetailsById(id);
			String bmbatchId = bmInfo.get("bmbatchId").toString();
			String studentId = bmInfo.get("studentId").toString();
			String name = bmInfo.get("name").toString();
			String sfzh = bmInfo.get("sfzh").toString();
			String professionCode = bmInfo.get("professionCode").toString();
			String professionName = bmInfo.get("professionName").toString();
			String applyTechLevel = bmInfo.get("applyTechLevel").toString();
			String description = name + "-" + sfzh + "-" + professionName + "(" + professionCode + ")-" + applyTechLevel
					+ "-继教平台-职业鉴定报名费用";
			//是否缴纳培训费
			if (Constants.YES.equals(isPayUpgradeAmount)) {
				description += "及培训费";
				Double upgradeMoney = studentBmService.getUpgradeFee(id, getCurrentHostOrgId(request));
				if (upgradeMoney != null) {
					money = money + upgradeMoney;
				}
			}
			FeeType[] feeTypeArr = new FeeType[] { FeeType.ZYJDZC };
			String[] ywIdArr = new String[] { id };
			Double[] amountArr = new Double[] { money };
			ZyjdBmBatch batch = bmBatchService.selectById(bmbatchId);
			CommOrder commOrder = orderService.createOrderByStudent(FeeCategory.ZYJDZC, studentId,
					getCurrentHostOrgId(request), feeTypeArr, ywIdArr, amountArr, description, description,
					batch.getBankId());
			// 工行支付--聚合支付
			JSONObject icbcConfig = bankConfigService.getICBCPayConfig(commOrder.getBankId());
			PayMethod payMethod = null;
			if (icbcConfig != null) {
				payMethod = PayMethod.ICBC_JUHE_MOBILE_PAY;
			}
			// 建行-聚合聚合支
			JSONObject ccbConfig = bankConfigService.getCCBPayConfig(commOrder.getBankId());
			if (ccbConfig != null) {
				payMethod = PayMethod.CCB_JUHE_MOBILE_PAY;
			}
			String payUrl = orderService.goPay(commOrder.getId(), payMethod, null, null);
			mv.addObject("payUrl", payUrl);
		} catch (Exception e) {
			LOGGER.error("生成职业鉴定聚合支付信息失败:", e);
			mv.addObject("errorMesage", e.getMessage());
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/"+  "juhePay");
		return mv;
	}

	/**
	 * 首页
	 */
	@RequestMapping("/indexPage")
	public Object indexPage(HttpServletRequest request, ModelAndView mv) throws UnsupportedEncodingException {
		String bmbatchId = request.getParameter("bmbatchId");
		ZyjdBmBatch bmbatch = bmBatchService.selectById(bmbatchId);
		mv.addObject("bmbatch", bmbatch);
		LoginStudent loginStudent = (LoginStudent) request.getSession().getAttribute(Constants.STUDENT_USER_SESSION_KEY);
		if (loginStudent != null) {
			EntityWrapper<ZyjdBm> bmWrapper = new EntityWrapper<>();
			bmWrapper.eq("bmbatch_id", bmbatch.getId());
			bmWrapper.eq("student_id", loginStudent.getUser().getId());
			List<ZyjdBm> bmList = studentBmService.selectList(bmWrapper);
			List<Map<String, Object>> bmInfos = new ArrayList<>();
			for (ZyjdBm zyjdBm : bmList) {
				Map<String, Object> bmInfo = studentBmService.getDetailsById(zyjdBm.getId());
				bmInfo.put("attachments", attachmentService.selectList((EntityWrapper<Attachment>) new EntityWrapper<Attachment>().eq("yw_id", bmInfo.get("id"))));
				if (BaseUtil.getStringValueFromMap(bmInfo, "oldTechLevel") != null) {
					bmInfo.put("old_tech_level",
							TechLevel.valueOf(BaseUtil.getStringValueFromMap(bmInfo, "oldTechLevel")).getName());
				}
				if (BaseUtil.getStringValueFromMap(bmInfo, "applyTechLevel") != null) {
					bmInfo.put("apply_tech_level",
							TechLevel.valueOf(BaseUtil.getStringValueFromMap(bmInfo, "applyTechLevel")).getName());
				}
				Double money = studentBmService.getBmFee(zyjdBm.getId(), getCurrentHostOrgId(request));
				Double upgradeMoney = studentBmService.getUpgradeFee(zyjdBm.getId(), getCurrentHostOrgId(request));
				
				if (money != null && PayStatus.YJ != zyjdBm.getPayStatus()) {
					String studentDomain = super.getCurrentHostOrgPortalWebUrl(request);
					String bankId = bmbatch.getBankId();
					if (StringUtils.isEmpty(bankId)) {
						Bank defaultBank = bankService.getDefaultBank(super.getCurrentHostOrgId(request));
						if (defaultBank != null) {
							bankId = defaultBank.getId();
						}
					}
					if (StringUtils.isNotEmpty(bankId)) {
						// 微信支付
						JSONObject wxConfig = bankConfigService.getWxPayConfig(bankId);
						if (wxConfig != null) {
							//微信公众号支付
							String wxpayurl = MessageFormat.format("{0}/kaosheng/mobile/zyjd/bm/wxJsApiPay/{1}", 
									studentDomain, zyjdBm.getId());
							bmInfo.put("wxpayurl", wxpayurl);
							bmInfo.put("wx_app_id", wxConfig.getString("wx_app_id"));
						}
						// 支付宝支付
						JSONObject aliConfig = bankConfigService.getAliPayConfig(bankId);
						if (aliConfig != null) {
							String aliPayUrl = studentDomain + "/kaosheng/mobile/zyjd/bm/aliWapPay/" + zyjdBm.getId();
							bmInfo.put("alipayurl", aliPayUrl);
						}
						// 工行支付--聚合支付
						JSONObject icbcConfig = bankConfigService.getICBCPayConfig(bankId);
						if (icbcConfig != null) {
							String payUrl = studentDomain + "/kaosheng/mobile/zyjd/bm/juhePay/" + zyjdBm.getId();
							bmInfo.put("payurl", payUrl);
						}
						// 建行支付--聚合支付
						JSONObject ccbConfig = bankConfigService.getCCBPayConfig(bankId);
						if (ccbConfig != null) {
							String payUrl = studentDomain + "/kaosheng/mobile/zyjd/bm/juhePay/" + zyjdBm.getId();
							bmInfo.put("payurl", payUrl);
						}
					}
				}
				bmInfo.put("jfEndTime",bmbatch.getJfEndTime() != null ? DateUtils.format(bmbatch.getJfEndTime()) : "");
				bmInfo.put("money", money);
				bmInfo.put("upgrade_money", upgradeMoney);
				bmInfos.add(bmInfo);
			}
			mv.addObject("bmInfos", bmInfos);
			mv.addObject("select", dictService.getZyjsbmDictList());
			mv.addObject("studentInfo", studentInfoService.getByStudentId(loginStudent.getUser().getId()));
			mv.addObject("bmInfo", CollectionUtils.isNotEmpty(bmInfos) ? bmInfos.get(0) : new HashMap<String, Object>());
			mv.setViewName(PAGE_VIEW_PREFIX + "/" + bmbatch.getType().name().toLowerCase() + "/" + "index");
		}
		else {
			mv.setViewName("redirect:/kaosheng/mobile/zyjd/bm/loginPage?bmbatchType=" + bmbatch.getType() + "&bmbatchId="
					+ bmbatch.getId());
		}
		return mv;
	}

	/**
	 * 删除报名信息
	 */
	@RequestMapping("/deleteById")
	@ResponseBody
	public Object deleteById(HttpServletRequest request, String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请传入报名ID");
		}
		ZyjdBm zyjdBm = studentBmService.selectById(id);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名信息在系统中不存在");
		}
		if (PayStatus.YJ == zyjdBm.getPayStatus()) {
			throw BizException.withMessage("您报名的这个职业已经缴费，无法删除");
		}
		studentBmService.deleteById(id);
		return true;
	}

	/**
	 * 获取工种的报考条件
	 */
	@RequestMapping("/getCondition")
	@Operation(desc = "获取工种的报考条件", loginRequired = false)
	@ResponseBody
	public Object getCondition(HttpServletRequest request, String professionId, TechLevel techLevel) {
		if (StringUtils.isEmpty(professionId) || techLevel == null) {
			return Collections.EMPTY_LIST;
		}
		return studentBmService.getCondition(professionId, techLevel);
	}

	/**
	 * 下载土地地质测绘准考证
	 */
	@RequestMapping("/downloadTddzclZkz")
	@Operation(desc = "下载土地地质测绘准考证")
	public void downloadTddzclZkz(HttpServletRequest request, HttpServletResponse response, String id)
			throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("报名ID不能为空");
		}

		OutputStream os = null;
		InputStream is = null;
		try {
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition", "attachment;filename=tddzcl-kaoshi_zkz.docx");
			os = response.getOutputStream();
			// 准考证的字段值获取
			Map<String, Object> zcxx = studentBmService.getZkzById(id);
			// 准考证值的拼接
			File file = studentBmService.creatTddzlclZkz(zcxx);
			is = new FileInputStream(file);
			IOUtils.copy(is, os);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
	}

	/**
	 * 获取工种的报考条件
	 */
	@RequestMapping("/getStudentInfo")
	@Operation(desc = "获取工种的报考条件", loginRequired = false)
	@ResponseBody
	public Object getSchoolStudentInfo(HttpServletRequest request, @RequestParam(required = false) String sfzh) {
		if (StringUtils.isEmpty(sfzh)) {
			return null;
		}
		StudentUser studentUser = studentUserService.findBySfzh(sfzh, super.getCurrentHostOrgId(request));
		if (studentUser != null) {
			StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
			return studentInfo;
		}
		return null;
	}

	/**
	 * 下载土地地质测绘报名表
	 */
	@RequestMapping("/downloadBmSheet")
	@Operation(desc = "下载土地地质测绘报名表")
	public void downloadBmSheet(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请传入报名ID");
		}
		OutputStream os = null;
		InputStream is = null;
		try {
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition", "attachment;filename=tddzcl_bmb.docx");
			os = response.getOutputStream();
			Map<String, Object> bmInfo = studentBmService.getDetailsById(id);
			String applyTechLevel = BaseUtil.convertNullToEmpty(bmInfo.get("applyTechLevel"));
			// 中级 副高级 是笔试 正高级是 面试
			File file = studentBmService.creatTddzlclBmb(bmInfo,
					TechLevel.ZHONG.name().equals(applyTechLevel) || TechLevel.FGAO.name().equals(applyTechLevel));
			is = new FileInputStream(file);
			IOUtils.copy(is, os);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
	}


	/**
	 * 答辩评审学生报名首页
	 */
	@RequestMapping("/home/<USER>")
	public Object bmPage(HttpServletRequest request, ModelAndView mv, @PathVariable("bmbatchId") String bmbatchId) {
		ZyjdBmBatch bmbatch = bmBatchService.selectById(bmbatchId);
		if (bmbatch.getStatus() == BmOpenStatus.BLOCK && Constants.YES.equals(bmbatch.getIsAutoNextBatch())) {
			bmbatch = bmBatchService.getLatestOpenBatch(bmbatch.getType(), super.getCurrentHostOrgId(request));
			if (bmbatch == null) {
				throw BizException.withMessage("未获取到同类型下已经开放的报名批次");
			}
		}
		if (bmbatch != null) {
			mv.addObject("bmbatch", bmbatch);
			List<ZyjdIndustry> industryList = studentBmService.getIndustryByBmbatchId(bmbatch.getId());
			mv.addObject("industryList", industryList);
			if (industryList.size() > 0) {
				String industryId = industryList.get(0).getId();
				mv.addObject("industryId", industryId);
				List<Map<String, Object>> professionList = studentBmService.getOpenedProfession(bmbatch.getId(),
						industryList.stream().map(ZyjdIndustry::getId).collect(Collectors.toList()));
				List<Map<String, Object>> professions = new ArrayList<>();
				LoginStudent loginStudent = (LoginStudent) request.getSession().getAttribute(Constants.STUDENT_USER_SESSION_KEY);
				List<ZyjdBm> bmList = null;
				if (loginStudent != null) {
					String studentId = loginStudent.getUser().getId();
					StudentInfo studentInfo = studentInfoService.getByStudentId(studentId);
					mv.addObject("username", studentInfo.getName());
					EntityWrapper<ZyjdBm> bmWrapper = new EntityWrapper<>();
					bmWrapper.eq("bmbatch_id", bmbatchId);
					bmWrapper.eq("student_id", studentId);
					bmList = zyjdBmService.selectList(bmWrapper);
				}
				for (Map<String, Object> zyjdProfession : professionList) {
					com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) JSON.toJSON(zyjdProfession);
					String[] techLevels = zyjdProfession.get("techLevel").toString().split(",");
					for (int i = 0; i < techLevels.length; i++) {
						techLevels[i] = TechLevel.findByEnumName(techLevels[i]).getDesc();
					}
					json.put("techLevelDesc", String.join(",", techLevels));
					if (bmList != null) {
						ZyjdBm zyjdBm = bmList.stream().filter(x -> x.getProfessionId().equals(zyjdProfession.get("id").toString())).findFirst().orElse(null);
						if (zyjdBm != null) {
							json.put("bmId",zyjdBm.getId());
						}
					}
					professions.add(json);
				}
				mv.addObject("professionList", professions);
			}
		} else {
			throw BizException.withMessage("报名批次不存在");
		}
		// 判断是否是移动端跳转
		String url = "";
		if (isMobile(request.getHeader("User-Agent"))) {
			url = "mobile-";
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/zcps/" + url + "home");
		return mv;
	}

	/**
	 * 职业鉴定手机端报名入口
	 */
	@RequestMapping("/startPage-zc/{bmbatchId}")
	public Object bmPage(HttpServletRequest request, ModelAndView mv, @PathVariable("bmbatchId") String bmbatchId,
						 @RequestParam(required = false) String industryId,
						 @RequestParam(required = false) String scopeId,
						 @RequestParam(required = false) String id) {
		Map<String, Object> bmInfo = null;
		ZyjdBmBatch bmbatch = null;
		if (StringUtils.isNotEmpty(id)) {//获取已有报名信息
			bmInfo = service.getDetailsById(id);
			bmInfo.put("statusDesc",bmInfo.get("status") == null ? "" : ZyjdBmStatus.valueOf(bmInfo.get("status").toString()).getName());
			bmbatchId = BaseUtil.getStringValueFromMap(bmInfo, "bmbatchId");
			bmbatch = bmBatchService.selectById(bmbatchId);
			mv.addObject("result", bmInfo);
		}
		else {
			bmbatch = bmBatchService.selectById(bmbatchId);
			if (bmbatch.getStatus() == BmOpenStatus.BLOCK && Constants.YES.equals(bmbatch.getIsAutoNextBatch())) {
				bmbatch = bmBatchService.getLatestOpenBatch(bmbatch.getType(), super.getCurrentHostOrgId(request));
				if (bmbatch == null) {
					throw BizException.withMessage("未获取到同类型下已经开放的报名批次");
				}
			}
			ZyjdBmScope zyjdBmScope = bmScopeService.selectById(scopeId);
			if (zyjdBmScope != null) {
				Integer maxPersonNum = zyjdBmScope.getMaxPersonNum();
				if (maxPersonNum != null) {
					Integer count = zyjdBmService.selectCount((EntityWrapper<ZyjdBm>) new EntityWrapper<ZyjdBm>()
							.eq("bmbatch_id", bmbatchId)
							.eq("industry_id", industryId)
							.eq("profession_id", zyjdBmScope.getProfessionId())
							.in("apply_tech_level", zyjdBmScope.getTechLevel().split(","))
							.eq("status", ZyjdBmStatus.SHTG));
					if (count >= maxPersonNum) {
						throw BizException.withMessage("已到达报名人数上限，请联系管理员");
					}
				}
			}
		}
		List<Dict> provinceList = dictService.getProvinceByDictCode();
		mv.addObject("provinceList", provinceList);
		//查询当前主办单位ID的二级学院
		mv.addObject("colleges", orgService.getChildren(super.getCurrentHostOrgId(request)).stream().filter(x ->
				Constants.YES.equals(x.getIsCollege())).collect(Collectors.toList()));
		// 查询直属单位
		EntityWrapper<InfUnit> wrapper = new EntityWrapper<>();
		wrapper.eq("status", Zt.OK.name());
		mv.addObject("unitList", zcpsDirectlyUnitsService.selectList(wrapper));
		if (bmbatch != null) {
			mv.addObject("bmbatch", bmbatch);
			List<ZyjdIndustry> industryList = new ArrayList<>();
			industryList.add(zyjdIndustryService.selectById(industryId));
			if (bmInfo != null) {
				industryId = BaseUtil.getStringValueFromMap(bmInfo, "industryId");
			}
			mv.addObject("industryId", industryId);
			mv.addObject("industryList", industryList);
			List<ZyjdProfession> professionList = studentBmService.getOpenedProfession(bmbatch.getId(),
					industryId);
			mv.addObject("professionList", professionList);
		} else {
			throw BizException.withMessage("报名批次不存在");
		}
		String bmbatchType = bmbatch != null ? bmbatch.getType().name() : ZyjdBmBatchType.ZYJD.name();
		String pageViewSuffix = "";
		if (StringUtils.isNotEmpty(id)) {
			mv.addObject("id", id);
			pageViewSuffix = "edit-";
		}
		// 判断是否是移动端跳转
		String url = "";
		if (isMobile(request.getHeader("User-Agent"))) {
			url = "mobile-";
		}
		mv.setViewName(PAGE_VIEW_PREFIX + "/" + bmbatchType.toLowerCase() + "/" + url + pageViewSuffix + industryId.toLowerCase());
		return mv;
	}

	private boolean isMobile(String userAgent) {
		return true;
//		String[] mobileAgents = {"Mobile", "Android", "iPhone", "iPad", "Windows Phone", "BlackBerry"};
//		return Arrays.stream(mobileAgents).anyMatch(userAgent::contains);
	}
}
