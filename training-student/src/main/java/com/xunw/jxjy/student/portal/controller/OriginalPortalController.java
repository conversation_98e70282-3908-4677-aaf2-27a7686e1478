package com.xunw.jxjy.student.portal.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.sys.entity.*;
import com.xunw.jxjy.model.sys.service.*;
import com.xunw.jxjy.model.zypx.service.*;
import com.xunw.jxjy.student.core.base.HostOrgResolveService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.enums.Jjkhd;
import com.xunw.jxjy.model.enums.Zbzt;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.inf.service.CourseLiveService;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.inf.service.CoursewareMaterialService;
import com.xunw.jxjy.model.inf.service.CoursewareService;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.mobile.service.ZypxMobileBmService;
import com.xunw.jxjy.model.portal.service.IndexService;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.service.QuestionDB2CourseService;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.wdxx.entity.MyPracticeRecord;
import com.xunw.jxjy.model.wdxx.service.PortalPracticeRecordService;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.utils.ModelHelper;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 培训平台原始门户，非定制化门户
 * <AUTHOR>
 *
 */
@Controller
public class OriginalPortalController extends BaseController {

	private final static Logger logger = LoggerFactory.getLogger(OriginalPortalController.class);
	private static final Integer DEFAULT_PAGE_SIZE = 12;

	@Autowired
	private IndexService indexService;
	@Autowired
	private CoursewareService coursewareService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private NoticeService noticeService;
	@Autowired
	private UserService userService;
	@Autowired
	private ZypxXmService xmService;
	@Autowired
	private CoursewareMaterialService coursewareMaterialService;
	@Autowired
	private QuestionDBService questionDBService;
	@Autowired
	private PortalPracticeRecordService portalPracticeRecordService;
	@Autowired
	private ZypxExamDataService examDataService;
	@Autowired
	private ZypxLiveMaterialService liveMaterialService;
	@Autowired
	private ZypxLiveService liveService;
	@Autowired
	private CourseLiveService courseLiveService;
	@Autowired
	private ZypxMobileBmService mobileBmService;
	@Autowired
	private QuestionDB2CourseService question2CourseService;
	@Autowired
	private PxxmXmBmAdviceService pxxmXmBmAdviceService;
	@Autowired
	private HostOrgResolveService hostOrgResolveService;
	@Autowired
	private OrgService orgService;


	/**
	 * 通用门户首页加载
	 */
	@RequestMapping("/portal/index")
	public Object list(HttpServletRequest request, ModelAndView mv) throws Exception {
		//获取主办单位id
		String hostOrgId = getCurrentHostOrgId(request);
		List<Map<String, Object>> categoryList = indexService.getNoticeCategory(hostOrgId);
		mv.addObject("categoryList", categoryList);
		List<List<Map<String, Object>>> newsList = new ArrayList<List<Map<String, Object>>>();
		if (categoryList.size() > 0) {
			for (Map<String, Object> category : categoryList) {
				Map<String, Object> condition = new HashMap<String, Object>();
				condition.put("categoryId", BaseUtil.getStringValueFromMap(category, "id"));
				Page<Map<String, Object>> pageInfo = indexService.getNotice(condition, Constants.DEFAULT_PAGE_NUMBER,
						15);
				newsList.add(pageInfo.getRecords());
			}
		}
		mv.addObject("newsList", newsList);
		List<Map<String, Object>> hotXmList = indexService.getHotXm(hostOrgId);
		if(!hotXmList.isEmpty()){
			for(Map<String,Object> map:hotXmList){
				String id=BaseUtil.getStringValueFromMap(map,"id");
				List<Map<String,Object>> courseList= mobileBmService.getCourseByXmId(id);
				//项目的总课时
				Double totalHours = 0.0;
				for (Map<String, Object> course : courseList) {
					if (BaseUtil.isNotEmpty(course.get("hours"))) {
						totalHours += Double.parseDouble(course.get("hours").toString());
					}
				}
				Integer bmCount = indexService.selectCountByXmId(id);
				map.put("bm_count", bmCount);
				map.put("total_hours",totalHours);
			}
		}
		mv.addObject("xmList",hotXmList);
		//课程
		Map<String, Object> condition = new HashMap<String, Object>();
		condition.put("hostOrgId", hostOrgId);
		condition.put("type", "KJ");
		Page<Map<String, Object>> courseList = indexService.getCourse(condition, Constants.DEFAULT_PAGE_NUMBER,
				Constants.DEFAULT_PAGE_SIZE);
		//课件学习人数
		for(Map<String,Object> map:courseList.getRecords()){
			String courseId = BaseUtil.getStringValueFromMap(map,"id");
			Integer count= indexService.getStudyCount(courseId);
			//教师姓名
			Course course = courseService.selectById(courseId);
			EntityWrapper<Courseware> wrapper = new EntityWrapper<Courseware>();
			wrapper.eq("course_id", course.getId());
			List<Courseware> coursewareList = coursewareService.selectList(wrapper);
			if (CollectionUtils.isEmpty(coursewareList)) {
				map.put("kj", new Courseware());
			}
			Courseware courseware = coursewareList.get(0);
			map.put("kj", courseware);
			map.put("count",count);
		}
		mv.addObject("courseList",courseList.getRecords());
		Map<String, Object> jiangshiCondition = new HashMap<String, Object>();
		jiangshiCondition.put("hostOrgId", hostOrgId);
		Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(jiangshiCondition, Constants.DEFAULT_PAGE_NUMBER,
				Constants.DEFAULT_PAGE_SIZE);
		mv.addObject("teacherList", pageInfo.getRecords());
		Map<String, Object> bannerCondition = new HashMap<String, Object>();
		bannerCondition.put("hostOrgId", hostOrgId);
		Page<Map<String, Object>> syslbts = indexService.getBanner(bannerCondition, Constants.DEFAULT_PAGE_NUMBER,
				Constants.DEFAULT_PAGE_SIZE);
		mv.addObject("bannerList", syslbts.getRecords());
		mv.setViewName("pages/portal/original/index");
		return mv;
	}

	/**
	 * 新闻首页
	 */
	@RequestMapping("/portal/newsIndex")
	public Object newsIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize,
			String categoryId) throws Exception {
		// 取出职业培训所有的新闻类型
		List<Map<String, Object>> categorys = indexService.getNoticeCategory(super.getCurrentHostOrgId(request));
		mv.addObject("categorys", categorys);
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : Constants.DEFAULT_PAGE_SIZE;
		if (categorys.size() > 0) {
			// 默认展示第一个类型
			if (StringUtils.isEmpty(categoryId)) {
				mv.addObject("activeCategory", categorys.get(0));
			} else {
				List<Map<String, Object>> categoryList = categorys.stream()
						.filter(x -> BaseUtil.getStringValueFromMap(x, "id").equals(categoryId))
						.collect(Collectors.toList());
				mv.addObject("activeCategory", categoryList.get(0));
			}
			Map<String, Object> condition = new HashMap<String, Object>();
			condition.put("categoryId",
					StringUtils.isEmpty(categoryId) ? BaseUtil.getStringValueFromMap(categorys.get(0), "id")
							: categoryId);
			Page pageInfo = indexService.getNotice(condition, pageNum, pageSize);
			mv.addObject("newsList", pageInfo.getRecords());
			mv.addObject("pageSize", pageSize);
			mv.addObject("pageNum", pageNum);
			mv.addObject("count", pageInfo.getTotal());
		}
		mv.setViewName("pages/portal/original/newsIndex");
		return mv;
	}

	/**
	 * 培训首页
	 */
	@RequestMapping("/portal/trainingIndex")
	public Object trainingIndex(HttpServletRequest request, ModelAndView mv, String parentTypeId, String typeId,String keyword,
			Integer pageNum, Integer pageSize) throws Exception {
		// 获取所有的项目类型，只返回一级类型、二级类型
		List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
		Set<String> level1Ids = new HashSet<String>();
		List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.XM);
		for (Map<String, Object> typeMap : typeList) {
			level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
			level1Array.add(typeMap);
		}
		mv.addObject("level1Array", level1Array);
		if (StringUtils.isNotEmpty(parentTypeId)) {
			List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
			mv.addObject("level2Array", childTypes);
		} else {
			mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.XM));
		}

		// condition
		Map<String, Object> condition = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(typeId)) {
			condition.put("typeId", typeId);
		} else {
			condition.put("typeId", parentTypeId);
		}
		if (StringUtils.isNotEmpty(typeId)) {
			mv.addObject("curTypeId", typeId);
		}
		if (StringUtils.isNotEmpty(parentTypeId)) {
			mv.addObject("curParentTypeId", parentTypeId);
		}
		if (StringUtils.isNotEmpty(keyword)) {
			condition.put("keyword", keyword);
		}
		//获取主办单位ID
		condition.put("hostOrgId", getCurrentHostOrgId(request));
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
		Page pageInfo = indexService.getXmList(condition, pageNum, pageSize);
		mv.addObject("xmList", pageInfo.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("count", pageInfo.getTotal());
		mv.setViewName("pages/portal/original/trainingIndex");
		return mv;
	}

	/**
	 * 课程首页
	 */
	@RequestMapping("/portal/courseIndex")
	public Object courseIndex(HttpServletRequest request, ModelAndView mv, String parentTypeId, String typeId,
			Integer pageNum, Integer pageSize) throws Exception {
		// 获取所有的项目类型，只返回一级类型、二级类型
		List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
		Set<String> level1Ids = new HashSet<String>();
		List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
		for (Map<String, Object> typeMap : typeList) {
			level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
			level1Array.add(typeMap);
		}
		mv.addObject("level1Array", level1Array);
		if (StringUtils.isNotEmpty(parentTypeId)) {
			List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
			mv.addObject("level2Array", childTypes);
		} else {
			mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
		}
		Map<String, Object> condition = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(typeId)) {
			condition.put("typeId", typeId);
		} else {
			condition.put("typeId", parentTypeId);
		}
		if (StringUtils.isNotEmpty(typeId)) {
			mv.addObject("curTypeId", typeId);
		}
		if (StringUtils.isNotEmpty(parentTypeId)) {
			mv.addObject("curParentTypeId", parentTypeId);
		}
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
		//获取主办单位ID
		condition.put("hostOrgId", getCurrentHostOrgId(request));
		condition.put("type", "KJ");
		Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
		//课件学习人数
		for(Map<String,Object> map:pageInfo.getRecords()){
			String courseId = BaseUtil.getStringValueFromMap(map,"id");
			Integer count= indexService.getStudyCount(courseId);
			map.put("count",count);
		}
		mv.addObject("courseList", pageInfo.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("count", pageInfo.getTotal());
		mv.setViewName("pages/portal/original/courseIndex");
		return mv;
	}

	/**
	 * 师资首页
	 */
	@RequestMapping("/portal/teacherIndex")
	public Object teacherIndex(HttpServletRequest request, ModelAndView mv, Integer pageNum, Integer pageSize)
			throws Exception {
		Map<String, Object> condition = new HashMap<String, Object>();
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
		//获取主办单位id
		condition.put("hostOrgId", getCurrentHostOrgId(request));
		Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(condition, pageNum, pageSize);
		mv.addObject("teacherList", pageInfo.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("count", pageInfo.getTotal());
		mv.setViewName("pages/portal/original/teacherIndex");
		return mv;
	}

	/**
	 * 直播列表 在线课堂
	 */
	@RequestMapping("/portal/zhiboIndex")
	public Object zhiboIndex(HttpServletRequest request, ModelAndView mv, String parentTypeId, String typeId,
			Integer pageNum, Integer pageSize) throws Exception {
		// 获取所有的项目类型，只返回一级类型、二级类型
		List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
		Set<String> level1Ids = new HashSet<String>();
		List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
		for (Map<String, Object> typeMap : typeList) {
			level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
			level1Array.add(typeMap);
		}
		mv.addObject("level1Array", level1Array);
		if (StringUtils.isNotEmpty(parentTypeId)) {
			List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
			mv.addObject("level2Array", childTypes);
		} else {
			mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
		}
		Map<String, Object> condition = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(typeId)) {
			condition.put("typeId", typeId);
		} else {
			condition.put("typeId", parentTypeId);
		}
		if (StringUtils.isNotEmpty(typeId)) {
			mv.addObject("curTypeId", typeId);
		}
		if (StringUtils.isNotEmpty(parentTypeId)) {
			mv.addObject("curParentTypeId", parentTypeId);
		}
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
		//获取主办单位ID
		condition.put("hostOrgId", getCurrentHostOrgId(request));
		//获取主办单位Id
		condition.put("type", "ZB");
		Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
		mv.addObject("courseList", pageInfo.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("count", pageInfo.getTotal());
		mv.setViewName("pages/portal/original/zhiboIndex");
		return mv;
	}

	/**
	 * 直播详情
	 */
	@RequestMapping("/portal/zhiboDetail")
	public Object zhiboDetail(HttpServletRequest request, ModelAndView mv, String id, @RequestParam(required = false) String lessonId) throws Exception {
		CourseLive courseLive = courseLiveService.getByCourseId(id);
		Integer studyCount= indexService.getZbStudyCount(id);
		Integer bmCourseCount=indexService.getBmCourseCountByCourseId(id);
		String liveContent = courseLive != null ? courseLive.getContent() : null;
		Courselive courselive = ModelHelper.convertObject(liveContent);
		if (courselive != null) {
			List<com.xunw.jxjy.model.common.courselive.Chapter> chapters = courselive.getChapters();
			com.xunw.jxjy.model.common.courselive.Chapter activeChapter = null;
			com.xunw.jxjy.model.common.courselive.Lesson activeLesson = null;
			if(BaseUtil.isNotEmpty(chapters)){
				for(com.xunw.jxjy.model.common.courselive.Chapter chapter:chapters) {
					List<com.xunw.jxjy.model.common.courselive.Lesson> lessons = chapter.getLessons();
					for(com.xunw.jxjy.model.common.courselive.Lesson lesson:lessons) {
						Live live = liveService.selectById(lesson.getId());
						lesson.setZbzt(live.getZbzt());
						User user = userService.selectById(live.getJsId());
						lesson.setJsxm(user.getName());
						 //回放 直播状态已完成 并且 会话id为空
	                    if (Zbzt.FINISHED == live.getZbzt() && live.getHkspsc() != null && live.getHkspsc() > 0) {
	                        if (StringUtils.isEmpty(live.getHkhhida())) {
	                            lesson.setLiveDocPath(live.getHkdza());
	                        }
	                        if (StringUtils.isEmpty(live.getHkhhidb())) {
	                            lesson.setLiveTeacherPath(live.getHkdzb());
	                        }
	                    }
	                    //直播推流
	                    else if (Zbzt.PLAYING == live.getZbzt()) {
	                        lesson.setLiveDocPath(live.getHlsdza());
	                        lesson.setLiveTeacherPath(live.getHlsdzb());
	                        if (request.getScheme().indexOf("https") > -1) {
	                        	lesson.setLiveDocPath(lesson.getLiveDocPath().replace("http://pili-live-hls.zb.whxunw.com","https://pili-live-hls-zb.whxunw.com"));
	                        	lesson.setLiveTeacherPath(lesson.getLiveTeacherPath().replace("http://pili-live-hls.zb.whxunw.com","https://pili-live-hls-zb.whxunw.com"));
							}
	                        if (StringUtils.isEmpty(lessonId)) {
								activeLesson = lesson;
								activeChapter = chapter;
							}
	                    }
	                    if (StringUtils.isNotEmpty(lessonId) && lesson.getId().equals(lessonId)) {
	                    	activeLesson = lesson;
							activeChapter = chapter;
						}
					}
				}
				activeChapter = activeChapter != null ? activeChapter : chapters.get(0);
				activeLesson = activeLesson != null ? activeLesson : activeChapter.getLessons().get(0);
			}
			mv.addObject("courselive", courselive);//直播对象
			mv.addObject("activeLesson", activeLesson);
			mv.addObject("activeChapter", activeChapter);
			List<String> lessonIds = new ArrayList<String>();
			if (courselive != null) {
				courselive.getChapters().forEach(x->x.getLessons().forEach(y->{
					lessonIds.add(y.getId());
				}));
			}
			if (lessonIds != null && lessonIds.size() > 0) {
				Map<String, Object> condition = new HashMap<String, Object>();
				condition.put("liveIds", lessonIds);
				List<Map<String, Object>> materials = liveMaterialService.getList(condition);
				materials.forEach(x -> {
					//格式化文件大小显示
					x.put("file_size", BaseUtil.formatFileSize(Long.valueOf(BaseUtil.getStringValueFromMap(x, "fileSize"))));
				});
				mv.addObject("materials", materials);//所有资料
			}
		}
		mv.addObject("studyCount",studyCount);
		mv.addObject("bmCourseCount",bmCourseCount);
		mv.addObject("course", courseService.selectById(id));//课程对象
		mv.setViewName("pages/portal/original/zhiboPlay");
		return mv;
	}


	/**
	 * 题库列表
	 */
	@RequestMapping("/portal/questionDbIndex")
	public Object qrzkIndex(HttpServletRequest request, ModelAndView mv, String parentTypeId, String typeId, Integer pageNum,
			Integer pageSize) throws Exception {
		// 获取所有的项目类型，只返回一级类型、二级类型
		List<Map<String, Object>> level1Array = new ArrayList<Map<String, Object>>();
		Set<String> level1Ids = new HashSet<String>();
		List<Map<String, Object>> typeList = indexService.getTopType(super.getCurrentHostOrgId(request), TypeCategory.COURSE);
		for (Map<String, Object> typeMap : typeList) {
			String pid = BaseUtil.getStringValueFromMap(typeMap, "parentId");
			if (StringUtils.isEmpty(pid)) {
				level1Ids.add(BaseUtil.getStringValueFromMap(typeMap, "id"));
				level1Array.add(typeMap);
			}
		}
		mv.addObject("level1Array", level1Array);
		if (StringUtils.isNotEmpty(parentTypeId)) {
			List<Map<String, Object>> childTypes = indexService.getChildType(parentTypeId);
			mv.addObject("level2Array", childTypes);
		} else {
			mv.addObject("level2Array", indexService.getSecondLevelType(super.getCurrentHostOrgId(request), TypeCategory.COURSE));
		}
		Map<String, Object> condition = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(typeId)) {
			condition.put("typeId", typeId);
		} else {
			condition.put("typeId", parentTypeId);
		}
		if (StringUtils.isNotEmpty(typeId)) {
			mv.addObject("curTypeId", typeId);
		}
		if (StringUtils.isNotEmpty(parentTypeId)) {
			mv.addObject("curParentTypeId", parentTypeId);
		}
		pageNum = pageNum != null ? pageNum : Constants.DEFAULT_PAGE_NUMBER;
		pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
		//获取主办单位ID
		condition.put("hostOrgId", getCurrentHostOrgId(request));
		condition.put("type", "TK");
		Page<Map<String, Object>> pageInfo = indexService.getCourse(condition, pageNum, pageSize);
		mv.addObject("courseList", pageInfo.getRecords());
		mv.addObject("pageSize", pageSize);
		mv.addObject("pageNum", pageNum);
		mv.addObject("count", pageInfo.getTotal());
		mv.setViewName("pages/portal/original/questionDbIndex");
		return mv;
	}

	/**
	 * 成功案例
	 */
	@RequestMapping("/portal/case")
	public Object caseIndex(HttpServletRequest request, ModelAndView mv) throws Exception {
		mv.setViewName("pages/portal/original/case");
		return mv;
	}

	/**
	 * 关于我们
	 */
	@RequestMapping("/portal/aboutus")
	public Object aboutus(HttpServletRequest request, ModelAndView mv) throws Exception {
		mv.setViewName("pages/portal/original/aboutus");
		return mv;
	}

	/**
	 * 以下内容是非首页的内容
	 */

	/**
	 * 培训详情
	 */
	@RequestMapping("/portal/trainingDetail")
	public Object trainingDetail(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		ZypxXm xm = xmService.selectById(id);
		List<Map<String, Object>> courseSettingList = indexService.getXmCourseSetting(xm.getId());
		mv.addObject("pxxm", xm);
		mv.addObject("bmCount", xmService.getBmCountByXmId(id));
		mv.addObject("courseSettingList", courseSettingList);
		// 获取相关课程的课件学习资料
		List<Map<String, Object>> materialList = indexService.getCoursewareMaterialByXmId(xm.getId());
		for (Map<String, Object> map : materialList) {
			String fileSize = BaseUtil.getStringValueFromMap(map, "fileSize");
			fileSize = BaseUtil.formatFileSize(Long.valueOf(fileSize));
			map.put("file_size", fileSize);
		}
		mv.addObject("materialList", materialList);
		// 获取一个讲师
		Map<String, Object> teacherCondition = new HashMap<String, Object>();
		teacherCondition.put("hostOrgId", super.getCurrentHostOrgId(request));
		teacherCondition.put("id",null);
		Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(teacherCondition, 1, 1);
		List<Map<String, Object>> teacherList = pageInfo.getRecords();
		mv.addObject("teacherList", teacherList);
		mv.setViewName("pages/portal/original/trainingDetail");
		return mv;
	}

	/**
	 * 师资详情
	 */
	@RequestMapping("/portal/teacherDetail")
	public Object teacherDetail(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		Map<String, Object> teacherCondition = new HashMap<String, Object>();
		teacherCondition.put("hostOrgId", super.getCurrentHostOrgId(request));
		teacherCondition.put("id",id);
		Page<Map<String, Object>> pageInfo = indexService.getPortalTeacher(teacherCondition, 1, 1);
		List<Map<String, Object>> teacherList = pageInfo.getRecords();
		mv.addObject("teacher", teacherList.get(0));
		mv.setViewName("pages/portal/original/teacherDetail");
		return mv;
	}

	/**
	 * 新闻详情
	 */
	@RequestMapping("/portal/newsDetail")
	public Object newsDetail(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		// 取出职业培训所有的新闻类型
		List<Map<String, Object>> categorys = indexService.getNoticeCategory(getCurrentHostOrgId(request));
		mv.addObject("categorys", categorys);
		Notice notice = noticeService.selectById(id);
		NoticeCategory noticeCategory = indexService.getNoticeCategoryById(notice.getCategoryId());
		mv.addObject("activeCategory", noticeCategory);
		mv.addObject("notice", notice);
		Map<String, Object> condition = new HashMap<String, Object>();
		condition.put("categoryId", noticeCategory.getId());
		Page newsPages = indexService.getNotice(condition, 1, Integer.MAX_VALUE);
		//获取上一条新闻的ID、下一条新闻的ID
		List<Map<String, Object>> allNews = newsPages.getRecords();
		int index = 0 ;
		for (int i=0;i<allNews.size();i++) {
			Map map = allNews.get(i);
			if (id.equals(BaseUtil.getStringValueFromMap(map, "id"))) {
				index = i;
				break;
			}
		}
		if (index > 0) {
			Map<String, Object> lastNews = allNews.get(index - 1);
			mv.addObject("lastNewsId", BaseUtil.getStringValueFromMap(lastNews, "id"));
		}
		if (index < allNews.size() - 1) {
			Map<String, Object> nextNews = allNews.get(index + 1);
			mv.addObject("nextNewsId", BaseUtil.getStringValueFromMap(nextNews, "id"));
		}
		mv.setViewName("pages/portal/original/newsDetail");
		return mv;
	}

	/**
	 * 课件播放
	 */
	@RequestMapping("/portal/coursePlay")
	public Object coursePlay(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		Course course = courseService.selectById(id);
		Integer studyCount= indexService.getZbStudyCount(id);
		Integer bmCourseCount=indexService.getBmCourseCountByCourseId(id);
		EntityWrapper<Courseware> wrapper = new EntityWrapper<Courseware>();
		wrapper.eq("course_id", course.getId());
		List<Courseware> coursewareList = coursewareService.selectList(wrapper);
		if (CollectionUtils.isEmpty(coursewareList)) {
			mv.addObject("courseLearn", "");
			mv.addObject("xxzlList", new JSONArray());
			mv.addObject("kj", new Courseware());
			mv.addObject("course", new Course());
			mv.setViewName("pages/portal/original/coursePlay");
			return mv;
		}
		Courseware courseware = coursewareList.get(0);
		Courselearn course_learn = ModelHelper.convertObject(courseware.getContent());
		mv.addObject("courseLearn", course_learn);
		// 查询课件学习资料
		EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper<CoursewareMaterial>();
		materialWrapper.orderBy("create_time", false);
		List<CoursewareMaterial> materialList = coursewareMaterialService.selectList(materialWrapper);
		for (CoursewareMaterial coursewareMaterial : materialList) {
			coursewareMaterial.setDisplaySize(BaseUtil.formatFileSize(coursewareMaterial.getFileSize()));
		}
		mv.addObject("studyCount",studyCount);
		mv.addObject("bmCourseCount",bmCourseCount);
		mv.addObject("xxzlList", materialList);
		mv.addObject("kj", courseware);
		mv.addObject("course", course);
		mv.setViewName("pages/portal/original/coursePlay");
		return mv;
	}

	/**
	 * 题库详情
	 */
	@RequestMapping("/portal/coursePlayQuestionDb")
	public Object coursePlayQuestionDb(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		LoginStudent loginStudent = super.getLoginStudent(request);
		Course course = courseService.selectById(id);
		// 根据课程获取题库列表
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (QuestionDBEntity questionDBEntity : questionDBService.getQuestionDBEntity(course.getId())) {
			Map<String, Object> map = new HashMap<String, Object>();
			String questionDbId = questionDBEntity.getId();
			map.put("id", questionDbId);
			map.put("name", questionDBEntity.getName());
			if (loginStudent != null) {
				String studentId = loginStudent.getUser().getId();
				// 我的练习
				EntityWrapper<MyPracticeRecord> myPraceticeRecordWrapper = new EntityWrapper<MyPracticeRecord>();
				myPraceticeRecordWrapper.eq("student_id", studentId);
				myPraceticeRecordWrapper.eq("db_id", questionDbId);
				List<MyPracticeRecord> myPaperList = portalPracticeRecordService.selectList(myPraceticeRecordWrapper);
				if (myPaperList.size() > 0) {
					String paperId = myPaperList.get(0).getPaperId();
					// 考试记录
					EntityWrapper<ExamData> examDataWrapper = new EntityWrapper<ExamData>();
					examDataWrapper.eq("student_id", studentId);
					examDataWrapper.eq("paper_id", paperId);
					List<ExamData> examDatas = examDataService.selectList(examDataWrapper);
					if (examDatas.size() > 0) {
						ExamDataStatus examDataStatus = examDatas.get(0).getStatus();
						map.put("examDataStatus", examDataStatus.name());
					}
				}
			}
			list.add(map);
		}
		mv.addObject("course", course);
		mv.addObject("list", list);
		mv.setViewName("pages/portal/original/coursePlayQuestionDb");
		return mv;
	}

	/**
	 * 考生注册功能
	 */
	@RequestMapping("/portal/register")
	public ModelAndView register(HttpServletRequest request, ModelAndView mv) {
		mv.setViewName("pages/portal/original/register");
		return mv;
	}

	/**
	 * 展示试题
	 */
	@Transactional
	@RequestMapping("/portal/doQuestionDBPaper")
	public Object subjectPlay(HttpServletRequest request, ModelAndView mv, String id) throws Exception {
		String studentId = getLoginStudentId(request);
		QuestionDBEntity questionDBEntity = questionDBService.selectById(id);
		QuestionDB2CourseEntity question2CourseEntity = question2CourseService.getDetailsByDbId(id);
		Course course = courseService.selectById(question2CourseEntity.getCourseId());
		Paper paper = indexService.makePaperByQuestionDB(id, studentId);
		EntityWrapper<ExamData> wrapper = new EntityWrapper();
		wrapper.eq("student_id", studentId);
		wrapper.eq("paper_id", paper.getId());
		List<ExamData> examDataList = examDataService.selectList(wrapper);
		if (examDataList.size() > 0) {
			mv.addObject("examDataStatus", examDataList.get(0).getStatus());
		}
		mv.addObject("db", questionDBEntity);
		mv.addObject("course", course);
		mv.addObject("paper", paper);
		mv.setViewName("pages/portal/original/doQuestionDBPaper");
		return mv;
	}

	/**
	 * 交卷
	 */
	@RequestMapping("/portal/submitPaper")
	@ResponseBody
	public Object submitPaper(HttpServletRequest request, String paperId) throws Exception {
		EntityWrapper<ExamData> wrapper = new EntityWrapper<ExamData>();
		String studentId = super.getLoginStudentId(request);
		wrapper.eq("paper_id",  paperId);
		wrapper.eq("student_id", studentId);
		wrapper.orderBy("save_time", false);
		List<ExamData> examDataList = examDataService.selectList(wrapper);
		ExamData examData = examDataList.size() > 0 ? examDataList.get(0) : null;
		if (BaseUtil.isNotEmpty(examData)) {
			if (ExamDataStatus.WJJ != examData.getStatus()) {
				throw BizException.withMessage("用户已提交过试卷，不允许重复提交!");
			}
			examData = examDataList.get(0);
		}
		else {
			examData = new ExamData();
		}
		// 为保障提交时时最新答案，也存一次答案
		Map<String, Object> examDataMap = new HashMap<>();
		Map<String, String[]> mapx = request.getParameterMap();
		for (Map.Entry<String, String[]> entry : mapx.entrySet()) {
			if (entry.getKey().startsWith("Q-")) {
				examDataMap.put(entry.getKey(), StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
			}
		}

		JSONObject userDataJson = JSONObject.fromObject(examData);
		String studentAnswer = userDataJson == null ? "" : userDataJson.toString();
		examData.setData(studentAnswer);
		examData.setScore(0);
		examData.setEndTime(new Date());
		examData.setStatus(ExamDataStatus.YJJDPG);
		examData.setClient(Jjkhd.PC);

		boolean boo = true;
		if (BaseUtil.isEmpty(examData.getId())) {
			examData.setId(BaseUtil.generateId());
			examData.setPaperId(paperId);
			examData.setStudentId(studentId);
			examData.setStartTime(new Date());
			examData.setSaveTime(new Date());
			examDataService.insert(examData);
		} else {
			examDataService.updateById(examData);
		}
		return true;
	}

	/**
	 * 查询课件学习资料
	 */
	@RequestMapping("/portal/CoursewareMaterial")
	@ResponseBody
	public Object getCoursewareMaterial(String kjId,String chapterId,String lessonId){
		EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper<CoursewareMaterial>();
		materialWrapper.eq("kj_id", kjId);
		materialWrapper.eq("chapter_id", chapterId);
		materialWrapper.eq("lesson_id",lessonId);
		materialWrapper.orderBy("create_time", false);
		List<CoursewareMaterial> materialList = coursewareMaterialService.selectList(materialWrapper);
		for (CoursewareMaterial coursewareMaterial : materialList) {
			coursewareMaterial.setDisplaySize(BaseUtil.formatFileSize(coursewareMaterial.getFileSize()));
		}
		return materialList;
	}



	/** 添加新闻咨询
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/portal/addAdvice")
	@ResponseBody
	public Object addAdvice(HttpServletRequest request,
							HttpServletResponse response,
							   @RequestParam(required = false) String name,
							   @RequestParam(required = false) String mobile,
							   @RequestParam(required = false) String xmId,
							   @RequestParam(required = false) String content) {
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("电话号码不能为空");
		}
		if (StringUtils.isEmpty(content)) {
			throw BizException.withMessage("咨询内容不能为空");
		}
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		String studentId = super.getLoginStudentId(request);
		pxxmXmBmAdviceService.addAdvice(name,mobile,content,studentId,xmId);
		return true;
	}
}
