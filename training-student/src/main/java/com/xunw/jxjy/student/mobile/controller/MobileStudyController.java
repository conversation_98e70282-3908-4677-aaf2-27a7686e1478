package com.xunw.jxjy.student.mobile.controller;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber;
import com.xunw.jxjy.model.zypx.service.ZypxXmBmCardNumberService;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.inf.entity.Form;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.mobile.service.ZypxMobileBmService;
import com.xunw.jxjy.model.mobile.service.ZypxMobileXmService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmMaterial;
import com.xunw.jxjy.model.zypx.service.PxxmXmBmAdviceService;
import com.xunw.jxjy.model.zypx.service.ZypxQaService;
import com.xunw.jxjy.model.zypx.service.ZypxXmMaterialService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

import net.sf.json.JSONObject;

/**
 * 职业培训---学习板块
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/study")
public class MobileStudyController extends BaseController {

	@Autowired
	private ZypxMobileXmService service;
	@Autowired
	private ZypxQaService qaService;
	@Autowired
	private ZypxTypeService typeService;
	@Autowired
	private ZypxMobileBmService mobileBmService;
	@Autowired
	private FormService formService;
	@Autowired
	private ZypxXmBmCardNumberService zypxXmBmCardNumberService;

	/**
	 * 此接口 在项目扫描二维码会用到，所以无需登录
	 */
	@RequestMapping("/getById")
	@Operation(desc = "项目详情查询")
	public Object detail(HttpServletRequest request, @RequestParam(required = false) String id) throws ParseException {
		ZypxXm zypxXm = service.selectById(id);
		if (StringUtils.isNotEmpty(zypxXm.getTypeId())) {
			ZypxType zypxType = typeService.selectById(zypxXm.getTypeId());
			zypxXm.setZypxType(zypxType);
		}
		String formId = formService.getFormByXmId(zypxXm.getId());
		if (StringUtils.isNotEmpty(formId)) {
			Form form = formService.selectById(formId);
			zypxXm.setForm(form);
		}
		String qaId = qaService.getQaByXmId(id);
		zypxXm.setQaId(qaId);
		String studentId = getLoginStudentId(request);
		zypxXm.setStudentId(studentId);
		if (StringUtils.isNotEmpty(studentId)) {
			ZypxBm zypxBm = mobileBmService.getBmInfo(id, studentId);
			if (zypxBm != null) {
				zypxXm.setBmId(zypxBm.getId());
			}
		}
		return zypxXm;
	}

	/**
	 * 去第三方平台学习（神奇的考点母题）
	 * <a href="https://apifox.com/apidoc/shared-01dfd768-d0ee-467b-a25f-fd58fefd58b2/api-18376811">...</a>
	 */
	@RequestMapping("/toOpenStudy")
	@Operation(desc = "去第三方平台学习（神奇的考点母题）")
	public Object toOpenStudy(HttpServletRequest request, @RequestParam(required = false) String bmId) throws IOException {
		LoginStudent loginStudent = super.getLoginStudent(request);
		//查是否绑定学习卡
		List<ZypxXmBmCardNumber> bmCardNumbers = zypxXmBmCardNumberService.findByBmId(bmId);
		if (CollectionUtils.isEmpty(bmCardNumbers)) {
			throw BizException.withMessage("暂未绑定学习卡，请联系管理员处理");
		}
		//自动登录-获取第三方平台跳转地址
		OkHttpClient client = new OkHttpClient().newBuilder()
				.build();
		MediaType mediaType = MediaType.parse("application/json");
		Map<String, String> map = new HashMap<>();
		map.put("SecretKey", Constants.secretKey);
		map.put("NickName", loginStudent.getInfo().getName());
		map.put("MobileTel", loginStudent.getInfo().getMobile());
		map.put("UserId", loginStudent.getUser().getId());
		okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSON.toJSONString(map));
		Request r = new Request.Builder()
				.url("https://core.shenqimuti.cn/school/AutoLogin")
				.method("POST", body)
				.addHeader("Content-Type", "application/json")
				.build();
		Response execute = client.newCall(r).execute();
		//获取响应数据
		String resp = execute.body().string();
		return JSON.parseObject(resp).getJSONObject("Data").getString("ToMobileUrl");
	}

	/**
	 * 去第三方平台学习（神奇的考点母题）
	 * <a href="https://apifox.com/apidoc/shared-01dfd768-d0ee-467b-a25f-fd58fefd58b2/api-18376811">...</a>
	 */
	@RequestMapping("/mini/toOpenStudy")
	@Operation(desc = "去第三方平台学习（神奇的考点母题）小程序内嵌网页")
	public Object miniToOpenStudy(HttpServletRequest request, @RequestParam(required = false) String bmId) throws IOException {
		LoginStudent loginStudent = super.getLoginStudent(request);
		//查是否绑定学习卡
		List<ZypxXmBmCardNumber> bmCardNumbers = zypxXmBmCardNumberService.findByBmId(bmId);
		if (CollectionUtils.isEmpty(bmCardNumbers)) {
			throw BizException.withMessage("暂未绑定学习卡，请联系管理员处理");
		}
		//自动登录-获取第三方平台跳转地址
		OkHttpClient client = new OkHttpClient().newBuilder()
				.build();
		MediaType mediaType = MediaType.parse("application/json");
		Map<String, String> map = new HashMap<>();
		map.put("SecretKey", Constants.secretKey);
		map.put("NickName", loginStudent.getInfo().getName());
		map.put("MobileTel", loginStudent.getInfo().getMobile());
		map.put("UserId", loginStudent.getUser().getId());
		okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSON.toJSONString(map));
		Request r = new Request.Builder()
				.url("https://core.shenqimuti.cn//school/EmbedWeb")
				.method("POST", body)
				.addHeader("Content-Type", "application/json")
				.build();
		Response execute = client.newCall(r).execute();
		//获取响应数据
		String resp = execute.body().string();
		return JSON.parseObject(resp).getJSONObject("Data").getString("ToMobileUrl");
	}
}