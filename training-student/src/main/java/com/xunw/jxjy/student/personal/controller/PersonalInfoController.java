package com.xunw.jxjy.student.personal.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;

import com.xunw.jxjy.common.utils.JWTUtils;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.student.core.annotation.Operation;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;
import org.springframework.web.util.HtmlUtils;


/**
 * 个人中心-个人信息维护
 * <AUTHOR>
 */
@Controller
@RequestMapping("/personal/")
public class PersonalInfoController extends BaseController {
	
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
    private OrgService orgService;
	@Autowired
	private DictService dictService;
	
	/**
	 * 修改我的信息
	 */
	@RequestMapping("/infoUpdate")
	@Operation(desc = "修改学员个人信息")
	public Object infoUpdate(HttpServletRequest request, ModelAndView mv) {
		 //查询考生信息
		LoginStudent loginUser = getLoginStudent(request);
        StudentUser studentUser = studentUserService.selectById(loginUser.getUser().getId());
        StudentInfo studentInfo = studentInfoService.selectById(loginUser.getInfo().getId());
		//获取省
		List<Dict> provinceList=dictService.getProvinceByDictCode();
		mv.addObject("provinceList",provinceList);
        mv.addObject("studentUser", studentUser);
        mv.addObject("studentInfo", studentInfo);
		mv.setViewName("pages/personal/pages/personalInfoUpdate");
		return mv;
	}
	
	/**
	 * 修改自己的个人信息
	 */
	@RequestMapping("/infoUpdateSubmit")
	@ResponseBody
	@Operation(desc = "提交修改学员个人信息")
	public Object infoUpdateSubmit(HttpServletRequest request, 
			@RequestParam(required = false) String id,
		    @RequestParam(required = false) String name,
		    @RequestParam(required = false) String sfzh,
			@RequestParam(required = false) String mobile,
			@RequestParam(required = false) Gender gender,
			@RequestParam(required = false) Education education,
			@RequestParam(required = false) String address,
			@RequestParam(required = false) String studentPhoto,
			@RequestParam(required = false) StudentType studentType,
			@RequestParam(required = false) String graduateSchool,
			@RequestParam(required = false) String college,
			@RequestParam(required = false) String classz,
			@RequestParam(required = false) String specialty,
			@RequestParam(required = false) String zw,
			@RequestParam(required = false) String zc,
            @RequestParam(required = false) String company,
			@RequestParam(required = false) String provinceCode,
			@RequestParam(required = false) String cityCode,
			@RequestParam(required = false) String districtCode) {
		if (request.getSession().getAttribute("csrf") == null) {
			throw BizException.withMessage("请求异常，请重新登录后再试");
		}
		if(StringUtils.isEmpty(name)){
			throw BizException.withMessage("请输入姓名");
		}
		if(StringUtils.isEmpty(sfzh)){
			throw BizException.withMessage("请输入身份证号");
		}
		if(!BaseUtil.isIDCardNumber(sfzh)){
			throw BizException.withMessage("请输入正确的身份证号");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		if (!BaseUtil.isMobile(mobile)) {
			throw BizException.withMessage("请输入正确的11位手机号");
		}
		if (gender == null) {
			throw BizException.withMessage("请选择性别");
		}
		if (studentType == null) {
			throw BizException.withMessage("请选择学员类型");
		}
		
		if (studentType == StudentType.SCHOOL) {
			if (studentType == StudentType.SCHOOL && StringUtils.isEmpty(graduateSchool)) {
				throw BizException.withMessage("在校学生必须填写自己所在的学校");
			}
			if (studentType == StudentType.SCHOOL && StringUtils.isEmpty(college)) {
				throw BizException.withMessage("在校学生必须填写自己所在的院系");
			}
			if (studentType == StudentType.SCHOOL && StringUtils.isEmpty(specialty)) {
				throw BizException.withMessage("在校学生必须填写自己的专业");
			}
			if (studentType == StudentType.SCHOOL && StringUtils.isEmpty(classz)) {
				throw BizException.withMessage("在校学生必须填写自己所在的班级");
			}
		}
		
		if (studentType == StudentType.SOCIAL) {
			if (StringUtils.isEmpty(company)) {
				throw BizException.withMessage("请输入您所在的单位");
			}
			if (StringUtils.isEmpty(zw)) {
				throw BizException.withMessage("请输入您的职务");
			}
			if (StringUtils.isEmpty(zc)) {
				throw BizException.withMessage("请输入您的职称");
			}
		}
		StudentInfo studentInfo = studentInfoService.selectById(getLoginStudent(request).getInfo().getId());
		StudentUser studentUser = studentUserService.selectById(getLoginStudentId(request));
		
		//数据合法性校验
		if (studentType == StudentType.SCHOOL) {
			company = null;
			zw = null;
			zc = null;
			studentInfo.setIsGraduated(Constants.NO);
		}
		else {
			college = null;
			specialty = null;
			classz = null;
			studentInfo.setIsGraduated(Constants.YES);
		}
		if (StringUtils.isNotEmpty(sfzh)) {
			StudentUser check = studentUserService.findBySfzh(sfzh, super.getCurrentHostOrgId(request));
			if (check!= null && !check.getId().equals(studentUser.getId())) {
				throw BizException.withMessage("身份证号码已经被其他学员使用，如您确定是本人的身份证号，请使用该身份证号作为账号并重新登录，然后再修改个人信息");
			}
		}
		if (StringUtils.isNotEmpty(mobile)) {
			StudentUser check = studentUserService.findByMobile(mobile, super.getCurrentHostOrgId(request));
			if (check!= null && !check.getId().equals(studentUser.getId())) {
				throw BizException.withMessage("手机号码已经被其他学员使用，如您确定是本人的手机号，请使用该手机号作为账号并重新登录，然后再修改个人信息");
			}
		}
		
		//修改学员用户信息
		studentUser.setStudentType(studentType);
		studentUser.setCompany(this.htmlEscape(company));
		studentUserService.updateAllColumnById(studentUser);
		
		studentInfo.setSfzh(sfzh);
		studentInfo.setName(this.htmlEscape(name));
		studentInfo.setGender(gender);
		studentInfo.setGraduateSchool(this.htmlEscape(graduateSchool));
		studentInfo.setCollege(this.htmlEscape(college));
		studentInfo.setSpecialty(this.htmlEscape(specialty));
		studentInfo.setClassz(this.htmlEscape(classz));
		studentInfo.setEducation(education);
		studentInfo.setAddress(this.htmlEscape(address));
		studentInfo.setMobile(mobile);
		studentInfo.setStudentPhoto(studentPhoto);
		studentInfo.setZc(this.htmlEscape(zc));
		studentInfo.setZw(this.htmlEscape(zw));
		studentInfo.setCompanyProvinceCode(provinceCode);
		studentInfo.setCompanyCityCode(cityCode);
		studentInfo.setCompanyDistrictCode(districtCode);
		studentInfoService.updateAllColumnById(studentInfo);

		//更新会话中的用户信息
		LoginStudent loginStudent = new LoginStudent();
		StudentInfo latestStudentInfo = studentInfoService.getByStudentId(getLoginStudentId(request));
		StudentUser latestStudentUser = studentUserService.selectById(getLoginStudentId(request));
		loginStudent.setUser(latestStudentUser);
		loginStudent.setInfo(latestStudentInfo);
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		
		return true;
	}

	/**
	 * @Description   获取子机构
	 * <AUTHOR>
	 * @Time  2021/5/20 17:37
	 */
	@RequestMapping("/getOrg")
    @ResponseBody
    @Operation(desc = "获取子机构")
    public Object getOrg(HttpServletRequest request,@RequestParam(required = false) String id) {
        List<Org> children = orgService.getChildren(id);
        return children;
    }

	/**
	 *获取市区
	 */
	@RequestMapping("/selecCityAndDistrict")
	@ResponseBody
	@Operation(desc = "获取市和区")
	public Object selectRegionCodeAndName(HttpServletRequest request,@RequestParam(required = false) String id){
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("上级地区不能为空");
		}
		List<Dict> list = dictService.getChildrenByParent(id);
		List<Map<String,Object>> relist=new ArrayList<>();
		for (Dict dict:list){
			Map<String,Object> map=new HashMap<>();
			if(dict!=null){
				map.put("dictCode",dict.getDictValue());
				map.put("name",dict.getDictName());
				relist.add(map);
			}
		}
		return  relist;
	}

	/**
	 * 修改密码
	 */
	@RequestMapping("/updatePassword")
	@ResponseBody
	@Operation(desc = "修改密码")
	public Object updatePassword(HttpServletRequest request,@RequestParam String newPassword){
		if (StringUtils.isEmpty(newPassword)){
			throw BizException.withMessage("新密码不能为空");
		}
		String studentId = super.getLoginStudentId(request);
		StudentUser studentUser = studentUserService.selectById(studentId);
		if (studentUser == null){
			throw BizException.withMessage("学员用户不存在");
		}
		if (BaseUtil.isRawPasswordForStudent(newPassword)) {//弱密码检测
			throw BizException.withMessage("新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合");
		}
		studentUser.setPassword(DigestUtils.md5Hex(newPassword));
		studentUserService.updateById(studentUser);
		return true;
	}

	private String htmlEscape(String param) {
		if (param != null) {
			param = HtmlUtils.htmlEscape(param);
		}
		return param;
	}
}
