package com.xunw.jxjy.student.mobile.controller;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/addressList")
public class MobileAddressListController extends BaseController {

    @Autowired
    private ZypxBmService zypxBmService;

    @Autowired
    private ZypxXmCourseSettingService zypxXmCourseSettingService;

    @RequestMapping("/list")
    @Operation(desc = "通讯录")
    public Object list(@RequestParam(required = false) String id,
                       @RequestParam(required = false) Integer type,
                       @RequestParam(required = false) String keyword) {
        if (BaseUtil.isEmpty(id)) {
            throw BizException.withMessage("项目id不能为空");
        }
        if (type == null) {
            throw BizException.withMessage("类型不能为空");
        }
        Object result = null;
        switch (type) {
            case 1:
                result = zypxBmService.getClassLeaderInfoByXmId(id, keyword);
                break;
            case 2:
                result = zypxXmCourseSettingService.getXmLinkmanByXmId(id, keyword);
                break;
            case 3:
                result = zypxBmService.getAllStudentByXmId(id, keyword);
                break;
        }
        return result;
    }
}
