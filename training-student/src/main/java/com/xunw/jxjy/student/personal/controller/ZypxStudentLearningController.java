package com.xunw.jxjy.student.personal.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.qiniu.pili.PiliException;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.QiniuZhiboUtils;
import com.xunw.jxjy.model.common.Chapter;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.Lesson;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.enums.LearningType;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Zbzt;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.inf.params.CoursewareCommentQueryParams;
import com.xunw.jxjy.model.inf.params.LiveCommentQueryParams;
import com.xunw.jxjy.model.inf.service.*;
import com.xunw.jxjy.model.learning.entity.CoursewareLearningProgress;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.personal.params.StudentPaperQueryParams;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.personal.service.ZypxStudentLearningService;
import com.xunw.jxjy.model.personal.service.ZypxStudentPaperService;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.tk.service.QuestionEntityService;
import com.xunw.jxjy.model.utils.FaceVerifyUtil;
import com.xunw.jxjy.model.zypx.entity.TeacherTargetResult;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.service.*;
import com.xunw.jxjy.paper.utils.ModelHelper;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 * 个人中心-我的学习
 */
@Controller
@RequestMapping("/personal/")
public class ZypxStudentLearningController extends BaseController {

    @Autowired
    private ZypxStudentBmService studentBmService;
    @Autowired
    private ZypxStudentLearningService service;
    @Autowired
    private ZypxXmService xmService;
    @Autowired
    private ZypxTypeService typeService;
    @Autowired
    private CoursewareService coursewareService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private ZypxCoursewareLearningProgressService coursewareLearningProgressService;
    @Autowired
    private ZypxXmCourseSettingService xmCourseSettingService;
    @Autowired
    private UserService userService;
    @Autowired
    private CoursewareMaterialService coursewareMaterialService;
    @Autowired
    private CoursewareCommentService coursewareCommentService;
    @Autowired
    private CoursewareNoteService coursewareNoteService;
    @Autowired
    private ZypxLiveService liveService;
    @Autowired
    private LiveNoteService liveNoteService;
    @Autowired
    private TeacherTargetResultService teacherTargetResultService;
    @Autowired
    private CoursewareTargetResultService coursewareTargetResultService;
    @Autowired
    private LiveCommentService liveCommentService;
    @Autowired
    private ZypxStudentPaperService studentPaperService;
    @Autowired
    private CourseLiveService courseLiveService;
    @Autowired
    private CoursewarePopQuesService coursewarePopQuesService;
    @Autowired
    private QuestionEntityService questionEntityService;
    @Autowired
    private ZypxXmBmCardNumberService zypxXmBmCardNumberService;

    /**
     * 我的学习
     */
    @RequestMapping("/wdxx")
    @Operation(desc = "我的学习")
    public Object wdbm(HttpServletRequest request, ModelAndView mv) {
        LoginStudent loginStudent = super.getLoginStudent(request);
        String studentId = loginStudent.getUser().getId();
        List<Map<String, Object>> xmList = studentBmService.getStudentBMCGXmList(studentId);
        mv.addObject("xmList", xmList);
        mv.addObject("xmListCount", xmList.size());
        mv.setViewName("pages/personal/pages/wdxx");
        return mv;
    }

    /**
     * 去第三方平台学习（神奇的考点母题）
     * <a href="https://apifox.com/apidoc/shared-01dfd768-d0ee-467b-a25f-fd58fefd58b2/api-18376811">...</a>
     */
    @RequestMapping("/toOpenStudy")
    @Operation(desc = "去第三方平台学习（神奇的考点母题）")
    @ResponseBody
    public Object toOpenStudy(HttpServletRequest request, @RequestParam(required = false) String bmId) throws IOException {
        LoginStudent loginStudent = super.getLoginStudent(request);
        //查是否绑定学习卡
        List<ZypxXmBmCardNumber> bmCardNumbers = zypxXmBmCardNumberService.findByBmId(bmId);
        if (CollectionUtils.isEmpty(bmCardNumbers)) {
            throw BizException.withMessage("暂未绑定学习卡，请联系管理员处理");
        }
        //自动登录-获取第三方平台跳转地址
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        Map<String, String> map = new HashMap<>();
        map.put("SecretKey", Constants.secretKey);
        map.put("NickName", loginStudent.getInfo().getName());
        map.put("MobileTel", loginStudent.getInfo().getMobile());
        map.put("UserId", loginStudent.getUser().getId());
        RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(map));
        Request r = new Request.Builder()
                .url("https://core.shenqimuti.cn/school/AutoLogin")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response execute = client.newCall(r).execute();
        //获取响应数据
        String resp = execute.body().string();
        return JSON.parseObject(resp).getJSONObject("Data").getString("ToPcUrl");
    }

    /**
     * 进入学习的页面
     */
    @RequestMapping("/wdxxDetails")
    @Operation(desc = "进入学习的页面")
    public Object details(HttpServletRequest request, ModelAndView mv, String xmId) {
        LoginStudent loginStudent = super.getLoginStudent(request);
        String studentId = loginStudent.getUser().getId();
        ZypxXm zypxXm = xmService.selectById(xmId);
        if (zypxXm.getTypeId() != null) {
            zypxXm.setZypxType(typeService.selectById(zypxXm.getTypeId()));
        }
        mv.addObject("xm", zypxXm);
        //查询学员的所有已报名课程
        List<Map<String, Object>> bmCourseList = studentBmService.getStudentBmCourseList(xmId, studentId);
        for (Map<String, Object> map : bmCourseList) {

    		if (map.get("isCourseware") != null && Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "isCourseware"))) {
            	String kjContent = BaseUtil.getStringValueFromMap(map, "kjContent");
				Courselearn courselearn = ModelHelper.convertObject(kjContent);
				 if (courselearn == null) {
                     continue;
                 }
                 List<Chapter> chapters = courselearn.getChapters();
                 if (BaseUtil.isNotEmpty(chapters)) {
                     int chapterIndex = 0;
                     for (Chapter chapter : chapters) {
                         chapter.setName("第" + BaseUtil.toCNLowerNum(chapterIndex + 1) + "章&nbsp;&nbsp;" + chapter.getName());
                         for (Lesson lesson : chapter.getLessons()) {
                        	 EntityWrapper<CoursewareLearningProgress> prgressWrapper = new EntityWrapper<>();
                             prgressWrapper.eq("xm_id", xmId);
                             prgressWrapper.eq("kj_id", BaseUtil.getStringValueFromMap(map, "kjId"));
                             prgressWrapper.eq("student_id", studentId);
                             prgressWrapper.eq("chapter_id", chapter.getId());
                             prgressWrapper.eq("lesson_id", lesson.getId());
                             List<CoursewareLearningProgress> bizKjxxjds = coursewareLearningProgressService.selectList(prgressWrapper);
                             CoursewareLearningProgress progress = bizKjxxjds.size() > 0 ? bizKjxxjds.get(0) : null;
                             lesson.setCurPlayed(progress != null ? progress.getDuration() : 0);
                         }
                         chapterIndex++;
                     }
                 }
                 map.put("courselearn", courselearn);
                 map.put("course_progress", service.getLeanringCourseProgress(xmId, studentId,
                 		BaseUtil.getStringValueFromMap(map, "courseId"), LearningType.KJ));//学习进度
                
            } 
            if (map.get("isLive") != null && Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "isLive"))) {
                Courselive courselive = ModelHelper.convertObject(BaseUtil.getStringValueFromMap(map, "liveContent"));
                if (courselive == null) {
                    continue;
                }
                List<com.xunw.jxjy.model.common.courselive.Chapter> chapters = courselive.getChapters();
                if (CollectionUtils.isNotEmpty(chapters)) {
                    int chapterIndex = 0;
                    for (com.xunw.jxjy.model.common.courselive.Chapter chapter : chapters) {
                        //章节id
                        List<com.xunw.jxjy.model.common.courselive.Lesson> lessons = chapter.getLessons();
                        chapter.setName("第" + BaseUtil.toCNLowerNum(chapterIndex + 1) + "章&nbsp;&nbsp;" + chapter.getName());
                        for (com.xunw.jxjy.model.common.courselive.Lesson lesson : lessons) {
                            Live live = liveService.selectById(lesson.getId());
                            lesson.setZbzt(live.getZbzt());
                            User user = userService.selectById(live.getJsId());
                            lesson.setJsxm(user.getName());
                        }
                        chapterIndex++;
                    }
                }
                map.put("courselive", courselive);
                map.put("live_progress", service.getLeanringCourseProgress(xmId, studentId,
                		BaseUtil.getStringValueFromMap(map, "courseId"), LearningType.ZB));//学习进度
            } 
            if (map.get("isMs") != null && Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "isMs"))) {
                CourseMs coursems = ModelHelper.convertObject(BaseUtil.getStringValueFromMap(map, "msContent"));
                if (coursems == null) {
                    continue;
                }
                List<com.xunw.jxjy.model.common.coursems.Chapter> chapters = coursems.getChapters();
                if (CollectionUtils.isNotEmpty(chapters)) {
                    int chapterIndex = 0;
                    for (com.xunw.jxjy.model.common.coursems.Chapter chapter : chapters) {
                        //章节id
                        List<com.xunw.jxjy.model.common.coursems.Lesson> lessons = chapter.getLessons();
                        chapter.setName("第" + BaseUtil.toCNLowerNum(chapterIndex + 1) + "章&nbsp;&nbsp;" + chapter.getName());
                        for (com.xunw.jxjy.model.common.coursems.Lesson lesson : lessons) {
                        	if (StringUtils.isNotEmpty(lesson.getTeacherId())) {
    							User teacher = userService.selectById(lesson.getTeacherId());
    							lesson.setTeacherName(teacher != null ? teacher.getName() : null);
    						}
                        }
                        chapterIndex++;
                    }
                }
                map.put("coursems", coursems);
                map.put("ms_progress", service.getLeanringCourseProgress(xmId, studentId,
                		BaseUtil.getStringValueFromMap(map, "courseId"), LearningType.MS));//学习进度
            }
    	
            map.put("course_score", service.getLearningCourseScore(xmId, studentId, BaseUtil.getStringValueFromMap(map, "courseId")));
        }
       
        mv.addObject("bmCourseList", bmCourseList);
        List<Map<String, Object>> studentScoreRankList = service.getStudentLeanringScoreRankList(xmId);
        mv.addObject("rankList", studentScoreRankList);
        //计算我排名
        Integer myRank = 0;
        for (int i = 0; i < studentScoreRankList.size(); i++) {
            Map<String, Object> map = studentScoreRankList.get(i);
            if (studentId.equals(BaseUtil.getStringValueFromMap(map, "studentId"))) {
                myRank = i + 1;
                break;
            }
        }
        
        //获取项目成绩
        Map<String, Object> trainingScore = service.getTrainingScore(xmId, studentId);
        Double myLeanringScore = BaseUtil.getDoubleValueFromMap(trainingScore, "learningScore", 0d);
        Double pszyScore = BaseUtil.getDoubleValueFromMap(trainingScore, "pszyScore", 0d);
        Double zjkhScore = BaseUtil.getDoubleValueFromMap(trainingScore, "zjkhScore", 0d);
        
        //项目学时完成情况
        String totalHours = BaseUtil.getStringValueFromMap(trainingScore, "totalHours"); 
        String totalFinishedHours = BaseUtil.getStringValueFromMap(trainingScore, "totalFinishedHours"); 
        String hoursFinished = totalFinishedHours + "学时/" + totalHours + "学时";
        
        // 项目平时作业
        StudentPaperQueryParams studentPaperQueryParams = new StudentPaperQueryParams();
        studentPaperQueryParams.setStudentId(studentId);
        studentPaperQueryParams.setXmId(xmId);
        studentPaperQueryParams.setCategory(PaperCategory.PSZY);
        studentPaperQueryParams.setSize(Integer.MAX_VALUE);
        List<Map<String, Object>> homeworks = studentPaperService.getStudentPaperList(studentPaperQueryParams).getRecords();
        
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("category", PaperCategory.PSZY.getValue());
        paramMap.put("studentId", studentId);
        paramMap.put("xmId", xmId);
        mv.addObject("myRank", myRank);
        mv.addObject("progress", myLeanringScore);
        mv.addObject("myLeanringScore", myLeanringScore);
        mv.addObject("myPszyScore", pszyScore);
        mv.addObject("myZjkhScore", zjkhScore);
        mv.addObject("homeworks", homeworks);
        mv.addObject("hoursFinished", hoursFinished);//学时完成情况
        Double currentTotalScore = 0.00;
		currentTotalScore += myLeanringScore * (zypxXm.getLearningScoreZb() / 100);
		currentTotalScore += pszyScore * (zypxXm.getPracticeScoreZb() / 100);
		currentTotalScore += zjkhScore * (zypxXm.getFinalExamScoreZb() / 100);
        mv.addObject("myTotalScore", currentTotalScore);
        mv.setViewName("pages/personal/pages/wdxxDetails");
        return mv;
    }

    /**
     * 课件学习
     */
    @RequestMapping("/courseStudy")
    @Operation(desc = "课程学习")
	public Object courseStudy(HttpServletRequest request, ModelAndView mv, String xmId, String coursewareId,
			String lessonId, String courseSettingId, String photoPath) throws Exception {
		LoginStudent loginStudent = super.getLoginStudent(request);
		String studentId = loginStudent.getUser().getId();
		Courseware courseware = coursewareService.selectById(coursewareId);
		String content = courseware.getContent();
		// 获取每一个课时的总时长，观看时长
		Courselearn courselearn = ModelHelper.convertObject(content);
		List<Chapter> chapters = courselearn.getChapters();
		Chapter activeChapter = null;
		Lesson activeLesson = null;
		if (BaseUtil.isNotEmpty(chapters)) {
			int chapterIndex = 0;
			for (Chapter chapter : chapters) {
				// 章节id
				String zjId = chapter.getId();
				List<Lesson> lessons = chapter.getLessons();
				chapter.setName("第" + BaseUtil.toCNLowerNum(chapterIndex + 1) + "章&nbsp;&nbsp;" + chapter.getName());
				for (Lesson lesson : lessons) {
					// 课时id
					String leId = lesson.getId();
					if (leId.equals(lessonId)) {
						activeLesson = lesson;
						activeLesson.setFilepath(activeLesson.getFilepath().replace("http:", "https:"));
						activeChapter = chapter;
					}
					EntityWrapper<CoursewareLearningProgress> prgressWrapper = new EntityWrapper<>();
					prgressWrapper.eq("xm_id", xmId);
					prgressWrapper.eq("kj_id", courseware.getId());
					prgressWrapper.eq("student_id", studentId);
					prgressWrapper.eq("chapter_id", zjId);
					prgressWrapper.eq("lesson_id", leId);
					List<CoursewareLearningProgress> bizKjxxjds = coursewareLearningProgressService
							.selectList(prgressWrapper);
					CoursewareLearningProgress progress = bizKjxxjds.size() > 0 ? bizKjxxjds.get(0) : null;
					lesson.setCurPlayed(progress != null ? progress.getDuration() : 0);
					// 获取课时的学习资料
					EntityWrapper<CoursewareMaterial> materialWrapper = new EntityWrapper();
					materialWrapper.eq("kj_id", courseware.getId());
					materialWrapper.eq("chapter_id", zjId);
					materialWrapper.eq("lesson_id", leId);
					List<CoursewareMaterial> files = coursewareMaterialService.selectList(materialWrapper);
                    files.forEach(x->x.setUrl(x.getUrl().replaceAll("http://", "https://")));
					lesson.setFiles(files);
				}
				chapterIndex++;
			}
		}
		mv.addObject("courselearn", courselearn);
		mv.addObject("courseware", courseware);
		mv.addObject("activeLesson", activeLesson);
		mv.addObject("activeChapter", activeChapter);
		mv.addObject("xmId", xmId);
		ZypxXmCourseSetting zypxXmCourseSetting = xmCourseSettingService.selectById(courseSettingId);
		mv.addObject("courseSetting", zypxXmCourseSetting);
		mv.addObject("bmCount", service.countBmByCourse(courseSettingId));
		ZypxXm zypxXm = xmService.selectById(xmId);
		mv.addObject("xm", zypxXm);
		// 获取课程的报名人数作为学习人数
		if (zypxXmCourseSetting.getTeacherId() != null) {
			User teacher = userService.selectById(zypxXmCourseSetting.getTeacherId());
			mv.addObject("teacher", teacher);
		}
		// 评分
		EntityWrapper<CoursewareTargetResult> wrapper = new EntityWrapper<>();
		wrapper.eq("kj_id", courseware.getId());
		wrapper.eq("student_id", studentId);
		List<CoursewareTargetResult> targetResultList = coursewareTargetResultService.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(targetResultList)) {
			mv.addObject("starCount", targetResultList.get(0).getStarCount());
		}

		// 获取笔记
		CoursewareNote coursewareNote = new CoursewareNote(coursewareId, activeChapter.getId(), lessonId,
				super.getLoginStudentId(request));
		mv.addObject("notes", coursewareNoteService.getList(coursewareNote));
		// 获取评论
		CoursewareCommentQueryParams params = new CoursewareCommentQueryParams();
		params.setKjId(coursewareId);
		params.setChapterId(activeChapter.getId());
		params.setLessonId(lessonId);
		mv.addObject("comments", coursewareCommentService.getList(params));

		mv.addObject("course", courseService.selectById(courseware.getCourseId()));
		mv.addObject("photoPath", photoPath);// 进入观看时的抓拍、人脸比对的照片

		// 获取节的弹题目集合
		EntityWrapper<CoursewarePopQues> coursewarePopQuesWrapper = new EntityWrapper<>();
		coursewarePopQuesWrapper.eq("kj_id", coursewareId);
		coursewarePopQuesWrapper.eq("chapter_id", activeChapter.getId());
		coursewarePopQuesWrapper.eq("lesson_id", lessonId);
		coursewarePopQuesWrapper.isNotNull("ques_id");
		coursewarePopQuesWrapper.orderBy("pop_time", false);
		List<Object> quesList = new ArrayList<Object>();
		for (CoursewarePopQues coursewarePopQues : coursewarePopQuesService.selectList(coursewarePopQuesWrapper)) {
			Map<String, Object> question = questionEntityService.getQuesDetailsById(coursewarePopQues.getQuesId());
			if (StringUtils.containsAny(BaseUtil.getStringValueFromMap(question,"type"), Stlb.SINGLECHOICE.name(), Stlb.MULTIPLECHOICE.name(), Stlb.JUDGMENT.name())) {
                quesList.add(ModelHelper.convertObject(String.valueOf(question.get("data"))));
            }
		}
		mv.addObject("quesList", quesList);
		mv.setViewName("pages/personal/pages/courseStudy");
		return mv;
	}

    /**
     * 直播学习
     */
    @RequestMapping("/liveStudy")
    @Operation(desc = "直播学习")
    public Object liveStudy(HttpServletRequest request, ModelAndView mv, String xmId, String courseSettingId, String lessonId,String photoPath) {
        String studentId = super.getLoginStudentId(request);
        ZypxXmCourseSetting zypxXmCourseSetting = xmCourseSettingService.selectById(courseSettingId);
        CourseLive courseLive = courseLiveService.getByCourseId(zypxXmCourseSetting.getCourseId());
        String content = null;
        if (courseLive != null) {
        	content = courseLive.getContent();
		}
        //获取每一个课时的总时长，观看时长
        Courselive courselive = ModelHelper.convertObject(content);
        List<com.xunw.jxjy.model.common.courselive.Chapter> chapters = courselive.getChapters();
        com.xunw.jxjy.model.common.courselive.Chapter activeChapter = null;
        com.xunw.jxjy.model.common.courselive.Lesson activeLesson = null;
        Live activeLive = null;
        if (BaseUtil.isNotEmpty(chapters)) {
            int chapterIndex = 0;
            for (com.xunw.jxjy.model.common.courselive.Chapter chapter : chapters) {
                List<com.xunw.jxjy.model.common.courselive.Lesson> lessons = chapter.getLessons();
                chapter.setName("第" + BaseUtil.toCNLowerNum(chapterIndex + 1) + "章&nbsp;&nbsp;" + chapter.getName());
                for (com.xunw.jxjy.model.common.courselive.Lesson lesson : lessons) {
                	 Live live = liveService.selectById(lesson.getId());
                    if (lessonId.equals(lesson.getId())) {
                        activeLesson = lesson;
                        activeChapter = chapter;
                        activeLive = live;
                    }
                   
                    lesson.setZbzt(live.getZbzt());
                    User user = userService.selectById(live.getJsId());
                    lesson.setJsxm(user.getName());

                    //回放 直播状态已完成 并且 会话id为空
                    if (Zbzt.FINISHED == live.getZbzt() && live.getHkspsc() != null && live.getHkspsc() > 0) {
                        if (StringUtils.isEmpty(live.getHkhhida())) {
                            lesson.setLiveDocPath(live.getHkdza());
                        }
                        if (StringUtils.isEmpty(live.getHkhhidb())) {
                            lesson.setLiveTeacherPath(live.getHkdzb());
                        }
                    }
                    //直播推流
                    else if (Zbzt.PLAYING == live.getZbzt()) {
                        lesson.setLiveDocPath(live.getHlsdza());
                        lesson.setLiveTeacherPath(live.getHlsdzb());
                        if (request.getScheme().indexOf("https") > -1) {
                        	lesson.setLiveDocPath(lesson.getLiveDocPath().replace("http://pili-live-hls.zb.whxunw.com","https://pili-live-hls-zb.whxunw.com"));
                        	lesson.setLiveTeacherPath(lesson.getLiveTeacherPath().replace("http://pili-live-hls.zb.whxunw.com","https://pili-live-hls-zb.whxunw.com"));
						}
                    }
                    //查询直播的学习资料
                    lesson.setFiles(liveService.getLiveMaterialByLiveId(live.getId()));
                }
                chapterIndex++;
            }
        }
        try {
			mv.addObject("docIsLiving", QiniuZhiboUtils.isLiving(activeLive.getZblmca()) ? "WORKING" : "OFFLINE");
		} catch (PiliException e) {
			mv.addObject("docIsLiving", "OFFLINE");
		}
        try {
			mv.addObject("teacherIsLiving", QiniuZhiboUtils.isLiving(activeLive.getZblmcb()) ? "WORKING" : "OFFLINE");
		} catch (PiliException e) {
			mv.addObject("teacherIsLiving", "OFFLINE");
		}
        mv.addObject("courselive", courselive);
        mv.addObject("activeLesson", activeLesson);
        mv.addObject("activeChapter", activeChapter);
        mv.addObject("xmId", xmId);
        mv.addObject("photoPath", photoPath);
        mv.addObject("courseSetting", zypxXmCourseSetting);
        ZypxXm zypxXm = xmService.selectById(xmId);
        mv.addObject("xm", zypxXm);
        
        //获取课程的报名人数
        mv.addObject("bmCount", service.countBmByCourse(courseSettingId));

        //获取直播笔记
        mv.addObject("notes",getLiveNoteList(request,lessonId));

        // 获取直播评论
        LiveCommentQueryParams params = new LiveCommentQueryParams();
        params.setLiveId(lessonId);
        mv.addObject("comments", liveCommentService.getList(params));

        EntityWrapper<TeacherTargetResult> wrapper = new EntityWrapper<>();
        wrapper.eq("teacher_id", activeLesson.getJsid());
        wrapper.eq("student_id", studentId);
        List<TeacherTargetResult> targetResultList = teacherTargetResultService.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(targetResultList)) {
            mv.addObject("starCount", targetResultList.get(0).getStarCount());
        }
        mv.addObject("course", courseService.selectById(zypxXmCourseSetting.getCourseId()));
        mv.setViewName("pages/personal/pages/liveStudy");
        return mv;
    }

    /**
     * 记录课件学习进度,客户端轮训调用此接口一分钟调用一次,返回已经观看的时长
     */
    @RequestMapping("/markKjxxJd")
    @ResponseBody
    @Operation(desc = "记录课件学习进度")
    public Object markKjxxJd(HttpServletRequest request, @RequestParam(required = false) String xmId,
                             @RequestParam(required = false) String kjId, @RequestParam(required = false) String chapterId,
                             @RequestParam(required = false) String lessonId, @RequestParam(required = false) Integer minutes) {
        LoginStudent loginStudent = super.getLoginStudent(request);
        return service.markKjxxProgress(xmId, loginStudent.getUser().getId(),
                kjId, chapterId, lessonId, minutes);
    }
    
    /**
	 * 保存课件学习抓拍图片
	 */
	@RequestMapping("/savePhotoLog")
	@ResponseBody
	@Operation(desc = "保存课件学习抓拍图片")
	public Object savePhotoLog(HttpServletRequest request,
			@RequestParam(required = false) String xmId,
			@RequestParam(required = false) String kjId,
			@RequestParam(required = false) String chapterId,
			@RequestParam(required = false) String lessonId,
			@RequestParam(required = false) String base64PhotoPath,
			@RequestParam(required = false) String url) throws Exception{
		if(StringUtils.isEmpty(xmId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(kjId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(chapterId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(lessonId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if (StringUtils.isEmpty(url) && StringUtils.isEmpty(base64PhotoPath)){
			throw BizException.withMessage("未获取到抓拍图片");
		}
		if (StringUtils.isNotEmpty(url)) {
			service.savePhotoLog(xmId, kjId, chapterId, lessonId, url, super.getLoginStudentId(request));
		}
		if (StringUtils.isNotEmpty(base64PhotoPath)) {
	        url = FileHelper.convertImgBase64SrcStrToUrl(base64PhotoPath);
	        service.savePhotoLog(xmId, kjId, chapterId, lessonId, url, super.getLoginStudentId(request));
		}
		return true;
	}
	
	 /**
	 * 保存直播学习抓拍图片
	 */
	@RequestMapping("/saveLivePhotoLog")
	@ResponseBody
	@Operation(desc = "保存直播学习抓拍图片")
	public Object saveLivePhotoLog(HttpServletRequest request,
			@RequestParam(required = false) String xmId,
			@RequestParam(required = false) String courseliveId,
			@RequestParam(required = false) String chapterId,
			@RequestParam(required = false) String lessonId,
			@RequestParam(required = false) String base64PhotoPath,
			@RequestParam(required = false) String url) throws Exception{
		if(StringUtils.isEmpty(xmId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(courseliveId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(chapterId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if(StringUtils.isEmpty(lessonId)){
			throw BizException.PARAMS_ERROR;//参数缺失
		}
		if (StringUtils.isEmpty(url) && StringUtils.isEmpty(base64PhotoPath)){
			throw BizException.withMessage("未获取到抓拍图片");
		}
		if (StringUtils.isNotEmpty(url)) {
			service.saveLivePhotoLog(xmId, courseliveId, chapterId, lessonId, url, super.getLoginStudentId(request));
		}
		if (StringUtils.isNotEmpty(base64PhotoPath)) {
	        url = FileHelper.convertImgBase64SrcStrToUrl(base64PhotoPath);
	        service.saveLivePhotoLog(xmId, courseliveId, chapterId, lessonId, url, super.getLoginStudentId(request));
		}
		return true;
	}
    
    /**
     * 课件学习人脸检测、人脸比对
     */
	@RequestMapping("/faceVerify")
	@ResponseBody
	@Operation(desc = "课件学习人脸检测、人脸比对")
	public Object faceVerify(HttpServletRequest request, @RequestParam(required = false) String xmId,
			@RequestParam(required = false) String kjId, @RequestParam(required = false) String baseStr,
			@RequestParam(required = false) String isOpenVerify) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		if (StringUtils.isEmpty(kjId)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		if (StringUtils.isEmpty(baseStr)) {
			throw BizException.withMessage("未获取到抓拍照片,无法人脸比对");
		}
		if (StringUtils.isEmpty(isOpenVerify)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		String newUrl = FileHelper.convertImgBase64SrcStrToUrl(baseStr);
		// 需要人脸比对
		if (Constants.YES.equals(isOpenVerify)) {
			boolean boo = true;
			boo = service.faceVerify(xmId, kjId, newUrl, super.getLoginStudentId(request));
			if (!boo) {
				throw BizException.withMessage("抓拍照片与您的系统预留照片不符，请重试！");
			}
		} else {// 不需要人脸比对，直接检测人脸
			Map<String, Object> faceDetect = FaceVerifyUtil.isFaceDetect(newUrl);
			if (faceDetect == null || !((boolean) faceDetect.get("flag"))) {
				throw BizException.withMessage("没有识别到头像，请重新拍摄！");
			}
		}
		return newUrl;
	}
	
	 /**
     * 直播学习人脸检测、人脸比对
     */
	@RequestMapping("/livefaceVerify")
	@ResponseBody
	@Operation(desc = "直播学习人脸检测、人脸比对")
	public Object livefaceVerify(HttpServletRequest request, @RequestParam(required = false) String xmId,
			@RequestParam(required = false) String courseliveId, @RequestParam(required = false) String baseStr,
			@RequestParam(required = false) String isOpenVerify) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		if (StringUtils.isEmpty(courseliveId)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		if (StringUtils.isEmpty(baseStr)) {
			throw BizException.withMessage("未获取到抓拍照片,无法人脸比对");
		}
		if (StringUtils.isEmpty(isOpenVerify)) {
			throw BizException.PARAMS_ERROR;// 参数缺失
		}
		String newUrl = FileHelper.convertImgBase64SrcStrToUrl(baseStr);
		// 需要人脸比对
		if (Constants.YES.equals(isOpenVerify)) {
			boolean boo = true;
			boo = service.liveFaceVerify(xmId, courseliveId, newUrl, super.getLoginStudentId(request));
			if (!boo) {
				throw BizException.withMessage("抓拍照片与您的系统预留照片不符，请重试！");
			}
		} else {// 不需要人脸比对，直接检测人脸
			Map<String, Object> faceDetect = FaceVerifyUtil.isFaceDetect(newUrl);
			if (faceDetect == null || !((boolean) faceDetect.get("flag"))) {
				throw BizException.withMessage("没有识别到头像，请重新拍摄！");
			}
		}
		return newUrl;
	}

    /**
     * @Description 保存笔记
     * <AUTHOR>
     * @Time 2021/5/25 10:51
     */
    @RequestMapping("/saveNote")
    @ResponseBody
    @Operation(desc = "保存笔记")
    public Object saveNote(HttpServletRequest request, @RequestParam(required = false) String kjId,
                           @RequestParam(required = false) String chapterId,
                           @RequestParam(required = false) String lessonId,
                           @RequestParam(required = false) String bj) {
        CoursewareNote coursewareNote =
                new CoursewareNote(BaseUtil.generateId2(), kjId, chapterId, lessonId, bj,
                        super.getLoginStudentId(request), new Date());
        coursewareNoteService.save(coursewareNote);
        return true;
    }

    /**
     * @Description 查询笔记
     * <AUTHOR>
     * @Time 2021/5/25 10:51
     */
    @RequestMapping("/notes")
    @ResponseBody
    @Operation(desc = "查询笔记")
    public Object notes(HttpServletRequest request, @RequestParam(required = false) String kjId,
                        @RequestParam(required = false) String chapterId,
                        @RequestParam(required = false) String lessonId) {
        CoursewareNote coursewareNote =
                new CoursewareNote(kjId, chapterId, lessonId, super.getLoginStudentId(request));
        return coursewareNoteService.getList(coursewareNote);
    }


    /**
     * @Description 保存评论
     * <AUTHOR>
     * @Time 2021/5/25 10:51
     */
    @RequestMapping("/saveComment")
    @ResponseBody
    @Operation(desc = "保存评论")
    public Object saveComment(HttpServletRequest request, @RequestParam(required = false) String kjId,
                              @RequestParam(required = false) String chapterId,
                              @RequestParam(required = false) String lessonId,
                              @RequestParam(required = false) String pl) {
        CoursewareComment coursewareNote =
                new CoursewareComment(BaseUtil.generateId2(), kjId, chapterId, lessonId, pl, super.getLoginStudentId(request), new Date());
        coursewareCommentService.save(coursewareNote);
        return true;
    }

    /**
     * @Description 查询评论
     * <AUTHOR>
     * @Time 2021/5/25 10:51
     */
    @RequestMapping("/comments")
    @ResponseBody
    @Operation(desc = "查询评论")
    public Object comments(HttpServletRequest request, CoursewareCommentQueryParams params) {
        return coursewareCommentService.getList(params);
    }

    /**
     * @Description 保存直播笔记
     */
    @RequestMapping("/saveLiveNote")
    @ResponseBody
    @Operation(desc = "保存直播笔记")
    public Object saveLiveNote(HttpServletRequest request,
                               @RequestParam(required = false) String liveId,
                               @RequestParam(required = false) String text) {

        LiveNote liveNote = new LiveNote();
        liveNote.setId(BaseUtil.generateId2());
        liveNote.setLiveId(liveId);
        liveNote.setContent(text);
        liveNote.setCreatorId(super.getLoginStudentId(request));
        liveNote.setCreateTime(new Date());
        liveNoteService.insert(liveNote);
        return true;
    }

    /**
     * @Description 查询直播笔记
     */
    @RequestMapping("/getLiveNoteList")
    @ResponseBody
    @Operation(desc = "查询直播笔记")
    public Object getLiveNoteList(HttpServletRequest request, @RequestParam(required = false) String liveId) {
        EntityWrapper<LiveNote> wrapper = new EntityWrapper<>();
        wrapper.eq("live_id", liveId);
        wrapper.eq("creator_id", super.getLoginStudentId(request));
        wrapper.orderBy("create_time", false);
        return liveNoteService.selectList(wrapper);
    }

    /**
     * @Description 保存直播评论
     */
    @RequestMapping("/saveLiveComment")
    @ResponseBody
    @Operation(desc = "保存直播评论")
    public Object saveLiveComment(HttpServletRequest request,
                                  @RequestParam(required = false) String liveId,
                                  @RequestParam(required = false) String content) {
        LiveComment liveComment =
                new LiveComment(BaseUtil.generateId2(), liveId, content, new Date(),super.getLoginStudentId(request));
        liveCommentService.save(liveComment);
        return true;
    }
    
    /**
     * @Description 查询评论
     */
    @RequestMapping("/listLiveComments")
    @ResponseBody
    @Operation(desc = "查询评论")
    public Object liveComments(HttpServletRequest request, LiveCommentQueryParams params) {
        return liveCommentService.getList(params);
    }
    
    /**
     * 直播讲师评分
     */
    @RequestMapping("/saveTeacherTargetResult")
    @ResponseBody
    @Operation(desc = "直播讲师评分")
    public Object saveTeacherTargetResult(HttpServletRequest request, @RequestParam(required = false) String teacherId, @RequestParam(required = false) String starCount) {
        if (StringUtils.isEmpty(teacherId)) {
            throw BizException.withMessage("讲师ID为空");
        }
        if (StringUtils.isEmpty(starCount)) {
            throw BizException.withMessage("评价星为空");
        }
        String studentId = super.getLoginStudentId(request);
        EntityWrapper<TeacherTargetResult> wrapper = new EntityWrapper<>();
        wrapper.eq("teacher_id", teacherId);
        wrapper.eq("student_id", studentId);
        List<TeacherTargetResult> targetResultList = teacherTargetResultService.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(targetResultList)) {
            TeacherTargetResult result = targetResultList.get(0);
            result.setStarCount(Integer.valueOf(starCount));
            teacherTargetResultService.updateById(result);
        } else {
            TeacherTargetResult result = new TeacherTargetResult();
            result.setId(BaseUtil.generateId2());
            result.setStarCount(Integer.valueOf(starCount));
            result.setTeacherId(teacherId);
            result.setStudentId(studentId);
            result.setTime(new Date());
            teacherTargetResultService.insert(result);
        }
        return true;
    }

    /**
     * 课件评分
     */
    @RequestMapping("/saveCoursewareTargetResult")
    @ResponseBody
    @Operation(desc = "课件评分")
    public Object saveCoursewareTargetResult(HttpServletRequest request, @RequestParam(required = false) String kjId, @RequestParam(required = false) String starCount) {
        if (StringUtils.isEmpty(kjId)) {
            throw BizException.withMessage("课件ID为空");
        }
        if (StringUtils.isEmpty(starCount)) {
            throw BizException.withMessage("评价星为空");
        }
        String studentId = super.getLoginStudentId(request);
        coursewareTargetResultService.saveCoursewareTargetResult(studentId,kjId,starCount);
        return true;
    }
}
