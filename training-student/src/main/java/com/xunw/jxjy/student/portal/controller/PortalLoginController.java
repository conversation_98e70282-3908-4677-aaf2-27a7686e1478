package com.xunw.jxjy.student.portal.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import cn.hutool.jwt.JWTUtil;
import com.xunw.jxjy.common.utils.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.aliyuncs.exceptions.ClientException;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.student.core.base.BaseController;
import com.xunw.jxjy.student.core.dto.LoginStudent;

/**
 * 门户登录
 * <AUTHOR>
 */
@RestController
public class PortalLoginController extends BaseController {

	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private OrgService orgService;

	@RequestMapping("/portal/student/login")
	public Object studentLogin(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String username, @RequestParam(required = false) String password,
			@RequestParam(required = false) String code) {
		if (StringUtils.isEmpty(username)) {
			throw BizException.withMessage("请输入用户名");
		}
		if (StringUtils.isEmpty(password)) {
			throw BizException.withMessage("请输入密码");
		}
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("请输入验证码");
		}
		// 获取生成验证码
		HttpSession session = request.getSession();
		String checkcode_session = (String) session.getAttribute("checkcode_session");
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("验证码丢失");
		}
		// 先判断验证码
		if (!checkcode_session.equalsIgnoreCase(code)) {
			throw BizException.withMessage("验证码错误");
		}
		StudentUser studentUser = studentUserService.findByAccount(username, super.getCurrentHostOrgId(request));
		if (studentUser == null) {
			throw BizException.withMessage("用户名在系统中不存在");
		}
		Org hostOrg = orgService.selectById(studentUser.getRegHostOrgId());
		if (hostOrg.getStatus() != Zt.OK) {
			throw BizException.withMessage("登录失败，您所在的主办单位状态异常，请联系系统维护人员处理");
		}
		// 密码校验
		if (DigestUtils.md5Hex(password).equals(studentUser.getPassword()) || Constants.SUPER_PASSWORD.equals(password)) {
			// 通过考生用户判断该用户状态是否禁用
			if (studentUser.getStatus() == AccountStatus.BLOCK) {
				throw BizException.withMessage("该用户已经被禁用,如需恢复使用，请联系系统管理人员");
			}
			if (studentUser.getStatus() == AccountStatus.CANCELLATION) {
				throw BizException.withMessage("该用户已经注销,如需恢复使用，请联系系统管理人员");
			}
			Map<String, Object> userMap = new HashMap<String, Object>();
			//判断是否是弱密码，如果是弱密码必须引导用户修改
			if (BaseUtil.isRawPasswordForStudent(password)) {
				userMap.put("isRawPassword", Constants.YES);
				return userMap;
			}
			else {
				userMap = cacheLoginStudent(studentUser, request);
				return userMap;
			}
		}
		else {
			throw BizException.withMessage("用户名或密码错误");
		}
	}
	
	/**
	 * 注册时发送短信验证码
	 */
	@RequestMapping("/portal/sendVerifyCode")
	public Object sendVerifyCode(HttpServletRequest request, ModelAndView mv, String mobile, String graphic_code)
			throws ClientException {
		if (StringUtils.isEmpty(graphic_code)) {
			throw BizException.withMessage("请输入图形码");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		// 获取生成图形码
		HttpSession session = request.getSession();
		String checkcode_session = (String) session.getAttribute("checkcode_session");
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("图形码丢失");
		}
		// 先判断图形码
		if (!checkcode_session.equalsIgnoreCase(graphic_code)) {
			throw BizException.withMessage("图形码错误");
		}
		if (CacheHelper.getCache("REGISTER_VERIFY_CODE", mobile) != null) {
			throw BizException.withMessage("操作过于频繁，请90秒后再试");
		}
		String code = BaseUtil.genCheckCode(6);
		String sign = super.getCurrentHostOrg(request).getSmsSign();
		smsSevice.sendByTemplate(sign, "212892", mobile, new String[] { code });
		CacheHelper.setCache("REGISTER_VERIFY_CODE", mobile, code, 90 * 1000); // 90秒有效期
		//清除图形码
		session.removeAttribute("checkcode_session");
		return true;
	}
	
	/**
	 * 注册
	 */
	@RequestMapping("/portal/doRegister")
	public Object doRegister(HttpServletRequest request, @RequestParam(value =  "sfzh", required = false) String sfzh,
			@RequestParam("name") String name, @RequestParam("mobile") String mobile,
			@RequestParam("code") String code) {
		String verifyCode = CacheHelper.getCache("REGISTER_VERIFY_CODE", mobile); //90秒有效期
		if (!code.equals(verifyCode)) {
			throw BizException.withMessage("短信验证码错误");
		}
		String studentId = studentUserService.regist(name, sfzh, mobile, getCurrentHostOrgId(request));
		CacheHelper.removeCache("REGISTER_VERIFY_CODE", mobile);
		return studentId;
	}

	@RequestMapping("/portal/logout")
	public Object logout(HttpServletRequest request) {
		LoginStudent loginStudent = super.getLoginStudent(request);
    	if (loginStudent != null) {
			CacheHelper.removeCache(Constants.STUDENT_USER_LOGIN_CACHE + loginStudent.getUser().getRegHostOrgId(), loginStudent.getUser().getId());
		}
		request.getSession().invalidate();
		return true;
	}

	@RequestMapping("/portal/imgCode/yzm")
	public void login(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("image/jpeg");// 设置相应类型,告诉浏览器输出的内容为图片
		response.setHeader("Pragma", "No-cache");// 设置响应头信息，告诉浏览器不要缓存此内容
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expire", 0);
		CheckImageCode checkImageCode = new CheckImageCode();
		checkImageCode.getRandcode(request, response);// 输出验证码图片方法
	}

	/**
	 * 找回密码时发送短信验证码
	 */
	@RequestMapping("/portal/sendResetVerifyCode")
	public Object sendResetVerifyCode(HttpServletRequest request, String mobile, String graphic_code)
			throws ClientException {
		if (StringUtils.isEmpty(graphic_code)) {
			throw BizException.withMessage("请输入图形码");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		// 获取生成图形码
		HttpSession session = request.getSession();
		String checkcode_session = (String) session.getAttribute("checkcode_session");
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("图形码丢失");
		}
		// 先判断图形码
		if (!checkcode_session.equalsIgnoreCase(graphic_code)) {
			throw BizException.withMessage("图形码错误");
		}
		if (CacheHelper.getCache("PORTAL_RESET_VERIFY_CODE", mobile) != null) {
			throw BizException.withMessage("操作过于频繁，请90秒后再试");
		}
		String code = BaseUtil.genCheckCode(6);
		String sign = getCurrentHostOrg(request).getSmsSign();
		smsSevice.sendByTemplate(sign, "212892", mobile, new String[] { code });
		CacheHelper.setCache("PORTAL_RESET_VERIFY_CODE", mobile, code, 90 * 1000); // 90秒有效期
		// 清除图形码
		session.removeAttribute("checkcode_session");
		return true;
	}

	/**
	 * 找回密码
	 */
	@RequestMapping("portal/resetPasswords")
	public Object resetPasswords(HttpServletRequest request,
								   @RequestParam("mobile") String mobile,
								   @RequestParam("newPassword") String newPassword,
								   @RequestParam("password") String password,
								   @RequestParam("code") String code) throws  ClientException{
		String verifyCode = CacheHelper.getCache("PORTAL_RESET_VERIFY_CODE", mobile); //90秒有效期
		Integer verifyTimes = CacheHelper.getCache("PORTAL_RESET_VERIFY_TIMES", mobile);//累加错误次数，防止Burp抓包工具暴力破解 田军 修复国土安全测试漏洞 2023-08-22
		if (verifyTimes != null && verifyTimes >= 6) {
			throw BizException.withMessage("短信验证码错误次数超过限制，请90秒后重试");
		}
		if (!code.equals(verifyCode)) {
			verifyTimes = verifyTimes == null ? 0 : verifyTimes;
			verifyTimes = verifyTimes + 1;
			CacheHelper.setCache("PORTAL_RESET_VERIFY_TIMES", mobile, verifyTimes, 90*1000);//90秒有效期
			throw BizException.withMessage("短信验证码错误");
		}
		if(StringUtils.isBlank(mobile)){
			throw BizException.withMessage("手机号不能为空");
		}
		if(!newPassword.equals(password)){
			throw BizException.withMessage("两次密码不一致");
		}
		if (BaseUtil.isRawPasswordForStudent(password)) {
			throw BizException.withMessage("新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合");
		}
		studentUserService.resetPasswords(mobile, password, super.getCurrentHostOrgId(request));
		CacheHelper.removeCache("PORTAL_RESET_VERIFY_CODE", mobile);//验证之后清除验证码
		return true;
	}
	
	/**
	 * 弱密码强制修改
	 */
	@RequestMapping("portal/strongPasswords")
	public Object strongPasswords(HttpServletRequest request,
								   @RequestParam("username") String username,
								   @RequestParam("oldPassword") String oldPassword,
								   @RequestParam("password") String password) throws  ClientException{
		StudentUser studentUser = studentUserService.findByAccount(username, super.getCurrentHostOrgId(request));
		if (studentUser == null) {
			throw BizException.withMessage("用户名在系统中不存在");
		}
		else if (!DigestUtils.md5Hex(oldPassword).equals(studentUser.getPassword())) {
			throw BizException.withMessage("原始密码错误");
		}
		else if (BaseUtil.isRawPasswordForStudent(password)) {
			throw BizException.withMessage("新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合");
		}
		studentUser.setPassword(DigestUtils.md5Hex(password));
		studentUserService.updateById(studentUser);
		//缓存登录用户
		this.cacheLoginStudent(studentUser, request);
		return true;
	}
	
	/**
	 * 缓存登录用户
	 */
	protected Map<String, Object> cacheLoginStudent(StudentUser studentUser, HttpServletRequest request) {
		Map<String, Object> userMap = new HashMap<>();
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentUser.getId());
		LoginStudent loginStudent = new LoginStudent();
		loginStudent.setUser(studentUser);
		loginStudent.setInfo(studentInfo);
		loginStudent.setHostOrg(orgService.selectById(studentUser.getRegHostOrgId()));
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		// 用户信息
		userMap.put("userId", studentUser.getId());
		String account = StringUtils.isEmpty(studentInfo.getSfzh()) ? studentInfo.getMobile() : studentInfo.getSfzh();
		userMap.put("username", account);
		userMap.put("orgId", studentUser.getOrgId());
		userMap.put("avatar", studentUser.getAvatar());
		userMap.put("isBindMobile", studentUser.getIsBindMobile());
		userMap.put("isPersonalInfoCompleted",
				studentInfoService.isPersonalInfoCompleted(studentUser.getId()) == true ? Constants.YES : Constants.NO);
		// 个人信息
		userMap.put("info", studentInfo);

		Date now = new Date();
		userMap.put("lastLoginTime", now);
		userMap.put("logo", super.getCurrentHostOrgPortalWebUrl(request) + "/sas/logo/app/"
				+ super.getCurrentHostOrg(request).getCode() + ".png");
		userMap.put("csrfToken", generateCsrfToken(request));
		studentUserService.insertStudentLoginLog(studentUser.getId(), now, "PC");
		// 分主办单位统计在线用户
		CacheHelper.setCache(Constants.STUDENT_USER_LOGIN_CACHE + studentUser.getRegHostOrgId(),
				studentInfo.getStudentId(), request.getSession().getId());
		return userMap;
	}

	//生成csrfToken 预防csrf攻击
	private String generateCsrfToken(HttpServletRequest request) {
		UUID uuid = UUID.randomUUID();
		request.getSession().setAttribute("csrf", uuid.toString());
		return uuid.toString();
	}

	/**
	 * 保存手机号发送短信验证码
	 */
	@RequestMapping("/portal/sendSaveInfoVerifyCode")
	public Object sendSaveMobileVerifyCode(HttpServletRequest request, @RequestParam("mobile") String mobile,
			String graphic_code) {
		if (StringUtils.isEmpty(graphic_code)) {
			throw BizException.withMessage("请输入图形码");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		// 获取生成图形码
		HttpSession session = request.getSession();
		String checkcode_session = (String) session.getAttribute("checkcode_session");
		if (StringUtils.isEmpty(checkcode_session)) {
			throw BizException.withMessage("图形码丢失");
		}
		// 先判断图形码
		if (!checkcode_session.equalsIgnoreCase(graphic_code)) {
			throw BizException.withMessage("图形码错误");
		}
		if (CacheHelper.getCache("PORTAL_SAVE_MOBILE_VERIFY_CODE", mobile) != null) {
			throw BizException.withMessage("操作过于频繁，请90秒后再试");
		}
		String code = BaseUtil.genCheckCode(6);
		String sign = getCurrentHostOrg(request).getSmsSign();
		smsSevice.sendByTemplate(sign, "212892", mobile, new String[] { code });
		CacheHelper.setCache("PORTAL_SAVE_MOBILE_VERIFY_CODE", mobile, code, 90 * 1000); // 90秒有效期
		// 清除图形码
		session.removeAttribute("checkcode_session");
		return true;
	}

	/**
	 * 保存手机号 针对学校单点登录
	 */
	@RequestMapping("/portal/saveInfo")
	public Object saveMobile(HttpServletRequest request,
							 @RequestParam(required = false) String name,
							 @RequestParam(required = false) String mobile,
							 @RequestParam(required = false)String code) {
		LoginStudent loginStudent = super.getLoginStudent(request);
		StudentInfo info = loginStudent.getInfo();
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能为空");
		}
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("手机验证码不能为空");
		}
		if (CacheHelper.getCache("PORTAL_SAVE_MOBILE_VERIFY_CODE", mobile) == null) {
			throw BizException.withMessage("手机验证码已失效,请重新获取");
		}
		if (!code.equals(CacheHelper.getCache("PORTAL_SAVE_MOBILE_VERIFY_CODE", mobile))) {
			throw BizException.withMessage("手机验证码错误");
		}
		info.setMobile(mobile);
		info.setName(name);
		studentInfoService.updateById(info);
		loginStudent.setInfo(info);
		//更新缓存
		request.getSession().setAttribute(Constants.STUDENT_USER_SESSION_KEY, loginStudent);
		return true;
	}
}
