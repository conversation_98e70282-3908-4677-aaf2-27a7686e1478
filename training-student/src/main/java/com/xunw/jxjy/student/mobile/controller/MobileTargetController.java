package com.xunw.jxjy.student.mobile.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.AnswerType;
import com.xunw.jxjy.model.zypx.entity.Target;
import com.xunw.jxjy.model.zypx.entity.TargetResult;
import com.xunw.jxjy.model.zypx.entity.TargetSetting;
import com.xunw.jxjy.model.zypx.params.TargetResultQueryParams;
import com.xunw.jxjy.model.zypx.service.TargetResultService;
import com.xunw.jxjy.model.zypx.service.TargetService;
import com.xunw.jxjy.model.zypx.service.TargetSettingService;
import com.xunw.jxjy.student.core.annotation.Operation;
import com.xunw.jxjy.student.core.base.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@RestController
@RequestMapping("/kaosheng/mobile/biz/zypx/target")
public class MobileTargetController extends BaseController {

	@Autowired
	private TargetResultService targetResultService;
	@Autowired
    private TargetService targetService;
    @Autowired
    private TargetSettingService targetSettingService;

	/**
	 * 获取顶层指标分类
	 */
	@RequestMapping("/list")
    @Operation(desc = "获取顶层指标分类")
	public Object list(@RequestParam(required = false) String xmId){
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return targetSettingService.list(xmId);
	}

	/**
	 * 获取评价指标
	 */
	@RequestMapping("/detail")
	@Operation(desc = "获取评价指标")
	public Object detail(HttpServletRequest request,
						 @RequestParam(required = false) String id,
						 @RequestParam(required = false) String xmId,
						 @RequestParam(required = false) String courseSettingId){
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("指标分类id不能为空");
		}
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return targetService.detail(id, xmId, courseSettingId, super.getLoginStudentId(request));
	}

	@RequestMapping("/evaluate")
	@Operation(desc = "保存评价")
	public Object CourseEvaluate(HttpServletRequest request, @RequestBody JSONObject json) {
		String courseSettingId = json.getString("courseSettingId");
		String xmId = json.getString("xmId");
		if (BaseUtil.isEmpty(courseSettingId) && BaseUtil.isEmpty(xmId)) {
			throw BizException.withMessage("参数异常");
		}
		JSONArray evaluates = json.getJSONArray("evaluates");
		List<TargetResult> targetResultList = new ArrayList<>();
		for (Object evaluate : evaluates) {
			JSONObject jsonObject = (JSONObject) evaluate;
			String settingId = jsonObject.getString("settingId");
			if (StringUtils.isEmpty(settingId)) {
				throw BizException.withMessage("请选择评价指标");
			}
			TargetSetting targetSetting = targetSettingService.selectById(settingId);
			Target target = targetService.selectById(targetSetting.getTargetId());
			Integer starCount = jsonObject.getInteger("starCount");
			if (!AnswerType.ADVICE.name().equals(target.getAnswerType()) && starCount == null) {
				throw BizException.withMessage("请选择评价分数");
			}
			TargetResult targetResult = new TargetResult();
			targetResult.setId(BaseUtil.generateId2());
			targetResult.setStudentId(super.getLoginStudentId(request));
			targetResult.setStarCount(starCount);
			targetResult.setRemark(jsonObject.getString("content"));
			targetResult.setSettingId(settingId);
			targetResult.setCourseSettingId(courseSettingId);
			targetResult.setTime(new Date());
			targetResultList.add(targetResult);
		}
		DBUtils.insertBatch(targetResultList, TargetResult.class);
		return true;
	}

	/**
	 * 保存项目评价
	 */
	@RequestMapping("/eval")
	@Operation(desc = "保存项目评分")
	public Object setScore(HttpServletRequest request,
						   @RequestParam(required = false)String id,
						   @RequestParam(required = false)String targetId,
						   @RequestParam(required = false)String starCount) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择项目信息");
		}
		if (StringUtils.isEmpty(targetId)) {
			throw BizException.withMessage("请选择项目指标信息");
		}
		if (StringUtils.isEmpty(starCount)) {
			throw BizException.withMessage("请选择评价分数");
		}
		String[] targetIds = targetId.split(",");
		String[] starCounts = starCount.split(",");
		String studentId = super.getLoginStudentId(request);
		TargetResultQueryParams targetResultQueryParams = new TargetResultQueryParams();
		targetResultQueryParams.setXmId(id);
		for (int i = 0; i < targetIds.length; i++) {
			targetResultQueryParams.setTargetId(targetIds[i]);
			targetResultQueryParams.setScore(Integer.parseInt(starCounts[i]));
			targetResultService.add(targetResultQueryParams, studentId);
		}
		return true;
	}

}
