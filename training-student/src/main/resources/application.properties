#####################应用程序总配置文件#####################
####应用名
application.name=training-student
server.port=8027

####系统运行参数配置
#文件存储临时目录
att.tempdir=d://Documents/D_SOFT/TEMP
#文件存储方式 是本地存储 还是七牛云存储
att.store-type=CLOUD
#文件归档存储路径(本地存储时启用,非本地存储无效)
att.store-dir=/Volumes/D/STORE
# 由系统生成的文件存储相对路径前缀(请勿包含文件路径分隔符""或者"/")
att.root-dir=guotu_att
#文件访问路径前缀
att.url-prefix=http://jxjy-att.whxunw.com/

#七牛云配置
qiniu.access-Key=eO32A7yxJWpO6BAu65L4sykwUTYRfshEZ2uWhDod
qiniu.secret-key=yyJWIpNz6kgbYLrtnoQhUb2g4mXcMh6Nzsx77cab
##必须是华东地区下面的存储空间##
qiniu.bucket=jxjy-study
qiniu.urlprefix=http://jxjy-att.whxunw.com/
##生成的直播回放文件的前缀##
qiniu.zbhfprefix=JXJY
##七牛云 图片高级处理之旋转参数##
qiniu.imageMogr2-rotate=?imageMogr2/rotate/

#QQ地图服务调用key
qq.map-key=A66BZ-Y5LH6-P4ESZ-ECDC6-JDCUZ-4EFE4
qq.map-app-name=training_ms_mark_arrive_app

#异步任务线程池设置
task.corePoolSize=8
task.maxPoolSize=12
task.queueCapacity=200

#文件上传
spring.http.encoding.charset=UTF-8
spring.http.multipart.enabled=true
spring.http.multipart.max-file-size=1024MB
spring.http.multipart.max-request-size=1024MB

####平台数据库地址以及账号配置
#spring.datasource.url=******************************************
spring.datasource.url=*******************************************
spring.datasource.username=st_2023_test
spring.datasource.password=st_2023_test

spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

###CRP数据源配置
crp.datasource.url=*********************************************************
#crp.datasource.url=******************************************************
crp.datasource.username=jbxx
crp.datasource.password=hbgt_jbsj89###
crp.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
crp.datasource.type=com.alibaba.druid.pool.DruidDataSource


####数据库连接池配置
spring.datasource.initial-size=5
spring.datasource.min-idle=5
spring.datasource.max-active=60
# 配置获取连接等待超时的时间
spring.datasource.max-wait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.time-between-eviction-runs-millis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.min-evictable-idle-time-millis=300000
spring.datasource.validation-query=SELECT 1 FROM DUAL
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=false
spring.datasource.test-on-return=false
# 打开PSCache，并且指定每个连接上PSCache的大小
spring.datasource.pool-prepared-statements=true
spring.datasource.max-pool-prepared-statement-per-connection-size=20
# 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
spring.datasource.filters=stat,wall,log4j
# 通过connectProperties属性来打开mergeSql功能；慢SQL记录
spring.datasource.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
# 合并多个DruidDataSource的监控数据
#spring.datasource.useGlobalDataSourceStat=true

####MyBatis-Plus插件配置
mybatis-plus.typeAliasesPackage=com.xunw.jxjy.model.common.entity,com.xunw.jxjy.model.exam.entity,com.xunw.jxjy.model.inf.entity,com.xunw.jxjy.model.learning.entity,com.xunw.jxjy.model.sys.entity,com.xunw.jxjy.model.tk.entity,com.xunw.jxjy.model.wdxx.entity,com.xunw.jxjy.model.zyjd.entity,com.xunw.jxjy.model.zypx.entity,com.xunw.jxjy.model.alias
mybatis-plus.typeEnumsPackage=com.xunw.jxjy.model.enums
mybatis-plus.mapper-locations=classpath:/mapper/*Mapper.xml
mybatis-plus.global-config.id-type=1
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.jdbc-type-for-null=null
mybatis-plus.configuration.call-setters-on-nulls=true
# 如果不需要打印SQL请注释此行代码
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
spring.jackson.time-zone=GMT+8
spring.aop.proxy-target-class=true

####系统日志配置
logging.config=classpath:logback-spring.xml
####JSON序列化日期类型的默认时间格式配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

####REDIS (RedisProperties)
# Redis数据库索引（默认为0）
spring.redis.database=1
# Redis服务器地址
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=sebms_study
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=500
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=100
# 连接池中的最小空闲连接s
spring.redis.pool.min-idle=20
# 连接超时时间（毫秒）
spring.redis.timeout=180000
#spring session 存储
spring.session.store-type=redis

####freemarker模板配置
spring.freemarker.request-context-attribute=request
spring.freemarker.allow-request-override=false
spring.freemarker.allow-session-override=false
spring.freemarker.cache=false
spring.freemarker.charset=UTF-8
spring.freemarker.check-template-location=true
spring.freemarker.content-type=text/html
spring.freemarker.enabled=true
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=true
spring.freemarker.prefer-file-system-access=true
spring.freemarker.suffix=.html
spring.freemarker.template-loader-path=classpath:/templates/
spring.freemarker.settings.template_update_delay=0
spring.freemarker.settings.default_encoding=UTF-8
spring.freemarker.settings.classic_compatible=true
spring.freemarker.order=1
spring.resources.static-locations=classpath:/templates/static/

#######短信平台秘钥配置#######
sms.key=KMaYrOIiFV6PgCSf
sms.secret=xiFKt2cUzVJapyPFquaLuFzDH8AI5EmE

##face++配置
facePlus.key=iM3sI9lzGXCaCGd1W3rVfpL1K_ouDGQW
facePlus.secret=3O2lX_vk6yqShSjuOJYWVH4HjUsARXXH
  

##腾讯云多人会议配置参数
txy.SDKAppID = 1600046249
txy.SDKSecretKey = f5f92d56187b1ba28acdeffa3143fb2146f143b527621a5bdb0ee245629a954e
txy.identifier = administrator
txy.notify.password = 123456
txy.playback_url = https://trtc-video.whxunw.com
tencentyun.sdk.secretId = AKIDSuSs9LSQE0gDZmxypW2MVoAMNE3B9xd9
tencentyun.sdk.secretKey = RipmbHrJcmo9WuJnMxU0aFxqowGoQ8kW
tencentyun.sdk.region = ap-beijing
tencentyun.sdk.bucket = xunw-1325701489

weather.web_key = 7ef8570c589be6f07d2da01b7ba1aa4a
weather.applet_key = c5fff3155870c4be3da5be0dfa4d7f3d
weather.request_url = https://restapi.amap.com/v3/weather/weatherInfo

env.hostOrgId=