body * {
  box-sizing: border-box;
  flex-shrink: 0;
}
body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
    Arial, PingFang SC-Light, Microsoft YaHei;
  margin: 0;
}
input {
  background-color: transparent;
  border: 0;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}

button:active {
  opacity: 0.6;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}
.justify-around {
  display: flex;
  justify-content: space-around;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.align-start {
  display: flex;
  align-items: flex-start;
}
.align-center {
  display: flex;
  align-items: center;
}
.align-end {
  display: flex;
  align-items: flex-end;
}
.cp {
  cursor: pointer;
}
.usn {
  user-select: none;
  -webkit-user-drag: none;
}
.color-f {
  color: #ffffff;
}
a {
  text-decoration: none;
  color: inherit;
}
.con{
  width: 1200px;
  margin: auto;
  position: relative;
}
.p-t15{
  padding-top: 15px;
}
.p-t10{
  padding-top: 10px;
}
.p-t20{
  padding-top: 20px;
}
.pr{
  position: relative;
}

/* 页头 */
.header {
  background-image: linear-gradient(
    360deg,
    rgba(1, 88, 168, 0.04) 0,
    rgba(1, 88, 168, 0.93) 100%
  );
  width: 100%;
  height: 100px;
  position: fixed;
  z-index: 10;
}
.header_con {
  width: 1200px;
  margin: auto;
  height: 100%;
}
.image_1 {
  width: 235px;
  height: 60px;
}
.box_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 2px;
  height: 20px;
  margin: 0 15px;
}
.header .title {
  font-weight: 400;
  font-size: 20px;
}
.header_right {
  position: relative;
  height: 50px;
  display: flex;
  align-items: flex-end;
}
.header .right_Tab {
  font-weight: 400;
  font-size: 18px;
  color: #ffffff;
}
.header .right_Tab > a {
  margin-left: 36px;
  position: relative;
  cursor: pointer;
}
.header .right_Tab .right_Tab_Check::after {
  content: "";
  width: 30px;
  border-bottom: 3px solid #ffa000;
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}
.header .loginBtn {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ffffff;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  padding: 0 8px;
  position: absolute;
  align-items: center;
  right: 0;
  top: -10px;
  cursor: pointer;
}
.header .loginBtn img {
  width: 10px;
  height: 12px;
  margin-right: 6px;
}
/* 轮播图 */
.carousel {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 660px;
}

.carousel-images {
  display: flex;
  transition: transform 0.5s ease;
  width: 100%;
}
.carousel-images a {
  width: 100%;
}
.carousel-images img {
  width: 100%;
  height: 600px;
  flex-shrink: 0;
}
.carousel-m {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.carousel-m span {
  display: inline-block;
  width: 30px;
  height: 5px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px 10px 10px 10px;
  cursor: pointer;
}
.carousel-m .carousel-m-check {
  background: #ffffff;
}

/* .hovertx{
  transition: all .3s ease;
  
}
.hovertx:hover{
  transform: scale(1.03);
} */

/* 页脚 */
.foot {
  /* height: 512px; */
  padding: 50px;
  background-image: url(../img/index/foot.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position-y: bottom;
  font-weight: 400;
  font-size: 14px;
  color: #dddddd;
}
.foot-c-title {
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
}
.foot .con .foot-c-nr {
  margin-top: 15px;
  display: block;
}
.foot-one,
.foot-t {
  padding: 20px 0;
}
.foot-s-qr {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
.foot-s-qr>div{
  text-align: center;
  margin: 0 20px;
}
.pb20{
  padding-bottom: 20px;
}