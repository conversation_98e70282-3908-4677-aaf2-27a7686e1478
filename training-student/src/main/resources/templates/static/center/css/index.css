/* 理工要闻 */
.lg-xw {
  width: 100%;
  background: url(../img/index/lg_yw.png) 100% 100%;
  padding: 50px;
}
.con-header {
  padding-bottom: 20px;
  display: flex;
  align-items: center;
  position: relative;
  user-select: none;
  -webkit-user-drag: none;
}
.con-header-t {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24px;
  margin: auto;
}
.con-header-t-font-c {
  color: #005caf;
}
.con-header-t img {
  width: 161px;
  height: 16px;
  margin: 0 15px;
}
.con-header-all {
  font-weight: 400;
  font-size: 16px;
  color: #667280;
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
}
.con-header-all img {
  margin-left: 4px;
}

.lg-xw-con {
}
.lg-xw-con-left {
  margin-left: 17px;
  position: relative;
}
.lg-xw-con-left-yj {
  font-weight: 400;
  font-size: 18px;
  color: #ffffff;
  position: absolute;
  left: -17px;
  top: -10px;
}
.lg-xw-con-left-yj > div {
  position: absolute;
  top: 18px;
  left: 17px;
}
.lg-xw-con-left-bt {
  background: linear-gradient(270deg, #0054a0 0%, #006ccd 100%);
  border-radius: 0px 0px 10px 10px;
  padding: 23px 30px;
  font-weight: 400;
  font-size: 18px;
  color: #ffffff;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  width: 600px;
  margin-top: -4px;
}
.lg-xw-con-left img {
  border-radius: 10px 10px 0 0;
}
.lg-xw-con-right {
  width: 530px;
}

.lg-xw-con-right-xw {
  border-bottom: 1px solid #eaeaea;
  display: block;
  padding-bottom: 15px;
  margin-bottom: 15px;
}
.lg-xw-con-right-xw:last-of-type {
  border: none;
}
.lg-xw-con-right-xw-time {
  font-weight: 400;
  font-size: 20px;
  line-height: 21px;
  color: #006ccd;
  position: relative;
}
.lg-xw-con-right-xw-time::after {
  content: "";
  width: calc(100% - 100px);
  border-bottom: 1px solid #006ccd;
  display: block;
  position: absolute;
  right: 0;
  bottom: 4px;
}
.lg-xw-con-right-xw-time > font {
  font-size: 16px;
}
.lg-xw-con-right-xw-title {
  font-weight: 400;
  font-size: 18px;
  line-height: 27px;
  color: #006ccd;
  margin: 10px 0 10px;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  width: 100%;
}
.lg-xw-con-right-xw-con {
  font-weight: 400;
  font-size: 16px;
  color: #667280;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.lg-xw-con-right-xw-t-t {
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 18px;
  color: #102842;
  /* display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden; */
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}
.lg-xw-con-right-xw-t-d {
  font-weight: 400;
  font-size: 16px;
  color: #667280;
}
/* 通知公告 */
.tz-gg {
  padding-top: 40px;
  overflow: hidden;
  margin-bottom: -5px;
}
.tg-kp {
  width: 380px;
}
.tg-kp-bg {
  width: 380px;
  height: 128px;
  padding: 30px 20px;
  font-weight: 400;
  font-size: 18px;
  color: #102842;
  position: relative;
}
.tg-kp-bg img {
  position: absolute;
  left: 0;
  top: 0;
}
.tg-kp-bg font {
  font-family: PingFang SC, PingFang SC;
  position: absolute;
  font-size: 80px;
  right: 10px;
  top: -10px;
  color: #deebf6;
}
.tg-kp-title {
  font-weight: 400;
  font-size: 18px;
  color: #102842;
  text-align: center;
  margin-top: 10px;
}
.tg-kp-check .tg-kp-title {
  color: #006ccd;
}
.tg-kp-check .tg-kp-bg {
  color: #ffffff;
}
.tg-kp-check .tg-kp-bg font {
  color: #197ad1;
}
.tg-kp .pr {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.tzgg-hr {
  align-items: center;
  position: relative;
}
.tzgg-hr::after {
  content: "";
  border: 1px solid #eaeaea;
  width: 200%;
  position: absolute;
  bottom: -6px;
  z-index: -1;
  left: -50%;
}
.tzgg-hr .tzgg-hr-left,
.tzgg-hr-right {
  border: 1px solid #eaeaea;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  color: #999999;
  text-align: center;
  font-size: 16px;
  position: absolute;
  top: 10px;
  background: #fff;
}
.tzgg-hr .tzgg-hr-left:hover,
.tzgg-hr-right:hover {
  color: #006ccd;
}
.tzgg-hr .tzgg-hr-right {
  right: 0;
}
.tzgg-hr > div {
  position: relative;
  width: 380px;
}
.tzgg-hr > div::after {
  content: "";
  border: 1px solid #eaeaea;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  left: calc(50% - 4px);
  background: #fff;
  transform: scale(0.5);
  top: -4px;
}

.tzgg-hr > .tzgg-hr-point::after {
  border-color: #006ccd;
}
/* 热门项目 */
.rm-xm {
  background: #eff5fa;
  height: 616px;
  padding: 50px;
  background-image: url(../img/index/rmxm.png);
  background-size: 100% 366px;
  background-repeat: no-repeat;
  background-position-y: bottom;
}
.rmxm-con {
  margin-top: 20px;
}
.rmxm-con .rmxm-con-left {
  width: 160px;
  height: 442px;
}
.rmxm-con .rmxm-con-right {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% - 160px);
}
.rmxm-con-right-bk {
  width: 233px;
  height: 208px;
  margin-left: 27px;
  margin-bottom: 27px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(1, 88, 167, 0.15);
  border-radius: 10px 10px 10px 10px;
  cursor: pointer;
}
.rmxm-con-right-bk-title {
  margin: 15px;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 推荐专业 */
.tj-zy {
  padding: 50px 50px 23px;
}

.tjzy-con {
  margin-top: 20px;
}
.tjzy-con .tjzy-con-right {
  width: 160px;
  height: 442px;
}
.tjzy-con .tjzy-con-left {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% - 160px);
}
.tjzy-con-left-bk {
  width: 233px;
  height: 208px;
  margin-right: 27px;
  margin-bottom: 27px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(1, 88, 167, 0.15);
  border-radius: 10px 10px 10px 10px;
  cursor: pointer;
}
.tjzy-con-left-bk-title {
  margin: 15px;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  position: relative;
  height: 43px;
}
.tjzy-con-left-bk-cc {
  width: 49px;
  height: 22px;
  background: #fff1d9;
  border-radius: 4px 4px 4px 4px;
  font-weight: 400;
  font-size: 13px;
  line-height: 22px;
  color: #ffa000;
  text-align: center;
  position: absolute;
  right: 0;
  bottom: 0;
}
/* 校园风光 */
.xy-fg {
  background: #f2f5fa;
  /* height: 512px; */
  padding: 50px;
  background-image: url(../img/index/xyfg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position-y: bottom;
}
.xyfg-con {
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}
.xyfg-con-bk {
  width: 227px;
  height: 305px;
  position: relative;
  margin: 0 32px;
  transition: all 1s ease;
}
.xyfg-con-bk img {
  /* position: absolute; */
}
.xyfg-con-bk div {
  position: absolute;
  bottom: 25px;
  width: 100%;
  text-align: center;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
}
.xyfg-scroll {
  position: relative;
  white-space: nowrap;
  display: flex;
  z-index: 2;
  padding: 0 18px;
}

.xyfg-con-b {
  width: 100%;
  height: 40px;
  background: linear-gradient(270deg, #0054a0 0%, #006ccd 100%);
  border-radius: 10px 10px 10px 10px;
  position: absolute;
  bottom: 0;
}
/* 登录 */
.dioModel {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 33;
  background: rgba(0, 0, 0, 0.3);
  display: none;
}
.c-login {
  background: #ffffff;
  box-shadow: 0px 0px 23px 0px rgba(12, 138, 255, 0.11);
  border-radius: 10px 10px 10px 10px;
  width: 400px;
  height: 500px;
  min-width: 400px;
  overflow: hidden;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.c-l-t {
  font-weight: 900;
  font-size: 18px;
  color: #333333;
  display: flex;
  user-select: none;
}
.c-login-con {
  padding: 0 40px;
}
.c-l-t input {
  display: none;
}
.c-l-t label {
  width: 50%;
}
.c-l-t div {
  background: #f5f6f7;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-bottom: 1px solid #dedede;
}
.c-l-t input:checked + div {
  color: #00489b;
  background: none;
  border: none;
}
.c-login-title {
  font-weight: 900;
  font-size: 18px;
  color: #333333;
  text-align: center;
  padding-top: 40px;
}
.c-login .dlbox {
  background: #fafbff;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #ecedef;
  padding: 8px 20px;
  margin-top: 30px;
  display: flex;
  align-items: center;
}
.login_icon {
  width: 26px;
  height: 26px;
}
.c-login .input2 {
  border: none;
  outline: none;
  width: calc(100% - 30px);
  height: 30px;
  padding-left: 10px;
}

.zh {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.jzzh {
  display: flex;
  align-items: center;
  color: #999999;
}
.zh .jz {
  margin-top: 2px;
}
.jzzh input {
  margin-right: 6px;
}
.c-logi {
  background: #00489b;
  border-radius: 5px 5px 5px 5px;
  color: #ffffff;
  width: 100%;
  height: 40px;
  margin-top: 15px;
  border: none;
}
.wxlogin {
  background: #18a132;
  border-radius: 5px 5px 5px 5px;
  color: #ffffff;
  width: 100%;
  height: 40px;
  margin-top: 15px;
  border: none;
}
.l-tic {
  font-weight: 400;
  font-size: 13px;
  color: #888888;
  margin-top: 15px;
}
.l-tic-t {
  font-weight: 700;
  color: #555555;
}
.jy {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #e7e7e7;
  font-weight: 700;
  font-size: 13px;
  color: #555555;
}
.jy a {
  background-repeat: no-repeat;
  font-family: "Microsoft YaHei UI";
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  margin: 0 0 0 10px;
  padding: 0 0 0 25px;
  background-size: 14px;
}
.firefox_pic {
  background-image: url("../img/index/hf.png");
}
.google_pic {
  background-image: url("../img/index/gg.png");
}
.firefox_360 {
  background-image: url("../img/index/360.png");
}
.close{
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
  width: 20px;
  height: 20px;
}
.c-login a{
  color: #00489B;
}
.hovertx:hover{
  transition: all 0.2s ease-in-out;
    box-shadow: 0 5px 12px 0 rgba(87, 83, 83, 0.4);
    transform: translateY(-2px);
}