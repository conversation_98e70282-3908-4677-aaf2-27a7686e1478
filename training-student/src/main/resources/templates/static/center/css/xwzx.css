.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

ul li {
  list-style: none;
  margin: 0;
  padding: 0;
}

ol li {
  list-style: none;
}

ol,
ul {
  list-style: none outside none;
}

li {
  list-style-image: none;
}

a {
  text-decoration: none;
  outline: none;
}

body {
  color: #666;
  font-size: 12px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

* {
  margin: 0;
  padding: 0;
}

.mid {
  width: 1200px;
  margin: 0 auto;
}

.mt3 {
  margin-top: 30px !important;
}

.mt6 {
  margin-top: 60px !important;
}

.mt0 {
  margin-top: 0 !important;
}

.mb1 {
  margin-bottom: 10px !important;
}

.mb3 {
  margin-bottom: 30px !important;
}

.mr1 {
  margin-right: 10px !important;
}

.mr3 {
  margin-right: 30px !important;
}

.relative {
  position: relative;
}

.pb0 {
  padding-bottom: 0 !important;
}

.pb2 {
  padding-bottom: 20px !important;
}

.pb6 {
  padding-bottom: 60px !important;
}

.pt0 {
  padding-top: 0px !important;
}

.pt3 {
  padding-top: 30px !important;
}

.pt5 {
  padding-top: 50px !important;
}

.pt9 {
  padding-top: 70px;
}

.prl15 {
  padding: 0 15px !important;
}

/* ******************************** */
.bannerbox {
  height: 440px;
}

.swiper-banner {
  height: 440px;
  width: 100%;
}

.classify {
  background: #fff;
  padding: 10px 0;
}

.infoBox {
  background: url(../img/xwzx/background.png) no-repeat;
  background-size: cover;
  min-height: 500px;
}

.mianbao {
  padding: 20px 0;
}

.miaobaoa {
  display: block;
  float: left;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  line-height: 36px;
}

.miaobaosp {
  padding: 0 5px;
  float: left;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  line-height: 36px;
}

.site {
  float: left;
  font-size: 14px;
  line-height: 36px;
  font-weight: 400;
  color: #999999;
}

.infoLeft {
  width: 200px;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
}

.selectTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  background: url('../img/xwzx/Mask group (1).png') no-repeat;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
}

.titleTwo .selectTitle {
  background: url('../img/xwzx/Mask group (2).png') no-repeat;
}

.selectTitle img {
  width: 15px;
  margin-right: 10px;
}

.level_one {
  width: 100%;
  line-height: 60px;
  text-align: center;
  background-color: #E9EBED;
  border-radius: 0 0 0 10px;
}

.level_one .node_one {
  cursor: pointer;
}

.level_one>.on {
  background: linear-gradient(180deg, #0054A0 0%, #006CCD 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.level_one .node_one:hover {
  background: linear-gradient(180deg, #0054A0 0%, #006CCD 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.infoRight {
  width: calc(100% - 200px);
  padding: 30px 40px;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px 0px rgba(1, 88, 167, 0.15);
  border-radius: 0px 10px 10px 10px;
}

.infoRight .rightTitle {
  font-weight: 400;
  font-size: 18px;
  color: #102842;
  line-height: 25px;
  font-style: normal;
  text-transform: none;
  margin-bottom: 30px;
}

.infoRight .rightList {
  box-sizing: border-box;
}

.rightList div {
  margin-bottom: 20px;
}

.rightLista {
  display: block;
  float: left;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  font-style: normal;
  text-transform: none;
  line-height: 27px;
  max-width: 815px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rightLista:hover {
  background: linear-gradient(180deg, #0054A0 0%, #006CCD 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rightListdate {
  float: right;
  line-height: 27px;
  font-weight: 400;
  font-size: 16px;
  color: #667280;
  font-style: normal;
  text-transform: none;
}

.paperBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px
}

.pagination>li {
  display: inline;
}

.pagination>li>a,
.pagination>li>span {
  position: relative;
  float: left;
  width: 46px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  line-height: 35px;
  color: #102842;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #DEDEDE;
  border-radius: 5px;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0 5px;
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
  z-index: 2;
  color: #fff;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
  z-index: 3;
  color: #fff;
  cursor: default;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

/* 
.pagination>li:first-child>a:hover {
  color: #102842;
  background: #fff;
}
.pagination>li:last-child>a:hover {
  color: #102842;
  background: #fff;
} */

.pageJump {
  display: inline-block;
  padding-left: 0;
  margin: 20px 10px;
  border-radius: 4px;
  vertical-align: top;
}

.pageJump .jump_qw {
  font-size: 14 px;
  font-weight: 500;
  color: #102842;
  margin-right: 10px;
}

.pageJump .button,
.pageJump input {
  font-size: 16px;
  padding: 6px 12px;
  line-height: 1.3333333;
  color: #102842;
  text-decoration: none;
  background: #fff;
  border: 1px solid #DEDEDE;
  border-radius: 5px;
}

.pageJump .button {
  width: 60px;
  height: 34px;
  font-weight: 500;
  font-size: 14px;
  color: #102842;
}

.pageJump input {
  width: 46px;
  height: 34px;
}

.pageJumpinput:focus,
.pageJumpinput:hover {
  border-color: #0054A0;
}

.pageJump .button {
  cursor: pointer;
}

.pageJump .button:hover {
  color: #fff;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

.newsTitle {
  font-weight: 400;
  font-size: 24px;
  color: #102842;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 14px;
}

.newsTip {
  font-weight: 400;
  font-size: 14px;
  color: #667280;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 20px;
}

.newsLine {
  width: 100%;
  height: 0px;
  border: 1px solid #EDEDED;
  margin-bottom: 30px;
}

.newsInfo {
  font-weight: 400;
  font-size: 14px;
  color: #102842;
  line-height: 36px;
  font-style: normal;
  text-transform: none;
}