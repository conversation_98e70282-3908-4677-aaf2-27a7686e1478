.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

ul li {
  list-style: none;
  margin: 0;
  padding: 0;
}

ol li {
  list-style: none;
}

ol,
ul {
  list-style: none outside none;
}

li {
  list-style-image: none;
}

a {
  text-decoration: none;
  outline: none;
}

body {
  color: #666;
  font-size: 12px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

* {
  margin: 0;
  padding: 0;
}

.mid {
  width: 1200px;
  margin: 0 auto;
}

.mt3 {
  margin-top: 30px !important;
}

.mt6 {
  margin-top: 60px !important;
}

.mt0 {
  margin-top: 0 !important;
}

.mb1 {
  margin-bottom: 10px !important;
}

.mb3 {
  margin-bottom: 30px !important;
}

.mr1 {
  margin-right: 10px !important;
}

.mr3 {
  margin-right: 30px !important;
}

.relative {
  position: relative;
}

.pb0 {
  padding-bottom: 0 !important;
}

.pb2 {
  padding-bottom: 20px !important;
}

.pb6 {
  padding-bottom: 60px !important;
}

.pt0 {
  padding-top: 0px !important;
}

.pt3 {
  padding-top: 30px !important;
}

.pt5 {
  padding-top: 50px !important;
}

.pt9 {
  padding-top: 70px;
}

.prl15 {
  padding: 0 15px !important;
}

/* ******************************** */
.bannerbox {
  height: 440px;
}

.swiper-banner {
  height: 440px;
  width: 100%;
}

.contentTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 70px 0 30px 0;
}

.contentTitle span {
  font-weight: 400;
  font-size: 24px;
  color: #102842;
  line-height: 34px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin: 0 26px;
}

.contentTitle .contentTitleImg {
  width: 191px;
  height: 19px;
}

.contentIntroduction {
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  line-height: 30px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  text-indent: 2em;
  margin-bottom: 40px;
}

.contentDisplay {
  position: relative;
  height: 414px;
  width: 100%;
  margin-bottom: 30px;
}

.contentDisplay .longLine {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 576px;
}

.longLine .leftYellow {
  width: 13px;
  height: 19px;
  background: #FFA000;
  border-radius: 0px 0px 0px 0px;
}

.longLine .rightBlue {
  width: 553px;
  height: 19px;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
  border-radius: 0px 0px 0px 0px;
}

.contentDisplay .shortLine {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 79px;
}

.shortLine .leftBlue {
  width: 56px;
  height: 11px;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
  border-radius: 0px 0px 0px 0px;
}

.shortLine .rightYellow {
  width: 13px;
  height: 11px;
  background: #FFA000;
  border-radius: 0px 0px 0px 0px;
}

.contentDisplay .leftImg {
  position: absolute;
  left: 0;
  top: 0;
  width: 631px;
  height: 381px;
  z-index: 2;
}

.contentDisplay .rightContent {
  position: absolute;
  right: 0;
  top: 25px;
  width: 604px;
  height: 388px;
  background: #d9e9f7;
  z-index: 1;
  padding: 15px 34px 15px 50px;
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  line-height: 30px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  text-indent: 2em;
}

.bottomContent {
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  line-height: 28px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  text-indent: 2em;
  margin-bottom: 70px;
}

.contactUs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 120px;
}

.contactUs .leftInfo {
  width: 539px;
}

.contactUs .leftInfo .leftInfoTitle {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.contactUs .leftInfo .leftInfoTitle .leftYellow {
  width: 8px;
  height: 30px;
  background: #FFA000;
  border-radius: 0px 0px 0px 0px;
  margin-right: 10px;
}

.contactUs .leftInfo .leftInfoTitle .centerText {
  font-weight: 400;
  font-size: 30px;
  color: #102842;
  line-height: 24px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  margin-right: 10px;
}

.contactUs .leftInfo .leftInfoTitle .rightText {
  font-weight: 400;
  font-size: 30px;
  line-height: 24px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  color: #e5eef6;
}

.contactUs .leftInfo .contactUsLine {
  width: 100%;
  height: 0px;
  border: 1px solid #EDEDED;
  margin-bottom: 25px;
}

.contactUs .contentAddress {
  font-weight: 400;
  font-size: 16px;
  color: #102842;
  line-height: 24px;
  text-align: justify;
  font-style: normal;
  text-transform: none;
}


.contactUs .contentAddress .flexText {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.contactUs .contentAddress .flexText img {
  width: 24px;
  height: 24px;
  margin-right: 24px;
}

.contactUs .contentAddress .colorText {
  margin-bottom: 8px;
  margin-left: 48px;
}

.contactUs .contentAddress .colorText span {
  color: #667280;
}

.contactUs .contentAddress .maginContent {
  margin-top: 34px;
}

.contactUs .rightInfo {
  width: 638px;
  height: 312px;
}