.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: "";
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

ul li {
  list-style: none;
  margin: 0;
  padding: 0;
}

ol li {
  list-style: none;
}

ol,
ul {
  list-style: none outside none;
}

li {
  list-style-image: none;
}

a {
  text-decoration: none;
  outline: none;
}

body {
  color: #666;
  font-size: 12px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei",
    sans-serif;
}

* {
  margin: 0;
  padding: 0;
}

.mid {
  width: 1200px;
  margin: 0 auto;
}

.mt3 {
  margin-top: 30px !important;
}

.mt6 {
  margin-top: 60px !important;
}

.mt0 {
  margin-top: 0 !important;
}

.mb1 {
  margin-bottom: 10px !important;
}

.mb3 {
  margin-bottom: 30px !important;
}

.mr1 {
  margin-right: 10px !important;
}

.relative {
  position: relative;
}

.pb0 {
  padding-bottom: 0 !important;
}

.pb2 {
  padding-bottom: 20px !important;
}

.pb6 {
  padding-bottom: 60px !important;
}

.pt0 {
  padding-top: 0px !important;
}

.pt2 {
  padding-top: 20px !important;
}

.pt5 {
  padding-top: 50px !important;
}

.pt9 {
  padding-top: 70px;
}

.prl15 {
  padding: 0 15px !important;
}

/* ******************************** */
.bannerbox {
  height: 440px;
}

.swiper-banner {
  height: 440px;
  width: 100%;
}

.classify {
  background: #fff;
  padding: 10px 0;
}

.imagedrop {
  width: 300px;
  height: 550px;
  position: absolute;
  right: -350px;
  top: 0;
  background: url(../img/zkzy/imagedrop.png) no-repeat;
  z-index: -1;
}

.imagedrop2 {
  width: 300px;
  height: 550px;
  position: absolute;
  left: -350px;
  top: 200px;
  background: url(../img/zkzy/imagedrop.png) no-repeat;
  z-index: -1;
}

.mianbao {
  padding: 20px 0;
}

.miaobaoa {
  display: block;
  float: left;
  font-size: 18px;
  line-height: 20px;
  color: #999999;
  line-height: 36px;
}

.miaobaosp {
  padding: 0 5px;
  float: left;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  line-height: 36px;
}

.site {
  float: left;
  font-size: 18px;
  line-height: 36px;
  font-weight: 400;
  color: #999999;
}

.seach {
  position: relative;
}

.seachimg {
  width: 14px;
  position: absolute;
  top: 11px;
  left: 15px;
}

.seach input {
  width: 280px;
  height: 36px;
  border: 1px solid #d3d6d9;
  border-radius: 8px;
  padding: 0 70px 0 35px;
  outline: 1px solid #d3d6d9;
}

.seach button {
  position: absolute;
  right: 0;
  width: 60px;
  top: 0;
  height: 100%;
  border-radius: 5px;
  border: none;
  color: white;
  cursor: pointer;

  background: linear-gradient(270deg, #0054a0 0%, #006ccd 100%);
}

.saixuan {
  float: left;
  width: 100px;
  font-size: 18px;
  line-height: 24px;
  color: #102842;
  font-weight: 400;
}

.saicon {
  float: left;
  overflow: hidden;
  width: 1100px;
}

.leimg_on {
  display: block !important;
}

.le_on {
  height: 44px;
}

.saixuanimg {
  width: 15px;
}

.saicon li {
  float: left;
  padding: 0 10px;
  margin-bottom: 20px;
  border-radius: 5px;
  margin-right: 10px;
  font-size: 18px;
  line-height: 24px;
  cursor: pointer;
  font-weight: 500;
  color: #102842;
}

.saicon li:hover {
  color: #006ccd;
}

.saicon li.on {
  color: #006ccd;
}

.xiangmu_list {
  padding: 20px 0;
}

.pxcon {
  float: left;
  width: 280px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(1, 88, 167, 0.15);
  border-radius: 10px;
  cursor: pointer;
  margin-right: 25px;
  margin-bottom: 30px;
}

.xiangmu_list .pxcon:nth-child(4n + 4) {
  margin-right: 0;
}

.pxcon:hover {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 5px 12px 0 rgba(87, 83, 83, 0.4);
  transform: translateY(-2px);
}

.pxcon .pxtext {
  padding: 10px;
  height: 80px;
}

.pxtext .pxh3 {
  font-size: 18px;
  font-weight: 400;
  color: #102842;
  line-height: 24px;
  margin-bottom: 10px;
  width: 100%;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.pxtext .pxpbox {
  height: 22px;
}

.pxpbox .pxp {
  display: inline-block;
  border-radius: 4px;
  padding: 5px;
  font-weight: 400;
  font-size: 13px;
  color: #ffa000;
  background-color: #fff1d9;
  line-height: 12px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.pxcon .bgtu2 {
  width: 100%;
  height: 155px;
  border-radius: 10px 10px 0 0;
}

.paperBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px
}

.pagination>li {
  display: inline;
}

.pagination>li>a,
.pagination>li>span {
  position: relative;
  float: left;
  width: 46px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  line-height: 35px;
  color: #102842;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #DEDEDE;
  border-radius: 5px;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0 5px;
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
  z-index: 2;
  color: #fff;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
  z-index: 3;
  color: #fff;
  cursor: default;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

/* 
.pagination>li:first-child>a:hover {
  color: #102842;
  background: #fff;
}
.pagination>li:last-child>a:hover {
  color: #102842;
  background: #fff;
} */

.pageJump {
  display: inline-block;
  padding-left: 0;
  margin: 20px 10px;
  border-radius: 4px;
  vertical-align: top;
}

.pageJump .jump_qw {
  font-size: 14 px;
  font-weight: 500;
  color: #102842;
  margin-right: 10px;
}

.pageJump .button,
.pageJump input {
  font-size: 16px;
  padding: 6px 12px;
  line-height: 1.3333333;
  color: #102842;
  text-decoration: none;
  background: #fff;
  border: 1px solid #DEDEDE;
  border-radius: 5px;
}

.pageJump .button {
  width: 60px;
  height: 34px;
  font-weight: 500;
  font-size: 14px;
  color: #102842;
}

.pageJump input {
  width: 46px;
  height: 34px;
}

.pageJumpinput:focus,
.pageJumpinput:hover {
  border-color: #0054A0;
}

.pageJump .button {
  cursor: pointer;
}

.pageJump .button:hover {
  color: #fff;
  background: linear-gradient(270deg, #0054A0 0%, #006CCD 100%);
}

.zkzy-details {
  background: url(../img/zkzy/toptitle.png) no-repeat;
}

.top-box {
  height: 120px;
}

.details-top {
  background-color: #fff;
  padding: 40px;
  display: flex;
  justify-content: space-between;
  border-radius: 10px;
}

.details-logo {
  width: 400px;
  height: 225px;
}

.top-box {
  width: 720px;
  padding: 0 30px;
}

.top-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.top-title .title-name {
  width: 100%;
  font-weight: 400;
  font-size: 24px;
  color: #102842;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.top-title .title-tip {
  border-radius: 4px;
  padding: 5px;
  font-weight: 400;
  font-size: 13px;
  color: #ffa000;
  background-color: #fff1d9;
  line-height: 12px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.top-info {
  height: 180px;
  font-weight: 400;
  font-size: 16px;
  color: #667280;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 6;
  line-clamp: 6;
}

.details-bottom {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
}

.bottom-title {
  font-weight: 400;
  font-size: 20px;
  color: #102842;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.bottom-info {
}

.bottom-info table {
  border-collapse: separate; /* 使边框分开，以便看到圆角效果 */
  border-spacing: 0; /* 设置边框间距为0 */
  overflow: hidden; /* 防止内容溢出 */
  border: 1px solid #dbe0ee; /* 设置表格的边框颜色 */
  border-radius: 5px; /* 设置整个表格的圆角 */
  width: 100%; /* 设置表格宽度 */
  margin: auto; /* 居中表格 */
}

.bottom-info thead {
  font-size: 16px;
  color: #102842;
  line-height: 24px;
  font-style: normal;
  text-transform: none;
}

.bottom-info th,
.bottom-info td {
  padding: 20px 0;
  border: 1px solid #dbe0ee;
}

.xm-con {
  background: url(../img/pxxm/pxxm-bg.png) 100% 100%;
  background-position: 0 10%;
  padding-bottom: 20px;
}

.bmzx-form {
  margin: 20px 0;
}
.bmzx-form .form-label {
  font-weight: 500;
  font-size: 16px;
  color: #102842;
  width: 100px;
  display: inline-block;
  text-align: right;
}
.bmzx-form-h {
  padding: 20px;
}
.bmzx-form-o {
  display: flex;
}
.bmzx-form-o .bmzx-form-h {
  width: 50%;
}
.bmzx-form-o .bmzx-form-h input {
  width: 350px;
  height: 40px;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #999999 !important;
  margin-left: 10px;
  outline: none;
  padding: 10px;
}
.bmzx-form-h textarea {
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #999999 !important;
  resize: none;
  width: 930px;
  margin-left: 13px;
  padding: 10px;
  outline: none;
  height: 100px;
}
.bmzx-form-remark {
  display: flex;
}
.bmzx-form button {
  width: 130px;
  height: 40px;
  background: linear-gradient(270deg, #0054a0 0%, #006ccd 100%);
  border-radius: 5px 5px 5px 5px;
  text-align: center;
  margin: auto;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
}
.bmzx-form-btn{
  text-align: center;
}
