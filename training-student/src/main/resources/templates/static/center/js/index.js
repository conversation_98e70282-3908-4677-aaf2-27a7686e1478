// 轮播图
let index = 0; // 当前图片索引
const images = document.querySelectorAll(".carousel-images img"); // 获取所有图片元素
const totalImages = images.length; // 图片总数
const carousel = document.querySelector(".carousel-images"); // 获取轮播图容器
const carouselm = document.querySelectorAll(".carousel-m span");
let timeOut = null;
function updateCar() {
  carousel.style.transform = `translateX(-${index * 100}%)`; // 移动轮播图位置
  carouselm.forEach((e, i) => {
    e.className = i == index ? "carousel-m-check" : "";
  });
  setD();
}

function moveToNext() {
  index = (index + 1) % totalImages; // 更新索引，循环显示图片
  updateCar();
}
setD();
// 设置定时器自动轮播，例如每3秒切换一次图片
function setD() {
  if (timeOut) {
    clearTimeout(timeOut);
  }
  timeOut = setTimeout(moveToNext, 3000);
}
carouselm.forEach((e, i) => {
  e.addEventListener("click", function (event) {
    index = i;
    updateCar();
  });
});

// 校园风光

function scrollD() {
  const xyScroll = document.querySelector(".xyfg-scroll");
  const xybk = document.querySelector(".xyfg-con-bk");
  xybk.style.marginLeft='-259px'
  setTimeout(()=>{
    xyScroll.appendChild(xybk);
    xybk.style.marginLeft=''
  },1000)
}
setInterval(scrollD, 3000);

function closeModel(){
  document.getElementById('dioModel').style='display:none;'
}