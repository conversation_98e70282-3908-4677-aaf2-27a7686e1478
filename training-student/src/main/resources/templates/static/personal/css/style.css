@charset "utf-8";
.mid{
	width: 1200px;
	margin: 0 auto;
}
.head{
	background-color: #fff;
	height: 100px;
}
.logo{
	float: left;
	width: 300px;
	margin-top: 16px;
}
.search_box{
	float: left;
	margin-left: 200px;
	font-size: 0;
	margin-top: 26px;
	border: 1px solid #2f81ca;
	overflow: hidden;
}
.select_sou{
	display: inline-block;
	vertical-align: top;
	border: none;
	outline: none;
	width: 86px;
	height: 36px;	
	box-sizing: border-box;
	padding-left: 15px;
}
.input_sou{
	display: inline-block;
	vertical-align: top;
	border: none;
	border-left: 1px solid #2F81CA;
	height: 36px;
	line-height: 36px;
	outline: none;
	padding-left: 10px;
	width: 260px;
}
.sou_btn{
	background:#2F81CA url(../img/sou.png) no-repeat;
	background-size: 24px;
	background-position: center center;
	width: 40px;
	height: 36px;
	display: inline-block;
	vertical-align: top;
	border: none;
	outline: none;
	cursor: pointer;
}
.login_nav{
	float: right;
	margin-top: 26px;
}
.logina{
	display: inline-block;
	padding: 0 5px;
	line-height: 36px;
	font-size: 14px;
	color: #666;
}
.logina:hover{
	color: #2F81CA;
}
.banner_box{
	position: relative;
	width: 100%;
}
.indexnav{
	width: 100%;
	top: 0;
	position: absolute;
	height: 24px;
	background-color: rgba(27,91,178,0.9);
	padding: 13px 0;
	color: #FFFFFF;
	z-index: 10;
}
.nava{
	display: inline-block;
	vertical-align: middle;
	padding: 0 10px;
	border-radius: 3px;
	line-height: 24px;
	margin: 0 10px;
	color: #FFFFFF;
	font-size: 14px;
}
.nava:hover{
	color: #fdccb4;
}
.nava.on{
	color: #ff694a;
	background-color: #FFFFFF;
}
.banner{
	width: 100%;
}
.banner_about{	
	padding: 50px 0 30px 0;
	display: flex;
	flex-flow: row;
	justify-content:space-between;
}
.numli{
	width: 23%;
	background: linear-gradient(to bottom,#5899f3,#1b5bb2);
	box-sizing: border-box;
	border-radius: 6px;
	padding: 15px 0;
}
.numh2{
	color: #FFFFFF;
	font-weight: normal;
	text-align: center;
	line-height: 56px;
	font-size: 42px;
}
.numsp{
	font-size: 20px;
	margin-left: 5px;
}
.nump{
	text-align: center;
	font-size: 14px;
	color: #FFFFFF;
	line-height: 24px;
}
.section1{
	padding: 20px 0;
	overflow: hidden;
}
.indexnews{
	width: 800px;
	float: left;
	box-sizing: border-box;
	padding: 15px 20px;
	background-color: #f8f8f8;
}
.newsh2{
	float: left;
	width: 100px;
	height: 36px;
	position: relative;
	color: #333;
	font-size: 16px;
	text-align: center;
	line-height: 36px;
	margin-bottom: 10px;
	cursor: pointer;
}
.newsh2.on{
	background-color: #1b5bb2;
	color: #FFFFFF;
}
.newsh2.on::after{
	content: '';
	display: block;
	position: absolute;
	top: 36px;
	left: 42px;
	width:0;
	height:0;
	border-right:8px solid transparent;
	border-left:8px solid transparent;
	border-top:8px solid #1b5bb2;
}
.more{
	display: block;
	float: right;
	line-height: 36px;
	color: #1B5BB2;
	font-size: 16px;
}
.newslist{
	padding-top: 10px;
}
.newslist ul li{
	overflow: hidden;
}
.newsa{
	color: #333333;
	line-height: 42px;
	font-size: 14px;
	display: block;
	float: left;
	max-width: 670px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.newslist ul li:hover .newsa{
	color: #1B5BB2;
}
.date{
	float: right;
	color: #999;
	font-size: 14px;
	line-height: 42px;
}
.login_box{
	float: left;
	width: 378px;
	padding:0 1px;
	border: 1px solid #DDDDDD;
	margin-left: 20px;
	height: 332px;
	box-sizing: border-box;
}
.jsli{	
	background:linear-gradient(to bottom,#fff,#ededed);
	width: 33.33%;
	height: 76px;
	cursor: pointer;
}
.jslip1{
	background: url(../img/ico1a.png) no-repeat;
	background-size: 36px;
	background-position: center 10px;
	padding-top: 50px;
	line-height: 26px;
	text-align: center;
	font-size: 16px;
	color: #666;
}
.jslip2{
	background: url(../img/ico2a.png) no-repeat;
	background-size: 36px;
	background-position: center 10px;
	padding-top: 50px;
	line-height: 26px;
	text-align: center;
	font-size: 16px;
	color: #666;
}
.jslip3{
	background: url(../img/ico3a.png) no-repeat;
	background-size: 36px;
	background-position: center 10px;
	padding-top: 50px;
	line-height: 26px;
	text-align: center;
	font-size: 16px;
	color: #666;
}
.jsli.on{
	background: #FFFFFF;
}
.jsli.on .jslip1{
	background: url(../img/ico1.png) no-repeat;
	background-position: center 10px;
	background-size: 36px;
	color: #1B5BB2;
}
.jsli.on .jslip2{
	background: url(../img/ico2.png) no-repeat;
	background-position: center 10px;
	background-size: 36px;
	color: #1B5BB2;
}
.jsli.on .jslip3{
	background: url(../img/ico3.png) no-repeat;
	background-position: center 10px;
	background-size: 36px;
	color: #1B5BB2;
}
.dlbox{
	padding: 20px;
}
.inpcon{
	border: 1px solid #DDDDDD;
	overflow: hidden;
	border-radius: 3px;
	margin-bottom: 20px;
}
.inpico{
	display: block;
	float: left;
	width: 48px;
	height: 40px;
	background-color: #f3f3f3;
}
.icon{
	width: 26px;
	margin: 7px auto;
	display: block;
}
.input1{
	float: left;
	width: 284px;
	line-height: 40px;
	font-size: 14px;
	height: 40px;
	border: none;
	outline: none;
	box-sizing: border-box;
	padding-left: 10px;
}
.jzbox{
	float: left;
	line-height: 30px;
}
.inputcheck{
	display: inline-block;
	vertical-align: middle;
	margin-right: 5px;
	width: 16px;
	height: 16px;
	border: 1px solid #DDDDDD;
	margin-bottom: 3px;
}
.mibox{
	float: right;
}
.jia{
	color: #666666;
	font-size: 14px;
	line-height: 30px;
}
.jia:hover{
	color: #ff8200;
}
.dlbtn{
	width: 100%;
	height: 40px;
	border-radius: 3px;
	background-color: #FF8200;
	color: #FFFFFF;
	border: none;
	outline: none;
	cursor: pointer;
	margin-top: 20px;
}
.section2{
	background: url(../img/s2_bg.png) no-repeat;
	background-position: center center;
	background-size: cover;
	width: 100%;
	height: 602px;
}
.s2h2{
	font-size: 24px;
	color: #333333;
	text-align: center;
	line-height: 40px;
	padding-top: 15px;
	font-weight: normal;
}
.zs{
	display: block;
	width: 100px;
	margin: 15px auto;
}
.s2hp{
	font-size: 16px;
	line-height: 30px;
	text-align: center;
	color: #888;
}
.swiper-father{
    width:1280px;
    margin: 15px auto 0 auto;
    position:relative;
}
.swiper-father .swiper-button-prev{
	background: url(../img/left.png) no-repeat;
	background-size: cover;
	width: 40px;
	height: 80px;
	left: 0;
}
.swiper-father .swiper-button-next{
	background: url(../img/right.png) no-repeat;
	background-size: cover;
	width: 40px;
	height: 80px;
	right: 0;
}
.peixun{
	width: 1200px;
	margin: 0 auto;
}
.peixun .swiper-wrapper{
	float: left;
	width: 280px;
}
.peixun .swiper-slide{
	margin-right: 25px;
}
.pxcon{
	float: left;
	width: 280px;
	box-sizing: border-box;
	padding: 22px;
	background-color: #FFFFFF;
	margin-top: 15px;
}
.peixun .swiper-button-prev{
	left: -20px;
}
.peixun .swiper-button-next{
	right: -20px;
}
.pxh3{
	font-size: 16px;
	line-height: 30px;
	padding-top: 10px;
	text-align: left;
	font-weight: normal;
	color: #222;
	width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.pxp{
	font-size: 14px;
	text-align: left;
	line-height: 24px;
	color: #888;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.baoming{
	width: 170px;
	line-height: 40px;
	border-radius: 20px;
	display: block;
	margin: 15px auto 0 auto;
	text-align: center;
	font-size: 14px;
	color: #333333;
	background-color: #c8c8c8;
}
.pxcon:hover{
	box-shadow: 0px 0px 10px rgba(0,0,0,0.15);
	margin-top: 0;
}
.pxcon:hover .baoming{
	background-color: #fe5a01;
	color: #FFFFFF;
}
.scon{
	float: left;
	width: 280px;
	margin-right: 24px;
	box-sizing: border-box;
	padding: 22px 0 0 0;
	margin-top: 15px;
}
.shibox .scon:last-child{
	margin-right: 0;
}
.sbox{
	border: 1px solid #DDDDDD;
	background-color: #f0f0f0;
}
.sh3{
	color: #333333;
	font-size: 18px;
	padding: 20px;
	font-weight: normal;
}
.sh3sp{
	font-size: 14px;
	margin-left: 20px;
}
.shp{
	padding: 0 20px 20px 20px;
	font-size: 14px;
	line-height: 24px;
	height: 72px;
	overflow: hidden;
    text-overflow: ellipsis;
  	display: -webkit-box;
  	-webkit-line-clamp: 3;
  	line-clamp: 3;
  	-webkit-box-orient: vertical;
}
.ck{
	display: block;
	background-color: #FFFFFF;
	font-size: 14px;
	color: #FE5A01;
	padding-left: 20px;
	line-height: 40px;
}
.sbox:hover{
	background: linear-gradient(to right,#fb8b4e,#fc5a02);
	border: 1px solid #fe5a01;
}
.sbox:hover .sh3{
	color: #FFFFFF;
}
.sbox:hover .shp{
	color: #FFFFFF;
}
.shiwrap{
	padding-bottom: 40px;
}
.shiwrap>.swiper-pagination{
	width: 100px;
	right: 0!important;
	left: auto;
}
.shiwrap .swiper-pagination-bullet-active{
	background-color: #f4bc67;
}
.section4{
	width: 100%;
	margin-top: 20px;
}
.footer{
	background-color: #343745;
	padding: 20px;
	text-align: center;
}
.footh4{
	font-size: 18px;
	line-height: 30px;
	text-align: center;
	margin-bottom: 15px;
	color: #FFFFFF;
}
.footp{
	font-size: 14px;
	line-height: 30px;
	color: #ddd;
}
.footpa{
	color: #ddd;
}
.footpa:hover{
	color: #FFFFFF;
}
.othernav{
	width: 100%;
	height: 24px;
	background-color: #497cc1;
	padding: 13px 0;
	color: #FFFFFF;
	z-index: 10;
}
.mianbao{
	padding: 20px 0;
}
.indeximg{
	width: 18px;
	float: left;
	margin-bottom: 2px;
	margin-right: 5px;
}
.miaobaoa{
	display: block;
	float: left;
	font-size: 14px;
	line-height: 20px;
	color: #999;
}
.miaobaoa:hover{
	color: #1B5BB2;
}
.miaobaosp{
	padding: 0 5px;
	float: left;
	color: #999;
	font-size: 14px;
	line-height: 20px;
}
.site{
	float: left;
	font-size: 14px;
	line-height: 20px;
	color: #999;
}
.newsleft{
	float: left;
	width: 180px;
	margin-right: 20px;
	background-color: #f8f8f8;
}
.newlh1{
	font-size: 16px;
	line-height: 40px;
	color: #222222;
	text-align: center;
	border-bottom: 1px solid #DDDDDD;
}
.newsli{
	line-height: 180px;
	height: 40px;
	line-height: 42px;
	font-size: 14px;
	color: #666666;
	text-align: center;
	border-bottom: 1px solid #DDDDDD;
	cursor: pointer;
}
.newsli.on{
	background-color: #497cc1;
	color: #FFFFFF;
}
.newsright{
	float: left;
	width: 1000px;
}
.znewslist{
	padding: 10px;
	border-bottom: 1px solid #DDDDDD;
}
.newsimg{
	float: left;
	width: 215px;
	height: 126px;
	margin-right: 25px;
}
.rnewscon{
	width: 740px;
	float: left;
}
.rnewsh2{
	font-size: 16px;
	line-height: 36px;	
}
.rnewsa{
	color: #222222;
	display: block;
	float: left;
	max-width: 580px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.rnewsh2sp{
	display: inline-block;
	margin-left: 5px;
	background-color: #ff8200;
	width: 60px;
	line-height: 20px;
	font-size: 12px;
	font-weight: normal;
	border-radius: 3px;
	text-align: center;
	color: #FFFFFF;
}
.date1{
	float: right;
	color: #999;
	font-weight: normal;
	font-size: 14px;
	line-height: 36px;
}
.rnewsp{
	font-size: 14px;
	line-height: 24px;
	color: #666666;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 4;
	line-clamp: 4;
	-webkit-box-orient: vertical;
}
.znewslist:hover{
	background-color: #f5f5f5;
}
.znewslist:hover .rnewsa{
	color: #497cc1;
}
/*分页样式*/
.fenye{
	width: 100%;
	box-sizing: border-box;
	padding: 15px;
	background-color: #FFFFFF;
}
.ui-paging-container{color:#666;font-size: 12px;}
.ui-paging-container ul{overflow: hidden;text-align: center;}
.ui-paging-container ul,.ui-paging-container li{list-style: none;}
.ui-paging-container li{display: inline-block;padding:3px 8px;height: 24px;line-height: 16px;box-sizing: border-box;margin-left: 5px;color:#666;vertical-align: top;}
.ui-paging-container li.ui-pager{cursor: pointer; border:1px solid #ddd;border-radius: 2px;}
.ui-paging-container li.ui-pager:hover,.ui-paging-container li.focus{background-color: #497cc1;color:#FFFFFF;border:1px solid #497cc1;}
.ui-paging-container li.ui-paging-ellipse{border:none;}
.ui-paging-container li.ui-paging-toolbar{padding:0;}
.ui-paging-container li.ui-paging-toolbar select{height:24px;border:1px solid #ddd;color: #666;}
.ui-paging-container li.ui-paging-toolbar input{line-height: 22px; height:22px;padding:0;border:1px solid #ddd;text-align: center;width: 30px;margin:0 0 0 5px;vertical-align: top;}
.ui-paging-container li.ui-paging-toolbar a{vertical-align: middle; text-decoration: none;display: inline-block;height:22px;border:1px solid #ddd;vertical-align: top;border-radius: 2px;line-height: 22px;padding:0 3px;cursor: pointer;margin-left: 5px;color: #666;}
.ui-paging-container li.ui-pager-disabled,.ui-paging-container li.ui-pager-disabled:hover{background-color: #eee;;cursor: default;border:none;color:#aaa;}

.xwh2{
	text-align: center;
	line-height: 36px;
	font-size: 18px;
	color: #222222;
}
.xwhp{
	text-align: center;
	line-height: 30px;
	font-size: 12px;
	color: #999;
	padding-bottom: 10px;
	border-bottom: 1px solid #DDDDDD;
}
.news_content{
	padding: 10px;
	font-size: 14px;
	color: #666666;
	line-height: 24px;
}
.newp{
	font-size: 14px;
	color: #666666;
	line-height: 24px;
}
.newstu{
	margin: 10px auto;
	display: block;
}
.pad2{
	padding: 20px;
}
.fybtn1{
	display: block;
	width: 80px;
	line-height: 30px;
	border-radius: 4px;
	text-align: center;
	color: #FFFFFF;
	background-color: #1b5bb2;
	margin: 0 10px;
}
.fybtn2{
	display: block;
	width: 80px;
	line-height: 30px;
	border-radius: 4px;
	text-align: center;
	color: #FFFFFF;
	background-color: #ff8200;
	margin: 0 10px;
}
.saixuan{
	float: left;
	width: 70px;
	font-size: 14px;
	line-height: 24px;
	color: #666666;
}
.saicon{
	float: left;
	overflow: hidden;
	width: 1130px;
}
.saicon li{
	float: left;
	padding: 0 15px;
	margin-bottom: 8px;
	border-radius: 12px;
	margin-right: 15px;
	font-size: 14px;
	line-height: 24px;
	color: #999;
	cursor: pointer;
}
.saicon li:hover{
	color: #fff;
	background:linear-gradient(to right, #ffae12, #f07d17);
}
.saicon li.on{
	color: #fff;
	background:linear-gradient(to right, #ffae12, #f07d17);
}
.xiangmu_list{
	padding: 20px 0;
}
.xiangmu_list .pxcon{
	margin-right: 25px;
	margin-bottom: 10px;
	border: 1px solid #DDDDDD;
}
.xiangmu_list .pxcon:nth-child(4n+4){
	margin-right: 0;
}
.shizi_list .scon:nth-child(4n+4){
	margin-right: 0;
}
.shizi_con{
	padding: 20px;
	background-color: #235fae;
}
.shiimg{
	float: left;
	width: 280px;
	height: 210px;
}
.rshi{
	float: left;
	width: 920px;
}
.shih2{
	font-size: 24px;
	line-height: 30px;
	font-weight: normal;
	padding: 20px;
	color: #FFFFFF;
}
.shih2sp{
	display: inline-block;
	margin-left: 30px;
	font-size: 14px;
	color: #FFFFFF;
}
.ship{
	font-size: 16px;
	line-height: 24px;
	padding: 0 20px;
	color: #FFFFFF;
}
.zjh3{
	font-size: 16px;
	padding: 15px 0;
	line-height: 30px;
	color: #222222;
}
.pt0{
	padding-top: 0;
}
.kcbox{
	padding: 20px;
	box-sizing: border-box;
	box-shadow: 0px 0px 10px rgba(0,0,0,0.15);
	background-color: #FFFFFF;
}
.kct{
	float: left;
	width: 234px;
	height: 234px;
	margin-right: 20px;
}
.rkcbox{
	float: left;
	width: 906px;
}
.pxsm{
	font-size: 14px;
	line-height: 24px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	line-clamp: 3;
	-webkit-box-orient: vertical;
	color: #888888;
	padding: 5px 0;
	min-height: 72px;
	border-bottom: 1px solid #DDDDDD;
}
.kctable{
	width: 100%;
}
.kctable td{
	width: 50%;
	color: #888;
	font-size: 14px;
	line-height: 30px;
}
.kico{
	display: inline-block;
	vertical-align: top;
	width: 14px;
	padding: 6px;
	margin-bottom: 4px;
}
.bmt{
	color: #333333;
	font-size: 14px;
	line-height: 30px;
	display: inline-block;
	vertical-align: top;
}
.bmbtn{
	margin-top: 10px;
	width: 170px;
	height: 40px;
	display: block;
	border-radius: 20px;
	background-color: #fe5a01;
	color: #FFFFFF;
	text-align: center;
	font-size: 14px;
	color: #FFFFFF;
	line-height: 40px;
}
.mt1{
	margin-top: 25px;
}
.l_px{
	float: left;
	width: 890px;
}
.pxhd{
	background-color: #f5f5f5;
}
.pxhd ul li{
	float: left;
	cursor: pointer;
	font-size: 16px;
	line-height: 46px;
	border-top: 2px solid transparent;
	color: #222222;
	padding: 0 20px;
}
.pxhd ul li.on{
	background-color: #FFFFFF;
	color: #1b5bb2;
	border-top: 2px solid #1B5BB2;
}
.pxbd{
	padding: 20px;
}
.kctz{
	font-size: 14px;
	line-height: 24px;
	color: #666666;
}
.r_px{
	float: left;
	width: 280px;
	margin-left: 30px;
	border: 1px solid #DDDDDD;
	box-sizing: border-box;
}
.kch3{
	color: #222222;
	font-size: 16px;
	padding-left: 10px;
	line-height: 48px;
}
.rpx .scon{
	margin-right: 0;
}
.zh3{
	font-size: 16px;
	color: #222222;
	line-height: 36px;
	margin-bottom: 10px;
}
.kcxx_list{
	background-color: #f8f8f8;
	padding: 0 10px;
	margin-bottom: 15px;
}
.kcxxp{
	line-height: 40px;
	font-size: 14px;
	color: #333333;
	float: left;
	width: 750px;
}
.kcxx_list:hover{
	background-color: #f5f5f5;
}
.kcxx_list:hover .kcxxp{
	color: #1B5BB2;
}
.time{
	margin-left: 10px;
	color: #999999;
	font-size: 12px;
}
.bfbtn{
	background-color: #FE5A01;
	color: #FFFFFF;
	width: 80px;
	text-align: center;
	display: block;
	border-radius: 4px;
	line-height: 30px;
	margin: 5px 0;
	float: right;
}
.bfbtn1{
	background-color: #2F81CA;
	color: #FFFFFF;
	width: 80px;
	text-align: center;
	display: block;
	border-radius: 4px;
	line-height: 30px;
	margin: 5px 0;
	float: right;
}
.xyname{
	font-size: 14px;
	color: #1B5BB2;
	line-height: 30px;
}
.lyp{
	color: #666;
	font-size: 14px;
	line-height: 24px;
}
.lytime{
	color: #999999;
	font-size: 12px;
	line-height: 20px;
}
.lybox{
	padding: 10px;
	border-bottom: 1px solid #DDDDDD;
}
.lybox:hover{
	background-color: #F8F8F8;
}
.abouth2{
	font-size: 24px;
	color: #222222;
	font-weight: normal;
	text-align: center;
	line-height: 60px;
	padding: 10px 0;
}
.about1{
	float: left;
	width: 280px;
	margin-right: 20px;
}
.aboutrp{
	float: left;
	width: 900px;
	box-sizing: border-box;
	padding: 10px;
	font-size: 14px;
	color: #666666;
	line-height: 24px;
}
.about2{
	float: left;
	width: 640px;
	margin-right: 20px;
}
.aboutp2{
	float: left;
	width: 540px;
	box-sizing: border-box;
	font-size: 14px;
	line-height: 48px;
	color: #333333;
	padding: 10px;
}
.pb1{
	padding-bottom: 20px;
}
.shipin{
	float: left;
	width: 840px;
	height: 513px;
	cursor: pointer;
}
.sprbox{
	float: left;
	width: 300px;
	margin-left: 20px;
}
.kjh3{
	font-size: 18px;
	line-height: 30px;
	color: #222222;
	padding: 10px 0;
}
.kjsx{
	display: block;
	background-color: #ff8200;
	width: 80px;
	line-height: 24px;
	font-size: 12px;
	font-weight: normal;
	border-radius: 3px;
	text-align: center;
	color: #FFFFFF;
}
.pxpad{
	padding: 20px 0;
}
.zjsz{
	font-size: 14px;
	color: #333;
	line-height: 38px;
	padding: 5px 0;
}
.pxsm1{
	font-size: 14px;
	line-height: 24px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 8;
	line-clamp: 8;
	-webkit-box-orient: vertical;
	color: #888888;
	padding: 5px 0 10px 0;
	margin-bottom: 5px;
	min-height: 192px;
	border-bottom: 1px solid #DDDDDD;
}
.bmbtn1{
	margin-top: 5px;
	width: 170px;
	height: 40px;
	display: block;
	border-radius: 20px;
	background-color: #FF8200;
	color: #FFFFFF;
	text-align: center;
	font-size: 14px;
	color: #FFFFFF;
	line-height: 40px;
}
.pinbox{
	padding: 10px;
	border-bottom: 2px solid #ddd;
}
.text1{
	border: 1px solid #DDDDDD;
	width: 100%;
	box-sizing: border-box;
	height: 100px;
	padding: 10px;
}
.fbbtn{
	background-color: #FE5A01;
	color: #FFFFFF;
	width: 80px;
	text-align: center;
	display: block;
	border-radius: 4px;
	line-height: 30px;
	margin: 5px 0;
	float: right;
}
.bgtu{
	background: url(../img/tu1.png) no-repeat;
	background-size: 100% 100%;
	width: 234px;
	height: 234px;
	box-sizing: border-box;
	padding: 60px 15px;
	justify-content: center;
	display: flex;
	flex-flow:column;
}
.kctit{
	text-align: center;
	color: #FFFFFF;
	font-size: 18px;
	font-weight: bold;
	line-height:40px;
	overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.kcbg{
	background: url(../img/kcbg.png) no-repeat;
	background-size: 100% 100%;
	width: 280px;
	height: 155px;
	box-sizing: border-box;
	padding: 25px 15px;
	justify-content: center;
	display: flex;
	flex-flow:column;
}
.kctit1{
	text-align: center;
	color: #FFFFFF;
	font-size: 18px;
	font-weight: bold;
	line-height:40px;
	overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.kch1{
	font-size: 16px;
	color: #222222;
	line-height: 30px;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.prize{
	float: left;
	font-size: 14px;
	color: #FF694A;
	line-height: 24px;
}
.rbm{
	font-size: 14px;
	color: #999999;
	float: right;
	line-height: 24px;
}
.kccon{
	padding: 5px 10px;
	border: 1px solid #EEEEEE;
}
.mt2{
	margin-top: 5px;
}
.qybanner{
	width: 100%;
}
.anli{
	float: left;
	width: 180px;
	margin-right: 24px;
	margin-bottom: 25px;
}
.anli:nth-child(6n+6){
	margin-right: 0;
}
.anliimg{
	width: 180px;
	height: 180px;
	box-sizing: border-box;
	border: 1px solid #EEEEEE;
	border-radius: 6px;
}
.anlip{
	text-align: center;
	font-size: 14px;
	line-height: 36px;
	color: #222222;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.tu{
	width: 280px;
	height: 210px;
}
.buildbox{
	margin: 20px 0;
	box-shadow: 0px 0px 10px rgba(0,0,0,0.15);
	padding: 50px;
}
.buildimg{
	width: 420px;
	display: block;
	margin: 0 auto;
}
.buildp{
	font-size: 16px;
	text-align: center;
	color: #888888;
	line-height: 24px;
	margin-top: 30px;
}
.b_top{
	box-shadow: 1px 0px 10px rgba(0,0,0,0.15);
}
.zhucebox{
	width: 380px;
	margin: 0 auto;
}
.zccon{
	margin-bottom: 15px;
}
.inputzc{
	width: 380px;
	border: 1px solid #DDDDDD;
	border-radius: 4px;
	height: 40px;
	box-sizing: border-box;
	padding-left: 10px;
}
.inputzc1{
	width: 280px;
	border: 1px solid #DDDDDD;
	border-radius: 4px;
	height: 40px;
	box-sizing: border-box;
	padding-left: 10px;
}
.yzm{
	display: inline-block;
	vertical-align: middle;
	line-height: 40px;
	font-size: 14px;
	color: #FC5A02;
	width: 80px;
	margin-left: 15px;
}
.butnzc{
	background-color: #FC5A02;
	color: #FFFFFF;
	outline: none;
	border: none;
	border-radius: 6px;
	height: 40px;
	width: 380px;
	margin: 25px auto;
	display: block;
	cursor: pointer;
}
.zcoutbox{
	box-shadow: 1px 0px 20px rgba(0,0,0,0.1);
	width: 600px;
	margin: 40px auto;
	padding: 30px;
	border-radius: 10px;
}
/*个人中心*/
.ztop{
	width: 100%;
	height: 50px;
	background-color: #FFFFFF;
	position: fixed;
	box-shadow: 0px 0px 10px rgba(0,0,0,0.1);
	top: 0;
	z-index: 10;
}
.zwrap{
	padding-top: 50px;
}
.bgf5{
	background-color: #F5F5F5;
}
.zlogobox{
	float: left;
	display: block;
}
.zlogo{
	width: 170px;
	height: 44px;
	margin: 3px 0;
}
.znav{
	float: left;
	padding-left: 30px;
}
.znavli{
	float: left;
	display: block;
	margin: 0 10px;
	color: #333333;
	font-size: 14px;
	line-height: 50px;
}
.znavli.on{
	color: #0079FE;
}
.rztop{
	float: right;
}
.wenbox{
	display: inline-block;
	line-height: 50px;
	margin-right: 20px;
	color: #666;
}
.wen{
	width: 18px;
	height: 18px;
	display: inline-block;
	margin:16px 5px 16px 0;
}
.txtop{
	display: inline-block;
	position: relative;
}
.tx1{
	display: inline-block;
	width: 32px;
	height: 32px;
	margin: 9px 5px;
	border-radius: 50%;
}
.txname{
	display: inline-block;
	line-height: 50px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100px;
}
.xiala{
	width: 20px;
	height: 20px;
	margin: 15px 5px;
	cursor: pointer;
}
.rotate{
	transform: rotate(180deg);
	-webkit-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
}
.xlbox{
	position: absolute;
	top: 45px;
	box-shadow: 0px 1px 3px rgba(0,0,0,0.15);
	right: 0;
	width: 100px;
	box-sizing: border-box;
	padding: 8px 20px;
	background-color: #FFFFFF;
}
.xla{
	color: #333333;
	display: block;
	line-height: 30px;
}
.xla:hover{
	color: #0079FE;
}
.mianbao1{
	padding: 10px 0;
}
.zleft{
	float: left;
	width: 170px;
	background-color: #FFFFFF;
	min-height: calc(100vh - 90px);
	margin-right: 10px;
}
.txbox{
	margin: 20px;
	display: block;
}
.tximg{
	display: block;
	margin: 0 auto;
	width: 60px;
	height: 60px;
	border-radius: 50%;
}
.txp{
	font-size: 14px;
	text-align: center;
	line-height: 30px;
	color: #666;
}
.zleftli{
	display: block;
	color: #333333;
	line-height: 20px;
	margin: 15px 0;
	padding-left:30px;
	border-left: 3px solid transparent;
}
.zlisp{
	display: inline-block;
	vertical-align: top;
	background-color: #DDDDDD;
	width: 8px;
	height: 8px;
	margin:6px 10px;
}
.zleftli.on{
	color: #0079FE;
	border-left: 3px solid #0079FE;
}
.zleftli.on .zlisp{
	background-color: #0079FE;
}
.zright{
	float: left;
	width: 1020px;
}
.zcon{
	padding: 10px 15px;
	background-color: #FFFFFF;
	margin-bottom: 10px;
}
.zch3{
	font-size: 16px;
	color: #333333;
	font-weight: normal;
	line-height: 36px;
	padding-bottom: 10px;
}
.zpeix{
	padding: 10px 0;
	margin-bottom: 20px;
}
.zpeix:last-child{
	margin-bottom: 0;
}
.zpeileft{
	position: relative;
	float: left;
	width: 180px;
	height: 100px;
	margin:10px 20px 10px 0;
}
.peiximg{
	width: 100%;
	height: 100%;
}
.bianma{
	background-color: rgba(16, 141, 233, 0.6);
	color: #FFFFFF;
	position: absolute;
	top: 0;
	padding: 0 8px;
	left: 0;
	line-height: 20px;
}
.zpeimid{
	float: left;
}
.peih4{
	font-size: 14px;
	color: #333333;
	line-height: 36px;
}
.biaoq{
	display: inline-block;
	color: #0200FF;
	background-color: rgba(2, 0, 255, 0.2);
	line-height: 20px;
	margin: 8px;
	border-radius: 10px;
	padding: 0 10px;
	font-size: 12px;
	font-weight: normal;
}
.peijs{
	line-height: 30px;
	color: #999999;
}
.peisp{
	display: inline-block;
	margin-right: 20px;
}
.baom{
	display: block;
	padding-right: 30px;
	float: right;
	color: #0079FE;
	font-size: 14px;
	line-height: 36px;
}
.xuan{
	padding: 10px 0 15px 0;
	line-height: 20px;
	color: #333333;
}
.checkbox{
	display: inline-block;
	vertical-align: top;
	width: 14px;
	height: 14px;
	margin:3px 4px;
}
.kebox{
	border: 1px solid #DDDDDD;
	display: block;
	margin: 0 20px 20px 0;
	width: 232px;
	box-sizing: border-box;
	float: left;
	position: relative;
}
.kebox:nth-child(4n+4){
	margin-right: 0;
}
.keimg{
	width: 232px;
	height: 140px;
}
.checkbox1{
	position: absolute;
	top: 0;
	left: 0;
	width: 15px;
	height: 15px;
}
.kecon{
	padding: 10px 15px;
}
.keh4{
	font-size: 14px;
	color: #333333;
	line-height: 30px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
.kep{
	color: #999999;
	font-size: 14px;
	line-height: 24px;
}
.xuefen{
	float: left;
	font-size: 12px;
	color: #999999;
	line-height: 24px;
}
.rtoux{
	float: right;
	padding: 3px 0;
	text-align: right;
}
.rtximg{
	float: right;
	display: inline-block;
	position: relative;
	margin-right: -6px;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	border: 1px solid #F0F0F0;
	box-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}
.tczz{
	position: fixed;
	width: 100%;
	height: 100vh;
	top: 0;
	left: 0;
	z-index: 99;
	background-color: rgba(0,0,0,0.4);
}
.tccon{
	width: 240px;
	border-radius: 6px;
	overflow: hidden;
	background-color: #FFFFFF;
	margin: 150px auto 0 auto;
}
.tcp{
	padding:40px 30px;
	font-size: 14px;
	line-height: 24px;
}
.qd1{
	line-height: 36px;
	color: #999999;
	display: block;
	box-sizing: border-box;
	width: 50%;
	text-align: center;
	font-size: 14px;
	border-top: 1px solid #eee;
}
.qd2{
	line-height: 36px;
	color: #0079FE;
	display: block;
	box-sizing: border-box;
	width: 50%;
	text-align: center;
	font-size: 14px;
	border-top: 1px solid #eee;
	border-left: 1px solid #eee;
}
.jindubox{
	color: #999999;
	line-height: 24px;
}
.jdcon{
	display: inline-block;
	vertical-align: top;
	margin: 8px 5px;
	width: 360px;
	height: 8px;
	border-radius: 4px;
	background-color: #EDEDED;
	overflow: hidden;
}
.jdnum{
	background-color: rgb(88, 163, 247);
	height: 8px;
	border-radius: 4px;
}
.cjbox{
	padding-top: 20px;
	width:240px;
}
.fenshu{
	text-align: center;
	font-weight: normal;
	margin-bottom: 10px;
	font-size: 26px;
	color: rgba(47, 24, 255, 0.6);
	line-height: 40px;
}
.color1{
	color: rgba(24, 144, 255, 0.8);;
}
.cjbz{
	text-align: center;
}
.backlist{
	padding-right: 15px;
	display: block;
	color: #0079FE;
	font-size: 14px;
	line-height: 36px;
	text-align: right;
}
.qhbox{
	padding: 0 20px;
}
.qhli{
	padding: 0 15px;
	margin-right: 10px;
	cursor: pointer;
	line-height: 30px;
	display: block;
	color: #666;
	float: left;
	border-bottom: 2px solid transparent;
}
.qhli.on{
	color: #5899F3;
	border-bottom: 2px solid #5899F3;
}
.zkcbox{
	float: left;
	width: 720px;
}
.kecheng{
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: 10px;
	margin-bottom: 10px;
}
.paihbox{
	float: left;
	width: 290px;
	margin-left: 10px;
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: 10px;
}
.ketu{
	float: left;
	width: 60px;
	height: 60px;
	margin: 10px;
	background-color: #4b02e9;
	color: #FFFFFF;
	font-size: 36px;
	text-align: center;
	line-height: 60px;
	border-radius: 3px;
}
.kexx{
	float: left;
}
.keh3{
	font-size: 14px;
	color: #333333;
	line-height: 24px;
	padding-top: 10px;
}
.kep1{
	font-size: 12px;
	color: #999999;
	line-height: 20px;
}
 
.percentBox {
    width: 50px;
    height: 50px;
}
.pertxt{
	width: 50px;
    height: 50px;
    line-height: 50px;
    position: absolute;
    margin-top: -53px;
    text-align: center;
    color: #343a41;
    font-size: 14px;
    font-family: Arial;
}
.shou{
	margin-top: 10px;
	padding-right: 10px;
	cursor: pointer;
}
.mulu{
	padding: 10px 10px 10px 80px;
}
.zhangjie{
	font-size: 14px;
	color: #333333;
	font-weight: normal;
	line-height: 30px;
}
.zhe{
	float: right;
	width: 24px;
	margin:3px;
	cursor: pointer;
}
.zjcon{
	background-color: #fafafa;
	margin: 10px 0;
	display: block;
	border-bottom: 2px solid transparent;
	position: relative;
}
.kicon{
	float: left;
	width: 20px;
	height: 20px;
	margin: 5px;
}
.rkexx{
	width: calc(100% - 30px);
	box-sizing: border-box;
	padding-right: 10px;
	float: left;
}
.kexh4{
	font-weight: normal;
	color: #333333;
	line-height: 30px;
	font-size: 12px;
}
.kexsp{
	color: #999999;
}
.kexhr{
	float: right;
}
.kebz{
	font-size: 12px;
	color: #333333;
	line-height: 20px;
	margin-bottom: 10px;
}
.teacher{
	display: inline-block;
	width: 18px;
	height: 18px;
	margin-right: 5px;
}
.kecza{
	float: right;
	color: #0079FE;
}
.kejindu{
	display: none;
	width: 100%;
	height: 2px;
	background-color: #DDDDDD;
}
.kejinducon{
	background-color: #58a3f7;
	height: 2px;
}
.zjcon:hover{
	background-color: #f6faff;
	border-bottom: none;
}
.zjcon:hover .kejindu{
	display: block;
}
.kicon1{
	display: inline-block;
	width: 18px;
	height: 18px;
	margin: 6px 3px;
}
.cjh1{
	padding: 0 5px 15px 5px;
}
.cjh3{
	font-size: 14px;
	color: #333333;
	line-height: 24px;
	padding-left: 5px;
	font-weight: normal;
}
.cjp{
	color: #999999;
	font-size: 12px;
	line-height: 16px;
}
.shua{
	width: 24px;
	margin: 8px;
	cursor: pointer;
}
.tableph{
	width: 100%;
}
.tableph td{
	padding-bottom: 15px;
}
.tableph tr td:last-child{
	text-align: center;
}
.no{
	width: 20px;
	text-align: center;
	height: 20px;
	font-size:14px;
	color: #333333;
	line-height: 20px;
}
.txph{
	width: 36px;
	height: 36px;
	border-radius: 50%;
}
.phname{
	font-size: 14px;
	color: #333333;
	line-height: 20px;
	font-weight: normal;
}
.phdw{
	font-size: 12px;
	line-height: 18px;
	color: #999999;
}
.zycon{
	background-color: #FFFFFF;
	padding: 20px;
	margin-bottom: 10px;
}
.zyimg{
	float: left;
	width: 80px;
	height: 80px;
	margin: 10px;
}
.zyxx{
	float: left;
	width: 420px;
}
.zyxh3{
	font-size: 14px;
	color: #333333;
	line-height: 36px;
	padding-top: 5px;
}
.zyxp{
	font-size: 12px;
	color: #999999;
	line-height: 20px;
}
.zongfen{
	font-size: 14px;
	color: #666666;
	float: left;
	width: 100px;
	line-height: 100px;
	margin-right: 40px;
}
.zybtn{
	display: block;
	border: 1px solid #58A3F7;
	line-height: 30px;
	margin: 32px 10px;
	box-sizing: border-box;
	color: #58a3f7;
	text-align: center;
	width: 80px;
	border-radius: 4px;
}
.fsbtn{
	padding: 22px 10px;
	text-align: center;
	font-size: 36px;
	line-height: 50px;
	display: block;
}
.fsbtn span{
	font-size: 12px;
	display: inline-block;
	margin-left: 3px;
}
.jige{
	color: #44b549;
}
.bujige{
	color: #f64743;
}
.xwrap{
	padding: 20px 100px;
	background-color: #FFFFFF;
	position: relative;
}
.xxtx{
	width: 90px;
	height: 90px;
	border-radius: 50%;
	display: block;
	margin: 10px auto;
}
.txh2{
	font-size: 16px;
	color: #333333;
	line-height: 30px;
	text-align: center;
}
.xingbie{
	display: inline-block;
	width: 20px;
	margin: 5px;
}
.xh3{
	font-size: 14px;
	color: #333333;
	padding-top: 30px;
	line-height: 24px;
	padding-bottom: 10px;
}
.tablexx{
	width: 100%;
	padding: 10px 0;
}
.tablexx td{
	color: #666;
	height: 24px;
	vertical-align: top;
	padding-bottom: 15px;
}
.xico{
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-right: 8px;
}
.zp1{
	width: 120px;
}
.xxbtn{
	display: block;
	width: 80px;
	height: 28px;
	background-color: #0079FE;
	color: #FFFFFF;
	text-align: center;
	line-height: 28px;
	border-radius: 3px;
	float: right;
}
.xxxbtn{
	display: block;
	width: 80px;
	height: 28px;
	background-color: #0079FE;
	color: #FFFFFF;
	text-align: center;
	line-height: 28px;
	border-radius: 3px;
	float: center;
}

.inputx{
	width: 200px;
	height: 28px;
	border: 1px solid #DDDDDD;
	box-sizing: border-box;
	padding-left: 5px;
}
.inputx2{
	width: 608px;
	height: 28px;
	border: 1px solid #DDDDDD;
	box-sizing: border-box;
	padding-left: 5px;
}
.qian{
	position: absolute;
	right: 10px;
	top: 0;
	width: 70px;
	text-align: center;
	border-radius: 0 0 10px 10px;
	line-height: 20px;
	background-color: #DDDDDD;
	color: #333333;
}
.qian.over{
	background-color: #d7dcf6;
	color: #0079FE;
}
.kezt{
	display: inline-block;
	background-color: #dff0ff;
	color: #0079FE;
	margin: 2px 8px;
	padding: 0 5px;
	border-radius: 3px;
	font-weight: normal;
	line-height: 20px;
	font-size: 12px;
}
.ksp{
	display: inline-block;
	margin-right: 50px;
}
.zbwrap{
	background-color: #FFFFFF;
	padding: 10px;
}
.zbtith2{
	font-size: 18px;
	font-weight: normal;
	line-height: 36px;
	color: #333333;
}
.zbtitp{
	font-size: 12px;
	line-height: 24px;
	color: #999999;
}
.ptb10{
	padding: 10px 0;
}
.tx2{
	width: 40px;
	height: 40px;
	display: inline-block;
	margin: 10px;
}
.jsxx{
	display: inline-block;
	width: 160px;
}
.star{
	padding: 5px 0;
	cursor: pointer;
}
.star img{
	width: 16px;
}
.jsname{
	color: #333333;
	font-size: 12px;
	line-height: 30px;
	font-weight: normal;
}
.pingjia{
	display: block;
	border: 1px solid #DDDDDD;
	width: 70px;
	line-height: 24px;
	color: #666666;
	text-align: center;
	float: right;
	border-radius: 3px;
}
.wancp2{
	font-size: 1.3rem;
	text-align: center;
	line-height: 2.4rem;
}
.zhibo{
	position: relative;
	float: left;
	width: 750px;
	height: 500px;
}
.zhibofix{
	position: absolute;
	right: 0;
	top: 0;
}
.zhibo_menu{
	float: left;
	width: 250px;
	box-sizing: border-box;
	padding: 10px;
	height: 500px;
	background-color: #FAFAFA;
	overflow-y: auto;
}
.zbmenuh3{
	font-size: 14px;
	color: #333333;
	line-height: 30px;
	font-weight: normal;
}
.zbmenuh3 span{
	color: #999999;
	float: right;
	display: block;
	font-size: 12px;
}
.zbzjh3{
	font-size: 12px;
	line-height: 20px;
	font-weight: normal;
	padding: 6px 0;
	color: #333333;
}
.zbzjxl{
	float: right;
	cursor: pointer;
	width: 20px;
	height: 20px;
	margin: 0 2px;
}
.zjli{
	padding:5px 0 5px 20px;
	font-size: 12px;
	color: rgba(69, 74, 86, 0.65);
	line-height: 20px;
	cursor: pointer;
	border-bottom: 1px solid #EEEEEE;
}
.zjsp{
	display: block;
	float: right;
	color: rgba(69, 74, 86, 0.65);
}
.zjli.on{
	background-size: 14px;
	background-position:3px 8px;
	color: #0079FE;
}
.bjbtn{
	border: 1px solid #58A3F7;
	color: #0079FE;
	line-height: 24px;
	width: 80px;
	text-align: center;
	display: block;
	margin:10px 10px 10px 670px;
	border-radius: 3px;
}
.tab_zb{
	border-bottom: 1px solid #DDDDDD;
	margin-bottom: 10px;
}
.tab_zbli{
	float: left;
	padding: 0 20px;
	line-height: 30px;
	margin: 0 10px;
	cursor: pointer;
}
.tab_zbli.on{
	color: #0079FE;
	border-bottom: 1px solid #0079FE;
}
.zbcon1_left{
	width: 650px;
	float: left;
	padding:0 20px;
	box-sizing: border-box;
}
.jianh3{
	font-size: 14px;
	line-height: 30px;
	font-weight: normal;
	padding-left: 10px;
	color: #333333;
	padding-top: 20px;
}
.jianh3:first-child{
	padding-top: 0;
}
.kejieshao{
	float: left;
	width: calc(100% - 80px);
	box-sizing: border-box;
	padding: 10px;
	font-size: 12px;
	color: #999999;
	line-height: 20px;
}
.jstx2{
	float: left;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	margin: 10px;
	display: block;
}
.zbcon1_right{
	float: left;
	width: 350px;
	box-sizing: border-box;
	padding: 10px;
}
.shubox{
	padding: 10px;
	border: 1px solid #eee;
	margin-bottom: 20px;
}
.shuh3{
	font-size: 16px;
	font-weight: normal;
	color: #333333;
	line-height: 30px;
}
.shuh3a{
	float: right;
	display: block;
	font-size: 12px;
	color: #0079FE;
}
.shubox .tablexx td{
	padding-bottom: 5px;
	line-height: 16px;
}
.shubox .tablexx td .star{
	padding: 0;
}
.pbtn2{
	display: block;
	float: right;
	background-color: #0079FE;
	margin-right: 10px;
	color: #FFFFFF;
	line-height: 28px;
	padding: 0 15px;
	border-radius: 4px;
	font-size: 12px;
}
.pbtn1{
	display: block;
	float: right;
	background-color: #C8C8C8;
	margin-right: 10px;
	color: #FFFFFF;
	line-height: 28px;
	padding: 0 15px;
	border-radius: 4px;
	font-size: 12px;
}
.bjinput{
	float: left;
	width: calc(100% - 110px);
	margin-right: 10px;
	border: 1px solid #eee;
	box-sizing: border-box;
	padding-left: 8px;
	height: 40px;
	border-radius: 4px;
	background-color: #f4f5f7;
}
.bibtn{
	border: none;
	outline: none;
	cursor: pointer;
	width: 100px;
	height: 40px;
	background-color: #0079FE;
	text-align: center;
	line-height: 40px;
	border-radius: 4px;
	color: #FFFFFF;
}
.bijili{
	background-color: #fbfbfb;
	padding: 10px;
	margin-bottom: 15px;
}
.bjicon{
	width: 18px;
	margin:3px 6px;
	float: left;
}
.biji{
	width: calc(100% - 250px);
	float: left;
}
.bijip1{
	font-size: 14px;
	color: #333333;
	line-height: 24px;
}
.bijip2{
	font-size: 12px;
	color: #999999;
	line-height: 18px;
}
.biaoji{
	display: inline-block;
	width:14px;
	margin: 2px;
}
.bjtime{
	float: right;
	text-align: right;
	width: 220px;
	color: #999999;
	font-size: 12px;
	line-height: 24px;
}
.pingnum{
	color: #333333;
	font-size: 14px;
	line-height: 30px;
	padding-bottom: 15px;
}
.ptx1{
	width: 36px;
	height: 36px;
	border-radius: 50%;
	float: left;
	margin:2px 7px;
}
.bjinput1{
	width: calc(100% - 160px);
}
.pingli{
	margin-bottom: 20px;
}
.ptx2{
	width: 36px;
	height: 36px;
	border-radius: 50%;
	float: left;
	margin:7px;
}
.pingcon{
	margin-left: 10px;
	width: calc(100% - 60px);
	float: left;
}
.pingname{
	font-size: 12px;
	line-height: 18px;
	color: #333333;
}
.pingp1{
	font-size: 14px;
	color: #333333;
	line-height: 20px;
}
.pingtime{
	font-size: 12px;
	color: #999999;
	line-height: 20px;
}
.tih2{
	font-size: 16px;
	font-weight: normal;
	line-height: 30px;
	color: #333333;
}
.back{
	font-size: 12px;
	background-color: #0079FE;
	color: #FFFFFF;
	text-align: center;
	width: 60px;
	line-height: 24px;
	border-radius: 3px;
	display: block;
	float: right;
	margin: 3px;
}
.timup{
	font-size: 12px;
	color: #666666;
	line-height: 24px;
}
.tileft{
	float: left;
	width: 700px;
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: 10px 20px;
}
.tiright{
	float: left;
	width: 310px;
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: 10px;
	margin-left: 10px;
}
.titype{
	font-size: 14px;
	color: #333333;
	line-height: 24px;
}
.titype1{
	font-size: 14px;
	color: #333333;
	line-height: 24px;
	font-weight: normal;
}
.ticon{
	padding-bottom: 15px;
}
.timu{
	font-size: 12px;
	line-height: 20px;
	color: #333333;
	padding: 5px 0;
}
.tix1{
	display: block;
	padding-left: 5px;
	font-size: 12px;
	line-height: 30px;
	color: #666666;
}
.tix1 input[type=radio],.tix1 input[type=checkbox]{
	width: 14px;
	height: 14px;
	margin:7px 7px 3px 7px;
	display: inline-block;
	vertical-align: top;
}
.tix1 input[type=radio]{   
    background-color: transparent;  
    border: 0;  
    -webkit-appearance: none; 
    outline: 0 !important;  
}   
.tix1 input[type=radio]:after{  
    content: "";  
    display:block;  
    width: 14px;
	height: 14px;  
    border: 1px solid #C8C8C8;
    box-sizing:border-box;
    border-radius: 50%;  
}  
.tix1 input[type=radio]:checked:after{  
    background: url(../img/radio-check.png) no-repeat;
    background-size: 100% 100%;
    border: none;
}
.tix1 input[type=checkbox]{   
    background-color: transparent;  
    border: 0;  
    -webkit-appearance: none; 
    outline: 0 !important;  
}   
.tix1 input[type=checkbox]:after{  
    content: "";  
    display:block;  
    width: 14px;
	height: 14px;  
    border: 1px solid #C8C8C8;
    box-sizing:border-box;
    border-radius: 3px;  
}  
.tix1 input[type=checkbox]:checked:after{  
    background: url(../img/checkbox-check.png) no-repeat;
    background-size: 100% 100%;
    border: none;
}
.datiqu{
	border: 1px solid #DDDDDD;
	box-sizing: border-box;
	padding:5px 10px;
	height: 100px;
	width: 100%;
	resize: none;
	font-size: 12px;
	margin-top: 10px;
}
.tihao{
	width: 28px;
	height: 28px;
	text-align: center;
	line-height: 28px;
	color: #333333;
	border: 1px solid #DDDDDD;
	float: left;
	margin: 0 15px 10px 0;
}
.tihao.over{
	background-color: #0079FE;
	color: #FFFFFF;
}
.tihao.dui{
	background-color: #44B549;
	color: #FFFFFF;
}
.tihao.cuo{
	background-color: #F64743;
	color: #FFFFFF;
}
.fieldset{
	background-color: #F3F3F3;
	padding: 10px;
	border: 1px solid #C8C8C8;
	margin-bottom: 10px;
	line-height: 20px;
}
.legend{
	padding: 5px;
}
.fieldset .dui{
	color: #44B549;
}
.fieldset .cuo{
	color: #F64743;
}
.jxp{
	color: #999999;
}
.tjbtn{
	width: 100px;
	line-height: 30px;
	border-radius: 3px;
	text-align: center;
	display: block;
	background-color: #0079FE;
	color: #FFFFFF;
	margin: 10px auto;
}
.kstime{
	display: block;
	float: right;
	font-size: 16px;
	line-height: 30px;
	color: #0079FE;
}
.kstime img{
	display: inline-block;
	vertical-align: top;
	width: 26px;
	height: 26px;
	margin:2px;
}
.fli{
	width: 310px;
	height: 150px;
	box-sizing: border-box;
	padding: 10px 15px;
	float: left;
	margin-right: 25px;
}
.fli:nth-child(3n+3){
	margin-right: 0;
}
.flex1{
	background-color: #f1fbff;
	border-left: 6px solid #2ec3ff;
}
.flex2{
	background-color: #fffbf1;
	border-left: 6px solid #fdc73f;
}
.flex3{
	background-color: #f1f1ff;
	border-left: 6px solid #4646fd;
}
.saoma{
	float: right;
	display: block;
	color: #0079FE;
	font-size: 12px;
}
.mt3{
	margin-top: 10px;
}
.zsh1{
	font-size: 16px;
	color: #333333;
	line-height: 30px;
	padding-bottom: 10px;
}
.zsimg{
	width: 700px;
	height: 500px;
	display: block;
}
.zsli{
	width: 250px;
	box-sizing: border-box;
	padding: 10px;
	cursor: pointer;
}
.zsimg1{
	width: 230px;
	height: 164px;
	display: block;
}
.zsp1{
	color: #333333;
	line-height: 20px;
}
.pingbox{
	width: 700px;
}
.ptable{
	width: 100%;
}
.pr0{
	padding-right: 0;
}
.tccon1{
	width: 800px;
	border-radius: 4px;
	overflow: hidden;
	background-color: #FFFFFF;
	margin: 80px auto 0 auto;
	box-sizing: border-box;
	padding: 10px 20px;
	position: relative;
}
.close{
	position: absolute;
	right: 10px;
	top: 10px;
	width: 20px;
	height: 20px;
	cursor: pointer;
}
.ptch2{
	font-size: 12px;
	color: #333333;
	padding-top: 10px;
	line-height: 30px;
}
.dbli{
	float: left;
	width: 220px;
	height: 130px;
	box-sizing: border-box;
	padding: 15px;
	margin-right: 20px;
}
.dbh3{
	font-size: 16px;
	color:#FFFFFF;
	line-height: 40px;
	font-weight: normal;
}
.dbh2{
	font-size: 32px;
	color:#FFFFFF;
	line-height: 60px;
}
.dbli1{
	background: url(../img/bg1.png) no-repeat;
	background-size: 100% 100%;
}
.dbli2{
	background: url(../img/bg2.png) no-repeat;
	background-size: 100% 100%;
}
.dbli3{
	background: url(../img/bg3.png) no-repeat;
	background-size: 100% 100%;
}
.dbli4{
	background: url(../img/bg4.png) no-repeat;
	background-size: 100% 100%;
}
.fixtime{
	position: absolute;
	top: 0;
	left: 1px;
	width: 100%;
	background-color: rgba(0,0,0,0.5);
	color: #FFFFFF;
	font-size: 12px;
	line-height: 20px;
	box-sizing: border-box;
	padding-left: 10px;
}
.fixzt{
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: 1px 10px 1px 15px;
	line-height: 24px;
	border-radius: 15px 0 0 15px;
	position: absolute;
	right: 0;
	top: 112px;
	color: #666666;
	font-size: 12px;
}
.ztimg{
	display: inline-block;
	width: 14px;
	height: 14px;
	margin: 5px 2px 5px 0;
}
.xuea{
    float: right;
    display: block;
    font-size: 12px;
    line-height: 24px;
    color: #0079FE;
    cursor: pointer;
    position: relative;
}
.xueb{
    float: right;
    display: block;
    font-size: 12px;
    line-height: 24px;
    color: #999;
    position: relative;
}
.ewmbox{
	position: absolute;
	top: 26px;
	padding: 5px;
	right: -10px;
	background-color: #FFFFFF;
	box-shadow: 0px 0px 8px rgba(0,0,0,0.15);
}
.ewm{
	width: 60px;
	height: 60px;
}
.ellipse{
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	max-width: 140px;
}
.juanbox{
	border: 1px solid #DDDDDD;
	display: block;
	margin: 0 20px 20px 0;
	width: 220px;
	padding: 0 10px;
	box-sizing: border-box;
	float: left;
}
.kszt{
    float: right;
    display: block;
    font-size: 12px;
    line-height: 36px;
    color: #0079FE;
}
.kszt1{
    color: #AAAAAA;
}
.juanimg{
	display: block;
	margin: 15px auto 25px auto;
	width: 80px;
	height: 80px;
}
.kstp{
	line-height: 30px;
	color: #999;
	margin: 10px auto;
	text-align: center;
}
.kefile{
	line-height: 24px;
	color: #999999;
	font-size: 12px;
}
.file{
	display: inline-block;
	width: 16px;
	height: 16px;
	margin: 4px;
}

/*8.23*/
.liaobox{
	margin:5px 10px;
	height: 300px;
	overflow-y: auto;
	box-sizing: border-box;
	padding: 5px 10px;
	border: 1px solid #DDDDDD;
}
.myname{
	color: #44b549;
	line-height: 24px;
}
.qipao{
	display: inline-block;
	padding: 5px 10px;
	border-radius: 6px;
	color: #333;
	line-height: 18px;
	font-size: 12px;
	background-color: #e8e8e8;
	margin-bottom: 5px;
}
.username{
	color: #4f9afe;
	line-height: 24px;
}
.xitong{
	display: inline-block;
	background-color: #f0f0f0;
	border-radius: 6px;
	line-height: 14px;
	font-size: 10px;
	padding: 5px 10px;
	max-width: 90%;
	margin: 5px 0;
}
.liaocz{
	color: #FB6041;
	display: inline-block;
	margin: 0 5px;
}
.shubox2{
	border: 1px solid #DDDDDD;
	height: 90px;
	box-sizing: border-box;
	margin: 0 10px;
	padding: 5px;
	position: relative;
}
.shutext2{
	border:none;
	width: 96%;
	height: 40px;
	resize: none;
	outline: none;
}
.zbtn2{
	background-color: #0079FE;
	color: #FFFFFF;
	text-align: center;
	display: inline-block;
	vertical-align: top;
	font-size: 12px;
	margin: 0 10px 10px 0;
	line-height: 28px;
	width: 80px;
	border-radius: 4px;
}

.timu{
	font-size: 12px;
	line-height: 20px;
	color: #333333;
	padding: 5px 0;
	width: 100%;
}
.timu td{
	vertical-align: top;
	padding:0 5px;
}
.timu tr td:nth-child(1){
	width: 20px;
}
.tih{
	background-color: #007AFF;
	color: #FFFFFF;
	display: block;
	width: 20px;
	height: 20px;
	border: none;
	text-align: center;
	line-height: 20px;
	margin: 3px 0;
	border-radius: 3px;
}
.xuanxiang{
	width: 100%;
}
.xuanxiang td{
	vertical-align: top;
	padding:0 5px;
}
.xuanxiang tr td:nth-child(1){
	width: 20px;
	padding: 0;
	text-align: center;
}
.datiqu{
	width: 640px;
}
.tix1{
	padding-left: 0;
}

/*订单*/
.ddbox{
	padding: 10px;
}
.ddpd{
	padding: 20px 5px;
}
.ddh3{
	font-size: 16px;
	line-height: 24px;
	color: #333333;
	padding: 10px 5px;
	border-top: 1px dashed #DDDDDD;
}
.tabledd{
	background-color: #f8fbfe;
	border-collapse: collapse;
	border: 1px solid #dae4ea;
	width: 100%;
	margin: 15px 0;
}
.tabledd td{
	border-bottom: 1px solid #DAE4EA;
	font-size: 14px;
	height: 50px;
	padding: 5px 20px;
	color: #333333;
}
.zfbox{
	float: left;
	width: 233px;
	height: 65px;
	border: 1px solid #666;
	border-radius: 2px;
	margin: 20px 20px 20px 5px;
	cursor: pointer;
	padding: 13px 0;
	box-sizing: border-box;
}
.zfbox.check{
	border: 1px solid #368ee0;
	background: url(../img/check.png) no-repeat;
	background-position: right bottom;
	background-size: 20px 20px; 
}
.zficon{
	display: block;
	width: 138px;
	height: 38px;
	margin: 0 auto;
}
.ddsjh{
	font-size: 14px;
	color: #333333;
	font-weight: bold;
	line-height: 24px;
	margin: 5px;
}
.xg{
	display: inline-block;
	padding: 0 15px;
	border: 1px solid #368EE0;
	color: #008bd1;
	font-size: 14px;
	line-height: 24px;
	background-color: #f8fbfe;
	border-radius: 3px;
	margin: 0 20px;
}
.tabledd2{
	border-collapse: collapse;
	width: 100%;
}
.tabledd2 td{
	height: 50px;
	font-size: 14px;
	text-align: right;
	padding: 0 30px;
}
.tabledd2 tr td:nth-child(1){
	color: #999999;
}
.tabledd2 tr td:nth-child(2){
	color: #333;
}
.tabledd2 tr td.ddprize{
	font-size: 20px;
	font-weight: bold;
	color: #e14438!important;
}
.goumai{
	float: right;
	margin:20px 30px;
	background-color: #fd4d3f;
	border-radius: 4px;
	width: 120px;
	height: 45px;
	line-height: 45px;
	font-size: 18px;
	color: #FFFFFF;
	text-align: center;
	display: block;
}
.inphm{
	display: inline-block;
	line-height: 24px;
	height: 24px;
	font-size: 14px;
	width: 120px;
	padding-left: 5px;
	border: 1px solid #DDDDDD;
}
.zxh2 {
	font-size: 18px;
	color: #333333;
	line-height: 42px;
}
.readonly{
	border: none;
	outline: none;
}
.zftit{
	font-size: 14px;
	text-align: center;
	line-height: 40px;
}
.zftit span{
	color: #e14438;
	font-size: 20px;
	font-weight: bold;
}
.fukm{
	width: 150px;
	margin: 20px auto;
	display: block;
}
.zfbtn{
	display: block;
	width: 45%;
	line-height: 32px;
	border-radius: 4px;
	text-align: center;
	background-color: #0079FE;
	color: #FFFFFF;
	border: 1px solid #0079FE;
	font-size: 14px;
}
.zfbtn:first-child{
	color: #0079FE;
	background-color: #FFFFFF;
}

/*缴费*/
.zpeix{
	position: relative;
}
.jiaofei{
	position: absolute;
	right: 120px;
	width: 60px;
	height: 60px;
	top: 0px;
}
.color2{
	color: #FF8200;
}
/*修改密码*/
.tccon3{
	width: 550px;
	border-radius: 8px;
	overflow: hidden;
	background-color: #FFFFFF;
	margin: 80px auto 0 auto;
	box-sizing: border-box;
	padding: 20px 40px;
	position: relative;
}
.tch3{
	font-size: 18px;
	color: #333333;
	font-weight: bold;
	line-height: 40px;
	padding-bottom: 20px;
}
.close2{
	position: absolute;
	right: 30px;
	top: 20px;
	width: 20px;
	height: 20px;
	cursor: pointer;
}
.xgcon{
	padding:30px 40px;
}
.xgp1{
	font-size: 14px;
	line-height: 40px;
	color: #333333;
	width: 20%;
	float: left;
}
.inputxg{
	width: 80%;
	float: left;
	height: 40px;
	box-sizing: border-box;
	border: 1px solid #DDDDDD;
	border-radius: 5px;
	padding-left: 10px;
}
.xgbtn1{
	background-color: #2971F6;
	color: #FFFFFF;
	border: none;
	width: 120px;
	height: 40px;
	border-radius: 5px;
	margin: 20px;
	outline: none;
	font-size: 14px;
	cursor: pointer;
}
.xgbtn2{
	background-color: #FFFFFF;
	color: #2971F6;
	border: 1px solid #2971F6;
	width: 120px;
	height: 40px;
	border-radius: 5px;
	margin: 20px;
	outline: none;
	font-size: 14px;
	box-sizing: border-box;
	cursor: pointer;
}
.zright .lxlist {
	height: 85px;
	padding: 10px;
}
.zright .lximg {
	float: left;
	width: 150px;
	height: 85px;
	border-radius: 5px 5px 5px 5px;
	margin-right: 15px;
}
.zright .lxcon {
	float: left;
	max-width: 440px;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}
.zright .lxcon .lxconh3 {
	font-size: 16px;
	font-weight: bold;
	color: #333333;
	line-height: 19px;
}
.zright .lxcon .lxconxq {
	font-size: 14px;
	font-weight: 400;
	color: #666666;
}
.zright .lxcon .lxconsj {
	font-size: 14px;
	font-weight: 400;
	color: #666666;
	line-height: 16px;
}
.zright .lxbtn1 {
	background: #00795A;
	border-radius: 5px 5px 5px 5px;
	font-size: 14px;
	font-weight: 400;
	color: #FFFFFF;
	line-height: 28px;
	width: 74px;
	height: 28px;
	display: block;
	text-align: center;
	float: right;
	margin: 25px 0 0 0;
}
