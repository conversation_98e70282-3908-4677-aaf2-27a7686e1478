<!DOCTYPE html>
<html>
	<#include "pages/personal/common/top.html">
	<style>
		.timu,.timu p,.timu td,.timu td p,.timu span,.xuanxiang p,.xuanxiang td,.xuanxiang td p,.xuanxiang,.xuanxiang span{
			font-size: 12px!important;
			font-family: "微软雅黑",arial!important;
		}
		.finished {
			background-color:#0079FE;
			color:#FFFFFF
		}
	</style>
	<body class="bgf5">
		<#include "pages/personal/common/header.html">
		<div class="zwrap mid">
			<div class="mianbao1 clearfix">
				<div class="site">当前位置：</div>
				<a href="${request.contextPath}/personal/home" class="miaobaoa">个人中心</a>
				<span class="miaobaosp">></span> 
				<div id="ly2" class="site">我的学习</div>
			</div>
			<div class="clearfix">
				<#include "pages/personal/common/left.html">
				<div class="zright">
					<div class="zcon" style="position:relative">
						<h2 class="tih2">
							${paperMap.name}
						</h2>
						<#if paperMap.courseCode??><p class="timup">课程名称：${paperMap.courseCode}-${paperMap.courseName}</p></#if>
						<p class="timup">培训项目：${paperMap.serialNumber}-${paperMap.title}</p>
						<p class="timup">考试时间：${paperMap.startTime?string("yyyy-MM-dd HH:mm:ss")}～${paperMap.endTime?string("yyyy-MM-dd HH:mm:ss")}</p>
						<p class="timup">试卷总分：${paperMap.totalScore}分 &nbsp;&nbsp;及格分：${paperMap.passedScore}分</p>
						<#if paperMap.category!="PSZY"><p class="timup">考试时间：${paperMap.duration}分钟</p></#if>
						<#if examData.score??>
							<#if paperMap.category=="PSZY">
								<p class="timup">得分：${examData.score}</p>
							<#elseif paperMap.isGradePublish == 1>
								<p class="timup">得分：${examData.score}</p>
							<#else>
								<p class="timup">得分：待公布</p>
							</#if>
						<#else>
							<p class="timup">得分：批阅中...</p>
						</#if>
						<p class="timup" style="color:red;">*提示：您答错的题目会被标记为红色</p>
					</div>
					<div class="clearfix">
						<div class="tileft">
							<form method="post" id="form_paper_detail">
								<input type="hidden" name="paperId" value="${paperMap.id}" >
								<#list paper.sections as section>
									<#assign perScore=0 />
									<#assign totalScore=0 />
									<#list section.questions as question>
										<#if question_index==0><#assign perScore=question.score /></#if>
										<#assign totalScore=totalScore + question.score />
									</#list>
									<h2 class="titype">
										第${BaseUtil.toCNLowerNum(section_index+1)}题、${section.remark}
										<#if section.remark == "套题">
										（本题有${section.questions?size}大题，共${totalScore}分）
										</#if>
										<#if section.remark != "套题">
										（本大题共${section.questions?size}小题，每小题${perScore}分，共${totalScore}分）
										</#if>
									</h2>
									<!-- 小题区域 -->
									<div class="ticon">
										<#list section.questions as question>
											<!-- 小题模板 -->
											<#if question.type == "SINGLECHOICE">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<#list question.options as option>
																<tr style="width:100%">
																	<td style="width:60px"><input type="radio" class="qk-choice" value="${option.alisa}" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_${option_index}"/>${option.alisa}.</td>
																	<td><label for="${question.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																</tr>
															</#list>
													</table>
													<#if paperMap.isShowAnswer == "1">
														<table class="timu" style="color:green">
																	<tr>
																		<td>
																			标准答案:${question.answer}
																		</td>
																	</tr>
																	<tr>
																		<td>
																			试题解析:${question.resolve}
																		</td>
																	</tr>
														</table>
													</#if>
											<#elseif question.type == "MULTIPLECHOICE">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<#list question.options as option>
																<tr>
																	<td style="width:60px"><input type="checkbox" class="qk-choice" value="${option.alisa}" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_${option_index}"/>${option.alisa}.</td>
																	<td><label for="${question.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																</tr>
															</#list>
													</table>
													<#if paperMap.isShowAnswer == "1">
														<table class="timu" style="color:green">
																	<tr>
																		<td>
																			标准答案:${question.answer}
																		</td>
																	</tr>
																	<tr>
																		<td>
																			试题解析:${question.resolve}
																		</td>
																	</tr>
														</table>
													</#if>
											<#elseif question.type == "JUDGMENT">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<tr>
																<td style="width:60px"><input type="radio" class="qk-choice" value="Y" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_Y"/>A.</td>
																<td><label for="${question.id}_Y" style="cursor:pointer">正确</label></td>
															</tr>
															<tr>
																<td><input type="radio" class="qk-choice" value="N" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_N"/>B.</td>
																<td><label for="${question.id}_N" style="cursor:pointer">错误</label></td>
															</tr>
													</table>
													<#if paperMap.isShowAnswer == "1">
														<table class="timu" style="color:green">
																	<tr>
																		<td>
																			标准答案:${question.answer}
																		</td>
																	</tr>
																	<tr>
																		<td>
																			试题解析:${question.resolve}
																		</td>
																	</tr>
														</table>
													</#if>
											<#elseif question.type == "BLANKFILL">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}.</span></td>
															<td>${question.content?replace('\\[BlankArea+\\d\\]','&nbsp;<input type="text" style="width: 100px" data-qid="${question.id}" name="Q-${question.id}" class="qk-blank" />&nbsp;','r')}</td>
														</tr>
													</table>
													<#if paperMap.isShowAnswer == "1">
														<table class="timu" style="color:green">
																	<tr>
																		<td>
																			标准答案:${question.answer}
																		</td>
																	</tr>
																	<tr>
																		<td>
																			试题解析:${question.resolve}
																		</td>
																	</tr>
														</table>
													</#if>
											<#elseif question.type == "ESSAY" || question.type == "MCJST" || question.type == "LST" || question.type == "JDT">
													<table class="timu">
															<tr>
																<td><span class="tih">${question_index}</span></td>
																<td>${question.content}</td>
															</tr>
													</table>
													<table class="xuanxiang tix1">
														<tr>
															<td class="jianda" rowspan="2"><textarea class="datiqu qk-txt" name="Q-${question.id}" data-qid="${question.id}" placeholder="答题区"></textarea></td>
														</tr>
													</table>
													<h2 class="titype">附件列表</h2>
														<div class="ticon" style="border:1px dashed #ddd;padding-right:20px">
														<table class="xuanxiang tix1" id="card${question.id}">
																<#if cardList??>
																	<#list cardList as card>
																		<#if card.questionId == question.id>
																			<tr class="answer_card_file" id="c_${card.id}">
																				<td style="width:100%;text-align:left;">附件：${card.name}(<a href="${card.url}" target="_blank">预览</a>)</td>
																			</tr>
																		</#if>
																	</#list>
																</#if>
														</table>
													</div>
													<#if paperMap.isShowAnswer == "1">
														<table class="timu" style="color:green">
																	<tr>
																		<td>
																			标准答案:${question.answer}
																		</td>
																	</tr>
																	<tr>
																		<td>
																			试题解析:${question.resolve}
																		</td>
																	</tr>
														</table>
													</#if>
											<!-- 套题 -->
											<#elseif question.type == "TT">
													<#assign childrenScore=0 >
													<#list question.children as childQues>
														<#assign childrenScore=childrenScore + childQues.score />
													</#list>
													<table class="timu" id="quick-Q-${question.id}" style="text-align:left">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${(question.content?replace('<p>',''))?replace('</p>','')}&nbsp;(本题有${question.children?size }小题，共${childrenScore }分)</td>
														</tr>
													</table>
													<#if question.children??>
														<table class="tix1">
															<tr>
																<td style="padding-left:5px">
																	<#list question.children as children>
																		<input type="hidden" class="${question.id }" value="${children.id }" childrenType="${children.type }"/>
																		<!-- 循环套题下的子题 -->
																		<#if children.type == "SINGLECHOICE">
																			<table class="timu" style="text-align:left">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1" style="text-align:left">
																					<#list  children.options as option>
																						<tr>
																							<td style="width:60px"><input type="radio" class="qk-choice" value="${option.alisa}" data-qid="${children.id}" name="Q-${children.id}"  parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_${option_index}"/>${option.alisa}.</td>
																							<td><label for="${children.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																						</tr>
																					</#list>
																			</table>
																			<#if paperMap.isShowAnswer == "1">
																				<table class="timu" style="color:green;">
																							<tr>
																								<td>
																									标准答案:${children.answer}
																								</td>
																							</tr>
																							<tr>
																								<td>
																									试题解析:${children.resolve}
																								</td>
																							</tr>
																				</table>
																			</#if>
																		<#elseif children.type == "MULTIPLECHOICE">
																			<table class="timu" style="text-align:left">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																					<#list children.options as option>
																						<tr>
																							<td style="width:60px"><input type="checkbox" class="qk-choice" value="${option.alisa}" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_${option_index}"/>${option.alisa}.</td>
																							<td><label for="${children.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																						</tr>
																					</#list>
																			</table>
																			<#if paperMap.isShowAnswer == "1">
																				<table class="timu" style="color:green;text-align:left">
																							<tr>
																								<td>
																									标准答案:${children.answer}
																								</td>
																							</tr>
																							<tr>
																								<td>
																									试题解析:${children.resolve}
																								</td>
																							</tr>
																				</table>
																			</#if>
																		<#elseif children.type == "JUDGMENT">
																			<table class="timu" style="text-align:left">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1" style="text-align:left">
																					<tr>
																						<td style="width:60px"><input type="radio" class="qk-choice" value="Y" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_Y"/>A.</td>
																						<td><label for="${children.id}_Y" style="cursor:pointer">正确</label></td>
																					</tr>
																					<tr>
																						<td style="width:60px"><input type="radio" class="qk-choice" value="N" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_N"/>B.</td>
																						<td><label for="${children.id}_N" style="cursor:pointer">错误</label></td>
																					</tr>
																			</table>
																			<#if paperMap.isShowAnswer == "1">
																				<table class="timu" style="color:green;text-align:left">
																							<tr>
																								<td>
																									标准答案:${children.answer}
																								</td>
																							</tr>
																							<tr>
																								<td>
																									试题解析:${children.resolve}
																								</td>
																							</tr>
																				</table>
																			</#if>
																		<#elseif children.type == "BLANKFILL">
																			<table class="timu" style="text-align:left">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${((children.content?replace('\\[BlankArea+\\d\\]','&nbsp;<input type="text" style="width: 100px" data-qid="${children.id}" name="Q-${children.id}" class="qk-blank" parentId="${question.id }" childrenNumber="${question.children?size }" onkeyup="illume(this)" />&nbsp;','r'))?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<#if paperMap.isShowAnswer == "1">
																				<table class="timu" style="color:green;text-align:left">
																							<tr>
																								<td>
																									标准答案:${children.answer}
																								</td>
																							</tr>
																							<tr>
																								<td>
																									试题解析:${children.resolve}
																								</td>
																							</tr>
																				</table>
																			</#if>
																		<#elseif children.type == "ESSAY" || children.type == "MCJST" || children.type == "LST" || children.type == "JDT">
																			<table class="timu" style="text-align:left">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																				<tr>
																					<td class="jianda" rowspan="2"><textarea class="datiqu qk-txt" name="Q-${children.id}" data-qid="${children.id}" placeholder="答题区" parentId="${question.id }" childrenNumber="${question.children?size }" onkeyup="illume(this)"></textarea></td>
																				</tr>
																			</table>
																			<h2 class="titype" style="text-align:left">附件列表</h2>
																			<div class="ticon" style="border:1px dashed #ddd;text-align:left">
																						<table class="xuanxiang tix1" id="card${children.id}">
																								<#if cardList??>
																									<#list cardList as card>
																											<#if card.questionId == children.id>
																											<tr class="answer_card_file" id="c_${card.id}">
																												<td style="width:100%;text-align:left;">附件：${card.name}(<a href="${card.url}" target="_blank">预览</a>)</td>
																											</tr>
																											</#if>
																									</#list>
																								</#if>
																						</table>
																			</div>
																			<#if paperMap.isShowAnswer == "1">
																				<table class="timu" style="color:green;text-align:left">
																							<tr>
																								<td>
																									标准答案:${children.answer}
																								</td>
																							</tr>
																							<tr>
																								<td>
																									试题解析:${children.resolve}
																								</td>
																							</tr>
																				</table>
																			</#if>
																		</#if>
																	</#list>
																</td>
															</tr>
														</table>
													</#if>
											</#if>
										</#list>
									</div>
								</#list>
							</form>
							<#if paperMap.category=="PSZY"><a href="javascript:void(0)" onclick="redoPaper();" class="tjbtn">重做</a></#if>
						</div>
						<div class="tiright">
							<h2 class="titype">选项卡</h2>
							<#list paper.sections as section>
								<h3 class="titype1">${BaseUtil.toCNLowerNum(section_index+1)}、${section.remark}（每小题${section.rscore}分，共${section.questions?size}题）</h3>
								<div class="clearfix ptb10">
									<#list section.questions as question>
										<div class="tihao" onclick="xwUserPaper.moveToQuestion('${question.id}')" quickToMove="${question.id}" id="fast_${question.id}" style="cursor:pointer">${question_index+1}</div>
									</#list>
								</div>
							</#list>
							<#if paperMap.category=="PSZY"><div style="margin-top:10px"><a href="javascript:void(0)" onclick="redoPaper();" class="tjbtn">重做</a></div></#if>
						</div>
					</div>
				</div>
			</div>
		</div>
</body>
<script src="/comm/compressImg.js"></script>
<script>
var tm_paper_id = "${paperMap.id}";
var tm_student_id = "${Session['session-student-user'].user.id}";
$(function(){
	<#if paperMap.category=="PSZY">
		$("#wdlx").addClass("on");
		$("#ly2").html("我的练习")
	<#else>
		$("#xmks").addClass("on");
		$("#ly2").html("项目考试")
	</#if>
	
	$('input').attr("disabled","disabled")//将input元素设置为disabled
	xwUserPaper.initPaper();
	
	//将错题的题干标记为红色
	var scoreDetails = '${scoreDetail}';
	scoreDetails = JSON.parse(scoreDetails);
	for (var p in scoreDetails) {
		var v = scoreDetails[p];
		if(!v || v==0){
			var aqid = p.replace("Q-", "");
			$("#quick-Q-" + aqid).css({"color":"red"});
		}
	}
	
});
var xwUserPaper = {
		//试卷当前最新表单值,用于判断,试卷答案是否改变,改变了则自动保存,避免无效且太频繁的自动向后保存的请求
		formCurrentSerializeArray : '',
		initPaper : function(){
			xwUserPaper.loadLastFill();
		},
		loadLastFill : function(){
			//从数据库加载答题历史
			xwUserPaper.loadLastUnsubmitAnwser();
			<#list paper.sections as section>
				<#list section.questions as question>
					<#if question.children??>
						illume2('${question.id}','${question.children?size}');
					</#if>
				</#list>
			</#list>
		},
		fillAnswerToForm : function(initAnswerDatasStr){
			console.log('------initAnswerDatasStr:',initAnswerDatasStr);
			if(utils.isEmpty(initAnswerDatasStr)){
				return;
			}
			var cacheJson = JSON.parse(initAnswerDatasStr);
			$.each(cacheJson, function(idx, item){
				if(item["type"]=="blank"){
					$("input[name='"+item["name"]+"']").each(function(ii, iblank){
						$(this).val(item["value"].split("`")[ii]);
					});
				}else if(item["type"]=="choice"){
					$("input[name='"+item["name"]+"']").val(item["value"].split("`"));

				}else if(item["type"]=="essay"){
					$("textarea[name='"+item["name"]+"']").val(item["value"]);
				}

				try{
					var theqid = item["name"].replace("Q-","");
					if(item["type"]=="blank"){
						if(xw_checker_blanker_filled(item["name"])){
							$("#fast_"+theqid).addClass("finished");
						}
					}else{
						if(!utils.isEmpty(item["value"])){
							$("#fast_"+theqid).addClass("finished");
						}
					}
				}catch(e){}

			});
		},

		//加载上次未提交的答题记录
		loadLastUnsubmitAnwser : function(){
			<#if paperInitData?? && paperInitData.items??>
				this.fillAnswerToForm('${paperInitData.items}');
			</#if>
		},
		moveToQuestion : function(qid){
			var thetop = $("#quick-Q-" + qid).offset().top;
			$("html:not(:animated),body:not(:animated)").animate({ scrollTop: thetop}, 500);
		}
};

//填空题的输入判断
function xw_checker_blanker_filled(n){
	var len = $("input[name='"+n+"']").length;
	var mylen = 0;
	$("input[name='"+n+"']").each(function(){
		var chval = $(this).val();
		if(utils.isEmpty(chval)){

		}else{
			mylen ++;
		}
	});
	return len == mylen;
}

//套题点亮右侧导航
function illume(obj){
	var parentId = $(obj).attr("parentId");
	var childrenNumber = $(obj).attr("childrenNumber");
	illume2(parentId, childrenNumber);
}
//判断套题是否完成，如果已完成就点亮套题
function illume2(parentId, childrenNumber){
	if(!childrenNumber || childrenNumber == 0){
		return;
	}
	var num = 0;
	$("."+parentId).each(function(){
		var children_id = $(this).val();
		var children_type = $(this).attr("childrenType");
		if(children_type == 2){
			var boo = false;
			$("[name=Q-"+children_id+"]").each(function(){
				if($(this).prop('checked')){
					boo = true;
				}
			})
			if(boo == true){
				num ++;
			}
		}else if(children_type == 3){
			if($("input[name='Q-"+children_id+"']:checked").val()){
				num ++;
			}
		}else if(children_type == 4){
			var boo = true;
			$("[name=Q-"+children_id+"]").each(function(){
				if(!$(this).val()){
					boo = false;
				}
			})
			if(boo == true){
				num ++;
			}
		}else{
			if($("[name=Q-"+children_id+"]").val()){
				num ++;
			}
		}
  	});
	if(num == childrenNumber){
		$("#fast_"+parentId).css("color","#FFFFFF");
		$("#fast_"+parentId).css("background","#0079FE");
	}else{
		$("#fast_"+parentId).css("color","");
		$("#fast_"+parentId).css("background","");
	}
}

//试题重做
function redoPaper(){
	layer.confirm('是否确定要重做试卷？', {
		btn : [ '确定', '取消'],icon:3
	}, function () {
		layer.load();
		$.ajax({
			type: "POST",
			url: "${request.contextPath}/personal/paper/redoPaper",
			data: {"paperId":tm_paper_id},
			dataType: "json",
			success: function(ret){
				layer.closeAll();
				if(0 == ret["code"]){
					document.location.href = '${request.contextPath}/personal/paper/doPaper?paperId='+tm_paper_id;
				}else{
					layer.alert(ret.message, {shadeClose: true,icon: 0,time: 10000});
				}
			},
			error:function(){
				layer.closeAll();
				layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
			}
		});
	}, function(){
		layer.closeAll();
	});
}
</script>
</html>