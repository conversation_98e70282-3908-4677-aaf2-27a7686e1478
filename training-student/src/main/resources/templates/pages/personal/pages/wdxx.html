<!DOCTYPE html>
<html>
	<#include "pages/personal/common/top.html">
	<body class="bgf5">
		<#include "pages/personal/common/header.html">
		<div class="zwrap mid">
			<div class="mianbao1 clearfix">
				<div class="site">当前位置：</div>
				<a href="${request.contextPath}/personal/home" class="miaobaoa">个人中心</a>
				<span class="miaobaosp">></span> 
				<div class="site">我的学习</div>
			</div>
			<div class="clearfix">
				<#include "pages/personal/common/left.html">
				<div class="zright">
					<#if xmList?size = 0>
						<div style="background-color: #FFFFFF;min-height: calc(100vh - 190px);text-align: center;padding-top: 100px;">
							<img src="/personal/img/blank.png" >
						</div>
					</#if>
					<div class="zcon">
						<#list xmList as xm>
							<div class="zpeix clearfix">
								<div class="zpeileft">
									<#if xm.xct??>
										<img src="${xm.xct}" class="peiximg">
									<#else>
										<img src="/portal/original/img/xm_common.png" class="peiximg">
									</#if>
									<div class="bianma">${xm.serialNumber}</div>
								</div>
								<div class="zpeimid">
									<h4 class="peih4">${xm.title}</h4>
									<div class="peijs"><span class="peisp">项目分类：${xm.typeName}</span><span class="peisp">培训对象：${xm.trainees}</span></div>
									<div class="peijs">培训时间：${xm.startTime?string("yyyy-MM-dd HH:mm:ss")}～${xm.endTime?string("yyyy-MM-dd HH:mm:ss")}</div>
								</div>
								<#if xm.isunderway == 1>
									<#if xm.isOpenStudy == 1 && xm.openStudyType == 'XWD'>
										<a target="_blank" href="${xm.openStudyUrl}?phone=${xm.mobile}" title="开始学习" class="baom">开始学习</a>
									<#elseif xm.isOpenStudy == 1 && xm.openStudyType == 'SQKD'>
										<a title="开始学习" class="baom" onclick="toOpenStudy()">开始学习</a>
									<#else>
										<a target="_blank" href="${request.contextPath}/personal/wdxxDetails?xmId=${xm.xmId}" title="开始学习" class="baom">开始学习</a>
									</#if>
								<#else>
									<a href="javascript:void(0)" class="baom" style="color:#b3abab">已结束</a>
								</#if>
							</div>
						</#list>
					</div>
				</div>
			</div>
		</div>
		<script>
			$(function(){
				$("#wdxx").addClass("on");
			});

			function toOpenStudy() {
				layer.load();
				$.ajax({
					url: "${request.contextPath}/personal/openStudy",
					type: "POST",
					dataType: "json",
					success: function(data) {
						if(data.code == 0) {
							window.open(data)
						}
					},
					error:function(){
						layer.closeAll('loading');
						layer.alert("服务器异常，请刷新后重试", {shadeClose: true,icon: 0,time: 50000,end:function(){
							window.location.reload();
						}});
					}
				})
			}
		</script>
	</body>
</html>