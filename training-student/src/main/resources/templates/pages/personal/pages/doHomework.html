<!DOCTYPE html>
<html>
	<#include "pages/personal/common/top.html">
	<style>
		.timu,.timu p,.timu td,.timu td p,.timu span,.xuanxiang p,.xuanxiang td,.xuanxiang td p,.xuanxiang,.xuanxiang span{
			font-size: 12px!important;
			font-family: "微软雅黑",arial!important;
			text-align: left;
		}
		.finished {
			background-color:#0079FE;
			color:#FFFFFF
		}
	</style>
	<body class="bgf5">
		<#include "pages/personal/common/header.html">
		<div class="zwrap mid">
			<div class="mianbao1 clearfix">
				<div class="site">当前位置：</div>
				<a href="${request.contextPath}/personal/home" class="miaobaoa">个人中心</a>
				<span class="miaobaosp">></span> 
				<div class="site">我的学习</div>
			</div>
			<div class="clearfix">
				<#include "pages/personal/common/left.html">
				<div class="zright">
					<div class="zcon" style="position:relative">
						<h2 class="tih2">
							${paperMap.name}
						</h2>
						<#if paperMap.courseCode??><p class="timup">课程名称：${paperMap.courseCode}-${paperMap.courseName}</p></#if>
						<p class="timup">培训项目：${paperMap.serialNumber}-${paperMap.title}</p>
						<p class="timup">考试时间：${paperMap.startTime?string("yyyy-MM-dd HH:mm:ss")}～${paperMap.endTime?string("yyyy-MM-dd HH:mm:ss")}</p>
						<p class="timup">试卷总分：${paperMap.totalScore}分 &nbsp;&nbsp;及格分：${paperMap.passedScore}分</p>
						<#if paperMap.category!="PSZY"><p class="timup">考试时间：${paperMap.duration}分钟</p></#if>
						<!-- 摄像头位置 -->
						<#if paperMap.isOpenCamera == "1">
							<video id="cameraDiv" width="151" height="201" style="position:fixed;right:0px;top:100px;"></video>
							<canvas id="canvas" width="151" height="201"  style="border:1px solid #d3d3d3;" hidden="hidden"></canvas>
						</#if>
					</div>
					<div class="clearfix">
						<div class="tileft">
							<form method="post" id="form_paper_detail">
								<input type="hidden" name="paperId" value="${paperMap.id}" >
								<#list paper.sections as section>
									<#assign perScore=0 />
									<#assign totalScore=0 />
									<#list section.questions as question>
										<#if question_index==0><#assign perScore=question.score /></#if>
										<#assign totalScore=totalScore + question.score />
									</#list>
									<h2 class="titype">
										第${BaseUtil.toCNLowerNum(section_index+1)}题、${section.remark}
										<#if section.remark == "套题">
										（本题有${section.questions?size}大题，共${totalScore}分）
										</#if>
										<#if section.remark != "套题">
										（本大题共${section.questions?size}小题，每小题${perScore}分，共${totalScore}分）
										</#if>
									</h2>
									<!-- 小题区域 -->
									<div class="ticon">
										<#list section.questions as question>
											<!-- 小题模板 -->
											<#if question.type == "SINGLECHOICE">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<#list question.options as option>
																<tr>
																	<td style="width:60px"><input type="radio" class="qk-choice" value="${option.alisa}" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_${option_index}"/>${option.alisa}.</td>
																	<td><label for="${question.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																</tr>
															</#list>
													</table>
											<#elseif question.type == "MULTIPLECHOICE">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<#list question.options as option>
																<tr>
																	<td style="width:60px"><input type="checkbox" class="qk-choice" value="${option.alisa}" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_${option_index}"/>${option.alisa}.</td>
																	<td><label for="${question.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																</tr>
															</#list>
													</table>
											<#elseif question.type == "JUDGMENT">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${question.content}</td>
														</tr>
													</table>
													<table class="xuanxiang tix1">
															<tr>
																<td style="width:60px"><input type="radio" class="qk-choice" value="Y" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_Y"/>A.</td>
																<td><label for="${question.id}_Y" style="cursor:pointer">正确</label></td>
															</tr>
															<tr>
																<td><input type="radio" class="qk-choice" value="N" data-qid="${question.id}" name="Q-${question.id}" id="${question.id}_N"/>B.</td>
																<td><label for="${question.id}_N" style="cursor:pointer">错误</label></td>
															</tr>
													</table>
											<#elseif question.type == "BLANKFILL">
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}.</span></td>
															<td>${question.content?replace('\\[BlankArea+\\d\\]','&nbsp;<input type="text" style="width: 100px" data-qid="${question.id}" name="Q-${question.id}" class="qk-blank" />&nbsp;','r')}</td>
														</tr>
													</table>
											<#elseif question.type == "ESSAY" || question.type == "MCJST" || question.type == "LST" || question.type == "JDT">
													<#assign hasZgt=1 />
													<table class="timu">
															<tr>
																<td><span class="tih">${question_index+1}</span></td>
																<td>${question.content}</td>
															</tr>
													</table>
													<table class="xuanxiang tix1">
														<tr>
															<td class="jianda" rowspan="2"><textarea class="datiqu qk-txt" name="Q-${question.id}" data-qid="${question.id}" placeholder="答题区"></textarea></td>
														</tr>
													</table>
											<!-- 套题 -->
											<#elseif question.type == "TT">
													<#assign childrenScore=0 >
													<#list question.children as childQues>
														<#assign childrenScore=childrenScore + childQues.score />
													</#list>
													<table class="timu" id="quick-Q-${question.id}">
														<tr>
															<td><span class="tih">${question_index + 1}</span></td>
															<td>${(question.content?replace('<p>',''))?replace('</p>','')}&nbsp;(本题有${question.children?size }小题，共${childrenScore }分)</td>
														</tr>
													</table>
													<#if question.children??>
														<table class="xuanxiang tix1">
															<tr>
																<td style="padding-left:5px;width:100%;">
																	<#list question.children as children>
																		<input type="hidden" class="${question.id }" value="${children.id }" childrenType="${children.type }"/>
																		<!-- 循环套题下的子题 -->
																		<#if children.type == "SINGLECHOICE">
																			<table class="timu">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																					<#list  children.options as option>
																						<tr>
																							<td style="width:60px"><input type="radio" class="qk-choice" value="${option.alisa}" data-qid="${children.id}" name="Q-${children.id}"  parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_${option_index}"/>${option.alisa}.</td>
																							<td><label for="${children.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																						</tr>
																					</#list>
																			</table>
																		<#elseif children.type == "MULTIPLECHOICE">
																			<table class="timu">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																					<#list children.options as option>
																						<tr>
																							<td style="width:60px"><input type="checkbox" class="qk-choice" value="${option.alisa}" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_${option_index}"/>${option.alisa}.</td>
																							<td><label for="${children.id}_${option_index}" style="cursor:pointer">${option.text}</label></td>
																						</tr>
																					</#list>
																			</table>
																		<#elseif children.type == "JUDGMENT">
																			<table class="timu">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																					<tr>
																						<td style="width:60px"><input type="radio" class="qk-choice" value="Y" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_Y"/>A.</td>
																						<td><label for="${children.id}_Y" style="cursor:pointer">正确</label></td>
																					</tr>
																					<tr>
																						<td style="width:60px"><input type="radio" class="qk-choice" value="N" data-qid="${children.id}" name="Q-${children.id}" parentId="${question.id }" childrenNumber="${question.children?size }" onclick="illume(this)" id="${children.id}_N"/>B.</td>
																						<td><label for="${children.id}_N" style="cursor:pointer">错误</label></td>
																					</tr>
																			</table>
																		<#elseif children.type == "BLANKFILL">
																			<table class="timu">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${((children.content?replace('\\[BlankArea+\\d\\]','&nbsp;<input type="text" style="width: 100px" data-qid="${children.id}" name="Q-${children.id}" class="qk-blank" parentId="${question.id }" childrenNumber="${question.children?size }" onkeyup="illume(this)" />&nbsp;','r'))?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																		<#elseif children.type == "ESSAY" || children.type == "MCJST" || children.type == "LST" || children.type == "JDT">
																			<#assign hasZgt=1 />
																			<table class="timu">
																				<tr>
																					<td><span class="tih">${question_index+1}.${children_index + 1}</span></td>
																					<td>${(children.content?replace('<p>',''))?replace('</p>','')}&nbsp;(${children.score }分)</td>
																				</tr>
																			</table>
																			<table class="xuanxiang tix1">
																				<tr>
																					<td class="jianda" rowspan="2"><textarea class="datiqu qk-txt" name="Q-${children.id}" data-qid="${children.id}" placeholder="答题区" parentId="${question.id }" childrenNumber="${question.children?size }" onkeyup="illume(this)"></textarea></td>
																				</tr>
																			</table>
																		</#if>
																	</#list>
																</td>
															</tr>
														</table>
													</#if>
											</#if>
										</#list>
									</div>
								</#list>
							</form>
							<#if hasZgt?? && hasZgt == 1>
								<h2 class="titype">主观题答题卡上传区域（答案中请写清楚所属试题的题号、题干，支持上传图片和文档）</h2>
								<div class="ticon" style="border:1px dashed #ddd">
									<table class="timu">
										<tr>
											<td><button type="button" onclick="showUpload();">选择文件</button>
											<input type="file" onchange="selectedFile()" id="answerCardFile" style="display:none"></td>
										</tr>
									</table>
									<table class="xuanxiang tix1" id="card">
											<#if cardList??>
												<#list cardList as card>
													<tr class="answer_card_file" id="c_${card.id}">
														<td style="width:300px;text-align:left;">主观题答案附件${card_index+1}(<a href="${card.url}" target="_blank">下载</a>、&nbsp;<a href="javascript:deleteCard('${card.id}')" style="font-size:10px;">删除</a>)</td>
													</tr>
												</#list>
											</#if>
									</table>
								</div>
							</#if>
							<a href="javascript:void(0)" onclick="xwUserPaper.submitPaper();" class="tjbtn">提交试卷</a>
						</div>
						<div class="tiright">
							<h2>
								<table>
									<tr>
										<td style="valign:middle"><img src="/personal/img/kstime.png"></td>
										<td style="valign:middle"><span id="div_processor_timer">--:--:--</span></td>
									</tr>
								</table>
							</h2>
							<#if (SHORTEST_DELIVER_LEFT_TIME > 0)><div id="time2" style="color:red;margin: 10px 0px 0px 0px;">--:--</div></#if>
							<h2 class="titype">选项卡</h2>
							<#list paper.sections as section>
								<h3 class="titype1">${BaseUtil.toCNLowerNum(section_index+1)}、${section.remark}（每小题${section.rscore}分，共${section.questions?size}题）</h3>
								<div class="clearfix ptb10">
									<#list section.questions as question>
										<div class="tihao" onclick="xwUserPaper.moveToQuestion('${question.id}')" quickToMove="${question.id}" id="fast_${question.id}" style="cursor:pointer">${question_index+1}</div>
									</#list>
								</div>
							</#list>
							<div style="margin-top:10px"><a href="javascript:void(0)" onclick="xwUserPaper.submitPaper();" class="tjbtn">提交试卷</a></div>
						</div>
					</div>
				</div>
			</div>
		</div>
</body>
<script>
var tm_maxtime = 0;
var tm_timer = null;//考试剩余时间
var _time2 = ${SHORTEST_DELIVER_LEFT_TIME?c};//最短交卷时间
var _interval2 = null;
var tm_paper_id = "${paperMap.id}";
var tm_student_id = "${Session['session-student-user'].user.id}";
$(function(){
	$("#wdlx").addClass("on");
	<#if paperMap.isOpenCamera == '1'>
		layer.confirm('当前考试，需要开启电脑摄像头，是否继续？', {
			btn : [ '确定', '取消'],icon:3,
			cancel:function(index, layero){
				document.location.href = '${request.contextPath}/personal/wdxxDetails?xmId=${paperMap.xmId}';
	        }
		}, function () {
			layer.closeAll();
			openCamera();
		}, function(){
			document.location.href = '${request.contextPath}/personal/wdxxDetails?xmId=${paperMap.xmId}';
		});
		
	</#if>
	if(_time2 > 0){
		_interval2 = setInterval(function(){
			if(_time2 > 0){
        		$("#time2").html(tm_fn_formatSeconds(_time2) + "后可提交试卷");
            	--_time2;
        	}else{
        		$("#time2").html("");
        		clearInterval(_interval2);
        	}
		},1000);
	}
	
});
var xwUserPaper = {
		//试卷当前最新表单值,用于判断,试卷答案是否改变,改变了则自动保存,避免无效且太频繁的自动向后保存的请求
		formCurrentSerializeArray : '',
		initPaper : function(){
			var stimer = "--:--:--";
			$("#div_processor_timer").html(stimer);
			//绑定输入提示
			xwUserPaper.bindQuickTip();
			xwUserPaper.loadLastFill();
			//获取剩余时间(分钟)
			xwUserPaper.getLeftTime();
		},
		loadLastFill : function(){
			var cacheData = xwUserDataCache.getCache(tm_student_id, tm_paper_id);
			var dbTimestamp = parseFloat('${paperInitData.timestamp}');
			if(utils.isEmpty(cacheData)){
				//从数据库加载答题历史
				xwUserPaper.loadLastUnsubmitAnwser();
			}else if(0 == dbTimestamp){
				//自动加载本地缓存答题历史
				tmLoadUserPaperCache(tm_student_id, tm_paper_id);
			}else {
				var browserTimestamp = xwUserDataCache.getTCache();
				if (browserTimestamp > dbTimestamp) {
					tmLoadUserPaperCache(tm_student_id, tm_paper_id);
				} else {
					xwUserPaper.loadLastUnsubmitAnwser();
				}
			}
			xwUserPaper.formCurrentSerializeArray = getFormSerializeArrayJsonStr();

			<#list paper.sections as section>
				<#list section.questions as question>
					<#if question.children??>
						illume2('${question.id}','${question.children?size}');
					</#if>
				</#list>
			</#list>
		},
		fillAnswerToForm : function(initAnswerDatasStr){
			if(utils.isEmpty(initAnswerDatasStr)){
				return;
			}
			var cacheJson = JSON.parse(initAnswerDatasStr);
			$.each(cacheJson, function(idx, item){
				if(item["type"]=="blank"){
					$("input[name='"+item["name"]+"']").each(function(ii, iblank){
						$(this).val(item["value"].split("`")[ii]);
					});
				}else if(item["type"]=="choice"){
					$("input[name='"+item["name"]+"']").val(item["value"].split("`"));

				}else if(item["type"]=="essay"){
					$("textarea[name='"+item["name"]+"']").val(item["value"]);
				}

				try{
					var theqid = item["name"].replace("Q-","");
					if(item["type"]=="blank"){
						if(xw_checker_blanker_filled(item["name"])){
							$("#fast_"+theqid).addClass("finished");
						}
					}else{
						if(!utils.isEmpty(item["value"])){
							$("#fast_"+theqid).addClass("finished");
						}
					}
				}catch(e){}

			});
		},

		//加载上次未提交的答题记录
		loadLastUnsubmitAnwser : function(){
			<#if paperInitData?? && paperInitData.items??>
				this.fillAnswerToForm('${paperInitData.items}');
				xwUserDataCache.setCache(tm_student_id, tm_paper_id, '${paperInitData.items}');
			</#if>
		},

		submitPaper : function(){
			var formCurrentSerializeArrayNew = getFormSerializeArrayJsonStr();
			if(formCurrentSerializeArrayNew != xwUserPaper.formCurrentSerializeArray){
				xwUserPaper.formCurrentSerializeArray = formCurrentSerializeArrayNew;
				ajaxSubmit();
			}
			if(_time2 > 0){
				layer.alert("未到交卷时间，不能交卷！", {icon: 0,time: 5000,shadeClose: true});
				return;
			}
			layer.confirm('是否确定提交试卷？', {
						btn: ['确定','取消'],icon:3
					}, function(){
						var formcheck = xwUserPaper.checkPaperBeforeSubmit();
						if(formcheck){
							xwUserPaper.ajaxSubmitPaper();
						}else{
							window.setTimeout(function(){
								layer.confirm('试卷还未做完，是否确定提交？', {
											btn: ['确定','取消'],icon:3
										}, function(){
											xwUserPaper.ajaxSubmitPaper();
										}
								);
							},100);
						}
					}
			);
		},
		
		checkPaperBeforeSubmit : function(){
			var result = true;
			$("[quickToMove]").each(function(index,element){
				if(!$(element).hasClass("finished")){
					var qid = $(element).attr("quickToMove");
					xwUserPaper.moveToQuestion(qid);
					result = false;
					return false;
				}
			});
			return result;
		},
		
		ajaxSubmitPaper	:	function(){
			layer.closeAll();
			layer.msg('试卷提交中，请稍等...', {
				  icon: 16
				  ,shade: 0.4
			});
			$.ajax({
				type: "POST",
				url: "${request.contextPath}/personal/paper/submitPaper",
				data: $("#form_paper_detail").serialize(),
				dataType: "json",
				success: function(ret){
					layer.closeAll();
					if(0 == ret["code"]){
						xwUserDataCache.clsCache(tm_student_id, tm_paper_id);
						layer.alert("提交成功。", {shadeClose: true,icon: 1,time: 5000,end:function(){layer.load();window.location.href="${request.contextPath}/personal/wdlx";}});
					}else{
						layer.alert(ret.message, {shadeClose: true,icon: 0,time: 10000});
					}
				},
				error:function(){
					layer.closeAll();
					layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
				}
			});
		},

		moveToQuestion : function(qid){
			if($("#quick-Q-" + qid).offset()) {
				var thetop = $("#quick-Q-" + qid).offset().top;
				$("html:not(:animated),body:not(:animated)").animate({scrollTop: thetop}, 500);
			}
		},
		
		countDown : function(){
			var tm_msg;
			if(tm_maxtime > 0){

				tm_msg = ""+ tm_fn_formatSeconds(tm_maxtime) + "";
				//alert(tm_msg);
				if(tm_maxtime <= 30){
					tm_msg += "<font style='color:red'><b>试卷准备提交中...</b></font>";
				}

				$("#div_processor_timer").html(tm_msg);

				if(tm_maxtime == 5*60) {
					layer.alert('距离考试结束还有5分钟，请抓紧时间作答', {icon: 0,shadeClose: true,time: 10000});
				}
				--tm_maxtime;
			}else{
				clearInterval(tm_timer); 
				$("#div_processor_timer").html("<font style='color:red'><b>已到交卷时间</b></font>");
				xwUserPaper.ajaxSubmitPaper();
			}
		},

		getLeftTime : function(){
			$.ajax({
				type: "POST",
				url: "${request.contextPath}/personal/paper/getLeftTime",
				data: {"paperId":tm_paper_id, "t":Math.random()},
				dataType: "json",
				success: function(ret){
					if(0 == ret.code){
						tm_maxtime = parseInt(ret.data);
						tm_timer = setInterval(function(){xwUserPaper.countDown();},1000); 
					}else{
						layer.alert("获取考试剩余时间失败，请刷新后重试，如还有问题，请与管理员联系。", {icon: 0,shadeClose: true});
					}
				},
				error:function(){
					layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
				}
			}); 
		},

		bindQuickTip : function(){
			//选择题绑定
			$(".qk-choice").click(function(){
				var thename = $(this).attr("name");
				var theqid = $(this).data("qid");
				var chval = [];

				$.each($('input[name='+thename+']:checked'), function(idx, item){
					chval.push($(this).val());
				});
				chval = chval.join("`");

				if(utils.isEmpty(chval)){
					xwUserDataCache.delCache(tm_student_id, tm_paper_id, theqid);
					$("#fast_"+theqid).removeClass("finished");
				}else{
					//增加到本地缓存
					xwUserDataCache.addCache(tm_student_id, tm_paper_id, theqid, "choice", chval);
					$("#fast_"+theqid).addClass("finished");
				}

			});
			//填空题绑定
			$(".qk-blank").on("keyup mouseleave",function(){
				var thename = $(this).attr("name");
				var theqid = $(this).data("qid");
				var charrval = [];

				$.each($('input[name='+thename+']'), function(idx, item){
					charrval.push($(this).val());
				});
				
				charrval = charrval.join("`");

				if(xw_checker_blanker_filled(thename)){
					//增加到本地缓存
					xwUserDataCache.addCache(tm_student_id, tm_paper_id, theqid, "blank", charrval);
					$("#fast_"+theqid).addClass("finished");
				}else{
					xwUserDataCache.addCache(tm_student_id, tm_paper_id, theqid, "blank", charrval);
					$("#fast_"+theqid).removeClass("finished");
				}

			});
			//问答题绑定
			$(".qk-txt").on("keyup mouseleave",function(){
				var thename = $(this).attr("name");
				var theqid = $(this).data("qid");
				var chval = $(this).val();
				
				if(utils.isEmpty(chval)){
					xwUserDataCache.delCache(tm_student_id, tm_paper_id, theqid);
					$("#fast_"+theqid).removeClass("finished");
				}else{
					//增加到本地缓存
					xwUserDataCache.addCache(tm_student_id, tm_paper_id, theqid, "essay", chval);
					$("#fast_"+theqid).addClass("finished");
				}
			});
		}
};

//填空题的输入判断
function xw_checker_blanker_filled(n){
	var len = $("input[name='"+n+"']").length;
	var mylen = 0;
	$("input[name='"+n+"']").each(function(){
		var chval = $(this).val();
		if(utils.isEmpty(chval)){

		}else{
			mylen ++;
		}
	});
	return len == mylen;
}

//本地缓存操作
var xwUserDataCache = {
		timestampCachePrefixx : 'TRTime',
		support : function(){
			try{
				if(window.localStorage){
					return true;
				}else{
					return false;
				}
			}catch(e){
				return false;
			}

		},

		addCache : function(student_id, paper_id, qid, qtype, val){
			if(!xwUserDataCache.support()){
				return;
			}
			var cacheData = xwUserDataCache.getCache(student_id, paper_id);
			var cacheKey = "TRC" + student_id + paper_id;
			var cacheJson = [];

			try{
				if(!utils.isEmpty(cacheData)){
					cacheJson = JSON.parse(cacheData);
				}

				$(cacheJson).each(function(idx, item){
					var _name = "Q-" + qid;
					if(_name == item["name"]){
						cacheJson.splice(idx, 1);
						return false;
					}
				});
				cacheJson.push({"name" : "Q-" + qid, "type" : qtype, "value" : val});

				var strCacheData = JSON.stringify(cacheJson);
				localStorage.setItem(cacheKey, strCacheData);
				this.setCacheTimestamp();

			}catch(e){
				//BROWSER DOESN'T SUPPORTED
			}

		},

		getCache : function(student_id, paper_id){
			if(!xwUserDataCache.support()){
				return;
			}
			var cacheKey = "TRC" + student_id + paper_id;
			return localStorage.getItem(cacheKey);
		},
		
		setCache : function(student_id, paper_id,strCacheData){
			var cacheKey = "TRC" + student_id + paper_id;
			localStorage.setItem(cacheKey, strCacheData);
			this.setCacheTimestamp();
		},

		getTCache : function(){
			if(!xwUserDataCache.support()){
				return 0;
			}
			var cacheKey = this.timestampCachePrefixx + tm_student_id + tm_paper_id;
			return (localStorage.getItem(cacheKey) == null ? 0 : localStorage.getItem(cacheKey)) - 0;
		},

		delCache : function(student_id, paper_id, qid){
			if(!xwUserDataCache.support()){
				return;
			}
			var cacheData = xwUserDataCache.getCache(student_id, paper_id);
			var cacheKey = "TRC" + student_id + paper_id;
			var cacheJson = [];

			try{
				if(!utils.isEmpty(cacheData)){
					cacheJson = JSON.parse(cacheData);
				}

				$(cacheJson).each(function(idx, item){
					var _name = "Q-" + qid;
					if(_name == item["name"]){
						cacheJson.splice(idx, 1);
					}
				});
				var strCacheData = JSON.stringify(cacheJson);
				localStorage.setItem(cacheKey, strCacheData);
				this.setCacheTimestamp();
			}catch(e){
				//BROWSER DOESN'T SUPPORTED
			}
		},

		clsCache : function(student_id, paper_id){
			if(!xwUserDataCache.support()){
				return;
			}
			var cacheKey = "TRC" + student_id + paper_id;
			localStorage.removeItem(cacheKey);
			localStorage.removeItem(this.timestampCachePrefixx + student_id + paper_id);
		},
		//设置当前页面缓存秒级时间戳
		setCacheTimestamp : function(){
			var cacheKey = this.timestampCachePrefixx + tm_student_id + tm_paper_id;
			var cacheData = (new Date()).getTime();
			localStorage.setItem(cacheKey, cacheData);
		}
	};

	//加载本地缓存
	function tmLoadUserPaperCache(student_id, paper_id){
		var cacheData = xwUserDataCache.getCache(student_id, paper_id);
		try{
			xwUserPaper.fillAnswerToForm(cacheData);
		}catch(e){
			//BROWSER DOESN'T SUPPORTED
		}
}

<#if paperMap.isOpenCamera == '1'>
function openCamera() {
   	if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia) {
	      //调用用户媒体设备, 访问摄像头
	      getUserMedia({video : {width: 151, height: 201}}, success, error);
	} 
   	else {
  	  	layer.msg('检测到摄像头没有正常工作，很抱歉，您无法继续考试了，请退出考试后重试！', {icon: 0,time: 300000,btn:['知道了'],end:function(){
			layer.closeAll();
			document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${xm.id}";
		}});
	 }
	
   	startToExam();
   	
   	var myVideo=document.getElementById("cameraDiv");  
   	myVideo.addEventListener('pause',function(){  
   		layer.msg('检测到摄像头没有正常工作，很抱歉，您无法继续考试了，请退出考试后重试！', {icon: 0,time: 300000,btn:['知道了'],end:function(){
			layer.closeAll();
			document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${xm.id}";
		}}); 
   	})  
}

function startToExam(){
	$.ajax({
		type: "POST",
		url: "${request.contextPath}/personal/paper/startExam",
		data: {"paperId":tm_paper_id, "t":Math.random(), "photoPath":'${photoPath}'},
		dataType: "json",
		success: function(ret){
			if(ret.code == 0){
				xwUserPaper.initPaper();
				ajaxSubmit();
				autoSaveTask();
				logPhotoTask();
			}else{
				layer.alert(ret.message, {shadeClose: true,icon: 0,time: 50000,end:function(){
					document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${paperMap.xmId}";
				}});
			}
		},
		error:function(){
			layer.alert("服务器异常，请刷新后重试", {shadeClose: true,icon: 0,time: 50000,end:function(){
				window.location.reload();
			}});
		}
	});
}

function logPhotoTask(){
	setInterval(function(){
		try{
			context.drawImage(video, 0, 0, 151, 201);
			var canvas = document.getElementById("canvas");
			var baseStr = canvas.toDataURL("png");
			//压缩并上传考生图片
			compressStudentPictureAndUpload(baseStr);
		}catch(e){
			//do nothing
		}
	},${paperMap.photoCatchInterval}*1000);
}

function compressStudentPictureAndUpload(baseStr){
	//抓拍图片按照151*201尺寸压缩，理论上超过50K就要压缩，但是现代电脑摄像头排出的照片都大于50K，故此处直接压缩
	var maxWidth = 151;
	var maxHeight= 201;
	var _ir=ImageResizer({
		resizeMode:"auto",
		dataSource:baseStr,
		dataSourceType:"base64",
		maxWidth:maxWidth, //允许的最大宽度
		maxHeight:maxHeight, //允许的最大高度
		onTmpImgGenerate:function(img){
		},
		success:function(resizeImgBase64,canvas){
			completeUpload(resizeImgBase64);
		}
	});
}

function completeUpload(baseStr){
	if(baseStr){
		$.ajax({
			type: "POST",
			url: "${request.contextPath}/personal/paper/logPhoto",
			data: {"paperId":tm_paper_id,"base64PhotoPath":baseStr,"t":Math.random()},
			dataType: "json",
			success: function(ret){
				if(ret.code != 0){
					layer.alert(ret.message, {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
				}
			},
			error:function(){
				layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
			}
		});
	}else{
		layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
	}
}

function closeCapture(){
	try{
		if (window.stream) {
            window.stream.getTracks().forEach(track => {
                track.stop();
            });
        }
	}catch(e){
		console.log("关闭摄像头时出现异常", e);
	}
}

function getUserMedia(constraints, success, error) {
	closeCapture();
  if (navigator.mediaDevices.getUserMedia) {
	  console.log("navigator.mediaDevices.getUserMedia = true");
    //最新的标准API
    navigator.mediaDevices.getUserMedia(constraints).then(success).catch(error);
  } else if (navigator.webkitGetUserMedia) {
	  console.log("navigator.webkitGetUserMedia = true");
    //webkit核心浏览器
    navigator.webkitGetUserMedia(constraints,success, error)
  } else if (navigator.mozGetUserMedia) {
	  console.log("navigator.mozGetUserMedia = true");
    //firfox浏览器
    navigator.mozGetUserMedia(constraints, success, error);
  } else if (navigator.getUserMedia) {
	  console.log("navigator.getUserMedia = true");
    //旧版API
    navigator.getUserMedia(constraints, success, error);
  }
}
 
var video = document.getElementById('cameraDiv');
var canvas = document.getElementById('canvas');
var context = canvas.getContext('2d');

var streamSign = null;
function success(stream) {
	streamSign = stream;
  //兼容webkit核心浏览器
  //let CompatibleURL = window.URL || window.webkitURL;
  //将视频流设置为video元素的源
  console.log(stream);
  //video.src = CompatibleURL.createObjectURL(stream);
  video.srcObject = stream;
  video.play();
}

function error(error) {
  	layer.msg('检测到摄像头没有正常工作[ERROR]，很抱歉，您无法继续考试了，请退出考试后重试！', {icon: 0,time: 300000,btn:['知道了'],end:function(){
		layer.closeAll();
		document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${xm.id}";
	}});
}

//关闭摄像头
function closeCamera() {
	 if(streamSign){
		streamSign.getTracks()[0].stop();
	 }
};
</#if>

<#if paperMap.isOpenCamera != '1'>
$(document).ready(function() {
	startToExam()
});
function startToExam(){
	$.ajax({
		type: "POST",
		url: "${request.contextPath}/personal/paper/startExam",
		data: {"paperId":tm_paper_id, "t":Math.random()},
		dataType: "json",
		success: function(ret){
			if(ret.code == 0){
				xwUserPaper.initPaper();
				ajaxSubmit();
				autoSaveTask();
			}else{
				layer.alert(ret.message, {icon: 5,shadeClose: true,end:function(){
					document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${paperMap.xmId}";
				}});
			}
		},
		error:function(){
			layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
		}
	});
}
</#if>

function getFormSerializeArrayJsonStr(){
	var d = {};
	var t = $('#form_paper_detail').serializeArray();
	$.each(t, function() {
		var tmp = d[this.name];
		if(tmp == null){
			tmp = [this.value];
		}else{
			tmp.push(this.value);
		}
		d[this.name] = tmp;
	});
	var formJsonStr = JSON.stringify(d).toString();
	return formJsonStr;
}

/**
 *  ajax提交
 */
function ajaxSubmit(){
	$.ajax({
		type: "POST",
		url: "${request.contextPath}/personal/paper/ajaxSavePaper?timestamp=" + (new Date()).getTime(),
		data: $('#form_paper_detail').serializeArray(),
		dataType: "json",
		success: function(ret){
			if(ret.code!=0){
				layer.alert(ret.message, {icon: 5,shadeClose: true,end:function(){
					document.location.href="${request.contextPath}/personal/wdxxDetails?xmId=${paperMap.xmId}";
				}});
			}
		},
		error:function(e){
			layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
			//top.location.reload();
		}
	});
}

/**
 * 定义自动保存答题进度
 */
function autoSaveTask(){
	var interval_time = ${intervalTime?c};
	if(!interval_time){
		interval_time = 60;
	}else{
		interval_time = interval_time - 0;
	}
	setInterval(function(){
		var formCurrentSerializeArrayNew = getFormSerializeArrayJsonStr();
		if(formCurrentSerializeArrayNew != xwUserPaper.formCurrentSerializeArray){
			xwUserPaper.formCurrentSerializeArray = formCurrentSerializeArrayNew;
			ajaxSubmit();
		}
	},interval_time * 1000);
}

//套题点亮右侧导航
function illume(obj){
	var parentId = $(obj).attr("parentId");
	var childrenNumber = $(obj).attr("childrenNumber");
	illume2(parentId, childrenNumber);
}
//判断套题是否完成，如果已完成就点亮套题
function illume2(parentId, childrenNumber){
	if(!childrenNumber || childrenNumber == 0){
		return;
	}
	var num = 0;
	$("."+parentId).each(function(){
		var children_id = $(this).val();
		var children_type = $(this).attr("childrenType");
		if(children_type == 2){
			var boo = false;
			$("[name=Q-"+children_id+"]").each(function(){
				if($(this).prop('checked')){
					boo = true;
				}
			})
			if(boo == true){
				num ++;
			}
		}else if(children_type == 3){
			if($("input[name='Q-"+children_id+"']:checked").val()){
				num ++;
			}
		}else if(children_type == 4){
			var boo = true;
			$("[name=Q-"+children_id+"]").each(function(){
				if(!$(this).val()){
					boo = false;
				}
			})
			if(boo == true){
				num ++;
			}
		}else{
			if($("[name=Q-"+children_id+"]").val()){
				num ++;
			}
		}
  	});
	if(num == childrenNumber){
		$("#fast_"+parentId).css("color","#FFFFFF");
		$("#fast_"+parentId).css("background","#0079FE");
	}else{
		$("#fast_"+parentId).css("color","");
		$("#fast_"+parentId).css("background","");
	}
}
//显示上传窗口
function showUpload(){
	$("#answerCardFile").click();
}
//上传主观题答题卡
function selectedFile(){
	var files = document.getElementById('answerCardFile').files;
	if(!files || files.length == 0){
		layer.alert('请选择附件！', {icon: 5,shadeClose: true,end:function(){}});
	}
	else{
		var file = files[0];
		if (file.size>10*1024*1024){
			layer.alert('附件大小不能超过10M！', {icon: 5,shadeClose: true,end:function(){}});
		}
		else{
			 var formdata = new FormData();
             formdata.append("file", file);
			 formdata.append("paperId", tm_paper_id);
			 utils.uploadBizFile('${request.contextPath}/personal/paper/saveCard', formdata, function(data){
				var count = $('.answer_card_file').length;
				var html = '<tr class="answer_card_file" id="c_'+data.id+'">'+
								'<td style="width:300px;text-align:left;">主观题答案附件' + (count + 1) +'(<a href="'+data.url+'" target="_blank">下载</a>、&nbsp;<a href="javascript:deleteCard(\''+data.id+'\')" style="font-size:10px;">删除</a>)</td>'+
							'</tr>';
				$("#card").append($(html));
				layer.alert('附件上传成功',{icon:6},function(){$("#answerCardFile").val('');layer.closeAll();});
	         });
		}
	}
}
//删除主观题答题卡
function deleteCard(id){
	$.ajax({
		type: "POST",
		url: "${request.contextPath}/personal/paper/delCard?timestamp=" + (new Date()).getTime(),
		data: {id:id},
		dataType: "json",
		success: function(ret){
			if(ret.code == 0){
				$("#c_"+id).remove();
			}
			else{
				layer.alert('删除失败',{icon:5},function(){layer.closeAll();});
			}
		},
		error:function(e){
			layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5,shadeClose: true,end:function(){window.location.reload();}});
			//top.location.reload();
		}
	});
}
</script>
</html>