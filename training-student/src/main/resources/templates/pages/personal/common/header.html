<#attempt>
	<#include "pages/personal/common/navbar/${currentHostOrg.code}-personal.html" />
	<#recover>
<div class="fix_top1 ztop">
	<div class="mid clearfix">
		<a href="###" class="zlogobox">
			<img src="${currentHostOrg.portalLogo}" class="zlogo">
		</a>
		<div class="znav clearfix">
			<#if currentHostOrg.isPortalCustomized == 1>
				<#include "pages/personal/common/navbar/${currentHostOrg.code}.html">
			<#else>
				<#include "pages/personal/common/navbar/original.html">
			</#if>
		</div>
		<div class="rztop">
<#--			<a href="###" class="wenbox" style="position: relative;top: -20px;">-->
<#--				<img src="/personal/img/wen.png" class="wen">帮助-->
<#--			</a>-->
			<div class="txtop">
				<#if Session["session-student-user"].user.avatar != null>
					<img src="${Session["session-student-user"].user.avatar}" class="tx1">
				<#else>
					<img src="/personal/img/student-avatar.gif" class="tx1">
				</#if>
				<span class="txname">${Session["session-student-user"].info.name}</span>
				<img src="/personal/img/xiala.png" class="xiala">
				<div class="xlbox" style="display: none;">
					<a href="${request.contextPath}/personal/infoUpdate" class="xla">个人信息</a>
					<a href="javascript:xg1();" class="xla">修改密码</a>
					<a href="javascript:logout();" class="xla">退出</a>
				</div>
			</div>
		</div>
	</div>
</div>
<div style="margin-top: 50px"></div>
</#attempt>

<!--修改密码-->
<div class="tczz" id="xg1" style="display: none">
	<div class="tccon3">
		<h3 class="tch3">修改密码</h3>
		<div style="color:red;font-size:14px;word-break: break-all;"><span>新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合</span></div>
		<img src="img/close.png" class="close2" id="close1">
		<div class="xgcon" style="padding:0px 0px;margin-top:20px">
			<div class="clearfix">
				<div class="xgp1">新密码：</div>
				<input type="password" class="inputxg" id="newPassword" placeholder=""/>
			</div>
			<div class="clearfix mt1">
				<div class="xgp1">确认密码：</div>
				<input type="password" class="inputxg" id="reNewPassword" placeholder=""/>
			</div>
		</div>
		<div class="flexbox_center">
			<button class="xgbtn1" onclick="javascript:updatePassword()">确定</button>
			<button class="xgbtn2" onclick="javascript:cancel()">取消</button>
		</div>
	</div>
</div>
<div class="fix_bottom_right">
	<img class="scroll-to-top" id="scrollToTopBtn" title="返回顶部" src="/portal/st/img/totop.png">
</div>
<script>
//全局的ajax访问，处理ajax清求时sesion超时 
$.ajaxSetup({
  complete : function(XMLHttpRequest, textStatus) {
    var sessionstatus = XMLHttpRequest.getResponseHeader("sessionstatus"); // 通过XMLHttpRequest取得响应头，sessionstatus，
    if (sessionstatus == "timeout") {
      	//超时则直接跳转到门户首页
    	document.location.href='${request.contextPath}/';
    }
  }
});

$(".xiala").click(function(){
	$(this).toggleClass("rotate");
	$(this).next(".xlbox").slideToggle();
})

function logout(){
	$.ajax({
      async:false,
      type: "POST",
      url: "${request.contextPath}/portal/logout",
      success: function(ret){
    	  document.cookie="_xunw_student_logined=0";
      	  document.location.href='${request.contextPath}/';
      }
	});
}

$(".xuea").click(function(){
	$(this).children(".ewmbox").slideToggle();
})

function xg1(){
	$("#xg1").show();
}
$("#close1").click(function(){
	$("#xg1").hide();
})

function updatePassword() {
	var newPassword = $("#newPassword").val();
	var reNewPassword = $("#reNewPassword").val();
	if (newPassword == null){
		layer.alert("请输入密码", {shadeClose: true,icon: 0,time: 50000,end:function(){}});
		return
	}
	if (newPassword !== reNewPassword){
		layer.alert("两次输入的密码不一致，请重新输入", {shadeClose: true,icon: 0,time: 50000,end:function(){
			$(".newPassword").attr("value","")
			$(".reNewPassword").attr("value","")
		}});
		return
	}
	$.ajax({
		async:false,
		type: "POST",
		url: "${request.contextPath}/personal/updatePassword",
		data: {"newPassword":newPassword},
		dataType: "json",
		success: function(ret){
			if (ret.code == 0){
				layer.alert('密码修改成功',{icon:6,end:function(){
					$(".newPassword").attr("value","")
					$(".reNewPassword").attr("value","")
					$("#xg1").hide();
               	}});
			}
			else{
				layer.alert(ret.message, {shadeClose: true,icon: 0,time: 50000,end:function(){}});
			}
		}
	});
}

function cancel() {
	$("#xg1").hide();
}

document.addEventListener('DOMContentLoaded', function() {
	var scrollToTopBtn = document.getElementById('scrollToTopBtn');

	// 显示/隐藏按钮
	window.addEventListener('scroll', function() {
		if (window.pageYOffset > 300) {
			scrollToTopBtn.style.display = 'block'; // 显示按钮
		} else {
			scrollToTopBtn.style.display = 'none'; // 隐藏按钮
		}
	});

	// 点击按钮时滚动到顶部
	scrollToTopBtn.addEventListener('click', function() {
		window.scrollTo({ top: 0, behavior: 'smooth' });
	});
});

</script>
<style>
	/*修改密码*/
	.tccon3{
		width: 550px;
		border-radius: 8px;
		overflow: hidden;
		background-color: #FFFFFF;
		margin: 80px auto 0 auto;
		box-sizing: border-box;
		padding: 20px 40px;
		position: relative;
	}
	.tch3{
		font-size: 18px;
		color: #333333;
		font-weight: bold;
		line-height: 40px;
		padding-bottom: 20px;
	}
	.close2{
		position: absolute;
		right: 30px;
		top: 20px;
		width: 20px;
		height: 20px;
		cursor: pointer;
	}
	.xgcon{
		padding:30px 40px;
	}
	.xgp1{
		font-size: 14px;
		line-height: 40px;
		color: #333333;
		width: 20%;
		float: left;
	}
	.inputxg{
		width: 80%;
		float: left;
		height: 40px;
		box-sizing: border-box;
		border: 1px solid #DDDDDD;
		border-radius: 5px;
		padding-left: 10px;
	}
	.xgbtn1{
		background-color: #2971F6;
		color: #FFFFFF;
		border: none;
		width: 120px;
		height: 40px;
		border-radius: 5px;
		margin: 20px;
		outline: none;
		font-size: 14px;
		cursor: pointer;
	}
	.xgbtn2{
		background-color: #FFFFFF;
		color: #2971F6;
		border: 1px solid #2971F6;
		width: 120px;
		height: 40px;
		border-radius: 5px;
		margin: 20px;
		outline: none;
		font-size: 14px;
		box-sizing: border-box;
		cursor: pointer;
	}

	.indexnav {
		width: 100%;
		background-color: #00795A;
		color: #FFFFFF;
		position: relative;
	}
	.ztop{
		position: relative;
	}
	.nava{
		position: relative;
	}
	.nava.on1:after {
		content: " ";
		width: 2rem;
		height: 0.1rem;
		background: #FFFFFF;
		position: absolute;
		bottom: -0.2rem;
		left: 50%;
		transform: translateX(-50%);
	}
	.zwrap{
		padding-top: 0;
	}

	/* 固定顶部第一个容器 */
	.fix_top1 {
		position: fixed;
		top: 0;
		left: 0;
		background-color: #fff;
		height: 50px;
		width: 100%;
		z-index: 20; /* 确保在其他内容之上 */
	}

	/* 固定顶部第二个容器 */
	.fix_top2 {
		position: fixed;
		top: 50px; /* 在第一个容器下方显示 */
		left: 0;
		height: 24px;
		width: 100%;
		z-index: 10; /* 确保在第一个容器下方 */
		background-color: #00795A;
		color: #FFFFFF;
	}

	.fix_bottom_right {
		position: fixed;
		right: 24px;
		bottom: 64px;
		background-color: rgb(251, 251, 251);
		width: 44px;
		border-radius: 22px;
		z-index: 10;
	}

	.scroll-to-top {
		width: 100%;
		border-radius: 50%;
		padding: 10px 0;
		cursor: pointer;
		display: none;
	}
</style>