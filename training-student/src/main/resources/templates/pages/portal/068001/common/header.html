<script>
    $(function () {
        var $control = $('#userbox_tabs>li');
        var $img = $('#userbox_login>li');
        $control.click(function () {
            var $th = $(this);
            var $n = $th.index();
            $control.removeClass();
            $th.addClass('usertab-current');
            $img.css('display', 'none');
            $img.eq($n).css('display', 'block');
        })
        $('.logina').hover(function () {
            $('.sub-menu').css('display', 'block');
        }, function () {

        });
        $('.controlbox').mouseleave(function () {
            $('.sub-menu').hide();
        })
    });
</script>
<script>
    function close() {
        $("#userbox").css("display", "none");
        $("#mask").css("display", "none");
        $("#resetbox").css("display", "none");
        $("#rawpassbox").css("display", "none");
    }

    function openLogin() {
        $("#userbox").css("display", "block");
        $("#mask").css("display", "block");
        $("#userbox_tabs>li").removeClass();
        $("#gologin").addClass("usertab-current");
        $("#userbox_login>li:first-child").css("display", "block");
        $("#userbox_login>li:last-child").css("display", "none");
        $("#resetbox").css("display", "none");
        $("#rawpassbox").css("display", "none");
    }

    function openReg() {
        $("#userbox").css("display", "block");
        $("#mask").css("display", "block");
        $("#userbox_tabs>li").removeClass();
        $("#goreg").addClass("usertab-current");
        $("#userbox_login>li:first-child").css("display", "none");
        $("#userbox_login>li:last-child").css("display", "block");
        $("#resetbox").css("display", "none");
        $("#rawpassbox").css("display", "none");
    }

    function openReset() {
        $("#userbox").css("display", "none");
        $("#userbox_login>li:first-child").css("display", "none");
        $("#userbox_login>li:last-child").css("display", "none");
        $("#rawpassbox").css("display", "none");
        $("#resetbox").css("display", "block");
    }

    function checkLogin() {
        var studentId = '${Session["session-student-user"].user.id}';
        if (!utils.isEmpty(studentId)) {
            window.location.href = "${request.contextPath}/personal/home";
        } else {
            openLogin();
        }
    }

    $(document).ready(function (e) {
        //登录enter事件
        $('#login_enter').keydown(function (event) {
            if (event.key === 'Enter') {
                const emptyInputs = $(this).find('input').filter(function () {
                    return $(this).val() === '';
                });
                if (emptyInputs.length === 0) {
                    dologin();
                } else {
                    // 聚焦到第一个未填写的子元素输入框
                    emptyInputs.first().focus();
                }
            }
        });

        //注册enter事件
        $('#regist_enter').keydown(function (event) {
            if (event.key === 'Enter') {
                const emptyInputs = $(this).find('input').filter(function () {
                    return $(this).val() === '';
                });
                if (emptyInputs.length === 0) {
                    regist();
                } else {
                    // 聚焦到第一个未填写的子元素输入框
                    emptyInputs.first().focus();
                }
            }
        });
        //忘记密码enter事件
        $('#resetbox').keydown(function (event) {
            if (event.key === 'Enter') {
                const emptyInputs = $(this).find('input').filter(function () {
                    return $(this).val() === '';
                });
                if (emptyInputs.length === 0) {
                    resetPasswords();
                } else {
                    // 聚焦到第一个未填写的子元素输入框
                    emptyInputs.first().focus();
                }
            }
        });
    });
</script>
<div class="fix_top1 head">
    <div class="mid clearfix">
        <a href="${request.contextPath}/"><img src="${currentHostOrg.portalLogo}" class="logo1"></a>
        <div class="fr flexbox">
            <div class="flexbox_center mr40">
                <img src="/portal/st/img/home_icon1.png" class="home_icon">
                <#if Session["session-student-user"].user??>
                    <a href="javascript:checkLogin();"
                       class="topa">个人中心[${Session["session-student-user"].info.name}]</a>
                <#else>
                    <a href="javascript:checkLogin();" class="topa">学员登录</a>
                </#if>
            </div>
            <div class="flexbox_center">
                <img src="/portal/st/img/home_icon2.png" class="home_icon">
                <a <#if adminDomain==studentDomain>href="${adminDomain}admin-web/" <#else>href="${adminDomain}"</#if>
                   target="_blank" class="topa">管理平台</a>
            </div>
        </div>
    </div>
</div>

<div class="userbox" id="userbox" style="display: none;">
    <ul id="userbox_tabs">
        <li id="gologin" class="usertab-current">登录</li>
        <li class="" id="goreg">注册</li>
        <div class="close_reg" id="close_userbox">
            <a href="javascript:close()">
                <img src="/portal/st/img/icon04.png" alt="" style="width: 14px;height: 14px;">
            </a>
        </div>
    </ul>
    <ul id="userbox_login">
        <li id="login_enter" style="display: block;"><!-- 登录页 -->
            <div class="userbox-inputbox" style="width:364px">
                <div class="inpbox clearfix">
                    <img src="/portal/st/img/icon01.png" class="dlicon">
                    <input type="text" name="username" id="user_name" placeholder="用户名（身份证号/手机号）"
                           class="inputdl">
                </div>
                <div class="inpbox clearfix">
                    <img src="/portal/st/img/icon02.png" class="dlicon">
                    <input type="password" name="password" id="user_password" placeholder="密码（默认身份证后六位）"
                           class="inputdl">
                </div>
                <div class="inpbox clearfix">
                    <input type="text" name="user_code" id="user_code" placeholder="输入验证码" class="inputdl">
                    <a href="javascript:refYzm();"><img style="width: 121px;
              height: 48px;
              z-index: 1;
              position: absolute;
              right: 0;
              top: 0;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码"></a>
                </div>
                <button class="btn-login btn-default" onclick="dologin()" style="width:364px">登 录</button>
                <p class="tip"><a href="javascript:openReset()">忘记密码?</a>
					<a style="float: right;color: #FF0F00;" href="javascript:openReg()">注册新用户</a>
                </p>
            </div>
        </li>
        <li style="display: none;" id="regist_enter"><!-- 注册页 -->
            <div class="userbox-inputbox" style="width:364px">
                <div class="inpbox clearfix">
                    <input type="text" name="name" placeholder="姓名（必填）" class="inputdl">
                </div>
                <div class="inpbox clearfix">
                    <input type="text" name="sfzh" placeholder="身份证号（选填）" class="inputdl">
                </div>
                <div class="inpbox clearfix">
                    <input type="text" name="mobile" placeholder="手机号（必填）" class="inputdl" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')">
                </div>
                <div class="inpbox clearfix">
                	<input type="text" name="graphic_code" maxlength="4" placeholder="请输入图形码" class="inputdl">
					<a href="javascript:refYzm();">
						<img style="width:125px;height:48px;position:absolute;left:237px;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码" />
					</a>						
				</div>
                <div class="inpbox clearfix">
                    <input type="text" name="code" placeholder="请输入验证码" class="inputdl">
                    <button onclick="sendCode()" id="sendCodeBtn" class="fl btn-getcode"
                            style="width: 123px;
              height: 48px;
              position: absolute;
              right: 0;
              top: 0;
              margin: 0;
              border-radius: 6px;">获取验证码
                    </button>
                </div>
                <button class="btn-reg btn-default" onclick="regist()" style="width:364px">注 册</button>
                <p class="tip">已有账号？<a href="javascript:openLogin()">马上登录</a></p>
            </div>
        </li>
    </ul>
</div>
<!--忘记密码-->
<div class="xtczz" id="resetbox" style="display: none;">
    <div class="xtccon" style="border-radius:0px">
        <a href="javascript:close()"><img src="/portal/st/img/icon04.png" class="xclose"></a>
        <h2 class="xdlh2" style="color:#666">忘记密码</h2>
        <div class="xinpbox clearfix">
            <input class="xinputdl" name="mobile1" placeholder="请输入您的手机号"
                   onkeyup="this.value=this.value.replace(/[^0-9]/g,'')"
                   onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')">
        </div>
        <div class="xinpbox clearfix">
            <input class="xinputdl2" name="graphic_code1" maxlength="4" placeholder="请输入图形码">
			<a href="javascript:refYzm();">
				<img style="width:110px;height:40px;position:absolute;left:258px;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码" />
			</a>						
		</div>
        <div class="xinpbox clearfix">
            <input class="xinputdl2" name="code1" placeholder="请输入验证码">
            <a href="javascript:sendResetCode()" id="sendCodeBtn1" class="xhq" style="width: 115px">获取验证码</a>
        </div>
        <div class="xinpbox clearfix">
            <input class="xinputdl" type="password" name="newPassword" placeholder="请输入新密码">
        </div>
        <div class="xinpbox clearfix">
            <input class="xinputdl" type="password" name="password1" placeholder="请输入确认密码">
        </div>
        <button class="xdlbtn" onclick="resetPasswords()" style="margin: 10px 0 0 0;">重置密码</button>
        <p class="tip">
            已有账号？<a href="javascript:openLogin()">马上登录</a>
            <a style="float: right;color: #FF0F00;" href="javascript:openReg()">注册新用户</a>
        </p>
    </div>
</div>
<!--弱密码修改&ndash;&gt;-->
<div class="xtczz" id="rawpassbox" style="display: none;">
    <div class="xtccon" style="border-radius:0px">
        <a href="javascript:close()"><img src="/portal/original/img/close.png" class="xclose"></a>
        <h2 class="xdlh2" style="color:#666">您的密码过于简单，请重新设置</h2>
        <div style="color:red;font-size:14px;text-align:center">新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合</div>
        <div class="xinpbox clearfix">
            <input class="xinputdl" type="password" name="strongPassword" placeholder="请输入新密码"/>
        </div>
        <div class="xinpbox clearfix">
            <input class="xinputdl" type="password" name="strongPasswordConfirm" placeholder="请输入确认密码"/>
        </div>
        <button class="xdlbtn" onclick="strongPasswords()">确定</button>
    </div>
</div>
<div class="mask" style="display: none;" id="mask"></div>
<!--右边固定-->
<div class="fix_right">
    <div class="fixrli frewmcon">
        <div class="fixr_div">
            <img src="/portal/st/img/wxqr.jpg" class="fixr_icon1">
            <span class="frewmp">微信小程序</span>
        </div>
        <div class="frewmbox clearfix">
            <div class="frewmli">
                <img src="/portal/st/img/wxQrCode.jpg" class="frewm">
            </div>
        </div>
    </div>
    <div class="fixrxian"></div>
    <div class="fixrli frewmcon">
        <div class="fixr_div">
            <img src="/portal/st/img/cico1.png" class="fixr_icon1">
            <span class="frewmp">手机网页</span>
        </div>
        <div class="frewmbox clearfix">
            <div class="frewmli">
                <img src="/portal/st/img/qr.png" class="frewm">
            </div>
        </div>
    </div>
    <div class="fixrxian"></div>
    <div class="fixrli frewmcon2">
        <div class="fixr_div">
            <img src="/portal/st/img/cico2.png" class="fixr_icon1">
            <span class="frewmp">官方热线</span>
        </div>
        <div class="frewmbox2 clearfix">
            <p class="frewmphone mb1">官方热线</p>
            <p class="frewmphone font_bold">027-59728535</p>
            <div class="frewmli">

            </div>
        </div>
    </div>
</div>
<div class="fix_bottom_right">
    <img class="scroll-to-top" id="scrollToTopBtn" title="返回顶部" src="/portal/st/img/totop.png">
</div>

<!--<div class="userbox" id="userbox" style="display: none;width:480px">-->
<!--	<ul id="userbox_tabs">-->
<!--		<li id="gologin">登录</li>-->
<!--		<li class="usertab-current" id="goreg">注册</li>-->
<!--		<div class="close_reg" id="close_userbox">-->
<!--			<a href="javascript:close()">-->
<!--				<img src="/portal/original/img/close.png" alt="" >-->
<!--			</a>-->
<!--		</div>	-->
<!--	</ul>-->
<!--	<ul id="userbox_login">-->
<!--		<li id="login_enter"><!-- 登录页 &ndash;&gt;-->
<!--			<div class="userbox-inputbox" style="width:364px">-->
<!--			<h3>学员登录</h3>-->
<!--				<input type="text" style="width:364px" name="username" id="user_name" placeholder="用户名（身份证号/手机号）" class="input-default">-->
<!--				<input type="password" style="width:364px" name="password" id="user_password" placeholder="密码（默认身份证后六位）" class="input-default">-->
<!--				<div id="code" class="oh">-->
<!--					<input type="text" name="user_code" id="user_code" placeholder="输入验证码" class="fl input-code" style="width:243px">-->
<!--					<div class="code-img" class="fl" style="width:84px;margin-left:243px;margin-top:22px">-->
<!--						<a href="javascript:refYzm();"><img style="width:121px;height:42px;position:absolute" src="${request.contextPath}/portal/imgCode/yzm" id="img_verifycode" alt="点击刷新验证码" /></a>						-->
<!--					</div>-->
<!--				</div>-->
<!--				<button class="btn-login btn-default" onclick="dologin()" style="width:364px">登  录</button>				-->
<!--				<p class="tip"><a href="javascript:openReset()">忘记密码</a><a style="float: right" href="javascript:openReg()">注册新用户</a></p>-->
<!--			</div>-->
<!--		</li>-->
<!--		<li style="display: none;" id="regist_enter"><!-- 注册页 &ndash;&gt;-->
<!--			<div class="userbox-inputbox" style="width:364px">-->
<!--				<h3>学员注册</h3>-->
<!--				<input type="text" style="width:364px" name="name" placeholder="姓名（必填）" class="input-default">-->
<!--				<input type="text" style="width:364px" name="sfzh" placeholder="身份证号（选填）" class="input-default">-->
<!--				<input type="text" style="width:364px" name="mobile" placeholder="手机号（必填）" class="input-default" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')">-->
<!--				<div class="oh">-->
<!--					<input type="text" name="code" placeholder="输入验证码" class="fl input-code" style="width: 258px;">-->
<!--					<button onclick="sendCode()" id="sendCodeBtn" class="fl btn-getcode" style="width:104px;margin-left:0px">获取验证码</button>-->
<!--				</div>	-->
<!--				<button class="btn-reg btn-default" onclick="regist()" style="width:364px">注 册</button>-->
<!--				<p class="tip">已有账号？<a href="javascript:openLogin()">马上登录</a></p>-->
<!--			</div>-->
<!--		</li>-->
<!--	</ul>-->
<!--</div>-->
<!--	<!--忘记密码&ndash;&gt;-->
<!--	<div class="xtczz" id="resetbox" style="display: none;">-->
<!--		<div class="xtccon" style="border-radius:0px">-->
<!--			<a href="javascript:close()"><img src="/portal/original/img/close.png" class="xclose"></a>-->
<!--			<h2 class="xdlh2" style="color:#666">忘记密码</h2>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl" name="mobile1" placeholder="请输入您的手机号"  onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')"/>-->
<!--			</div>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl2" name="code1" placeholder="请输入验证码"/>-->
<!--				<a href="javascript:sendResetCode()" id="sendCodeBtn1" class="xhq" style="width: 115px">获取验证码</a>-->
<!--			</div>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl" type="password" name="newPassword" placeholder="请输入新密码"/>-->
<!--			</div>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl" type="password" name="password1" placeholder="请输入确认密码"/>-->
<!--			</div>-->
<!--			<button class="xdlbtn" onclick="resetPasswords()">重置密码</button>-->
<!--		</div>-->
<!--	</div>-->
<!--	<!--弱密码修改&ndash;&gt;-->
<!--	<div class="xtczz" id="rawpassbox" style="display: none;">-->
<!--		<div class="xtccon" style="border-radius:0px">-->
<!--			<a href="javascript:close()"><img src="/portal/original/img/close.png" class="xclose"></a>-->
<!--			<h2 class="xdlh2" style="color:#666">您的密码过于简单，请重新设置</h2>-->
<!--			<div style="color:red;font-size:14px;text-align:center">新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合</div>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl" type="password" name="strongPassword" placeholder="请输入新密码"/>-->
<!--			</div>-->
<!--			<div class="xinpbox clearfix">-->
<!--				<input class="xinputdl" type="password" name="strongPasswordConfirm" placeholder="请输入确认密码"/>-->
<!--			</div>-->
<!--			<button class="xdlbtn" onclick="strongPasswords()">确定</button>-->
<!--		</div>-->
<!--	</div>-->
<!--<div class="mask" style="display: none;" id="mask"></div>-->
<style>
    /*nav a {*/
    /*        text-decoration: none;*/
    /*    }*/
    /*    nav > ul > li {*/
    /*        float: left;*/
    /*        text-align: center;*/
    /*        padding: 0 0.5em;*/
    /*    }*/

    /*	.sub-menu {*/
    /*		display:none;*/
    /*        position:absolute;*/
    /*        right:-15px;*/
    /*        top:60px;*/
    /*        padding-left: 0 !important;*/
    /*        height:50px;*/
    /*        width:100px;*/
    /*        overflow:hidden;*/
    /*        background-color:#fff;*/
    /*    }*/
    /*    */
    /*	.mid{*/
    /*	*/
    /*		position:relative;*/
    /*	}*/
    /*    .sub-menu>li {*/
    /*        color: white;*/
    /*        display:ineline-block;*/
    /*        font-size:11px;*/
    /*        width:100%;*/
    /*        height:20px;*/
    /*        line-height:20px;*/
    /*        text-align:center;*/
    /*    }*/

    /*    .sub-menu li:hover {*/
    /*        background-color:#f8f8f8;*/
    /*    }*/


    /*    .sub-menu li:hover a {*/
    /*        color: white;*/
    /*    }*/

    /*    ul {*/
    /*        list-style: none;*/
    /*    }*/

    /* 固定顶部第一个容器 */
    .fix_top1 {
        position: fixed;
        top: 0;
        left: 0;
        background-color: #f6fffd;
        height: 70px;
        width: 100%;
        z-index: 20; /* 确保在其他内容之上 */
    }

    /* 固定顶部第二个容器 */
    .fix_top2 {
        position: fixed;
        top: 70px; /* 在第一个容器下方显示 */
        left: 0;
        height: 52px;
        width: 100%;
        z-index: 10; /* 确保在第一个容器下方 */
        background-color: #00795A;
        color: #FFFFFF;
    }

    .fix_bottom_right {
        position: fixed;
        right: 24px;
        bottom: 64px;
        background-color: rgb(251, 251, 251);
        width: 44px;
        border-radius: 22px;
        z-index: 10;
    }

    .scroll-to-top {
        width: 100%;
        border-radius: 50%;
        padding: 10px 0;
        cursor: pointer;
        display: none;
    }
</style>
<script>
    // 窗口尺寸变化时，弹出框自动在屏幕居中
    // window.onresize = autoresize;
    // function autoresize(){
    // 		var y = $(window).width();
    // 		var wheight = $(window).height();
    // 		var left = y/2-200;
    // 		var ttop =(wheight-430)/2;
    // 		$("#userbox").css("left",left+"px");
    // 		$("#userbox").css("top",ttop+"px");
    // 	}
    // 	autoresize();
</script>
<script type="text/javascript">
    //发送短信验证码
    function codeInterval() {
        var code = $("#sendCodeBtn");
        code.attr("href", "javascript:void(0)");
        var time = 90;
        var set = setInterval(function () {
            code.html("(" + --time + ")秒后重新获取");
        }, 1000);
        setTimeout(function () {
            code.attr("href", "javascript:sendCode()");
            code.html("重新获取验证码");
            clearInterval(set);
        }, 90000);
    }

    //发送短信验证码
    function resetCodeInterval() {
        var code = $("#sendCodeBtn1");
        code.attr("href", "javascript:void(0)");
        var time = 90;
        var set = setInterval(function () {
            code.html("(" + --time + ")秒后重新获取");
        }, 1000);
        setTimeout(function () {
            code.attr("href", "javascript:sendResetCode()");
            code.html("重新获取验证码");
            clearInterval(set);
        }, 90000);
    }

    function sendCode() {
        var mobile = $('input[name="mobile"]').val();
        if (utils.isEmpty(mobile)) {
            layer.alert('请输入您的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile']").focus();
                }
            });
            return;
        }
        var graphic_code = $('input[name="graphic_code"]').val();
        if (utils.isEmpty(graphic_code)) {
            layer.alert('请输入图形码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='graphic_code']").focus();
                }
            });
            return;
        }
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/sendVerifyCode",
            data: {
                "t": Math.random(),
                "mobile": mobile,
                "graphic_code": graphic_code
            },
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    layer.alert('验证码已发送', {
                        icon: 6, end: function () {
                            //倒计时 90秒
                            codeInterval();
                            refYzm();
                        }
                    });
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5}, function () {
                    window.location.reload();
                });
            }
        });
    }

    function sendResetCode() {
        var mobile = $('input[name="mobile1"]').val();
        if (utils.isEmpty(mobile)) {
            layer.alert('请输入您的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile1']").focus();
                }
            });
            return;
        }
        var graphic_code = $('input[name="graphic_code1"]').val();
        if (utils.isEmpty(graphic_code)) {
            layer.alert('请输入图形码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='graphic_code1']").focus();
                }
            });
            return;
        }
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/sendResetVerifyCode",
            data: {
            	"t": Math.random(),
                "mobile": mobile,
                "graphic_code": graphic_code
            },
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    layer.alert('验证码已发送', {
                        icon: 6, end: function () {
                            //倒计时 90秒
                            resetCodeInterval();
                            refYzm();
                        }
                    });
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5}, function () {
                    window.location.reload();
                });
            }
        });
    }

    function regist() {
        var name = $('input[name="name"]').val();
        if (!name) {
            layer.alert('请输入您的姓名！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='name']").focus();
                }
            });
            return;
        }
        var sfzh = $('input[name="sfzh"]').val();
        if (sfzh && !utils.isCardNo(sfzh)) {
            layer.alert('请输入正确的身份证号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='sfzh']").focus();
                }
            });
            return;
        }
        var mobile = $('input[name="mobile"]').val();
        if (!mobile) {
            layer.alert('请输入您的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile']").focus();
                }
            });
            return;
        }
        if (!utils.isMobileNo(mobile)) {
            layer.alert('请输入正确的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile']").focus();
                }
            });
            return;
        }
        var code = $('input[name="code"]').val();
        if (!code) {
            layer.alert('请输入短信验证码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='code']").focus();
                }
            });
            return;
        }
        layer.load();
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/doRegister",
            data: {
                "t": Math.random(),
                name: name,
                sfzh: sfzh,
                mobile: mobile,
                code: code
            },
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    layer.alert('注册成功，默认密码为Abc@加您的手机号或身份证后六位！', {icon: 6});
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon: 5});
            }
        });
    }

    function refYzm() {
        $(".img_verifycode").attr("src", "${request.contextPath}/portal/imgCode/yzm?t=" + Math.random());
    }

    function resetPasswords() {
        var mobile = $('input[name="mobile1"]').val();
        if (!mobile) {
            layer.alert('请输入您的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile1']").focus();
                }
            });
            return;
        }
        if (!utils.isMobileNo(mobile)) {
            layer.alert('请输入正确的手机号！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='mobile1']").focus();
                }
            });
            return;
        }
        var newPassword = $('input[name="newPassword"]').val();
        var password = $('input[name="password1"]').val();
        if (!newPassword) {
            layer.alert('请输入您的密码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='newPassword']").focus();
                }
            });
            return;
        }
        if (!password) {
            layer.alert('请输入确认密码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='password']").focus();
                }
            });
            return;
        }
        if (newPassword != password) {
            layer.alert('密码不一致，请重新确认密码！', {icon: 5, btn: ['知道了']});
            return;
        }
        var code = $('input[name="code1"]').val();
        if (!code) {
            layer.alert('请输入短信验证码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='code1']").focus();
                }
            });
            return;
        }
        layer.load();
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/resetPasswords",
            data: {
                "t": Math.random(),
                mobile: mobile,
                newPassword: newPassword,
                password: password,
                code: code
            },
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    layer.alert('密码重置成功', {icon: 6}, function () {
                        layer.closeAll();
                        openLogin();
                    });
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon: 5});
            }
        });
    }

    function strongPasswords() {
        var _username = $("#user_name").val();
        var _password = $("#user_password").val();
        var strongPassword = $('input[name="strongPassword"]').val();
        var strongPasswordConfirm = $('input[name="strongPasswordConfirm"]').val();
        if (!strongPassword) {
            layer.alert('请输入您的密码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='strongPassword']").focus();
                }
            });
            return;
        }
        if (!strongPasswordConfirm) {
            layer.alert('请输入确认密码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='strongPasswordConfirm']").focus();
                }
            });
            return;
        }
        if (strongPassword != strongPasswordConfirm) {
            layer.alert('密码不一致，请重新输入！', {icon: 5, btn: ['知道了']});
            return;
        }
        layer.load();
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/strongPasswords",
            data: {
                "t": Math.random(),
                username: _username,
                oldPassword: _password,
                password: strongPassword
            },
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    layer.alert('密码修改成功', {icon: 6}, function () {
                        layer.closeAll();
                        close();
                        window.location.reload();
                    });
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon: 5});
            }
        });
    }

    function dologin() {
        var _username = $("#user_name").val();
        var _password = $("#user_password").val();
        var _code = $("#user_code").val();
        if (utils.isEmpty(_username)) {
            layer.alert('请输入用户名', {icon: 5});
            return;
        }
        if (utils.isEmpty(_password)) {
            layer.alert('请输入密码', {icon: 5});
            return;
        }
        if (utils.isEmpty(_code)) {
            layer.alert('请输入验证码', {icon: 5});
            return;
        }
        layer.load();
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/student/login",
            data: {"t": Math.random(), "username": _username, "password": _password, "code": _code},
            dataType: "json",
            success: function (ret) {
                layer.closeAll('loading');
                if (0 == ret["code"]) {
                    if (ret.data.isRawPassword && ret.data.isRawPassword == 1) {
                        $('#rawpassbox').show();
                        $("#userbox").hide();
                    } else {
                        //document.location.href="${request.contextPath}/personal/home";
                        localStorage.setItem("csrf-token", ret.data.csrfToken);
                        window.location.reload();
                    }
                } else {
                    layer.alert(ret['message'], {icon: 5});
                }
            },
            error: function (ret) {
                layer.closeAll('loading');
                layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon: 5});
            }
        });
    }

    function loginout() {
        $.ajax({
            async: false,
            type: "POST",
            url: "${request.contextPath}/portal/logout",
            success: function (ret) {
                //清除个人中心的cookie中的登录标记 by tianjun
                document.cookie = "_xunw_student_logined=0";
                window.location.reload();
            }
        });
    }

    function selectTabMenu() {
        $("#user_menu").show();
    }

    document.addEventListener('DOMContentLoaded', function() {
        var scrollToTopBtn = document.getElementById('scrollToTopBtn');

        // 显示/隐藏按钮
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.style.display = 'block'; // 显示按钮
            } else {
                scrollToTopBtn.style.display = 'none'; // 隐藏按钮
            }
        });

        // 点击按钮时滚动到顶部
        scrollToTopBtn.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    });
</script>