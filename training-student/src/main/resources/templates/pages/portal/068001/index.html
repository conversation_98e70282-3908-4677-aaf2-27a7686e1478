<!DOCTYPE html>
<html>
<#include "pages/portal/${hostOrgCode}/common/top.html">
<body>
<#include "pages/portal/${hostOrgCode}/common/header.html">
<div class="head">
    <div class="fix_top2 indexnav">
        <div class="mid flexbox navabox">
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/index" class="nava on">首页</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/trainingIndex" class="nava">行业培训</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/zyjdIndex" class="nava">技能认定</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/courseIndex" class="nava">在线课程</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/zhiboIndex" class="nava">在线直播</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/newsIndex" class="nava">新闻动态</a>
            <a href="${request.contextPath}/portal/st/${hostOrgCode}/aboutus" class="nava">关于我们</a>
        </div>
    </div>
</div>
<div class="mb3" style="margin-top: 122px">
    <div class="bannerbox swiper-container" id="swiper1">
        <div class="swiper-wrapper">
            <#if (bannerList?? && bannerList?size > 0)>
                <#list bannerList as syslbt>
                    <div class="swiper-slide" style="height: auto">
                        <a href="${syslbt.link}" target='_BLANK'>
                            <img src="${syslbt.url}" class="banner">
                        </a>
                    </div>
                </#list>
            <#else>
                <div class="swiper-slide">
                    <a href="javascript:void(0)">
                        <img src="/portal/st/img/banner-default.png" class="banner">
                    </a>
                </div>
            </#if>
        </div>
        <!-- 如果需要分页器 -->
        <div class="swiper-pagination"></div>
    </div>
</div>
<div class="mid">
    <div class="clearfix">
        <div class="bkcon" style="margin-right: 0;width: 700px">
            <h2 class="bkh2">
                <img src="/portal/st/img/new1.png" class="bkimg1">
                <span>${categoryList[0].name}</span>
                <span class="more1">了解更多培训信息</span>
            </h2>
            <div class="clearfix">
                <#if (newsList?? && newsList?size > 0)>
                    <#list newsList as items>
                        <#if items_index == 0>
                            <#list items as item>
                                <#if item_index lt 2>
                                    <a <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/st/${hostOrgCode}/newsDetail?id=${item.id}"</#if>>
                                        <div class="bkli">
                                            <img src="${item.url!'/portal/st/img/news.png'}" class="bkicon">
                                            <div class="bkh3">${item.title}</div>
                                            <p class="bkp"><#if item.publishTime??>${item.publishTime?string('yyyy-MM-dd')}</#if></p>
                                        </div>
                                    </a>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                </#if>
            </div>
        </div>
        <div class="zxcon">
            <#list newsList as news>
                <#if news_index == 0>
                    <#list news as item>
                        <#assign categoryId=item.categoryId>
                    </#list>
                </#if>
            </#list>
            <h2 class="zxh2"><a href="${request.contextPath}/portal/st/${hostOrgCode}/newsIndex?categoryId=${categoryId}" class="more2">更多 ></a></h2>
            <div class="zxbox">
                <#if (newsList?? && newsList?size > 0)>
                    <#list newsList as news>
                        <#if news_index == 0>
                            <#list news as item>
                                <#assign categoryId=item.categoryId>
                                <#if (item_index == 2 || item_index gt 2) && item_index lt 9>
                                    <div class="clearfix">
                                        <a <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/st/${hostOrgCode}/newsDetail?id=${item.id}" class="zxa">${item.title}</#if></a>
                                        <p class="zxdate"><#if item.publishTime??>${item.publishTime?string('yyyy-MM-dd')}</#if></p>
                                    </div>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                </#if>
            </div>
        </div>
    </div>
    <div class="mb3">
        <div class="clearfix">
            <#list newsList as news>
                <#if news_index == 1>
                    <#list news as item>
                        <#assign typeId=item.categoryId>
                    </#list>
                </#if>
            </#list>
            <div class="bkcon">
                <h2 class="bkh2">
                    <img src="/portal/st/img/new2.png" class="bkimg1">
                    <span>${categoryList[1].name}</span>
                    <span class="more1">通知公告政策研读</span>
                </h2>
            </div>
            <div class="zxcon">
                <h2 class="zxh2"><a href="${request.contextPath}/portal/st/${hostOrgCode}/newsIndex?categoryId=${typeId}" class="more2">更多 ></a></h2>
            </div>
        </div>
        <div class="gglist flexbox_between" style="justify-content: normal">
            <#list newsList as items>
                <#if items_index == 1>
                    <#list items as item>
                        <#if item_index lt 3>
                            <div class="ggcon">
                                <a <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/st/${hostOrgCode}/newsDetail?id=${item.id}"</#if> class="z01">
                                    <h2 class="ggh2">${item.title}</h2>
                                </a>
                                <p class="clearfix ggp">
                                    <span class="ggtime"><#if item.publishTime??>${item.publishTime?string('yyyy-MM-dd')}</#if></span>
                                    <a href="${request.contextPath}/portal/st/${hostOrgCode}/newsDetail?id=${item.id}" class="ggview">查看详情 ></a>
                                </p>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</div>

<div class="pxkcbg">
    <div class="mid">
        <div class="clearfix">
            <div class="bkcon">
                <h2 class="bkh2">
                    <img src="/portal/st/img/new3.png" class="bkimg1">
                    <span>推荐课程</span>
                    <span class="more1">精选课程，助你成为职场达人</span>
                </h2>
            </div>
            <div class="zxcon">
                <h2 class="zxh2"><a href="${request.contextPath}/portal/st/${hostOrgCode}/courseIndex" class="more2">更多 ></a></h2>
            </div>
        </div>
        <div class="xiangmu_list clearfix pt0">
            <#list courseList as kc>
                <#if kc_index lt 8>
                    <div class="pxcon">
                        <a href="${request.contextPath}/portal/st/${hostOrgCode}/coursePlay?id=${kc.id}">
                            <img src="${kc.logo!'/portal/st/img/pxkc.png'}" class="bgtu2">
                            <div class="pxtext">
                                <h3 class="pxh3">${kc.name}</h3>
                                <div>
                                    <p class="pxp">${kc.hours}学时</p>
                                    <p class="pxp">${kc.count}人学习</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </#if>
            </#list>
        </div>
    </div>
</div>
<#include "pages/portal/${hostOrgCode}/common/footer.html">
</body>
<script>
    var mySwiper = new Swiper('#swiper1', {
        direction: 'horizontal', // 水平切换选项
        loop: true, // 循环模式选项
        autoplay: true,
        // 如果需要分页器
        pagination: {
            el: '.swiper-pagination',
        },
    })

    var mySwiper = new Swiper('#swiper2', {
        direction: 'horizontal', // 水平切换选项
        loop: true, // 循环模式选项
        slidesPerView: 'auto',
        loopedSlides: 9,
        // 如果需要前进后退按钮
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    })

    var mySwiper = new Swiper('#swiper3', {
        direction: 'horizontal', // 水平切换选项
        loop: true, // 循环模式选项
        autoplay: {
            delay: 8000,//8秒切换一次
        },
        // 如果需要分页器
        pagination: {
            el: '.swiper-pagination',
        },
    })

    //新闻切换
    function fun(curindex, allnum) {
        for (var i = 1; i <= allnum; i++) {
            $("#a" + i).removeClass("on");
            $("#a-" + i).hide();
        }
        $("#a-" + curindex).show();
        $("#a" + curindex).addClass("on");
    }

    //角色切换
    function fun1(curindex, allnum) {
        for (var i = 1; i <= allnum; i++) {
            $("#b" + i).removeClass("on");
            $("#b-" + i).hide();
        }
        $("#b-" + curindex).show();
        $("#b" + curindex).addClass("on");
    }
</script>
<style>
    .bkli:first-child {
        margin-right: 20px;
    }
</style>
</html>
