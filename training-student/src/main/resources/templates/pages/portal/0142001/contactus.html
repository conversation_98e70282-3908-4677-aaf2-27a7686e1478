<!DOCTYPE html>
<html>

<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=23858baa4a37064fdd5706110c1fab37"></script>
<style>
    .amap-icon img {
        width: 25px;
        height: 34px;
    }
    .amap-marker-label{
        border: 0;
        background-color: transparent;
    }
    .amap-content-body {
        min-width: 200px;
        max-width: 240px;
        font-family: Helvetica,"Hiragino Sans GB","Microsoft Yahei","微软雅黑",Arial,sans-serif;
        box-shadow: 0 0 0.5px rgb(0 0 100 / 60%);
        background: #fff;
        border-radius: 2px;
        text-align: left;
        border: 1px solid silver;
    }
    .amap-lib-infowindow {
        padding: 0;
        position: relative;
        background-color: #fff;
        margin: 8px;
    }
    .amap-lib-infowindow-title {
        line-height: 22px;
        font-size: 14px;
        border-bottom: 1px #99adce solid;
        padding-right: 15px;
    }
    .amap-lib-infowindow-content {
        padding-top: 5px;
        overflow: hidden;
        font-size: 12px;
        zoom: 1;
    }
    .amap-combo-sharp {
        margin: 0 auto;
        bottom: 1px;
        position: relative;
        background: url(https://webapi.amap.com/theme/v1.3/images/amap-info.png) -5px -564px no-repeat;
        width: 18px;
        height: 9px;
    }
    #container{
        width: 770px;
        height: 450px;
    }
</style>
<#include "pages/portal/${currentHostOrg.code}/common/top.html">
<body class="bg1">
<#include "pages/portal/${currentHostOrg.code}/common/header.html">
<!-- 联系我们-->
<div class="mid mt15 bgf">
    <div class="aboutbox">
    <h3 class="abh3">
        <img src="/portal/lgd/img/jt.png" class="jt1">武汉理工大学教育培训中心简介<img src="/portal/lgd/img/jt.png" class="jt2">
    </h3>
    <div class="clearfix">
        <p class="abp1">
            武汉理工大学教育培训中心是学校的二级单位，与继续教育学院合署办公。面向各级党政机关、企事业单位管理人员、各行业专业技术人员、一线职工，围绕管理水平、专业技术、技能提升等多方面开展培训。教育培训中心自成立以来，秉承“面向国家战略与区域发展、面向行业转型升级、服务学员能力提升、服务学习型社会建设﹑构建高质量教育培训体系”的工作理念，全面推进学校非学历继续教育的发展。
        </p>
        <img src="/portal/lgd/img/abtu1.png" class="abtu1">
    </div>
    <div class="clearfix">
        <img src="/portal/lgd/img/abtu2.png" class="abtu2">
        <p class="abp2">
            武汉理工大学教育培训中心充分依托建工建材、交通运输、汽车三大行业的行业背景优势，独特的学科优势和雄厚的师资力量，重点开设企业中高层管理能力提升、企业文化与党组织建设、人力资源管理、财务管理与资本运营、生产与运营管理、营销管理、项目经理实践、建筑信息化模型BIM、注册建造师考证集训、房屋安全管理、交通运输安全、安全和应急管理、海事管理、公路工程监理、船员培训、职业教师素质提升、汽车轻量化与智能制造、智能网联汽车、新能源汽车技术等具有行业特色的培训项目。同时结合地方经济和社会发展的需要开办政府治理、科技和管理创新、乡村振兴计划等项目。
        </p>
    </div>
    <p class="adp3 mt20">
        近年来，培训中心得到政府和行业企业的密切支持，建设了一批政府和企事业单位培训基地，“国家级专业技术人员继续教育基地”、“武汉理工大学船员培训中心”、“中国混凝土与水泥制品工程师继续教育基地”、“交通高级财会人员培训基地”、“交通运输部安全从业人员素质教育培训基地”、“交通行业复合型高级财会人员培训基地和审计人才培训基地”、“国家级众创空间”、“湖北省知识产权培训基地”、“湖北省退役军人职业技能培训定点机构”、“武汉市高技能人才培训基地”、“武汉市职业技能等级认定社会培训评价组织”等。
    </p>
    <p class="adp3">
        新的发展时期，武汉理工大学教育培训中心将以习近平新时代中国特色社会主义思想为指导，坚定社会主义办学方向，规范办学，严格管理，为学习型社会建设做出积极贡献。
    </p>
        <div class="clearfix mt40">
            <div class="ditu2" id="container" style="width: 550px;height: 384px">
            </div>
            <div class="dcon2">
                <div class="dili">
                    <img src="/portal/lgd/img/licon1.png" class="licon">
                    <p class="dilip">咨询电话：027-87651939，027-87651942</p>
                </div>
                <div class="dili">
                    <img src="/portal/lgd/img/licon2.png" class="licon">
                    <p class="dilip">联系人：18062077878（李老师），18571777916（王老师）</p>
                    <p class="dilip" style="margin-left: 39px;">理工大培训中心QQ ：873992679</p>
                    <p class="dilip" style="margin-left: 39px;">理工大职业技能等级认定咨询QQ群：369302648</p>
                    <p class="dilip" style="margin-left: 39px;">Altium认证电子工程师培训QQ群：497422195</p>
                    <p class="dilip" style="margin-left: 39px;">BIM考试咨询QQ群：856438226</p>
                </div>
                <div class="dili">
                    <img src="/portal/lgd/img/licon3.png" class="licon">
                    <p class="dilip">地址：武汉市洪山区武汉理工大学马房山校区东院继续教育学院</p>
                </div>
                <div class="dili">
                    <img src="/portal/lgd/img/licon1.png" class="licon">
                    <p class="dilip">武汉理工大学继续教育学院</p>
                    <p class="dilip" style="margin-left: 39px;">招生办：027-87880058 </p>
                    <p class="dilip" style="margin-left: 39px;">教学办：027-87858411，027-87651794</p>
                    <p class="dilip" style="margin-left: 39px;">学籍办：027-87850980</p>
                </div>
            </div>
        </div>
    </div>
</div>
<#include "pages/portal/${currentHostOrg.code}/common/footer.html">

<script type="text/javascript">
  $(function () {
    $("#contactus").addClass("on");
  })

  var map = new AMap.Map('container', {
    resizeEnable: true,
    center: [114.353255, 30.521189],
    zoom: 16
  });

  var marker = new AMap.Marker({
    position: map.getCenter(),
    icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
    offset: new AMap.Pixel(-13, -30)
  });

  marker.setMap(map);

  // 设置鼠标划过点标记显示的文字提示
  marker.setTitle('武汉理工大学继续教育学院');

  // 设置label标签
  marker.setLabel({
    offset: new AMap.Pixel(0, 0),  //设置文本标注偏移量
    content: '<div class="amap-content-body amap-pl-pc">' +
            '<div class="amap-lib-infowindow">' +
            '<div class="amap-lib-infowindow-title">' +
            '<span class="poi-name">武汉理工大学继续教育学院</span></div>' +
            '<div class="amap-lib-infowindow-content">' +
            '<div class="amap-lib-infowindow-content-wrap">' +
            '<div>地址：珞狮路165武汉理工大学马房山校区东院</div>' +
            '</div></div></div></div>' +
            '<div class="amap-combo-sharp"></div>', //设置文本标注内容
    direction: 'top' //设置文本标注方位
  });
</script>
</body>
</html>
