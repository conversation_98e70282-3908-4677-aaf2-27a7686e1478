<!DOCTYPE html>
<html>
<#include "pages/portal/${currentHostOrg.code}/common/top.html">
	<body class="bg1">
	<#include "pages/portal/${currentHostOrg.code}/common/header.html">
	<div class="bannerbox">
	    <div class="swiper-container" id="swiper1">
	        <div class="swiper-wrapper">
	            <#list bannerList as syslbt>
	                    <div class="swiper-slide">
	                        <a href="${syslbt.link}" target="_blank">
	                            <img src="${syslbt.url}" class="banner">
	                        </a>
	                </div>
	            </#list>
	        </div>
	        <div class="swiper-pagination"></div>
	    </div>
    </div>


    <div class="bgf">
        <div class="mid">
            <h3 class="sh3">
                <img src="/portal/lgd/img/sicon1.png" class="sicon" >新闻资讯
                <span class="xwsp" href="${request.contextPath}/portal/${currentHostOrg.code}/newsIndex?categoryId=ZXXW">了解更多资讯动态</span>
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/newsIndex?categoryId=ZXXW" class="fr more2">更多 ></a>
            </h3>
            <div class="clearfix">
                <#list leftNews as newsItem>
                <#if (newsItem?? && newsItem?size > 0) >
                <#list newsItem as item>
                <div class="xwbox1" <#if item.isLink?? && item.isLink == '1'>onclick="window.open('${item.linkUrl}','_blank')" <#else>onclick="window.open('${request.contextPath}/portal/${currentHostOrg.code}/newsDetail?id=${item.id}','_blank');"</#if>   >
                    <img src="${item.url?default('/portal/lgd/img/shi4.png')}" href="" class="xwtu">
                    <div class="xwxh1">${item.title}</div>
                    <div class="xwdate">${item.createTime?string("yyyy-MM-dd")}</div>
                </div>
            </#list>
        </#if>
    </#list>

    <div class="xwbox2">
        <#if rightNews?? && rightNews?size gt 0>
        <#list rightNews as item>
        <div class="clearfix" >
            <a <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/${currentHostOrg.code}/newsDetail?id=${item.id}"</#if> class="xwp1" target="_blank">${item.title}</a>
            <div class="xwdate2">${item.createTime?string("yyyy.MM.dd")}</div>
        </div>
        </#list>
        </#if>
    </div>
    </div>


    <h3 class="sh3">
        <img src="/portal/lgd/img/sicon2.png" class="sicon">通知公告
        <span class="xwsp">通知公告政策研读</span>
        <a href="${request.contextPath}/portal/${currentHostOrg.code}/noticeIndex?categoryId=TZGG" class="fr more2">更多 ></a>
    </h3>
    <div class="clearfix">
        <#if noticeList?? && noticeList?size gt 0>
        <#list noticeList as item>
        <div class="tzli">
            <div class="tzshu">0${item_index+1}</div>
            <div class="tzh1">${item.title}</div>
            <div class="clearfix">
                <p class="tzdate">${item.createTime?string("yyyy-MM-dd HH:mm")}</p>
                <a <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/${currentHostOrg.code}/noticeDetail?id=${item.id}"</#if> class="more3" target="_blank">查看详情</a>
            </div>
        </div>
    </#list>
    </#if>
</div>
</div>
</div>


<div class="xbox1 mid">
    <h3 class="sh3">
        <img src="/portal/lgd/img/sicon3.png" class="sicon">热门项目
        <span class="xwsp">最新热门报考项目</span>
        <a href="${request.contextPath}/portal/${currentHostOrg.code}/projectList" class="fr more2">更多 ></a>
    </h3>
    <div class="clearfix">
        <#if projectList?? && projectList?size gt 0>
        <#list projectList as item>
        <a href="${request.contextPath}/portal/${currentHostOrg.code}/getCourseDeatilById?id=${item.id}" class="xmxli">
            <img src="${item.logo!'/portal/original/img/kctu2.png'}" class="xmtua">
            <p class="xmpp1">${item.name}</p>
        </a>
    </#list>
</#if>
</div>
</div>
<div class="bgf mt15">
    <div class="mid">
        <h3 class="sh3">
            <img src="/portal/lgd/img/sicon4.png" class="sicon">推荐课程
            <span class="xwsp">超多人学习的精品课程</span>
            <a href="${request.contextPath}/portal/${currentHostOrg.code}/courseList" class="fr more2">更多 ></a>
        </h3>
        <div class="clearfix">
            <#if courseList??>
            <#list courseList as course>
            <a href="${request.contextPath}/portal/${currentHostOrg.code}/courseIndex?kjType=${course.tag}&courseId=${course.id}" class="keli">
                <#if course.xct??>
                <img src="${course.xct}" class="xmtua">
                <#else>
                <img src="/portal/lgd/img/course.png" class="xmtua">
            </#if>

            <div class="xmcon">
                <p class="kep1">${course.name}</p>
                <div class="clearfix">
                    <span class="kebq">${course.courseHours}课时</span>
                    <!-- <span class="kebq">${course.bmCount}人报名</span> -->
                    <#if course.amount?? && course.amount!=0>
                    <p class="keprize">${course.amount}元</p>
                    <#else>
                    <p class="keprize">免费</p>
                    </#if>
                </div>
            </div>
            </a>
        </#list>
    </#if>
</div>
</div>
</div>


    <#include "pages/portal/${currentHostOrg.code}/common/footer.html">
    </body>
<script>
  $(function () {
    $("#index").addClass("on");
  })

  var mySwiper = new Swiper ('#swiper1', {
      direction: 'horizontal', // 水平切换选项
      loop: true, // 循环模式选项
      autoplay: true,

      // 如果需要分页器
      pagination: {
          el: '.swiper-pagination',
      },

  })
  var mySwiper = new Swiper ('#swiper2', {
      direction: 'horizontal', // 水平切换选项
      loop: true, // 循环模式选项
      // 如果需要前进后退按钮
      navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
      },
      autoplay: {disableOnInteraction: false},

  })

  function a1(){
      $("#tc1").show();
  }
  $(".close").click(function(){
      $(this).parent().parent(".tczz").hide();
  })

  $("#click1").click(function(){
      $("#tc1").hide();
      $("#tc2").show();
  })
  $("#click2").click(function(){
      $("#tc1").show();
      $("#tc2").hide();
  })
  function checkLogin(){
  	var studentId = '${Session["session-student-user"].user.id}';
 		if(!utils.isEmpty(studentId)){
		window.location.href = "${request.contextPath}/personal/home";
	}else{
		openLogin();
	}
  }
</script>
</html>

