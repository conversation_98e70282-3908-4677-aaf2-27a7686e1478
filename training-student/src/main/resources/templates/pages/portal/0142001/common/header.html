<div class="fix_top1 head">
    <div class="mid clearfix">
        <a href="${request.contextPath}/"><img src="/portal/lgd/img/logo.png" class="logo"></a>
        <div class="fr">
            <a href="###" class="topb" onclick="checkLogin()"><img src="/portal/lgd/img/hicon1.png" class="topicon" />
            	<#if Session["session-student-user"].user??>
                    个人中心[<span style="display: inline-block;width: 100px;height: 44px;">
                        <span style="position: absolute;white-space: nowrap;width: 100px;overflow: hidden;text-overflow: ellipsis;">
                            ${Session["session-student-user"].info.name}
                        </span>
                    </span>]
                <#else>
                    学员登录
                </#if>
            </a>
            <a href="###" class="topb wxtopa">
				<img src="/portal/lgd/img/hicon2.png" class="topicon" />手机网页
				<div class="wxtc">
					<img src="${request.contextPath}/portal/weixin/qrcode" class="ewm">
					<p class="ewmp">微信扫码访问</p>
				</div>
			</a>
            <#if currentHostOrg.appletQrCode??>
                <a href="###" class="topb wxtopa">
                    <img src="/portal/lgd/img/hicon3.png" class="topicon" />微信小程序
                    <div class="wxtc">
                        <img src="${currentHostOrg.appletQrCode}" class="ewm">
                        <p class="ewmp">微信扫码</p>
                    </div>
                </a>
            </#if>
            <a <#if adminDomain==studentDomain>href="${adminDomain}admin-web/"<#else>href="${adminDomain}"</#if> target="_blank" class="topb"><img src="/portal/lgd/img/hicon4.png" class="topicon" />管理平台</a>
        </div>
    </div>
</div>

<div class="fix_top2 nav2">
    <div class="mid clearfix">
        <ul>
            <li id="index">
            	<a href="${request.contextPath}/portal/${currentHostOrg.code}/index" class="nava">首页</a>
            </li>
            <li id="profileIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/projectList" class="nava">培训项目</a>
            </li>
            <li id="noticeIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/noticeIndex?categoryId=TZGG" class="nava">通知公告</a>
            </li>
            <li id="newsIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/newsIndex?categoryId=ZXXW" class="nava">新闻资讯</a>
            </li>
            <li id="teachersIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/teachersIndex" class="nava">师资力量</a>
            </li>
            <li id="courseIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/courseList" class="nava">在线学堂</a>
            </li>
            <li id="ourclassIndex">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/credentialQuery" class="nava">证书查询</a>
            </li>
            <li id="contactus">
                <a href="${request.contextPath}/portal/${currentHostOrg.code}/contactus" class="nava">关于我们</a>
            </li>
            <li id="dxks">
                <a href="http://wsdx.whut.edu.cn/" target="_blank" class="nava">党校考试</a>
            </li>
        </ul>
    </div>
</div>
<!--单点登录完善手机号-->
<div class="tczz" style="display: none">
    <div class="tccon save_mobile">
<#--        <img src="/portal/lgd/img/close.png" class="close"/>-->
        <div class="fz3">完善个人信息</div>
        <div class="dinpbox clearfix mt20">
            <img src="/portal/lgd/img/dicon1.png" class="dicon">
            <input class="input1" name="name"  placeholder="请输入姓名"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon5.png" class="dicon">
            <input class="input1" name="mobile"  placeholder="请输入手机号"/>
        </div>
        <div class="clearfix mt15">
        	<div class="dinpbox2 clearfix">
	        	<img src="/portal/lgd/img/dicon3.png" class="dicon">
	            <input class="input2" name="graphic_code2" maxlength="4" placeholder="请输入图形码">
            </div>
			<a href="javascript:refYzm();">
				<img style="width:120px;height:40px;position:absolute;left:280px;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码" />
			</a>						
		</div>
        <div class="clearfix mt15">
            <div class="dinpbox2 clearfix">
                <img src="/portal/lgd/img/dicon3.png" class="dicon">
                <input class="input2" name="code" placeholder="输入验证码"/>
            </div>
            <button onclick="getSaveInfoCode()" id="saveMobileCodeBtn" class="txyzm">获取验证码</button>
        </div>
        <button class="button1" onclick="saveInfo()">确定</button>
        <p class="dlp" style="text-align:left">请完善您的个人信息，方便后续的培训事宜</p>
    </div>
</div>
<!--登录-->
<div class="tczz" id="tc1" style="display: none;">
    <div class="tccon">
        <img src="/portal/lgd/img/close.png" class="close"/>
        <div class="fz3">学员登录</div>
        <div class="dinpbox clearfix mt20">
            <img src="/portal/lgd/img/dicon1.png" class="dicon">
            <input class="input1" name="username" id="user_name" placeholder="用户名（身份证号）"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon2.png" class="dicon">
            <input class="input1" name="password" type="password" id="user_password"  placeholder="密码（身份证后六位）"/>
        </div>
        <div class="clearfix mt15">
            <div class="dinpbox2 clearfix">
                <img src="/portal/lgd/img/dicon3.png" class="dicon">
                <input class="input2" name="user_code" id="user_code" placeholder="输入验证码"/>
            </div>
            <img src="${request.contextPath}/portal/imgCode/yzm" onclick="javascript:refYzm();" alt="点击刷新验证码" class="txyzm img_verifycode">
        </div>
        <button class="button1" onclick="dologin()">登录</button>
        <p class="dlp" style="text-align:left">没有账号？<a href="javascript:void(0)" class="dla" id="click1">马上注册</a><a style="float:right" href="javascript:openReset()" class="dla">忘记密码</a></p>
    </div>
</div>
<!--注册-->
<div class="tczz" id="tc2" style="display: none;">
    <div class="tccon register">
        <img src="/portal/lgd/img/close.png" class="close"/>
        <div class="fz3">学员注册</div>
        <div class="dinpbox clearfix mt20">
            <img src="/portal/lgd/img/dicon1.png" class="dicon">
            <input class="input1" name="name" placeholder="请输入姓名"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon4.png" class="dicon">
            <input class="input1" name="sfzh" placeholder="请输入身份证号"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon5.png" class="dicon">
            <input class="input1" name="mobile" placeholder="请输入手机号"/>
        </div>
        <div class="clearfix mt15">
        	<div class="dinpbox2 clearfix">
	        	<img src="/portal/lgd/img/dicon3.png" class="dicon">
	            <input class="input2" name="graphic_code" maxlength="4" placeholder="请输入图形码">
            </div>
			<a href="javascript:refYzm();">
				<img style="width:120px;height:40px;position:absolute;left:280px;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码" />
			</a>						
		</div>
        <div class="clearfix mt15">
            <div class="dinpbox2 clearfix">
                <img src="/portal/lgd/img/dicon3.png" class="dicon">
                <input class="input2" name="code" placeholder="请输入验证码"/>
            </div>
            <button onclick="sendCode()" id="sendCodeBtn" class="txyzm">获取验证码</button>
        </div>
        <button class="button1" onclick="regist()">注册</button>
        <p class="dlp">已有账号？<a href="###" class="dla" id="click2">立即登录</a></p>
    </div>
</div>
<!--忘记密码-->
<div class="tczz" id="resetpassbox" style="display: none;">
    <div class="tccon">
        <img src="/portal/lgd/img/close.png" class="close"/>
        <div class="fz3">忘记密码</div>
        <div class="dinpbox clearfix mt20">
            <img src="/portal/lgd/img/dicon5.png" class="dicon">
           	<input class="input1" name="mobile1" placeholder="请输入您的手机号"  onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')"/>
        </div>
        <div class="clearfix mt15">
        	<div class="dinpbox2 clearfix">
	        	<img src="/portal/lgd/img/dicon3.png" class="dicon">
	            <input class="input2" name="graphic_code1" maxlength="4" placeholder="请输入图形码">
            </div>
			<a href="javascript:refYzm();">
				<img style="width:120px;height:40px;position:absolute;left:280px;" src="${request.contextPath}/portal/imgCode/yzm" class="img_verifycode" alt="点击刷新验证码" />
			</a>						
		</div>
        <div class="clearfix mt15">
            <div class="dinpbox2 clearfix">
                <img src="/portal/lgd/img/dicon3.png" class="dicon">
                <input class="input2" name="code1" placeholder="请输入验证码"/>
            </div>
            <button onclick="sendResetCode()" id="sendCodeBtn1" class="txyzm">获取验证码</button>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon2.png" class="dicon">
            <input class="input1" type="password" name="newPassword" placeholder="请输入新密码"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon2.png" class="dicon">
            <input class="input1" type="password" name="password1" placeholder="请输入确认密码"/>
        </div>
        <button class="button1" onclick="resetPasswords()">重置密码</button>
    </div>
</div>
<!--弱密码修改-->
<div class="tczz" id="rawpassbox" style="display: none;">
    <div class="tccon">
        <img src="/portal/lgd/img/close.png" class="close"/>
        <div class="fz3">您的密码过于简单请重新设置</div>
        <div style="color:red;font-size:12px;">新密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合</div>
        <div class="dinpbox clearfix mt20">
            <img src="/portal/lgd/img/dicon2.png" class="dicon">
            <input class="input1" type="password" name="strongPassword" placeholder="请输入新密码"/>
        </div>
        <div class="dinpbox clearfix mt15">
            <img src="/portal/lgd/img/dicon2.png" class="dicon">
            <input class="input1" type="password" name="strongPasswordConfirm" placeholder="请输入确认密码"/>
        </div>
        <button class="button1" onclick="strongPasswords()">保存</button>
    </div>
</div>
<div class="fix_bottom_right">
    <img class="scroll-to-top" id="scrollToTopBtn" title="返回顶部" src="/portal/st/img/totop.png">
</div>
<div style="margin-top: 149px"></div>
<style>
    /*!* 固定顶部第一个容器 *!*/
    .fix_top1 {
        position: fixed;
        top: 0;
        left: 0;
        background-color: #fff;
        height: 80px;
        width: 100%;
        z-index: 20; /* 确保在其他内容之上 */
    }

    /* 固定顶部第二个容器 */
    .fix_top2 {
        position: fixed;
        top: 80px; /* 在第一个容器下方显示 */
        left: 0;
        height: 54px;
        width: 100%;
        z-index: 10; /* 确保在第一个容器下方 */
        background-color: #0266C1;
    }

    .fix_bottom_right {
        position: fixed;
        right: 24px;
        bottom: 64px;
        background-color: rgb(251, 251, 251);
        width: 44px;
        border-radius: 22px;
        z-index: 10;
    }

    .scroll-to-top {
        width: 100%;
        border-radius: 50%;
        padding: 10px 0;
        cursor: pointer;
        display: none;
    }
</style>
<script>
	function close(){
		$("#tc1").hide();
		$("#tc2").hide();
		$("#resetpassbox").hide();
		$("#rawpassbox").hide();
	}
	function openLogin(){
		$("#tc2").hide();
		$("#resetpassbox").hide();
		$("#tc1").show();
	}
	function openRegist(){
		$("#tc1").hide();
		$("#resetpassbox").hide();
		$("#tc2").show();
	}
	function openReset(){
		$("#tc1").hide();
		$("#tc2").hide();
		$("#resetpassbox").show();
	}
    function checkLogin(){
    	var studentId = '${Session["session-student-user"].user.id}';
    	if(!utils.isEmpty(studentId)){
			window.location.href = "${request.contextPath}/personal/home";
		}else{
			openLogin();
		}
    }
    function refYzm(){
    	$(".img_verifycode").attr("src", "${request.contextPath}/portal/imgCode/yzm?t=" + Math.random());
    }
    $(".close").click(function(){
        $(this).parent().parent(".tczz").hide();
    })

    $("#click1").click(function(){
    	openRegist();
    })
    $("#click2").click(function(){
    	openLogin();
    })
    function sendResetCode(){
		var mobile = $('input[name="mobile1"]').val();
		if(utils.isEmpty(mobile)){
			layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='mobile1']").focus();}});
			return;
		}
		var graphic_code = $('input[name="graphic_code1"]').val();
        if (utils.isEmpty(graphic_code)) {
            layer.alert('请输入图形码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='graphic_code1']").focus();
                }
            });
            return;
        }
		$.ajax({
			async:false,
			type: "POST",
			url: "${request.contextPath}/portal/sendResetVerifyCode",
			data: {
				"t": Math.random(),
                "mobile": mobile,
                "graphic_code": graphic_code
			},
			dataType: "json",
			success: function(ret){
				layer.closeAll('loading');
				if(0 == ret["code"]){
					layer.alert('验证码已发送',{icon:6,end:function(){
							//倒计时 90秒
							resetCodeInterval();
							refYzm();
						}});
				}
				else{
					layer.alert(ret['message'],{icon:5});
				}
			},
			error:function(ret){
				layer.closeAll('loading');
				layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5},function(){window.location.reload();});
			}
		});
	}
  	//发送短信验证码
	function resetCodeInterval(){
		var code = $("#sendCodeBtn1");
		code.attr("href","javascript:void(0)");
		var time = 90;
		var set=setInterval(function(){
			code.html("("+--time+")秒后重新获取");
		}, 1000);
		setTimeout(function(){
			code.attr("href","javascript:sendResetCode()");
			code.html("重新获取验证码");
			clearInterval(set);
		}, 90000);
	}
	function resetPasswords(){
		var mobile = $('input[name="mobile1"]').val();
		if(!mobile){
			layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='mobile1']").focus();}});
			return;
		}
		if(!utils.isMobileNo(mobile)){
			layer.alert('请输入正确的手机号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='mobile1']").focus();}});
			return;
		}
		var newPassword = $('input[name="newPassword"]').val();
		var password = $('input[name="password1"]').val();
		if(!newPassword){
			layer.alert('请输入您的密码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='newPassword']").focus();}});
			return;
		}
		if(!password){
			layer.alert('请输入确认密码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='password']").focus();}});
			return;
		}
		if(newPassword!=password){
			layer.alert('密码不一致，请重新确认密码！', {icon: 5,btn:['知道了']});
			return;
		}
		var code = $('input[name="code1"]').val();
		if(!code){
			layer.alert('请输入短信验证码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='code1']").focus();}});
			return;
		}
		layer.load();
		$.ajax({
			async:false,
			type: "POST",
			url: "${request.contextPath}/portal/resetPasswords",
			data: {
				"t" : Math.random(),
				mobile:mobile,
				newPassword:newPassword,
				password:password,
				code:code
			},
			dataType: "json",
			success: function(ret){
				layer.closeAll('loading');
				if(0 == ret["code"]){
					layer.alert('密码重置成功',{icon:6},function() {
						layer.closeAll();
						openLogin();
					});
				}
				else{
					layer.alert(ret['message'],{icon:5});
				}
			},
			error:function(ret){
				layer.closeAll('loading');
				layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon:5});
			}
		});
	}
	function strongPasswords(){
		var _username = $("#user_name").val();
		var _password = $("#user_password").val();
		var strongPassword = $('input[name="strongPassword"]').val();
		var strongPasswordConfirm = $('input[name="strongPasswordConfirm"]').val();
		if(!strongPassword){
			layer.alert('请输入您的密码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='strongPassword']").focus();}});
			return;
		}
		if(!strongPasswordConfirm){
			layer.alert('请输入确认密码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='strongPasswordConfirm']").focus();}});
			return;
		}
		if(strongPassword!=strongPasswordConfirm){
			layer.alert('密码不一致，请重新输入！', {icon: 5,btn:['知道了']});
			return;
		}
		layer.load();
		$.ajax({
			async:false,
			type: "POST",
			url: "${request.contextPath}/portal/strongPasswords",
			data: {
				"t" : Math.random(),
				username:_username,
				oldPassword:_password,
				password:strongPassword
			},
			dataType: "json",
			success: function(ret){
				layer.closeAll('loading');
				if(0 == ret["code"]){
					layer.alert('密码修改成功',{icon:6},function() {
						layer.closeAll();
						close();
						window.location.reload();
					});
				}
				else{
					layer.alert(ret['message'],{icon:5});
				}
			},
			error:function(ret){
				layer.closeAll('loading');
				layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon:5});
			}
		});
	}

    function dologin() {
		var _username = $("#user_name").val();
		var _password = $("#user_password").val();
		var _code = $("#user_code").val();
		if(utils.isEmpty(_username)){
			layer.alert('请输入用户名', {icon:5});
			return;
		}
		if(utils.isEmpty(_password)){
			layer.alert('请输入密码', {icon:5});
			return;
		}
		if(utils.isEmpty(_code)){
			layer.alert('请输入验证码', {icon:5});
			return;
		}
		layer.load();
	    $.ajax({
	      async:false,
	      type: "POST",
	      url: "${request.contextPath}/portal/student/login",
	      data: {"t" : Math.random(),"username":_username,"password":_password,"code":_code},
	      dataType: "json",
	      success: function(ret){
	          layer.closeAll('loading');
	          if(0 == ret["code"]){
	        	  if(ret.data.isRawPassword && ret.data.isRawPassword == 1){
	        		  close();
	        		  $('#rawpassbox').show();
	        	  }
	        	  else{
	      			//document.location.href="${request.contextPath}/personal/home";
                    localStorage.setItem("csrf-token", ret.data.csrfToken);
					window.location.reload();
	        	  }
	          }
	          else{
	              layer.alert(ret['message'], {icon:5});
	          }
	      },
	      error:function(ret){
	          layer.closeAll('loading');
	          layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon:5});
	      }
	  });
	}
	//发送短信验证码
	function codeInterval(){
		var code = $("#sendCodeBtn");
		code.attr("href","javascript:void(0)");
		var time = 90;
		var set=setInterval(function(){
			code.html("("+--time+")秒后重新获取");
		}, 1000);
		setTimeout(function(){
			code.attr("href","javascript:sendCode()");
			code.html("重新获取验证码");
			clearInterval(set);
		}, 90000);
	}

	function sendCode(){
		var mobile = $('.register input[name="mobile"]').val();
		if(utils.isEmpty(mobile)){
			layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$(".register input[name='mobile']").focus();}});
			return;
		}
		var graphic_code = $('input[name="graphic_code"]').val();
        if (utils.isEmpty(graphic_code)) {
            layer.alert('请输入图形码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='graphic_code']").focus();
                }
            });
            return;
        }
		$.ajax({
	        async:false,
	        type: "POST",
	        url: "${request.contextPath}/portal/sendVerifyCode",
	        data: {
	        	"t": Math.random(),
                "mobile": mobile,
                "graphic_code": graphic_code
			},
	        dataType: "json",
	        success: function(ret){
	            layer.closeAll('loading');
	            if(0 == ret["code"]){
	               	layer.alert('验证码已发送',{icon:6,end:function(){
	               		//倒计时 90秒
	                	codeInterval();
	                	refYzm();
	               	}});
	            }
	            else{
	                layer.alert(ret['message'],{icon:5});
	            }
	        },
	        error:function(ret){
	            layer.closeAll('loading');
				layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5},function(){window.location.reload();});
	        }
	    });
	}


    //发送短信验证码
    function codeInterval2(){
        var code = $("#saveMobileCodeBtn");
        code.attr("href","javascript:void(0)");
        var time = 90;
        var set=setInterval(function(){
            code.html("("+--time+")秒后重新获取");
        }, 1000);
        setTimeout(function(){
            code.attr("href","javascript:getSaveMobileCode()");
            code.html("重新获取验证码");
            clearInterval(set);
        }, 90000);
    }

    function getSaveInfoCode(){
        var mobile = $('.save_mobile input[name="mobile"]').val();
        if(utils.isEmpty(mobile)){
            layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$(".save_mobile input[name='mobile']").focus();}});
            return;
        }
        var graphic_code = $('input[name="graphic_code2"]').val();
        if (utils.isEmpty(graphic_code)) {
            layer.alert('请输入图形码！', {
                icon: 5, btn: ['知道了'], end: function () {
                    $("input[name='graphic_code2']").focus();
                }
            });
            return;
        }
        $.ajax({
            async:false,
            type: "POST",
            url: "${request.contextPath}/portal/sendSaveInfoVerifyCode",
            data: {
            	"t": Math.random(),
                "mobile": mobile,
                "graphic_code": graphic_code
            },
            dataType: "json",
            success: function(ret){
                layer.closeAll('loading');
                if(0 == ret["code"]){
                    layer.alert('验证码已发送',{icon:6,end:function(){
                            //倒计时 90秒
                            codeInterval2();
                            refYzm();
                        }});
                }
                else{
                    layer.alert(ret['message'],{icon:5});
                }
            },
            error:function(ret){
                layer.closeAll('loading');
                layer.alert('系统异常，请刷新后再试，如还有异常，请与技术服务人员联系！', {icon: 5},function(){window.location.reload();});
            }
        });
    }

    function saveInfo(){
        var name = $('.save_mobile input[name="name"]').val();
        if(utils.isEmpty(name)){
            layer.alert('请输入您的姓名！', {icon: 5,btn:['知道了'],end:function(){$(".save_mobile input[name='name']").focus();}});
            return;
        }
        var mobile = $('.save_mobile input[name="mobile"]').val();
        if(utils.isEmpty(mobile)){
            layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$(".save_mobile input[name='mobile']").focus();}});
            return;
        }
        if(!utils.isMobileNo(mobile)){
            layer.alert('请输入正确手机号！', {icon: 5,btn:['知道了'],end:function(){$(".save_mobile input[name='mobile']").focus();}});
            return;
        }
        var code = $('.save_mobile input[name="code"]').val();
        if(utils.isEmpty(code)) {
            layer.alert('请输入短信验证码！', {icon: 5,btn:['知道了'],end:function(){$(".save_mobile input[name='code']").focus();}});
            return;
        }
        layer.load();
        $.ajax({
            async:false,
            type: "POST",
            url: "${request.contextPath}/portal/saveInfo",
            data: {
                "t" : Math.random(),
                name: name,
                mobile: mobile,
                code: code
            },
            dataType: "json",
            success: function(ret){
                layer.closeAll('loading');
                if(0 == ret["code"]){
                    layer.alert('操作成功',{icon:6});
                    $('.save_mobile').parent('.tczz').hide();
                }
                else{
                    layer.alert(ret['message'],{icon:5});
                }
            },
            error:function(ret){
                layer.closeAll('loading');
                layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon:5});
            }
        });
    }

	function regist(){
		var name = $('.register input[name="name"]').val();
		if(!name){
			layer.alert('请输入您的姓名！', {icon: 5,btn:['知道了'],end:function(){$("input[name='name']").focus();}});
			return;
		}
		var sfzh = $('.register input[name="sfzh"]').val();
		if(!sfzh){
			layer.alert('请输入您的身份证号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='sfzh']").focus();}});
			return;
		}
		if(!utils.isCardNo(sfzh)){
			layer.alert('请输入正确身份证号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='sfzh']").focus();}});
			return;
		}
		var mobile = $('.register input[name="mobile"]').val();
		if(!mobile){
			layer.alert('请输入您的手机号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='mobile']").focus();}});
			return;
		}
		if(!utils.isMobileNo(mobile)){
			layer.alert('请输入正确的手机号！', {icon: 5,btn:['知道了'],end:function(){$("input[name='mobile']").focus();}});
			return;
		}
		var code = $('.register input[name="code"]').val();
		if(!code){
			layer.alert('请输入短信验证码！', {icon: 5,btn:['知道了'],end:function(){$("input[name='code']").focus();}});
			return;
		}
		layer.load();
		$.ajax({
	          async:false,
	          type: "POST",
	          url: "${request.contextPath}/portal/doRegister",
	          data: {
	        	  "t" : Math.random(),
	        	  name:name,
	        	  sfzh:sfzh,
	        	  mobile:mobile,
	        	  code:code
				},
	          dataType: "json",
	          success: function(ret){
	              layer.closeAll('loading');
	              if(0 == ret["code"]){
					  layer.alert('注册成功，默认密码为Abc@加您的手机号或身份证后六位！',{icon:6});
	              }
	              else{
	                  layer.alert(ret['message'],{icon:5});
	              }
	          },
	          error:function(ret){
	              layer.closeAll('loading');
	              layer.alert('请求处理失败，请联系系统管理员解决此问题', {icon:5});
	          }
	      });
	}


    document.addEventListener('DOMContentLoaded', function() {
        var scrollToTopBtn = document.getElementById('scrollToTopBtn');

        // 显示/隐藏按钮
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.style.display = 'block'; // 显示按钮
            } else {
                scrollToTopBtn.style.display = 'none'; // 隐藏按钮
            }
        });

        // 点击按钮时滚动到顶部
        scrollToTopBtn.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    });

    $(document).ready(function (e) {
        <#if Session["session-student-user"].user?? && Session["session-student-user"].isSso == '1' && !Session["session-student-user"].info.mobile??>
               $('.save_mobile').parent('.tczz').show();
        </#if>
        //登录enter事件
        $('#tc1').keydown(function(event) {
            if (event.key === 'Enter') {
                const emptyInputs = $(this).find('input').filter(function() {
                    return $(this).val() === '';
                });
                if (emptyInputs.length === 0) {
                    dologin();
                } else {
                    // 聚焦到第一个未填写的子元素输入框
                    emptyInputs.first().focus();
                }
            }
        });

        //注册enter事件
        $('#tc2').keydown(function(event) {
            if (event.key === 'Enter') {
                const emptyInputs = $(this).find('input').filter(function() {
                    return $(this).val() === '';
                });
                if (emptyInputs.length === 0) {
                    regist();
                } else {
                    // 聚焦到第一个未填写的子元素输入框
                    emptyInputs.first().focus();
                }
            }
        });
    });

</script>
