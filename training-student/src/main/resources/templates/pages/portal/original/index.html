<!DOCTYPE html>
<html>
<#include "pages/portal/original/common/top.html">
	<body class="bg1">
	<#include "pages/portal/original/common/header.html">
		<div class="banner_box">
			<div class="fix_top2 indexnav">
				<div class="mid">
					<a href="${request.contextPath}/portal/index" class="nava on">首页</a>
					<a href="${request.contextPath}/portal/trainingIndex" class="nava">培训项目</a>
					<a href="${request.contextPath}/portal/courseIndex" class="nava">网上课件</a>
					<a href="${request.contextPath}/portal/zhiboIndex" class="nava">在线课堂</a>
					<a href="${request.contextPath}/portal/questionDbIndex" class="nava">在线题库</a>
					<a href="${request.contextPath}/portal/teacherIndex" class="nava">师资列表</a>
					<a href="${request.contextPath}/portal/newsIndex" class="nava">新闻资讯</a>
					<a href="${request.contextPath}/portal/aboutus" class="nava">关于我们</a>
				</div>
			</div>
		</div>
		<div class="banner_box" style="margin-top: 145px">
			<div class="swiper-container" id="swiper1" >
				<div class="swiper-wrapper">
					<#if (bannerList?? && bannerList?size > 0)>
						<#list bannerList as syslbt>
							<div class="swiper-slide">
								<a href="${syslbt.link}"  target='_BLANK'>
									<img src="${syslbt.url}" class="banner">
								</a>
							</div>
						</#list>
					<#else>
						<div class="swiper-slide">
							<a href="javascript:void(0)">
								<img src="/portal/original/img/banner-default.png" class="banner">
							</a>
						</div>
					</#if>
			</div>
			<!-- 如果需要分页器 -->
			<div class="swiper-pagination"></div>
			</div>
		</div>
		<div class="mid section1">
			<div class="left_xm">
				<h2 class="xmh2">热门项目<a href="${requet.contextPath}/portal/trainingIndex" class="xmmore">更多></a></h2>
				<div class="clearfix">
					<#if xmList?? && xmList?size gt 0>
					<#list xmList as xm>
					<div class="xmbox">
						<div class="xmtu" style="cursor:pointer; background-image: url(<#if xm.xct==null>/portal/original/img/pxxm.png<#else>${xm.xct}</#if>)" onclick="window.open('${request.contextPath}/portal/trainingDetail?id=${xm.id}','_self')">
						</div>
						<div class="xmcon">
							<h3 class="xmname">${xm.title}</h3>
							<p class="xmtime">${xm.totalHours}学时&nbsp;&nbsp; ｜ &nbsp;&nbsp;${xm.trainees}</p>
							<div class="clearfix">
								<p class="prize">
									<#if xm.amount?? && xm.amount!="" && xm.amount!="0">
										<span>￥</span>${xm.amount}
									<#else>
										免费
									</#if>
								</p>
								<p class="bmshu">已有${xm.bmCount}人报名</p>
							</div>
						</div>
					</div>
					</#list>
					</#if>
				</div>
			</div>
			<div class="right_xw" >
				<div class="xwbox">
					<div class="tab clearfix">
						<!-- 新闻分类列表 -->
						<#list categoryList as category>
							<#if category_index lt 4>
							<div id="a${category_index+1}" class="tabli <#if category_index==0>on</#if>" onclick="fun(${category_index+1},${categoryList?size})">${category.name}</div>
							</#if>
						</#list>
					</div>
				<#list newsList as newsItem>
					<div <#if newsItem_index!=0> style="display: none;"</#if>  id="a-${newsItem_index+1}">
						<#if (newsItem?? && newsItem?size > 0) >
							 <#list newsItem as item>
									<div class="xwli">
										<a  <#if item.isLink?? && item.isLink == '1'>href="${item.linkUrl}" <#else>href="${request.contextPath}/portal/newsDetail?id=${item.id}"</#if> class="xwp" target= "_blank">${item.title}</a>
										<p class="xwtime">${item.createTime?date}</p>
									</div>
							 </#list>
						</#if>
					</div>
				</#list>
				</div>
				<a href="${request.contextPath}/portal/newsIndex" class="xwmore">
					<img src="/portal/original/img/rjt.png" class="rjt">
					查看更多
				</a>
				</div>
		</div>
		<div class="mid">
			<h2 class="xmh2">推荐课程<a href="${request.contextPath}/portal/courseIndex" class="xmmore">更多></a></h2>
			<div class="clearfix">
				<#if  courseList?? && courseList?size gt 0>
				<#list courseList as kc>
					 <#if kc_index lt 8>
						<div class="kcbox1">
							<#if kc.logo??>
								<div class="kctu" style="background-image: url(${kc.logo}) ;cursor:pointer;color:#FFF" onclick="window.open(' ${request.contextPath}/portal/coursePlay?id=${kc.id}','_self')" ></div>
							<#else>
								<div class="kctu" style="background-image: url(/portal/original/img/pxkc.png) ;cursor:pointer;color:#FFF" onclick="window.open(' ${request.contextPath}/portal/coursePlay?id=${kc.id}','_self')" >
									${kc.name}
								</div>
							</#if>
							
							<div class="xmcon">
								<h3 class="xmname">${kc.name}</h3>
								<p class="xmtime">${kc.hours}学时&nbsp;&nbsp; ｜ &nbsp;&nbsp;<img src="/portal/original/img/lstx.png" class="lstx">${kc.kj.teacherName}</p>
								<div class="clearfix">
									<p class="prize">
										<#if kc.amount?? && kc.amount!="" && kc.amount!="0">
											<span>￥</span>${kc.amount}
										<#else>
											免费
										</#if>
									</p>
									<p class="bmshu">已有${kc.count}人学习</p>
								</div>
							</div>
						</div>
					</#if>
				</#list>
				</#if>
			</div>
		</div>
		<div class="mid">
			<h2 class="xmh2">优秀师资</h2>
			<div class="clearfix">
				<#list teacherList as teacher>
					<#if teacher_index lt 4>
						<div class="shili" style="background: #FFFFFF;cursor:pointer;" onclick="window.open('${request.contextPath}/portal/teacherDetail?id=${teacher.id}','_self')">
							<img src="${teacher.photo!'/portal/original/img/teacher.png'}" class="shitu">
							<div class="sname" style="color: #333;">${teacher.name}</div>
							<div class="szhi" style="color: #333;">${teacher.zw}</div>
						</div>
					</#if>
				</#list>
			</div>
		</div>
			<#include "pages/portal/original/common/footer.html">
	</body>
	<script>      
  var mySwiper = new Swiper ('#swiper1', {
    direction: 'horizontal', // 水平切换选项
    loop: true, // 循环模式选项
    autoplay:true,
    // 如果需要分页器
    pagination: {
      el: '.swiper-pagination',
    },   
  })    
  	
  	var mySwiper = new Swiper ('#swiper2', {
    direction: 'horizontal', // 水平切换选项
    loop: true, // 循环模式选项
    slidesPerView: 'auto',
    loopedSlides: 9,
    // 如果需要前进后退按钮
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  })    
  
  var mySwiper = new Swiper ('#swiper3', {
    direction: 'horizontal', // 水平切换选项
    loop: true, // 循环模式选项
    autoplay: {
	    delay: 8000,//8秒切换一次
	  },
    // 如果需要分页器
    pagination: {
      el: '.swiper-pagination',
    },   
  })    
  	
  	//新闻切换
  	function fun(curindex,allnum){
	for(var i=1;i<=allnum;i++){
	$("#a"+i).removeClass("on");
    	$("#a-"+i).hide();	
    }
		$("#a-"+curindex).show();
		$("#a"+curindex).addClass("on");
	}	
	//角色切换
  	function fun1(curindex,allnum){
	for(var i=1;i<=allnum;i++){
	$("#b"+i).removeClass("on");
    	$("#b-"+i).hide();	
    }
		$("#b-"+curindex).show();
		$("#b"+curindex).addClass("on");
	}	
	</script>
</html>
