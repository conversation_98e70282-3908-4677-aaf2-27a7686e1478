<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentHostOrg.portalSysName}</title>
  <link rel="shortcut icon" type="image/x-icon" href="${currentHostOrg.adminLogo}" />
  <link rel="stylesheet" href="/center/css/common.css" />
  <link rel="stylesheet" href="/center/css/zkzy.css" />
  <script type="text/javascript" src="/portal/lgd/js/jquery-1.9.1.min.js"></script>
  <script type="text/javascript" src="/center/js/pager.js"></script>
</head>
<style>
  body {
    background-color: #f2f6f9;
  }
</style>

<body>
<#include "pages/portal/center/common/header.html">
  <div class="top-box"></div>
  <div class="mid pr">
    <div class="mianbao clearfix">
      <div class="fl">
        <a href="${request.contextPath}/" class="miaobaoa">首页</a>
        <span class="miaobaosp">&gt;</span>
        <div class="site">自考专业</div>
        <span class="miaobaosp">&gt;</span>
        <div class="site">详情</div>
      </div>
    </div>
  </div>
  <div class="mid mb3">
    <div class="details-top">
      <img src="${specialty.sImgUrl!'/center/img/zkzy/Mask group(9).png'}" class="details-logo">
      <div class="top-box">
        <div class="top-title">
          <span class="title-name">${specialty.sCode} - ${specialty.sNameStr}</span>
          <span class="title-tip"><#if item.sEdulevel == 'A'>专科<#else>本科</#if></span>
        </div>
        <div class="top-info">
          ${specialty.sIntroduction}
        </div>
      </div>
    </div>
  </div>
  <div class="mid">
    <div class="details-bottom">
      <div class="bottom-title">专业课程</div>
      <div class="bottom-info">
        <table align="center" border="1" cellspacing="0">
          <thead align="center" bgcolor="#F6F8FF">
            <tr>
              <th width="140">课程类别</th>
              <th width="100">序号</th>
              <th width="200">课程代码</th>
              <th width="200">课程名称</th>
              <th width="140">课程学分</th>
              <th width="140">备注</th>
              <th width="200">最低学分/学分小计</th>
            </tr>
          </thead>
          <tbody align="center">
            <#list specialty.courseTypes as courseType>
              <#list courseType.courseChildTypes as courseChildType>
                <#list courseChildType.specialtyCourses as specialtyCourse>
                  <#list specialtyCourse.course as c>
                    <#if courseType.spctStatus == 0>
                      <tr>
                        <#if specialtyCourse_index == 0>
                          <td <#if specialtyCourse_index == 0>rowspan="${courseChildType.specialtyCourses?size}"</#if>>${courseType.typeName}</td>
                        </#if>
                        <td colspan="6">${courseType.spctExplain}</td>
                      </tr>
                    <#elseif courseType.spctStatus == 1>
                      <tr>
                        <#if specialtyCourse_index == 0>
                          <td <#if specialtyCourse_index == 0>rowspan="${courseChildType.specialtyCourses?size}"</#if>>${courseType.typeName}</td>
                        </#if>
                        <td>${specialtyCourse_index+1}</td>
                        <#if specialtyCourse.spcIsDegree == 1>
                          <td class="table-xue">
                            ${c.cCode}
                            <img src="/center/img/zkzy/xue.png" alt="" class="img-xue">
                          </td>
                        <#else>
                          <td>${c.cCode}</td>
                        </#if>
                        <td>${c.cName}</td>
                        <td>${c.cScore}</td>
                        <#if specialtyCourse_index == 0>
                          <td <#if specialtyCourse_index == 0>rowspan="${courseChildType.specialtyCourses?size}"</#if>>${courseChildType.spcctRemark}</td>
                          <td <#if specialtyCourse_index == 0>rowspan="${courseChildType.specialtyCourses?size}"</#if>>${courseType.spctMinScore} / ${courseType.spctSumScore}</td>
                        </#if>
                      </tr>
                    </#if>
                  </#list>
                </#list>
              </#list>
            </#list>
            <tr>
              <td>其他要求</td>
              <td colspan="6">${specialty.sRequire}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <!-- 页脚 -->
<#include "/pages/portal/center/common/footer.html">
</body>
<script>
  (function () {
    $(".right_Tab").children().each(function() {
      $(this).removeClass("right_Tab_Check");
    });
    $("#specialtyIndex").attr("class", "right_Tab_Check");
  })()
</script>
</html>