<!DOCTYPE html>
<html lang="en">
  <#include "pages/portal/center/common/top.html">
  <body>
  <#include "pages/portal/center/common/header.html">
    <!-- 轮播图 -->
    <div class="carousel" style="height: 500px">
      <div class="carousel-images">
        <#list bannerList as item>
          <a href="${item.link}" target="_blank"
          ><img src="${item.url}" style="height: 440px;"
            /></a>
        </#list>
      </div>
      <div class="carousel-m">
        <#list bannerList as item>
          <#if item_index == 0>
            <span class="carousel-m-check"></span>
          <#else>
            <span class=""></span>
          </#if>
        </#list>
      </div>
    </div>
    <!-- 理工要闻 -->
    <div class="lg-xw">
      <div class="con">
        <div class="con-header">
          <div class="con-header-t">
            <img src="/center/img/index/t-1.png" alt="" />
            <span> <font class="con-header-t-font-c">理工</font>·要闻 </span>
            <img src="/center/img/index/t-2.png" alt="" />
          </div>
          <a class="con-header-all" href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryName=新闻资讯">
            更多
            <img src="/center/img/index/all.png" alt="" width="20px" />
          </a>
        </div>

        <div class="lg-xw-con justify-between">
          <#list news as item>
            <#if item_index == 0>
              <a class="lg-xw-con-left" href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeDetail?id=${item.id}">
                <div class="lg-xw-con-left-yj">
                  <img
                          src="/center/img/index/lg-yw-zj.png"
                          alt=""
                          width="148px"
                          height="63px"
                  />
                  <div>${(item.publishTime!item.createTime)?string("yyyy-MM-dd")}</div>
                </div>
                <img
                        src="${item.url?default("/center/img/index/lg_yw_1.png")}"
                        alt=""
                        width="600px"
                        height="340px"
                />
                <div class="lg-xw-con-left-bt">
                  ${item.title}
                </div>
              </a>
            </#if>
          </#list>

          <div class="lg-xw-con-right">
            <#list news as item>
              <#if item_index gt 0 && item_index lt 6>
                <a class="lg-xw-con-right-xw" href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeDetail?id=${item.id}">
                  <div id="lg-xw-con-right-hoverd" style="display: none">
                    <div class="lg-xw-con-right-xw-time" ><font>${(item.publishTime!item.createTime)?string("yyyy")}-</font>${(item.publishTime!item.createTime)?string("MM-dd")}</div>
                    <div class="lg-xw-con-right-xw-title">
                      ${item.title}
                    </div>
                    <div class="lg-xw-con-right-xw-con">
                      ${item.content?replace('<[^>]*>?', '', 'ri')}
                    </div>
                  </div>
                  <div id="lg-xw-con-right-no-hover">
                    <div class="lg-xw-con-right-xw-t-t">
                      ${item.title}
                    </div>
                    <div class="lg-xw-con-right-xw-t-d">${(item.publishTime!item.createTime)?string("yyyy-MM-dd")}</div>
                  </div>
                </a>
              </#if>
            </#list>
          </div>
        </div>
      </div>
    </div>
    <!-- 通知公告 -->
    <div class="tz-gg">
      <div class="con">
        <div class="con-header">
          <div class="con-header-t">
            <img src="/center/img/index/t-1.png" alt="" />
            <span> <font class="con-header-t-font-c">通知</font>·公告 </span>
            <img src="/center/img/index/t-2.png" alt="" />
          </div>
          <a class="con-header-all" href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryName=通知公告">
            更多
            <img src="/center/img/index/all.png" alt="" width="20px" />
          </a>
        </div>

        <div class="justify-between p-t20 tg-kp-hover">
          <#list notices as item>
            <#if item_index lt 3>
                <a class="tg-kp" href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeDetail?id=${item.id}">
                  <div class="tg-kp-bg">
                    <img
                            class="tzgg-img-1"
                            src="/center/img/index/tzgg-1.png"
                            width="380px"
                            height="128px"
                            alt=""
                    />
                    <img
                            style="display: none"
                            class="tzgg-img-2"
                            src="/center/img/index/tzgg-2.png"
                            width="380px"
                            height="128px"
                            alt=""
                    />
                    <font>0${item_index+1}</font>
                    <div class="pr">${item.title}</div>
                  </div>
                  <div class="tg-kp-title">${(item.publishTime!item.createTime)?string("yyyy-MM-dd")}</div>
                </a>
            </#if>
          </#list>
        </div>
        <div class="justify-between p-t15 tzgg-hr">
<#--          <a href="javascript:void(0);" class="tzgg-hr-left"><</a>-->
          <div class="tzgg-hr-point" ></div>
          <div></div>
          <div></div>
<#--          <a href="javascript:void(0);" class="tzgg-hr-right">></a>-->
        </div>
        <div class="p-t15">
          <img
            src="/center/img/index/tzgg-bg.png"
            alt=""
            width="100%"
            height="127px"
          />
        </div>
      </div>
    </div>
    <!-- 热门项目 -->
    <div class="rm-xm">
      <div class="con">
        <div class="con-header">
          <div class="con-header-t">
            <img src="/center/img/index/t-1.png" alt="" />
            <span> <font class="con-header-t-font-c">热门</font>·项目 </span>
            <img src="/center/img/index/t-2.png" alt="" />
          </div>
          <a class="con-header-all" href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectList">
            更多
            <img src="/center/img/index/all.png" alt="" width="20px" />
          </a>
        </div>
        <div class="rmxm-con justify-between">
          <img src="/center/img/index/rmxm-logo.png" alt="" class="rmxm-con-left" />
          <div class="rmxm-con-right">
            <#list projectList as item>
              <a class="rmxm-con-right-bk hovertx" href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectDetail?id=${item.id}">
                <img
                        src="${item.url!"/center/img/index/xm1.png"}"
                        alt=""
                        width="233px"
                        height="131px"
                />
                <div class="rmxm-con-right-bk-title">
                  ${item.name}
                </div>
              </a>
            </#list>
          </div>
        </div>
      </div>
    </div>
    <!-- 推荐专业 -->
    <div class="tj-zy">
      <div class="con">
        <div class="con-header">
          <div class="con-header-t">
            <img src="/center/img/index/t-1.png" alt="" />
            <span> <font class="con-header-t-font-c">推荐</font>·专业 </span>
            <img src="/center/img/index/t-2.png" alt="" />
          </div>
          <a class="con-header-all" href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtys">
            更多
            <img src="/center/img/index/all.png" alt="" width="20px" />
          </a>
        </div>
        <div class="tjzy-con justify-between">
          <div class="tjzy-con-left">
            <#list specialtyList as item>
              <a class="tjzy-con-left-bk hovertx" href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtyDetail?s_id=${item.sId}">
                <img
                        src="${item.sImgUrl!'/center/img/index/zy1.png'}"
                        alt=""
                        width="233px"
                        height="131px"
                />
                <div class="tjzy-con-left-bk-title">
                  ${item.sCode} - ${item.sNameStr}
                  <div class="tjzy-con-left-bk-cc"><#if item.sEdulevel == 'A'>专科<#else>本科</#if></div>
                </div>
              </a>
            </#list>
          </div>
          <img src="/center/img/index/tjzy-logo.png" alt="" class="tjzy-con-right" />
        </div>
      </div>
    </div>
    <!-- 校园风光 -->
    <div class="xy-fg">
      <div class="con">
        <div class="con-header">
          <div class="con-header-t">
            <img src="/center/img/index/t-1.png" alt="" />
            <span> <font class="con-header-t-font-c">校园</font>·风光 </span>
            <img src="/center/img/index/t-2.png" alt="" />
          </div>
          <!-- <a class="con-header-all" href="javascript:void(0);">
            更多
            <img src="/center/img/index/all.png" alt="" width="20px" />
          </a> -->
        </div>
        <div class="xyfg-con usn">
          <div class="xyfg-scroll">
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg1.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>校艺术馆</div>
            </div>
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg2.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>校园樱花树</div>
            </div>
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg3.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>武汉理工大学教学楼</div>
            </div>
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg4.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>武汉理工大学图书馆</div>
            </div>
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg5.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>西院飞马广场</div>
            </div>
            <div class="xyfg-con-bk">
              <img
                src="/center/img/index/xyfg6.png"
                alt=""
                width="227px"
                height="305px"
              />
              <div>图书馆内景</div>
            </div>
          </div>
          <div class="xyfg-con-b"></div>
        </div>
      </div>
    </div>
    <!-- 页脚 -->
    <#include "/pages/portal/center/common/footer.html">
    <!-- 登录 -->
    <div class="dioModel" id="dioModel" >
      <div class="c-login">
        <img src="/center/img/index/close.png" class="close usn" alt="" onclick="closeModel()">
        <div class="c-login-title">统一身份登录</div>

        <div class="c-login-con">
          <div class="dlbox">
            <img src="/center/img/index/icon1.png" class="login_icon" />
            <input class="input2" placeholder="请输入用户名" />
          </div>
          <div class="dlbox">
            <img src="/center/img/index/icon2.png" class="login_icon" />
            <input class="input2" placeholder="请输入密码" />
          </div>
          <div class="zh">
            <div class="jzzh">
              <input type="checkbox" name="" id="" />
              <span class="jz">记住账号</span>
            </div>
            <a href="">忘记密码</a>
          </div>

          <button class="c-logi">立即登录</button>
          <button class="wxlogin">微信登录</button>

          <div class="l-tic">
            <span class="l-tic-t">温馨提示：</span>
            请教职工和考生妥善保管好自己的账号、密码，如果忘记密码请使用 <a href="javascript:void(0);">“忘记密码”</a> 功能进行重置
          </div>
          <div class="jy">
            <span class="jy-t">建议使用：</span>
            <a
              class="firefox_pic"
              target="_blank"
              href="http://www.firefox.com.cn/download/"
            >
              火狐
            </a>
            <a
              class="google_pic"
              target="_blank"
              href="https://google.cn/intl/zh-CN/chrome/"
            >
              谷歌
            </a>
            <a
              class="firefox_360"
              target="_blank"
              href="https://browser.360.cn/ee/"
            >
              360极速
            </a>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="/center/js/index.js"></script>
<script>
  (function () {
    $(".right_Tab").children().each(function() {
      $(this).removeClass("right_Tab_Check");
    });
    $("#index").attr("class", "right_Tab_Check");

    $(".tg-kp").hover(
      function() {
        $(this).addClass("tg-kp-check");
        $(this).find(".tzgg-img-1").hide();
        $(this).find(".tzgg-img-2").show();
      },
      function() {
        $(this).removeClass("tg-kp-check");
        $(this).find(".tzgg-img-2").hide();
        $(this).find(".tzgg-img-1").show();
      }
    );
    $(".lg-xw-con-right-xw").hover(
      function () {
        $(this).find("#lg-xw-con-right-hoverd").show();
        $(this).find("#lg-xw-con-right-no-hover").hide();
      },
      function () {
        $(this).find("#lg-xw-con-right-hoverd").hide();
        $(this).find("#lg-xw-con-right-no-hover").show();
      }
    );
  })();
</script>
</html>
