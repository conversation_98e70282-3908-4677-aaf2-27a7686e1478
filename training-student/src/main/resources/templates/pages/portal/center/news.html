<!DOCTYPE html>
<html lang="en">
<#include "pages/portal/center/common/top.html">
<body>
<#include "pages/portal/center/common/header.html">
  <div class="bannerbox">
    <img src="/center/img/xwzx/Mask group.png" class="swiper-banner">
  </div>
  <div class="infoBox pt3">
    <div class="mid">
      <div class="mianbao clearfix">
        <div class="fl">
          <a href="${request.contextPath}/" class="miaobaoa">首页</a>
          <span class="miaobaosp">&gt;</span>
          <div class="site">新闻资讯</div>
        </div>
      </div>
    </div>
    <div class="mid flex-row pb6">
      <div class="infoLeft">
        <div class="leftSelect">
          <div class="selectTitle"><img src="/center/img/xwzx/Group 1.png" alt="">教育培训</div>
          <ul class="level_one">
            <#list categorys as item>
              <#if !item.isZk??>
                <li class="node_one <#if item.id = activeCategory.id>on</#if>" id="${item.id}">
                  <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryId=${item.id}&pageNum=1&pageSize=10">${item.name}</a>
                </li>
              </#if>
            </#list>
          </ul>
        </div>
        <div class="leftSelect titleTwo">
          <div class="selectTitle"><img src="/center/img/xwzx/Group 2.png" alt="">自学考试</div>
          <ul class="level_one">
            <#list categorys as item>
              <#if item.isZk?? && item.isZk == 1>
                <li class="node_one <#if item.id = activeCategory.id>on</#if>" id="${item.id}">
                  <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryId=${item.id}&pageNum=1&pageSize=10">${item.name}</a>
                </li>
              </#if>
            </#list>
          </ul>
        </div>
      </div>
      <div class="infoRight">
        <div class="rightTitle"><#if activeCategory.isZk?? && activeCategory.isZk == 1>自学考试<#else>教育培训</#if></div>
        <div class="rightList">
          <#list noticeList as item>
            <div class="clearfix">
              <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeDetail?id=${item.id}" class="rightLista">${item.title}</a>
              <p class="rightListdate"><#if item.publishTime??>${item.publishTime?string("yyyy-MM-dd")}<#else>${item.createTime?string("yyyy-MM-dd")}</#if></p>
            </div>
          </#list>
        </div>
        <div class="paperBox" <#if pages lt 2>style="display: none"</#if>>
          <ul class="pagination" id="page">
          </ul>
          <div class="pageJump">
            <span class="mr1 jump_qw">前往</span>
            <input class="mr1" type="text" />
            <button type="button" class="button">跳转</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
<#include "/pages/portal/center/common/footer.html">
</body>
<script>
  function fun (curindex, allnum) {
    if ($("#level_" + curindex)[0].className.search("le_on") != -1) {
      $("#level_" + curindex).removeClass("le_on");
    } else {
      $("#level_" + curindex).addClass("le_on");
    }
  }
  $(".level_one li").click(function () {
    $(".level_one li").removeClass("on");
    $(this).addClass("on");
    var categoryId = $('.level_one').find('.node_one.on').attr('id');
    categoryId = categoryId || '';
    window.location.href="${request.contextPath}/portal/center/${hostOrgCode}/noticeIndex?categoryId="+categoryId+"&pageNum=1&pageSize=10";
  });

  Page({
    num:${pages!2},//页码数
    startnum:${pageNum!1},//指定页码
    elem: $('#page'),		//指定的元素
    callback: function (n) {	//回调函数
      var categoryId = $('.level_one').find('.node_one.on').attr('id');
      categoryId = categoryId || '';
      window.location.href="${request.contextPath}/portal/center/${hostOrgCode}/noticeIndex?categoryId="+categoryId+"&pageNum="+n+"&pageSize=10";
    }
  });

  (function () {
    $(".right_Tab").children().each(function() {
      $(this).removeClass("right_Tab_Check");
    });
    $("#noticeIndex").attr("class", "right_Tab_Check");
  })()

</script>

</html>