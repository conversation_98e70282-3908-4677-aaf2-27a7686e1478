<!DOCTYPE html>
<html lang="en">
<#include "pages/portal/center/common/top.html">
<body>
<#include "pages/portal/center/common/header.html">
  <div class="bannerbox">
    <img src="/center/img/xwzx/Mask group.png" class="swiper-banner">
  </div>
  <div class="infoBox pt3">
    <div class="mid">
      <div class="mianbao clearfix">
        <div class="fl">
          <a href="${request.contextPath}/" class="miaobaoa">首页</a>
          <span class="miaobaosp">&gt;</span>
          <div class="site">新闻资讯</div>
        </div>
      </div>
    </div>
    <div class="mid flex-row pb6">
      <div class="infoLeft">
        <div class="leftSelect">
          <div class="selectTitle"><img src="/center/img/xwzx/Group 1.png" alt="">教育培训</div>
          <ul class="level_one">
            <#list categorys as item>
                <li class="node_one <#if item.id = activeCategory.id>on</#if>">
                  <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryName=${item.name}">${item.name}</a>
                </li>
            </#list>
          </ul>
        </div>
        <div class="leftSelect titleTwo">
          <div class="selectTitle"><img src="/center/img/xwzx/Group 2.png" alt="">自学考试</div>
          <#list categorys as item>
            <#if item.isZk?? && item.isZk== 1>
              <li class="node_one <#if item.id = activeCategory.id>on</#if>">
                <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/noticeIndex?categoryName=${item.name}">${item.name}</a>
              </li>
            </#if>
          </#list>
        </div>
      </div>
      <div class="infoRight">
        <div class="newsTitle">${notice.title}</div>
        <div class="newsTip">
          <span class="mr3">${(notice.publishTime!notice.createTime)?string("yyyy-MM-dd hh:mm")}</span>
          <span>发布来源：${notice.writer}</span>
        </div>
        <div class="newsLine"></div>
        <div class="newsInfo">
          ${notice.content}
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
<#include "/pages/portal/center/common/footer.html">
</body>
<script>
  function fun (curindex, allnum) {
    if ($("#level_" + curindex)[0].className.search("le_on") != -1) {
      $("#level_" + curindex).removeClass("le_on");
    } else {
      $("#level_" + curindex).addClass("le_on");
    }
  }
  $(".level_one li").click(function () {
    $(".level_one li").removeClass("on");
    $(this).addClass("on");
    // var parentTypeId = $('.level_one').find('.node_one.on').attr('id');
    // parentTypeId = parentTypeId || '';
    // var typeId = '';
    // window.location.href="/training/portal/trainingIndex?parentTypeId="+parentTypeId+"&typeId="+typeId+"&pageNum=1&pageSize=12";		
  });

  (function () {
    $(".right_Tab").children().each(function() {
      $(this).removeClass("right_Tab_Check");
    });
    $("#noticeIndex").attr("class", "right_Tab_Check");
  })()
</script>

</html>