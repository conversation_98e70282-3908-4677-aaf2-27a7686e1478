<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>${currentHostOrg.portalSysName}</title>
  <link rel="shortcut icon" type="image/x-icon" href="${currentHostOrg.adminLogo}" />
  <link rel="stylesheet" type="text/css" href="/center/css/common.css" />
  <link rel="stylesheet" type="text/css" href="/center/css/index.css" />
  <link rel="stylesheet" type="text/css" href="/center/css/pxxm.css" />
  <script type="text/javascript" src="/portal/lgd/js/jquery-1.9.1.min.js" ></script>
  <script type="text/javascript" src="/center/js/pager.js" ></script>
</head>
<body>
<#include "pages/portal/center/common/header.html">
    <div class="xm-con">
      <div class="mb3">
        <div class="bannerbox">
          <img src="/center/img/zkzy/Mask group.png" class="swiper-banner" />
        </div>
      </div>
      <div class="mid pr">
        <div class="mianbao clearfix">
          <div class="fl">
            <a href="${request.contextPath}/" class="miaobaoa">首页</a>
            <span class="miaobaosp">&gt;</span>
            <div class="site">培训项目</div>
          </div>
          <div class="seach fr">
            <img src="/center/img/zkzy/Vector.png" class="seachimg" />
            <input type="text" name="keyword" placeholder="搜索培训项目" />
            <button onclick="search()">搜索</button>
          </div>
        </div>
        <div class="clearfix">
          <span class="saixuan">一级分类：</span>
          <ul class="saicon level_one" id="level_1">
            <#list level1Array as item>
              <li class="node_one <#if (curParentTypeId?? && item.id == curParentTypeId)>on</#if>" id="${item.id}">${item.name}</li>
            </#list>
          </ul>
        </div>
        <div class="clearfix">
          <span class="saixuan">二级分类：</span>
          <ul class="saicon level_two" id="level_2">
            <#list level2Array as item>
              <li class="node_two <#if (curTypeId?? && item.id == curTypeId)>on</#if>" id="${item.id}">${item.name}</li>
            </#list>
          </ul>
        </div>
      </div>

      <div class="mid">
        <div class="xiangmu_list clearfix">
          <#list xmList as item>
            <div class="pxcon">
              <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectDetail?id=${item.id}">
                <img src="${item.logo!'/center/img/zkzy/Mask group(1).png'}" class="bgtu2" />
                <div class="pxtext">
                  <h3 class="pxh3">${item.name}</h3>
                </div>
              </a>
            </div>
          </#list>
        </div>
      </div>
      <div class="mid paperBox" <#if pages lt 2>style="display: none" </#if>>
        <ul class="pagination" id="page"></ul>
        <div class="pageJump">
          <span class="mr1 jump_qw">前往</span>
          <input class="mr1" type="text" />
          <button type="button" class="button">跳转</button>
        </div>
      </div>
    </div>
    <!-- 页脚 -->
<#include "/pages/portal/center/common/footer.html">
  </body>
  <script>
    function fun(curindex, allnum) {
      if ($("#level_" + curindex)[0].className.search("le_on") != -1) {
        $("#level_" + curindex).removeClass("le_on");
      } else {
        $("#level_" + curindex).addClass("le_on");
      }
    }
    $(".level_one li").click(function () {
      $(".level_one li").removeClass("on");
      $(this).addClass("on");
      var parentTypeId = $('.level_one').find('.node_one.on').attr('id');
      parentTypeId = parentTypeId || '';
      var typeId = '';
      var keyword = $("input[name='keyword']").val();
      keyword = keyword || '';
      window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectList?parentTypeId="+parentTypeId+"&typeId="+typeId+"&name="+keyword+"&pageNum=1&pageSize=8";
    });
    $(".level_two li").click(function () {
      $(".level_two li").removeClass("on");
      $(this).addClass("on");
      var parentTypeId = $('.level_one').find('.node_one.on').attr('id');
      parentTypeId = parentTypeId || '';
      var typeId = $('.level_two').find('.node_two.on').attr('id');
      typeId = typeId || '';
      var keyword = $("input[name='keyword']").val();
      keyword = keyword || '';
      window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectList?parentTypeId="+parentTypeId+"&typeId="+typeId+"&name="+keyword+"&pageNum=1&pageSize=8";
    });

    Page({
      num:${pages!2},//页码数
      startnum:${pageNum!1},//指定页码
      elem:$('#page'),//指定的元素
      callback:function(n){//回调函数
        var parentTypeId = $('.level_one').find('.node_one.on').attr('id');
        parentTypeId = parentTypeId || '';
        var typeId = $('.level_two').find('.node_two.on').attr('id');
        typeId = typeId || '';
        var keyword = $("input[name='keyword']").val();
        keyword = keyword || '';
        window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectList?parentTypeId="+parentTypeId+"&typeId="+typeId+"&name="+keyword+"&pageNum="+n+"&pageSize=8";
      }
    });

    (function () {
      $(".right_Tab").children().each(function() {
        $(this).removeClass("right_Tab_Check");
      });
      $("#trainingIndex").attr("class", "right_Tab_Check");
    })()

    function search() {
      $(".level_two li").removeClass("on");
      $(this).addClass("on");
      var parentTypeId = $('.level_one').find('.node_one.on').attr('id');
      parentTypeId = parentTypeId || '';
      var typeId = $('.level_two').find('.node_two.on').attr('id');
      typeId = typeId || '';
      var keyword = $("input[name='keyword']").val();
      keyword = keyword || '';
      window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/projectList?parentTypeId="+parentTypeId+"&typeId="+typeId+"&name="+keyword+"&pageNum=1&pageSize=8";
    }
  </script>
</html>
