<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentHostOrg.portalSysName}</title>
  <link rel="shortcut icon" type="image/x-icon" href="${currentHostOrg.adminLogo}" />
  <link rel="stylesheet" href="/center/css/common.css" />
  <link rel="stylesheet" href="/center/css/zkzy.css" />
  <script type="text/javascript" src="/portal/lgd/js/jquery-1.9.1.min.js"></script>
  <script type="text/javascript" src="/center/js/pager.js"></script>
</head>

<body>
<#include "pages/portal/center/common/header.html">
  <div class="mb3">
    <div class="bannerbox">
      <img src="/center/img/zkzy/Mask group.png" class="swiper-banner">
    </div>
  </div>

  <div class="infoBox pr">
    <div class="mid">
      <div class="mianbao clearfix">
        <div class="fl">
          <a href="${request.contextPath}/" class="miaobaoa">首页</a>
          <span class="miaobaosp">&gt;</span>
          <div class="site">自考专业</div>
        </div>
        <div class="seach fr">
          <img src="/center/img/zkzy/Vector.png" class="seachimg">
          <input type="text" name="keyword" placeholder="搜索自考专业" value="${keyword}">
          <button onclick="search()">搜索</button>
        </div>
      </div>
      <div class="clearfix">
        <span class="saixuan">类型：</span>
        <ul class="saicon level_one" id="level_1">
          <li class="node_one <#if !activeEdulevel?? || activeEdulevel == ''>on</#if>">全部</li>
          <li class="node_one <#if activeEdulevel == 'B'>on</#if>" id="B">本科</li>
          <li class="node_one <#if activeEdulevel == 'A'>on</#if>" id="A">专科</li>
        </ul>
      </div>
<!--      <div class="clearfix">-->
<!--        <span class="saixuan">学科：</span>-->
<!--        <ul class="saicon level_two" id="level_2">-->
<!--          <li class="node_two <#if !activeSubjectId?? || activeSubjectId = ''>on</#if>">全部</li>-->
<!--          <#list subjects as item>-->
<!--            <li class="node_two <#if activeSubjectId == item.sId>on</#if>" id="${item.sId}">${item.sName}</li>-->
<!--          </#list>-->
<!--        </ul>-->
<!--      </div>-->
    </div>
    <div class="mid">
      <div class="xiangmu_list clearfix">
        <#list specialtys as item>
          <div class="pxcon hovertx">
            <a href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtyDetail?s_id=${item.sId}">
              <img src="${item.sImgUrl!'/center/img/zkzy/Mask group(1).png'}" class="bgtu2">
              <div class="pxtext">
                <h3 class="pxh3">${item.sCode} - ${item.sNameStr}</h3>
                <div class="pxpbox">
                  <p class="pxp fr"><#if item.sEdulevel == 'A'>专科<#else>本科</#if></p>
                </div>
              </div>
            </a>
          </div>
        </#list>
      </div>
    </div>
    <div class="mid paperBox" <#if pages lt 2>style="display: none"</#if>>
      <ul class="pagination" id="page">
      </ul>
      <div class="pageJump">
        <span class="mr1 jump_qw">前往</span>
        <input class="mr1" type="text" />
        <button type="button" class="button">跳转</button>
      </div>
    </div>
    <div class="imagedrop"></div>
    <div class="imagedrop2"></div>
  </div>
  
  <!-- 页脚 -->
  <#include "/pages/portal/center/common/footer.html">
</body>
<script>
  function fun (curindex, allnum) {
    if ($("#level_" + curindex)[0].className.search("le_on") != -1) {
      $("#level_" + curindex).removeClass("le_on");
    } else {
      $("#level_" + curindex).addClass("le_on");
    }
  }
  $(".level_one li").click(function () {
    $(".level_one li").removeClass("on");
    $(this).addClass("on");
    var edulevel = $('.level_one').find('.node_one.on').attr('id');
    edulevel = edulevel || '';
    var keyword = $("input[name='keyword']").val();
    keyword = keyword || '';
    window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtys?s_id=${item.s_id}&s_edulevel="+edulevel+"&keyword="+keyword+"&pageNum=1&pageSize=8";
  });

  $(".level_two li").click(function () {
    $(".level_two li").removeClass("on");
    $(this).addClass("on");
    var edulevel = $('.level_one').find('.node_one.on').attr('id');
    edulevel = edulevel || '';
    var subjectId = $('.level_two').find('.node_two.on').attr('id');
    subjectId = subjectId || '';
    var keyword = $("input[name='keyword']").val();
    keyword = keyword || '';
    window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtys?s_id=${item.s_id}&s_edulevel="+edulevel+"&s_subject_id="+subjectId+"&keyword="+keyword+"&pageNum=1&pageSize=8";
  });

  Page({
    num: ${pages!2},					//页码数
    startnum: ${pageNum!1},				//指定页码
    elem: $('#page'),		//指定的元素
    callback: function (n) {	//回调函数
      var edulevel = $('.level_one').find('.node_one.on').attr('id');
      edulevel = edulevel || '';
      var subjectId = $('.level_two').find('.node_two.on').attr('id');
      subjectId = subjectId || '';
      var keyword = $("input[name='keyword']").val();
      keyword = keyword || '';
      window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtys?s_id=${item.s_id}&s_edulevel="+edulevel+"&s_subject_id="+subjectId+"&keyword="+keyword+"&pageNum="+n+"&pageSize=8";
    }
  });

  function search() {
    var edulevel = $('.level_one').find('.node_one.on').attr('id');
    edulevel = edulevel || '';
    var subjectId = $('.level_two').find('.node_two.on').attr('id');
    subjectId = subjectId || '';
    var keyword = $("input[name='keyword']").val();
    keyword = keyword || '';
    window.location.href="${request.contextPath}/portal/center/${currentHostOrg.code}/specialtys?s_id=${item.s_id}&s_edulevel="+edulevel+"&s_subject_id="+subjectId+"&keyword="+keyword+"&pageNum=1&pageSize=8";
  }

  (function () {
    $(".right_Tab").children().each(function() {
      $(this).removeClass("right_Tab_Check");
    });
    $("#specialtyIndex").attr("class", "right_Tab_Check");
  })()

</script>

</html>