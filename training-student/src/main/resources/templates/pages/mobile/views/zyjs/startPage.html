<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentHostOrg.name}</title>
  <!-- 引入Vue 3 CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- 引入Element Plus CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <!-- 引入Element Plus JavaScript 库 -->
  <script src="https://unpkg.com/element-plus"></script>
  <script src="https://unpkg.com/element-plus/dist/locale/zh-cn"></script>
  <script src="/js/jquery-1.9.1.min.js"></script>
  <script src="/js/compressImg.js"></script>
  <script type="text/javascript" src="/comm/utils.js" ></script>
  <script type="text/javascript" src="/plugins/layer/layer.js" ></script>
  <style>
    /* :root {
      --el-font-size-base: 16px;
    } */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
    }
    #app {
      background-color: #F4F7FF;
      width: 100%;
      min-height: 100%;
    }
    .header {
      height: 380px;
      background-image: url(/mobile/images/header-bg.png);
      background-size: 100% 100%;
      text-align: center;
    }
    .content {
      width: 1200px;
      margin: -190px auto;
      padding-bottom: 70px;
    }
    .title {
      padding-top: 70px;
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
      letter-spacing: 3px;
      text-shadow: 0px 2px 4px rgba(0,0,0,0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .login {
      display: inline-block;
      margin-top: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: 500;
      font-size: 16px;
      color: #FFE500;
    }
    .form-box {
      background-color: #fff;
      padding-bottom: 30px;
      text-align: center;
      border-radius: 10px;
    }
    .form {
      padding: 50px 285px 30px 120px;
    }

    .el-form-item {
      margin-bottom: 20px;
    }
    .el-form-item__label {
      padding-right: 20px;
    }

    .avatar-uploader .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .avatar-uploader .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
    }

    .el-button--primary {
      background-color: #085CE0;
    }

    .el-button--primary:hover {
      background-color: #085CE0;
    }

    .steps {
      display: flex;
      justify-content: center;
      align-items: baseline;
      padding-top: 50px;
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }

    .out-circle {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 4px;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      border: 1px solid #E7E7E7;
    }

    .active-out-circle {
      border: 1px solid #085CE0;
    }

    .inner-circle {
      width: 36px;
      height: 36px;
      line-height: 36px;
      border-radius: 50%;
      background: #999999;
      color: #fff;
      font-weight: 800;
      font-size: 16px;
    }

    .active-inner-circle {
      background: #085CE0;
    }

    .line {
      width: 140px;
      height: 4px;
      background: #E7E7E7;
      border-radius: 7px;
    }

    .active-line {
      background: #085CE0;
    }

    @media screen and (max-width: 1200px) {
      #app {
        background-image: url(/mobile/images/h5-bg.png);
        background-size: 100% 100%;
        height: 100%;
        overflow: auto;
      }
      .header {
        height: 200px;
        background-image: none;
      }
      .title {
        padding-top: 30px;
        font-size: 20px;
        text-shadow: 0px 0px 2px #255B90;
      }
      .login {
        margin-top: 8px;
        font-size: 12px;
      }
      .content {
        width: 100%;
        margin: -108px auto;
        padding-bottom: 20px;
      }
      .form-box {
        margin: 14px;
        padding-bottom: 0;
        background-color: transparent;
      }
      .form {
        padding: 12px 12px 8px 12px;
      }
      .el-form {
        background-color: #fff;
        border-radius: 6px;
      }
      .el-form-item {
        margin-bottom: 14px;
      }
      .el-button {
        width: 100%;
        margin-top: 14px;
      }
      .el-button--primary {
        background-color: #FFE500;
        color: #333;
      }
      .el-button--primary:hover {
        color: #333;
        background-color: #FFE500;
      }

      .btns {
        display: flex;
      }

      .steps {
        padding: 10px 0;
        margin-bottom: 10px;
        border-radius: 6px;
        background: #FFE500;
        font-size: 12px;
      }

      .active-step {
        color: #085CE0;
      }

      .out-circle {
        width: 24px;
        height: 24px;
        border: 3px solid #fff;
        background: #fff;
      }

      .active-out-circle {
        border: 3px solid #085CE0;
      }

      .inner-circle {
        width: 20px;
        height: 20px;
        line-height: 20px;
        background: #fff;
        color: #333;
        font-weight: 800;
        font-size: 12px;
      }

      .active-inner-circle {
        background: #fff;
      }

      .line {
        width: 140px;
        height: 4px;
        background: #fff;
        border-radius: 7px;
      }

      .active-line {
        background: #085CE0;
      }

    }
  </style>
</head>
<body>
  <div id="app">
    <div class="header">
      <div class="title">高级研修班报名信息采集</div>
      <a class="login" href="${request.contextPath}/kaosheng/mobile/zyjd/bm/loginPage?bmbatchType=ZYJS&bmbatchId=${bmbatch.id}" target="_blank">(已报名学员请点击登录)</a>
    </div>

    <div class="content">
      <div class="form-box">
        <div class="steps">
          <div class="step active-step">
            <div class="out-circle active-out-circle">
              <div class="inner-circle active-inner-circle">1</div>
            </div>
            基本信息
          </div>
          <div class="line" :class="{ 'active-line': active == 1 }"></div>
          <div class="step" :class="{ 'active-step': active == 1} ">
            <div class="out-circle" :class="{ 'active-out-circle': active == 1 }">
              <div class="inner-circle" :class="{ 'active-inner-circle': active == 1 }">2</div>
            </div>
            培训信息
          </div>
        </div>
        <el-form
        class="form"
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="246px"
        class="demo-ruleForm"
        status-icon
        :label-position="labelPosition"
      >
        <div v-if="active == 0">
          <template v-for="item in formOpt">
            <el-form-item v-if="!item.type" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请输入' + item.label,
                trigger: 'blur',
              },
            ]: []">
              <el-input v-model="ruleForm[item.prop]" :placeholder="'请输入' + item.label" />
            </el-form-item>
            <el-form-item v-if="item.type == 'select'" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请选择' + item.label,
                trigger: 'change',
              },
            ]: []">
            <template #label>
              <span v-if="isRequired(item.prop)" style="color: #f56c6c; margin-right: 4px;">*</span>
              <span>{{ item.label }}:</span>
            </template>
              <el-select v-model="ruleForm[item.prop]" :placeholder="'请选择' + item.label" clearable>
                <el-option v-for="option in item.options" :label="option.label" :value="option.value" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="item.type == 'date'" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请选择' + item.label,
                trigger: 'change',
              },
            ]: []">
              <el-date-picker
                v-model="ruleForm[item.prop]"
                type="date"
                :label="item.label"
                :placeholder="'请选择' + item.label"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </template>
        </div>
        <div v-if="active == 1">
          <template v-for="item in formOpt2">
            <el-form-item v-if="!item.type" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请输入' + item.label,
                trigger: 'blur',
              },
            ]: []">
              <el-input v-model="ruleForm[item.prop]" :placeholder="'请输入' + item.label" />
            </el-form-item>
            <el-form-item v-if="item.type == 'select'" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请选择' + item.label,
                trigger: 'change',
              },
            ]: []">
            <template #label>
              <span v-if="isRequired(item.prop)" style="color: #f56c6c; margin-right: 4px;">*</span>
              <span>{{ item.label }}:</span>
            </template>
              <el-select v-model="ruleForm[item.prop]" :placeholder="'请选择' + item.label" clearable>
                <el-option v-for="option in item.options" :label="option.label" :value="option.value" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="item.type == 'date'" :key="item.prop" :label="item.label + ':'" :prop="item.prop" :rules="!item.norequire ? [
              {
                required: true,
                message: '请选择' + item.label,
                trigger: 'change',
              },
            ]: []">
              <el-date-picker
                v-model="ruleForm[item.prop]"
                type="date"
                :label="item.label"
                :placeholder="'请选择' + item.label"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </template>
          <el-form-item label="报名回执:" prop="receipt" :rules="[
            {
              required: true,
              message: '请上传报名回执',
              trigger: 'blur',
            },
          ]">
            <el-upload
              action="#"
              :id="uuid"
              :class="['avatar-uploader', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
              :multiple="false"
              :disabled="self_disabled"
              :show-file-list="false"
              :http-request="handleHttpUpload"
              :before-upload="beforeUpload"
              :on-success="uploadSuccess"
              :on-error="uploadError"
              :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.receipt">
                <img :src="ruleForm.receipt" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
      <template v-if="active == 0">
        <el-button size="large" type="primary" @click="handleStep(1)">
          下一步
        </el-button>
      </template>  
      <div v-else class="btns">
        <el-button size="large" @click="handleStep(0)">
          上一步
        </el-button>
        <el-button v-if="" size="large" type="primary" @click="submitForm(ruleFormRef)">
          提交报名信息
        </el-button>
      </div>  
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, reactive, onMounted, onUnmounted } = Vue;
    const app = createApp({
      setup() {

        const certiCategoryValue = reactive([
          <#if select?? && select.certiCategorys??>
            <#list select.certiCategorys as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const xbValue = reactive([
          <#if select?? && select.xbs??>
            <#list select.xbs as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])
        const mzValue = reactive([
          <#if select?? && select.mzs??>
            <#list select.mzs as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const xlValue = reactive([
          <#if select?? && select.xls??>
            <#list select.xls as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const xwValue = reactive([
          <#if select?? && select.xws??>
            <#list select.xws as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const unitNatureValue = reactive([
          <#if select?? && select.unitNatures??>
            <#list select.unitNatures as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const zcSeriesValue = reactive([
          <#if select?? && select.zcSeriess??>
            <#list select.zcSeriess as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const zcValue = reactive([
          <#if select?? && select.zcs??>
            <#list select.zcs as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const zcLevelValue = reactive([
          <#if select?? && select.zcLevels??>
            <#list select.zcLevels as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])
        const zyQualificationNameValue = reactive([
          <#if select?? && select.zyQualificationNames??>
            <#list select.zyQualificationNames as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])
        const zyQualificationLevelValue = reactive([
          <#if select?? && select.zyQualificationLevels??>
            <#list select.zyQualificationLevels as item>
              {label: '${item.dictName}', value: '${item.dictValue}'},
            </#list>
          </#if>
        ])

        const active = ref(0)
        const formOpt = [
          { label: '行政区划代码', prop: 'regionalismCode' },
          { label: '证件类型', prop: 'certiCategory', type: 'select', options: certiCategoryValue },
          { label: '证件号码', prop: 'sfzh' },
          { label: '手机号', prop: 'mobile' },
          { label: '姓名', prop: 'name' },
          { label: '性别', prop: 'xb', type: 'select', options: xbValue },
          { label: '民族', prop: 'mz', type: 'select', options: mzValue },
          { label: '出生日期', prop: 'birthday', type: 'date' },
          { label: '学历', prop: 'xl', type: 'select',options: xlValue },
          { label: '学位', prop: 'xw', type: 'select',options: xwValue }
        ]
        const formOpt2 = [
          { label: '所在单位统一社会信用代码', prop: 'socialCreditCode' },
          { label: '所在单位名称', prop: 'company' },
          { label: '所在单位性质', prop: 'unitNature', type: 'select',options: unitNatureValue },
          { label: '是否由所在单位提供继续教育培训', prop: 'isProvideJxjy', type: 'select', options: [{ label: '是', value: '1' }, { label: '否', value: '0' }] },
          { label: '取得证书类型', prop: 'obtainCertiCategory', type: 'select', options: [{ label: '职称证书', value: '1' }, { label: '职业资格证书', value: '2' }] },
          { label: '职称系列', prop: 'zcSeries', type: 'select', norequire: true, options:zcSeriesValue  },
          { label: '职称名称', prop: 'zc', type: 'select', norequire: true,options: zcValue },
          { label: '职称级别', prop: 'zcLevel', type: 'select', norequire: true, options: zcLevelValue },
          { label: '职业资格名称', prop: 'zyQualificationName', type: 'select', norequire: true, options: zyQualificationNameValue },
          { label: '职业资格等级', prop: 'zyQualificationLevel', type: 'select', norequire: true, options: zyQualificationLevelValue }
        ]

        const labelPosition = ref('top')
        const ruleFormRef = ref()
        const ruleForm = reactive({
          regionalismCode: '',
          certiCategory: '',
          sfzh: '',
          mobile: '',
          name: '',
          xb: '',
          mz: '',
          birthday: '',
          xl: '',
          xw: '',
          socialCreditCode: '',
          company: '',
          unitNature: '',
          isProvideJxjy: '',
          obtainCertiCategory: '',
          zcSeries: '',
          zc: '',
          zcLevel: '',
          zyQualificationName: '',
          zyQualificationLevel: '',
          receipt: '',
        })

        const rules = reactive({
          sfzh: [
            { min: 6, message: '证件号码长度必须大于等于6位', trigger: 'blur' },
          ],
          mobile: [
		        { validator: (rule, value, callback) => {
              if (!/^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/.test(value)) {
                callback(new Error(`手机号格式不正确`))
              } else {
                callback()
              }
            }, trigger: 'blur' },
          ],
          zcSeries: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称系列'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zc: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称名称'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zcLevel: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称级别'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zyQualificationName: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '2' && !value) {
                  callback(new Error('请选择职业资格名称'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zyQualificationLevel: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '2' && !value) {
                  callback(new Error('请选择职业资格等级'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        })

        const handleStep = async (step) => {
          if (step == 1) {
            await ruleFormRef.value.validate((valid, fields) => {
              if (valid) {
                active.value = step
              } 
            })
          } else {
            active.value = step
          }
        }

        const submitForm = async (formEl) => {
          if (!formEl) return
          var str = window.location.href.split('/');
          await formEl.validate((valid, fields) => {
            if (valid) {
              layer.load();
              $.ajax({
                type: "post",
                url: "/kaosheng/mobile/zyjd/bm/create",
                data: JSON.stringify({...ruleForm, bmbatchId : '${bmbatch.id}'}),
                dataType: "json",
                contentType: "application/json",
                success: function(ret){
                  layer.closeAll('loading');
                  if(ret.code == 0){
                    layer.alert('报名信息提交成功',{icon:6});
                  }else{
                    layer.alert(ret.message,{icon:5});
                  }
                },
                error:function(){
                  layer.closeAll('loading');
                  layer.alert('报名信息提交失败，请稍后重试',{icon:5});
                }
              });
            }
          })
        }

        const handleHttpUpload = async (options) => {
          const { file } = options;
          const reader = new FileReader();
          reader.onload = (e) => {
            utils.compressAndUploadImage(file, (url) => {
              ruleForm.receipt = url
              ruleFormRef.value?.validateField(['receipt'])
     		  ElementPlus.ElMessage({
     	            title: '温馨提示',
     	            message: '图片上传成功！',
     	            type: 'success',
     	          });
            })
          };
          reader.readAsDataURL(file);
        };

        const uploadSuccess = () => {
          /* ElementPlus.ElMessage({
            title: '温馨提示',
            message: '图片上传成功！',
            type: 'success',
          }); */
        };

        /**
         * @description 图片上传错误
         * */
        const uploadError = () => {
          ElementPlus.ElMessage({
            title: '温馨提示',
            message: '图片上传失败，请您重新上传！',
            type: 'error',
          });
        };

        const isRequired = (prop) => {
          // 根据具体的条件判断字段是否为必填项
          if (prop === 'zcSeries' || prop === 'zc' || prop === 'zcLevel' ) {
            return ruleForm.obtainCertiCategory === '1';
          } else if (prop === 'zyQualificationName' || prop === 'zyQualificationLevel') {
            return ruleForm.obtainCertiCategory === '2';
          }
        };

        const updateLabelPosition = () => {
          if (window.innerWidth > 1200) {
            labelPosition.value = 'right';
          } else {
            labelPosition.value = 'top';
          }
        };

        onMounted(() => {
          updateLabelPosition();
          window.addEventListener('resize', updateLabelPosition);
        });

        onUnmounted(() => {
          window.removeEventListener('resize', updateLabelPosition);
        });

        return {
          active,
          formOpt,
          formOpt2,
          labelPosition,
          ruleFormRef,
          ruleForm,
          rules,
          submitForm,
          handleHttpUpload,
          uploadSuccess,
          uploadError,
          isRequired,
          handleStep
        };
      },
    });

    // 使用Element Plus组件
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn,
    });
    app.mount('#app');
  </script>
</body>
</html>