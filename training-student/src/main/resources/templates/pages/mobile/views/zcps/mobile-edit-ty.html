<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentHostOrg.name}</title>
  <!-- 引入Vue 3 CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="/js/jquery-1.9.1.min.js"></script>
  <script type="text/javascript" src="/plugins/layer/layer.js" ></script>
  <script type="text/javascript" src="/comm/utils.js" ></script>
  <script src="/js/compressImg.js"></script>
  <!-- 引入Element Plus CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <!-- 引入Element Plus JavaScript 库 -->
  <script src="https://unpkg.com/element-plus"></script>
  <script src="https://unpkg.com/element-plus/dist/locale/zh-cn"></script>
  <link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
  <style>
    /* :root {
      --el-font-size-base: 16px;
    } */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
    }
    #app {
      background-color: #F4F7FF;
      width: 100%;
      min-height: 100%;
    }
    .header {
      height: 380px;
      background-image: url(/mobile/images/zcps-mobile-h5-bg.png);
      background-size: 100% 100%;
      text-align: center;
    }
    .content {
      /* width: 1200px; */
      margin: -190px auto;
      padding-bottom: 70px;
    }
    .title {
      padding-top: 70px;
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
      letter-spacing: 3px;
      text-shadow: 0px 2px 4px rgba(0,0,0,0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .login {
      display: inline-block;
      margin-top: 16px;
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: 500;
      font-size: 16px;
      color: #FFE500;
    }
    .form-box {
      background-color: #fff;
      padding-bottom: 30px;
      text-align: center;
      border-radius: 10px;
    }
    .form {
      padding: 50px 285px 30px 120px;
    }

    .el-form-item {
      flex: 1;
      margin-bottom: 20px;
      text-align: left;
    }
    .el-form-item__label {
      padding-right: 20px;
    }

    .to-date {
      padding: 8px 15px;
      margin: 0px 0px 14px 12px;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;
      border: 1px solid #dcdfe6;
      line-height: 1;
      font-weight: 500;
      cursor: pointer;
      user-select: none;
    }

    .to-date-active {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }

    .avatar-uploader .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .avatar-uploader .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .upload-demo.left-btn .el-upload {
      display: flex;
      justify-content: flex-start; /* 按钮固定在左边 */
      align-items: center; /* 水平居中对齐 */
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
    }

    .el-button--primary {
      background-color: #085CE0;
    }

    .el-button--primary:hover {
      background-color: #085CE0;
    }

    .steps {
      display: flex;
      justify-content: center;
      align-items: baseline;
      padding-top: 50px;
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }

    .out-circle {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 4px;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      border: 1px solid #E7E7E7;
    }

    .active-out-circle {
      border: 1px solid #085CE0;
    }

    .inner-circle {
      width: 36px;
      height: 36px;
      line-height: 36px;
      border-radius: 50%;
      background: #999999;
      color: #fff;
      font-weight: 800;
      font-size: 16px;
    }

    .active-inner-circle {
      background: #085CE0;
    }

    .line {
      width: 140px;
      height: 4px;
      background: #E7E7E7;
      border-radius: 7px;
    }

    .active-line {
      background: #085CE0;
    }

    @media screen /*and (max-width: 1200px)*/ {
      #app {
        background-image: url(/mobile/images/zcps-mobile-h5-bg.png);
        background-size: 100% 100%;
        height: 100%;
        overflow: auto;
      }
      .header {
        height: 180px;
        background-image: none;
      }
      .title {
        padding-top: 30px;
        font-size: 20px;
        text-shadow: 0px 0px 2px #255B90;
      }
      .login {
        margin-top: 8px;
        font-size: 12px;
      }
      .content {
        width: 100%;
        margin: -108px auto;
        padding-bottom: 20px;
      }
      .form-box {
        margin: 14px;
        padding-bottom: 0;
        background-color: transparent;
      }
      .form-card {
        background-color: #fff;
        border-radius: 6px;
        padding: 12px 12px 8px 12px;
        margin-bottom: 10px;
      }
      .card-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        background-color: #fff;
        padding: 10px 8px;
        margin-bottom: 10px;
        border-radius: 6px;
      }
      .el-form-item {
        margin-bottom: 14px;
      }
      .el-date-editor {
        width: 100% !important;
      }
      .step-button {
        width: 100%;
        margin-top: 14px;
        background-color: #FFE500;
        color: #333;
      }
      .step-button:hover {
        color: #333;
        background-color: #FFE500;
      }

      .btns {
        display: flex;
        justify-content: center;
      }

      .steps {
        padding: 10px 0;
        margin-bottom: 10px;
        border-radius: 6px;
        background: #FFE500;
        font-size: 12px;
      }

      .active-step {
        color: #085CE0;
      }

      .out-circle {
        width: 24px;
        height: 24px;
        border: 3px solid #fff;
        background: #fff;
      }

      .active-out-circle {
        border: 3px solid #085CE0;
      }

      .inner-circle {
        width: 20px;
        height: 20px;
        line-height: 20px;
        background: #fff;
        color: #333;
        font-weight: 800;
        font-size: 12px;
      }

      .active-inner-circle {
        background: #fff;
      }

      .line {
        width: 140px;
        height: 4px;
        background: #fff;
        border-radius: 7px;
      }

      .active-line {
        background: #085CE0;
      }
    }

    @media screen and (min-width: 1200px){
      .content {
        width: 75%;
      }
    }
    #editor—wrapper {
      text-align: left;
      border: 1px solid #ccc;
      z-index: 100; /* 按需定义 */
    }
    #toolbar-container { border-bottom: 1px solid #ccc; }
    #editor-container { height: 300px; }
    #editor—wrapper2 {
      text-align: left;
      border: 1px solid #ccc;
      z-index: 100; /* 按需定义 */
    }
    #toolbar-container2 { border-bottom: 1px solid #ccc; }
    #editor-container2 { height: 300px; }
  </style>
</head>
<body>
<div id="app">
  <div class="header">
    <div class="title">{{ title }}</div>
    <!-- <a class="login" href="${request.contextPath}/kaosheng/mobile/zyjd/bm/loginPage?bmbatchType=QDY&bmbatchId=${bmbatch.id}" target="_blank">(已报名学员请点击登录)</a> -->
  </div>

  <div class="content">
    <div class="form-box">
      <el-form
              ref="ruleFormRef"
              :model="ruleForm"
              :rules="rules"
              label-width="246px"
              class="demo-ruleForm"
              status-icon
              :label-position="labelPosition"
              :disabled="isDisabled"
      >
        <div class="form-card">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="ruleForm.name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="ruleForm.gender">
              <el-radio label="M">男</el-radio>
              <el-radio label="F">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="出生年月" prop="birthday">
            <el-date-picker
                    v-model="ruleForm.birthday"
                    type="date"
                    placeholder="请选择出生年月"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="政治面貌" prop="politicalType">
            <el-select v-model="ruleForm.politicalType" placeholder="请选择政治面貌" clearable>
              <el-option v-for="option in politicalOptions" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="籍贯" prop="certiAddress">
            <el-input v-model="ruleForm.certiAddress" placeholder="请输入籍贯" />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="ruleForm.mobile" placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="文化程度" prop="education">
            <el-select v-model="ruleForm.education" placeholder="请选择文化程度" clearable>
              <el-option v-for="option in eduOptions" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="从业时间" prop="workTime">
            <el-date-picker
                    v-model="ruleForm.workTime"
                    type="date"
                    placeholder="请选择从业时间"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="现职业" prop="profession">
            <el-input v-model="ruleForm.profession" placeholder="请输入现职业" />
          </el-form-item>
          <el-form-item label="申报工种" prop="professionId">
            <el-select name="professionId" v-model="ruleForm.professionId" placeholder="请选择申报工种" clearable @change="changeSbzy">
              <el-option v-for="option in professionList" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="级别" prop="applyTechLevel">
            <el-select name="applyTechLevel" v-model="ruleForm.applyTechLevel" placeholder="请选择级别" clearable>
              <el-option v-for="option in levelList" :label="option.desc" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="原职业资格等级" prop="oldTechLevel">
            <el-select v-model="ruleForm.oldTechLevel" placeholder="请选择原职业资格等级" clearable>
              <el-option v-for="option in levelOptions" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="资格等级获取时间" prop="oldTechLevelTime">
            <el-date-picker
                    v-model="ruleForm.oldTechLevelTime"
                    type="date"
                    placeholder="请选择资格等级获取时间"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="从事本职业工作年限" prop="workYears">
            <el-input v-model="ruleForm.workYears" placeholder="请输入从事本职业工作年限" />
          </el-form-item>
          <el-form-item label="身份证号" prop="sfzh">
            <el-input v-model="ruleForm.sfzh" placeholder="请输入身份证号" />
          </el-form-item>
          <el-form-item label="直属单位" prop="unitName">
            <el-select name="unitName" v-model="ruleForm.unitName" placeholder="请选择直属单位" clearable>
              <el-option v-for="option in unitList" :label="option.name" :value="option.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="何时何地受过何种奖励" prop="reward">
            <!-- <el-input type="textarea" :rows="3" v-model="ruleForm.reward" placeholder="请输入何时何地受过何种奖励" /> -->
            <div id="editor—wrapper">
              <div id="toolbar-container"><!-- 工具栏 --></div>
              <div id="editor-container"><!-- 编辑器 --></div>
            </div>
          </el-form-item>
          <el-form-item label="登记照:" prop="studentPhoto" :rules="[
              {
                required: true,
                message: '请上传登记照',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    action="#"
                    :class="['avatar-uploader']"
                    :multiple="false"
                    :show-file-list="false"
                    data="studentPhoto"
                    :http-request="handleHttpUpload"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.studentPhoto">
                <img :src="ruleForm.studentPhoto" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
        </div>

        <div class="card-title">主要学历和工作经历(包括技术配合、进修)</div>
        <div class="form-card" v-for="(item, index) in ruleForm.workExperience" :key="item.key">
          <el-form-item label="开始时间" :prop="'workExperience.' + index + '.a'"
                        :rules="{
                required: true,
                message: '请选择开始时间',
                trigger: 'change',
              }"
          >
            <el-date-picker
                    v-model="item.a"
                    type="month"
                    placeholder="请选择开始时间"
                    format="YYYY/MM"
                    value-format="YYYY-MM"
            />
          </el-form-item>
          <div style="display: flex; align-items: flex-end;">
            <el-form-item label="结束时间" :prop="'workExperience.' + index + '.b'"
                          :rules="{
                  required: true,
                  message: '请选择结束时间',
                  trigger: 'change',
                }"
            >
              <el-date-picker
                      v-model="item.b"
                      type="month"
                      placeholder="请选择结束时间"
                      format="YYYY/MM"
                      value-format="YYYY-MM"
                      :disabled="item.b == '至今'"
              />
            </el-form-item>
            <!-- <el-button @click="handleNow('workExperience', index)" style="margin: 0 0 14px 12px;">至今</el-button> -->
            <div :class="[item.b == '至今' ? 'to-date-active' : '']" class="to-date" @click="handleNow('workExperience', index)">至今</div>
          </div>
          <el-form-item label="在何地何单位学习或工作" :prop="'workExperience.' + index + '.c'"
                        :rules="{
                required: true,
                message: '请输入在何地何单位学习或工作',
                trigger: 'blur',
              }"
          >
            <el-input v-model="item.c" placeholder="请输入在何地何单位学习或工作" />
          </el-form-item>
          <el-form-item label="任何职务" :prop="'workExperience.' + index + '.d'"
                        :rules="{
                required: true,
                message: '请输入任何职务',
                trigger: 'blur',
              }"
          >
            <el-input v-model="item.d" placeholder="请输入任何职务" />
          </el-form-item>
          <div>
            <el-button size="small" :disabled="index != (ruleForm.workExperience.length - 1)" @click="handleAddArr('workExperience')" type="primary">添加</el-button>
            <el-button size="small" :disabled="ruleForm.workExperience.length == 1" @click="handleDelArr('workExperience', index)" type="danger">删除</el-button>
          </div>
        </div>

        <div class="form-card">
          <el-form-item label="身份证正面:" prop="sfzzm" :rules="[
              {
                required: true,
                message: '请上传身份证正面',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    action="#"
                    :class="['avatar-uploader']"
                    :multiple="false"
                    :show-file-list="false"
                    data="sfzzm"
                    :http-request="handleHttpUpload"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.sfzzm">
                <img :src="ruleForm.sfzzm" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="身份证反面:" prop="sfzfm" :rules="[
              {
                required: true,
                message: '请上传身份证反面',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    action="#"
                    :class="['avatar-uploader']"
                    :multiple="false"
                    :show-file-list="false"
                    data="sfzfm"
                    :http-request="handleHttpUpload"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.sfzfm">
                <img :src="ruleForm.sfzfm" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="原职业资格证书:" prop="oldCertiPhoto" :rules="[
              {
                required: true,
                message: '请上传原职业资格证书',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    action="#"
                    :class="['avatar-uploader']"
                    :multiple="false"
                    :show-file-list="false"
                    :http-request="handleHttpUpload"
                    data="oldCertiPhoto"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.oldCertiPhoto">
                <img :src="ruleForm.oldCertiPhoto" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="学历证书:" prop="eduCertiPhoto" :rules="[
              {
                required: true,
                message: '请上传学历证书',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    action="#"
                    :class="['avatar-uploader']"
                    :multiple="false"
                    :show-file-list="false"
                    :http-request="handleHttpUpload"
                    data="eduCertiPhoto"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.eduCertiPhoto">
                <img :src="ruleForm.eduCertiPhoto" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
        </div>

        <div class="form-card">
          <el-form-item label="技师鉴定考评申报表:" prop="appraisalForms" :rules="[
              {
                required: true,
                message: '请上传技师鉴定考评申报表',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    :file-list="ruleForm.appraisalForms"
                    class="upload-demo left-btn"
                    action="#"
                    :multiple="false"
                    :http-request="handleHttpUpload2"
                    data="appraisalForms"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="handleRemove"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="技术总结:" prop="technicalSummaryForms" :rules="[
              {
                required: true,
                message: '请上传技术总结',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    :file-list="ruleForm.technicalSummaryForms"
                    class="upload-demo left-btn"
                    action="#"
                    :multiple="false"
                    :http-request="handleHttpUpload2"
                    data="technicalSummaryForms"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="handleRemove"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="论文:" prop="thesisForms" :rules="[
              {
                required: true,
                message: '请上传论文',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    :file-list="ruleForm.thesisForms"
                    class="upload-demo left-btn"
                    action="#"
                    :multiple="false"
                    :http-request="handleHttpUpload2"
                    data="thesisForms"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="handleRemove"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="工作业绩评定材料:" prop="jsWorkAchievementForms" :rules="[
              {
                required: true,
                message: '请上传工作业绩评定材料',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    :file-list="ruleForm.jsWorkAchievementForms"
                    class="upload-demo left-btn"
                    action="#"
                    :multiple="false"
                    :http-request="handleHttpUpload2"
                    data="jsWorkAchievementForms"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="handleRemove"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="获奖证书及成果证明材料:" prop="certificatesForms" :rules="[
              {
                required: true,
                message: '请上传技师鉴定考评申报表',
                trigger: 'blur',
              },
            ]">
            <el-upload
                    :file-list="ruleForm.certificatesForms"
                    class="upload-demo left-btn"
                    action="#"
                    :multiple="false"
                    :http-request="handleHttpUpload2"
                    data="certificatesForms"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="uploadError"
                    :on-remove="handleRemove"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </el-form-item>
        </div>

        <div class="form-card">
          <el-form-item label="工作业绩" prop="achievement">
            <!-- <el-input type="textarea" :rows="3" v-model="ruleForm.achievement" placeholder="请输入工作业绩" /> -->
            <div id="editor—wrapper2">
              <div id="toolbar-container2"><!-- 工具栏 --></div>
              <div id="editor-container2"><!-- 编辑器 --></div>
            </div>
          </el-form-item>
        </div>

        <div class="form-card">
          <el-upload
                  :file-list="ruleForm.attachments"
                  class="upload-demo"
                  action="#"
                  :multiple="false"
                  data="attachments"
                  :http-request="handleHttpUpload2"
                  :before-upload="beforeUpload"
                  :on-success="uploadSuccess"
                  :on-error="uploadError"
                  :on-remove="handleRemove"
          >
            <el-button type="primary">上传附件</el-button>
          </el-upload>
        </div>
      </el-form>
      <div class="form-card">审核状态：${result.statusDesc}<#if result.status=="BH">&emsp;&emsp;驳回原因：${result.approveAdvice}</#if></div>
    <el-button v-if="!isDisabled" class="step-button" size="large" type="primary" @click="submitForm(ruleFormRef)">
      修改信息
    </el-button>
  </div>
</div>
</div>

<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
  const { createApp, ref, reactive, onMounted, onUnmounted } = Vue;
  const app = createApp({
    setup() {
      const bmbatchId = ref("${bmbatchId}");
      const industryId = ref("${industryId}");
      const professionList = ref([
        <#if professionList??>
              <#list professionList as item>
      {value: '${item.id}', label: '${item.code}-${item.name}'},
    </#list>
    </#if>
    ]);
    const attachments = ref([
    <#if result.files??>
    <#list result.files as item>
      {name: '${item.name}', url: '${item.url}'},
    </#list>
    </#if>
    ]);
    const levelList = ref([]);
    const unitList = ref([
      <#if unitList??>
        <#list unitList as item>
          {name: '${item.name}'},
        </#list>
      </#if>
    ]);

    const active = ref(0)
    const title = ref("${bmbatch.name}")

      const getArrStr  = (str) => {
        if (str) {
          const data = JSON.parse(str)
          return data.map((e, ind) => {
            let keys = ['a','b','c','d','e'];
            let values = e.split('|^|');
            let newObj = {};
            keys.forEach((key, index) => {
              if (values[index]) {
                newObj[key] = values[index];
              }
            });
            newObj.key = Date.now() + ind
            return newObj;
          })
        } else {
          return []
        }
      }

      const labelPosition = ref('top')
      const ruleFormRef = ref()
      const isDisabled = ref('${result.status}' !== 'BH')
      const ruleForm = reactive({
        id: '${result.id}',
        name: '${result.name}',
        gender: '${result.gender}',
        birthday: '${result.birthday}',
        certiAddress: '${result.certiAddress}',
        politicalType: '${result.politicalType}',
        workTime: '${result.workTime}',
        oldTechLevel: '${result.oldTechLevel}',
        oldTechLevelTime: '${result.oldTechLevelTime}',
        education: '${result.education}',
        workYears: '${result.workYears}',
        profession: '${result.profession}',
        jsTime: '${result.jsTime}',
        gw: '${result.gw}',
        company: '${result.company}',
        sfzh: '${result.sfzh}',
        mobile: '${result.mobile}',
        sfzzm: '${result.sfzzm}',
        sfzfm: '${result.sfzfm}',
        oldCertiPhoto: '${result.oldCertiPhoto}',
        eduCertiPhoto: '${result.eduCertiPhoto}',
        professionId: '${result.professionId}',
        applyTechLevel: '${result.applyTechLevel}',
        unitName: '${result.unitName}',
        reward: '${result.reward}',
        studentPhoto: '${result.studentPhoto}',
        skillSpeciality: '${result.skillSpeciality}',
        achievement: '${result.achievement}',
        workExperience: getArrStr('${result.workExperience}'),
        attachments:attachments,
        appraisalForms:'${result.appraisalForms}' ? JSON.parse('${result.appraisalForms}') : [],
        technicalSummaryForms:'${result.technicalSummaryForms}' ? JSON.parse('${result.technicalSummaryForms}') : [],
        thesisForms:'${result.thesisForms}' ? JSON.parse('${result.thesisForms}') : [],
        jsWorkAchievementForms:'${result.jsWorkAchievementForms}' ? JSON.parse('${result.jsWorkAchievementForms}') : [],
        latentAbilityForms:'${result.latentAbilityForms}' ? JSON.parse('${result.latentAbilityForms}') : [],
        certificatesForms:'${result.certificatesForms}' ? JSON.parse('${result.certificatesForms}') : [],
      })
      const rules = reactive({
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' },
        ],
        birthday: [
          { required: true, message: '请选择出生年月', trigger: 'change' },
        ],
        politicalType: [
          { required: true, message: '请选择政治面貌', trigger: 'change' },
        ],
        workTime: [
          { required: true, message: '请选择从业时间', trigger: 'change' },
        ],
        // oldTechLevel: [
        //   { required: true, message: '请选择原职业资格等级', trigger: 'change' },
        // ],
        certiAddress: [
          { required: true, message: '请输入籍贯', trigger: 'blur' },
        ],
        nation: [
          { required: true, message: '请选择民族', trigger: 'change' },
        ],
        education: [
          { required: true, message: '请选择文化程度', trigger: 'change' },
        ],
        workYears: [
          { required: true, message: '请输入从事本职业工作年限', trigger: 'blur' },
        ],
        profession: [
          { required: true, message: '请输入现职业', trigger: 'blur' },
        ],
        jsTime: [
          { required: true, message: '请输入取得技师资格时间', trigger: 'blur' },
        ],
        gw: [
          { required: true, message: '请输入现岗位', trigger: 'blur' },
        ],
        company: [
          { required: true, message: '请输入工作单位', trigger: 'blur' },
        ],
        sfzh: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
        ],
        professionId: [
          { required: true, message: '请选择申报工种', trigger: 'change' },
        ],
        applyTechLevel: [
          { required: true, message: '请选择级别', trigger: 'change' },
        ],
        unitName: [
          { required: true, message: '请选择直属单位', trigger: 'change' },
        ],
        reward: [
          { required: true, message: '请输入何时何地受过何种奖励', trigger: 'blur' },
        ],
        skillSpeciality: [
          { required: true, message: '请输入主要技术特长、贡献及成果', trigger: 'blur' },
        ],
        achievement:[
          { required: true, message: '请输入工作业绩', trigger: 'blur' },
        ],
        mobile: [
          { required: true, message: '请输入工作业绩', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error(`请输入手机号`))
              }else if (!/^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/.test(value)) {
                callback(new Error(`手机号格式不正确`))
              } else {
                callback()
              }
            }, trigger: 'blur' },
        ],
      })

      const handleAddArr = (key) => {
        ruleForm[key].push({
          a: '',
          b: '',
          c: '',
          d: '',
          e: '',
          key: Date.now(),
        })
      }
      const handleDelArr = (key, index) => {
        ruleForm[key].splice(index, 1)
      }

      const submitForm = async (formEl) => {
        console.log(ruleForm);
        if (!formEl) return
        await formEl.validate((valid, fields) => {
          if (valid) {
            console.log(ruleForm)
            let params = {...ruleForm,bmbatchId:bmbatchId.value,industryId:industryId.value}
            console.log(params)
            const keys = ['workExperience']
            keys.map(key => {
              params[key] = JSON.stringify(params[key].map(item => {
                delete item.key
                item = Object.values(item).filter(value => value)
                return item.join('|^|')
              }))
            })
            params.attachments = JSON.stringify(params.attachments)
            params.appraisalForms = JSON.stringify(params.appraisalForms)
            params.technicalSummaryForms = JSON.stringify(params.technicalSummaryForms)
            params.thesisForms = JSON.stringify(params.thesisForms)
            params.jsWorkAchievementForms = JSON.stringify(params.jsWorkAchievementForms)
            params.latentAbilityForms = JSON.stringify(params.latentAbilityForms)
            params.certificatesForms = JSON.stringify(params.certificatesForms)
            params = JSON.stringify(params);
            layer.load(1);
            $.ajax({
              type: "post",
              url: "/kaosheng/mobile/zyjd/bm/edit",
              data: params,
              dataType: "json",
              contentType: "application/json",
              success: function (ret) {
                layer.closeAll('loading');
                if (ret.code == 0) {
                  layer.confirm('报名信息修改成功!', {icon: 6}, function (index) {
                    window.opener = null;
                    window.open("", "_self");
                    window.close();
                    layer.close(index);
                  });
                } else {
                  layer.alert(ret.message, {icon: 5});
                }
              },
              error: function () {
                layer.closeAll('loading');
                layer.alert('报名信息修改失败，请稍后重试', {icon: 5});
              }
            });
            console.log('submit!')
          } else {
            console.log('error submit!', fields)
          }
        })
      }

      const handleHttpUpload = async (options) => {
        const { file, data: key } = options;
        const reader = new FileReader();
        reader.onload = (e) => {
          utils.compressAndUploadImage(file, (url) => {
            ruleForm[key] = url
            ruleFormRef.value?.validateField([key])
            ElementPlus.ElMessage({
              title: '温馨提示',
              message: '图片上传成功！',
              type: 'success',
            });
          })
        };
        reader.readAsDataURL(file);
      };

      const handleHttpUpload2 = async (options) => {
        const { file,data: key  } = options;
        const reader = new FileReader();
        reader.onload = (e) => {
          utils.uploadFileNoLogin(file, (url) => {
            ruleForm[key].push({url,name: file.name})
            ruleFormRef.value?.validateField([key])
            ElementPlus.ElMessage({
              title: '温馨提示',
              message: '文件上传成功！',
              type: 'success',
            });
          })
        };
        reader.readAsDataURL(file);
      };

      const handleRemove = (file, fileList) => {
        const key = file.data
        // 从文件列表中移除
        ruleForm[key] = ruleForm[key].filter(f => f.name !== file.name);
      };

      const uploadSuccess = () => {
      };

      /**
       * @description 图片上传错误
       * */
      const uploadError = () => {
        ElementPlus.ElMessage({
          title: '温馨提示',
          message: '图片上传失败，请您重新上传！',
          type: 'error',
        });
      };

      const politicalOptions = ref([
        { value: "TY", label: "中国共青团团员"},
        { value: "DY", label: "中国共产党党员"},
        { value: "MZ", label: "民族党派人士"},
        { value: "WDPMZ", label: "无党派民主人士"},
        { value: "QT", label: "群众"},
      ])

      const levelOptions = ref([
        { value: "ONE", label: "高级技师"},
        { value: "TWO", label: "技师"},
      ])

      const eduOptions = ref([
        { value: "GAOZHONG", label: "高中（中专）及以下"},
        { value: "DAZHUNAN", label: "专科"},
        { value: "BENKE", label: "本科"},
        { value: "YANJIUSHENG", label: "研究生及以上"},
      ])

      const updateLabelPosition = () => {
        if (window.innerWidth > 1200) {
          labelPosition.value = 'right';
        } else {
          labelPosition.value = 'top';
        }
      };

      const changeSbzy = (val) => {
        if (!val) {
          levelList.value = [];
          ruleForm.applyTechLevel = '';
          return
        }
        layer.load(1);
        $.ajax({
          type: "post",
          url: "/kaosheng/mobile/zyjd/bm/getLevelAndDirection",
          timeout: 180000,
          async: false,
          data: {
            bmbatchId: bmbatchId.value,
            industryId: industryId.value,
            professionId: val
          },
          dataType: "json",
          success: function (ret) {
            layer.closeAll('loading');
            if (ret.code == 0) {
              //申报等级
              levelList.value = ret.data.levelList || [];
            } else {
              layer.alert(ret.message, {icon: 5});
            }
          },
          error: function () {
            layer.closeAll('loading');
            layer.alert('获取职业等级、职业方向信息失败,请稍后重试', {icon: 5});
          }
        });
      }
      changeSbzy(ruleForm.professionId)

      const handleNow = (key, index) => {
        if (isDisabled.value) {
          return;
        }
        if (ruleForm[key][index].b == '至今') {
          ruleForm[key][index].b = ''
          return
        }
        ruleForm[key][index].b = '至今'
        ruleFormRef.value?.validateField([key + '.' + index + '.b'])
      }

      onMounted(() => {
        updateLabelPosition();
        window.addEventListener('resize', updateLabelPosition);

        const { createEditor, createToolbar } = window.wangEditor

        const editorConfig = {
          placeholder: '何时何地受过何种奖励',
          readOnly: isDisabled.value,
          onChange(editor) {
            const html = editor.getHtml()
            console.log('editor content', html)
            if (html != '<p><br></p>') {
              ruleForm.reward = html
              ruleFormRef.value?.validateField(['reward'])
            } else {
              ruleForm.reward = ''
            }
          }
        }

        const editor = createEditor({
          selector: '#editor-container',
          html: ruleForm.reward,
          config: editorConfig,
          mode: 'simple', // or 'simple'
        })

        const toolbarConfig = {
          toolbarKeys: [
            "bold","underline","italic","through","clearStyle","headerSelect","header1","header2","header3",
            "color","bgColor","blockquote","emotion",
            "indent","delIndent","justifyLeft","justifyRight","justifyCenter","lineHeight","redo","undo","divider",
            "bulletedList","numberedList"
          ]
        }

        const toolbar = createToolbar({
          editor,
          selector: '#toolbar-container',
          config: toolbarConfig,
          mode: 'simple', // or 'simple'
        })

        const editorConfig2 = {
          placeholder: '工作业绩',
          readOnly: isDisabled.value,
          onChange(editor) {
            const html = editor.getHtml()
            console.log('editor content', html)
            if (html != '<p><br></p>') {
              ruleForm.achievement = html
              ruleFormRef.value?.validateField(['achievement'])
            } else {
              ruleForm.achievement = ''
            }
          }
        }

        const editor2 = createEditor({
          selector: '#editor-container2',
          html: ruleForm.achievement,
          config: editorConfig2,
          mode: 'simple', // or 'simple'
        })

        const toolbar2 = createToolbar({
          editor,
          selector: '#toolbar-container2',
          config: toolbarConfig,
          mode: 'simple', // or 'simple'
        })

      });

      onUnmounted(() => {
        window.removeEventListener('resize', updateLabelPosition);
      });

      return {
        politicalOptions,
        levelOptions,
        eduOptions,
        handleAddArr,
        handleDelArr,

        active,
        title,
        labelPosition,
        ruleFormRef,
        ruleForm,
        rules,
        submitForm,
        handleHttpUpload,
        handleHttpUpload2,
        handleRemove,
        uploadSuccess,
        uploadError,
        bmbatchId,
        industryId,
        professionList,
        levelList,
        unitList,
        changeSbzy,
        handleNow,
        isDisabled
      };
    },
  });

  // 使用Element Plus组件
  app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  });
  app.mount('#app');
</script>
</body>
</html>