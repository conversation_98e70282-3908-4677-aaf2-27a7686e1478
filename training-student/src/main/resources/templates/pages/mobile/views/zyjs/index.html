<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${currentHostOrg.name}</title>
  <!-- 引入Vue 3 CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- 引入Element Plus CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <!-- 引入Element Plus JavaScript 库 -->
  <script src="https://unpkg.com/element-plus"></script>
  <script src="https://unpkg.com/element-plus/dist/locale/zh-cn"></script>
  <script src="/js/jquery-1.9.1.min.js"></script>
  <script type="text/javascript" src="/comm/utils.js" ></script>
  <script type="text/javascript" src="/plugins/layer/layer.js" ></script>
  <style>
    /* :root {
      --el-font-size-base: 16px;
    } */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
    }
    #app {
      background-color: #F4F7FF;
      width: 100%;
      min-height: 100%;
    }
    .header {
      height: 380px;
      background-image: url(/mobile/images/header-bg.png);
      background-size: 100% 100%;
      text-align: center;
    }
    .content {
      width: 1200px;
      margin: -190px auto;
      padding-bottom: 70px;
    }
    .title {
      padding-top: 70px;
      font-family: Source Han Sans SC, Source Han Sans SC;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
      letter-spacing: 3px;
      text-shadow: 0px 2px 4px rgba(0,0,0,0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .login {
      display: inline-block;
      margin-top: 16px;
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      font-size: 26px;
      color: #FFE500;
    }
    .category {
      display: flex;
      align-items: center;
      width: 110%;
      color: #085CE0;
      font-family: PingFang SC, PingFang SC;
      font-weight: 800;
      font-size: 20px;
      margin-bottom: 26px;
    }
    .category-title {
      margin: 0 14px 0 2px;
    }
    .line {
      flex: 1;
      border: 1px dashed #085CE0;
    }
    .form-box {
      background-color: #fff;
      padding-bottom: 30px;
      border-radius: 10px;
    }
    .form {
      padding: 50px 285px 30px 120px;
    }

    .el-form-item {
      margin-bottom: 20px;
    }
    .el-form-item__label {
      padding-right: 20px;
    }

    .avatar-uploader .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .avatar-uploader .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      text-align: center;
    }

    .detail-box {
      padding: 0 14px;
      margin-bottom: 14px;
      background-color: #fff;
      border-radius: 6px;
      font-size: 14px;
      color: #333;
    }

    .detail-item {
      display: flex;
      padding: 10px 0;
      font-weight: 500;
      border-bottom: 1px solid #F6F6F6;
    }

    .detail-item:last-child {
      border: none;
    }

    .label {
      flex-shrink: 0;
      margin-right: 16px;
      width: 100px;
      color: #888888;
    }
    
    @media screen and (max-width: 1200px) {
      #app {
        background-image: url(/mobile/images/h5-bg.png);
        background-size: 100% 100%;
        height: 100%;
        overflow: auto;
      }
      .header {
        height: auto;
        background-image: none;
      }
      .title {
        padding-top: 30px;
        font-size: 20px;
        text-shadow: 0px 0px 2px #255B90;
      }
      .login {
        display: block;
        margin: 14px 14px 14px;
        padding: 12px 14px;
        background: #FFE500;
        border-radius: 5px;
        font-weight: bold;
        font-size: 14px;
        color: #065ADE;
        text-align: left;
      }
      .category {
        width: 100%;
        color: #fff;
        font-size: 14px;
      }
      .line {
        border-color: #F5F5F5;
      }
      .content {
        width: 100%;
        margin: 14px auto;
        padding-bottom: 20px;
      }
      .form-box {
        margin: 14px;
        padding-bottom: 0;
        background-color: transparent;
      }
      .form {
        padding: 12px 12px 8px 12px;
      }
      .el-form {
        background-color: #fff;
        border-radius: 6px;
        margin-bottom: 10px;
      }
      .el-form-item {
        margin-bottom: 14px;
      }

      .category {
        margin-bottom: 10px;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="header">
      <div class="title">高级研修班报名信息采集</div>
      <div class="login">${studentInfo.name}，欢迎您！</div>
    </div>

    <div class="content">
      <div class="form-box">
        <el-form
          v-if="labelPosition == 'right'"
          class="form"
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="246px"
          class="demo-ruleForm"
          status-icon
          :label-position="labelPosition"
        >
          <div class="category">
            <img src="/mobile/images/starX2.png" alt="">
            <div class="category-title">基本信息</div>
            <div class="line"></div>
          </div>
          <el-form-item v-for="item in formOpt" :key="item.prop" :label="item.label + ':'" :prop="item.prop">
            <el-input disabled v-model="ruleForm[item.prop]"  />
          </el-form-item>
          <div class="category">
            <img src="/mobile/images/starX2.png" alt="">
            <div class="category-title">培训信息</div>
            <div class="line"></div>
          </div>
          <el-form-item v-for="item in formOpt2" :key="item.prop" :label="item.label + ':'" :prop="item.prop">
            <el-input disabled v-model="ruleForm[item.prop]"  />
          </el-form-item>
          <el-form-item label="报名回执:" prop="receipt">
            <el-upload
              action="#"
              :id="uuid"
              :class="['avatar-uploader', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
              :multiple="false"
              disabled
              :show-file-list="false"
              :http-request="handleHttpUpload"
              :before-upload="beforeUpload"
              :on-success="uploadSuccess"
              :on-error="uploadError"
              :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
            >
              <template v-if="ruleForm.receipt">
                <img :src="ruleForm.receipt" class="avatar" />
              </template>
              <template v-else>
                <el-icon class="avatar-uploader-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </el-icon>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="状态:" prop="status_str">
          	<el-input disabled v-model="ruleForm['status_str']" />
          </el-form-item>
          <el-form-item v-if="ruleForm.status != 'YTJ'" label="审核时间:" prop="approveTime">
          	<el-input disabled v-model="ruleForm['approveTime']" />
          </el-form-item>
          <el-form-item v-if="ruleForm.status == 'BH'" label="原因:" prop="approveAdvice">
          	<el-input disabled v-model="ruleForm['approveAdvice']" />
          </el-form-item>
          <el-form-item v-if="ruleForm.status == 'BH'" label="操作:">
           	<el-button type="danger" onclick="deleteBmInfo('${bmInfo.id}')">删除重报</el-button>
          	<el-button type="primary" onclick="editBminfo('${bmInfo.id}')">修改</el-button>
          </el-form-item>
        </el-form>
        <div v-else>
          <div class="category">
            <img src="/mobile/images/star.png" alt="">
            <div class="category-title">基本信息</div>
            <div class="line"></div>
          </div>
          <el-form
            class="form"
            ref="ruleFormRef"
            :model="ruleForm"
            label-position="top"
            disabled
          >
            <el-form-item v-for="item in formOpt" :key="item.prop" :label="item.label + ':'" :prop="item.prop">
              <el-input v-model="ruleForm[item.prop]"  />
            </el-form-item>
          </el-form>
          <div class="category">
            <img src="/mobile/images/star.png" alt="">
            <div class="category-title">培训信息</div>
            <div class="line"></div>
          </div>
          <el-form
            class="form"
            ref="ruleFormRef"
            :model="ruleForm"
            label-position="top"
          >
            <el-form-item v-for="item in formOpt2" :key="item.prop" :label="item.label + ':'" :prop="item.prop">
              <el-input disabled v-model="ruleForm[item.prop]"  />
            </el-form-item>
            <el-form-item label="报名回执:" prop="receipt">
              <el-upload
                action="#"
                :id="uuid"
                :class="['avatar-uploader', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
                :multiple="false"
                disabled
                :show-file-list="false"
                :http-request="handleHttpUpload"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                :on-error="uploadError"
                :accept="['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].join(',')"
              >
                <template v-if="ruleForm.receipt">
                  <img :src="ruleForm.receipt" class="avatar"/>
                </template>
                <template v-else>
                  <el-icon class="avatar-uploader-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </el-icon>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="状态:" prop="status_str">
	          	<el-input disabled v-model="ruleForm['status_str']" />
	          </el-form-item>
	          <el-form-item v-if="ruleForm.status != 'YTJ'" label="审核时间:" prop="approveTime">
	          	<el-input disabled v-model="ruleForm['approveTime']" />
	          </el-form-item>
	          <el-form-item v-if="ruleForm.status == 'BH'" label="原因:" prop="approveAdvice">
	          	<el-input disabled v-model="ruleForm['approveAdvice']" />
	          </el-form-item>
	          <el-form-item v-if="ruleForm.status == 'BH'" label="操作:">
	           	<el-button type="danger" onclick="deleteBmInfo('${bmInfo.id}')">删除重报</el-button>
	          	<el-button type="primary" onclick="editBminfo('${bmInfo.id}')">修改</el-button>
	          </el-form-item>
          </el-form>
          <!-- <div class="detail-box">
            <div class="detail-item" v-for="item in formOpt2">
              <div class="label">{{ item.label }}</div>
              <div>{{ ruleForm[item.prop] }}</div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, reactive, onMounted, onUnmounted } = Vue;
    const app = createApp({
      setup() {
        const name = ref('')
        const formOpt = [
          { label: '报名批次', prop: 'bmbatchName' },
          { label: '行政区划代码', prop: 'regionalismCode' },
          { label: '证件类型', prop: 'certiCategory' },
          { label: '证件号码', prop: 'sfzh' },
          { label: '手机号', prop: 'mobile' },
          { label: '姓名', prop: 'name' },
          { label: '性别', prop: 'xb' },
          { label: '民族', prop: 'mz' },
          { label: '出生日期', prop: 'birthday' },
          { label: '学历', prop: 'xl' },
          { label: '学位', prop: 'xw' },
        ]
        const formOpt2 = [
          { label: '所在单位统一社会信用代码', prop: 'socialCreditCode' },
          { label: '所在单位名称', prop: 'company' },
          { label: '所在单位性质', prop: 'unitNature' },
          { label: '是否由所在单位提供继续教育培训', prop: 'isProvideJxjy' },
          { label: '取得证书类型', prop: 'obtainCertiCategory' },
          { label: '职称系列', prop: 'zcSeries' },
          { label: '职称名称', prop: 'zc' },
          { label: '职称级别', prop: 'zcLevel' },
          { label: '职业资格名称', prop: 'zyQualificationName' },
          { label: '职业资格等级', prop: 'zyQualificationLevel' },
        ]
        const labelPosition = ref('top')
        const ruleFormRef = ref()
        var certiCategory = '${bmInfo.certiCategory}'
       	<#if select?? && select.certiCategorys??>
			<#list select.certiCategorys as item>
				<#if item.dictValue == bmInfo.certiCategory>
					certiCategory = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var xb = '${bmInfo.xb}'
		<#if select?? && select.xbs??>
			<#list select.xbs as item>
				<#if item.dictValue == bmInfo.xb>
					xb = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var mz = '${bmInfo.mz}'
		<#if select?? && select.mzs??>
			<#list select.mzs as item>
				<#if item.dictValue == bmInfo.mz>
					mz = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var xl = '${bmInfo.xl}'
		<#if select?? && select.xls??>
			<#list select.xls as item>
				<#if item.dictValue == bmInfo.xl>
					xl = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var xw = '${bmInfo.xw}'
		<#if select?? && select.xws??>
			<#list select.xws as item>
				<#if item.dictValue == bmInfo.xw>
					xw = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var unitNature = '${bmInfo.unitNature}'
		<#if select?? && select.unitNatures??>
			<#list select.unitNatures as item>
				<#if item.dictValue == bmInfo.unitNature>
					unitNature = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var isProvideJxjy = ''
		<#if '1' == bmInfo.isProvideJxjy>
			isProvideJxjy = '是'
		<#else>
			isProvideJxjy = '否'
		</#if>
			
		var obtainCertiCategory = ''
		<#if '1' == bmInfo.obtainCertiCategory>
			obtainCertiCategory = '职称证书'
		<#elseif '2' == bmInfo.obtainCertiCategory>
			obtainCertiCategory = '职业资格证书'
		</#if>
			
		var zcSeries = '${bmInfo.zcSeries}'
		<#if select?? && select.zcSeriess??>
			<#list select.zcSeriess as item>
				<#if item.dictValue == bmInfo.zcSeries>
					zcSeries = '${item.dictName}'
				</#if>
			</#list>
		</#if>
			
		var zc = '${bmInfo.zc}'
		<#if select?? && select.zcs??>
			<#list select.zcs as item>
				<#if item.dictValue == bmInfo.zc>
					zc = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var zcLevel = '${bmInfo.zcLevel}'
		<#if select?? && select.zcLevels??>
			<#list select.zcLevels as item>
				<#if item.dictValue == bmInfo.zcLevel>
					zcLevel = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var zyQualificationName = '${bmInfo.zyQualificationName}'
		<#if select?? && select.zyQualificationNames??>
			<#list select.zyQualificationNames as item>
				<#if item.dictValue == bmInfo.zyQualificationName>
					zyQualificationName = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var zyQualificationLevel = '${bmInfo.zyQualificationLevel}';
		<#if select?? && select.zyQualificationLevels??>
			<#list select.zyQualificationLevels as item>
				<#if item.dictValue == bmInfo.zyQualificationLevel>
					zyQualificationLevel = '${item.dictName}'
				</#if>
			</#list>
		</#if>
		
		var status_str = ''
		<#if bmInfo.status=='YTJ'>
			status_str = '待审核'
		</#if>
		<#if bmInfo.status=='SHTG' || bmInfo.status=='DBK'>
			status_str = '审核通过'
		<#else>
			<#if bmInfo.status=='BH'>
				status_str = '审核不通过'
			</#if>
		</#if>
        const ruleForm = reactive({
          id: '${bmInfo.id}',
          bmbatchName: '${bmInfo.bmbatchName}',
          regionalismCode: '${bmInfo.regionalismCode}',
          certiCategory: certiCategory,
          sfzh: '${bmInfo.sfzh}',
          mobile: '${bmInfo.mobile}',
          name: '${bmInfo.name}',
          xb: xb,
          mz: mz,
          birthday: '${bmInfo.birthday}',
          xl: xl,
          xw: xw,
          socialCreditCode: '${bmInfo.socialCreditCode}',
          company: '${bmInfo.company}',
          unitNature: unitNature,
          isProvideJxjy: isProvideJxjy,
          obtainCertiCategory: obtainCertiCategory,
          zcSeries: zcSeries,
          zc: zc,
          zcLevel: zcLevel,
          zyQualificationName: zyQualificationName,
          zyQualificationLevel: zyQualificationLevel,
          receipt: '${bmInfo.receipt}',
          status: '${bmInfo.status}',
          status_str: status_str,
          approveTime: '${bmInfo.approveTime}',
          approveAdvice: '${bmInfo.approveAdvice}'
        })

        const rules = reactive({
          sfzh: [
            { min: 6, message: '证件号码长度必须大于等于6位', trigger: 'blur' },
          ],
          mobile: [
		        { validator: (rule, value, callback) => {
              if (!/^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/.test(value)) {
                callback(new Error(`手机号格式不正确`))
              } else {
                callback()
              }
            }, trigger: 'blur' },
          ],
          zcSeries: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称系列'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zc: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称名称'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zcLevel: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '1' && !value) {
                  callback(new Error('请选择职称级别'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zyQualificationName: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '2' && !value) {
                  callback(new Error('请选择职业资格名称'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          zyQualificationLevel: [
            {
              validator: (rule, value, callback) => {
                if (ruleForm.obtainCertiCategory === '2' && !value) {
                  callback(new Error('请选择职业资格等级'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        })

        const handleHttpUpload = async (options) => {
          const { file } = options;
          const reader = new FileReader();
          reader.onload = (e) => {
            ruleForm.receipt = e.target.result
            ruleFormRef.value?.validateField(['receipt'])
          };
          reader.readAsDataURL(file);
        };

        const uploadSuccess = () => {
          ElementPlus.ElMessage({
            title: '温馨提示',
            message: '图片上传成功！',
            type: 'success',
          });
        };

        /**
         * @description 图片上传错误
         * */
        const uploadError = () => {
          ElementPlus.ElMessage({
            title: '温馨提示',
            message: '图片上传失败，请您重新上传！',
            type: 'error',
          });
        };

        const isRequired = (prop) => {
          // 根据具体的条件判断字段是否为必填项
          if (prop === 'zcSeries' || prop === 'zc' || prop === 'zcLevel' ) {
            return ruleForm.obtainCertiCategory === '1'; 
          } else if (prop === 'zyQualificationName' || prop === 'zyQualificationLevel') {
            return ruleForm.obtainCertiCategory === '2'; 
          }
        };

        const updateLabelPosition = () => {
          if (window.innerWidth > 1200) {
            labelPosition.value = 'right';
          } else {
            labelPosition.value = 'top';
          }
        };

        onMounted(() => {
          updateLabelPosition();
          window.addEventListener('resize', updateLabelPosition);
        });

        onUnmounted(() => {
          window.removeEventListener('resize', updateLabelPosition);
        });
 
        return {
          name,
          formOpt,
          formOpt2,
          labelPosition,
          ruleFormRef,
          ruleForm,
          rules,
          handleHttpUpload,
          uploadSuccess,
          uploadError,
          isRequired,
        };
      },
    });
    
    // 使用Element Plus组件
    app.use(ElementPlus, {
      locale: ElementPlusLocaleZhCn,
    });
    app.mount('#app');
    
    function deleteBmInfo(id){
    	layer.confirm('确定删除这条报名信息并重新报名吗?', {
    		btn: ['确定','取消']
    	}, function(){
    		  layer.closeAll();
    		  layer.load(1);
    		  $.ajax({
    			  async:false,
    	          type: "post",
    	          url: "/kaosheng/mobile/zyjd/bm/deleteById",
    	          data: {
    	        	  "t" : Math.random(),
    	        	  id : id
    	          },
    	          dataType: "json",
    	          success: function(ret){
    				   layer.closeAll('loading');
    		           if(ret.code == 0){
    		        	   layer.alert('报名信息删除成功,即将跳转到报名页面',{icon:6},function(){
    		                	document.location.href="${request.contextPath}/kaosheng/mobile/zyjd/bm/startPage/${bmbatch.id}";
    		               });
    		           }else{
    		        	   layer.alert(ret.message,{icon:5});
    		          }
    	          },
    	          error:function(){
    	          	layer.closeAll('loading');
    	          	layer.alert('报名信息删除失败，请稍后重试',{icon:5});
    	          }
    	      });
    	});
    }
    
    function editBminfo(id){
    	document.location.href="${request.contextPath}/kaosheng/mobile/zyjd/bm/startPage/${bmbatch.id}?id="+id;
    }
  </script>
</body>
</html>