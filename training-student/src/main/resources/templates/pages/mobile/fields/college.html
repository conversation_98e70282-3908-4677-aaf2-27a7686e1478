<div class="form-group show_college" <#if field.showRule??>style="display:none"</#if>>
     <label class="col-lg-4 control-label">
      <#if (field.required == true || field.requiredRule??)><span class="required">*</span></#if>${field.label}：
     </label>
    <div class="col-lg-5">
    	<select name="college" id="college" class="form-control">
    		<option value="">-请选择-</option>
    		<#if (colleges?? && colleges?size>0)>
    			<#list colleges as clg>
    				<option value="${clg.name}">${clg.name}</option>
    			</#list>
    		</#if>
    	</select>
    	<#if field.tips??><span class="text-primary">${field.tips}</span></#if>
    </div>
   <div class="col-lg-3"></div>
</div>
<script type="text/javascript">
	var formConfigJson = window.formConfigJson || JSON.parse('${formConfigJson}');
	for (var i=0;i<formConfigJson.fields.length;i++) {
		if(formConfigJson.fields[i].name == 'college' && !formConfigJson.fields[i].validators){
			var validators = {};
			if(formConfigJson.fields[i].required){
				validators.notEmpty = {
					message: '请输入${field.label}'
		        }
			}
			formConfigJson.fields[i].validators = validators;
			break;
		}
	}
	$(function(){
		var college = '${result.college}';
		if(college){
			$('*[name="college"]').val(college);
		}
	})
</script>