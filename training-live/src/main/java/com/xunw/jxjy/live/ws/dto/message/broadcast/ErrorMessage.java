package com.xunw.jxjy.live.ws.dto.message.broadcast;

public class ErrorMessage extends ReturnMessage {

    String errorMessage;

    public ErrorMessage(String sendUserId, String errorMessage) {
        this.code = -1;                 //0成功 -1异常
        this.sendUserId = sendUserId;   //消息发送者 userId
        this.errorMessage = errorMessage;
    }

    public ErrorMessage(String errorMessage) {
        this.code = -1;                 //0成功 -1异常
        this.errorMessage = errorMessage;             //错误文本
    }

    public ErrorMessage(){
        this.code = -1;                 //0成功 -1异常
    }

    public ErrorMessage withErrorMessage(String error) {
        this.errorMessage = error;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
