package com.xunw.jxjy.live.web;

import com.xunw.jxjy.live.web.exception.BusinessException;
import com.xunw.jxjy.live.web.exception.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@ControllerAdvice
public class GlobeExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobeExceptionHandler.class);//记录打印日志用的

    @ExceptionHandler
    @ResponseBody
    public CommonResponse exceptionHandler(HttpServletRequest req, HttpServletResponse res, Exception e) {

        if (e instanceof BusinessException) {
            return getBusinessResponse(((BusinessException) e));
        }

        CommonResponse commonResponse = new CommonResponse();

        if (e != null && e.getMessage() != null && e.getMessage().startsWith("JSON parse error")) {
            commonResponse.setCode(ResponseStatus.argumentsError.code);
            commonResponse.setMessage("参数不是一个合法的json");
            return commonResponse;
        }

        commonResponse.setCode(ResponseStatus.commError.code);
        commonResponse.setMessage((e != null && e.getMessage() != null) ? e.getMessage() : ResponseStatus.commError.msg);
        return commonResponse;
    }

    private static CommonResponse getBusinessResponse(BusinessException e) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setMessage(e.getMessage());
        commonResponse.setCode(e.getCode());
        return commonResponse;
    }

    public static CommonResponse getResponse(Throwable e) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(ResponseStatus.commError.code);
        commonResponse.setMessage(e == null || e.getMessage() == null ? ResponseStatus.commError.msg : e.getMessage());
        return commonResponse;
    }

}
