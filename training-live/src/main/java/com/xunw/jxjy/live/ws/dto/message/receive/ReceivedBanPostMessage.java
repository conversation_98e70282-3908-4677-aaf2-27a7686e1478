package com.xunw.jxjy.live.ws.dto.message.receive;


import com.xunw.jxjy.live.ws.dto.message.Message;

public class ReceivedBanPostMessage extends Message {

    /**
     * 禁言的目标用户 userId
     */
    String targetUserId;
    /**
     * 1 解除禁言，  0 禁言
     */
    int isClear;
    /**
     * 1 全局禁言，  0 指定用户禁言
     */
    int isBanPostAll;

    public String getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(String targetUserId) {
        this.targetUserId = targetUserId;
    }

    public int getIsClear() {
        return isClear;
    }

    public void setIsClear(int isClear) {
        this.isClear = isClear;
    }

    public int getIsBanPostAll() {
        return isBanPostAll;
    }

    public void setIsBanPostAll(int isBanPostAll) {
        this.isBanPostAll = isBanPostAll;
    }
}
