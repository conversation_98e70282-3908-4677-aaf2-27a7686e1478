package com.xunw.jxjy.live.ws.dto.message.receive;


import com.xunw.jxjy.live.ws.dto.message.Message;


public class ReceivedSpeakMessage extends Message {

    private String text;//发言内容如果是纯文本，存在 text中
    /**
     * @see com.xunw.jxjy.live.ws.config.SpeakType
     */
    private String type;//消息类型
    private String attachmentUrl;//如果是文件类型,语音图片等文件，把文件的七牛云地址存到这里

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }
}
