package com.xunw.jxjy.live.web;

import com.xunw.jxjy.live.web.exception.ResponseStatus;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.HashMap;

/**
 * json 响应
 */
public class CommonResponse implements Serializable {

    @ApiModelProperty("接口调用状态码 0 为成功 -1为异常")
    protected int code;

    @ApiModelProperty("接口调用失败原因")
    protected String message;

    @ApiModelProperty("接口返回业务数据")
    private Object data = new HashMap<>();

    public static CommonResponse success() {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(ResponseStatus.success.code);
        commonResponse.setMessage(ResponseStatus.success.msg);
        return commonResponse;
    }


    public static CommonResponse tokenErrorRep() {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setMessage("token error ！");
        commonResponse.setCode(101001);
        return commonResponse;
    }

    public static CommonResponse getUserLonginErrorRep() {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setMessage("用户信息拉取失败，请重新登录拉取最新信息");
        commonResponse.setCode(200026);
        return commonResponse;

    }

    public static CommonResponse success(Object data) {
        CommonResponse success = success();
        if (data == null) {
            success.setData(new HashMap<>());
        } else {
            success.setData(data);
        }
        return success;
    }

    public static CommonResponse fail(ResponseStatus responseStatus) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(responseStatus.code);
        commonResponse.setMessage(responseStatus.msg);
        return commonResponse;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
