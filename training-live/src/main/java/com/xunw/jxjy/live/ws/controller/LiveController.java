package com.xunw.jxjy.live.ws.controller;


import com.xiaoleilu.hutool.json.JSONObject;
import com.xunw.jxjy.live.stream.dto.SpeakHistoryDto;
import com.xunw.jxjy.live.web.CommonResponse;
import com.xunw.jxjy.live.ws.dto.request.CreateLiveReplayWatchRecordRequest;
import com.xunw.jxjy.live.ws.dto.request.CreateLiveRequest;
import com.xunw.jxjy.live.ws.dto.request.CreateNoteRequest;
import com.xunw.jxjy.live.ws.dto.request.KeepLiveReplayWatchHeartbeatRequest;
import com.xunw.jxjy.live.ws.dto.response.CourseInfoDto;
import com.xunw.jxjy.live.ws.dto.response.LiveInfoDto;
import com.xunw.jxjy.live.ws.dto.response.NoteRecordDto;
import com.xunw.jxjy.live.ws.dto.response.TeacherInfoDto;
import com.xunw.jxjy.live.ws.service.LiveService;
import com.xunw.jxjy.live.ws.service.WatchReplayRecordService;
import com.xunw.jxjy.live.ws.util.CacheHelper;
import com.xunw.jxjy.live.ws.util.SessionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "直播管理", description = "直播管理API")
@RestController
public class LiveController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiveController.class);//记录打印日志用的

    @Autowired
    LiveService liveService;
    @Autowired
    WatchReplayRecordService watchReplayRecordService;
    @Autowired
    SessionUtil sessionUtil;

    @ApiOperation(value = "查询直播间列表", notes = "userId为当前用户必填", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "当前用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isLiving", value = "是否正在直播", paramType = "query", dataType = "int")})
    @GetMapping("/getLiveList")
    public CommonResponse getLiveList(@RequestParam("userId") String userId, @RequestParam("liveId") String liveId, @RequestParam("isLiving") int isLiving) {
        //Pagination pagination = liveService.getLiveList(userId, liveId, isLiving);
        return CommonResponse.success(/*pagination*/);
    }

    @ApiOperation(value = "创建直播", notes = "userId为当前用户必填", httpMethod = "POST")
    @PostMapping("/createLive")
    public CommonResponse createLive(@RequestBody CreateLiveRequest request) {
        //liveService.createLive(request);
        return CommonResponse.success();
    }

    @ApiOperation(value = "添加笔记", notes = "userId为当前用户必填", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "考生用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "text", value = "笔记内容", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "timePoint", value = "视频的地多少秒做的笔记", paramType = "query", dataType = "Long")
    })
    @RequestMapping("/createNote")
    public CommonResponse createNote(HttpServletRequest request0, /*@RequestBody */CreateNoteRequest request /*@RequestParam("liveId") String liveId,
                                     @RequestParam("userId") String userId,
                                     @RequestParam("text") String text,
                                     @RequestParam("timePoint") Long timePoint*/) {
        //CreateNoteRequest request = new CreateNoteRequest();
        //request.setLiveId(liveId);
        //request.setUserId(userId);
        //request.setText(text);
        //request.setTimePoint(timePoint);
        liveService.createNote(request);
        return CommonResponse.success();
    }

    @ApiOperation(value = "保存录播的观看记录", notes = "开始时间时间(秒的时间戳)必填，截止时间服务端取当前时间", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "考生用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "Long")
    })
    @RequestMapping("/createLiveReplayWatchRecord")
    public CommonResponse createLiveReplayWatchRecord(HttpServletRequest request0, /*@Valid @RequestBody */CreateLiveReplayWatchRecordRequest request/*@RequestParam("liveId") String liveId,
                                                                                                                        @RequestParam("userId") String userId,
                                                                                                                        @RequestParam("startTime") Long startTime */) {
        //CreateLiveReplayWatchRecordRequest request = new CreateLiveReplayWatchRecordRequest();
        //request.setLiveId(liveId);
        //request.setUserId(userId);
        //request.setStartTime(startTime);
        //watchReplayRecordService.createLiveReplayWatchRecord(request);
        return CommonResponse.success();
    }


    /**
     * paramType :
     *       header-->放在请求头。请求参数的获取：@RequestHeader(代码中接收注解)
     *     query-->用于get请求的参数拼接。请求参数的获取：@RequestParam(代码中接收注解)
     *     path（用于restful接口）-->请求参数的获取：@PathVariable(代码中接收注解)
     *     body-->放在请求体。请求参数的获取：@RequestBody(代码中接收注解)
     *     form（不常用）
     *
     * @return
     */
    @ApiOperation(value = "发送录播的观看心跳", notes = "发送录播的观看心跳,一分钟调用一次"/*, httpMethod = "POST"*/)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "form", dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "考生用户ID", paramType = "form", dataType = "String")
    })
    @RequestMapping("/keepLiveReplayWatchHeartbeat")
    //public CommonResponse keepLiveReplayWatchHeartbeat(/*@Valid @RequestBody KeepLiveReplayWatchHeartbeatRequest request*/@RequestParam("liveId") String liveId,
    //                                                                                                                      @RequestParam("userId") String userId) {
    public CommonResponse keepLiveReplayWatchHeartbeat(HttpServletRequest request0, KeepLiveReplayWatchHeartbeatRequest request) {
        //log.info(String.valueOf(4/0));
        //KeepLiveReplayWatchHeartbeatRequest request = new KeepLiveReplayWatchHeartbeatRequest();
        //request.setLiveId(liveId);
        //request.setUserId(userId);
        watchReplayRecordService.keepLiveReplayWatchHeartbeat(request);
        return CommonResponse.success();
    }

    @ApiOperation(value = "录播的观看心跳检测间隔动态调整", notes = "录播的观看心跳检测间隔动态调整 输入新的时间间隔，单位：秒", httpMethod = "POST")
    @ApiImplicitParam(name = "invokeInterval", value = "心跳检测间隔", paramType = "query", dataType = "Long")
    @PostMapping("/liveReplayWatchRecordDynamicSetting")
    public CommonResponse liveReplayWatchRecordDynamicSetting(HttpServletRequest request0, @RequestParam("invokeInterval") Long invokeInterval) {
        watchReplayRecordService.liveReplayWatchRecordDynamicSetting(invokeInterval);
        return CommonResponse.success();
    }

    @ApiOperation(value = "获取当前直播间在线用户列表", notes = "liveId为当前直播间ID必填", httpMethod = "GET")
    @ApiImplicitParam(name = "liveId", value = "当前直播间ID", paramType = "query", dataType = "String")
    @RequestMapping("/getOnlineUserList")
    public CommonResponse getOnlineUserList(HttpServletRequest request0, @RequestParam("liveId") String liveId) {
        return CommonResponse.success(liveService.getOnlineUserList(liveId));
    }

    //@ApiOperation(value = "根据用户ID和直播ID获取直播间信息(进入直播间调用)", notes = "userId,liveId必填", httpMethod = "GET")
    //@ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "当前用户ID", paramType = "query", dataType = "String"),
    //        @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "Long")})
    //@GetMapping("/getLiveInfoByUserIdAndLiveId")
    //public CommonResponse getLiveInfoByUserIdAndLiveId(@RequestParam("userId") String userId, @RequestParam("liveId") String liveId) {
    //    LiveInfoDto response = liveService.getLiveInfoByUserIdAndLiveId0(userId, liveId);
    //    return CommonResponse.success(response);
    //}

    @ApiOperation(value = "获取直播间信息", notes = "liveId必填", httpMethod = "GET")
    @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String")
    @GetMapping("/getLiveInfoByLiveId")
    public /*LiveInfoDto*/CommonResponse getLiveInfoByLiveId(HttpServletRequest request0, @RequestParam("liveId") String liveId) {
        LiveInfoDto response = liveService.getLiveInfoByLiveId(liveId);
        //return response;
        return CommonResponse.success(response);
    }

    @ApiOperation(value = "获取教师信息", notes = "liveId必填", httpMethod = "GET")
    @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String")
    @GetMapping("/getTeacherInfoByLiveId")
    public /*TeacherInfoDto*/CommonResponse getTeacherInfoByLiveId(HttpServletRequest request0, @RequestParam("liveId") String liveId) {
        TeacherInfoDto response = liveService.getTeacherInfoByLiveId(liveId);
        //return response;
        return CommonResponse.success(response);
    }

    @ApiOperation(value = "获取课程信息", notes = "liveId必填", httpMethod = "GET")
    @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String")
    @GetMapping("/getCourseInfoByLiveId")
    public /*CourseInfoDto*/CommonResponse getCourseInfoByLiveId(HttpServletRequest request0, @RequestParam("liveId") String liveId) {
        CourseInfoDto response = liveService.getCourseInfoByLiveId(liveId);
        //return response;
        return CommonResponse.success(response);
    }

    @ApiOperation(value = "获取直播间发言历史", notes = "liveId直播id必填", httpMethod = "GET")
    @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "Long")
    @RequestMapping("/getSpeakHistoryListByLiveId")
    public /*List<SpeakHistoryDto>*/CommonResponse getSpeakHistoryListByLiveId(HttpServletRequest request0, @RequestParam("liveId") String liveId) {
        List<SpeakHistoryDto> speakHistoryList = liveService.getSpeakHistoryListByLiveId(liveId);
        //return speakHistoryList;
        return CommonResponse.success(speakHistoryList);
    }

    @ApiOperation(value = "获取直播笔记列表", notes = "userId,liveId必填", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "用户ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String")})
    @RequestMapping("/getNoteListByUserIdAndLiveId")
    public CommonResponse getNoteListByUserIdAndLiveId(HttpServletRequest request0, @RequestParam("userId") String userId, @RequestParam("liveId") String liveId) {
        System.out.println(userId+"        "+ liveId);
        List<NoteRecordDto> list = liveService.getNoteListByUserIdAndLiveId(userId, liveId);
        return CommonResponse.success(list);
    }


    @ApiOperation(value = "课间休息", notes = "liveId必填", httpMethod = "GET")
    @ApiImplicitParams(@ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String"))
    @RequestMapping("/offline")
    public CommonResponse offline(@RequestParam("liveId") String liveId) {
        JSONObject jSONObject = new JSONObject();
        jSONObject.put("code", "200");
        jSONObject.put("zbzt", "offline");
        sessionUtil.broadcastStrByLiveId(liveId, jSONObject);
        return CommonResponse.success("");
    }



    @ApiOperation(value = "恢复", notes = "liveId必填", httpMethod = "GET")
    @ApiImplicitParams(@ApiImplicitParam(name = "liveId", value = "直播ID", paramType = "query", dataType = "String"))
    @RequestMapping("/recovery")
    public CommonResponse recovery(@RequestParam("liveId") String liveId) {
        JSONObject jSONObject = new JSONObject();
        jSONObject.put("code", "200");
        jSONObject.put("zbzt", "recovery");
        sessionUtil.broadcastStrByLiveId(liveId, jSONObject);
        CacheHelper.getCache("liveId",liveId);
        return CommonResponse.success("");

    }


}
