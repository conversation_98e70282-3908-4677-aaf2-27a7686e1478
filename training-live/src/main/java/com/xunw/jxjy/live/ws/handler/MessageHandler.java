package com.xunw.jxjy.live.ws.handler;

import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import com.xiaoleilu.hutool.util.StrUtil;
import com.xunw.jxjy.live.common.util.DateUtil;
import com.xunw.jxjy.live.ws.config.MessageType;
import com.xunw.jxjy.live.ws.config.SpeakType;
import com.xunw.jxjy.live.ws.dto.OnlineUser;
import com.xunw.jxjy.live.ws.dto.message.Message;
import com.xunw.jxjy.live.ws.dto.message.broadcast.ErrorMessage;
import com.xunw.jxjy.live.ws.dto.message.broadcast.ReturnBanPostMessage;
import com.xunw.jxjy.live.ws.dto.message.broadcast.ReturnMessage;
import com.xunw.jxjy.live.ws.dto.message.broadcast.ReturnSpeakMessage;
import com.xunw.jxjy.live.ws.dto.message.receive.ReceivedBanPostMessage;
import com.xunw.jxjy.live.ws.dto.message.receive.ReceivedSpeakMessage;
import com.xunw.jxjy.live.ws.service.BanPostService;
import com.xunw.jxjy.live.ws.service.LiveService;
import com.xunw.jxjy.live.ws.util.SessionUtil;
import com.xunw.jxjy.live.ws.util.channerHelper.ChannelAttrKey;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import jodd.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageHandler.class);//记录打印日志用的

    @Autowired
    SessionUtil sessionUtil;
    @Autowired
    LiveService liveService;
    @Autowired
    BanPostService banPostService;

    public static void main(String[] args) {
        //String encode = Base64.getEncoder().encodeToString("/101/0/user1/userName1/avatar1".getBytes());
        //System.out.println(encode);
        //String decode = new String(Base64.getDecoder().decode(encode));
        //System.out.println(decode);
        //System.out.println(Base64.getEncoder().encodeToString("18961e25-f036-4c99-acf3-e7bbde09e984/0/user1/名字中文/avatar1".getBytes()));
        //
        //try {
        //    String decode = URLDecoder.decode("18961e25-f036-4c99-acf3-e7bbde09e984%2F0%2F8325e8ac-ec77-43a7-9cc5-2cfd999ec8de%2F%E7%94%B0%E5%86%9B%2Fhttp%3A%2F%2Fatt.whxunw.com%2Fjxjy_default_student_header.jpeg", "UTF-8");
        //    System.out.println(decode);
        //} catch (UnsupportedEncodingException e) {
        //    e.printStackTrace();
        //}

    }

    /**
     * 整个 web socket 网关的业务消息处理器，核心逻辑实现
     *
     * @param messageOriginal
     * @param ctx
     */
    public void handle(String messageOriginal, ChannelHandlerContext ctx) {
        LOGGER.info("MessageHandler: MessageHandler");
        Channel channel = ctx.channel();
        JSONObject messageJson = null;
        ErrorMessage errorMessage = new ErrorMessage();
        String liveId = "";
        try {

            if (StringUtil.isBlank(messageOriginal)) {
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("message is empty"));
                return;
            }

            //特殊字符过滤
            messageOriginal = messageOriginal.replaceAll("\\n", "");
            messageOriginal = messageOriginal.replaceAll("\n\r", "\\n\\r");
            messageOriginal = messageOriginal.replaceAll("\n", "\\n");
            if (messageOriginal.contains("\\")) {
                LOGGER.error("message with backslash: " + messageOriginal);
                String reg = "\\\\";
                messageOriginal = messageOriginal.replaceAll(reg, "");
                LOGGER.error("after remove backslash:" + messageOriginal);
            }
            LOGGER.info("receive message: " + JSONUtil.toJsonPrettyStr(messageOriginal));

            OnlineUser onlineUser = (OnlineUser) channel.attr(ChannelAttrKey.ONLINE_USER_INFO).get();
            if (onlineUser == null || StringUtil.isEmpty(onlineUser.getUserId())) {
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("can not get user info from channel"));
                return;
            }

            liveId = onlineUser.getLiveId();
            if (StringUtil.isBlank(liveId)) {
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("liveId is empty"));
                return;
            }

            messageJson = JSONUtil.parseObj(messageOriginal);
            messageJson.put("liveId", onlineUser.getLiveId());
            messageJson.put("sendUserId", onlineUser.getUserId());
            messageJson.put("isTeacher", onlineUser.getIsTeacher());
            String message = JSONUtil.toJsonStr(messageJson);

            //接收参数日志
            Integer messageId = liveService.saveMessageDebug(liveId, message);
            channel.attr(ChannelAttrKey.MESSAGE_ID).set(messageId);
            //上面是保存接收的，消息
            //把 messageId 关联到 channel,后面消息广播出去是关联这个 messageId

            Message abstractReceiveMessage = JSONUtil.toBean(message, Message.class);
            String messageType = abstractReceiveMessage.getMessageType();
            if (StringUtil.isBlank(messageType)) {
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("messageType is empty"));
                return;
            }
            if (Arrays.stream(MessageType.values()).noneMatch(x -> x.name().equalsIgnoreCase(messageType))) {
                String mustType = StrUtil.join(",", Arrays.stream(MessageType.values()).collect(Collectors.toSet()));
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("[" + mustType + "]messageType not exists:" + messageType));
                return;
            }

            ReturnMessage returnMessage = null;
            switch (MessageType.valueOf(messageType)) {
                case speak:
                    returnMessage = speakMessageHandle(liveId, channel, errorMessage, message);
                    break;
                case banPost:
                    returnMessage = banPostMessageHandle(liveId, channel, errorMessage, message);
                    break;
                //这俩消息是直接又服务端推送给客户端
                //case enter:
                //    returnMessage = JSONUtil.toBean(message, ReturnEnterMessage.class);
                //    break;
                //case leave:
                //    returnMessage = JSONUtil.toBean(message, ReturnLeaveMessage.class);
                //    break;
                default:
            }

            Objects.requireNonNull(returnMessage)
                    .withUserName(onlineUser.getUserName())
                    .withSendUserId(onlineUser.getUserId())
                    .withIsTeacher(onlineUser.getIsTeacher())
                    .withCurrentTime(DateUtil.getCurrentTimeString());

            sessionUtil.broadcastByLiveId(liveId, returnMessage);

        } catch (Throwable e) {
            sessionUtil.unicast(channel, errorMessage.withErrorMessage(e.getMessage()).withLiveId(liveId));
            e.printStackTrace();
        }
    }

    private ReturnMessage speakMessageHandle(String liveId, Channel channel, ErrorMessage errorMessage, String message) {

        ReturnMessage returnMessage = JSONUtil.toBean(message, ReturnSpeakMessage.class);

        //发言类型合法校验
        ReceivedSpeakMessage receivedSpeakMessage = JSONUtil.toBean(message, ReceivedSpeakMessage.class);
        String speakType = receivedSpeakMessage.getType();
        if (StringUtil.isBlank(speakType)) {
            sessionUtil.unicast(channel, errorMessage.withErrorMessage("speakType is empty, message text: " + speakType));
            return returnMessage;
        }
        if (Arrays.stream(SpeakType.values()).noneMatch(x -> x.name().equalsIgnoreCase(speakType))) {
            String mustType = StrUtil.join(",", Arrays.stream(SpeakType.values()).collect(Collectors.toSet()));
            sessionUtil.unicast(channel, errorMessage.withErrorMessage("[" + mustType + "]speakType not exists:" + speakType));
            return returnMessage;
        }

        //发言内容校验
        ReturnSpeakMessage returnSpeakMessage = (ReturnSpeakMessage) returnMessage;
        if (SpeakType.txt.name().equals(returnSpeakMessage.getType()) && StringUtil.isBlank(returnSpeakMessage.getText())) {
            sessionUtil.unicast(channel, errorMessage.withErrorMessage("发言消息中，发言类型为文本时，发言文本内容不能为空"));
            return returnMessage;
        }

        //发言附件地址校验
        if (!SpeakType.txt.name().equals(returnSpeakMessage.getType()) && StringUtil.isBlank(returnSpeakMessage.getAttachmentUrl())) {
            sessionUtil.unicast(channel, errorMessage.withErrorMessage("发言消息中，发言类型不为文本时，发言附件地址不能为空"));
            return returnMessage;
        }

        liveService.saveSpeakRecord(liveId, receivedSpeakMessage);
        return returnMessage;
    }

    private ReturnMessage banPostMessageHandle(String liveId, Channel channel, ErrorMessage errorMessage, String message) {

        ReturnBanPostMessage returnBanPostMessage = JSONUtil.toBean(message, ReturnBanPostMessage.class);

        if (returnBanPostMessage.getIsTeacher() == 0) {
            throw new RuntimeException("只有教师才能禁言!");
        }

        if (returnBanPostMessage.getIsBanPostAll() == 1) {

        } else {
            String banPostTargetUserId = returnBanPostMessage.getTargetUserId();
            if (StringUtil.isBlank(banPostTargetUserId)) {
                sessionUtil.unicast(channel, errorMessage.withErrorMessage("指定用户禁言消息，目标禁言用户ID不能为空"));
                return returnBanPostMessage;
            }
            OnlineUser targetUser = SessionUtil.getUserFromUserId(banPostTargetUserId);
            if (null == targetUser) {
                throw new RuntimeException("禁言目标用户不在直播间,操作失败!");
            } else {
                returnBanPostMessage.setTargetUserName(targetUser.getUserName());
            }
        }
        banPostService.updateBanPost(liveId, JSONUtil.toBean(message, ReceivedBanPostMessage.class));
        return returnBanPostMessage;
    }


    /**
     * 验证接收到的消息，保存调试信息
     *
     * @param message
     * @param channel
     */
    private void inputMessageVerifyAndSaveRecordReceive(String message, Channel channel) {

    }

}
