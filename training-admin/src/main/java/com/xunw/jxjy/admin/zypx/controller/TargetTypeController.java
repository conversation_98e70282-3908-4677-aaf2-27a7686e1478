package com.xunw.jxjy.admin.zypx.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zypx.entity.Target;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import com.xunw.jxjy.model.zypx.params.TargetTypeQueryParams;
import com.xunw.jxjy.model.zypx.service.TargetService;
import com.xunw.jxjy.model.zypx.service.TargetSettingService;
import com.xunw.jxjy.model.zypx.service.TargetTypeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2012.3.30
 * 职业培训-评分指标分类
 */
@RestController
@RequestMapping("/htgl/biz/targetType")
public class TargetTypeController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TargetTypeController.class);

    @Autowired
    private TargetTypeService service;
    @Autowired
    private TargetService targetService;
    @Autowired
    private TargetSettingService targetSettingService;


    /**
     * 评分指标分类列表
     */
    @RequestMapping("/list")
    @Operation(desc = "评分指标分类列表" )
    public Object cx(TargetTypeQueryParams params) throws Exception {
        return service.pageQuery(params);
    }

    /**
     * 添加评分指标分类
     */
    @RequestMapping("/add")
    @Operation(desc = "添加评分指标分类" )
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String parentId,
                      @RequestParam(required = false) String isCourseTarget,
                      @RequestParam(required = false) Integer sort) throws Exception {
        if(StringUtils.isEmpty(code)){
            throw BizException.withMessage("请输入指标分类编号");
        }
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入指标分类名称");
        }
        User user = super.getLoginUser(request).getUser();
        TargetType targetType = new TargetType();
        targetType.setId(BaseUtil.generateId());
        targetType.setCode(code);
        targetType.setName(name);
        targetType.setCreateTime(new Date());
        targetType.setCreatorId(user.getId());
        targetType.setParentId(parentId);
        targetType.setSort(sort);
        targetType.setIsCourseTarget(isCourseTarget);
        service.insert(targetType);
        return true;
    }

    /**
     * 评分指标树形
     */
    @RequestMapping("/tree")
    @Operation(desc = "评分指标树形",loginRequired = false)
    public Object tree(HttpServletRequest request, @RequestParam(required = false) Integer isShowTarget) throws Exception {
        return targetSettingService.getTargetTree(isShowTarget);
    }

    /**
     * 评分指标分类详情
     */
    @RequestMapping("/getById")
    @Operation(desc = "评分指标分类详情" )
    public Object getById(@RequestParam(required = false) String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
        return service.selectById(id);
    }

    /**
     * 修改评分指标分类
     */
    @RequestMapping("/edit")
    @Operation(desc = "修改评分指标分类" )
    public Object edit(HttpServletRequest request,
                       @RequestParam(required = false) String id,
                       @RequestParam(required = false) String code,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) String parentId,
                       @RequestParam(required = false) String isCourseTarget,
                       @RequestParam(required = false) Integer sort) throws Exception {
        if(StringUtils.isBlank(id)){
            throw BizException.withMessage("请选择一条数据");
        }
        TargetType check = service.selectById(id);
        if(check == null){
            throw BizException.withMessage("ID不存在:"+id);
        }
        if(StringUtils.isBlank(code)){
            throw BizException.withMessage("请输入指标分类编号");
        }
        if(StringUtils.isBlank(name)){
            throw BizException.withMessage("请输入指标分类名称");
        }
        check.setSort(sort);
        check.setCode(code);
        check.setName(name);
        check.setIsCourseTarget(isCourseTarget);
        service.updateById(check);
        return true;
    }

    /**
     * 删除评分指标分类
     */
    @RequestMapping("/deleteById")
    @Operation(desc = "删除评分指标分类" )
    public Object deleteById(
            @RequestParam(required = false) String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
        //Todo 指标分类下有指标不能删除
        EntityWrapper<Target> wrapper = new EntityWrapper<>();
        wrapper.eq("type_id", id);
        Integer count = targetService.selectCount(wrapper);
        if (count > 0) {
            throw BizException.withMessage("指标分类下有指标不能删除");
        }
        service.deleteById(id);
        return true;
    }
}

