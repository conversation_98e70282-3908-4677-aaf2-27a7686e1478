package com.xunw.jxjy.admin.inf.controller;

import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.QiniuZhiboUtils;
import com.xunw.jxjy.common.utils.QrCodeUtils;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.courselive.Chapter;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.courselive.Lesson;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.SysSettingEnum;
import com.xunw.jxjy.model.enums.Zbzt;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.CourseLive;
import com.xunw.jxjy.model.inf.params.CourseLiveQueryParams;
import com.xunw.jxjy.model.inf.service.CourseLiveService;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.inf.service.CoursewareService;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.zypx.service.ZypxLiveService;
import com.xunw.jxjy.paper.utils.ModelHelper;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 直播管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/inf/zypx/courseLive")
public class CourseLiveController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CourseLiveController.class);

	@Autowired
	private CourseLiveService service;
	@Autowired
	private CourseService courseService;
	@Autowired
	private ZypxLiveService liveService;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private ZypxBmService zypxBmService;
	private static final String QR_FORMAT = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={0}&redirect_uri=https://st-jxjy.whxunw.com/oauth2/login/{1}/{2}&response_type=code&scope=snsapi_base&state=#wechat_redirect";


	/**
	 * 直播分页列表
	 */
	@RequestMapping("/list")
    @Operation(desc = "直播分页查询")
    public Object list(
    				HttpServletRequest request,
    				CourseLiveQueryParams params) throws Exception {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
    	//直播讲师只能够查询自己的直播
    	if (getLoginRole(request) == Role.TEACHER) {
			params.setTeacherId(getLoginUserId(request));
		}
	 	Page<Map<String, Object>> page = service.pageQuery(params);
    	return page;
    }

	/**
	 * 查询直播的观看记录
	 */
	@RequestMapping("/listViewRecord")
    @Operation(desc = "查询直播的观看记录")
    public Object listViewRecord(
    				HttpServletRequest request,
    				CourseLiveQueryParams params) throws Exception {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
    	//直播讲师只能够查询自己的直播
    	if (getLoginRole(request) == Role.TEACHER) {
			params.setTeacherId(getLoginUserId(request));
		}
	 	Page<Map<String, Object>> page = service.getLiveViewRecord(params);
    	return page;
    }
	
	/**
	 * 生成二维码
	 */
	@RequestMapping("/qrCode/{courseId}")
	@Operation(desc = "生成二维码", loginRequired = false)
	public void qrCode(HttpServletRequest request,HttpServletResponse response, @PathVariable String courseId)
			throws Exception {
		String url = MessageFormat.format(QR_FORMAT,
				systemSettingService.getGlobalSetting(SysSettingEnum.WX_APP_ID),
				super.getCurrentHostOrg(request).getCode(),
				courseId);
		QrCodeUtils.createQrCode(url, 300, 300, super.getCurrentHostOrg(request).getAdminLogo(), response.getOutputStream());
	}

	/**
	 * 新增、编辑公用此接口
	 */
	@RequestMapping("/save")
    @Operation(desc = "编辑直播")
    public Object save(
    				HttpServletRequest request,
    				@RequestBody JSONObject model) throws Exception {
	 	service.saveCourseLive(model, getLoginUserId(request));
	 	return true;
    }

	/**
	 * 详情
	 */
	@RequestMapping("/getById")
    @Operation(desc = "直播详情")
    public Object getById(
    				HttpServletRequest request,
    				String id) throws Exception {
	 	CourseLive courseLive = service.selectById(id);
	 	Course course = courseService.selectById(courseLive.getCourseId());
	 	Map<String, Object> result = new HashMap<String, Object>();
	 	result.put("live", courseLive);
	 	result.put("course", course);
	 	Courselive courselive = ModelHelper.convertObject(courseLive.getContent());
	 	courselive.setId(courseLive.getId());
	 	result.put("courselive", courselive);
	 	courseLive.setContent(null);
	 	return result;
    }

	/**
	 * 删除
	 */
	@RequestMapping("/deleteById")
    @Operation(desc = "删除直播")
    public Object deleteById(
    				HttpServletRequest request,
    				String id) throws Exception {
	 	service.deleteById(id);
	 	return true;
    }

	/**
	 * 获取课程直播详情
	 */
	@RequestMapping("/getCourseLiveDetailsById")
	@Operation(desc = "获取课程直播详情")
	public Object getZbkcById(HttpServletRequest request,
			@RequestParam(required = false) String id) {
		if(StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请传入课程直播ID");
		}
		CourseLive courseLive = service.selectById(id);
		//本课程所有直播列表
		List<Map<String, Object>> zbList = new ArrayList<Map<String, Object>>();
		if(StringUtils.isNotEmpty(courseLive.getContent())) {
			Courselive courselive = ModelHelper.convertObject(courseLive.getContent());
			if (courselive != null && courselive.getChapters()!=null) {
				int chapterIndex=0;
				for (Chapter chapter : courselive.getChapters()) {
					if(chapter.getLessons() != null) {
						int lessonIndex = 0;
						for (Lesson lesson : chapter.getLessons()) {
							Map<String, Object> zbMap = new HashMap<String, Object>();
							String zbId = lesson.getId();
							Map<String, Object> channel = liveService.getLiveDetailById(zbId);
	  						if(channel != null) {
								//构造直播数据
								zbMap.put("lessonName", "第"+(chapterIndex+1)+"章-第"+(lessonIndex+1)+"课时");
		  						// 判断主流的推流状态
		  						try {
		  							channel.put("zbtlzta",
		  									QiniuZhiboUtils.isLiving((String) channel.get("zblmca")) ? "WORKING" : "OFFLINE");
		  						} catch (Exception e) {
		  							channel.put("zbtlzta", "OFFLINE");
		  						}
		  						//获取直播流的实时在线观看人数
		  						zbMap.put("liveCount", QiniuZhiboUtils.getQiNiuStreamLiveCountNum((String) channel.get("zblmca")));
		  						// 判断直播教师流的推流状态
		  						try {
		  							channel.put("zbtlztb",
		  									QiniuZhiboUtils.isLiving((String) channel.get("zblmcb")) ? "WORKING" : "OFFLINE");
		  						} catch (Exception e) {
		  							channel.put("zbtlztb", "OFFLINE");
		  						}
								zbMap.putAll(channel);
								zbList.add(zbMap);
	  						}
							lessonIndex++;
						}
					}
					chapterIndex++;
				}
			}
		}
		Map<String, Object> result = new HashMap<String, Object>();
		Course course = courseService.selectById(courseLive.getCourseId());
		result.put("zbList", zbList);
		result.put("course", course);
		result.put("loginUserId", super.getLoginUserId(request));

		return result;
	}

	/**
	 * 禁言设置
	 */
	@RequestMapping("/speakSetting")
	@Operation(desc = "禁言设置")
	public Object speakSetting(HttpServletRequest request,
							   @RequestParam(required = false) String id,
							   @RequestParam(required = false) String isCloseSpeak) throws Exception {
		if(StringUtils.isEmpty(id)){
			throw BizException.withMessage("直播id不能为空");
		}
		if(StringUtils.isEmpty(isCloseSpeak)){
			throw BizException.withMessage("请选择是否禁言");
		}
		Live live = liveService.selectById(id);
		if (live == null) {
			throw BizException.withMessage("直播不存在");
		}
		if (live.getZbzt() == Zbzt.FINISHED) {
			throw BizException.withMessage("直播已结束,不能设置全局禁言");
		}
		live.setSfqjjy(isCloseSpeak);
		liveService.updateById(live);
		return true;
	}

	//批量设置公开课
	@RequestMapping("/setPublicCourse")
	@Operation(desc = "批量设置公开课")
	public Object setPublicCourse(
			HttpServletRequest request,
			@RequestParam(required = false) String ids,
			@RequestParam(required = false) String isPublic
	) throws Exception {
		if(StringUtils.isEmpty(ids)){
			throw BizException.withMessage("请选择直播课");
		}
		if(StringUtils.isEmpty(isPublic)){
			throw BizException.withMessage("请选择是否是公开课");
		}
		List<String> courseLiveIdList = Arrays.asList(StringUtils.split(ids, ","));
		List<CourseLive> courseLives = service.selectBatchIds(courseLiveIdList);
		for (CourseLive courseLive : courseLives) {
			courseLive.setIsPublic(isPublic);
		}
		DBUtils.updateBatchById(courseLives, CourseLive.class);
		return true;
	}

	/**
	 * 已观看直播地址导出,可用于课件导入
	 */
	@RequestMapping("/addressExport")
	@Operation(desc = "导出信息表")
	public void export(
			HttpServletRequest request,
			HttpServletResponse response,
			CourseLiveQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
    	//直播讲师只能够查询自己的直播
    	if (getLoginRole(request) == Role.TEACHER) {
			params.setTeacherId(getLoginUserId(request));
		}
		response.setContentType("text/html;charset=utf-8");
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=live_address.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			service.addressExport(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:",e); //记录打印日志
		}
		finally {
			if(os != null){
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 直播回放转课件
	 */
	@RequestMapping("/convert")
	@Operation(desc = "直播回放转课件")
	public Object convert(
			HttpServletRequest request,
			HttpServletResponse response,
			String ids) throws Exception {
		service.convert(ids, super.getLoginUserId(request));
		return true;
	}

	/**
	 * 直播回放转课件
	 */
	@RequestMapping("/syncBmCount")
	@Operation(desc = "同步直播观看人数")
	public Object syncBmCount(HttpServletRequest request,
							  HttpServletResponse response,
							  String id) throws Exception {
		if (StringUtils.isEmpty(id)){
			throw BizException.withMessage("id不能为空");
		}
		CourseLive courseLive = service.selectById(id);
		if (Constants.YES.equals(courseLive.getIsPublic())) {
			zypxBmService.autoBmWhenStudyPublicCourse(courseLive.getCourseId(), id);
		}
		return true;
	}

	/**
	 * 导出直播观看记录
	 */
	@RequestMapping("/exportViewRecord")
	@Operation(desc = "导出直播观看记录")
	public void exportViewRecord(
			HttpServletRequest request,
			HttpServletResponse response,
			CourseLiveQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		response.setContentType("text/html;charset=utf-8");
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=live_view_records.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			service.exportViewRecord(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:",e); //记录打印日志
		}
		finally {
			if(os != null){
				IOUtils.closeQuietly(os);
			}
		}
	}

}
