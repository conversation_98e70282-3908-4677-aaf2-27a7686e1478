package com.xunw.jxjy.admin.inf.controller;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.AlipayHashMap;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.inf.entity.Hotel;
import com.xunw.jxjy.model.inf.params.HotelQueryParams;
import com.xunw.jxjy.model.inf.service.HotelService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.zypx.dto.HotelRoomTypeDTO;
import com.xunw.jxjy.paper.utils.ModelHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 合作宾馆
 *
 * <AUTHOR>
 * @time 2021.3.29
 */
@RestController
@RequestMapping("/htgl/inf/hotel")
public class HotelController extends BaseController {

    @Autowired
    private HotelService service;

    @RequestMapping("/list")
    @Operation(desc = "合作宾馆列表")
    public Object cx(
            HttpServletRequest request,
            HotelQueryParams params) throws Exception {
        if (StringUtils.isEmpty(params.getHostOrgId())) {
        	params.setHostOrgId(super.getCurrentHostOrgId(request));
        }
        return service.pageQuery(params);
    }

    @RequestMapping("/getAllList")
    @Operation(desc = "合作宾馆列表所有")
    public Object getAllList(
            HttpServletRequest request,
            HotelQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.getAllList(params);
    }

    //新增
    @RequestMapping("/add")
    @Operation(desc = "添加合作宾馆")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String serialNumber,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) Integer lcCount,
                      @RequestParam(required = false) Integer totalCount,
                      @RequestParam(required = false) String people,
                      @RequestParam(required = false) String phone,
                      @RequestParam(required = false) String address,
                      @RequestParam(required = false) String mapPosition,
                      @RequestParam(required = false) String addressRemak,
                      @RequestParam(required = false) String imgUrl,
                      @RequestParam(required = false) String roomTypes,
                      @RequestParam(required = false) String remark) throws Exception {
        if (StringUtils.isEmpty(serialNumber)) {
            throw BizException.withMessage("请输入宾馆编号");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("请输入宾馆名称");
        }
        if (StringUtils.isEmpty(people)) {
            throw BizException.withMessage("请输入联系人");
        }
        if (StringUtils.isEmpty(phone)) {
            throw BizException.withMessage("请输入联系电话");
        }
        if (StringUtils.isEmpty(address)) {
            throw BizException.withMessage("请输入宾馆地点");
        }
        if (StringUtils.isEmpty(mapPosition)) {
            throw BizException.withMessage("请输入宾馆经纬度坐标");
        }
        if (StringUtils.isEmpty(roomTypes)) {
            throw BizException.withMessage("请输入房间类型");
        }
        List<HotelRoomTypeDTO> roomTypeList = JSONObject.parseArray(roomTypes, HotelRoomTypeDTO.class);
        Map<String, HotelRoomTypeDTO> map = new HashMap<String, HotelRoomTypeDTO>();
        Set<String> repeatRooms = new LinkedHashSet<String>();
        for (HotelRoomTypeDTO hotelRoomTypeDTO : roomTypeList) {
        	if (map.containsKey(hotelRoomTypeDTO.getType())) {
        		repeatRooms.add(hotelRoomTypeDTO.getName());
			}
        	else {
        		map.put(hotelRoomTypeDTO.getType(), hotelRoomTypeDTO);
        	}
        }
        if (repeatRooms.size() > 0) {
            throw BizException.withMessage("房间类型：" + StringUtils.join(repeatRooms, ",") + "有重复");
        }
        
        Hotel hotel = new Hotel();
        hotel.setId(BaseUtil.generateId());
        hotel.setSerialNumber(serialNumber);
        hotel.setName(name);
        hotel.setLcCount(lcCount);
        hotel.setTotalCount(totalCount);
        hotel.setPeople(people);
        hotel.setPhone(phone);
        hotel.setAddress(address);
        hotel.setMapPosition(mapPosition);
        hotel.setAddressRemak(addressRemak);
        hotel.setCreateTime(new Date());
        //主办单位
        hotel.setHostOrgId(getCurrentHostOrgId(request));

        String roomTypesXml = ModelHelper.formatObject(roomTypeList);
        hotel.setRoomTypes(roomTypesXml);
        hotel.setImgUrl(imgUrl);
        hotel.setRemark(remark);
        service.insert(hotel);
        return true;
    }

    //获取
    @RequestMapping("/getById")
    @Operation(desc = "合作宾馆详情")
    public Object getById(@RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        Hotel hotel = service.selectById(id);
        List<HotelRoomTypeDTO> mapList = ModelHelper.convertObject(hotel.getRoomTypes());
        hotel.setRoomTypeList(mapList);
        return hotel;
    }

    //修改
    @RequestMapping("/edit")
    @Operation(desc = "修改合作宾馆")
    public Object edit(HttpServletRequest request,
                       @RequestParam(required = false) String id,
                       @RequestParam(required = false) String serialNumber,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) Integer lcCount,
                       @RequestParam(required = false) Integer totalCount,
                       @RequestParam(required = false) String people,
                       @RequestParam(required = false) String phone,
                       @RequestParam(required = false) String address,
                       @RequestParam(required = false) String mapPosition,
                       @RequestParam(required = false) String addressRemak,
                       @RequestParam(required = false) String imgUrl,
                       @RequestParam(required = false) String roomTypes,
                       @RequestParam(required = false) String remark) throws Exception {

        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        Hotel check = service.selectById(id);
        if (check == null) {
            throw BizException.withMessage("ID不存在:" + id);
        }
        if (StringUtils.isEmpty(serialNumber)) {
            throw BizException.withMessage("请输入宾馆编号");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("请输入宾馆名称");
        }
        if (lcCount == null) {
            throw BizException.withMessage("请输入楼层数");
        }
        if (totalCount == null) {
            throw BizException.withMessage("请输入容纳人数");
        }
        if (StringUtils.isEmpty(people)) {
            throw BizException.withMessage("请输入联系人");
        }
        if (StringUtils.isEmpty(phone)) {
            throw BizException.withMessage("请输入联系电话");
        }
        if (StringUtils.isEmpty(address)) {
            throw BizException.withMessage("请输入宾馆地点");
        }
        if (StringUtils.isEmpty(mapPosition)) {
            throw BizException.withMessage("请输入宾馆经纬度坐标");
        }
        if (StringUtils.isEmpty(roomTypes)) {
            throw BizException.withMessage("请输入房间类型");
        }
        List<HotelRoomTypeDTO> roomTypeList = JSONObject.parseArray(roomTypes, HotelRoomTypeDTO.class);
        Map<String, HotelRoomTypeDTO> map = new HashMap<String, HotelRoomTypeDTO>();
        Set<String> repeatRooms = new LinkedHashSet<String>();
        for (HotelRoomTypeDTO hotelRoomTypeDTO : roomTypeList) {
        	if (map.containsKey(hotelRoomTypeDTO.getType())) {
        		repeatRooms.add(hotelRoomTypeDTO.getName());
			}
        	else {
        		map.put(hotelRoomTypeDTO.getType(), hotelRoomTypeDTO);
        	}
        }
        if (repeatRooms.size() > 0) {
            throw BizException.withMessage("房间类型：" + StringUtils.join(repeatRooms, ",") + "有重复");
        }

        check.setSerialNumber(serialNumber);
        check.setName(name);
        check.setLcCount(lcCount);
        check.setTotalCount(totalCount);
        check.setPeople(people);
        check.setPhone(phone);
        check.setAddress(address);
        check.setMapPosition(mapPosition);
        check.setAddressRemak(addressRemak);

        String roomTypesXml = ModelHelper.formatObject(roomTypeList);
        check.setRoomTypes(roomTypesXml);
        check.setImgUrl(imgUrl);
        check.setRemark(remark);
        service.updateById(check);
        return true;
    }

    //删除
    @RequestMapping("/deleteById")
    @Operation(desc = "删除合作宾馆")
    public Object deleteById(
            @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        service.deleteById(id);
        return true;
    }

}
