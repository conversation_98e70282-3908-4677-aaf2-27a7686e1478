package com.xunw.jxjy.admin.zypx.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.inf.service.*;
import com.xunw.jxjy.model.zypx.dto.*;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.params.PlanQueryParams;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zypx.entity.Plan;
import com.xunw.jxjy.model.zypx.entity.PlanDetail;
import com.xunw.jxjy.model.zypx.params.PlanDetailQueryParams;
import com.xunw.jxjy.model.zypx.service.PlanDetailService;
import com.xunw.jxjy.model.zypx.service.PlanService;

import net.sf.json.JSONObject;

/**
 * 申报项目管理
 */
@RestController
@RequestMapping("/htgl/biz/planDetail")
public class ZypxPlanDetailController extends BaseController {

	@Autowired
	private PlanDetailService service;
	@Autowired
	private PlanService planService;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private UserService userService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private InfXmService infXmService;
	@Autowired
	private ZypxXmCourseSettingService xmCourseSettingService;
	@Autowired
	private Xm2CourseService xm2CourseService;
	@Autowired
	private ZypxTypeService zypxTypeService;

	/**
	 * 项目申报列表查询
	 */
	@RequestMapping("/list")
	@Operation(desc = "项目申报列表")
	public Object list(HttpServletRequest request, PlanDetailQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		return service.pageQuery(params);
	}

	@RequestMapping("/tree")
	@Operation(desc = "培训计划树")
	public Object tree(HttpServletRequest request, PlanQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		// 项目负责人只看到自己创建的培训计划 及 所在部门承办的工种的的技能类培训计划
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		return service.getPlanTree(params);
	}

	/**
	 *保存项目的申报信息
	 */
	@RequestMapping("/add")
	@Operation(desc = "保存项目的申报信息")
	public Object add(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {
		String planId = BaseUtil.getStringValueFromJson(params, "planId");
		Plan plan = planService.selectById(planId);
		if (plan == null) {
			throw BizException.withMessage("培训计划不存在");
		}
		PlanDetail planDetail = savePlanDetail(request, params, plan);
		return planDetail.getId();
	}

	private PlanDetail savePlanDetail(HttpServletRequest request, JSONObject params, Plan plan) {
		String name = BaseUtil.getStringValueFromJson(params, "name");
		String serialNumber = BaseUtil.getStringValueFromJson(params, "serialNumber");
		String entrustOrgId = BaseUtil.getStringValueFromJson(params, "entrustOrgId");// 委托单位
		String receiveOrgId = BaseUtil.getStringValueFromJson(params, "receiveOrgId");// 承办单位
		String leaderId = BaseUtil.getStringValueFromJson(params, "leaderId");
		String xmSources = BaseUtil.getStringValueFromJson(params, "xmSources");
		String trainingForm = BaseUtil.getStringValueFromJson(params, "trainingForm");
		String enroll = BaseUtil.getStringValueFromJson(params, "enrollWay");
		String address = BaseUtil.getStringValueFromJson(params, "address");
		Integer count = BaseUtil.getIntegerValueFromJson(params, "count");
		Double hours = BaseUtil.getDoubleValueFromJson(params, "hours");
		String startTime = BaseUtil.getStringValueFromJson(params, "startTime");
		String dictIndustryId = BaseUtil.getStringValueFromJson(params,"dictIndustryId");
		if (StringUtils.isEmpty(startTime)) {
			throw BizException.withMessage("开始时间不能为空");
		}
		String endTime = BaseUtil.getStringValueFromJson(params, "endTime");
		if (StringUtils.isEmpty(endTime)) {
			throw BizException.withMessage("结束时间不能为空");
		}
		if (DateUtils.parse(startTime,"yyyy-MM-dd HH:mm").getTime() >= DateUtils.parse(endTime,"yyyy-MM-dd HH:mm").getTime()){
			throw BizException.withMessage("结束时间必须在开始时间之后");
		}
		String contract = BaseUtil.getStringValueFromJson(params, "contract"); // 合同
		Double amount = BaseUtil.getDoubleValueFromJson(params, "amount");
		String units = BaseUtil.getStringValueFromJson(params, "units");
		String teachers = BaseUtil.getStringValueFromJson(params, "teachers");
		String isIssueCertificate = BaseUtil.getStringValueFromJson(params, "isIssueCertificate");
		String isOrgPay = BaseUtil.getStringValueFromJson(params, "isOrgPay");
		String trainees = BaseUtil.getStringValueFromJson(params, "trainees");

		String buyType = BaseUtil.getStringValueFromJson(params, "buyType");
		if (Objects.equals(isOrgPay, Constants.NO) && BuyType.findByEnumName(buyType) == null) {
			throw BizException.withMessage("购买类型参数错误");
		}
		String pyfa = BaseUtil.getStringValueFromJson(params, "pyfa");// 培训实施方案
		Double feeStandard = BaseUtil.getDoubleValueFromJson(params, "feeStandard");// 学员报名费
		String evalPaper = BaseUtil.getStringValueFromJson(params, "evalPaper");//评测实施方案
		String remark = BaseUtil.getStringValueFromJson(params, "remark");//备注
		BigDecimal contractAmount = new BigDecimal(params.getString("contractAmount"));//合同金额
		String industryCategory = BaseUtil.getStringValueFromJson(params, "industryCategory");//行业特色
		BigDecimal supportAmount = new BigDecimal(params.getString("supportAmount"));//资助金额
		String baseId = BaseUtil.getStringValueFromJson(params, "baseId");//所属基地
		String classzType = BaseUtil.getStringValueFromJson(params, "classzType");//培训班类型

		PlanDetail planDetail = new PlanDetail();
		planDetail.setId(BaseUtil.generateId2());
		planDetail.setPlanId(plan.getId());
		planDetail.setName(name);
		planDetail.setSerialNumber(serialNumber);
		planDetail.setStatus(AuditStatus.DRAFT);
		planDetail.setStartTime(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
		planDetail.setEndTime(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
		planDetail.setXmSources(XmSources.findByEnumName(xmSources));
		planDetail.setEntrustOrgId(entrustOrgId);
		planDetail.setReceiveOrgId(receiveOrgId);
		planDetail.setTrainees(trainees);
		planDetail.setContract(contract);
		planDetail.setPyfa(pyfa);
		planDetail.setTrainingForm(trainingForm);
		planDetail.setIsIssueCertificate(isIssueCertificate);
		planDetail.setTeachers(teachers);
		planDetail.setEnrollWay(EnrollWay.findByEnumName(enroll));
		planDetail.setLeaderId(leaderId);
		planDetail.setAddress(address);
		planDetail.setHours(hours);
		planDetail.setCount(count);
		planDetail.setAmount(amount);
		planDetail.setUnits(units);
		planDetail.setFeeStandard(feeStandard);
		planDetail.setIsOrgPay(isOrgPay);
		planDetail.setSaveTime(DateUtils.now());
		planDetail.setDictIndustryId(dictIndustryId);
		planDetail.setEvalPaper(evalPaper);
		planDetail.setUserId(super.getLoginUserId(request));
		planDetail.setBuyType(BuyType.findByEnumName(buyType));
		planDetail.setRemark(remark);
		planDetail.setContractAmount(contractAmount);
		planDetail.setIndustryCategory(industryCategory);
		planDetail.setSupportAmount(supportAmount);
		planDetail.setBaseId(baseId);
		planDetail.setClasszType(classzType);
		service.savePlanDetail(planDetail);

		String id = BaseUtil.getStringValueFromJson(params, "xmId");
		InfXm infXm = infXmService.selectById(id);
		if (BaseUtil.isNotEmpty(infXm)){
			EntityWrapper<Xm2Course> wrapper = new EntityWrapper<>();
			wrapper.eq("xm_id",infXm.getId());
			List<Xm2Course> xmCourses = xm2CourseService.selectList(wrapper);

			EntityWrapper<ZypxXmCourseSetting> entityWrapper = new EntityWrapper<>();
			entityWrapper.eq("xm_id",planDetail.getId());
			xmCourseSettingService.deleteList(entityWrapper);

			ArrayList<ZypxXmCourseSetting> list = new ArrayList<>();
			for (Xm2Course xmCourse: xmCourses) {
				ZypxXmCourseSetting zypxXmCourseSetting = new ZypxXmCourseSetting();
				zypxXmCourseSetting.setId(BaseUtil.generateId());
				zypxXmCourseSetting.setXmId(planDetail.getId());
				zypxXmCourseSetting.setCourseId(xmCourse.getCourseId());
				zypxXmCourseSetting.setCreateTime(DateUtils.now());
				zypxXmCourseSetting.setCreatorId(super.getLoginUserId(request));
				zypxXmCourseSetting.setIsCourseware(Constants.YES);
				list.add(zypxXmCourseSetting);
			}
			DBUtils.insertBatch(list,ZypxXmCourseSetting.class);
		}
		return planDetail;
	}

	/**
	 * 详情
	 */
	@RequestMapping("/getById")
	@Operation(desc = "项目申报详情")
	public Object getById(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		return service.selectById(id);
	}

	/**
	 * 删除
	 */
	@RequestMapping("/deleteById")
	@Operation(desc = "删除项目申报信息")
	public Object deleteById(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		PlanDetail planDetail = service.selectById(id);
		if (Constants.YES.equals(planDetail.getIsExecute())) {
			throw BizException.withMessage("您选择的项目已经执行，无法删除");
		}
		service.delete(id);
		return true;
	}

	/**
	 * 克隆
	 */
	@RequestMapping("/clone")
	@Operation(desc = "克隆项目申报信息")
	public Object clone(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		PlanDetail planDetail = service.selectById(id);
		PlanDetail cloneXm = (PlanDetail) planDetail.clone();
		cloneXm.setId(BaseUtil.generateId2());
		cloneXm.setName(cloneXm.getName() + "-克隆");
		cloneXm.setStatus(AuditStatus.DRAFT);
		cloneXm.setSerialNumber(service.buildXmSerialNumber(super.getCurrentHostOrgId(request)));
		cloneXm.setApproveItemId(null);
		cloneXm.setSaveTime(DateUtils.now());
		cloneXm.setIsExecute(null);
		cloneXm.setExecuteTime(null);
		cloneXm.setPaper(null);
		cloneXm.setContract(null);
		cloneXm.setPyfa(null);
		service.savePlanDetail(cloneXm);
		return true;
	}

	/**
	 * 修改项目申报信息
	 */
	@RequestMapping("/edit")
	@Operation(desc = "修改项目申报信息")
	public Object edit(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {
		String id = BaseUtil.getStringValueFromJson(params, "id");
		PlanDetail planDetail = service.selectById(id);
		if (planDetail == null) {
			throw BizException.withMessage("培训计划详情不存在");
		}
		String name = BaseUtil.getStringValueFromJson(params, "name");
		String serialNumber = BaseUtil.getStringValueFromJson(params, "serialNumber");
		String entrustOrgId = BaseUtil.getStringValueFromJson(params, "entrustOrgId");// 委托单位
		String receiveOrgId = BaseUtil.getStringValueFromJson(params, "receiveOrgId");// 承办单位
		String leaderId = BaseUtil.getStringValueFromJson(params, "leaderId");
		String xmSources = BaseUtil.getStringValueFromJson(params, "xmSources");
		String trainingForm = BaseUtil.getStringValueFromJson(params, "trainingForm");
		String enroll = BaseUtil.getStringValueFromJson(params, "enrollWay");
		String address = BaseUtil.getStringValueFromJson(params, "address");
		Integer count = BaseUtil.getIntegerValueFromJson(params, "count");
		Double hours = BaseUtil.getDoubleValueFromJson(params, "hours");
		String startTime = BaseUtil.getStringValueFromJson(params, "startTime");
		String dictIndustryId = BaseUtil.getStringValueFromJson(params,"dictIndustryId");
		if (StringUtils.isEmpty(startTime)) {
			throw BizException.withMessage("开始时间不能为空");
		}
		String endTime = BaseUtil.getStringValueFromJson(params, "endTime");
		if (StringUtils.isEmpty(endTime)) {
			throw BizException.withMessage("结束时间不能为空");
		}
		if (DateUtils.parse(startTime,"yyyy-MM-dd HH:mm").getTime() >= DateUtils.parse(endTime,"yyyy-MM-dd HH:mm").getTime()){
			throw BizException.withMessage("结束时间必须在开始时间之后");
		}
		String contract = BaseUtil.getStringValueFromJson(params, "contract"); // 合同
		Double amount = BaseUtil.getDoubleValueFromJson(params, "amount");
		String units = BaseUtil.getStringValueFromJson(params, "units");
		String teachers = BaseUtil.getStringValueFromJson(params, "teachers");
		String isIssueCertificate = BaseUtil.getStringValueFromJson(params, "isIssueCertificate");
		String isOrgPay = BaseUtil.getStringValueFromJson(params, "isOrgPay");
		String trainees = BaseUtil.getStringValueFromJson(params, "trainees");
		String pyfa = BaseUtil.getStringValueFromJson(params, "pyfa");// 培训实施方案
		Double feeStandard = BaseUtil.getDoubleValueFromJson(params, "feeStandard");// 学员报名费

		String buyType = BaseUtil.getStringValueFromJson(params, "buyType");
		if (Objects.equals(isOrgPay, Constants.NO) && BuyType.findByEnumName(buyType) == null) {
			throw BizException.withMessage("购买类型参数错误");
		}
		String evalPaper = BaseUtil.getStringValueFromJson(params, "evalPaper");//评测实施方案
		String remark = BaseUtil.getStringValueFromJson(params, "remark");//备注
		BigDecimal contractAmount = new BigDecimal(params.getString("contractAmount"));//合同金额
		String industryCategory = BaseUtil.getStringValueFromJson(params, "industryCategory");//行业特色
		BigDecimal supportAmount = new BigDecimal(params.getString("supportAmount"));//资助金额
		String baseId = BaseUtil.getStringValueFromJson(params, "baseId");//所属基地
		String classzType = BaseUtil.getStringValueFromJson(params, "classzType");//培训班类型

		planDetail.setName(name);
		planDetail.setSerialNumber(serialNumber);
		planDetail.setStatus(AuditStatus.DRAFT);
		planDetail.setStartTime(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
		planDetail.setEndTime(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
		planDetail.setXmSources(XmSources.findByEnumName(xmSources));
		planDetail.setEntrustOrgId(entrustOrgId);
		planDetail.setReceiveOrgId(receiveOrgId);
		planDetail.setTrainees(trainees);
		planDetail.setContract(contract);
		planDetail.setPyfa(pyfa);
		planDetail.setTrainingForm(trainingForm);
		planDetail.setIsIssueCertificate(isIssueCertificate);
		planDetail.setTeachers(teachers);
		planDetail.setEnrollWay(EnrollWay.findByEnumName(enroll));
		planDetail.setLeaderId(leaderId);
		planDetail.setAddress(address);
		planDetail.setHours(hours);
		planDetail.setCount(count);
		planDetail.setAmount(amount);
		planDetail.setUnits(units);
		planDetail.setFeeStandard(feeStandard);
		planDetail.setIsOrgPay(isOrgPay);
		planDetail.setSaveTime(DateUtils.now());
		planDetail.setDictIndustryId(dictIndustryId);
		planDetail.setEvalPaper(evalPaper);
		planDetail.setBuyType(BuyType.findByEnumName(buyType));
		planDetail.setRemark(remark);
		planDetail.setContractAmount(contractAmount);
		planDetail.setIndustryCategory(industryCategory);
		planDetail.setSupportAmount(supportAmount);
		planDetail.setBaseId(baseId);
		planDetail.setClasszType(classzType);
		service.editPlanDetail(planDetail);
		return planDetail.getId();
	}

	/**
	 * 提交项目申报信息
	 */
	@RequestMapping("/submit")
	@Operation(desc = "提交项目申报信息")
	public Object submit(HttpServletRequest request, @RequestParam(required = false) String id ) throws Exception {
		if(StringUtils.isBlank(id)){
			throw BizException.withMessage("请选择一条数据");
		}
		PlanDetail check = service.selectById(id);
		if (check==null) {
			throw BizException.withMessage("你要操作的数据不存在，id："+id);
		}
		// 项目信息不完整不能提交审核
		if (check.getXmSources() == null || StringUtils.isEmpty(check.getSerialNumber())) {
			throw BizException.withMessage("项目信息不完整，禁止提交审核!");
		}

		String isUploadProject = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.IS_UPLOAD_PROJECT, super.getCurrentHostOrgId(request));
		String isOpenPreApproval = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.IS_OPEN_PRE_APPROVAL, super.getCurrentHostOrgId(request));

		if (Constants.YES.equals(isUploadProject) && StringUtils.isEmpty(check.getPaper())) {
			throw BizException.withMessage("必须先上传立项审批表");
		}
		User user = super.getLoginUser(request).getUser();
		service.submitPlan(check, isOpenPreApproval, user);
		return true;
	}

	/**
	 * 下载立项审批表
	 */
	@RequestMapping("/downApproval")
	@Operation(desc = "下载立项审批表")
	public void downApproval(HttpServletResponse response, HttpServletRequest request,
							 @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请传入培训计划详情ID");
		}
		String dir = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.XMLXSP_TEMPLATE,
				getCurrentHostOrgId(request));
		if (dir == null) {
			throw BizException.withMessage("当前主办单位尚未配置立项审批表模板");
		}
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=approval.docx");
		File docx = service.downApproval(id, dir);
		try (OutputStream os = response.getOutputStream(); InputStream is = new FileInputStream(docx)) {
			IOUtils.copy(is, os);
			os.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			FileHelper.delFile(docx);
		}
	}

	/**
	 * 上传立项审批表
	 */
	@RequestMapping("/uploadApproval")
	@Operation(desc = "上传立项审批表")
	public Object xz(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file,
					 @RequestParam(required = false) String id) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		// 获取文件名称
		String name = file.getOriginalFilename();
		String basepath = attConfig.getRootDir() + "/jxjy/" + ProcessType.PLAN + "/xmsp-sheet/";
		String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()
				+ FileHelper.getExtension(name);
		String url = FileHelper.storeFile(basepath, file.getInputStream(), newFileName);
		PlanDetail check = service.selectById(id);
		check.setPaper(url);// 立项审批表
		check.setApproveUserId(null);
		check.setApproveStatus(null);
		check.setApprovalTime(null);
		check.setApproveAdvice(null);
		service.updateAllColumnById(check);
		return true;
	}

	/**
	 * 上传合同
	 */
	@RequestMapping("/uploadHt")
	@Operation(desc = "上传合同")
	public Object uploadHt(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file,
						   @RequestParam(required = false) String id) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		// 获取文件名称
		String name = file.getOriginalFilename();
		String basepath = attConfig.getRootDir() + "/jxjy/" + ProcessType.PLAN + "/xmsp-contract/";
		String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()
				+ FileHelper.getExtension(name);
		String url = FileHelper.storeFile(basepath, file.getInputStream(), newFileName);
		PlanDetail check = service.selectById(id);
		check.setContract(url);
		check.setContractUserId(null);
		check.setContractStatus(null);
		check.setContractTime(null);
		check.setContractAdvice(null);
		service.updateAllColumnById(check);
		return true;
	}

	/**
	 * 获取项目编号
	 */
	@RequestMapping("/getSerialNumber")
	@Operation(desc = "获取项目编号")
	public Object getSerialNumber(HttpServletRequest request) throws Exception {
		return service.buildXmSerialNumber(super.getCurrentHostOrgId(request));
	}

	/**
	 * 获取默认的项目负责人
	 */
	@RequestMapping("/getDefaultLeaderId")
	@Operation(desc = "获取默认项目负责人")
	public Object getDefaultLeaderId(HttpServletRequest request) throws Exception {
		List<Role> roles = userService.getRoleByUserId(getLoginUserId(request));
		for (Role role : roles) {
			if (role == Role.XM_LEADER) {
				return getLoginUserId(request);
			}
		}
		return null;
	}

	/**
	 * 获取默认承办单位
	 * 取当前用户所在的部门ID,仅限二级部门
	 */
	@RequestMapping("/getDefaultReceiveOrgId")
	@Operation(desc = "获取默认承办单位")
	public Object getDefaultOrganizer(HttpServletRequest request) throws Exception {
		Org org = super.getLoginOrg(request);
		if (Constants.YES.equals(org.getIsParent())) {
			return null;
		}
		else {
			Org parent = orgService.selectById(org.getParentId());
			return Constants.YES.equals(parent.getIsParent()) ? org.getId() : null;
		}
	}

	/**
	 * 获取默认的项目名称
	 */
	@RequestMapping("/getDefaultXmName")
	@Operation(desc = "获取默认的项目名称")
	public Object getDefaultXmName(HttpServletRequest request,@RequestParam(required = false) String planId) throws Exception {
		Plan plan = planService.selectById(planId);
		return plan != null ? plan.getName() : null;
	}

	/**
	 * 获取职业技能报名信息
	 */
	@RequestMapping("/getProfessionBmCountList")
	@Operation(desc = "获取职业技能报名信息")
	public Object getProfessionBmCountList(HttpServletRequest request,@RequestParam(required = false) String id,
										   @RequestParam(required = false) String planId,
										   @RequestParam(required = false,value = "bmbatchId") String batchId) throws Exception {
		if (StringUtils.isEmpty(planId) && StringUtils.isEmpty(batchId)) {
			throw BizException.withMessage("请传入培训计划id或批次id");
		}
		if (StringUtils.isEmpty(batchId)) {
			ZyjdBmBatch zyjdBmBatch = planService.getBmBatchByPlanId(planId);
			if (zyjdBmBatch == null) {
				throw BizException.withMessage("您选择的培训计划没有设置报名批次");
			}
			batchId = zyjdBmBatch.getId();
		}
		return service.getProfessionBmCountList(batchId, id);
	}

	/**
	 * 保存职业技能类项目的工种选择信息
	 */
	@RequestMapping("/saveProfessions")
	@Operation(desc = "保存职业技能类项目的工种选择信息")
	public Object saveProfessions(HttpServletRequest request, @RequestBody ApplyedProfessionDto applyedProfessionDto) throws Exception {
		if (StringUtils.isEmpty(applyedProfessionDto.getPlanId())) {
			throw BizException.withMessage("请传入培训计划id");
		}
		return service.saveProfessions(applyedProfessionDto, super.getLoginUserId(request));
	}

}
