package com.xunw.jxjy.admin.zcps.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.QrCodeUtils;
import com.xunw.jxjy.model.enums.BmOpenStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.ZyjdApproveStatus;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.service.ZyjdProfessionService;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmTrial;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmBatchQueryParams;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmScopeService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmTrialService;
import com.xunw.jxjy.model.zyjd.vo.ZyjdBmBatchVo;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职业鉴定批次管理
 */
@RestController
@RequestMapping("/htgl/biz/zcps/bmbatch")
public class ZcpsBmBatchController extends BaseController {

	@Autowired
	private ZyjdBmBatchService service;
	@Autowired
	private ZyjdProfessionService professionService;
	@Autowired
	private ZyjdBmScopeService zyjdBmScopeService;
	@Autowired
	private ZyjdBmTrialService zyjdBmTrialService;

	private static final String formId = "common-zcps";

	private static final String QR_FORMAT = "{0}kaosheng/mobile/zyjd/bm/home/<USER>";
	
	/**
	 * 列表接口
	 */
	@RequestMapping("/list")
	@Operation(desc = "列表")
	public Object list(HttpServletRequest request, ZyjdBmBatchQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setType(ZyjdBmBatchType.ZCPS);
		Page<Map<String, Object>> page = service.list(params);
		page.getRecords().forEach(x->{
			x.put("qr_url", MessageFormat.format(QR_FORMAT,
					super.getCurrentHostOrgPortalWebUrl(request),
					BaseUtil.getStringValueFromMap(x, "id")));
		});
		return page;
	}
	

	/**
	 * 生成报名二维码
	 */
	@RequestMapping("/qrCode/{id}")
	@Operation(desc = "生成报名二维码", loginRequired = false)
	public void qrCode(HttpServletRequest request,HttpServletResponse response, @PathVariable String id)
			throws Exception {
		String url = MessageFormat.format(QR_FORMAT, super.getCurrentHostOrgPortalWebUrl(request), id);
		QrCodeUtils.createQrCode(url, 300, 300, super.getCurrentHostOrg(request).getAdminLogo(), response.getOutputStream());
	}
	
	/**
	 * 新增批次
	 */
	@RequestMapping("/add")
	@Operation(desc = "新增批次")
	public Object add(HttpServletRequest request,
					  @RequestParam(required = false) String zcType,
					  @RequestParam(required = false) String name,
					  @RequestParam(required = false) String bmStartTime,
					  @RequestParam(required = false) String bmEndTime,
					  @RequestParam(required = false) String replyStartTime,
					  @RequestParam(required = false) String replyEndTime,
					  @RequestParam(required = false) String reviewStartTime,
					  @RequestParam(required = false) String reviewEndTime,
					  @RequestParam(required = false) String isOneTrial,
					  @RequestParam(required = false) String approveUserIds,
					  @RequestParam(required = false) String logo) throws Exception {
		if (StringUtils.isBlank(name)) {
			throw BizException.withMessage("批次名称不能为空");
		}
		if (StringUtils.isEmpty(bmStartTime)) {
			throw BizException.withMessage("报名开始时间不能为空");
		}
		if (StringUtils.isEmpty(bmEndTime)) {
			throw BizException.withMessage("报名结束时间不能为空");
		}
		if (DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"))) {
			throw BizException.withMessage("报名开始时间不能大于结束时间");
		}
		if (StringUtils.isEmpty(replyStartTime)) {
			throw BizException.withMessage("答辩开始时间不能为空");
		}
		if (StringUtils.isEmpty(replyEndTime)) {
			throw BizException.withMessage("答辩结束时间不能为空");
		}
		if (DateUtils.parse(replyStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(replyEndTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("答辩开始时间不能大于答辩结束时间");
		}
		if (StringUtils.isEmpty(reviewStartTime)) {
			throw BizException.withMessage("评审开始时间不能为空");
		}
		if (StringUtils.isEmpty(reviewEndTime)) {
			throw BizException.withMessage("评审结束时间不能为空");
		}
		if (DateUtils.parse(reviewStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(reviewEndTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("评审开始时间不能大于评审结束时间");
		}
		Integer count = service.selectCount((EntityWrapper<ZyjdBmBatch>) new EntityWrapper<ZyjdBmBatch>()
				.eq("name", name).eq("type", ZyjdBmBatchType.ZCPS)
				.eq("host_org_id", super.getCurrentHostOrgId(request)));
		if (count>0){
			throw BizException.withMessage("批次名称已存在");
		}
		if (StringUtils.isEmpty(isOneTrial)) {
			throw BizException.withMessage("请选择是否开启初审");
		}
		if (isOneTrial.equals(Constants.YES) && super.getLoginRole(request) == Role.HOST_ORG) {
			approveUserIds = super.getLoginUserId(request);
		}
		User user = this.getLoginUser(request).getUser();
		ZyjdBmBatch bmBatch = new ZyjdBmBatch();
		bmBatch.setId(BaseUtil.generateId2());
		bmBatch.setName(name);
		bmBatch.setType(ZyjdBmBatchType.ZCPS);
		bmBatch.setStatus(BmOpenStatus.BLOCK);
		if (super.getLoginRole(request) == Role.HOST_ORG) {
			bmBatch.setApproveStatus(ZyjdApproveStatus.PASS);
			bmBatch.setApproveTime(new Date());
			bmBatch.setApproveAdvice("主办单位管理员添加，自动审核通过");
			bmBatch.setApproveUserId(super.getLoginUserId(request));
		}
		bmBatch.setBmStartTime(DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setBmEndTime(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReplyStartTime(DateUtils.parse(replyStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReplyEndTime(DateUtils.parse(replyEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReviewStartTime(DateUtils.parse(reviewStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReviewEndTime(DateUtils.parse(reviewEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setCreateTime(new Date());
		bmBatch.setHostOrgId(super.getCurrentHostOrgId(request));
		bmBatch.setCreatorId(user.getId());
		bmBatch.setFormId(formId);
		bmBatch.setIsOneTrial(StringUtils.isEmpty(isOneTrial)? Constants.NO : isOneTrial);
		bmBatch.setLogo(logo);
		service.insert(bmBatch);
		if (StringUtils.isNotEmpty(approveUserIds)) {
			List<ZyjdBmTrial> list = new ArrayList<>();
			for (String id : approveUserIds.split(",")) {
				list.add(new ZyjdBmTrial(BaseUtil.generateId(), bmBatch.getId(), id, Role.HOST_ORG, 1));
			}
			DBUtils.insertBatch(list, ZyjdBmTrial.class);
		}
		return true;
	}
	
	/**
	 * 获取批次
	 */
	@RequestMapping("/getById")
	@Operation(desc = "获取批次")
	public Object getById(@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		ZyjdBmBatch zyjdBmBatch = service.selectById(id);
		ZyjdBmBatchVo zyjdBmBatchVo = new ZyjdBmBatchVo();
		BeanUtils.copyProperties(zyjdBmBatch,zyjdBmBatchVo);
		List<ZyjdBmTrial> zyjdBmTrials = zyjdBmTrialService.selectList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>().eq("bmbatch_id", id));
		zyjdBmBatchVo.setApproveUserIds(zyjdBmTrials.stream().map(ZyjdBmTrial::getUserId).collect(Collectors.toList()));
		return zyjdBmBatchVo;
	}
	
	/**
	 * 获取审批详情
	 */
	@RequestMapping("/getBatchById")
	@Operation(desc = "获取审批详情")
	public Object getBatchById(HttpServletRequest request,ZyjdBmBatchQueryParams params) throws Exception {
		if(StringUtils.isBlank(params.getId())){
			throw BizException.withMessage("请传入批次id");
		}
		return service.approveList(params).getRecords().get(0);
	}
	
	/**
	 * 修改批次
	 */
	@RequestMapping("/edit")
	@Operation(desc = "修改批次")
	public Object edit(HttpServletRequest request, @RequestParam(required = false) String id,
					   @RequestParam(required = false) String name,
					   @RequestParam(required = false) String bmStartTime,
					   @RequestParam(required = false) String bmEndTime,
					   @RequestParam(required = false) String replyStartTime,
					   @RequestParam(required = false) String replyEndTime,
					   @RequestParam(required = false) String reviewStartTime,
					   @RequestParam(required = false) String reviewEndTime,
					   @RequestParam(required = false) String isOneTrial,
					   @RequestParam(required = false) String approveUserIds,
					   @RequestParam(required = false) String logo) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		if (StringUtils.isBlank(name)) {
			throw BizException.withMessage("批次名称不能为空");
		}
		if (StringUtils.isEmpty(bmStartTime)) {
			throw BizException.withMessage("报名开始时间不能为空");
		}
		if (StringUtils.isEmpty(bmEndTime)) {
			throw BizException.withMessage("报名结束时间不能为空");
		}
		if (DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"))) {
			throw BizException.withMessage("报名开始时间不能大于结束时间");
		}
		if (StringUtils.isEmpty(replyStartTime)) {
			throw BizException.withMessage("答辩开始时间不能为空");
		}
		if (StringUtils.isEmpty(replyEndTime)) {
			throw BizException.withMessage("答辩结束时间不能为空");
		}
		if (DateUtils.parse(replyStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(replyEndTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("答辩开始时间不能大于答辩结束时间");
		}
		if (StringUtils.isEmpty(reviewStartTime)) {
			throw BizException.withMessage("评审开始时间不能为空");
		}
		if (StringUtils.isEmpty(reviewEndTime)) {
			throw BizException.withMessage("评审结束时间不能为空");
		}
		if (DateUtils.parse(reviewStartTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(reviewEndTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("评审开始时间不能大于评审结束时间");
		}
		if (StringUtils.isEmpty(isOneTrial)) {
			throw BizException.withMessage("请选择是否开启初审");
		}
		if (isOneTrial.equals(Constants.YES) && super.getLoginRole(request) == Role.HOST_ORG) {
			approveUserIds = super.getLoginUserId(request);
		}
		Integer count = service.selectCount((EntityWrapper<ZyjdBmBatch>) new EntityWrapper<ZyjdBmBatch>()
				.eq("name", name).eq("type", ZyjdBmBatchType.ZCPS)
				.eq("host_org_id", super.getCurrentHostOrgId(request))
				.ne("id", id));
		if (count>0){
			throw BizException.withMessage("批次名称已存在");
		}
		User user = this.getLoginUser(request).getUser();
		ZyjdBmBatch bmBatch = service.selectById(id);
		if (bmBatch == null) {
			throw BizException.withMessage("批次不存在");
		}
		bmBatch.setName(name);
		bmBatch.setBmStartTime(DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setBmEndTime(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReplyStartTime(DateUtils.parse(replyStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReplyEndTime(DateUtils.parse(replyEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReviewStartTime(DateUtils.parse(reviewStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setReviewEndTime(DateUtils.parse(reviewEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setUpdateTime(new Date());
		bmBatch.setUpdatorId(user.getId());
		bmBatch.setIsOneTrial(StringUtils.isEmpty(isOneTrial)? Constants.NO : isOneTrial);
		bmBatch.setLogo(logo);
		service.updateById(bmBatch);
		if (StringUtils.isNotEmpty(approveUserIds)) {
			List<ZyjdBmTrial> list = new ArrayList<>();
			zyjdBmTrialService.deleteList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>().eq("bmbatch_id", id));
			for (String userId : approveUserIds.split(",")) {
				list.add(new ZyjdBmTrial(BaseUtil.generateId(), bmBatch.getId(), userId, Role.HOST_ORG, 1));
			}
			DBUtils.insertBatch(list, ZyjdBmTrial.class);
		}
		return true;
	}
	
	/**
	 * 改变批次的报名状态
	 */
	@RequestMapping("/changeBmOpenStatus")
	@Operation(desc = "改变批次的报名状态")
	public Object changeBmOpenStatus(HttpServletRequest request, @RequestParam(required = false) String id,
			@RequestParam(required = false) BmOpenStatus status) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		if (status == null) {
			throw BizException.withMessage("请选择状态");
		}
		// 通过id获取此对象
		ZyjdBmBatch bmBatch = service.selectById(id);
		if (bmBatch == null) {
			throw BizException.withMessage("报名批次不存在");
		}
		Integer scopes = zyjdBmScopeService.selectCount((EntityWrapper<ZyjdBmScope>) new EntityWrapper<ZyjdBmScope>().eq("bmbatch_id", id));
		if (status == BmOpenStatus.BLOCK && scopes > 0) {
			throw BizException.withMessage("批次下存在申报范围，不能禁用");
		}
		bmBatch.setStatus(status);
		bmBatch.setUpdateTime(new Date());
		bmBatch.setUpdatorId(super.getLoginUserId(request));
		service.updateById(bmBatch);
		return true;
	}

	/**
	 * 批次下拉
	 */
	@RequestMapping("/select")
	@Operation(desc = "批次下拉")
	public Object select(HttpServletRequest request,@RequestParam(required = false) BmOpenStatus status) throws Exception {
		return service.selectList((EntityWrapper<ZyjdBmBatch>) new EntityWrapper<ZyjdBmBatch>()
				.eq(status != null, "status", status)
				.eq("type", ZyjdBmBatchType.ZCPS)
				.eq("host_org_id", super.getCurrentHostOrgId(request)));
	}

	/**
	 * 设置主任
	 */
	@RequestMapping("/setDirector")
	@Operation(desc = "设置主任")
	public Object setDirector(HttpServletRequest request, @RequestParam(required = false) String id,
							  @RequestParam(required = false) String directorId,
							  @RequestParam(required = false) String supervisor,
							  @RequestParam(required = false) String deputyDirectorIds) {
		service.setDirector(id, directorId, supervisor, deputyDirectorIds);
		return true;
	}

	/**
	 * 获取主任
	 */
	@RequestMapping("/directorDetail")
	@Operation(desc = "获取主任")
	public Object directorDetail(HttpServletRequest request, @RequestParam(required = false) String id) {
		return service.directorDetail(id);
	}

	/**
	 * 保存技能申报的数据
	 */
	@RequestMapping("/saveSkillApply")
	@Operation(desc = "保存技能申报的数据")
	public Object saveSkillApply(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {

		JSONArray jsonArray = params.getJSONArray("professionList");
		if (jsonArray == null || jsonArray.size() == 0) {
			throw BizException.withMessage("请选择您需要申报的工种");
		}

		String bmbatchId = BaseUtil.getStringValueFromJson(params, "id");
		String name = BaseUtil.getStringValueFromJson(params, "name");
		String years = BaseUtil.getStringValueFromJson(params, "years");
		ZyjdBmBatch zyjdBmBatch = service.selectById(bmbatchId);

		if (zyjdBmBatch != null && zyjdBmBatch.getApproveStatus() == ZyjdApproveStatus.CHECK) {
			throw BizException.withMessage("您的技能申报信息正在审核中...，无法更改");
		}

		if (zyjdBmBatch != null && zyjdBmBatch.getApproveStatus() == ZyjdApproveStatus.PASS) {
			throw BizException.withMessage("您的技能申报信息已经审核通过，无法更改");
		}

		if (zyjdBmBatch == null) {
			zyjdBmBatch = new ZyjdBmBatch();
			zyjdBmBatch.setId(BaseUtil.generateId2());
			zyjdBmBatch.setName(name);
			zyjdBmBatch.setYears(years);
			zyjdBmBatch.setUserId(super.getLoginUserId(request));
			zyjdBmBatch.setCreateTime(new Date());
			zyjdBmBatch.setType(ZyjdBmBatchType.ZYJD);
			zyjdBmBatch.setCreatorId(super.getLoginUserId(request));
			zyjdBmBatch.setHostOrgId(super.getCurrentHostOrgId(request));
		}
		else {
			zyjdBmBatch.setName(name);
			zyjdBmBatch.setYears(years);
		}
		
		zyjdBmBatch.setApproveStatus(ZyjdApproveStatus.CHECK);
		zyjdBmBatch.setApproveTime(null);
		zyjdBmBatch.setApproveAdvice(null);

		List<ZyjdBmScope> list = new ArrayList<ZyjdBmScope>();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject profession = jsonArray.getJSONObject(i);
			String professionId = BaseUtil.getStringValueFromJson(profession, "id");
			String checked = BaseUtil.getStringValueFromJson(profession, "checked");
			if (!checked.equals(Constants.STRING_TRUE)) {
				continue;
			}
			ZyjdProfession p = professionService.selectById(professionId);
			JSONArray techLevels = profession.getJSONArray("techLevels");
			List<String> applyLevels = new ArrayList<String>();
			for (int k = 0; k < techLevels.size(); k++) {
				Integer count = zyjdBmScopeService.checkLevel(super.getCurrentHostOrgId(request), professionId, techLevels.getString(k));
				if (count == 0){
					throw BizException.withMessage("无权限申报："+p.getCode()+"-"+p.getName()+"，等级:" + techLevels.getString(k));
				}
				applyLevels.add(techLevels.getString(k));
			}
			ZyjdBmScope zyjdBmScope = new ZyjdBmScope();
			zyjdBmScope.setId(BaseUtil.generateId2());
			zyjdBmScope.setBmbatchId(zyjdBmBatch.getId());
			zyjdBmScope.setIndustryId(p.getIndustryId());
			zyjdBmScope.setProfessionId(professionId);
			zyjdBmScope.setTechLevel(StringUtils.join(applyLevels, ","));
			zyjdBmScope.setCreateTime(new Date());
			zyjdBmScope.setCreatorId(super.getLoginUserId(request));
			list.add(zyjdBmScope);
		}
		service.saveSkillApply(zyjdBmBatch, list);
		return true;
	}
}
