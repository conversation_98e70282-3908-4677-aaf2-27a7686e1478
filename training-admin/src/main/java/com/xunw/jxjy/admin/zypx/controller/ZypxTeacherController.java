package com.xunw.jxjy.admin.zypx.controller;

import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.inf.params.TeacherQueryParams;
import com.xunw.jxjy.model.sys.service.UserInfoService;
import jxl.read.biff.BiffException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 职业培训师资
 */
@RestController
@RequestMapping("/htgl/biz/teacher")
public class ZypxTeacherController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ZypxTeacherController.class);//记录打印日志用的

	@Autowired
	private UserInfoService userInfoService;

	@RequestMapping("/list")
	@Operation(desc = "师资列表")
	public Object teacherList(HttpServletRequest request, TeacherQueryParams params){
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return userInfoService.findTeachers(params);
	}

	@RequestMapping("/add")
	@Operation(desc = "师资新增")
	public Object add(HttpServletRequest request, TeacherQueryParams params){
		return userInfoService.addTeacher(params,super.getLoginUserId(request),super.getCurrentHostOrgId(request));
	}

	@RequestMapping("/edit")
	@Operation(desc = "师资修改")
	public Object edit(HttpServletRequest request, TeacherQueryParams params){
		return userInfoService.editTeacher(params,super.getLoginUserId(request));
	}

	@RequestMapping("/teacherDetail")
	@Operation(desc = "师资详情")
	public Object teacherDetail(HttpServletRequest request, TeacherQueryParams params){
		return userInfoService.teacherDetail(params);
	}

	@RequestMapping("/importTeachers")
	@Operation(desc = "考评员导入")
	public Object importTeachers(HttpServletRequest request,
								 @RequestParam(value = "file") MultipartFile file,@RequestParam(required = false) String typeId) throws Exception {
		return userInfoService.batchImport(file, typeId, super.getCurrentHostOrgId(request), super.getLoginUserId(request));
	}

	@RequestMapping("/teacherImport")
	@Operation(desc = "师资导入")
	public Object teacherImport(HttpServletRequest request,
							   @RequestParam(value = "file") MultipartFile file,@RequestParam(required = false) String typeId) throws Exception {
		return userInfoService.teacherImport(file, typeId, super.getCurrentHostOrgId(request), super.getLoginUserId(request));
	}
}
