package com.xunw.jxjy.admin.zyjd.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.QrCodeUtils;
import com.xunw.jxjy.model.enums.AuditMode;
import com.xunw.jxjy.model.enums.BmOpenStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.ZyjdApproveStatus;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.params.ZyjdProfessionQueryParams;
import com.xunw.jxjy.model.inf.service.ZyjdProfessionService;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.utils.FormLoader;
import com.xunw.jxjy.model.zyjd.entity.BizBmAuditLog;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmTrial;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmBatchQueryParams;
import com.xunw.jxjy.model.zyjd.service.BizBmAuditLogService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmScopeService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmTrialService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 职业鉴定批次管理
 */
@RestController
@RequestMapping("/htgl/biz/zyjd/bmbatch")
public class ZyjdBmBatchController extends BaseController {

	@Autowired
	private ZyjdBmBatchService service;
	@Autowired
	private ZyjdProfessionService professionService;
	@Autowired
	private ZyjdBmScopeService zyjdBmScopeService;
	private static final String QR_FORMAT = "{0}kaosheng/mobile/zyjd/bm/startPage/{1}";
    @Autowired
    private ZyjdBmTrialService zyjdBmTrialService;
    @Autowired
	private ZyjdBmService zyjdBmService;
    @Autowired
	private BizBmAuditLogService bizBmAuditLogService;

	/**
	 * 列表接口
	 */
	@RequestMapping("/list")
	@Operation(desc = "列表")
	public Object list(HttpServletRequest request, ZyjdBmBatchQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		params.setNotType(ZyjdBmBatchType.ZCPS);
		Page<Map<String, Object>> page = service.list(params);
		page.getRecords().forEach(x->{
			x.put("qr_url", MessageFormat.format(QR_FORMAT, 
					super.getCurrentHostOrgPortalWebUrl(request),
					BaseUtil.getStringValueFromMap(x, "id")));
		});
		return page;
	}
	

	/**
	 * 生成报名二维码
	 */
	@RequestMapping("/qrCode/{id}")
	@Operation(desc = "生成报名二维码", loginRequired = false)
	public void qrCode(HttpServletRequest request,HttpServletResponse response, @PathVariable String id)
			throws Exception {
		String url = MessageFormat.format(QR_FORMAT, super.getCurrentHostOrgPortalWebUrl(request), id);
		QrCodeUtils.createQrCode(url, 300, 300, super.getCurrentHostOrg(request).getAdminLogo(), response.getOutputStream());
	}
	
	
	/**
	 * 批次审批列表
	 */
	@RequestMapping("/approveList")
	@Operation(desc = "批次审批列表")
	public Object approveList(HttpServletRequest request, ZyjdBmBatchQueryParams params) throws Exception {
		LoginUser loginUser = super.getLoginUser(request);
		if (loginUser.getRole() != Role.HOST_ORG) {
			params.setUserId(super.getLoginUserId(request));
		}
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setType(ZyjdBmBatchType.ZYJD);//只考虑技能认定的批次审核
		params.setIsApplyed(Constants.YES);//只查询技能申报的
		return service.approveList(params);
	}
	
	/**
	 * 审批操作
	 */
	@RequestMapping("/doApprove")
	@Operation(desc = "审批")
	public Object doApprove(HttpServletRequest request, @RequestParam(required = false) String id,
							@RequestParam(required = false) ZyjdApproveStatus approveStatus, @RequestParam(required = false) String approveAdvice)
			throws Exception {
		if (org.apache.commons.lang.StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请传入批次id");
		}
		LoginUser loginUser = super.getLoginUser(request);
		service.doApprove(id, approveStatus, approveAdvice , loginUser.getUser().getId());
		return true;
	}
	
	/**
	 * 新增批次
	 */
	@RequestMapping("/add")
	@Operation(desc = "新增批次")
	public Object add(HttpServletRequest request, 
			@RequestParam(required = false) String name,
			@RequestParam(required = false) ZyjdBmBatchType type,
			@RequestParam(required = false) String bmStartTime, 
			@RequestParam(required = false) String bmEndTime,
			@RequestParam(required = false) String jfEndTime,
			@RequestParam(required = false) String zkzStartTime,
			@RequestParam(required = false) String zkzEndTime,
			@RequestParam(required = false) String bankId,
			@RequestParam(required = false) String isAutoNextBatch,
			@RequestParam(required = false) String isAllowMultiProfession,
			@RequestParam(required = false) String isFeeSeparate,
			@RequestParam(required = false) String formId,
			@RequestParam(required = false) String years,
		    @RequestParam(required = false) String isOneTrial,
		    @RequestParam(required = false) AuditMode auditMode,
		    @RequestParam(required = false) String approveUserIds) throws Exception {
		if (StringUtils.isEmpty(years)) {
			throw BizException.withMessage("请选择年度");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入批次名称");
		}
		if (StringUtils.isEmpty(bmStartTime)) {
			throw BizException.withMessage("请选择报名开始时间");
		}
		if (StringUtils.isEmpty(bmEndTime)) {
			throw BizException.withMessage("请选择报名结束时间");
		}
		if(type == null) {
			throw BizException.withMessage("请选择类型");
		}
//		if (type == ZyjdBmBatchType.TDDZCL) {
			if (StringUtils.isEmpty(isOneTrial)) {
				throw BizException.withMessage("请选择是否开启初审");
			}
			if (isOneTrial.equals(Constants.YES) && super.getLoginRole(request) == Role.XM_LEADER) {
				approveUserIds = super.getLoginUserId(request);
			}
//		}
		User user = this.getLoginUser(request).getUser();
		ZyjdBmBatch bmBatch = new ZyjdBmBatch();
		bmBatch.setId(BaseUtil.generateId2());
		bmBatch.setName(name);
		bmBatch.setType(type);
		bmBatch.setStatus(BmOpenStatus.BLOCK);
		if (super.getLoginRole(request) == Role.HOST_ORG) {
			bmBatch.setApproveStatus(ZyjdApproveStatus.PASS);
			bmBatch.setApproveTime(new Date());
			bmBatch.setApproveAdvice("主办单位管理员添加，自动审核通过");
			bmBatch.setApproveUserId(super.getLoginUserId(request));
		}
		bmBatch.setBmStartTime(DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setBmEndTime(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setJfEndTime(DateUtils.parse(jfEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setZkzStartTime(DateUtils.parse(zkzStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setZkzEndTime(DateUtils.parse(zkzEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setCreateTime(new Date());
		bmBatch.setHostOrgId(super.getCurrentHostOrgId(request));
		bmBatch.setCreatorId(user.getId());
		bmBatch.setFormId(formId);
		bmBatch.setBankId(bankId);
		bmBatch.setYears(years);
		bmBatch.setIsAutoNextBatch(isAutoNextBatch == null ? Constants.NO : isAutoNextBatch);
		bmBatch.setIsAllowMultiProfession(isAllowMultiProfession == null ? Constants.NO : isAllowMultiProfession);
		bmBatch.setIsFeeSeparate(isFeeSeparate == null ? Constants.NO : isFeeSeparate);
		bmBatch.setIsOneTrial(StringUtils.isEmpty(isOneTrial)? Constants.NO : isOneTrial);
		bmBatch.setAuditMode(auditMode == null ? AuditMode.OR : auditMode);
		service.insert(bmBatch);
		if (StringUtils.isNotEmpty(approveUserIds)) {
			List<ZyjdBmTrial> list = new ArrayList<>();
			for (String id : approveUserIds.split(",")) {
				list.add(new ZyjdBmTrial(BaseUtil.generateId(), bmBatch.getId(), id, Role.XM_LEADER, 1));
			}
			DBUtils.insertBatch(list, ZyjdBmTrial.class);
		}
		return true;
	}
	
	/**
	 * 获取批次
	 */
	@RequestMapping("/getById")
	@Operation(desc = "获取批次")
	public Object getById(@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		ZyjdBmBatch zyjdBmBatch = service.selectById(id);
		com.alibaba.fastjson.JSONObject result = (com.alibaba.fastjson.JSONObject) JSON.toJSON(zyjdBmBatch);
		List<ZyjdBmTrial> zyjdBmTrials = zyjdBmTrialService.selectList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>().eq("bmbatch_id", id));
		result.put("approveUserIds", zyjdBmTrials.stream().map(ZyjdBmTrial::getUserId).collect(Collectors.toList()));
		return result;
	}
	
	/**
	 * 获取审批详情
	 */
	@RequestMapping("/getBatchById")
	@Operation(desc = "获取审批详情")
	public Object getBatchById(HttpServletRequest request,ZyjdBmBatchQueryParams params) throws Exception {
		if(StringUtils.isBlank(params.getId())){
			throw BizException.withMessage("请传入批次id");
		}
		return service.approveList(params).getRecords().get(0);
	}
	
	/**
	 * 修改批次
	 */
	@RequestMapping("/edit")
	@Operation(desc = "修改批次")
	public Object edit(HttpServletRequest request, @RequestParam(required = false) String id,
			@RequestParam(required = false) ZyjdBmBatchType type,
			@RequestParam(required = false) String name,
			@RequestParam(required = false) String bmStartTime,
			@RequestParam(required = false) String bmEndTime,
			@RequestParam(required = false) String jfEndTime,
			@RequestParam(required = false) String zkzStartTime,
			@RequestParam(required = false) String bankId,
			@RequestParam(required = false) String zkzEndTime,
			@RequestParam(required = false) String isAutoNextBatch,
			@RequestParam(required = false) String isAllowMultiProfession,
			@RequestParam(required = false) String isFeeSeparate,
			@RequestParam(required = false) String formId,
			@RequestParam(required = false) String years,
			@RequestParam(required = false) String isOneTrial,
			@RequestParam(required = false) AuditMode auditMode,
		    @RequestParam(required = false) String approveUserIds) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		if (StringUtils.isBlank(years)) {
			throw BizException.withMessage("请选择年度");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入名称");
		}
		if (StringUtils.isEmpty(bmStartTime)) {
			throw BizException.withMessage("请选择报名开始时间");
		}
		if (StringUtils.isEmpty(bmEndTime)) {
			throw BizException.withMessage("请选择报名结束时间");
		}
		if(type == null) {
			throw BizException.withMessage("请选择类型");
		}
//		if (type == ZyjdBmBatchType.TDDZCL) {
			if (StringUtils.isEmpty(isOneTrial)) {
				throw BizException.withMessage("请选择是否开启初审");
			}
			if (isOneTrial.equals(Constants.YES) && super.getLoginRole(request) == Role.XM_LEADER) {
				approveUserIds = super.getLoginUserId(request);
			}
//		}
		User user = this.getLoginUser(request).getUser();
		ZyjdBmBatch bmBatch = service.selectById(id);
		if (bmBatch == null) {
			throw BizException.withMessage("批次不存在");
		}
		if (auditMode != bmBatch.getAuditMode()) {
			EntityWrapper<ZyjdBm> entityWrapper = new EntityWrapper<>();
			entityWrapper.eq("bmbatch_id", id);
			// 查询报名审核记录存在不存在
			List<ZyjdBm> zyjdBms = zyjdBmService.selectList(entityWrapper);
			if (CollectionUtils.isNotEmpty(zyjdBms)) {
				List<String> bmIds = zyjdBms.stream().map(ZyjdBm::getId).collect(Collectors.toList());
				EntityWrapper<BizBmAuditLog> bmAuditLogEntityWrapper = new EntityWrapper<>();
				bmAuditLogEntityWrapper.in("bm_id",bmIds);
				if (bizBmAuditLogService.selectCount(bmAuditLogEntityWrapper) > 0) {
					throw BizException.withMessage("当前批次已存在学生报名审核，禁止修改审核模式");
				}
			}
		}
		bmBatch.setName(name);
		bmBatch.setType(type);
		bmBatch.setBmStartTime(DateUtils.parse(bmStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setBmEndTime(DateUtils.parse(bmEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setJfEndTime(DateUtils.parse(jfEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setZkzStartTime(DateUtils.parse(zkzStartTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setZkzEndTime(DateUtils.parse(zkzEndTime,"yyyy-MM-dd HH:mm"));
		bmBatch.setUpdateTime(new Date());
		bmBatch.setUpdatorId(user.getId());
		bmBatch.setFormId(formId);
		bmBatch.setBankId(bankId);
		bmBatch.setYears(years);
		bmBatch.setIsAutoNextBatch(isAutoNextBatch == null ? Constants.NO : isAutoNextBatch);
		bmBatch.setIsAllowMultiProfession(isAllowMultiProfession == null ? Constants.NO : isAllowMultiProfession);
		bmBatch.setIsFeeSeparate(isFeeSeparate == null ? Constants.NO : isFeeSeparate);
		bmBatch.setIsOneTrial(StringUtils.isEmpty(isOneTrial)? Constants.NO : isOneTrial);
		bmBatch.setAuditMode(auditMode);
		service.updateById(bmBatch);
		if (StringUtils.isNotEmpty(approveUserIds)) {
			List<ZyjdBmTrial> list = new ArrayList<>();
			zyjdBmTrialService.deleteList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>().eq("bmbatch_id", id));
			if (isOneTrial.equals(Constants.YES)) {
				for (String userId : approveUserIds.split(",")) {
					list.add(new ZyjdBmTrial(BaseUtil.generateId(), bmBatch.getId(), userId, Role.XM_LEADER, 1));
				}
				DBUtils.insertBatch(list, ZyjdBmTrial.class);
			}
		}
		return true;
	}
	
	/**
	 * 改变批次的报名状态
	 */
	@RequestMapping("/changeBmOpenStatus")
	@Operation(desc = "改变批次的报名状态")
	public Object changeBmOpenStatus(HttpServletRequest request, @RequestParam(required = false) String id,
			@RequestParam(required = false) BmOpenStatus status) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		if (status == null) {
			throw BizException.withMessage("请选择状态");
		}
		// 通过id获取此对象
		ZyjdBmBatch bmBatch = service.selectById(id);
		if (bmBatch == null) {
			throw BizException.withMessage("报名批次不存在");
		}
		bmBatch.setStatus(status);
		bmBatch.setUpdateTime(new Date());
		bmBatch.setUpdatorId(super.getLoginUserId(request));
		service.updateById(bmBatch);
		return true;
	}

	/**
	 * 批次下拉
	 */
	@RequestMapping("/select")
	@Operation(desc = "批次下拉")
	public Object select(HttpServletRequest request,ZyjdBmBatchQueryParams params) throws Exception {
		params.setSize(Integer.MAX_VALUE);
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setNotType(ZyjdBmBatchType.ZCPS);
		return service.list(params).getRecords();
	}
	
	/**
	 * 年度下拉
	 */
	@RequestMapping("/getYears")
	@Operation(desc = "年度下拉")
	public Object getYears(HttpServletRequest request) throws Exception {
		return  service.getYears();
	}
	
	/**
	 * 获取技能认定的表单模板配置
	 */
	@RequestMapping("/getFormConfigs")
	@Operation(desc = "获取技能认定的表单模板配置")
	public Object getForms(HttpServletRequest request) throws Exception {
		List<JSONObject> list = FormLoader.load(ZyjdBmBatchType.ZYJD, super.getCurrentHostOrg(request).getCode());
		return list;
	}
	
	
	/**
	 * 保存技能申报的数据
	 */
	@RequestMapping("/saveSkillApply")
	@Operation(desc = "保存技能申报的数据")
	public Object saveSkillApply(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {

		JSONArray jsonArray = params.getJSONArray("professionList");
		if (jsonArray == null || jsonArray.size() == 0) {
			throw BizException.withMessage("请选择您需要申报的工种");
		}

		String bmbatchId = BaseUtil.getStringValueFromJson(params, "id");
		String name = BaseUtil.getStringValueFromJson(params, "name");
		String years = BaseUtil.getStringValueFromJson(params, "years");
		ZyjdBmBatch zyjdBmBatch = service.selectById(bmbatchId);

		if (zyjdBmBatch != null && zyjdBmBatch.getApproveStatus() == ZyjdApproveStatus.CHECK) {
			throw BizException.withMessage("您的技能申报信息正在审核中...，无法更改");
		}

		if (zyjdBmBatch != null && zyjdBmBatch.getApproveStatus() == ZyjdApproveStatus.PASS) {
			throw BizException.withMessage("您的技能申报信息已经审核通过，无法更改");
		}

		if (zyjdBmBatch == null) {
			zyjdBmBatch = new ZyjdBmBatch();
			zyjdBmBatch.setId(BaseUtil.generateId2());
			zyjdBmBatch.setName(name);
			zyjdBmBatch.setYears(years);
			zyjdBmBatch.setUserId(super.getLoginUserId(request));
			zyjdBmBatch.setCreateTime(new Date());
			zyjdBmBatch.setType(ZyjdBmBatchType.ZYJD);
			zyjdBmBatch.setCreatorId(super.getLoginUserId(request));
			zyjdBmBatch.setHostOrgId(super.getCurrentHostOrgId(request));
		}
		else {
			zyjdBmBatch.setName(name);
			zyjdBmBatch.setYears(years);
		}
		
		zyjdBmBatch.setApproveStatus(ZyjdApproveStatus.CHECK);
		zyjdBmBatch.setApproveTime(null);
		zyjdBmBatch.setApproveAdvice(null);

		List<ZyjdBmScope> list = new ArrayList<ZyjdBmScope>();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject profession = jsonArray.getJSONObject(i);
			String professionId = BaseUtil.getStringValueFromJson(profession, "id");
			String checked = BaseUtil.getStringValueFromJson(profession, "checked");
			if (!checked.equals(Constants.STRING_TRUE)) {
				continue;
			}
			ZyjdProfession p = professionService.selectById(professionId);
			JSONArray techLevels = profession.getJSONArray("techLevels");
			List<String> applyLevels = new ArrayList<String>();
			for (int k = 0; k < techLevels.size(); k++) {
				Integer count = zyjdBmScopeService.checkLevel(super.getCurrentHostOrgId(request), professionId, techLevels.getString(k));
				if (count == 0){
					throw BizException.withMessage("无权限申报："+p.getCode()+"-"+p.getName()+"，等级:" + techLevels.getString(k));
				}
				applyLevels.add(techLevels.getString(k));
			}
			ZyjdBmScope zyjdBmScope = new ZyjdBmScope();
			zyjdBmScope.setId(BaseUtil.generateId2());
			zyjdBmScope.setBmbatchId(zyjdBmBatch.getId());
			zyjdBmScope.setIndustryId(p.getIndustryId());
			zyjdBmScope.setProfessionId(professionId);
			zyjdBmScope.setTechLevel(StringUtils.join(applyLevels, ","));
			zyjdBmScope.setCreateTime(new Date());
			zyjdBmScope.setCreatorId(super.getLoginUserId(request));
			list.add(zyjdBmScope);
		}
		service.saveSkillApply(zyjdBmBatch, list);
		return true;
	}

	/**
	 * 获取当前项目负责人可以申报的工种，并自动回填已经勾选的工种
	 */
	@RequestMapping("getApplySkills")
	@Operation(desc = "获取当前项目负责人可以申报的工种，并自动回填已经勾选的工种")
	public Object getCollegeSkills(HttpServletRequest request, @RequestParam(required = false) String id)
			throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map<String, Object>> professions = new ArrayList<Map<String, Object>>();
		String loginOrgId = super.getLoginOrgId(request);
		if (StringUtils.isNotEmpty(loginOrgId)) {
			ZyjdProfessionQueryParams params = new ZyjdProfessionQueryParams();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			params.setReceiveOrgId(loginOrgId);
			params.setSize(Integer.MAX_VALUE);
			professions = professionService.getHostOrgProfessionList(params).getRecords();

			if (StringUtils.isNotEmpty(id)) {
				List<ZyjdBmScope> list = service.getBmScopeByBatchId(id);
				for (Map<String, Object> map : professions) {
					for (ZyjdBmScope zyjdBmScope : list) {
						if (zyjdBmScope.getProfessionId().equals(BaseUtil.getStringValueFromMap(map, "id"))) {
							map.put("checked", true);
							map.put("tech_levels", StringUtils.split(zyjdBmScope.getTechLevel(), ","));
						}
					}
				}
				result.put("bmbatch", service.selectById(id));
			}
			result.put("professions", professions);
		}
		return result;
	}
}
