package com.xunw.jxjy.admin.zypx.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.zypx.entity.ZypxXmContact;
import com.xunw.jxjy.model.zypx.service.ZypxXmContactService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@RestController
@RequestMapping("/htgl/biz/xm/contact")
public class ZypxXmContactController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ZypxXmContactController.class);// 记录打印日志用的

	@Autowired
	private ZypxXmContactService zypxXmContactService;

	@RequestMapping("/add")
	@Operation(desc = "添加培训联系人")
	public Object add(@RequestParam(required = false) String xmId,
					  @RequestParam(required = false) String name,
					  @RequestParam(required = false) String phone,
					  @RequestParam(required = false) String remark) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		if (zypxXmContactService.checkRepeat(xmId, name, phone)) {
			throw BizException.withMessage("该项目下已存在该联系人");
		}
		zypxXmContactService.insert(new ZypxXmContact(BaseUtil.generateId(), xmId, name, phone, remark));
		return true;
	}

	@RequestMapping("/edit")
	@Operation(desc = "修改培训联系人")
	public Object add(@RequestParam(required = false) String id,
					  @RequestParam(required = false) String xmId,
					  @RequestParam(required = false) String name,
					  @RequestParam(required = false) String phone,
					  @RequestParam(required = false) String remark) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("id不能为空");
		}
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		if (zypxXmContactService.selectCount((EntityWrapper<ZypxXmContact>) new EntityWrapper<ZypxXmContact>()
				.eq("xm_id", xmId).eq("name", name).eq("phone", phone)
				.ne("id", id)) > 0) {
			throw BizException.withMessage("该项目下已存在该联系人");
		}
		zypxXmContactService.updateById(new ZypxXmContact(id, xmId, name, phone, remark));
		return true;
	}

	@RequestMapping("/delete")
	@Operation(desc = "删除培训联系人")
	public Object delete(@RequestParam(required = false) String ids) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("项目日程id不能为空");
		}
		zypxXmContactService.deleteBatchIds(Arrays.asList(ids.split(",")));
		return true;
	}

	@RequestMapping("/list")
	@Operation(desc = "培训联系人列表")
	public Object listSchedule(@RequestParam(required = false) String xmId, @RequestParam(required = false) String keyword) {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		Wrapper<ZypxXmContact> wrapper = new EntityWrapper<ZypxXmContact>().eq("xm_id", xmId);
		if (StringUtils.isNotEmpty(keyword)) {
			wrapper.andNew().like("name", keyword).or().like("phone", keyword);
		}
		return zypxXmContactService.selectList((EntityWrapper<ZypxXmContact>) wrapper);
	}

}