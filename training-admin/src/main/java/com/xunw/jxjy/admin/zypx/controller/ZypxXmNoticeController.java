package com.xunw.jxjy.admin.zypx.controller;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.common.entity.CommClass;
import com.xunw.jxjy.model.common.service.CommClassService;
import com.xunw.jxjy.model.enums.XmNoticeStatus;
import com.xunw.jxjy.model.zypx.entity.ZypxXmNotice;
import com.xunw.jxjy.model.zypx.params.ZypxXmCommonQueryParams;
import com.xunw.jxjy.model.zypx.service.ZypxXmNoticeService;

@RestController
@RequestMapping("/htgl/biz/xm/notice")
public class ZypxXmNoticeController extends BaseController {

	@Autowired
	private ZypxXmNoticeService zypxXmNoticeService;
	@Autowired
	private CommClassService commClassService;

	@RequestMapping("/getClass")
	@Operation(desc = "获取班级列表")
	public Object getClass(HttpServletRequest request, @RequestParam(required = false) String xmId) throws Exception {
		EntityWrapper<CommClass> commClassWrapper = new EntityWrapper<>();
		commClassWrapper.eq("host_org_id", super.getCurrentHostOrgId(request))
				.eq(BaseUtil.isNotEmpty(xmId), "xm_id", xmId)
				.orderBy("sort_no");
		return commClassService.selectList(commClassWrapper);
	}

	@RequestMapping("/add")
	@Operation(desc = "添加项目通知")
	public Object add(HttpServletRequest request,
					  @RequestParam(required = false) String xmId,
					  @RequestParam(required = false) String title,
					  @RequestParam(required = false) String content,
					  @RequestParam(required = false) String classIds,
					  @RequestParam(required = false) XmNoticeStatus status) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		if (StringUtils.isEmpty(title)) {
			throw BizException.withMessage("公告标题不能为空");
		}
		if (StringUtils.isEmpty(content)) {
			throw BizException.withMessage("通知内容不能为空");
		}
		if (status == null) {
			throw BizException.withMessage("公告状态不能为空");
		}
		zypxXmNoticeService.insert(new ZypxXmNotice(BaseUtil.generateId(), xmId, title, content, classIds, status,
				super.getLoginUser(request).getUser().getUsername(), new Date()));
		return true;
	}

	/**
	 * 单个/批量删除
	 * 
	 * @param request
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/delete")
	@Operation(desc = "删除项目通知")
	public Object delete(HttpServletRequest request, @RequestParam(required = false) String ids) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请至少选择一条记录");
		}
		zypxXmNoticeService.delete(ids);
		return true;
	}

	@RequestMapping("/get")
	@Operation(desc = "获取项目通知")
	public Object get(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("通知公告id不能为空");
		}
		return zypxXmNoticeService.selectById(id);
	}

	@RequestMapping("/edit")
	@Operation(desc = "修改项目通知")
	public Object edit(HttpServletRequest request,
					   @RequestParam(required = false) String id,
					   @RequestParam(required = false) String title,
					   @RequestParam(required = false) String content,
					   @RequestParam(required = false) String classIds,
					   @RequestParam(required = false) XmNoticeStatus status) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("通知公告id不能为空");
		}
		if (StringUtils.isEmpty(title)) {
			throw BizException.withMessage("通知标题不能为空");
		}
		if (StringUtils.isEmpty(content)) {
			throw BizException.withMessage("通知内容不能为空");
		}
		if (status == null) {
			throw BizException.withMessage("公告状态不能为空");
		}
		ZypxXmNotice zypxXmNotice = zypxXmNoticeService.selectById(id);
		if (zypxXmNotice == null) {
			throw BizException.withMessage("公告状态不存在");
		}
		zypxXmNotice.setTitle(title);
		zypxXmNotice.setContent(content);
		zypxXmNotice.setClassIds(classIds);
		zypxXmNotice.setStatus(status);
		zypxXmNotice.setUpdatorId(super.getLoginUser(request).getUser().getUsername());
		zypxXmNotice.setUpdateTime(new Date());
		zypxXmNoticeService.updateById(zypxXmNotice);
		return true;
	}

	/**
	 * 单个/批量设置状态
	 * 
	 * @param request
	 * @param ids
	 * @param status
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/setStatus")
	@Operation(desc = "设置项目通知状态")
	public Object setStatus(HttpServletRequest request, @RequestParam(required = false) String ids,
			@RequestParam(required = false) XmNoticeStatus status) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请至少选择一条记录");
		}
		zypxXmNoticeService.setStatus(ids, status, super.getLoginUser(request).getUser().getUsername());
		return true;
	}

	@RequestMapping("/list")
	@Operation(desc = "项目通知列表", loginRequired = false)
	public Object list(HttpServletRequest request, ZypxXmCommonQueryParams params) throws Exception {
		return zypxXmNoticeService.pageQuery(params);
	}
}