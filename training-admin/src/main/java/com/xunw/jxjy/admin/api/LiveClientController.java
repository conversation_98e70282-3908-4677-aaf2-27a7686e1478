package com.xunw.jxjy.admin.api;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.qiniu.util.Auth;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.ApiBaseController;
import com.xunw.jxjy.admin.core.base.HostOrgResolveService;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.config.QiniuConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.SpringBeanUtils;
import com.xunw.jxjy.model.enums.Zbzt;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.zypx.service.ZypxLiveService;

/**
 * 直播客户端接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class LiveClientController extends ApiBaseController{

	private static final Logger logger = Logger.getLogger(LiveClientController.class);

	@Autowired
	private ZypxLiveService liveService;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private HostOrgResolveService hostOrgResolveService;

	@RequestMapping("/zbclient/live/getLiveBaseInfo")
	@ResponseBody
	public Object getBaseInfo(HttpServletRequest request) {
		try {
			Map<String, Object> data = new HashMap<String, Object>();
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Map<String, Object> map = liveService.getLiveDetailById(liveId);
			if(map == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			data.put("teacherId", BaseUtil.convertNullToEmpty(map.get("teacherId")));
			data.put("teacherName", BaseUtil.convertNullToEmpty(map.get("teacherName")));
			data.put("teacherUserName", BaseUtil.convertNullToEmpty(map.get("teacherUsername")));
			data.put("teacherAvatar", BaseUtil.convertNullToEmpty(map.get("teacherAvatar")));
			Map<String, Object> live = new HashMap<String, Object>();
			live.put("id", BaseUtil.convertNullToEmpty(map.get("id").toString()));
			live.put("courseCode", BaseUtil.convertNullToEmpty(map.get("courseCode")));
			live.put("courseName", BaseUtil.convertNullToEmpty(map.get("courseName")));
			//获取直播的推流地址
			//判断数据库存储的推流地址是否过期，如果过期，则需要刷新
			Date gqsj = (Date)map.get("tldzgqsj");
			if(gqsj.before(new Date())) {
				Live bizZypxZb = liveService.updateZbtldz(liveId);
				live.put("docStreamPushUrl", BaseUtil.convertNullToEmpty(bizZypxZb.getZbtldza()));//主流
				live.put("teacherStreamPushUrl", BaseUtil.convertNullToEmpty(bizZypxZb.getZbtldzb()));//教师流
			}
			else {
				live.put("docStreamPushUrl", BaseUtil.convertNullToEmpty(map.get("zbtldza")));
				live.put("teacherStreamPushUrl", BaseUtil.convertNullToEmpty(map.get("zbtldzb")));
			}
			live.put("docStreamRtmpViewUrl", BaseUtil.convertNullToEmpty(map.get("rtmpdza")));
			live.put("docStreamFlvViewUrl", BaseUtil.convertNullToEmpty(map.get("flvdza")));
			live.put("docStreamHlsViewUrl", BaseUtil.convertNullToEmpty(map.get("hlsdza")));

			live.put("teacherStreamRtmpViewUrl", BaseUtil.convertNullToEmpty(map.get("rtmpdzb")));
			live.put("teacherStreamFlvViewUrl", BaseUtil.convertNullToEmpty(map.get("flvdzb")));
			live.put("teacherStreamHlsViewUrl", BaseUtil.convertNullToEmpty(map.get("hlsdzb")));
			data.put("live", live);
			return this.successResult(data);
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}

	@RequestMapping("/zbclient/live/getZbzt")
	@ResponseBody
	public Object getZbzt(HttpServletRequest request) {
		try {
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Live bizZypxZb = liveService.selectById(liveId);
			if(bizZypxZb == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			return this.successResult(bizZypxZb.getZbzt());
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}


	@RequestMapping("/zbclient/live/getZbztRedis")
	@ResponseBody
	public Object getZbztRedis(HttpServletRequest request) {
		try {
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			return this.successResult(CacheHelper.getCache(Constants.LIVE_CACHE, liveId));
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}

	/**
	 * 停止直播
	 * @param request
	 * @return
	 */
	@RequestMapping("/zbclient/live/stop")
	@ResponseBody
	public Object stopLive(HttpServletRequest request) {
		try {
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Live bizZypxZb = liveService.selectById(liveId);
			if(bizZypxZb == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			if(Zbzt.FINISHED == bizZypxZb.getZbzt()) {
				throw BizException.withMessage("直播已结束，请勿重复操作！");
			}
			String notifyUrl = hostOrgResolveService._CURRENT_HOST_ORG_ADMIN_WEB_URL(request) + Constants.ZB_NOTIFY_URI;
			liveService.updateChannelZhiboStatus(liveId, Zbzt.FINISHED, notifyUrl);
			CacheHelper.removeCache("liveIds",liveId);
	    	return this.successResult(true);
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}

	/**
	 * 开始直播
	 * @param request
	 * @return
	 */
	@RequestMapping("/zbclient/live/start")
	@ResponseBody
	public Object startLive(HttpServletRequest request) {
		try {

			String liveId = request.getParameter("liveId");
			logger.info("直播开始"+liveId);
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Live bizZypxZb = liveService.selectById(liveId);
			if(bizZypxZb == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			if(Zbzt.FINISHED == bizZypxZb.getZbzt()) {
				throw BizException.withMessage("直播已结束！");
			}
			liveService.updateChannelZhiboStatus(liveId, Zbzt.PLAYING, null);
			return this.successResult(true);
		} catch (Exception exception) {


			return this.errorResult(exception);
		}
	}


	/**
	 * 恢复直播
	 * @param request
	 * @return
	 */
	@RequestMapping("/zbclient/live/recovery")
	@ResponseBody
	public Object recoveryLive(HttpServletRequest request) {
		try {
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Live bizZypxZb = liveService.selectById(liveId);
			if(bizZypxZb == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			if(Zbzt.FINISHED == bizZypxZb.getZbzt()) {
				throw BizException.withMessage("直播已结束，请勿重复操作！");
			}
			liveService.updateChannelZhiboStatus(liveId, Zbzt.PLAYING, null);
			CacheHelper.setCache("liveIds",liveId,"recovery");

			return this.successResult(true);
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}

	/**
	 * 课间休息
	 * @param request
	 * @return
	 */
	@RequestMapping("/zbclient/live/offline")
	@ResponseBody
	public Object offlineLive(HttpServletRequest request) {
		try {
			String liveId = request.getParameter("liveId");
			if(StringUtils.isEmpty(liveId)) {
				throw BizException.withMessage("请传入直播ID[liveId]");
			}
			Live bizZypxZb = liveService.selectById(liveId);
			if(bizZypxZb == null) {
				throw BizException.withMessage("直播ID错误(liveId="+liveId+"),系统中不存在此直播ID");
			}
			if(Zbzt.FINISHED == bizZypxZb.getZbzt()) {
				throw BizException.withMessage("直播已结束，请勿重复操作！");
			}
			if(Zbzt.READY == bizZypxZb.getZbzt()) {
				logger.info("直播还未开始"+liveId);
				throw BizException.withMessage("直播未开始，请勿课间休息！");
			}
			liveService.updateChannelZhiboStatus(liveId, Zbzt.OFFLINE, null);
			CacheHelper.setCache("liveIds",liveId,"offline");
			return this.successResult(true);
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
	}


	@RequestMapping("/zbclient/upload")
    public Object uploadResource(
            HttpServletRequest request, @RequestParam(required = false,value = "file") MultipartFile file) throws Exception {
        try {
        	if(file== null){
                throw BizException.withMessage("在请求中没有检测到文件");
            }
            String ext = FileHelper.getExtension(file.getOriginalFilename());
            String path = attConfig.getRootDir()+"/upload/zbclent/resource/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
            String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
            String url = FileHelper.storeFile(path, file.getInputStream(), newFileName);
            Map<String,Object> rst = new HashMap<>();
            rst.put("fileName", file.getOriginalFilename());
            rst.put("fileSizeInByte", file.getSize());
            rst.put("url", url);
            return this.successResult(rst);
		} catch (Exception exception) {
			return this.errorResult(exception);
		}
    }
	
	/**
	 * 生成七牛云上传token
	 */
	@RequestMapping("/zbclient/genUploadToken")
	public Object genUploadToken(
			HttpServletRequest request, @RequestParam(required = false, value = "liveId") String liveId) throws Exception {
		QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
		Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
		Live live = liveService.selectById(liveId);
		String key = qiniuConfig.getZbhfprefix() + "_"+ live.getZblmca() + "_" + DateUtils.getCurrentDate("yyyyMMddHHmmss") +"_cut.mp4";
		String upToken = auth.uploadToken(qiniuConfig.getBucket());
		Map<String, Object> result = new HashMap<>();
		result.put("key", key);
		result.put("token", upToken);
		return this.successResult(result);
	}
	
	/**
	 * 在线剪辑客户端添加直播回放
	 */
	@RequestMapping("/zbclient/tjhf")
	public Object tjhf(HttpServletRequest request, Live zb){
		if(StringUtils.isEmpty(zb.getId())) {
			throw BizException.withMessage("请传入直播ID");
		}
		if(StringUtils.isEmpty(zb.getHkdza())) {
			throw BizException.withMessage("请填写回放地址");
		}
		if(zb.getHkspsc() == null) {
			throw BizException.withMessage("请填写回放时长");
		}
		Live bizZb = liveService.selectById(zb.getId());
		bizZb.setHkdza(zb.getHkdza());
		bizZb.setHkspsc(zb.getHkspsc());
		bizZb.setZbzt(Zbzt.FINISHED);
		bizZb.setXgsj(new Date());
		liveService.updateById(bizZb);
		return this.successResult(bizZb);
	}

}
