package com.xunw.jxjy.admin.zcps.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.GroupStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.zyjd.entity.ZcGroup;
import com.xunw.jxjy.model.zyjd.params.ZcpsStudentParams;
import com.xunw.jxjy.model.zyjd.service.ZcGroupService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 答辩评审分组
 */
@RestController
@RequestMapping("/htgl/biz/zcps/group")
public class ZcGroupController extends BaseController {

	private static final Logger Logger = LoggerFactory.getLogger(ZcGroupController.class);

	@Autowired
	private ZcGroupService zcGroupService;

	@RequestMapping("/list")
	@Operation(desc = "分组列表")
	public Object pageQuery(HttpServletRequest request,
							Page page,
							@RequestParam(required = false) String bmbatchId,
							@RequestParam(required = false) String type) {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		if (StringUtils.isEmpty(type)) {
			throw BizException.withMessage("分组类型不能为空");
		}
		return zcGroupService.pageQuery(bmbatchId, type, super.getLoginRole(request) == Role.TEACHER?super.getLoginUserId(request):null, page);
	}

	@RequestMapping("/add")
	@Operation(desc = "分组新增")
	public Object add(HttpServletRequest request, ZcGroup group) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(group.getBmbatchId())) {
			throw BizException.withMessage("批次id不能为空");
		}
		zcGroupService.commitGroupBeforeCheck(group.getBmbatchId(), group.getType());
		if (group.getNum() == null) {
			throw BizException.withMessage("分组序号不能为空");
		}
		if (StringUtils.isEmpty(group.getType())) {
			throw BizException.withMessage("分组类型不能为空");
		}
		if (StringUtils.isEmpty(group.getName())) {
			throw BizException.withMessage("分组名称不能为空");
		}
		Integer count = zcGroupService.selectCount((EntityWrapper<ZcGroup>) new EntityWrapper<ZcGroup>()
				.eq("bmbatch_id", group.getBmbatchId())
				.eq("num", group.getNum())
				.eq("type", group.getType())
				.eq("name", group.getName()));
		if (count > 0) {
			throw BizException.withMessage("当前批次下的分组名称序号不能重复");
		}
		group.setId(BaseUtil.generateId2());
		group.setStatus(GroupStatus.NOTSTARTED);
		group.setCreatedBy(super.getLoginUserId(request));
		group.setCreatedTime(new Date());
		zcGroupService.insert(group);
		return true;
	}

	@RequestMapping("/edit")
	@Operation(desc = "分组修改")
	public Object edit(HttpServletRequest request, ZcGroup group) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(group.getId())) {
			throw BizException.withMessage("分组id不能为空");
		}
		if (StringUtils.isEmpty(group.getBmbatchId())) {
			throw BizException.withMessage("批次id不能为空");
		}
		zcGroupService.commitGroupBeforeCheck(group.getBmbatchId(), group.getType());
		zcGroupService.editGroupBeforeCheck(group.getId());
		if (group.getNum() == null) {
			throw BizException.withMessage("分组序号不能为空");
		}
		if (StringUtils.isEmpty(group.getType())) {
			throw BizException.withMessage("分组类型不能为空");
		}
		if (StringUtils.isEmpty(group.getName())) {
			throw BizException.withMessage("分组名称不能为空");
		}
		Integer count = zcGroupService.selectCount((EntityWrapper<ZcGroup>) new EntityWrapper<ZcGroup>()
				.eq("bmbatch_id", group.getBmbatchId())
				.eq("num", group.getNum())
				.eq("type", group.getType())
				.eq("name", group.getName())
				.ne("id", group.getId()));
		if (count > 0) {
			throw BizException.withMessage("当前批次下的分组名称序号不能重复");
		}
		group.setStatus(GroupStatus.NOTSTARTED);
		group.setUpdatedBy(super.getLoginUserId(request));
		group.setUpdatedTime(new Date());
		zcGroupService.updateById(group);
		return true;
	}

	@RequestMapping("/getNotGroupStudent")
	@Operation(desc = "获取待分组学员信息")
	public Object getNotGroupStudent(@RequestParam(required = false) String bmbatchId,
									 @RequestParam(required = false) String type) {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		if (StringUtils.isEmpty(type)) {
			throw BizException.withMessage("分组类型不能为空");
		}
		return zcGroupService.getNotGroupStudent(bmbatchId, type);
	}

	@RequestMapping("/getGroupInfoByBatchId")
	@Operation(desc = "获取批次的分组情况")
	public Object getGroupInfoByBatchId(String bmbatchId, String type) {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		return zcGroupService.getGroupInfoByBatchId(bmbatchId, type);
	}

	@RequestMapping("/getGroupPersonInfo")
	@Operation(desc = "获取分组的人员情况")
	public Object getGroupPersonInfo(String groupId) {
		if (StringUtils.isEmpty(groupId)) {
			throw BizException.withMessage("分组id不能为空");
		}
		return zcGroupService.getGroupPersonInfo(groupId);
	}

	@RequestMapping("/getFinishedStudent")
	@Operation(desc = "获取答辩过的学员列表")
	public Object getFinishedStudent(String groupId) {
		if (StringUtils.isEmpty(groupId)) {
			throw BizException.withMessage("分组id不能为空");
		}
		return zcGroupService.getFinishedStudent(groupId);
	}

	@RequestMapping("/delete")
	@Operation(desc = "分组删除")
	public Object delete(HttpServletRequest request, String ids) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择一条数据");
		}
		zcGroupService.deleteGroup(ids);
		return true;
	}

	@RequestMapping("/settingStudent")
	@Operation(desc = "手工分组")
	public Object settingStudent(HttpServletRequest request, String id, String studentIds) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("分组id不能为空");
		}
		zcGroupService.settingStudent(id, studentIds);
		return true;
	}

	@RequestMapping("/autoSettingStudent")
	@Operation(desc = "智能分组")
	public Object autoSettingStudent(HttpServletRequest request, String bmbatchId, String type) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		zcGroupService.autoSettingStudent(bmbatchId, type, super.getLoginUserId(request));
		return true;
	}

	@RequestMapping("/genNum")
	@Operation(desc = "生成答辩序号")
	public Object genNum(HttpServletRequest request, String groupId, String type) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		zcGroupService.genNum(groupId, type);
		return true;
	}

	@RequestMapping("/settingTeacher")
	@Operation(desc = "设置评委")
	public Object settingTeacher(HttpServletRequest request,
								 @RequestParam(required = false) String id,
								 @RequestParam(required = false) String teacherIds,
								 @RequestParam(required = false) String leaderId) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("分组id不能为空");
		}
		zcGroupService.settingTeacher(id, teacherIds, leaderId);
		return true;
	}

	@RequestMapping("/view")
	@Operation(desc = "分组预览")
	public Object view(HttpServletRequest request,
					   @RequestParam(required = false) String id,
					   @RequestParam(required = false) String type) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("分组id不能为空");
		}
		if (StringUtils.isEmpty(type)) {
			throw BizException.withMessage("分组类型不能为空");
		}
		return zcGroupService.getGroupTeachersByBatchId(id, type);
	}


	@RequestMapping("/exportTeacher")
	@Operation(desc = "导出评委明细表")
	public Object exportTeacherWord(HttpServletRequest request, HttpServletResponse response,
									@RequestParam(required = false) String id,
									@RequestParam(required = false) String type) {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=teacher-info.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			zcGroupService.exportTeacher(id, type, os);
			os.flush();
		} catch (Exception e) {
			Logger.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
		return true;
	}

	@RequestMapping("/exportStudent")
	@Operation(desc = "导出学员明细表")
	public Object exportStudentWord(HttpServletRequest request, HttpServletResponse response,
									@RequestParam(required = false) String id,
									@RequestParam(required = false) String type) {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student-info.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			zcGroupService.exportStudent(id, type, os);
			os.flush();
		} catch (Exception e) {
			Logger.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
		return true;
	}

	/**
	 * 进入答辩
	 */
	@RequestMapping("/inRoom")
	@Operation(desc = "进入答辩")
	public Object inRoom(HttpServletRequest request,
						 @RequestParam(required = false) String groupId) {
		if (StringUtils.isEmpty(groupId)) {
			throw BizException.withMessage("分组id不能为空");
		}
		return zcGroupService.inRoomTeacher(groupId, super.getLoginUserId(request));
	}

	/**
	 * 重置房间状态
	 */
	@RequestMapping("/resetRoomStatus")
	@Operation(desc = "重置房间状态")
	public Object resetRoomStatus(HttpServletRequest request,
								  @RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("分组id不能为空");
		}
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		zcGroupService.resetRoomStatus(id);
		return true;
	}

	/**
	 * 学员信息查询
	 */
	@RequestMapping("/student/page")
	@Operation(desc = "学员信息查询")
	public Object studentPage(HttpServletRequest request,
							  ZcpsStudentParams params) {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return zcGroupService.studentPage(params);
	}

	/**
	 * 标记学员答辩状态
	 */
	@RequestMapping("/sign/student/reply/status")
	@Operation(desc = "标记学员答辩状态")
	public Object signReplyStatus(HttpServletRequest request,
							      @RequestParam(required = false) String ids,
								  @RequestParam(required = false) String status,
								  @RequestParam(required = false) String remark) {
		if (super.getLoginRole(request) != Role.HOST_ORG) {
			throw BizException.withMessage("暂无权限操作");
		}
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("id不能为空");
		}
		if (StringUtils.isEmpty(status)) {
			throw BizException.withMessage("答辩状态不能为空");
		}
		if (!"0".equals(status) && !"1".equals(status) && !"2".equals(status)) {
			throw BizException.withMessage("答辩状态格式错误");
		}
		zcGroupService.signReplyStatus(ids, status, remark, super.getLoginUserId(request));
		return true;
	}
}