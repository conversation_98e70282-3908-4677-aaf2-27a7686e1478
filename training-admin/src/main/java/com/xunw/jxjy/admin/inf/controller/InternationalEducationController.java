package com.xunw.jxjy.admin.inf.controller;

import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.inf.entity.InternationalEducation;
import com.xunw.jxjy.model.inf.params.InternationalEducationQueryParams;
import com.xunw.jxjy.model.inf.service.InternationalEducationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * 国际化教育管理
 */
@RestController
@RequestMapping("/htgl/inf/internationEducation")
public class InternationalEducationController extends BaseController {

    @Autowired
    private InternationalEducationService internationalEducationService;


    @RequestMapping("/list")
    @Operation(desc = "列表")
    public Object pageQuery(HttpServletRequest request, InternationalEducationQueryParams params) {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return internationalEducationService.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "新增")
    public Object add(HttpServletRequest request, InternationalEducation entity) {
        entity.check();
        entity.setHostOrgId(super.getCurrentHostOrgId(request));
        entity.setId(BaseUtil.generateId());
        entity.setCreatorId(super.getLoginUserId(request));
        entity.setCreateTime(new Date());
        internationalEducationService.insert(entity);
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "编辑")
    public Object edit(HttpServletRequest request, InternationalEducation entity) {
        entity.check();
        if (BaseUtil.isEmpty(entity.getId())) {
            throw BizException.withMessage("id不能为空");
        }
        entity.setHostOrgId(super.getCurrentHostOrgId(request));
        entity.setUpdatorId(super.getLoginUserId(request));
        entity.setUpdateTime(new Date());
        internationalEducationService.updateById(entity);
        return true;
    }

    @RequestMapping("/delete")
    @Operation(desc = "删除")
    public Object delete(HttpServletRequest request, @RequestParam(required = false) String ids) {
        if (BaseUtil.isEmpty(ids)) {
            throw BizException.withMessage("请至少选择一条数据");
        }
        internationalEducationService.deleteBatchIds(Arrays.asList(ids.split(",")));
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "获取详情")
    public Object getById(HttpServletRequest request, @RequestParam(required = false) String id) {
        if (BaseUtil.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        return internationalEducationService.selectById(id);
    }
}
