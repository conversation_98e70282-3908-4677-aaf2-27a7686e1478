package com.xunw.jxjy.admin.zypx.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.QrCodeUtils;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.Form;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.tk.service.DbPracticeService;
import com.xunw.jxjy.model.zypx.dto.ZypxXmDto;
import com.xunw.jxjy.model.zypx.entity.Plan;
import com.xunw.jxjy.model.zypx.entity.PlanDetail;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.params.PlanXmTreeQueryParams;
import com.xunw.jxjy.model.zypx.params.ZypxXmQueryParams;
import com.xunw.jxjy.model.zypx.service.PlanDetailService;
import com.xunw.jxjy.model.zypx.service.PlanService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 职业培训项目
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/xm")
public class ZypxXmController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ZypxXmController.class);//记录打印日志用的

	@Autowired
	private ZypxXmService service;
	@Autowired
	private ZypxTypeService typeService;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private PlanService planService;
	@Autowired
	private UserService userService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private FormService formService;
	@Autowired
	private PlanDetailService planDetailService;
	@Autowired
	private DbPracticeService dbPracticeService;
	@Autowired
	private ZypxXmCourseSettingService zypxXmCourseSettingService;

	private static final String XM_QR_FORMAT = "{0}h5/#/project_introduction?xmId={1}";
	private static final String QA_QR_FORMAT = "{0}h5/#/questionnaire_introduction?xmId={1}";

	@RequestMapping("/list")
    @Operation(desc = "培训项目查询")
    public Object list(HttpServletRequest request,
    	ZypxXmQueryParams params) throws Exception {
    	//若不指定培训计划  则根据主办单位查询培训计划
    	if (StringUtils.isEmpty(params.getPlanId())) {
    		params.setHostOrgId(super.getCurrentHostOrgId(request));
    	}
    	if (getLoginRole(request) == Role.XM_LEADER) {
    		params.setLeaderId(super.getLoginUserId(request));
		}
    	if (getLoginRole(request) == Role.ENTRUST_ORG) {
    		params.setEntrustOrgId(orgService.getTopOrg(super.getLoginOrgId(request)).getId());
    	}
    	Page<Map<String, Object>> page = service.pageQuery(params);
    	return page;
    }

	/**
	 * 生成项目二维码
	 */
	@RequestMapping("/qrCode/{id}")
	@Operation(desc = "生成二维码", loginRequired = false)
	public void qrCode(HttpServletRequest request,HttpServletResponse response, @PathVariable String id)
			throws Exception {
		String url = MessageFormat.format(XM_QR_FORMAT,
				super.getCurrentHostOrgPortalWebUrl(request), id);
		QrCodeUtils.createQrCode(url, 300, 300, super.getCurrentHostOrg(request).getAdminLogo(), response.getOutputStream());
	}

	/**
	 * 生成项目问卷二维码
	 */
	@RequestMapping("/qa/qrCode/{id}")
	@Operation(desc = "生成项目问卷二维码", loginRequired = false)
	public void qaQrCode(HttpServletRequest request,HttpServletResponse response, @PathVariable String id)
			throws Exception {
		String url = MessageFormat.format(QA_QR_FORMAT,
				super.getCurrentHostOrgPortalWebUrl(request), id);
		QrCodeUtils.createQrCode(url, 300, 300, super.getCurrentHostOrg(request).getAdminLogo(), response.getOutputStream());
	}

	@RequestMapping("/tree")
	@Operation(desc = "培训计划树")
	public Object tree(HttpServletRequest request, PlanXmTreeQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
		if (getLoginRole(request) == Role.ENTRUST_ORG) {
    		params.setEntrustOrgId(orgService.getTopOrg(super.getLoginOrgId(request)).getId());
    	}
		return service.getPlanXmTree(params);
	}

	@RequestMapping("/add")
	@Operation(desc = "新增培训项目")
	public Object add(HttpServletRequest request, ZypxXmDto xmDto) throws Exception {
		service.add(xmDto, super.getLoginUserId(request));
		return true;
	}

	@RequestMapping("/edit")
	@Operation(desc = "编辑培训项目")
	public Object edit(HttpServletRequest request, ZypxXmDto xmDto) throws Exception {
		service.edit(xmDto, super.getLoginUserId(request));
		return true;
	}

	 //获取项目
    @RequestMapping("/getById")
    @Operation(desc = "详情")
    public Object getById(HttpServletRequest request,
    		@RequestParam(required = false) String id) throws Exception {
    	ZypxXm zypxXm = service.selectById(id);
    	if (zypxXm.getTypeId() != null) {
    		ZypxType zypxType = typeService.selectById(zypxXm.getTypeId());
    		zypxXm.setZypxType(zypxType);
		}
    	if (StringUtils.isNotEmpty(zypxXm.getPlanId())) {
			Plan plan = planService.selectById(zypxXm.getPlanId());
			zypxXm.setPlan(plan);
		}
    	if (StringUtils.isNotEmpty(zypxXm.getLeaderId())) {
    		User user = userService.selectById(zypxXm.getLeaderId());
    		zypxXm.setLeader(user);
    	}
		if (StringUtils.isEmpty(zypxXm.getArriveRange())) {
			//设置允许签到的误差距离
			String distance = systemSettingService.getGlobalSetting(SysSettingEnum.ONLINE_ARRIVE_ALLOW_DISTANCE);
			zypxXm.setArriveRange(distance);
		}

		String formId = formService.getFormByXmId(zypxXm.getId());
		if (StringUtils.isNotEmpty(formId)) {
			Form form = formService.selectById(formId);
			zypxXm.setForm(form);
		}
		zypxXm.setCourses(zypxXmCourseSettingService.getAllCourseByXmId(id));
		PlanDetail planDetail = planDetailService.selectById(id);
		zypxXm.setPlanDetail(planDetail);
		return zypxXm;
    }

    // 获取项目类型树
 	@RequestMapping("/getTypeTree")
 	@Operation(desc = "获取项目类型树")
 	public Object getTypeTree(HttpServletRequest request) throws Exception {
 		return typeService.generateTreeSelect(getCurrentHostOrgId(request), TypeCategory.XM);
 	}

	// 获取所有项目类型（包括父和子）
	@RequestMapping("/getType")
	@Operation(desc = "获取所有的项目类型")
	public Object getType(HttpServletRequest request) throws Exception {
		return typeService.getType(getCurrentHostOrgId(request), TypeCategory.XM);
	}

 	/**
 	 * 获取项目编号
 	 */
 	@RequestMapping("/getSerialNumber")
 	@Operation(desc = "获取项目编号")
 	public Object getSerialNumber(HttpServletRequest request) throws Exception {
 		return planDetailService.buildXmSerialNumber(super.getCurrentHostOrgId(request));
 	}

 	// 培训项目下拉框 SAS服务下每一个主办单位只取自己的
  	@RequestMapping("/select")
  	@Operation(desc = "获取培训项目")
 	public Object select(HttpServletRequest request,@RequestParam(required = false) String year,
 			@RequestParam(required = false) String planId) {
  		ZypxXmQueryParams params = new ZypxXmQueryParams();
  		params.setHostOrgId(super.getCurrentHostOrgId(request));
  		params.setYear(year);
  		params.setPlanId(planId);
  		if (getLoginRole(request) == Role.XM_LEADER) {
  			params.setLeaderId(getLoginUserId(request));
		}
  		if (getLoginRole(request) == Role.ENTRUST_ORG) {
    		params.setEntrustOrgId(orgService.getTopOrg(super.getLoginOrgId(request)).getId());
    	}
		params.setCurrent(1);
		params.setSize(Integer.MAX_VALUE);
		return service.pageQuery(params).getRecords();
  	}

	/**
	 * 获取年度下拉
	 */
	@RequestMapping("/selectYear")
	@Operation(desc = "年度下拉")
	public Object selectYear( HttpServletRequest request){
		return planService.getYears(super.getCurrentHostOrgId(request));
	}


	@RequestMapping("/editScorePercent")
	@Operation(desc = "修改成绩合成标准")
	public Object editScorePercent(HttpServletRequest request,
			@RequestParam(required = false) String id,
			@RequestParam(required = false) Double learningScoreZb,
			@RequestParam(required = false) Double practiceScoreZb,
			@RequestParam(required = false) Double finalExamScoreZb
			) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目ID不能为空");
		}
		if (learningScoreZb == null) {
			throw BizException.withMessage("学习成绩占比不能为空");
		}
		if (practiceScoreZb == null) {
			throw BizException.withMessage("练习成绩占比不能为空");
		}
		if (finalExamScoreZb == null) {
			throw BizException.withMessage("终极考试成绩占比不能为空");
		}
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("培训项目不存在");
		}
		zypxXm.setLearningScoreZb(learningScoreZb);
		zypxXm.setPracticeScoreZb(practiceScoreZb);
		zypxXm.setFinalExamScoreZb(finalExamScoreZb);
		zypxXm.setUpdateTime(new Date());
		zypxXm.setUpdatorId(super.getLoginUserId(request));
		service.updateById(zypxXm);
		return true;
	}

	@RequestMapping("/statistic")
    @Operation(desc = "统计")
    public Object statisticByXm(HttpServletRequest request,
    	ZypxXmQueryParams params) throws Exception {
    	//若不指定培训计划  则根据主办单位查询培训计划
    	if (StringUtils.isEmpty(params.getPlanId())) {
    		params.setHostOrgId(super.getCurrentHostOrgId(request));
    	}
    	//项目负责人
		if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
    	return service.statisticByXm(params);
    }

	@RequestMapping("/statistic/export")
	@Operation(desc = "统计数据导出")
	public void statisticByXmExport(HttpServletRequest request,
									  HttpServletResponse response,
									  ZypxXmQueryParams params) {
		//若不指定培训计划  则根据主办单位查询培训计划
		if (StringUtils.isEmpty(params.getPlanId())) {
			params.setHostOrgId(super.getCurrentHostOrgId(request));
		}
		//项目负责人
		if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=xm_bm.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			service.statisticByXmExport(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}


	@RequestMapping("/statisticCost")
	@Operation(desc = "培训项目收费金额统计")
	public Object statisticCostByXm(HttpServletRequest request, ZypxXmQueryParams params) throws Exception {
		// 1、项目负责人只可看到自己负责项目的统计数据
		if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
		// 若不指定培训计划 则根据主办单位查询培训计划
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return service.statisticCostByXm(params);
	}

	/**
	 * 项目学习详情
	 */
	@RequestMapping("/xmStudyDetail")
	@Operation(desc = "项目学习详情")
	public Object xmStudyDetailStatistic(HttpServletRequest request,
										 @RequestParam(required = false) String id,
										 @RequestParam(required = false) String keyword,
										 Page page) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return service.xmStudyDetailStatistic(id, keyword, super.getCurrentHostOrgId(request), page);
	}

	/**
	 * 项目学习详情导出
	 */
	@RequestMapping("/xmStudyDetailExport")
	@Operation(desc = "项目学习详情导出")
	public void xmStudyDetailExport(HttpServletRequest request,
									HttpServletResponse response,
									@RequestParam(required = false) String id,
									@RequestParam(required = false) String keyword) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=xm_study_detail.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			service.xmStudyDetailExport(id, keyword, super.getCurrentHostOrgId(request), os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	@RequestMapping("/clone")
    @Operation(desc = "项目克隆")
    public Object clone(HttpServletRequest request,
    	String id)throws Exception {
		ZypxXm xm = service.selectById(id);
		ZypxXm cloneXm = (ZypxXm) xm.clone();
		cloneXm.setId(BaseUtil.generateId2());
		String code = BaseUtil.generateRandomEnglishLetterString(2).toUpperCase();
 		code += new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
 		cloneXm.setTitle(cloneXm.getTitle() + "-克隆");
		cloneXm.setSerialNumber(code);
		cloneXm.setCreateTime(new Date());
		service.insert(cloneXm);
		return true;
    }

	@RequestMapping("/batchSetStatus")
    @Operation(desc = "批量设置状态")
    public Object batchSetStatus(HttpServletRequest request,
    	String ids,XmStatus status)throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择项目");
		}
		if (status == null) {
			throw BizException.withMessage("请选择状态");
		}
		String[] xmIdArray = StringUtils.split(ids, ",");
		if (xmIdArray == null || xmIdArray.length == 0) {
			throw BizException.withMessage("请选择项目");
		}
		else {
			EntityWrapper<ZypxXm> wrapper = new EntityWrapper<ZypxXm>();
			wrapper.in("id", xmIdArray);
			List<ZypxXm> list = service.selectList(wrapper);
			for (ZypxXm zypxXm : list) {
				zypxXm.setStatus(status);
			}
			DBUtils.updateBatchById(list, ZypxXm.class);
		}
		return true;
    }

	@RequestMapping("/initArchive")
	@Operation(desc = "项目归档信息初始化")
	public Object initArchive(HttpServletRequest request, @RequestParam(required = false) String xmId) {
		return service.initArchive(xmId);
	}

	@RequestMapping("/archive")
	@Operation(desc = "项目归档")
	public Object archives(HttpServletRequest request,
			@RequestParam(required = false) String xmId,
			@RequestParam(required = false) String isFreePublic,
			@RequestParam(required = false) String isGovSubside,
			@RequestParam(required = false) String entrustOrgNature,
			@RequestParam(required = false) BigDecimal contractAmount,
			@RequestParam(required = false) String contractSignTime,
			@RequestParam(required = false) String contractFinishTime,
			@RequestParam(required = false) Double govAmount,
			@RequestParam(required = false) Double notGovAmount,
			@RequestParam(required = false) Double onlineHours,
			@RequestParam(required = false) Double offLineHours,
			@RequestParam(required = false) String dictIndustryId,
			@RequestParam(required = false) String countData,
			@RequestParam(required = false) Integer schoolTeacherCount,
			@RequestParam(required = false) String schoolTeacherName,
			@RequestParam(required = false) Integer outTeacherCount,
			@RequestParam(required = false) String outTeacherName,
			@RequestParam(required = false) String isTypical,
			@RequestParam(required = false) String typicalRemark,
			@RequestParam(required = false) Double totalCost,
			@RequestParam(required = false) Double surplus
			) {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择项目");
		}
		ZypxXm zypxXm = service.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		if (zypxXm.getStatus() == XmStatus.ARCHIVE) {
			throw BizException.withMessage("已经归档的项目不可再归档");
		}
		PlanDetail oldPlanDetail = planDetailService.selectById(xmId);
		if (oldPlanDetail != null) {
			if (StringUtils.isEmpty(oldPlanDetail.getPaper())) {
				throw BizException.withMessage("请上传立项表");
			}
			if (oldPlanDetail.getApproveStatus() == null
					|| oldPlanDetail.getApproveStatus() == ApproveResult.NOT_PASS) {
				throw BizException.withMessage("立项表未通过审核，项目不能归档");
			}
		}

		if (StringUtils.isEmpty(isFreePublic)) {
			throw BizException.withMessage("请选择是否为免费公益性项目");
		}
		if (StringUtils.isEmpty(isGovSubside)) {
			throw BizException.withMessage("请选择是否为政府补贴性项目");
		}
		if (StringUtils.isEmpty(entrustOrgNature)) {
			throw BizException.withMessage("请选择甲方类型");
		}
		if (contractAmount == null) {
			throw BizException.withMessage("请输入合同金额");
		}
		//设置归档信息
		oldPlanDetail.setIsFreePublic(isFreePublic);
		oldPlanDetail.setIsGovSubside(isGovSubside);
		oldPlanDetail.setEntrustOrgNature(entrustOrgNature);
		oldPlanDetail.setContractAmount(contractAmount);
		oldPlanDetail.setContractSignTime(DateUtils.parse(contractSignTime, "yyyyy-MM-dd"));
		oldPlanDetail.setContractFinishTime(DateUtils.parse(contractFinishTime, "yyyy-MM-dd"));
		oldPlanDetail.setGovAmount(govAmount);
		oldPlanDetail.setNotGovAmount(notGovAmount);
		oldPlanDetail.setOnlineHours(onlineHours);
		oldPlanDetail.setOffLineHours(offLineHours);
		oldPlanDetail.setDictIndustryId(dictIndustryId);
		oldPlanDetail.setCountData(countData);
		oldPlanDetail.setSchoolTeacherCount(schoolTeacherCount);
		oldPlanDetail.setSchoolTeacherName(schoolTeacherName);
		oldPlanDetail.setOuterTeacherCount(outTeacherCount);
		oldPlanDetail.setOuterTeacherName(outTeacherName);
		oldPlanDetail.setIsTypical(isTypical);
		oldPlanDetail.setTypicalRemark(typicalRemark);
		oldPlanDetail.setTotalCost(totalCost);
		oldPlanDetail.setSurplus(surplus);
		service.archives(zypxXm, oldPlanDetail);
		return true;
	}

	@RequestMapping("/archive/getDetails")
	@Operation(desc = "获取项目的归档信息")
	public Object getDetails(HttpServletRequest request, @RequestParam(required = false) String xmId) {
		return service.getDetails(xmId);
	}

	@RequestMapping("/archive/export")
	@Operation(desc = "导出项目的归档信息")
	public void archiveExport(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String xmId) throws Exception {
		OutputStream os = null;
		XSSFWorkbook wb = null;
		try {
			os = response.getOutputStream();
			response.setContentType("text/html;charset=utf-8");
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition", "attachment;filename=archive.xlsx");
			wb = service.archiveExport(xmId, os);
			wb.write(os);
		} catch (Exception e) {
			LOGGER.error("导出失败:", e); // 记录打印日志
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}


	@RequestMapping("/adviceList")
	@Operation(desc = "报名咨询列表查询")
	public Object adviceList(HttpServletRequest request,
					   ZypxXmQueryParams params) throws Exception {
		//若不指定培训计划  则根据主办单位查询培训计划
		if (StringUtils.isEmpty(params.getXmId())) {
			throw BizException.withMessage("项目ID不能为空");
		}
		return service.adviceList(params);
	}

	@RequestMapping("/profession/select")
	@Operation(desc = "选择项目下的职业")
	public Object professionSelect(HttpServletRequest request,
					  String xmId) throws Exception {
		if(StringUtils.isEmpty(xmId)) {
			return Collections.EMPTY_LIST;
		}
		else {
			return service.getProfessionByXmId(xmId);
		}
	}

	@RequestMapping("/dbPractices")
	@Operation(desc = "项目的练习题库")
	public Object dbPractices(@RequestParam(required = false) String xmId,
							  @RequestParam(required = false) String professionId,
							  @RequestParam(required = false) TechLevel techLevel) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return dbPracticeService.dbList(xmId, professionId, techLevel);
	}

	@RequestMapping("/setDbPractices")
	@Operation(desc = "设置项目的练习题库")
	public Object setDbPractices(HttpServletRequest request,
								 @RequestParam(required = false) String xmId,
								 @RequestParam(required = false) String professionId,
								 @RequestParam(required = false) TechLevel techLevel,
								 @RequestParam(required = false) String dbIds) throws Exception {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		if (StringUtils.isEmpty(dbIds)) {
			throw BizException.withMessage("题库id不能为空");
		}
		dbPracticeService.setDbPractices(xmId, professionId, techLevel, dbIds);
		return true;
	}

	@RequestMapping("/getStudentNotice")
	@Operation(desc = "获取项目的学员须知")
	public Object getStudentNotice(HttpServletRequest request, @RequestParam(required = false) String id)
			throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		return zypxXm;
	}

	@RequestMapping("/setStudentNotice")
	@Operation(desc = "设置项目的学员须知")
	public Object setStudentNotice(HttpServletRequest request, @RequestParam(required = false) String id,
			@RequestParam(required = false) String studentNotice) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("项目id不能为空");
		}
		ZypxXm zypxXm = service.selectById(id);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		zypxXm.setStudentNotice(studentNotice);
		zypxXm.setUpdatorId(super.getLoginUserId(request));
		zypxXm.setUpdateTime(new Date());
		service.updateById(zypxXm);
		return true;
	}

	@RequestMapping("/cockpit")
	@Operation(desc = "培训驾驶舱", loginRequired = false)
	public Object cockpit(HttpServletRequest request) throws Exception {
		return service.cockpit(super.getCurrentHostOrgId(request));
	}
}