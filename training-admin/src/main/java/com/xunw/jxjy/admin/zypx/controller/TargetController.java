package com.xunw.jxjy.admin.zypx.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.AnswerType;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zypx.entity.Target;
import com.xunw.jxjy.model.zypx.entity.TargetSetting;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import com.xunw.jxjy.model.zypx.params.TargetQueryParams;
import com.xunw.jxjy.model.zypx.params.TargetResultQueryParams;
import com.xunw.jxjy.model.zypx.params.TargetSettingQueryParams;
import com.xunw.jxjy.model.zypx.service.TargetResultService;
import com.xunw.jxjy.model.zypx.service.TargetService;
import com.xunw.jxjy.model.zypx.service.TargetSettingService;
import com.xunw.jxjy.model.zypx.service.TargetTypeService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.Date;

/**
 * <AUTHOR>
 * 职业培训-评分指标
 */
@RestController
@RequestMapping("/htgl/biz/target")
public class TargetController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TargetController.class);//记录打印日志用的

    @Autowired
    private TargetService service;
    @Autowired
    private TargetTypeService targetTypeService;
    @Autowired
    private TargetSettingService targetSettingService;
    @Autowired
    private TargetResultService targetResultService;

    /**
     * 评分指标列表
     */
    @RequestMapping("/list")
    @Operation(desc = "评分指标列表")
    public Object cx(TargetQueryParams params) throws Exception {
        return service.pageQuery(params);
    }

    /**
     * 设置指标
     */
    @RequestMapping("/set")
    @Operation(desc = "设置指标")
    public Object set(HttpServletRequest request, TargetSettingQueryParams params) {
        if (StringUtils.isBlank(params.getXmId())) {
            throw BizException.withMessage("请选择项目信息");
        }
        if (StringUtils.isBlank(params.getTargetIdList())) {
            throw BizException.withMessage("请选择设置的指标");
        }
        LoginUser loginUser = this.getLoginUser(request);
        return targetSettingService.targetSet(params, loginUser.getUser().getId());
    }

    /**
     * 获取项目评价指标
     */
    @RequestMapping("/getByXmId")
    @Operation(desc = "获取项目评价指标")
    public Object getByXmId(HttpServletRequest request, TargetSettingQueryParams params) throws Exception {
        if (StringUtils.isEmpty(params.getXmId())) {
            throw BizException.withMessage("请选择项目信息");
        }
        return targetSettingService.pageQuery(params);
    }

    /**
     * 获取项目评价指标评分
     */
    @RequestMapping("/getAvgTargetResult")
    @Operation(desc = "获取项目评价指标评分")
    public Object getAvgTargetResult(HttpServletRequest request, TargetSettingQueryParams params) throws Exception {
        if (StringUtils.isEmpty(params.getXmId())) {
            throw BizException.withMessage("请选择项目信息");
        }
        return targetSettingService.getAvgTargetResult(params.getXmId(), null);
    }

    /**
     * 获取项目综合排行榜
     */
    @RequestMapping("/getXmRank")
    @Operation(desc = "获取项目综合排行榜")
    public Object getXmRank(HttpServletRequest request) throws Exception {
        return targetSettingService.getXmRank(super.getCurrentHostOrgId(request));
    }

    /**
     * 获取课件排行榜
     */
    @RequestMapping("/getKjRank")
    @Operation(desc = "获取课件排行榜")
    public Object getKjRank(HttpServletRequest request) throws Exception {
        return targetSettingService.getKjRank(super.getCurrentHostOrgId(request));
    }

    /**
     * 获取老师排行榜
     */
    @RequestMapping("/getTeacherRank")
    @Operation(desc = "获取老师排行榜")
    public Object getTeacherRank(HttpServletRequest request) throws Exception {
        return targetSettingService.getTeacherRank(super.getCurrentHostOrgId(request));
    }

    /**
     * 项目评分指标评价
     */
    @RequestMapping("/setScore")
    @Operation(desc = "项目评分指标评价")
    public Object setScore(HttpServletRequest request, TargetResultQueryParams params) throws Exception {
        if (StringUtils.isEmpty(params.getXmId())) {
            throw BizException.withMessage("请选择项目信息");
        }
        if (StringUtils.isEmpty(params.getTargetId())) {
            throw BizException.withMessage("请选择项目指标信息");
        }
        if (params.getScore() == null) {
            throw BizException.withMessage("请选择评价分数");
        }
        return targetResultService.add(params, "123");
    }

    /**
     * 添加评分指标
     */
    @RequestMapping("/add")
    @Operation(desc = "添加评分指标")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String id,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String answerTypes,
                      @RequestParam(required = false) Integer maxScore) throws Exception {
        if (StringUtils.isBlank(id)) {
            throw BizException.withMessage("请选择评分指标分类");
        }
        TargetType check = targetTypeService.selectById(id);
        if (check == null) {
            throw BizException.withMessage("评分指标分类ID不存在:" + id);
        }
        if (StringUtils.isBlank(code)) {
            throw BizException.withMessage("请输入指标分类编号");
        }
        if (StringUtils.isBlank(name)) {
            throw BizException.withMessage("请输入指标分类名称");
        }
        if (answerTypes == null) {
            throw BizException.withMessage("请选择指标作答类型");
        }
        if ((answerTypes.contains(AnswerType.STAR.name()) || answerTypes.contains(AnswerType.SCORE.name())) && maxScore == null) {
            throw BizException.withMessage("请输入指标最大分值");
        }
        User sysJgyh = super.getLoginUser(request).getUser();
        Target target = new Target();
        target.setId(BaseUtil.generateId());
        target.setCode(code);
        target.setName(name);
        target.setTypeId(id);
        target.setCreateTime(new Date());
        target.setCreatorId(sysJgyh.getId());
        target.setAnswerType(answerTypes);
        target.setMaxScore(maxScore);
        service.insert(target);
        return true;
    }

    /**
     * 评分指标详情
     */
    @RequestMapping("/getById")
    @Operation(desc = "评分指标详情")
    public Object getById(
            @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        return service.selectById(id);
    }

    /**
     * 修改评分指标
     */
    @RequestMapping("/edit")
    @Operation(desc = "修改评分指标")
    public Object edit(@RequestParam(required = false) String id,
                       @RequestParam(required = false) String code,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) String answerTypes,
                       @RequestParam(required = false) Integer maxScore) throws Exception {
        if (StringUtils.isBlank(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        Target check = service.selectById(id);
        if (check == null) {
            throw BizException.withMessage("评分指标ID不存在:" + id);
        }
        if (StringUtils.isBlank(code)) {
            throw BizException.withMessage("请输入指标分类编号");
        }
        if (StringUtils.isBlank(name)) {
            throw BizException.withMessage("请输入指标分类名称");
        }
        if (answerTypes == null) {
            throw BizException.withMessage("请选择指标作答类型");
        }
        if ((answerTypes.contains(AnswerType.STAR.name()) || answerTypes.contains(AnswerType.SCORE.name())) && maxScore == null) {
            throw BizException.withMessage("请输入指标最大分值");
        }
        check.setCode(code);
        check.setName(name);
        check.setAnswerType(answerTypes);
        check.setMaxScore(maxScore);
        service.updateById(check);
        return true;
    }

    /**
     * 删除评分指标
     */
    @RequestMapping("/deleteById")
    @Operation(desc = "删除评分指标")
    public Object deleteById(
            @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        //todo 指标设置表有数据不能删除
        EntityWrapper<TargetSetting> wrapper = new EntityWrapper<>();
        wrapper.eq("target_id", id);
        Integer count = targetSettingService.selectCount(wrapper);
        if (count > 0) {
            throw BizException.withMessage("指标设置表有数据不能删除");
        }
        service.deleteById(id);
        return true;
    }

    /**
     * 获取课程评价记录
     */
    @RequestMapping("/getCourseTargetResult")
    @Operation(desc = "获取课程评价记录")
    public Object getCourseTargetResult(String xmId, String courseId, String keyword, Page page) {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("请选择项目信息");
        }
        return targetResultService.getCourseTargetResult(xmId, courseId, keyword, page);
    }

    /**
     * 获取课程评价学员详细记录
     */
    @RequestMapping("/getCourseTargetResultByStudent")
    @Operation(desc = "获取课程评价学员详细记录")
    public Object getCourseTargetResultByStudent(String xmId, String courseId, String studentId) {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        if (StringUtils.isEmpty(courseId)) {
            throw BizException.withMessage("课程id不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        return targetResultService.getCourseTargetResultByStudent(xmId, courseId, studentId);
    }

    /**
     * 导出课程评价学员详细记录
     */
    @RequestMapping("/exportCourseTargetResultByStudent")
    @Operation(desc = "导出课程评价学员详细记录")
    public void exportCourseTargetResultByStudent(HttpServletResponse response, String xmId, String courseId) {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        if (StringUtils.isEmpty(courseId)) {
            throw BizException.withMessage("必须选择一门课程");
        }
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student_course_target.xls");
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            targetResultService.exportCourseTargetResultByStudent(xmId, courseId, os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出失败:", e);
        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }
}