package com.xunw.jxjy.admin.inf.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.inf.entity.Base;
import com.xunw.jxjy.model.inf.params.InfBaseQueryParams;
import com.xunw.jxjy.model.inf.service.InfBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

@RestController
@RequestMapping("/htgl/inf/base")
public class InfBaseController extends BaseController {

    @Autowired
    private InfBaseService infBaseService;

    @RequestMapping("/list")
    @Operation(desc = "基地列表")
    public Object list(HttpServletRequest request, InfBaseQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return infBaseService.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "添加基地")
    public Object add(HttpServletRequest request, Base base) throws Exception {
        base.check();
        base.setCreatorId(super.getLoginUserId(request));
        base.setHostOrgId(super.getCurrentHostOrgId(request));
        base.setCreateTime(new Date());
        base.setId(BaseUtil.generateId());
        infBaseService.insert(base);
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "详情")
    public Object getById(HttpServletRequest request, String id) throws Exception {
        if (BaseUtil.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        return infBaseService.selectById(id);
    }

    @RequestMapping("/delete")
    @Operation(desc = "删除基地")
    public Object delete(HttpServletRequest request, String ids) throws Exception {
        if (BaseUtil.isEmpty(ids)) {
            throw BizException.withMessage("请选择一条数据");
        }
        infBaseService.deleteBatchIds(Arrays.asList(ids.split(",")));
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改基地")
    public Object edit(HttpServletRequest request, Base base) throws Exception {
        if (BaseUtil.isEmpty(base.getId())) {
            throw BizException.withMessage("基地id不能为空");
        }
        base.check();
        base.setUpdateTime(new Date());
        base.setUpdatorId(super.getLoginUserId(request));
        infBaseService.updateById(base);
        return true;
    }

    @RequestMapping("/select")
    @Operation(desc = "基地下拉列表")
    public Object select(HttpServletRequest request) throws Exception {
        return infBaseService.selectList((EntityWrapper<Base>) new EntityWrapper<Base>().eq("host_org_id", super.getCurrentHostOrgId(request)));
    }
}
