package com.xunw.jxjy.admin.zcps.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.enums.HostorgSettingEnum;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZyjdProfessionService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmTrial;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmQueryParams;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmTrialService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职业技能等级认定报名信息查询
 */

@RestController
@RequestMapping("/htgl/biz/zcps/bm")
public class ZcpsBmQueryController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ZcpsBmQueryController.class);

	@Autowired
	private ZyjdBmService service;
	@Autowired
	private ZyjdBmBatchService bmBatchService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private ZyjdProfessionService professionService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private ZyjdBmTrialService zyjdBmTrialService;

	/**
	 * 列表查询
	 */
	@RequestMapping("/list")
	@Operation(desc = "报名信息查询列表")
	public Object list(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setType(ZyjdBmBatchType.ZCPS);
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		Page page = service.pageQuery(params);
		if (CollectionUtils.isNotEmpty(page.getRecords())) {
			for (Object record : page.getRecords()) {
				Map<String, Object> recordMap = (Map<String, Object>) record;
				// 是否有初审权限
				if (Constants.YES.equals(BaseUtil.getStringValueFromMap(recordMap, "isOneTrial"))
						&& StringUtils.equals(BaseUtil.getStringValueFromMap(recordMap, "status"), ZyjdBmStatus.YTJ.name()) ) {
					List<ZyjdBmTrial> zyjdBmTrials = zyjdBmTrialService.selectList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>().eq("bmbatch_id", BaseUtil.getStringValueFromMap(recordMap, "bmbatchId")));
					List<String> approveUserIds = zyjdBmTrials.stream().map(ZyjdBmTrial::getUserId).collect(Collectors.toList());
					recordMap.put("isAllowApprove", approveUserIds.contains(super.getLoginUserId(request)) ? Constants.YES : Constants.NO);
				} else {
					recordMap.put("isAllowApprove", Constants.YES);
				}
			}
		}
		return page;
	}

	/**
	 * 获取报名详情
	 */
	@RequestMapping("/getDetailsById")
	@Operation(desc = "获取报名详情")
	public Object getDetailsById(@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		return service.getDetailsById(id);
	}

	/**
	 * 报名信息导出
	 */
	@RequestMapping("/export")
	@Operation(desc = "导出信息表")
	public void export(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=zcps_student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			params.setType(ZyjdBmBatchType.ZCPS);
			service.exportZcpsBmList(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 删除报名信息
	 */
	@RequestMapping("/deleteById")
	@Operation(desc = "删除报名信息")
	public Object deleteById(@RequestParam(required = false) String ids) {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("缺少注册ID");
		}
		String[] arrayIds = StringUtils.split(ids, ",");
		List<String> idList = new ArrayList<String>();
		for (String id : arrayIds) {
			idList.add(id);
		}
		service.batchDel(idList);
		return true;
	}

	/**
	 * 导出学员的登记照
	 */
	@RequestMapping("/expStudentPhoto")
	@Operation(desc = "导出学员的登记照")
	public void expStudentPhoto(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		String filename = "studentPhoto.zip";
		response.setHeader("content-disposition", "attachment;filename=" + filename);
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.expStudentPhoto(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("error", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出全部附件
	 */
	@RequestMapping("/expAccessory")
	@Operation(desc = "导出资料")
	public void expAccessory(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		String filename = "Accessory.zip";
		response.setHeader("content-disposition", "attachment;filename=" + filename);
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			params.setSize(Integer.MAX_VALUE);
			service.expAccessory(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("error", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 标记报名状态
	 */
	@RequestMapping("/markBmStatus")
	@Operation(desc = "标记报名状态")
	public Object markBmStatus(HttpServletRequest request, @RequestParam(required = false) String ids,
			@RequestParam(required = false) ZyjdBmStatus status) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择数据");
		}
		if (status == null) {
			throw BizException.withMessage("请选择报名状态");
		}
		service.markBmStatus(ids, status,null);
		return true;
	}
	
	/**
	 * 审核操作
	 */
	@RequestMapping("/docheck")
    @Operation(desc = "审核操作")
    public Object shenhe(
           HttpServletRequest request,
           @RequestParam(required = false) String id,
           @RequestParam(required = false) String action,
           @RequestParam(required = false) Boolean payed,//标记已缴费
           @RequestParam(required = false) String notice
    ) throws Exception {
    	if(StringUtils.isEmpty(action)) {
    		throw BizException.withMessage("请选择是否通过");
    	}
    	ZyjdBm zyjdBm = service.selectById(id);
    	ZyjdBmBatch zyjdBmBatch = bmBatchService.selectById(zyjdBm.getBmbatchId());
    	if(Constants.YES.equals(action)) {
    		zyjdBm.setStatus(ZyjdBmStatus.SHTG);
    	}
    	else {
    		zyjdBm.setStatus(ZyjdBmStatus.BH);
    	}
    	zyjdBm.setApproveUserId(super.getLoginUserId(request));
    	zyjdBm.setApproveTime(new Date());
    	zyjdBm.setApproveAdvice(notice);
    	if(payed !=null && payed) {
    		zyjdBm.setPayStatus(PayStatus.YJ);
    		zyjdBm.setPayType(Constants.PayType.OFF_LINE);
    		zyjdBm.setMarkPaydUserId(super.getLoginUserId(request));
    	}
    	
    	String sendResultBySmsForSkillBm = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.SEND_RESULT_BY_SMS_FOR_SKILL_BM, super.getCurrentHostOrgId(request));
    	sendResultBySmsForSkillBm = StringUtils.isEmpty(sendResultBySmsForSkillBm) ? Constants.YES : sendResultBySmsForSkillBm;
    	
    	if(ZyjdBmBatchType.ZYJD == zyjdBmBatch.getType() && StringUtils.isEmpty(zyjdBm.getRecommendCode())) {
        	try {
        		Org org = super.getCurrentHostOrg(request);
    			StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
    			if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
                    ZyjdProfession zyjdProfession = professionService.selectById(zyjdBm.getProfessionId());
    				smsSevice.sendByTemplate(org.getSmsSign(), "228800", studentInfo.getMobile(),new String[] {studentInfo.getName(),zyjdProfession.getName()});
    			}
    			if(Constants.NO.equals(action)) {
    				smsSevice.sendByTemplate(org.getSmsSign(), "228801", studentInfo.getMobile(),new String[] {studentInfo.getName()});
    			}
    		} catch (Exception e) {
    			LOGGER.error("短信发送失败：",e);
    		}
    	}
    	else if (ZyjdBmBatchType.TDDZCL == zyjdBmBatch.getType()) {
    		try {
        		Org org = super.getCurrentHostOrg(request);
    			StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
    			if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
    				smsSevice.sendByTemplate(org.getSmsSign(), "234401", studentInfo.getMobile(),new String[] { studentInfo.getName()});
    			}
    		} catch (Exception e) {
    			LOGGER.error("短信发送失败：",e);
    		}
		}
    	else if(ZyjdBmBatchType.QDY == zyjdBmBatch.getType() && StringUtils.isEmpty(zyjdBm.getRecommendCode())){
            try {
                Org org = super.getCurrentHostOrg(request);
                StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
                if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
                    smsSevice.sendByTemplate(org.getSmsSign(), "229930", studentInfo.getMobile(),new String[] {studentInfo.getName()});
                }
                if(Constants.NO.equals(action)) {
                    smsSevice.sendByTemplate(org.getSmsSign(), "229929", studentInfo.getMobile(),new String[] {studentInfo.getName()});
                }
            } catch (Exception e) {
                LOGGER.error("短信发送失败：",e);
            }
        }
    	service.updateById(zyjdBm);
    	return true;
    }
	
	/**
	 * 统计列表
	 */
	@RequestMapping("/analysis")
	@Operation(desc = "统计列表")
	public Object analysis(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		return service.analysis(params);
	}
	
	/**
	 * 获取批次的报名数据统计详情
	 */
	@RequestMapping("/getAnalysisInfo")
	@Operation(desc = "获取批次的报名数据统计详情")
	public Object getAnalysisInfo(HttpServletRequest request,
					 ZyjdBmQueryParams params) throws Exception{
		return service.getAnalysisInfo(params);
	}
	
	/**
	 * 区分职业报名数据统计
	 */
	@RequestMapping("/statistcCount")
	@Operation(desc = "职业报名统计查询")
	public Object statistcCount(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return service.statistcCount(params);
	}


}
