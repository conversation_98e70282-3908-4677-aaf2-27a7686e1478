package com.xunw.jxjy.admin.comm.controller;

import java.util.Date;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.common.entity.CommClass;
import com.xunw.jxjy.model.common.params.CommClassQueryParams;
import com.xunw.jxjy.model.common.service.CommClassService;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;

/**
 * 班级管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/comm/class")
public class CommClassController extends BaseController {

    @Autowired
    private CommClassService service;
    @Autowired
    private ZypxBmService bmService;

    @RequestMapping("/list")
    @Operation(desc = "班级列表")
    public Object list(HttpServletRequest request, CommClassQueryParams params) throws Exception {
        LoginUser loginUser = super.getLoginUser(request);
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        if (loginUser.getRole() == Role.HEADERMASTER) {
            params.setHeadermasterId(loginUser.getUser().getId());
        }
        return service.pageQuery(params);
    }

    //添加
    @RequestMapping("/add")
    @Operation(desc = "添加班级")
    public Object add(
            HttpServletRequest request,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer limitCount,
            @RequestParam(required = false) String headermasterId,
            @RequestParam(required = false) String xmId,
            @RequestParam(required = false) String groupWork,
            @RequestParam(required = false) Integer sortNo
    ) throws Exception {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("班级名称不能为空");
        }
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("请选择项目");
        }
        CommClass commClass = new CommClass();
        commClass.setId(BaseUtil.generateId2());
        commClass.setName(name);
        commClass.setHostOrgId(getCurrentHostOrgId(request));
        commClass.setLimitCount(limitCount);
        commClass.setHeadermasterId(headermasterId);
        commClass.setCreatorId(super.getLoginUserId(request));
        commClass.setCreateTime(new Date());
        commClass.setXmId(xmId);
        commClass.setGroupWork(groupWork);
        commClass.setSortNo(sortNo);
        service.insert(commClass);
        return true;
    }

    //编辑班级信息
    @RequestMapping("/edit")
    @Operation(desc = "编辑班级信息")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer limitCount,
            @RequestParam(required = false) String headermasterId,
            @RequestParam(required = false) String xmId,
            @RequestParam(required = false) String groupWork,
            @RequestParam(required = false) Integer sortNo
    ) throws Exception {
        LoginUser loginUser = super.getLoginUser(request);
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("班级ID不能为空");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("班级名称不能为空");
        }
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("请选择项目");
        }
        CommClass commClass = service.selectById(id);
        if (commClass == null) {
            throw BizException.withMessage("班级不存在");
        }
        commClass.setName(name);
        commClass.setLimitCount(limitCount);
        commClass.setHeadermasterId(headermasterId);
        commClass.setXmId(xmId);
        commClass.setGroupWork(groupWork);
        commClass.setSortNo(sortNo);
        service.updateById(commClass);
        return true;
    }

    //班级详情
    @RequestMapping("/getById")
    @Operation(desc = "获取班级")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("班级ID不能为空");
        }
        return service.selectById(id);
    }

    //删除班级
    @RequestMapping("/deleteById")
    @Operation(desc = "删除班级")
    public Object deleteById(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("班级ID不能为空");
        }
        CommClass commClass = service.selectById(id);
        if (commClass == null) {
            throw BizException.withMessage("班级不存在");
        }
        EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
        wrapper.eq("class_id", id);
        if (bmService.selectCount(wrapper) > 0) {
            throw BizException.withMessage("该班级下存在学员，不能删除");
        }
        service.deleteById(id);
        return true;
    }

    //班级下拉选择
    @RequestMapping("/select")
    @Operation(desc = "班级下拉选择框")
    public Object select(
            HttpServletRequest request) throws Exception {
        LoginUser loginUser = super.getLoginUser(request);
        CommClassQueryParams params = new CommClassQueryParams();
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        if (loginUser.getRole() == Role.HEADERMASTER) {
            params.setHeadermasterId(loginUser.getUser().getId());
        }
        return service.pageQuery(params).getRecords();
    }

    @RequestMapping("/courseStudentSelect")
    @Operation(desc = "班级学生下拉选择框")
    public Object courseStudentSelect(
            HttpServletRequest request, @RequestParam String classId) throws Exception {
        return service.courseStudentSelect(classId);
    }


    //添加
    @RequestMapping("/addClassGroupLeader")
    @Operation(desc = "添加班级组长")
    public Object addClassGroupLeader(
            HttpServletRequest request,
            @RequestParam String id,
            @RequestParam String studentId
    ) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择班级");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("请选择学生");
        }
        CommClass dataBase = service.selectById(id);
        if (dataBase == null) {
            throw BizException.withMessage("班级不存在");
        }
        CommClass commClass = new CommClass();
        commClass.setId(id);
        commClass.setStudentId(studentId);
        service.updateById(commClass);
        return true;
    }

}
