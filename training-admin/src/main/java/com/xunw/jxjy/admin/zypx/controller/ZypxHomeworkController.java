package com.xunw.jxjy.admin.zypx.controller;

import com.alibaba.fastjson.JSONObject;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zypx.params.ZypxExamPaperQueryParams;
import com.xunw.jxjy.model.zypx.service.PlanService;
import com.xunw.jxjy.model.zypx.service.ZypxExamPaperService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;


/**
 * 职业培训-试卷管理-平时作业
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/zypx/paper/homework")
public class ZypxHomeworkController extends BaseController {

    @Autowired
    private ZypxExamPaperService service;
    @Autowired
    private ZypxXmService xmService;

    //平时作业列表
    @RequestMapping("/list")
    @Operation(desc = "平时作业列表")
    public Object list(HttpServletRequest request,
    					ZypxExamPaperQueryParams params) throws Exception {
    	//若不指定培训项目  则根据主办单位查询培训项目
    	if (StringUtils.isEmpty(params.getXmId())) {
    		params.setHostOrgId(super.getCurrentHostOrgId(request));
    	}
    	if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
    	params.setCategory(PaperCategory.PSZY);
        return service.pageQuery(params);
    }

    //新增
    @RequestMapping("/add")
    @Operation(desc = "新增平时作业")
    public Object add(
    					HttpServletRequest request,
    					@RequestParam(required = false) String name,
    					@RequestParam(required = false) String xmId,
    					@RequestParam(required = false) String courseId,
    					@RequestParam(required = false) Sjzt status,
    					@RequestParam(required = false) String startTime,
    					@RequestParam(required = false) String endTime,
    					@RequestParam(required = false) String isShowAnswer,
    					@RequestParam(required = false) Sjxslx paperShowType,
    					@RequestParam(required = false) Sjlx paperType,
    					@RequestParam(required = false) Stplsx quesSortType,
    					@RequestParam(required = false) String relationId,
    					@RequestParam(required = false) String remark) throws Exception {
	    if (StringUtils.isEmpty(name)) {
	    	throw BizException.withMessage("请输入试卷名称");
        }
        if (StringUtils.isEmpty(xmId)) {
        	throw BizException.withMessage("请选择培训项目");
        }
        if (status == null) {
        	throw BizException.withMessage("请选择试卷状态");
        }
        if (StringUtils.isEmpty(startTime)) {
        	throw BizException.withMessage("请选择开考时间");
        }
        if (StringUtils.isEmpty(endTime)) {
        	throw BizException.withMessage("请选择结束时间");
        }
		if (DateUtils.parse(startTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(endTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("结束时间不能早于开始时间");
		}
        if (StringUtils.isEmpty(isShowAnswer)) {
	    	throw BizException.withMessage("请选择是否显示答案");
        }
        if (paperShowType == null) {
	    	throw BizException.withMessage("请选择试卷显示类型");
        }
        if (paperType == null) {
	    	throw BizException.withMessage("请选择试卷类型");
        }
        if (quesSortType == null) {
	    	throw BizException.withMessage("请选择试题排列顺序");
        }
        boolean isSkill = xmService.isSkill(xmId);
        if(isSkill && StringUtils.isEmpty(relationId)) {
	    	throw BizException.withMessage("请选择职业及等级");
        }
    	String id = BaseUtil.generateId();
    	ExamPaper examPaper = new ExamPaper();
    	examPaper.setId(id);
    	examPaper.setName(name);
    	examPaper.setXmId(xmId);
    	examPaper.setCourseId(courseId);
    	examPaper.setCategory(PaperCategory.PSZY);
    	examPaper.setStatus(status);
    	examPaper.setStartTime(DateUtils.parse(startTime,"yyyy-MM-dd HH:mm"));
    	examPaper.setEndTime(DateUtils.parse(endTime,"yyyy-MM-dd HH:mm"));
    	examPaper.setIsShowAnswer(isShowAnswer);
    	examPaper.setPaperShowType(paperShowType);
    	examPaper.setPaperType(paperType);
    	examPaper.setQuesSortType(quesSortType);
    	examPaper.setExamModel(ExamModel.SDSK);
        User user = super.getLoginUser(request).getUser();
        Date createTime = new Date();
        examPaper.setCreatorId(user.getId());
        examPaper.setCreateTime(createTime);
        examPaper.setOrgId(user.getOrgId());
        examPaper.setRemark(remark);
        if (isSkill) {
        	String[] relations = StringUtils.split(relationId,"|");
        	examPaper.setProfessionId(relations[0]);
        	examPaper.setTechLevel(TechLevel.valueOf(relations[1]));
		}
        service.insert(examPaper);
        return true;
    }

    //平时作业批量设置属性
    @RequestMapping("/setProperty")
    @Operation(desc = "平时作业批量设置属性")
    public Object setProperty(HttpServletRequest request,
    		@RequestParam(required = false) String ids,
			@RequestParam(required = false) String startTime,
			@RequestParam(required = false) String endTime,
			@RequestParam(required = false) Sjzt status,
			@RequestParam(required = false) Stplsx quesSortType,
			@RequestParam(required = false) String scoreTime,
			@RequestParam(required = false) Integer duration,
			@RequestParam(required = false) String isShowAnswer,
			@RequestParam(required = false) String isAllowMobile) throws Exception {
    	if (StringUtils.isEmpty(ids)) {
	    	throw BizException.withMessage("请至少选择一条数据");
        }
    	service.setProperty(ids, startTime, endTime, status, quesSortType, null,
    			scoreTime, duration, isShowAnswer, isAllowMobile, false, super.getLoginUserId(request));
        return true;
    }

    //删除
    @RequestMapping("/deleteById")
    @Operation(desc = "删除平时作业")
    public Object deleteById(
    					@RequestParam(required = false) String id) throws Exception {
    	if (StringUtils.isEmpty(id)) {
	    	throw BizException.withMessage("请选择一条数据");
        }
    	service.deletePaperById(id);
    	return true;
    }

    //获取
    @RequestMapping("/getById")
    @Operation(desc = "获取平时作业")
    public Object getById(
    					@RequestParam(required = false) String id) throws Exception {
    	if (StringUtils.isEmpty(id)) {
	    	throw BizException.withMessage("请选择一条数据");
        }
    	ExamPaper examPaper = service.selectById(id);
    	examPaper.setData(null);
    	return examPaper;
    }

	//获取
	@RequestMapping("/getTypeCountById")
	@Operation(desc = "获取平时作业每个分类下的试题数量")
	public Object getTypeCountById(@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		ExamPaper examPaper = service.selectById(id);
		if (examPaper == null) {
			throw BizException.withMessage("试卷不存在");
		}
		return service.getTypeCountById(examPaper);
	}

    //修改
    @RequestMapping("/edit")
    @Operation(desc = "修改平时作业")
    public Object edit(
    					HttpServletRequest request,
    					@RequestParam(required = false) String id,
    					@RequestParam(required = false) String name,
    					@RequestParam(required = false) String xmId,
    					@RequestParam(required = false) String courseId,
    					@RequestParam(required = false) Sjzt status,
    					@RequestParam(required = false) String startTime,
    					@RequestParam(required = false) String endTime,
    					@RequestParam(required = false) Sjxslx paperShowType,
    					@RequestParam(required = false) String isShowAnswer,
    					@RequestParam(required = false) Sjlx paperType,
    					@RequestParam(required = false) Stplsx quesSortType,
    					@RequestParam(required = false) String remark,
    					@RequestParam(required = false) String scoreTime,
    					@RequestParam(required = false) String relationId,
    					@RequestParam(required = false) Integer duration) throws Exception {
    	if (StringUtils.isEmpty(id)) {
	    	throw BizException.withMessage("请选择一条数据");
        }
    	if (StringUtils.isEmpty(name)) {
	    	throw BizException.withMessage("请输入试卷名称");
        }
        if (StringUtils.isEmpty(xmId)) {
        	throw BizException.withMessage("请选择批次");
        }
        if (status == null) {
        	throw BizException.withMessage("请选择试卷状态");
        }
        if (StringUtils.isEmpty(startTime)) {
        	throw BizException.withMessage("请选择开考时间");
        }
        if (StringUtils.isEmpty(endTime)) {
        	throw BizException.withMessage("请选择结束时间");
        }
        if (DateUtils.parse(startTime,"yyyy-MM-dd HH:mm").after(DateUtils.parse(endTime,"yyyy-MM-dd HH:mm"))){
			throw BizException.withMessage("结束时间不能早于开始时间");
		}
        if (StringUtils.isEmpty(isShowAnswer)) {
	    	throw BizException.withMessage("请选择是否显示答案");
        }
        if (paperShowType == null) {
	    	throw BizException.withMessage("请选择试卷显示类型");
        }
        if (paperType == null) {
	    	throw BizException.withMessage("请选择试卷类型");
        }
        if (quesSortType == null) {
	    	throw BizException.withMessage("请选择试题排列顺序");
        }
        boolean isSkill = xmService.isSkill(xmId);
        if(isSkill && StringUtils.isEmpty(relationId)) {
	    	throw BizException.withMessage("请选择职业及等级");
        }
        ExamPaper examPaper = new ExamPaper(id,xmId, courseId, PaperCategory.PSZY, status,
        		DateUtils.parse(startTime,"yyyy-MM-dd HH:mm"), DateUtils.parse(endTime,"yyyy-MM-dd HH:mm"), null, null, name, quesSortType, paperType, remark, isShowAnswer, paperShowType);
        if (isSkill) {
        	String[] relations = StringUtils.split(relationId,"|");
        	examPaper.setProfessionId(relations[0]);
        	examPaper.setTechLevel(TechLevel.valueOf(relations[1]));
		}
        else {
        	examPaper.setProfessionId(null);
        	examPaper.setTechLevel(null);
        }
        Date xgsj = new Date();
        examPaper.setUpdatorId(super.getLoginUserId(request));
        examPaper.setUpdateTime(xgsj);
        examPaper.setExamModel(ExamModel.SDSK);
        return service.edit(examPaper);
    }

    //批量克隆试卷
    @RequestMapping("/batchClone")
    @Operation(desc = "批量克隆平时作业")
    public Object batchClonePaper(
    					@RequestParam(required = false) String ids,
    					@RequestParam(required = false) String relationId,
    					@RequestParam(required = false) String xmId) throws Exception {
    	if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择一条数据");
		}
    	if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择一个项目");
		}
    	String[] array = StringUtils.isNotEmpty(relationId) ? StringUtils.split(relationId, "|") : null;
    	String professionId = array != null && array.length > 1 ? array[0] : null;
    	TechLevel techLevel = array != null && array.length > 1 ? TechLevel.valueOf(array[1]) : null;
    	service.batchClonePaper(ids, xmId, professionId, techLevel);
        return true;
    }

    //配置
    @RequestMapping("/config")
    @Operation(desc = "配置平时作业")
    public Object config(
			    	HttpServletRequest request,
					@RequestBody(required = false) JSONObject json) throws Exception {
    	json.put("updatorId", super.getLoginUserId(request));
        return service.config(json);
    }

    //详情
    @RequestMapping("/getDetailsById")
    @Operation(desc = "平时作业详情")
    public Object getDetailsById(
    					@RequestParam(required = false) String paperId) throws Exception {
    	if (StringUtils.isEmpty(paperId)) {
			throw BizException.withMessage("请选择一条数据");
		}
    	return service.getDetailsById(paperId);
    }

    //查看
    @RequestMapping("/view")
    @Operation(desc = "查看平时作业")
    public Object view(
    					@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		return service.viewPaper(id);
    }

    //单条导出word
    @RequestMapping("/exportWord")
    @Operation(desc = "导出word")
    public void exportWord(
    					HttpServletResponse response,
    					@RequestParam(required = false) String id) throws Exception {
    	OutputStream os = null;
    	try {
			response.setContentType("text/html;charset=utf-8");
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition", "attachment;filename=paper_" + BaseUtil.generateRandomString(10) + ".doc");
			os = response.getOutputStream();
			service.exportWord(id, os);
			os.flush();
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
    }

	//单条导出word
	@RequestMapping("/exportWordA3")
	@Operation(desc = "导出word")
	public void exportWordA3(
			HttpServletResponse response,
			@RequestParam(required = false) String id) throws Exception {
		OutputStream os = response.getOutputStream();

			response.setContentType("text/html;charset=utf-8");
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition", "attachment;filename=paper_" + BaseUtil.generateRandomString(10) + ".docx");
			File docx = service.exportWordA3(id, os);

		try ( InputStream is = new FileInputStream(docx)) {
			IOUtils.copy(is, os);
			os.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			FileHelper.delFile(docx);
		}


	}

	//单条导出答题卡
	@RequestMapping("/exportAnswerSheet")
	@Operation(desc = "导出答题卡")
	public void exportAnswerSheet(
			HttpServletResponse response,
			@RequestParam(required = false) String id) throws Exception {
		OutputStream os = response.getOutputStream();

		response.setContentType("text/html;charset=utf-8");
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=answer_sheet_" + BaseUtil.generateRandomString(10) + ".docx");
		File docx = service.exportAnswerSheet(id, os);

		try ( InputStream is = new FileInputStream(docx)) {
			IOUtils.copy(is, os);
			os.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			FileHelper.delFile(docx);
		}

	}
	/**
	 * @Description  抽题模式组卷
	 * <AUTHOR>
	 */
	@RequestMapping("/configByChooseQues")
	@Operation(desc = "抽题模式组卷")
	public Object configByChooseQues(
			HttpServletRequest request,
			@RequestBody(required = false) JSONObject json) throws Exception {
		json.put("updatorId", super.getLoginUserId(request));
		service.configByChooseQues(json);
		return true;
	}
}
