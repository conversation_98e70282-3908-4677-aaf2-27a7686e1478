package com.xunw.jxjy.admin.comm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.config.TencentTUIRoomKitConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.GroupStatus;
import com.xunw.jxjy.model.zyjd.entity.ZcGroup;
import com.xunw.jxjy.model.zyjd.service.TxyService;
import com.xunw.jxjy.model.zyjd.service.ZcGroupService;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;


@RestController
@RequestMapping("/htgl/comm/tencent")
public class TencentNotifyController extends BaseController {

    private final static Logger logger = Logger.getLogger(TencentNotifyController.class);

    @Autowired
    private TxyService txyService;
    @Autowired
    private ZcGroupService zcGroupService;
    @Autowired
    private TencentTUIRoomKitConfig tencentTUIRoomKitConfig;

    /**
     * 录制完毕回调
     */
    @RequestMapping(value = "/notify", method = RequestMethod.POST)
    @Operation(desc = "录制回调", loginRequired = false)
    public void notify(HttpServletRequest request) throws Exception {
        String key = tencentTUIRoomKitConfig.getTxySDKSecretKey();
        String sign = request.getHeader("Sign");
        String param = getBodyString(request.getInputStream());
        param = param.substring(0, param.length() - 1);
        String resultSign = this.getResultSign(key, param);
        //校验签名
//        if(!resultSign.equals(sign)){
//            throw new BizQuietException("签名验证失败！");
//        }
        JSONObject boby = JSONObject.parseObject(param);
        //代表录制完毕
        int EventType = BaseUtil.getInt(boby.get("EventType"));
        if (310 == EventType) {
            JSONObject eventInfo = boby.getJSONObject("EventInfo");
            String roomId = eventInfo.getString("RoomId");
            JSONObject payload = eventInfo.getJSONObject("Payload");
            if (0 != BaseUtil.getInt(payload.get("Status"))) { //0：代表本录制文件正常上传至点播平台 1：代表本录制文件滞留在服务器或者备份存储上  2：代表本录制文件上传点播任务异常
                throw new BizException(payload.getString("Errmsg"));
            }
            JSONArray fileList = payload.getJSONArray("FileList");
            String videoUrl = null; //视频url
            if (fileList != null && fileList.size() > 0) {
                videoUrl = tencentTUIRoomKitConfig.getTxyPlayback_url() + "/" + eventInfo.get("TaskId") + "/" + fileList.get(0);
            }
            //更新视频url
            zcGroupService.updatePlayUrl(videoUrl, roomId);
        }
    }

    /**
     * 进入房间和解散房间回调
     */
    @RequestMapping(value = "/roomNotify", method = RequestMethod.POST)
    @Operation(desc = "进入房间和解散房间回调", loginRequired = false)
    public void roomNotify(HttpServletRequest request) throws Exception {
        String key = tencentTUIRoomKitConfig.getTxySDKSecretKey();
        String sign = request.getHeader("Sign");
        String param = getBodyString(request.getInputStream());
        param = param.substring(0, param.length() - 1);
        String resultSign = this.getResultSign(key, param);
        //校验签名
//        if(!resultSign.equals(sign)){
//            throw new BizQuietException("签名验证失败！");
//        }
        JSONObject boby = JSONObject.parseObject(param);
        int eventType = BaseUtil.getInt(boby.getString("EventType"));
        JSONObject eventInfo = boby.getJSONObject("EventInfo");
        //103 进入房间  102 解散房间
        if (103 == eventType) {
            logger.info(".....进入到腾讯云进入房间和解散房间回调：sign=" + sign + ";boby=" + boby + ";key=" + key + ";eventType=" + eventType);
            String roomId = eventInfo.getString("RoomId");
            //更新答辩状态
            zcGroupService.updateGroupStatus(roomId, GroupStatus.INPROGRESS);
            //进来判断是否开启录制，未开启则进行开启
            ZcGroup group = zcGroupService.getGroupByRoomId(roomId);
            if (group != null && BaseUtil.isEmpty(group.getTaskId())) {
                txyService.couldRoom(roomId);
            }
        } else if (102 == eventType) {
            logger.info(".....进入到腾讯云进入房间和解散房间回调：sign=" + sign + ";boby=" + boby + ";key=" + key + ";eventType=" + eventType);
            String roomId = eventInfo.getString("RoomId");
            //更新答辩状态
            zcGroupService.updateGroupStatus(roomId, GroupStatus.FINISHED);
        }
    }


    public String getBodyString(ServletInputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = "";
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\n");
            }
        } catch (IOException e) {
            logger.warn("getBodyString出现问题！");
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                    inputStream.close();
                } catch (IOException e) {
                    logger.error(ExceptionUtils.getMessage(e));
                }
            }
        }
        return sb.toString();
    }

    public String getResultSign(String key, String body) throws Exception {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(), "HmacSHA256");
        hmacSha256.init(secret_key);
        return org.apache.commons.codec.binary.Base64.encodeBase64String(hmacSha256.doFinal(body.getBytes()));
    }

}
