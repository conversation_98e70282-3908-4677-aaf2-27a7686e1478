package com.xunw.jxjy.admin.zyjd.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.AuditMode;
import com.xunw.jxjy.model.zyjd.entity.BizBmAuditLog;
import com.xunw.jxjy.model.zyjd.service.BizBmAuditLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.enums.BmOpenStatus;
import com.xunw.jxjy.model.enums.HostorgSettingEnum;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.inf.service.ZyjdProfessionService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmTrial;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmQueryParams;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmBatchService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmService;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmTrialService;

/**
 * 职业技能等级认定报名信息查询
 */

@RestController
@RequestMapping("/htgl/biz/zyjd/bm")
public class ZyjdBmQueryController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(ZyjdBmQueryController.class);

	@Autowired
	private ZyjdBmService service;
	@Autowired
	private ZyjdBmBatchService bmBatchService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private ZyjdProfessionService professionService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private SystemSettingService systemSettingService;
    @Autowired
    private ZyjdBmTrialService zyjdBmTrialService;
    @Autowired
	private BizBmAuditLogService bizBmAuditLogService;

	/**
	 * 列表查询
	 */
	@RequestMapping("/list")
	@Operation(desc = "报名信息查询列表")
	public Object list(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setCurrentRole(super.getLoginRole(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		return service.pageQuery(params);
	}

	/**
	 * 获取报名详情
	 */
	@RequestMapping("/getDetailsById")
	@Operation(desc = "获取报名详情")
	public Object getDetailsById(@RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("请选择一条数据");
		}
		return service.getDetailsById(id);
	}

	/**
	 * 报名信息导出
	 */
	@RequestMapping("/export")
	@Operation(desc = "导出信息表")
	public void export(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportBmList(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出七大员报名信息
	 */
	@RequestMapping("/exportQdy")
	@Operation(desc = "导出七大员信息表")
	public void exportQdy(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportQdy(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出专业技术人员报名信息
	 */
	@RequestMapping("/exportZyjs")
	@Operation(desc = "导出专业技术人员报名信息")
	public void exportZyjs(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params) throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportZyjs(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出省鉴定中心需要的报名数据
	 */
	@RequestMapping("/exportStandardData")
	@Operation(desc = "导出省鉴定中心的上报数据")
	public void exportStandardData(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportStandard(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 删除报名信息
	 */
	@RequestMapping("/deleteById")
	@Operation(desc = "删除报名信息")
	public Object deleteById(@RequestParam(required = false) String ids) {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("缺少注册ID");
		}
		String[] arrayIds = StringUtils.split(ids, ",");
		List<String> idList = new ArrayList<String>();
		for (String id : arrayIds) {
			idList.add(id);
		}
		service.batchDel(idList);
		return true;
	}

	@RequestMapping("/markPay")
	@Operation(desc = "批量标记缴费")
	public Object markPayed(HttpServletRequest request,
							@RequestParam(required = false) String ids,
							@RequestParam(required = false) PayStatus status,
							@RequestParam(required = false) Double amount,
							@RequestParam(required = false) String remark) {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("缺少报名ID");
		}
		if (status == null) {
			throw BizException.withMessage("状态不能为空");
		}
		if (status == PayStatus.YJ && amount == null) {
			throw BizException.withMessage("请输入缴费金额");
		}
		return service.markPayed(ids, amount, status, super.getLoginUserId(request), remark);
	}

	/**
	 * 标记退费
	 */
	@RequestMapping("/refund")
	@Operation(desc = "标记退费")
	public Object refund(
			HttpServletRequest request,
			@RequestParam(required = false) String ids,
			@RequestParam(required = false) String remark
	) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请勾选报名信息");
		}
		service.refund(ids, remark, getLoginUserId(request));
		return true;
	}

	/**
	 * 根据报名id获取订单信息
	 */
	@RequestMapping("/getOrderByBmId")
	@Operation(desc = "根据报名id获取订单信息")
	public Object getOrderByBmId(@RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("报名id不能为空");
		}
		return service.getOrderByBmId(id);
	}


	/**
	 * 导出学员的登记照
	 */
	@RequestMapping("/expStudentPhoto")
	@Operation(desc = "导出学员的登记照")
	public void expStudentPhoto(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		String filename = "studentPhoto.zip";
		response.setHeader("content-disposition", "attachment;filename=" + filename);
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.expStudentPhoto(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("error", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出全部附件
	 */
	@RequestMapping("/expAccessory")
	@Operation(desc = "导出资料")
	public void expAccessory(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params)
			throws Exception {
		response.setContentType("application/octet-stream");
		String filename = "Accessory.zip";
		response.setHeader("content-disposition", "attachment;filename=" + filename);
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			params.setSize(Integer.MAX_VALUE);
			service.expAccessory(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("error", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 土地地质测量-批量生成准考证号
	 */
	@RequestMapping("/batchCreateZkz")
	@Operation(desc = "批量生成准考证号")
	public Object batchCreateZkz(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		if (StringUtils.isEmpty(params.getBmbatchId())) {
			throw BizException.withMessage("请选择批次ID");
		}
		params.setStatus(ZyjdBmStatus.SHTG);
		params.setPayStatus(PayStatus.YJ);
		params.setSize(Integer.MAX_VALUE);
		params.setZkzIsNull(Constants.YES);
		Page<Map<String, Object>> pageInfo = service.pageQuery(params);
		List<String> idList = new ArrayList<String>();
		if (pageInfo.getRecords().size() == 0) {
			return 0;
		}
		for (Map<String, Object> map : pageInfo.getRecords()) {
			idList.add(map.get("id").toString());
		}
		service.batchCteateZkz(idList, params.getBmbatchId());
		return idList.size();
	}

	/**
	 * 导入考场信息并批量生成准考证号
	 * @param file 文件
	 * @param bmBatchId 批次id
	 */
	@RequestMapping("/importExaminationRoom")
	@Operation(desc = "导入考场信息并批量生成准考证号")
	public Object importExaminationRoom(
			HttpServletRequest request,
			@RequestParam(value = "file",required = false) MultipartFile file,
			@RequestParam(required = false) String bmBatchId) throws Exception {
		if (StringUtils.isEmpty(bmBatchId)) {
			throw BizException.withMessage("请选择批次ID");
		}
		return service.importExaminationRoom(file, bmBatchId,super.getCurrentHostOrgId(request));
	}

	/**
	 * 土地地质测量管理-报名信息查询-导出
	 */
	@RequestMapping("/tddc")
	@Operation(desc = "导出信息表")
	public void tddcdc(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String zcIds, ZyjdBmQueryParams params) throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=registration_summary.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportBmhz(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 林业报名数据导出
	 */
	@RequestMapping("/forestBmInfoExport")
	@Operation(desc = "导出林业报名数据")
	public void forestBmExport(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(required = false) String zcIds, ZyjdBmQueryParams params) throws Exception {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=registration_summary.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			if (super.getLoginRole(request) == Role.XM_LEADER) {
				params.setUserId(super.getLoginUserId(request));
			}
			service.exportForestBmInfo(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 标记报名状态
	 */
	@RequestMapping("/markBmStatus")
	@Operation(desc = "标记报名状态")
	public Object markBmStatus(HttpServletRequest request, @RequestParam(required = false) String ids,
			@RequestParam(required = false) ZyjdBmStatus status, @RequestParam(required = false) String remark) throws Exception {
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择数据");
		}
		if (status == null) {
			throw BizException.withMessage("请选择报名状态");
		}
		service.markBmStatus(ids, status, remark);
		return true;
	}

	/**
	 * 审核操作
	 */
	@RequestMapping("/docheck")
    @Operation(desc = "审核操作")
    public Object shenhe(
           HttpServletRequest request,
           @RequestParam(required = false) String id,
           @RequestParam(required = false) String action,
           @RequestParam(required = false) Boolean payed,//标记已缴费
           @RequestParam(required = false) String notice
    ) throws Exception {
    	if(StringUtils.isEmpty(action)) {
    		throw BizException.withMessage("请选择是否通过");
    	}
    	ZyjdBm zyjdBm = service.selectById(id);
    	ZyjdBmBatch zyjdBmBatch = bmBatchService.selectById(zyjdBm.getBmbatchId());
    	boolean flag = false; //标记是否初审完成
    	if(Constants.YES.equals(action)) {
			if (Objects.equals(zyjdBmBatch.getIsOneTrial(), Constants.YES) && zyjdBm.getStatus() == ZyjdBmStatus.YTJ) {
				List<ZyjdBmTrial> zyjdBmTrials = zyjdBmTrialService.selectList((EntityWrapper<ZyjdBmTrial>) new EntityWrapper<ZyjdBmTrial>()
						.eq("bmbatch_id", zyjdBm.getBmbatchId())
						.eq("user_role", super.getLoginRole(request))
						.eq("num", 1));
				List<String> userIds = zyjdBmTrials.stream().map(ZyjdBmTrial::getUserId).collect(Collectors.toList());
				if (userIds.stream().noneMatch(userId -> StringUtils.equals(userId, super.getLoginUserId(request)))) {
					throw BizException.withMessage("暂无权限操作");
				}
				if (zyjdBmBatch.getAuditMode() == AuditMode.AND) {
					// 查询当前审批通过的人数
					EntityWrapper<BizBmAuditLog> bizBmAuditLogEntityWrapper = new EntityWrapper<>();
					bizBmAuditLogEntityWrapper.eq("BM_ID",id);
					bizBmAuditLogEntityWrapper.eq("BM_TYPE","ZYJD");
					bizBmAuditLogEntityWrapper.eq("IS_FINISH",1);
					Integer auditNum = bizBmAuditLogService.selectCount(bizBmAuditLogEntityWrapper);
					// 判断是否是最后一个人审核
					if (userIds.size() == auditNum + 1) {
						zyjdBm.setStatus(ZyjdBmStatus.TRIALTG);
						flag = true;
					}
				} else {
					zyjdBm.setStatus(ZyjdBmStatus.TRIALTG);
				}
			} else {
				flag = true;
				zyjdBm.setStatus(ZyjdBmStatus.SHTG);
			}
    	}
    	else {
    		zyjdBm.setStatus(ZyjdBmStatus.BH);
    	}
    	zyjdBm.setApproveUserId(super.getLoginUserId(request));
    	zyjdBm.setApproveTime(new Date());
    	zyjdBm.setApproveAdvice(notice);
    	if(payed !=null && payed) {
    		zyjdBm.setPayStatus(PayStatus.YJ);
    		zyjdBm.setPayType(Constants.PayType.OFF_LINE);
    		zyjdBm.setMarkPaydUserId(super.getLoginUserId(request));
    	}

    	String sendResultBySmsForSkillBm = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.SEND_RESULT_BY_SMS_FOR_SKILL_BM, super.getCurrentHostOrgId(request));
    	sendResultBySmsForSkillBm = StringUtils.isEmpty(sendResultBySmsForSkillBm) ? Constants.YES : sendResultBySmsForSkillBm;

    	if(ZyjdBmBatchType.ZYJD == zyjdBmBatch.getType() && StringUtils.isEmpty(zyjdBm.getRecommendCode())) {
        	try {
        		Org org = super.getCurrentHostOrg(request);
    			StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
    			if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
                    ZyjdProfession zyjdProfession = professionService.selectById(zyjdBm.getProfessionId());
    				smsSevice.sendByTemplate(org.getSmsSign(), "228800", studentInfo.getMobile(),new String[] {studentInfo.getName(),zyjdProfession.getName()}, id);
    			}
    			if(Constants.NO.equals(action)) {
    				smsSevice.sendByTemplate(org.getSmsSign(), "228801", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
    			}
    		} catch (Exception e) {
    			LOGGER.error("短信发送失败：",e);
    		}
    	}
    	else if (ZyjdBmBatchType.TDDZCL == zyjdBmBatch.getType()) {
    		try {
        		Org org = super.getCurrentHostOrg(request);
    			StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
    			if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
    				smsSevice.sendByTemplate(org.getSmsSign(), "234401", studentInfo.getMobile(),new String[] { studentInfo.getName()}, id);
    			}
    			if(Constants.NO.equals(action)) {
    				smsSevice.sendByTemplate(org.getSmsSign(), "285700", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
    			}
    		} catch (Exception e) {
    			LOGGER.error("短信发送失败：",e);
    		}
		}
    	else if(ZyjdBmBatchType.QDY == zyjdBmBatch.getType() && StringUtils.isEmpty(zyjdBm.getRecommendCode())){
            try {
                Org org = super.getCurrentHostOrg(request);
                StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
                if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
                    smsSevice.sendByTemplate(org.getSmsSign(), "229930", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
                }
                if(Constants.NO.equals(action)) {
                    smsSevice.sendByTemplate(org.getSmsSign(), "229929", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
                }
            } catch (Exception e) {
                LOGGER.error("短信发送失败：",e);
            }
        } else if(ZyjdBmBatchType.ZCPS == zyjdBmBatch.getType() && StringUtils.isEmpty(zyjdBm.getRecommendCode())){
			try {
				Org org = super.getCurrentHostOrg(request);
				StudentInfo studentInfo = studentInfoService.getByStudentId(zyjdBm.getStudentId());
				if(Constants.YES.equals(action) && Constants.YES.equals(sendResultBySmsForSkillBm)) { //审核通过
					smsSevice.sendByTemplate(org.getSmsSign(), "290957", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
				}
				if(Constants.NO.equals(action)) {
					smsSevice.sendByTemplate(org.getSmsSign(), "290958", studentInfo.getMobile(),new String[] {studentInfo.getName()}, id);
				}
			} catch (Exception e) {
				LOGGER.error("短信发送失败：",e);
			}
		}
    	service.updateById(zyjdBm);

		BizBmAuditLog bizBmAuditLog = new BizBmAuditLog();
		bizBmAuditLog.setId(BaseUtil.generateId());
		bizBmAuditLog.setBmId(id);
		bizBmAuditLog.setBmType("ZYJD");
		if (ZyjdBmStatus.TRIALTG == zyjdBm.getStatus()) {
			bizBmAuditLog.setStatus(action);
		} else {
			bizBmAuditLog.setStatus(Constants.YES.equals(action) ? "2" : "3");
		}
		bizBmAuditLog.setUserId(super.getLoginUserId(request));
		bizBmAuditLog.setAdvice(notice);
		bizBmAuditLog.setTime(new Date());
		bizBmAuditLog.setIsFinish(Integer.valueOf(Constants.YES));
		EntityWrapper<BizBmAuditLog> bizBmAuditLogEntityWrapper = new EntityWrapper<>();
		bizBmAuditLogEntityWrapper.eq("BM_ID", id);
		bizBmAuditLogEntityWrapper.eq("BM_TYPE", "ZYJD");
		bizBmAuditLogEntityWrapper.orderBy("COUNT", false);
		List<BizBmAuditLog> bizBmAuditLogs = bizBmAuditLogService.selectList(bizBmAuditLogEntityWrapper);
		if (CollectionUtils.isNotEmpty(bizBmAuditLogs)) {
			// 查询最新轮次的审核记录是否重新提交审核，如果是则轮次+1 否则使用该轮次
			if (bizBmAuditLogs.get(0).getIsFinish() == 1) {
				bizBmAuditLog.setCount(bizBmAuditLogs.get(0).getCount());
			} else {
				bizBmAuditLog.setCount(bizBmAuditLogs.get(0).getCount() + 1);
			}
		} else {
			bizBmAuditLog.setCount(1);
		}
		bizBmAuditLogService.insert(bizBmAuditLog);
		if (flag) {
			// 修改初审审核流程为已完成
			BizBmAuditLog updateBmAuditLog = new BizBmAuditLog();
			updateBmAuditLog.setIsFinish(0);
			EntityWrapper<BizBmAuditLog> updateEntityWrapper = new EntityWrapper<>();
			updateEntityWrapper.eq("BM_ID", zyjdBm.getId());
			bizBmAuditLogService.update(updateBmAuditLog, updateEntityWrapper);
		}
    	return true;
    }

	/**
	 * 统计列表
	 */
	@RequestMapping("/analysis")
	@Operation(desc = "统计列表")
	public Object analysis(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (super.getLoginRole(request) == Role.XM_LEADER) {
			params.setUserId(super.getLoginUserId(request));
		}
		return service.analysis(params);
	}

	/**
	 * 获取批次的报名数据统计详情
	 */
	@RequestMapping("/getAnalysisInfo")
	@Operation(desc = "获取批次的报名数据统计详情")
	public Object getAnalysisInfo(HttpServletRequest request,
					 ZyjdBmQueryParams params) throws Exception{
		return service.getAnalysisInfo(params);
	}

	/**
	 * 区分职业报名数据统计
	 */
	@RequestMapping("/statistcCount")
	@Operation(desc = "职业报名统计查询")
	public Object statistcCount(HttpServletRequest request, ZyjdBmQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return service.statistcCount(params);
	}

	/**
	 * 专业技术人员批量导出报名回执附件
	 */
	@RequestMapping("/exportReceipt")
	@Operation(desc = "专业技术人员批量导出报名回执附件")
	public void exportReceipt(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params) {
		response.setContentType("application/zip");
		response.setHeader("Content-Disposition", "attachment; filename=receipt.zip");
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setCurrent(1);
		params.setSize(Integer.MAX_VALUE);
		params.setType(ZyjdBmBatchType.ZYJS);
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			service.exportReceipt(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("报名回执压缩包导出失败，原因：" + e.getMessage());
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 根据业务id获取短信记录
	 */
	@RequestMapping("/getSmsLog")
	@Operation(desc = "根据业务id获取短信记录")
	public Object getSmsLog(@RequestParam(required = false) String bizId) throws Exception {
		return smsSevice.getSmsLogByBizId(bizId);
	}

	/**
	 * 上传发票
	 */
	@RequestMapping("/uploadInvoice")
	@Operation(desc = "上传发票")
	public Object uploadInvoice(HttpServletRequest request,
								@RequestParam(required = false) String id,
								@RequestParam(required = false) String url) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("报名id不能为空");
		}
		if (StringUtils.isEmpty(url)) {
			throw BizException.withMessage("发票url不能为空");
		}
		service.uploadInvoice(id, url);
		return true;
	}

	/**
	 * 批量上传发票
	 */
	@RequestMapping("/batchUploadInvoice")
	@Operation(desc = "批量上传发票")
	public Object batchUploadInvoice(HttpServletRequest request,
									 @RequestParam(required = false) String bmbatchId,
									 @RequestParam(value = "file") MultipartFile file) throws IOException {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		if (file == null) {
			throw BizException.withMessage("发票压缩包不能为空");
		}
		service.batchUploadInvoice(bmbatchId, file, super.getCurrentHostOrgId(request));
		return true;
	}
	
	/**
	 * 技能报名管理-批量报名导入，含自动注册
	 */
	@RequestMapping("/importAndRegist")
	@Operation(desc = "批量导入")
	public Object batchImportAndRegist(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file,
			@RequestParam(required = false) String bmBatchId) throws Exception {
		if (StringUtils.isEmpty(bmBatchId)) {
			throw BizException.withMessage("请选择一个报名批次进行导入");
		}
		ZyjdBmBatch zyjdBmBatch = bmBatchService.selectById(bmBatchId);
		if (zyjdBmBatch == null) {
			throw BizException.withMessage("报名批次不存在");
		}
		if (BmOpenStatus.OK != zyjdBmBatch.getStatus()) {
			throw BizException.withMessage("无法进行批量报名导入，因为报名批次已经关闭");
		}
		if (zyjdBmBatch.getBmStartTime().after(new Date()) || zyjdBmBatch.getBmEndTime().before(new Date())) {
			throw BizException.withMessage("无法进行批量报名导入，因为该项目的设置的报名时间段是:"
					+ DateUtils.format(zyjdBmBatch.getBmStartTime(), "yyyy-MM-dd HH:mm:ss") + "至"
					+ DateUtils.format(zyjdBmBatch.getBmEndTime(), "yyyy-MM-dd HH:mm:ss"));
		}
		User user = getLoginUser(request).getUser();
		return service.batchImportAndRegist(file, user, zyjdBmBatch, super.getCurrentHostOrgId(request));
	}
    
}