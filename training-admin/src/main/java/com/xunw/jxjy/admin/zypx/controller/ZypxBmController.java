package com.xunw.jxjy.admin.zypx.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.common.collect.Lists;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.HttpKit;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.dto.BandCardNumberDto;
import com.xunw.jxjy.model.enums.BmStatus;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.XmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.params.ZypxBmQueryParams;
import com.xunw.jxjy.model.zypx.params.ZypxXmQueryParams;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxXmBmCardNumberService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 职业培训 报名管理
 */
@RestController
@RequestMapping("/htgl/biz/zypx/bm")
public class ZypxBmController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ZypxBmController.class);//记录打印日志用的

    private static final String getProductsURL = "https://core.shenqimuti.cn/product/GetProducts?secretKey=%s";

    @Autowired
    private ZypxBmService service;
    @Autowired
    private ZypxXmService xmService;
    @Autowired
    private ZypxXmCourseSettingService xmCourseSettingService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private SmsSevice smsSevice;

    /**
     * 培训计划-报名管理-分页查询
     * 默认不查询已归档项目的报名信息
     */
    @RequestMapping("/list")
    @Operation(desc = "报名管理列表")
    public Object list(
            HttpServletRequest request,
            ZypxBmQueryParams params) throws Exception {
    	LoginUser loginUser = super.getLoginUser(request);
    	//若不指定培训项目ID 则根据主办单位查询培训项目
    	if (StringUtils.isEmpty(params.getXmId())) {
    		params.setHostOrgId(super.getCurrentHostOrgId(request));
    	}
    	//班主任只能够查询自己班级的学员
		if (loginUser.getRole() == Role.HEADERMASTER) {
			params.setHeadermasterId(loginUser.getUser().getId());
		}
		//项目负责人只查询自己负责的项目
		if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(super.getLoginUserId(request));
		}
		//培训机构管理员只查询自己的学员
		if (getLoginRole(request) == Role.ORG) {
			params.setOrgId(super.getLoginTopOrg(request).getId());
		}
        return service.pageQuery(params);
    }


    /**
     * 报名省市区统计人数列表
     */
    @RequestMapping("/areaApplyStatList")
    @Operation(desc = "报名省市区统计人数列表")
    public Object pageList(
            HttpServletRequest request,
            ZypxBmQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        //项目负责人
        if (getLoginRole(request) == Role.XM_LEADER) {
            params.setLeaderId(getLoginUserId(request));
        }
        return service.areaApplyStatList(params);
    }

    /**
     * 培训计划-发票申请管理-分页查询
     * 默认不查询已归档项目的报名信息
     */
    @RequestMapping("/listInvoice")
    @Operation(desc = "发票申请管理列表")
    public Object listInvoice(
            HttpServletRequest request,
            ZypxBmQueryParams params) throws Exception {
        LoginUser loginUser = super.getLoginUser(request);
        //若不指定培训项目ID 则根据主办单位查询培训项目
        if (StringUtils.isEmpty(params.getXmId())) {
            params.setHostOrgId(super.getCurrentHostOrgId(request));
        }
        //班主任只能够查询自己班级的学员
        if (loginUser.getRole() == Role.HEADERMASTER) {
            params.setHeadermasterId(loginUser.getUser().getId());
        }
        //项目负责人ID
        if (loginUser.getRole() == Role.XM_LEADER) {
            params.setLeaderId(getLoginUserId(request));
        }
        params.setIsApplyedInvoice(Constants.YES);
        return service.pageQuery(params);
    }

    /**
     * 培训计划-报名管理-批量删除
     */
    @RequestMapping("/batchDelete")
    @Operation(desc = "批量删除报名信息")
    public Object batchDelete(
            HttpServletRequest request,
            @RequestParam(required = false) String bmIds) throws Exception {
        if(StringUtils.isEmpty(bmIds)){
            throw BizException.withMessage("请至少选择一条数据");
        }
        String [] spl = StringUtils.split(bmIds, ",");
        List<String> bmIdList = new ArrayList<>();
        for (String bmId : spl) {
            bmIdList.add(bmId);
        }
        return service.batchDel(bmIdList);
    }

    /**
     * 查询学员的报名详细信息
     */
    @RequestMapping("/getBmDetailsByBmId")
    @Operation(desc = "查询学员的报名详细信息")
    public Object getBmDetailsByBmId(HttpServletRequest request, String bmId) throws Exception {
        if (StringUtils.isEmpty(bmId)){
            throw BizException.withMessage("请传入学员的报名ID");
        }
        return service.getBmDetailsByBmId(bmId);
    }

    @RequestMapping("/doApprove")
    @Operation(desc = "审批")
    public Object doApprove(HttpServletRequest request,
                            @RequestParam(required = false) String bmId,
                            @RequestParam(required = false) BmStatus bmStatus,
                            @RequestParam(required = false) String approveAdvice
                            ){
        if (StringUtils.isEmpty(bmId)) {
            throw BizException.withMessage("请传入报名ID");
        }
        if (bmStatus == null) {
            throw BizException.withMessage("请选择审批结果");
        }
        ZypxBm zypxBm = service.selectById(bmId);
        if(zypxBm.getStatus() == BmStatus.BMCG){
            throw BizException.withMessage("审核通过，请勿修改");
        }
        LoginUser loginUser = super.getLoginUser(request);
        service.doApprove(bmId,bmStatus,loginUser.getUser().getId(),approveAdvice);
        try {
			ZypxXm xm = xmService.selectById(zypxBm.getXmId());
			StudentInfo studentInfo = studentInfoService.getByStudentId(zypxBm.getStudentId());
			if (Constants.YES.equals(xm.getIsAutoSendSms()) && StringUtils.isNotEmpty(studentInfo.getMobile())) {
				smsSevice.sendByTemplate(super.getCurrentHostOrg(request).getSmsSign(), 
						"280530", studentInfo.getMobile(), new String[] {studentInfo.getName(),xm.getTitle(),bmStatus.getName()});
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
        return true;
    }

    /**
     * 培训计划-报名管理-批量报名导入，含自动注册
     */
    @RequestMapping("/importAndRegist")
    @Operation(desc = "批量导入")
    public Object batchImportAndRegist(
            HttpServletRequest request,
            @RequestParam(value = "file") MultipartFile file,
            @RequestParam(required = false) String xmId,
            @RequestParam(required = false) String isOffline,
            @RequestParam(required = false) String isSyncChooseCourse) throws Exception {
        if (StringUtils.isEmpty(xmId)){
            throw BizException.withMessage("请选择一个培训项目进行导入");
        }
        ZypxXm zypxXm = xmService.selectById(xmId);
        if (zypxXm == null) {
            throw BizException.withMessage("培训项目不存在");
        }
        if (XmStatus.OK != zypxXm.getStatus()) {
            throw BizException.withMessage("无法进行批量报名导入，因为培训项目已经关闭");
        }
        if (zypxXm.getJtbmStartTime().after(new Date()) || zypxXm.getJtbmEndTime().before(new Date())) {
            throw BizException.withMessage("无法进行批量报名导入，因为该项目的设置的集体报名时间段是:"+
                    DateUtils.format(zypxXm.getJtbmStartTime(), "yyyy-MM-dd HH:mm:ss") + "至" +
                    DateUtils.format(zypxXm.getJtbmEndTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        User user = getLoginUser(request).getUser();
        return service.batchImportAndRegist(file, user.getId(), xmId, user.getOrgId(), isOffline, isSyncChooseCourse, super.getCurrentHostOrgId(request));
    }

    /**
     * 查看学员的报名课程信息
     */
    @RequestMapping("/getBmCourseByBmId")
    @Operation(desc = "查询学员的报名课程信息")
    public Object getBmCourseByBmId(
            HttpServletRequest request,
            ZypxBmQueryParams params
            ) throws Exception {
        if (StringUtils.isEmpty(params.getBmId())){
            throw BizException.withMessage("请传入学员的报名ID");
        }
        params.setCurrent(Constants.DEFAULT_PAGE_NUMBER);
        params.setSize(Integer.MAX_VALUE);
        return service.getBmCourseByBmId(params.getBmId(), params).getRecords();
    }

    /**
     * 培训计划-报名管理-批量导出
     */
    @RequestMapping("/export")
    @Operation(desc = "批量导出报名信息")
    public void export(
            HttpServletRequest request,
            HttpServletResponse response,
            ZypxBmQueryParams params) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student.xls");
        OutputStream os = null;
        try {
        	LoginUser loginUser = super.getLoginUser(request);
        	if (StringUtils.isEmpty(params.getXmId())) {
        		params.setHostOrgId(super.getCurrentHostOrgId(request));
        	}
        	//班主任只能够查询自己班级的学员
			if (loginUser.getRole() == Role.HEADERMASTER) {
				params.setHeadermasterId(loginUser.getUser().getId());
			}
			//项目负责人只查询自己负责的项目
			if (getLoginRole(request) == Role.XM_LEADER) {
				params.setLeaderId(getLoginUserId(request));
			}
			params.setSize(Integer.MAX_VALUE);
	        List<Map<String, Object>> records = service.pageQuery(params).getRecords();
            os = response.getOutputStream();
            service.batchExport(Lists.reverse(records), os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出失败:",e); //记录打印日志
        }
        finally {
            if(os != null){
                IOUtils.closeQuietly(os);
            }
        };
    }

    /**
     * 培训计划-报名管理-批量导出（自定义表单）
     */
    @RequestMapping("/exportWithFormData")
    @Operation(desc = "批量导出报名信息")
    public void exportWithFormData(
            HttpServletRequest request,
            HttpServletResponse response,
            ZypxBmQueryParams params) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student.xls");
        OutputStream os = null;
        LoginUser loginUser = super.getLoginUser(request);
    	if (StringUtils.isEmpty(params.getXmId())) {
    		throw BizException.withMessage("请选择一个培训项目");
    	}
    	//班主任只能够查询自己班级的学员
		if (loginUser.getRole() == Role.HEADERMASTER) {
			params.setHeadermasterId(loginUser.getUser().getId());
		}
		//项目负责人只查询自己负责的项目
		if (getLoginRole(request) == Role.XM_LEADER) {
			params.setLeaderId(getLoginUserId(request));
		}
		params.setSize(Integer.MAX_VALUE);
        try {
	        List<Map<String, Object>> records = service.pageQuery(params).getRecords();
            os = response.getOutputStream();
            service.batchExportWithFormData(params.getXmId(), Lists.reverse(records), os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出失败:",e); //记录打印日志
        }
        finally {
            if(os != null){
                IOUtils.closeQuietly(os);
            }
        };
    }


    @RequestMapping("/exportStudentInvoice")
    @Operation(desc = "导出开票信息")
    public void exportStudentInvoice(HttpServletRequest request, HttpServletResponse response, String xmId) {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("请选择一个培训项目");
        }
        String currentHostOrgId = super.getCurrentHostOrgId(request);
        SXSSFWorkbook workbook = service.exportStudentInvoice(response, xmId, currentHostOrgId);
        super.write(response, workbook);
    }
    



    /**
     * 划分班级
     */
    @RequestMapping("/setClass")
    @Operation(desc = "划分班级")
    public Object setClass(
            HttpServletRequest request,
            @RequestParam(required = false) String bmIds,
            @RequestParam(required = false) String classId
            ) throws Exception {
    	if (StringUtils.isEmpty(bmIds)) {
			throw BizException.withMessage("请勾选报名信息");
		}
    	if (StringUtils.isEmpty(classId)) {
    		throw BizException.withMessage("请勾选班级");
    	}
    	service.setClass(bmIds, classId);
    	return true;
    }

    /**
     * 批量标记已经开票
     */
    @RequestMapping("/markInvoice")
    @Operation(desc = "批量标记已经开票")
    public Object markInvoice(
            HttpServletRequest request,
            @RequestParam(required = false) String ids,
            @RequestParam(required = false) String isMarkInvoice
    ) throws Exception {
        if (StringUtils.isEmpty(ids)) {
            throw BizException.withMessage("请选择一条信息");
        }
        if (StringUtils.isEmpty(isMarkInvoice)) {
            throw BizException.withMessage("请传入标记开票状态");
        }
        service.markInvoice(ids,isMarkInvoice);
        return true;
    }

    /**
     * 批量标记缴费
     */
    @RequestMapping("/markPay")
    @Operation(desc = "批量标记缴费")
    public Object markPayed(
            HttpServletRequest request,
            @RequestParam(required = false) String ids,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Double payedAmount,
            @RequestParam(required = false) String markPayedReason
            ) throws Exception {
    	if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请勾选报名信息");
		}
        if (status == null) {
            throw BizException.withMessage("状态不能为空");
        }
    	if (Constants.YES.equals(status) && payedAmount == null) {
    		throw BizException.withMessage("请输入线下缴费的金额");
    	}
    	if (StringUtils.isEmpty(markPayedReason)) {
    		throw BizException.withMessage("请输入备注信息");
    	}
    	service.markPayed(ids, payedAmount, status, markPayedReason, super.getLoginUserId(request));
    	return true;
    }

    /**
     * 标记退费
     */
    @RequestMapping("/refund")
    @Operation(desc = "标记退费")
    public Object refund(
            HttpServletRequest request,
            @RequestParam(required = false) String ids,
            @RequestParam(required = false) String reason
    ) throws Exception {
        if (StringUtils.isEmpty(ids)) {
            throw BizException.withMessage("请勾选报名信息");
        }
        service.refund(ids, reason, getLoginUser(request).getUser());
        return true;
    }

    /**
     * 根据报名id获取订单信息
     */
    @RequestMapping("/getOrderByBmId")
    @Operation(desc = "根据报名id获取订单信息")
    public Object getOrderByBmId(@RequestParam(required = false) String id) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("报名id不能为空");
        }
        return service.getOrderByBmId(id);
    }

    /**
     * 培训计划-报名管理-批量导入分班信息
     */
    @RequestMapping("/importClass")
    @Operation(desc = "批量导入分班信息")
    public Object importClass(
            HttpServletRequest request,
            @RequestParam(value = "file") MultipartFile file,
            @RequestParam(required = false) String classId,
            @RequestParam(required = false) String xmId) throws Exception {
        if (StringUtils.isEmpty(xmId)){
            throw BizException.withMessage("请选择培训项目");
        }
        if (StringUtils.isEmpty(classId)){
            throw BizException.withMessage("请选择培训班级");
        }
        return service.importClass(file, xmId, classId, super.getCurrentHostOrgId(request));
    }

    /**
     * 导出附件zip,一个项目是一个文件夹
     */
    @RequestMapping("/exportAccessory")
    @Operation(desc = "批量导出附件")
    public void exportAccessory(
            HttpServletRequest request,
            HttpServletResponse response,
            ZypxBmQueryParams params) throws Exception {
        response.setContentType("application/octet-stream");
        String filename = "studentAccessory.zip";
        response.setHeader("content-disposition", "attachment;filename=" + filename);
        OutputStream os = null;
        try {
            LoginUser loginUser = super.getLoginUser(request);
            if (StringUtils.isEmpty(params.getXmId())) {
                params.setHostOrgId(super.getCurrentHostOrgId(request));
            }
            //班主任只能够查询自己班级的学员
            if (loginUser.getRole() == Role.HEADERMASTER) {
                params.setHeadermasterId(loginUser.getUser().getId());
            }
            //项目负责人只查询自己负责的项目
            if (getLoginRole(request) == Role.XM_LEADER) {
                params.setLeaderId(getLoginUserId(request));
            }
            params.setSize(Integer.MAX_VALUE);
            os = response.getOutputStream();
            service.expAccessory(params, os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出失败:",e); //记录打印日志
        }
        finally {
            if(os != null){
                IOUtils.closeQuietly(os);
            }
        };
    }

    /**
     * 培训计划-发票申请管理-批量导出
     */
    @RequestMapping("/exportInvoice")
    @Operation(desc = "批量导出开票信息")
    public void exportInvoice(
            HttpServletRequest request,
            HttpServletResponse response,
            ZypxBmQueryParams params) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=InvoiceApply.xls");
        OutputStream os = null;
        try {
            LoginUser loginUser = super.getLoginUser(request);
            if (StringUtils.isEmpty(params.getXmId())) {
                params.setHostOrgId(super.getCurrentHostOrgId(request));
            }
            //班主任只能够查询自己班级的学员
            if (loginUser.getRole() == Role.HEADERMASTER) {
                params.setHeadermasterId(loginUser.getUser().getId());
            }
            //项目负责人只查询自己负责的项目
    		if (getLoginRole(request) == Role.XM_LEADER) {
    			params.setLeaderId(getLoginUserId(request));
    		}
            params.setSize(Integer.MAX_VALUE);
            params.setIsApplyedInvoice(Constants.YES);
            List<Map<String, Object>> bizzypxBmList = service.pageQuery(params).getRecords();
            os = response.getOutputStream();
            service.exportInvoice(Lists.reverse(bizzypxBmList), os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出失败:",e); //记录打印日志
        }
        finally {
            if(os != null){
                IOUtils.closeQuietly(os);
            }
        };
    }

    /**
     * 发票详情
     */
    @RequestMapping("/getInvaoiceDetail")
    @Operation(desc = "发票详情")
    public Object getInvaoiceDetail(HttpServletRequest request,
                                     ZypxBmQueryParams params){
        Map<String,Object> result =service.getInvaoiceDetail(params);
        return result;
    }

    /**
     * 课程补报-按项目
     */
    @RequestMapping("/addBmCourseByXm")
    @Operation(desc = "课程补报-按项目")
    public Object addBmCourseByXm(HttpServletRequest request,
    		@RequestParam(required = false) String xmId,
    		@RequestParam(required = false) String courseSettingId){
    	if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择培训项目");
		}
    	if (StringUtils.isEmpty(courseSettingId)) {
			throw BizException.withMessage("请选择课程");
		}
    	List<Map<String, Object>> courseList = xmCourseSettingService.getAllCourseByXmId(xmId);
    	courseList = courseList.stream().filter(x->courseSettingId.equals(BaseUtil.getStringValueFromMap(x, "id"))).collect(Collectors.toList());
    	if (courseList ==null || courseList.size() == 0) {
			throw BizException.withMessage("您选择的课程设置不存在");
		}
    	service.addBmCourseByXm(xmId, courseSettingId);
        return true;
    }

    /**
     * 课程补报-按照学员
     */
    @RequestMapping("/addBmCourseByStudent")
    @Operation(desc = "课程补报-按学员")
    public Object addBmCourseByStudent(HttpServletRequest request,
    		@RequestParam(required = false) String xmId,
    		@RequestParam(required = false) String bmIds,
    		@RequestParam(required = false) String courseSettingId){
    	if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择一个培训项目");
		}
    	if (StringUtils.isEmpty(bmIds)) {
			throw BizException.withMessage("请选择学员报名数据");
		}
    	if (StringUtils.isEmpty(courseSettingId)) {
			throw BizException.withMessage("请选择课程");
		}
    	List<Map<String, Object>> courseList = xmCourseSettingService.getAllCourseByXmId(xmId);
    	courseList = courseList.stream().filter(x->courseSettingId.equals(BaseUtil.getStringValueFromMap(x, "id"))).collect(Collectors.toList());
    	if (courseList ==null || courseList.size() == 0) {
			throw BizException.withMessage("您选择的课程设置不存在");
		}
    	service.addBmCourseByStudent(bmIds, courseSettingId);
        return true;
    }

    /**
     * 短信群发
     */
	@RequestMapping("/sendSms")
	@Operation(desc = "短信群发")
	public Object sendSms(HttpServletRequest request, @RequestParam(required = false) String xmId,
			@RequestParam(required = false) String message) {
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择培训项目");
		}
		if (StringUtils.isEmpty(message)) {
			throw BizException.withMessage("请输入短信内容");
		}
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper<ZypxBm>();
		wrapper.eq("xm_id", xmId);
		List<ZypxBm> list = service.selectList(wrapper);
		if (list.size() == 0) {
			throw BizException.withMessage("操作失败，当前项目尚未有学员报名");
		}
		Org currentHostOrg = super.getCurrentHostOrg(request);
		for (ZypxBm zypxBm : list) {
			StudentInfo studentInfo = studentInfoService.getByStudentId(zypxBm.getStudentId());
			if (studentInfo != null && BaseUtil.isNotEmpty(studentInfo.getMobile())) {
				smsSevice.sendByContent(currentHostOrg.getSmsSign(), studentInfo.getMobile(), message, zypxBm.getId());
			}
		}
		return true;
	}

    @RequestMapping("/setOfflineBmCount")
    @Operation(desc = "人数设置")
    public Object setOfflineBmCount(@RequestParam(required = false) String xmId,
                                    @RequestParam(required = false) Integer offLineBmCount,
                                    @RequestParam(required = false) Integer onlineBmCount) {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("项目ID不能够为空");
        }
        if (offLineBmCount == null) {
            throw BizException.withMessage("人数不能够为空");
        }
        ZypxXm zypxXm = xmService.selectById(xmId);
        zypxXm.setOffLineBmCount(offLineBmCount);
        zypxXm.setOnlineBmCount(onlineBmCount);
        xmService.updateById(zypxXm);
        return true;
    }

    /**
     * 短信群发
     */
    @RequestMapping("/sendSmsByStu")
    @Operation(desc = "给已报名的勾选的学员短信群发")
    public Object sendSmsByStu(HttpServletRequest request,
                               @RequestParam(required = false) String mobiles,
                               @RequestParam(required = false) String message) {
        if (StringUtils.isEmpty(message)) {
            throw BizException.withMessage("请输入短信内容");
        }
        if (StringUtils.isEmpty(mobiles)) {
            throw BizException.withMessage("请至少选择一条数据");
        }
        String[] mobileStrs = StringUtils.split(mobiles, ",");
        Set<String> mobileSet = new HashSet<String>();
        for (String mobile : mobileStrs) {
            if (StringUtils.isNotEmpty(mobile)) {
                mobileSet.add(mobile);
            }
        }
        if (mobileSet.size() <= 0) {
            throw BizException.withMessage("请至少选择一个手机号不为空的学员。");
        }
        Org currentHostOrg = super.getCurrentHostOrg(request);
        String[] mobileArray = new String[mobileSet.size()];
        mobileSet.toArray(mobileArray);
        smsSevice.sendBatchByContent(currentHostOrg.getSmsSign(), mobileArray, message);
        return true;
    }
    @RequestMapping("/statisticCost")
    @Operation(desc = "培训项目收费金额统计")
    public Object statisticCost(HttpServletRequest request, ZypxXmQueryParams params) throws Exception {
        return service.statisticCost(params);
    }

    /**
     * 短信群发
     * @throws Exception 
     */
    @RequestMapping("/sendSmsByBm")
    @Operation(desc = "给已报名的勾选的学员短信群发")
    public Object sendSmsByBm(HttpServletRequest request,
                               @RequestParam(required = false) String ids,
			@RequestParam(required = false) String message) throws Exception {
		if (StringUtils.isEmpty(message)) {
			throw BizException.withMessage("请输入短信内容");
		}
		if (StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请至少选择一条数据");
		}
		Org currentHostOrg = super.getCurrentHostOrg(request);
		for (String bizId : StringUtils.split(ids, ",")) {
			Map<String, Object> bmDetails = service.getBmDetailsByBmId(bizId);
			StudentInfo studentInfo = (StudentInfo) bmDetails.get("studentInfo");
			if (studentInfo != null && BaseUtil.isNotEmpty(studentInfo.getMobile())) {
				smsSevice.sendByContent(currentHostOrg.getSmsSign(), studentInfo.getMobile(), message, bizId);
			}
		}
		return true;
	}


    /**
     * @param request 
     * @param xmId  项目id
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/batchUploadInvoice")
    @Operation(desc = "批量上传发票")
    public Object batchUploadInvoice(HttpServletRequest request,
                                     @RequestParam(required = false) String xmId,
                                     @RequestParam(value = "file") MultipartFile file) throws IOException {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("请选择报名的项目");
        }
        if (file == null) {
            throw BizException.withMessage("发票压缩包不能为空");
        }
        String result = service.batchUploadInvoice(xmId, file, super.getCurrentHostOrgId(request));
        if (result == null) {
            return true;
        } else {
            throw BizException.withMessage(result);
        }
    }

    /** 单个上传
     * @param request 
     * @param id
     * @param file
     * @return
     */
    @RequestMapping("/uploadInvoice")
    @Operation(desc = "上传发票")
    public Object uploadInvoice(HttpServletRequest request,
                                @RequestParam(required = false) String id,
                                @RequestParam(required = false) MultipartFile file) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("报名id不能为空");
        }
        if (file == null) {
            throw BizException.withMessage("发票文件不能为空");
        }
        service.uploadInvoice(id, file);
        return true;
    }

    /**
     * 设置班内职责
     */
    @RequestMapping("/setJob")
    @Operation(desc = "设置班内职责")
    public Object setJob(@RequestParam(required = false) String id,
                         @RequestParam(required = false) Integer shiftDuty) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("报名id不能为空");
        }
        if (shiftDuty == null) {
            throw BizException.withMessage("班内职责不能为空");
        }
        ZypxBm zypxBm = service.selectById(id);
        if (zypxBm == null) {
            throw BizException.withMessage("报名数据不存在");
        }
        zypxBm.setShiftDuty(shiftDuty);
        service.updateById(zypxBm);
        return true;
    }

    /**
     * 获取产品（神奇的考点母题）
     */
    @RequestMapping("/getProducts")
    @Operation(desc = "获取产品（神奇的考点母题）")
    public Object getProducts() {
        return JSON.parseObject(HttpKit.get(String.format(getProductsURL, Constants.secretKey))).get("Data");
    }

    /**
     * 批量绑定学习卡（神奇的考点母题）
     */
    @RequestMapping("/bandCardNumber")
    @Operation(desc = "批量绑定学习卡（神奇的考点母题）")
    public Object bandCardNumber(@org.springframework.web.bind.annotation.RequestBody BandCardNumberDto bandCardNumberDto) throws IOException {
        if (StringUtils.isEmpty(bandCardNumberDto.getBmIds())) {
            throw BizException.withMessage("请选择一条数据");
        }
        service.bandCardNumber(bandCardNumberDto);
        return true;
    }
}
