package com.xunw.jxjy.admin.zcps.controller;

import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.zyjd.entity.ZcGroup;
import com.xunw.jxjy.model.zyjd.service.ZcGroupService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 答辩评审分组
 */
@RestController
@RequestMapping("/htgl/biz/zcps/statistics")
public class ZcStatisticsController extends BaseController {

	private static final Logger Logger = LoggerFactory.getLogger(ZcStatisticsController.class);

	@Autowired
	private ZcGroupService zcGroupService;

	@RequestMapping("")
	@Operation(desc = "评审统计")
	public Object psStatistics(HttpServletRequest request,
							   @RequestParam(required = false) String bmbatchId) {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		return zcGroupService.psStatistics(bmbatchId);
	}

	@RequestMapping("/byProfession")
	@Operation(desc = "评审统计按工种统计")
	public Object byProfession(HttpServletRequest request,
							   @RequestParam(required = false) String bmbatchId) {
		if (StringUtils.isEmpty(bmbatchId)) {
			throw BizException.withMessage("批次id不能为空");
		}
		return zcGroupService.byProfessionStatistics(bmbatchId);
	}
}