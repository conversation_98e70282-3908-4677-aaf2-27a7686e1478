package com.xunw.jxjy.admin.inf.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.params.CourseQueryParams;
import com.xunw.jxjy.model.inf.service.CourseService;

/**
 * 课程管理
 */
@RestController
@RequestMapping("/htgl/inf/zypx/course")
public class CourseController extends BaseController {

	@Autowired
	private CourseService service;
    @Autowired
    private ZypxTypeService zypxTypeService;

    @RequestMapping("/list")
    @Operation(desc = "职业培训课程列表")
    public Object list(
    				HttpServletRequest request,
    				CourseQueryParams params) throws Exception {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
	 	return service.pageQuery(params);
    }

    @RequestMapping("/listOpenCourse")
    @Operation(desc = "查询有课件或有直播的课程列表")
    public Object pageQueryOpenCourse(
            HttpServletRequest request,
            CourseQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        params.setIsHasResource(Constants.YES);
        return service.pageQuery(params);
    }
    //新增
    @RequestMapping("/add")
    @Operation(desc = "添加职业培训课程")
    public Object add(
    					HttpServletRequest request,
    					@RequestParam(required = false) String typeId,
    					@RequestParam(required = false) String name,
    					@RequestParam(required = false) String tag,
    					@RequestParam(required = false) String logo,
    					@RequestParam(required = false) String xct,
    					@RequestParam(required = false) String kcxz,
    					@RequestParam(required = false) Double hours,
    					@RequestParam(required = false) Double amount,
    					@RequestParam(required = false) String remark) throws Exception {
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入课程名称");
        }
        if(StringUtils.isEmpty(kcxz)){
            throw BizException.withMessage("请选择课程性质");
        }
        if(hours == null){
            throw BizException.withMessage("请输入课程的学时");
        }
        if(hours <= 0){
        	throw BizException.withMessage("课程学时必须大于0");
        }
        String courseCode = service.genCourseCode();
        String id = BaseUtil.generateId();
        Course course = new Course(id, courseCode, name, super.getLoginUserId(request), new Date(), Zt.OK);
        course.setTypeId(typeId);
        course.setXct(xct);
        course.setLogo(logo);
        course.setKcxz(kcxz);
        course.setHours(hours);
        course.setTag(tag);
        course.setHostOrgId(super.getCurrentHostOrgId(request));
        course.setAmount(amount);
        course.setRemark(remark);
        service.insert(course);
        return service.selectById(id);
    }

    //获取
    @RequestMapping("/getById")
    @Operation(desc = "获取职业培训课程")
    public Object getById(
    				 	@RequestParam(required = false) String id) throws Exception {
    	if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
	 	Course course = service.selectById(id);
	 	return course;
    }

    //修改
    @RequestMapping("/edit")
    @Operation(desc = "修改职业培训课程")
    public Object edit(
    					HttpServletRequest request,
    					@RequestParam(required = false) String id,
    					@RequestParam(required = false) String typeId,
    					@RequestParam(required = false) String xct,
    					@RequestParam(required = false) String logo,
    					@RequestParam(required = false) String tag,
    					@RequestParam(required = false) String name,
                        @RequestParam(required = false) String kcxz,
                        @RequestParam(required = false) Double hours,
                        @RequestParam(required = false) Double amount,
                        @RequestParam(required = false) String remark
                        ) throws Exception {
    	if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
    	if(StringUtils.isEmpty(name)){
    		throw BizException.withMessage("请输入课程名称");
    	}
        if(StringUtils.isEmpty(kcxz)){
            throw BizException.withMessage("请选择课程性质");
        }
        if(hours == null){
            throw BizException.withMessage("请输入课程的学时");
        }
        if(hours <= 0){
        	throw BizException.withMessage("课程学时必须大于0");
        }
    	Course check = service.selectById(id);
    	if(check == null){
    	    throw BizException.withMessage("ID不存在:"+id);
        }
        check.setName(name);
        check.setUpdatorId(super.getLoginUserId(request));
        check.setUpdateTime(new Date());
        check.setTypeId(typeId);
        check.setXct(xct);
        check.setKcxz(kcxz);
        check.setTag(tag);
        check.setHours(hours);
        check.setLogo(logo);
        check.setAmount(amount);
        check.setRemark(remark);
	 	service.updateById(check);
	 	return true;
    }

    @RequestMapping("/batchEditTypeName")
    @Operation(desc = "批量修改课程类型")
    public Object batchEditTypeName(
            HttpServletRequest request,
            @RequestParam(required = false) String ids,
            @RequestParam(required = false) String typeId ) throws Exception {
        if (StringUtils.isEmpty(ids)){
            throw BizException.withMessage("请选择一条数据");
        }
        if (StringUtils.isEmpty(typeId)){
            throw BizException.withMessage("请选择一种类型");
        }
        ZypxType zypxType = zypxTypeService.selectById(typeId);
        if (zypxType == null){
            throw BizException.withMessage("分类不存在");
        }
        if (zypxType.getCategory() != TypeCategory.COURSE){
            throw BizException.withMessage("分类信息异常，请重新选择");
        }
        List<Course> courses = service.selectBatchIds(Arrays.asList(ids.split(",")));
        for (Course course: courses){
            course.setTypeId(typeId);
        }
        DBUtils.updateBatchById(courses, Course.class);
        return true;
    }
    
    //启用/停用
    @RequestMapping("/enable")
    @Operation(desc = "启用或停用职业培训课程")
    public Object enable(
    					HttpServletRequest request,
    				 	@RequestParam(required = false) String id,
    				 	@RequestParam(required = false) Zt status) throws Exception {
    	if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据");
    	}
    	if(status == null){
    		throw BizException.withMessage("请选择状态");
    	}
        Course check = service.selectById(id);
        if(check == null){
            throw BizException.withMessage("ID不存在:"+id);
        }
    	check.setStatus(status);
        check.setUpdatorId(super.getLoginUserId(request));
        check.setUpdateTime(new Date());
    	service.updateById(check);
    	return true;
    }

    //资源是否开放/关闭
    @RequestMapping("/openResourceOrNot")
    @Operation(desc = "资源是否开放/关闭")
    public Object openResourceOrNot(
                    HttpServletRequest request,
                    @RequestParam(required = false ) String id,
                    @RequestParam(required = false ) String isOpenZb,
                    @RequestParam(required = false ) String isOpenKj,
                    @RequestParam(required = false ) String isOpenTk
    )throws Exception{
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
        if(StringUtils.isEmpty(isOpenZb) || StringUtils.isEmpty(isOpenKj) || StringUtils.isEmpty(isOpenTk)){
            throw BizException.withMessage("请选择状态");
        }
        Course check = service.selectById(id);
        if(check == null){
            throw BizException.withMessage("ID不存在:"+id);
        }
        check.setIsOpenZb(isOpenZb);
        check.setIsOpenKj(isOpenKj);
        check.setIsOpenTk(isOpenTk);
        check.setUpdatorId(super.getLoginUserId(request));
        check.setUpdateTime(new Date());
        service.updateById(check);
        return true;
    }

    @RequestMapping("/select")
    @Operation(desc = "课程下拉框")
    public Object select(
    		HttpServletRequest request,
    		CourseQueryParams params) throws Exception {
    	params.setCurrent(1);
    	params.setSize(Integer.MAX_VALUE);
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
    	return service.pageQuery(params).getRecords();
    }

    @RequestMapping("/importCourse")
    @Operation(desc = "批量导入课程")
    public Object importCourse(
            HttpServletRequest request,
            @RequestParam(value = "file") MultipartFile File,
            @RequestParam(required = false) String typeId
    ) throws Exception {
        return service.importCourse(File, super.getLoginUserId(request), super.getCurrentHostOrgId(request),typeId);
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "课程删除")
    public Object deleteById(HttpServletRequest request, @RequestParam(required = false)String id)throws  Exception{
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请传入Id");
        }
            service.deleteByCourseId(id);
        return true;
    }
    
    @RequestMapping("/getCourseWithKj")
    @Operation(desc = "职业培训课程列表")
    public Object getCourseWithKj(
    				HttpServletRequest request,
    				CourseQueryParams params) throws Exception {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
	 	return service.getCourseWithKj(params);
    }

}
