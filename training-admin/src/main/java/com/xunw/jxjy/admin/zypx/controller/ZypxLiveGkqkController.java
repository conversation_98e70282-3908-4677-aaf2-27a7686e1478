package com.xunw.jxjy.admin.zypx.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.CourseLive;
import com.xunw.jxjy.model.inf.service.CourseLiveService;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.params.StudentLearningScoreQueryParams;
import com.xunw.jxjy.model.zypx.params.ZypxStudentLiveQkQkParams;
import com.xunw.jxjy.model.zypx.service.StudentLearningAnalysisService;
import com.xunw.jxjy.model.zypx.service.ZypxLiveLearningProgressService;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直播--学生观看情况
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/zypx/zb/xsgkqk")
public class ZypxLiveGkqkController extends BaseController {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(ZypxLiveGkqkController.class);
	
	@Autowired
	private ZypxLiveLearningProgressService service;
	@Autowired
	private OrgService orgService;
	@Autowired
	private CourseLiveService courseLiveService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private StudentLearningAnalysisService analysisService;
	@Autowired
	private StudentLearningAnalysisService studentLearningAnalysisService;

	// 分页查询学生观看情况
	@RequestMapping("/list")
	@Operation(desc = "查询学生观看情况列表")
	public Object list(HttpServletRequest request, ZypxStudentLiveQkQkParams params) {
		LoginUser loginUser = super.getLoginUser(request);
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		if (loginUser.getRole() == Role.HEADERMASTER) {
			params.setHeadermasterId(loginUser.getUser().getId());
		}
		else if (loginUser.getRole() == Role.TEACHER) {
			params.setTeacherId(loginUser.getUser().getId());
		}
		else if (loginUser.getRole() == Role.XM_LEADER) {
			params.setLeaderId(loginUser.getUser().getId());
		}
		else if (loginUser.getRole() == Role.ENTRUST_ORG) {
			Org topOrg = orgService.getTopOrg(super.getLoginOrgId(request));
			params.setEntrustOrgId(topOrg.getId());
		}
		Page pageInfo = service.pageQueryGkqk(params);
		return pageInfo;
	}

	// 学生观看情况导出
	@RequestMapping("/export")
	@Operation(desc = "学生观看情况导出")
	public void export(HttpServletRequest request, HttpServletResponse response, StudentLearningScoreQueryParams params)
			throws IOException, RowsExceededException, WriteException {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		LoginUser loginUser = super.getLoginUser(request);
		if (loginUser.getRole() == Role.HEADERMASTER) {
			params.setHeadermasterId(loginUser.getUser().getId());
		}
		//项目负责人只查询自己负责的项目
		if (loginUser.getRole() == Role.XM_LEADER) {
			params.setLeaderId(loginUser.getUser().getId());
		}
		//委托单位管理员
		if (loginUser.getRole() == Role.ENTRUST_ORG) {
			Org topOrg = orgService.getTopOrg(super.getLoginOrgId(request));
			params.setEntrustOrgId(topOrg.getId());
		}
		params.setSize(Integer.MAX_VALUE);
		Page pageInfo = studentLearningAnalysisService.pageQueryRecord(params);

		List<Map<String, Object>> contentsList = pageInfo.getRecords();
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student_watch_details.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			WritableWorkbook writableWorkbook = Workbook.createWorkbook(os);
			WritableSheet ws = writableWorkbook.createSheet("学生观看情况数据", 0);
			int row = 0;
			int col = 0;
			// 添加表头
			ws.addCell(new Label(col, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 10);
			ws.addCell(new Label(col, row, "类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "培训项目", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "身份证号码", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "承办单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "课程", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "课件学习进度", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "直播学习进度", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			ws.addCell(new Label(col, row, "面授签到（次）", OfficeToolExcel.getTitle()));
			ws.setColumnView(col++, 20);
			// 内容开始
			row++;
			for (Map<String, Object> content : contentsList) {
				col = 0;
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(row), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("typeName")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("title")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("studentName")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("sfzh")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("receiveOrgName")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(
						new Label(col++, row,
								BaseUtil.convertNullToEmpty(content.get("courseName")),
								OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, 
								BaseUtil.convertNullToEmpty(content.get("kjProgress")),
								OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("zbProgress")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(content.get("msCount")),
						OfficeToolExcel.getNormolCell()));
				row++;
			}
			writableWorkbook.write();
			writableWorkbook.close();
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出数据错误:",e);
		}
		finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}
	
	/**
	 * 查询观看情况详情
	 */
	@RequestMapping("/details")
	@Operation(desc = "查询观看情况详情")
	public Object details(String xmId, String courseLiveId, String studentId){
		CourseLive courseLive = courseLiveService.selectById(courseLiveId);
		Course course = courseService.selectById(courseLive.getCourseId());
		List<Map<String, Object>> list = service.getGkqkDetails(xmId, courseLiveId, studentId);
		//计算总时长、总观看时长
    	int zbsc = 0;
    	int gksc = 0;
    	for (Map<String, Object> map : list) {
    		zbsc += BaseUtil.getIntValueFromMap(map, "lessonDuration",  0);//直播时长
    		gksc += BaseUtil.getIntValueFromMap(map, "watchLessonDuration", 0);//总观看时长
    	}
		Map<String, Object> result = new HashMap<String, Object>();
    	result.put("zbsc", zbsc);
    	result.put("gksc", gksc);
    	result.put("list", list);
		Map<String, Object> courseScore = analysisService.getLearningCourseScore(xmId, studentId, courseLive.getCourseId());
    	result.put("courseScore",  courseScore);
    	result.put("course",  course);
    	return result;
	}
	
	/**
	 * 查询抓拍照片
	 */
	@RequestMapping("/getPhoto")
	@Operation(desc = "查询抓拍照片")
	public Object getPhoto(String xmId, String studentId, String lessonId){
		return service.getPhoto(xmId, studentId, lessonId);
	}
	
	/**
	 * 删除抓拍的照片
	 */
	@RequestMapping("/deletePhoto")
	@Operation(desc = "删除抓拍照片")
	public Object deletePhoto(
			@RequestParam(required = false) String logId
			) throws Exception {
		if(StringUtils.isEmpty(logId)){
			throw BizException.PARAMS_ERROR;
		}
		service.deletePhotoById(logId);
		return true;
	}
	
	/**
	 * 查询所有的抓拍照片
	 */
	@RequestMapping("/getAllPhoto")
	@Operation(desc = "查询抓拍照片")
	public Object getAllPhoto(String xmId, String studentId, String courseLiveId){
		return service.getAllPhotoLog(xmId, studentId, courseLiveId);
	}
}
