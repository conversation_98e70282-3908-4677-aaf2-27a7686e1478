package com.xunw.jxjy.admin.tk.controller;

import java.io.OutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.tk.params.QuestionDBQueryParams;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.zypx.service.PaperRepoService;

import net.sf.json.JSONObject;

/**
 *	主办单位题库管理-题库
 */
@RestController
@RequestMapping("/htgl/questionDb")
public class QuestionDBController extends BaseController {
	
	@Autowired
	private QuestionDBService service;
	@Autowired
	private PaperRepoService paperRepoService;
	
    @RequestMapping("/list")
    @Operation(desc = "题库列表")
    public Object list(
    				HttpServletRequest request,
    				QuestionDBQueryParams params) throws Exception {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
	 	return service.pageQuery(params);
    }
    
    //新增
    @RequestMapping("/add")
    @Operation(desc = "添加题库")
    public Object add(
    					HttpServletRequest request,
						@RequestBody(required = false) JSONObject json) throws Exception {
		service.add(json, super.getLoginUserId(request));
		return  true;
    }
    
    //删除题库,id是课程题库关系表ID
    @RequestMapping("/deleteById")
    @Operation(desc = "删除题库")
    public Object deleteById(
    					HttpServletRequest request,
    				 	@RequestParam(required = false) String ids) throws Exception {
    	if(StringUtils.isEmpty(ids)){
            throw BizException.withMessage("请选择一条数据");
        }
    	service.batchDelete(ids,getCurrentHostOrgId(request));
	 	return true;
    }
    
    //获取 id是课程题库关系表ID
    @RequestMapping("/getById")
    @Operation(desc = "获取题库")
    public Object getById(
    				 	@RequestParam(required = false) String id) throws Exception {
    	if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择一条数据");
        }
    	
	 	return service.getDbDetailsByQ2cId(id);
    }
    
    @RequestMapping("/edit")
    @Operation(desc = "修改题库")
    public Object edit(
    					HttpServletRequest request,
						@RequestBody(required = false) JSONObject json) throws Exception {
		service.edit(json, super.getLoginUserId(request), super.getCurrentHostOrgId(request));
		return true;
    }
    
    //题库下拉
    @RequestMapping("/select")
    @Operation(desc = "题库下拉选择")
    public Object select(HttpServletRequest request,@RequestParam(required = false) String courseId) throws Exception {
    	QuestionDBQueryParams params = new QuestionDBQueryParams();
    	if (super.getLoginRole(request)!= Role.ADMIN) {
			params.setHostOrgId(super.getCurrentHostOrgId(request));
		}
    	params.setCourseId(courseId);
    	params.setStatus(Zt.OK);
    	params.setSize(Integer.MAX_VALUE);
	 	return service.pageQuery(params).getRecords();
    }
    
    /**
	 * 获取被授权的题库
	 */
	@RequestMapping("/getAssignedDbList")
	@Operation(desc = "获取被授权的课件资源")
	public Object getAssignList(
			HttpServletRequest request
	) throws Exception {
		return service.getAssignedDbList(super.getCurrentHostOrgId(request));
	}
	
	/**
	 * 被授权的题库设置课程
	 */
	@RequestMapping("/setCourse")
	@Operation(desc = "被授权的题库设置课程")
	public Object setCourse(
			HttpServletRequest request,
			@RequestParam(required = false) String q2cId,
			@RequestParam(required = false) String dbId,
			@RequestParam(required = false) String courseId
	) throws Exception {
		if(StringUtils.isEmpty(courseId)){
            throw BizException.withMessage("请选择课程");
        }
		if(StringUtils.isEmpty(dbId)){
            throw BizException.withMessage("请选择题库");
        }
		service.setCourse(q2cId, dbId, courseId, super.getCurrentHostOrgId(request));
		return true;
	}
	
	/**
	 * 根据题库组卷
	 * @param request
	 * @param dbId	题库id
	 * @param paperCount	组卷量
	 * @param questionCount	试卷试题量
	 * @param score	每题分值
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/makePaper")
	@Operation(desc = "题库组卷")
	public Object makePaper(HttpServletRequest request, 
			@RequestParam(required = false) String dbId,
			@RequestParam(required = false) PaperCategory category, 
			@RequestParam(required = false) Integer paperCount,
			@RequestParam(required = false) Integer questionCount, 
			@RequestParam(required = false) Integer score)
			throws Exception {
		if (StringUtils.isEmpty(dbId)) {
			throw BizException.withMessage("请选择题库");
		}
		if (category == null) {
			throw BizException.withMessage("请选择试卷分类");
		}
		if (paperCount == null) {
			throw BizException.withMessage("请输入组卷量");
		} else if (paperCount.intValue() <= 0) {
			throw BizException.withMessage("请输入正确的组卷量");
		}
		if (questionCount == null) {
			throw BizException.withMessage("请输入单卷试题量");
		} else if (questionCount.intValue() <= 0) {
			throw BizException.withMessage("请输入正确的单卷试题量");
		}
		if (score == null) {
			throw BizException.withMessage("请输入每题分值");
		}
		return paperRepoService.makePaper(dbId, category, paperCount, questionCount, score,
				super.getLoginUserId(request), super.getCurrentHostOrgId(request));
	}
	
	/**
	 * 导出word
	 */
	@RequestMapping("/exportWord")
	@Operation(desc = "导出word")
	public void exportWord(HttpServletRequest request, HttpServletResponse response,String dbId) {
		OutputStream os = null;
		try {
			 response.setContentType("text/html;charset=utf-8");
			response.setContentType("application/octet-stream");
			response.setHeader("content-disposition",
					"attachment;filename=word"+BaseUtil.generateId2()+".doc");
			os = response.getOutputStream();
			service.exportWord(dbId, os);
			os.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			BaseUtil.close(os);
		}
	}
	
	/**
	 * 勾选题库组卷
	 * 
	 * @param request
	 * @param json
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/toMakePaper")
	@Operation(desc = "题库组卷")
	public Object toMakePaper(HttpServletRequest request,
			@RequestBody(required = false) com.alibaba.fastjson.JSONObject json) throws Exception {
		json.put("hostOrgId", super.getCurrentHostOrgId(request));
		json.put("creatorId", super.getLoginUserId(request));
		return paperRepoService.toMakePaper(json);
	}

}