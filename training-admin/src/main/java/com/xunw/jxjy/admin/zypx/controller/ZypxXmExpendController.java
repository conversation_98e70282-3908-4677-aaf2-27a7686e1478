package com.xunw.jxjy.admin.zypx.controller;

import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmExpend;
import com.xunw.jxjy.model.zypx.service.ZypxXmExpendService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@RestController
@RequestMapping("/htgl/biz/xm/expand")
public class ZypxXmExpendController extends BaseController {

    @Autowired
    private ZypxXmExpendService zypxXmExpendService;
    @Autowired
    private ZypxXmService zypxXmService;

    @RequestMapping("/add")
    @Operation(desc = "新增")
    public Object saveExpand(HttpServletRequest request,
                             @RequestParam(required = false) String xmId,
                             @RequestParam(required = false) String content,
                             @RequestParam(required = false) Double amount,
                             @RequestParam(required = false) String time,
                             @RequestParam(required = false) String operator) {
        ZypxXmExpend zypxXmExpend = new ZypxXmExpend();
        zypxXmExpend.setId(BaseUtil.generateId());
        zypxXmExpend.setXmId(xmId);
        zypxXmExpend.setContent(content);
        zypxXmExpend.setAmount(amount);
        zypxXmExpend.setTime(DateUtils.parse(time, "yyyy-MM"));
        zypxXmExpend.setOperator(operator);
        zypxXmExpend.check();
        zypxXmExpendService.insert(zypxXmExpend);
        return true;
    }

    @RequestMapping("/getExpendById")
    @Operation(desc = "查询项目支出")
    public Object getById(HttpServletRequest request, @RequestParam(required = false) String xmId) {
        if (BaseUtil.isEmpty(xmId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        ZypxXm zypxXm = zypxXmService.selectById(xmId);
        if (zypxXm == null) {
            throw BizException.withMessage("项目不存在");
        }
        return zypxXmExpendService.getByXmId(xmId);
    }

    @RequestMapping("/delete")
    @Operation(desc = "删除项目支出")
    public Object deleteExpand(HttpServletRequest request, @RequestParam(required = false) String ids) {
        if (BaseUtil.isEmpty(ids)) {
            throw BizException.withMessage("请选择一条数据");
        }
        zypxXmExpendService.deleteBatchIds(Arrays.asList(ids.split(",")));
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改项目支出")
    public Object deleteExpandBatch(HttpServletRequest request,
                                    @RequestParam(required = false) String id,
                                    @RequestParam(required = false) String xmId,
                                    @RequestParam(required = false) String content,
                                    @RequestParam(required = false) Double amount,
                                    @RequestParam(required = false) String time,
                                    @RequestParam(required = false) String operator) {
        if (BaseUtil.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        ZypxXmExpend zypxXmExpend = zypxXmExpendService.selectById(id);
        zypxXmExpend.setXmId(xmId);
        zypxXmExpend.setContent(content);
        zypxXmExpend.setAmount(amount);
        zypxXmExpend.setTime(DateUtils.parse(time, "yyyy-MM"));
        zypxXmExpend.setOperator(operator);
        zypxXmExpend.check();
        zypxXmExpendService.updateById(zypxXmExpend);
        return true;
    }

}
