package com.xunw.jxjy.admin.zypx.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import net.sf.json.JSONObject;

/**
 * 学员信息管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/studentInfo")
public class StudentInfoController {

	@Autowired
	private StudentInfoService service;
	@Autowired
	private StudentUserService studentUserService;

	@RequestMapping("/getById")
    @Operation(desc = "获取学员信息详情")
    public Object getById(
    					HttpServletRequest request,
    					@RequestParam(required = false) String studentId) throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();
		StudentInfo studentInfo = service.getByStudentId(studentId);
		result.put("info", studentInfo);
		StudentUser studentUser = studentUserService.selectUserById(studentId);
		result.put("user", studentUser);
		return result;
    }

	@RequestMapping("/edit")
    @Operation(desc = "编辑学员信息")
    public Object edit(
    					HttpServletRequest request,
    					@RequestBody JSONObject params
    		) throws Exception {
		String name = BaseUtil.getStringValueFromJson(params, "name");
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("姓名不能为空");
		}
		String sfzh = BaseUtil.getStringValueFromJson(params, "sfzh");
		if (StringUtils.isEmpty(sfzh)) {
			throw BizException.withMessage("身份证号不能为空");
		}
		if (!BaseUtil.isIDCardNumber(sfzh)) {
			throw BizException.withMessage("请输入正确的身份证号");
		}
		
		String mobile = BaseUtil.getStringValueFromJson(params, "mobile");
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能为空");
		}
		if (!BaseUtil.isMobile(mobile)) {
			throw BizException.withMessage("请输入正确的11位手机号");
		}
		String studentId = BaseUtil.getStringValueFromJson(params, "studentId");
		if (StringUtils.isEmpty(studentId)) {
			throw BizException.withMessage("学员ID不能为空");
		}
        //判断手机号是否跟之前的不一样
		StudentInfo studentInfo = service.getByStudentId(studentId);
		EntityWrapper<StudentInfo> mobileCheckWrapper = new EntityWrapper<StudentInfo>();
		mobileCheckWrapper.eq("reg_host_org_id", studentInfo.getRegHostOrgId());
		mobileCheckWrapper.eq("mobile", studentInfo.getMobile());
		mobileCheckWrapper.ne("student_id", studentId);
		if (service.selectCount(mobileCheckWrapper) > 0) {
			throw BizException.withMessage("您输入的手机号已经被其他学员使用，请更换手机号");
		}
		String gender = BaseUtil.getStringValueFromJson(params, "gender");
		if (StringUtils.isEmpty(gender)) {
			throw BizException.withMessage("请选择性别");
		}
		if (Gender.findByEnumName(gender) == null) {
			throw BizException.withMessage("系统无法识别传入的性别");
		}
		String studentType = BaseUtil.getStringValueFromJson(params, "studentType");
		if (StringUtils.isEmpty(studentType)) {
			throw BizException.withMessage("请选择学员类型");
		}
		if (StudentType.findByEnumName(studentType) == null) {
			throw BizException.withMessage("系统无法识别传入的学员类型");
		}
		String school = BaseUtil.getStringValueFromJson(params, "school");
		String college= BaseUtil.getStringValueFromJson(params,"college");
		String classz = BaseUtil.getStringValueFromJson(params,"classz");
		String specialty=BaseUtil.getStringValueFromJson(params,"specialty");
		String company=BaseUtil.getStringValueFromJson(params,"company");

		String orgId = BaseUtil.getStringValueFromJson(params, "orgId");
		String zw = BaseUtil.getStringValueFromJson(params, "zw");
		String zc = BaseUtil.getStringValueFromJson(params, "zc");
		String studentNum = BaseUtil.getStringValueFromJson(params, "studentNum");

		if (StudentType.findByEnumName(studentType) == StudentType.SCHOOL) {
			if (StringUtils.isEmpty(school)) {
				throw BizException.withMessage("在校学生必须填写自己所在的学校");
			}
			if (StringUtils.isEmpty(college)) {
				throw BizException.withMessage("在校学生必须填写自己所在的院系");
			}
			if (StringUtils.isEmpty(specialty)) {
				throw BizException.withMessage("在校学生必须填写自己的专业");
			}
			if (StringUtils.isEmpty(classz)) {
				throw BizException.withMessage("在校学生必须填写自己所在的班级");
			}
		}
		if (StudentType.findByEnumName(studentType) == StudentType.SOCIAL) {
			if (StringUtils.isEmpty(company)) {
				throw BizException.withMessage("请填写学员所在的单位");
			}
			if (StringUtils.isEmpty(zw)) {
				throw BizException.withMessage("请输入学员职位");
			}
			if (StringUtils.isEmpty(zc)) {
				throw BizException.withMessage("请选择学员职称");
			}
		}

		String education = BaseUtil.getStringValueFromJson(params, "education");
		if (StringUtils.isNotEmpty(education) &&  Education.findByEnumName(education) == null) {
			throw BizException.withMessage("系统无法识别传入的学历");
		}
		String address = BaseUtil.getStringValueFromJson(params, "address");

		StudentUser studentUser = studentUserService.selectById(studentId);

		//数据合法性校验
		if (StudentType.valueOf(studentType) == StudentType.SCHOOL) {
			orgId = null;
			zw = null;
			zc = null;
			company = null;
			studentInfo.setIsGraduated(Constants.NO);
		}
		else {
			college = null;
			specialty = null;
			classz = null;
			studentNum = null;
			studentInfo.setIsGraduated(Constants.YES);
		}
		if (StringUtils.isNotEmpty(sfzh)) {
			StudentUser check = studentUserService.findBySfzh(sfzh, studentUser.getRegHostOrgId());
			if (check!= null && !check.getId().equals(studentId)) {
				throw BizException.withMessage("身份证号码已经被其他学员使用，请更换身份证号码");
			}
		}

		//修改学员用户信息
		studentUser.setStudentType(StudentType.valueOf(studentType));
		studentUser.setOrgId(orgId);
		studentUser.setCompany(company);
		studentUserService.updateAllColumnById(studentUser);

		//修改学员附加信息
		String studentPhtoto = BaseUtil.getStringValueFromJson(params, "studentPhoto");
		studentInfo.setGender(Gender.findByEnumName(gender));
		studentInfo.setGraduateSchool(school);
		studentInfo.setCollege(college);
		studentInfo.setClassz(classz);
		studentInfo.setSpecialty(specialty);
		if (StringUtils.isNotEmpty(education)) {
			studentInfo.setEducation(Education.valueOf(education));
		}
		studentInfo.setAddress(address);
		studentInfo.setStudentNum(studentNum);
		studentInfo.setName(name);
		studentInfo.setMobile(mobile);
		studentInfo.setStudentPhoto(studentPhtoto);
		studentInfo.setSfzh(sfzh);
		studentInfo.setZw(zw);
		studentInfo.setZc(zc);
		service.updateAllColumnById(studentInfo);
		return true;
	}


}
