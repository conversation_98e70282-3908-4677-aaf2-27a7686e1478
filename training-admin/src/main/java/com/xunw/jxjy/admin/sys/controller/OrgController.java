package com.xunw.jxjy.admin.sys.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.admin.core.dto.LoginUser;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.dto.OrgTree;
import com.xunw.jxjy.model.enums.OrgType;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.OrgRelation;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.params.OrgQueryParams;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.model.sys.service.OrgService;

/**
 * 机构管理
 */
@RestController
@RequestMapping("/htgl/sys")
public class OrgController extends BaseController {

	@Autowired
	private OrgService service;
	@Autowired
	private DictService dictService;

	/**
	 * 查询
	 */
	@RequestMapping("/org/list")
	@Operation(desc = "机构列表")
	public Object list(HttpServletRequest request, OrgQueryParams params) throws Exception {
		LoginUser loginUser = super.getLoginUser(request);
		if (params.getOrgType() == null && StringUtils.isEmpty(params.getParentId())) {
			throw BizException.withMessage("机构类型、上级机构不能同时为空");
		} else {
			if (StringUtils.isEmpty(params.getParentId())) {
				if (loginUser.getRole() == Role.ADMIN) {// 超管不做限制，直接查询顶级机构
					params.setParentId(null);
					params.setIsParent(Constants.YES);
				} else {
					OrgType loginOrgType = loginUser.getOrg().getOrgType();
					Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
					if (params.getOrgType() == OrgType.HOST_ORG) {
						if (loginOrgType == OrgType.HOST_ORG) {
							params.setParentId(topOrg.getId());
						} else if (loginOrgType == OrgType.ORG) {
							List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
							params.setParentId(orgs.get(0).getId());
						} else if (loginOrgType == OrgType.ENTRUST_ORG) {
							List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
							params.setParentId(orgs.get(0).getId());
						}
					} else if (params.getOrgType() == OrgType.ORG) {
						if (loginOrgType == OrgType.HOST_ORG) {
							params.setHostOrgId(topOrg.getId());
						} else if (loginOrgType == OrgType.ORG) {
							params.setParentId(topOrg.getId());
						}
					} else if (params.getOrgType() == OrgType.ENTRUST_ORG) {
						if (loginOrgType == OrgType.HOST_ORG) {
							params.setHostOrgId(topOrg.getId());
						} else if (loginOrgType == OrgType.ENTRUST_ORG) {
							params.setParentId(topOrg.getId());
						}
					}
				}
			}
		}
		return service.list(params);
	}

	/**
	 * 添加机构
	 */
	@RequestMapping("/org/add")
	@Operation(desc = "添加机构")
	public Object add(HttpServletRequest request, @RequestParam(required = false) OrgType type,
			@RequestParam(required = false) String parentId,@RequestParam(required = false) String code,
			@RequestParam(required = false) String name, @RequestParam(required = false) String remark,
			@RequestParam(required = false) String contact, @RequestParam(required = false) String telephone,
			@RequestParam(required = false) String adminDomain, @RequestParam(required = false) String adminSysName,
			@RequestParam(required = false) String portalDomain, @RequestParam(required = false) String portalSysName,
			@RequestParam(required = false) String smsSign, @RequestParam(required = false) String isPortalCustomized,
			@RequestParam(required = false) String provinceCode, @RequestParam(required = false) String cityCode,
			@RequestParam(required = false) String isCrpIntegrate, @RequestParam(required = false) String isCollege,
			@RequestParam(required = false) String districtCode, @RequestParam(required = false) String nature,
		  	@RequestParam(required = false) String adminLogo, @RequestParam(required = false) String portalLogo,
		  	@RequestParam(required = false) String iosAppid,@RequestParam(required = false) String appletQrCode) throws Exception {
		if (type == null) {
			throw BizException.withMessage("机构类型不能够为空");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("机构名称不能够为空");
		}
		if (StringUtils.isNotEmpty(parentId)) {
			Org parentOrg = service.selectById(parentId);
			if (parentOrg == null) {
				throw BizException.withMessage("上级机构不存在");
			}
		}
		LoginUser loginUser = super.getLoginUser(request);
		if (StringUtils.isEmpty(parentId) && type == OrgType.HOST_ORG && loginUser.getRole() != Role.ADMIN) {
			throw BizException.withMessage("只有超管可以进行此操作");
		}
		if (StringUtils.isEmpty(parentId) && type == OrgType.ORG && getLoginOrgType(request) != OrgType.HOST_ORG) {
			throw BizException.withMessage("只有主办单位用户可以进行此操作");
		}
		if (StringUtils.isEmpty(parentId) && type == OrgType.ENTRUST_ORG && getLoginOrgType(request) != OrgType.HOST_ORG) {
			throw BizException.withMessage("只有主办单位用户可以进行此操作");
		}
		//code不传就随机生成一个
		code = StringUtils.isEmpty(code)?"ORG_"+System.currentTimeMillis():code;
		Org checkOrg = service.findByCode(code);
		if (checkOrg != null) {
			throw BizException.withMessage("机构代码已经被使用，请更换您的机构代码");
		}
		Org org = new Org();
		org.setId(BaseUtil.generateId2());
		org.setCode(code);
		org.setName(name);
		org.setOrgType(type);
		org.setRemark(remark);
		org.setParentId(parentId);
		org.setCreateTime(new Date());
		org.setCreatorId(loginUser.getUser().getId());
		org.setStatus(Zt.OK);
		org.setIsPortalCustomized(isPortalCustomized);
		org.setNature(nature);
		org.setAdminLogo(adminLogo);
		org.setPortalLogo(portalLogo);
		org.setIosAppid(iosAppid);
		org.setAppletQrCode(appletQrCode);
		if (StringUtils.isEmpty(parentId)) {
			org.setIsParent(Constants.YES);
			if (org.getOrgType() == OrgType.HOST_ORG) {
				// 门户
				if (StringUtils.isNotEmpty(portalDomain)) {
					domainUrlCheck(portalDomain);
					EntityWrapper<Org> orgWrapper = new EntityWrapper();
					orgWrapper.eq("portal_domain", portalDomain);
					if (service.selectCount(orgWrapper) > 0) {
						throw BizException.withMessage("您输入的门户域名已经有其他主办单位使用，请更换");
					}
					org.setPortalDomain(portalDomain);
				} else {
					throw BizException.withMessage("主办单位门户域名不能为空");
				}
				if (StringUtils.isNotEmpty(portalSysName)) {
					org.setPortalSysName(portalSysName);
				} else {
					throw BizException.withMessage("主办单位门户系统名称不能为空");
				}
				// 管理端
				if (StringUtils.isNotEmpty(adminDomain)) {
					domainUrlCheck(adminDomain);
					EntityWrapper<Org> orgWrapper = new EntityWrapper();
					orgWrapper.eq("admin_domain", adminDomain);
					if (service.selectCount(orgWrapper) > 0) {
						throw BizException.withMessage("您输入的管理端域名已经有其他主办单位使用，请更换");
					}
					org.setAdminDomain(adminDomain);
				} else {
					throw BizException.withMessage("主办单位管理端域名不能为空");
				}
				if (StringUtils.isNotEmpty(adminSysName)) {
					org.setAdminSysName(adminSysName);
				} else {
					throw BizException.withMessage("主办单位管理端系统名称不能为空");
				}
				CacheHelper.removeCache(Constants.HOST_ORG_DOMAIN_CACHE);// 清空主办单位缓存数据
				org.setSmsSign(smsSign);
			}
		}
		OrgRelation orgRelation = null;
		//主办单位用户 新增培训机构、委托单位，则需要添加主办单位与培训机构、委托单位的关联关系
		if (getLoginOrgType(request) == OrgType.HOST_ORG && (org.getOrgType() == OrgType.ORG || org.getOrgType() == OrgType.ENTRUST_ORG)
				&& StringUtils.isEmpty(parentId)) {
			orgRelation = new OrgRelation();
			orgRelation.setId(BaseUtil.generateId2());
			orgRelation.setHostOrgId(super.getCurrentHostOrgId(request));
			orgRelation.setOrgId(org.getId());
			if (org.getOrgType() == OrgType.ENTRUST_ORG && StringUtils.isEmpty(nature)) {
				throw BizException.withMessage("委托单位请填写单位性质");
			}
		}
		if (StringUtils.isNotEmpty(provinceCode)) {
			// 判断省级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("province", provinceCode) == null) {
				throw BizException.withMessage("选择的省份不存在.");
			}
		}
		org.setProvinceCode(provinceCode);
		if (StringUtils.isNotEmpty(cityCode)) {
			// 判断市级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("city", cityCode) == null) {
				throw BizException.withMessage("选择的市级不存在.");
			}
		}
		org.setCityCode(cityCode);
		if (StringUtils.isNotEmpty(districtCode)) {
			// 判断区级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("district", districtCode) == null) {
				throw BizException.withMessage("选择的县区不存在.");
			}
		}
		org.setDistrictCode(districtCode);
		org.setContact(contact);
		org.setIsCollege(isCollege);
		org.setTelephone(telephone);
		org.setIsCrpIntegrate(isCrpIntegrate);
		service.add(org, orgRelation);
		return true;
	}

	/**
	 * 校验主办单位中的域名设置是否正确，不能够包含http、https
	 */
	private void domainUrlCheck(String domainUrl) {
		if (domainUrl.indexOf("https") > -1 || domainUrl.indexOf("http") > -1 || domainUrl.indexOf("/") > -1) {
			throw BizException.withMessage("域名格式不正确，不能包含https、http以及子目录，请直接输入域名，如:jxjy.whxunw.com");
		}
	}

	/**
	 * 查询
	 */
	@RequestMapping("/org/getById")
	@Operation(desc = "机构详情")
	public Object getById(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("机构id不能够为空");
		}
		return service.selectById(id);
	}

	/**
	 * 修改机构
	 */
	@RequestMapping("/org/edit")
	@Operation(desc = "修改机构")
	public Object add(HttpServletRequest request, @RequestParam(required = false) String id,
			@RequestParam(required = false) String code, @RequestParam(required = false) String name,
			@RequestParam(required = false) String contact, @RequestParam(required = false) String telephone,
			@RequestParam(required = false) String adminDomain, @RequestParam(required = false) String adminSysName,
			@RequestParam(required = false) String remark, @RequestParam(required = false) String portalDomain,
			@RequestParam(required = false) String portalSysName, @RequestParam(required = false) String smsSign,
			@RequestParam(required = false) String isPortalCustomized,@RequestParam(required = false) String isCrpIntegrate,
			@RequestParam(required = false) String provinceCode, @RequestParam(required = false) String cityCode,
			@RequestParam(required = false) String isCollege, @RequestParam(required = false) String districtCode,
		  	@RequestParam(required = false) String nature, @RequestParam(required = false) String adminLogo,
		  	@RequestParam(required = false) String portalLogo, @RequestParam(required = false) String iosAppid,
		  	@RequestParam(required = false) String appletQrCode) throws Exception {
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("机构代码不能够为空");
		}
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("机构名称不能够为空");
		}
		Org checkOrg = service.findByCode(code);
		if (checkOrg != null && !checkOrg.getId().equals(id)) {
			throw BizException.withMessage("机构代码已经被使用，请更换您的机构代码");
		}
		Org org = service.selectById(id);
		if (org == null) {
			throw BizException.withMessage("机构不存在");
		}
		User user = super.getLoginUser(request).getUser();
		org.setCode(code);
		org.setName(name);
		org.setRemark(remark);
		org.setUpdateTime(new Date());
		org.setUpdatorId(user.getId());
		org.setIsPortalCustomized(isPortalCustomized);
		org.setNature(nature);
		org.setAdminLogo(adminLogo);
		org.setPortalLogo(portalLogo);
		org.setIosAppid(iosAppid);
		org.setAppletQrCode(appletQrCode);
		if (org.getOrgType() == OrgType.HOST_ORG && Constants.YES.equals(org.getIsParent())) {
			if (StringUtils.isNotEmpty(portalDomain)) {
				domainUrlCheck(portalDomain);
				EntityWrapper<Org> orgWrapper = new EntityWrapper();
				orgWrapper.eq("portal_domain", portalDomain);
				List<Org> checkList = service.selectList(orgWrapper);
				if (checkList.size() > 0 && !checkList.get(0).getId().equals(org.getId())) {
					throw BizException.withMessage("您输入的门户域名已经有其他主办单位使用，请更换");
				}
				org.setPortalDomain(portalDomain);
			} else {
				throw BizException.withMessage("主办单位门户域名不能为空");
			}
			if (StringUtils.isNotEmpty(portalSysName)) {
				org.setPortalSysName(portalSysName);
			} else {
				throw BizException.withMessage("主办单位门户系统名称不能为空");
			}
			if (StringUtils.isNotEmpty(adminDomain)) {
				domainUrlCheck(adminDomain);
				EntityWrapper<Org> orgWrapper = new EntityWrapper();
				orgWrapper.eq("admin_domain", adminDomain);
				List<Org> checkList = service.selectList(orgWrapper);
				if (checkList.size() > 0 && !checkList.get(0).getId().equals(org.getId())) {
					throw BizException.withMessage("您输入的管理端域名已经有其他主办单位使用，请更换");
				}
				org.setAdminDomain(adminDomain);
			} else {
				throw BizException.withMessage("主办单位管理端域名不能为空");
			}
			if (StringUtils.isNotEmpty(adminSysName)) {
				org.setAdminSysName(adminSysName);
			} else {
				throw BizException.withMessage("主办单位管理端系统名称不能为空");
			}
			org.setSmsSign(smsSign);
			CacheHelper.removeCache(Constants.HOST_ORG_DOMAIN_CACHE);// 清空主办单位缓存数据
		}
		if (StringUtils.isNotEmpty(provinceCode)) {
			// 判断省级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("province", provinceCode) == null) {
				throw BizException.withMessage("选择的省份不存在.");
			}
		}
		org.setProvinceCode(provinceCode);
		if (StringUtils.isNotEmpty(cityCode)) {
			// 判断市级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("city", cityCode) == null) {
				throw BizException.withMessage("选择的市级不存在.");
			}
		}
		org.setCityCode(cityCode);
		if (StringUtils.isNotEmpty(districtCode)) {
			// 判断区级编码是否存在
			if (dictService.getDictByDictCodeAndDictValue("district", districtCode) == null) {
				throw BizException.withMessage("选择的县区不存在.");
			}
		}
		org.setDistrictCode(districtCode);
		org.setContact(contact);
		org.setTelephone(telephone);
		org.setIsCrpIntegrate(isCrpIntegrate);
		org.setIsCollege(isCollege);
		service.updateById(org);
		return true;
	}

	/**
	 * 删除机构
	 */
	@RequestMapping("/org/deleteById")
	@Operation(desc = "删除机构")
	public Object deleteById(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("机构id不能够为空");
		}
		if (service.getChildren(id).size() > 0) {
			throw BizException.withMessage("操作失败，当前机构存在子部门，无法删除");
		}
		if (service.getSysUser(id).size() > 0) {
			throw BizException.withMessage("操作失败，当前机构下存在系统用户，无法删除");
		}
		if (service.getStudentUser(id).size() > 0) {
			throw BizException.withMessage("操作失败，当前机构下存在学员用户，无法删除");
		}
		CacheHelper.removeCache(Constants.HOST_ORG_DOMAIN_CACHE);// 清空主办单位缓存数据
		service.deleteById(id);
		return true;
	}

	/**
	 *  机构部门树，一次性返回机构部门所有的组织架构
	 */
	@RequestMapping("/org/{orgType}/tree")
	@Operation(desc = "机构树")
	public Object tree(HttpServletRequest request, @PathVariable(required = true) OrgType orgType) throws Exception {
		if (orgType != OrgType.HOST_ORG && orgType != OrgType.ORG && orgType != OrgType.ENTRUST_ORG) {
			throw BizException.withMessage("机构类型错误，只支持主办单位、培训机构、委托单位");
		}
		LoginUser loginUser = super.getLoginUser(request);
		OrgQueryParams params = new OrgQueryParams();
		params.setOrgType(orgType);
		if (loginUser.getRole() == Role.ADMIN) {
			params.setParentId(null);
		} else {
			OrgType loginOrgType = loginUser.getOrg().getOrgType();
			Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
			if (params.getOrgType() == OrgType.HOST_ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setParentId(topOrg.getId());
				} else if (loginOrgType == OrgType.ORG) {
					List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
					params.setParentId(orgs.get(0).getId());
				} else if (loginOrgType == OrgType.ENTRUST_ORG) {
					List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
					params.setParentId(orgs.get(0).getId());
				}
			} else if (params.getOrgType() == OrgType.ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setHostOrgId(topOrg.getId());
				} else if (loginOrgType == OrgType.ORG) {
					params.setParentId(topOrg.getId());
				}
			} else if (params.getOrgType() == OrgType.ENTRUST_ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setHostOrgId(topOrg.getId());
				} else if (loginOrgType == OrgType.ENTRUST_ORG) {
					params.setParentId(topOrg.getId());
				}
			}
		}
		List<OrgTree> tree = service.tree(params);
		return tree;
	}


	/**
	 * 机构部门用户树
	 */
	@RequestMapping("/org/{orgType}/orgUserTree")
	@Operation(desc = "机构树")
	public Object selectTree(HttpServletRequest request, @PathVariable(required = true) OrgType orgType,@RequestParam(required = false) Role role) throws Exception {
		if (orgType != OrgType.HOST_ORG && orgType != OrgType.ORG && orgType != OrgType.ENTRUST_ORG) {
			throw BizException.withMessage("机构类型错误，只支持主办单位、培训机构、委托单位");
		}
		LoginUser loginUser = super.getLoginUser(request);
		OrgQueryParams params = new OrgQueryParams();
		params.setOrgType(orgType);
		if (role != null){
			params.setRole(role);
		}
		if (loginUser.getRole() == Role.ADMIN) {
			params.setParentId(null);
		} else {
			OrgType loginOrgType = loginUser.getOrg().getOrgType();
			Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
			if (params.getOrgType() == OrgType.HOST_ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setParentId(topOrg.getId());
				} else if (loginOrgType == OrgType.ORG) {
					List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
					params.setParentId(orgs.get(0).getId());
				} else if (loginOrgType == OrgType.ENTRUST_ORG) {
					List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
					params.setParentId(orgs.get(0).getId());
				}
			} else if (params.getOrgType() == OrgType.ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setHostOrgId(topOrg.getId());
				} else if (loginOrgType == OrgType.ORG) {
					params.setParentId(topOrg.getId());
				}
			} else if (params.getOrgType() == OrgType.ENTRUST_ORG) {
				if (loginOrgType == OrgType.HOST_ORG) {
					params.setHostOrgId(topOrg.getId());
				} else if (loginOrgType == OrgType.ENTRUST_ORG) {
					params.setParentId(topOrg.getId());
				}
			}
		}
		List<OrgTree> tree = service.orgUserTree(params);
		return tree;
	}

	/**
	 * 主办单位下拉框
	 */
	@RequestMapping("/hostOrg/select")
	@Operation(desc = "主办单位下拉框")
	public Object hostOrgSelect(HttpServletRequest request) throws Exception {
		LoginUser loginUser = super.getLoginUser(request);
		// 超级管理员查询所有的主办单位
		if (loginUser.getRole() == Role.ADMIN) {
			EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
			wrapper.eq("org_type", OrgType.HOST_ORG);
			wrapper.eq("is_parent", 1);
			wrapper.orderBy("code", true);
			return service.selectList(wrapper);
		} else {
			OrgType loginOrgType = loginUser.getOrg().getOrgType();
			Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
			if (loginOrgType == OrgType.HOST_ORG) {
				List<Org> list = new ArrayList<Org>();
				list.add(topOrg);
				return list;
			} else if (loginOrgType == OrgType.ORG) {
				List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
				return orgs;
			} else if (loginOrgType == OrgType.ENTRUST_ORG) {
				List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
				return orgs;
			}
			return Collections.EMPTY_LIST;
		}
	}

	/**
	 * 培训机构下拉框
	 */
	@RequestMapping("/org/select")
	@Operation(desc = "培训机构下拉框")
	public Object orgSelect(HttpServletRequest request) throws Exception {
		LoginUser loginUser = super.getLoginUser(request);
		// 超级管理员查询所有的培训机构
		if (loginUser.getRole() == Role.ADMIN) {
			EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
			wrapper.eq("org_type", OrgType.ORG);
			wrapper.eq("is_parent", 1);
			wrapper.orderBy("code", true);
			return service.selectList(wrapper);
		} else {
			OrgType loginOrgType = loginUser.getOrg().getOrgType();
			Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
			if (loginOrgType == OrgType.HOST_ORG) {
				List<Org> list = service.getRelatedOrgByHostOrgId(topOrg.getId());
				return list;
			} else if (loginOrgType == OrgType.ORG) {
				List<Org> orgs = new ArrayList<Org>();
				orgs.add(topOrg);
				return orgs;
			}
			return Collections.EMPTY_LIST;
		
		}
	}
	
	/**
	 * 委托单位下拉框
	 */
	@RequestMapping("/entrustOrg/select")
	@Operation(desc = "委托单位下拉框")
	public Object cooperOrgSelect(HttpServletRequest request) throws Exception {
		LoginUser loginUser = super.getLoginUser(request);
		// 超级管理员查询所有的委托单位
		if (loginUser.getRole() == Role.ADMIN) {
			EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
			wrapper.eq("org_type", OrgType.ENTRUST_ORG);
			wrapper.eq("is_parent", 1);
			wrapper.orderBy("code", true);
			return service.selectList(wrapper);
		} else {
			OrgType loginOrgType = loginUser.getOrg().getOrgType();
			Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
			if (loginOrgType == OrgType.HOST_ORG) {
				List<Org> list = service.getRelatedEntrustOrgByHostOrgId(topOrg.getId());
				return list;
			} 
			else if (loginOrgType == OrgType.ORG) {
				List<Org> orgs = service.getRelatedHostOrgByOrgId(topOrg.getId());
				List<Org> list = service.getRelatedEntrustOrgByHostOrgId(orgs.get(0).getId());
				return list;
			}
			else if (loginOrgType == OrgType.ENTRUST_ORG) {
				List<Org> orgs = new ArrayList<Org>();
				orgs.add(topOrg);
				return orgs;
			}
			return Collections.EMPTY_LIST;
		}
	}

	/**
	 * 机构部门树-异步，只查询一层节点
	 */
	@RequestMapping("/org/{orgType}/asyncTree")
	@Operation(desc = "机构部门树-异步")
	public Object getChildren(HttpServletRequest request, @PathVariable(required = true) OrgType orgType,
			@RequestParam(required = false) String parentId, @RequestParam(required = false) String nature) {
		LoginUser loginUser = super.getLoginUser(request);
		if (StringUtils.isEmpty(parentId)) {
			if (loginUser.getRole() == Role.ADMIN) {
				EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
				wrapper.eq("org_type", orgType);
				wrapper.eq("is_parent", 1);
				wrapper.orderBy("code", true);
				return service.selectList(wrapper);
			} else {
				OrgType loginOrgType = loginUser.getOrg().getOrgType();
				Org topOrg = service.getTopOrg(loginUser.getOrg().getId());
				if (orgType == OrgType.ORG) {
					if (loginOrgType == OrgType.HOST_ORG) {
						List<Org> orgs = service.getRelatedOrgByHostOrgId(topOrg.getId());
						return orgs;
					}
					else if (loginOrgType == OrgType.ORG) {
						List<Org> orgs = new ArrayList<Org>();
						orgs.add(topOrg);
						return orgs;
					}
				}
				else if (orgType == OrgType.ENTRUST_ORG) {
					if (loginOrgType == OrgType.HOST_ORG) {
						List<Org> orgs = service.getRelatedEntrustOrgByHostOrgId(topOrg.getId());
						return StringUtils.isNotEmpty(nature) ? orgs.stream().filter(x->nature.equals(x.getNature())).
								collect(Collectors.toList()) : orgs;
					}
					else if (loginOrgType == OrgType.ENTRUST_ORG) {
						List<Org> orgs = new ArrayList<Org>();
						orgs.add(topOrg);
						return orgs;
					}
				}
				return Collections.EMPTY_LIST;
			}
		} else {
			return service.getChildren(parentId);
		}
	}
	
	/**
	 * 获取主办单位下的承办单位，只取二级单位
	 */
	@RequestMapping("/receiveOrg/select")
	@Operation(desc = "获取主办单位下的承办单位")
	public Object collegeSelect(HttpServletRequest request) throws Exception {
		Org org = super.getLoginOrg(request);
		if (org.getOrgType() == OrgType.HOST_ORG) {
			Org topOrg = service.getTopOrg(org.getId());
			List<Org> orgList = service.getChildren(topOrg.getId());
			return orgList;
		}
		else {
			Org topOrg = service.getTopOrg(org.getId());
			List<Org> orgList = service.getRelatedHostOrgByOrgId(topOrg.getId());
			orgList = service.getChildren(orgList.get(0).getId());
			return orgList;
		}
	}

}
