package com.xunw.jxjy.admin.sys.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.OrgType;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.params.StudentUserQueryParams;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/**
 * 学员用户管理
 */
@RestController
@RequestMapping("/htgl/sys/studentUser")
public class StudentUserController extends BaseController {

    @Autowired
    private StudentUserService service;

    //查询
    @RequestMapping("/list")
    @Operation(desc = "学员用户列表查询")
    public Object list(HttpServletRequest request,
    				 	StudentUserQueryParams params) throws Exception {
    	params.setRegHostOrgId(super.getCurrentHostOrgId(request));
    	if (super.getLoginRole(request) == Role.ORG) {
			params.setOrgId(super.getLoginTopOrg(request).getId());
		}
        return service.pageQuery(params);
    }

    //启用/停用
    @RequestMapping("/enable")
    @Operation(desc = "启用或者禁用学员用户")
    public Object enable(
    				 	@RequestParam(required = false) String id,
    				 	@RequestParam(required = false) AccountStatus status) throws Exception {
    	if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据");
    	}
    	if(status == null){
    		throw BizException.withMessage("状态不能为空");
    	}
    	StudentUser studentUser = service.selectById(id);
        if (studentUser==null){
        	throw BizException.withMessage("学员用户不存在");
        }
        studentUser.setStatus(status);
        service.updateById(studentUser);
    	return true;
    }

    /**
     * 重置密码，支持批量操作，多个ID值用逗号分开
     */
    @RequestMapping("/resetPassword")
    @Operation(desc = "支持批量操作，多个ID值用逗号分开")
    public Object resetPassword(
    				 	@RequestParam(required = false) String id) throws Exception {
    	if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据");
    	}
    	service.resetPasswords(id);
        return true;
    }

    /**
     * 设置培训机构，支持批量操作，多个ID值用逗号分开
     */
    @RequestMapping("/batchSetOrg")
    @Operation(desc = "支持批量操作，多个ID值用逗号分开")
    public Object batchSetOrg(
    		@RequestParam(required = false) String ids, @RequestParam(required = false) String orgId) throws Exception {
    	if(StringUtils.isEmpty(ids)){
    		throw BizException.withMessage("请选择学员");
    	}
    	if(StringUtils.isEmpty(orgId)){
    		throw BizException.withMessage("请选择培训机构");
    	}
    	service.batchSetOrg(ids, orgId);
    	return true;
    }

    //批量设置密码
    @RequestMapping("/batchSetPassword")
    @Operation(desc = "批量设置密码")
    public Object batchSetPassword(
    				 	@RequestParam(required = false) String ids, @RequestParam(required = false) String password) throws Exception {
    	if(StringUtils.isEmpty(ids)){
    		throw BizException.withMessage("请选择一条数据");
    	}
    	if (StringUtils.isEmpty(password)) {
    		throw BizException.withMessage("请输入新的密码");
		}
    	String[] idArray = StringUtils.split(ids, ",");
    	if (idArray == null || idArray.length == 0) {
    		throw BizException.withMessage("请选择一条数据");
		}
    	if (BaseUtil.isRawPassword(password)) {
			throw BizException.withMessage("密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合，不支持其他字符");
		}
    	StudentUser studentUser = new StudentUser();
    	studentUser.setPassword(DigestUtils.md5Hex(password));
    	EntityWrapper<StudentUser> wrapper = new EntityWrapper();
    	wrapper.in("id", idArray);
    	service.update(studentUser, wrapper);
        return true;
    }

    @RequestMapping("/import")
    @Operation(desc = "批量导入学员用户信息")
    public Object importStudentUser(
    					HttpServletRequest request,
    					@RequestParam(value = "file") MultipartFile file) throws Exception {
    	return service.batchImport(file, super.getCurrentHostOrgId(request));
    }

    @RequestMapping("/importAvatar")
	@Operation(desc = "批量导入学员头像")
	public Object importAvatar(
			HttpServletRequest request,
			@RequestParam(value = "file") MultipartFile file) throws Exception {
        if(file == null){
            throw BizException.withMessage("请上传文件");
        }
        if((".zip").equals(FileHelper.getExtension(file.getName()))){
			throw BizException.withMessage("请上传zip文件");
		}
		return service.batchImportAvatar(file, super.getCurrentHostOrgId(request));
	}

    @RequestMapping("/deleteByStudentId")
    @Operation(desc = "删除学员信息")
    public Object deleteById(
    					HttpServletRequest request,
    					@RequestParam String id) throws Exception {
    	service.deleteByStudentId(id);
    	return true;
    }

	@RequestMapping("/merge/pre")
	@Operation(desc = "账号合并前订单查询")
	public Object mergePre(@RequestParam String studentId1,@RequestParam String studentId2) throws Exception {
		return service.mergePre(studentId1, studentId2);
	}

	/**
	 *  账号合并 用户主动选择以哪个为主
	 * @param oldStudentId 需删除得用户id
	 * @param newStudentId 保留得用户id
	 */
    @RequestMapping("/merge")
    @Operation(desc = "账号合并")
    public Object merge(
    					HttpServletRequest request,
    					@RequestParam String oldStudentId,@RequestParam String newStudentId) throws Exception {
    	service.merge(oldStudentId, newStudentId);
    	return true;
    }

    //导出
    @RequestMapping("/export")
    @Operation(desc = "导出学员用户信息")
    public void export(
						HttpServletRequest request,
    					HttpServletResponse response,
    					StudentUserQueryParams params) throws Exception {
    	response.setContentType("text/html;charset=utf-8");
    	response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			if(getLoginOrgType(request) != OrgType.HOST_ORG){
				params.setOrgId(getLoginOrg(request).getId());
			}
			params.setRegHostOrgId(getCurrentHostOrgId(request));
			service.export(params, os);
			os.flush();
		}
		finally {
			if(os!=null) {
				IOUtils.closeQuietly(os);
			}
		}
    }
}
