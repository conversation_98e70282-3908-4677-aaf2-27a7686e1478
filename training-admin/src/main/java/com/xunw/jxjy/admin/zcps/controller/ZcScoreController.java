package com.xunw.jxjy.admin.zcps.controller;

import ch.qos.logback.core.spi.LifeCycle;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.admin.core.annotation.Operation;
import com.xunw.jxjy.admin.core.base.BaseController;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.zyjd.entity.ZcThesisScore;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.params.ZcpsScoreParams;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmQueryParams;
import com.xunw.jxjy.model.zyjd.service.ZyjdBmService;
import jxl.read.biff.BiffException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 答辩评审成绩管理
 */
@RestController
@RequestMapping("/htgl/biz/zcps/score")
public class ZcScoreController extends BaseController {

	Logger LOGGER = org.slf4j.LoggerFactory.getLogger(ZcScoreController.class);

	@Autowired
	private ZyjdBmService zyjdBmService;

	@RequestMapping("/list")
	@Operation(desc = "成绩列表")
	public Object pageQuery(HttpServletRequest request, ZcpsScoreParams params) {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setLoginUserId(super.getLoginRole(request) == Role.TEACHER ? super.getLoginUserId(request) : null);
		return zyjdBmService.zcpsScorePage(params);
	}

	@RequestMapping("/psList")
	@Operation(desc = "评审列表")
	public Object psList(HttpServletRequest request, ZcpsScoreParams params) {
		if (super.getLoginRole(request) != Role.TEACHER) {
			throw BizException.withMessage("权限不足");
		}
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setLoginUserId(super.getLoginUserId(request));
		params.setGroupType("2");
		return zyjdBmService.psList(params);
	}

	@RequestMapping("/zyList")
	@Operation(desc = "争议列表")
	public Object zyList(HttpServletRequest request, ZcpsScoreParams params) {
		if (super.getLoginRole(request) != Role.TEACHER) {
			throw BizException.withMessage("权限不足");
		}
		params.setHostOrgId(super.getCurrentHostOrgId(request));
//		params.setLoginUserId(super.getLoginUserId(request));
		return zyjdBmService.zyList(params);
	}

	/**
	 * 争议投票
	 */
	@RequestMapping("/zyVote")
	@Operation(desc = "争议投票")
	public Object zyVote(HttpServletRequest request,
						 @RequestParam(required = false) String bmId,
						 @RequestParam(required = false) String grade) {
		if (super.getLoginRole(request) != Role.HOST_ORG && super.getLoginRole(request) != Role.TEACHER) {
			throw BizException.withMessage("暂无权限");
		}
		if (StringUtils.isEmpty(bmId)) {
			throw BizException.withMessage("报名id不能为空");
		}
		if (StringUtils.isEmpty(grade)) {
			throw BizException.withMessage("投票结果不能为空");
		}
		zyjdBmService.zyVote(bmId, grade, super.getLoginUserId(request));
		return true;
	}

	/**
	 * 争议投票详情
	 */
	@RequestMapping("/voteDetail")
	@Operation(desc = "争议投票详情")
	public Object voteDetail(HttpServletRequest request,
							 @RequestParam(required = false) String bmId) {
		if (StringUtils.isEmpty(bmId)) {
			throw BizException.withMessage("报名id不能为空");
		}
		return zyjdBmService.voteDetail(bmId);
	}

	/**
	 * 组长最终评分
	 * @param grade 最终成绩 AA AB BB CC
	 */
	@RequestMapping("/finalScore")
	@Operation(desc = "组长最终评分")
	public Object finalScore(HttpServletRequest request,
							 @RequestParam(required = false) String bmId,
							 @RequestParam(required = false) String grade,
							 @RequestParam(required = false) String remark){
		if (super.getLoginRole(request) != Role.HOST_ORG && super.getLoginRole(request) != Role.TEACHER) {
			throw BizException.withMessage("暂无权限");
		}
		if (StringUtils.isEmpty(bmId)) {
			throw BizException.withMessage("报名id不能为空");
		}
		if (StringUtils.isEmpty(grade)) {
			throw BizException.withMessage("投票结果不能为空");
		}
		if (grade.length() != 2) {
			throw BizException.withMessage("投票结果有误");
		}
		zyjdBmService.finalScore(bmId, grade, remark,super.getLoginUserId(request));
		return true;
	}

	@RequestMapping("/batchImport")
	@Operation(desc = "成绩批量导入")
	public Object add(HttpServletRequest request,
					  @RequestParam(required = false) String bmbatchId,
					  @RequestParam(value = "file") MultipartFile file) throws BiffException, IOException {
		if (super.getLoginRole(request) != Role.HOST_ORG && super.getLoginRole(request) != Role.TEACHER) {
			return BizException.withMessage("暂无权限");
		}
		if (StringUtils.isEmpty(bmbatchId)) {
			return BizException.withMessage("批次id不能为空");
		}
		return zyjdBmService.zcpsScoreImport(file, bmbatchId, super.getLoginUserId(request));
	}

	/**
	 * 评审打分
	 */
	@RequestMapping("/psMark")
	@Operation(desc = "评审打分")
	public Object psMark(HttpServletRequest request,
						 @RequestParam(required = false) String bmId,
						 ZyjdBm zyjdBm) {
		if (super.getLoginRole(request) != Role.HOST_ORG && super.getLoginRole(request) != Role.TEACHER) {
			throw BizException.withMessage("暂无权限");
		}
		if (StringUtils.isEmpty(bmId)) {
			throw BizException.withMessage("报名id不能为空");
		}
		if (zyjdBm.getExamScore() == null) {
			throw BizException.withMessage("理论考试得分不能为空");
		}
		if (zyjdBm.getSkillScore() == null) {
			throw BizException.withMessage("操作技能得分不能为空");
		}
		if (zyjdBm.getJobScore() == null) {
			throw BizException.withMessage("工作业绩得分不能为空");
		}
		if (zyjdBm.getPotentialScore() == null) {
			throw BizException.withMessage("潜在能力得分不能为空");
		}
		if (zyjdBm.getSynthesizeScore() == null) {
			throw BizException.withMessage("综合评定不能为空");
		}
		if (zyjdBm.getThesisScore() == null) {
			throw BizException.withMessage("论文成绩不能为空");
		}
		if (zyjdBm.getScore() == null) {
			throw BizException.withMessage("评审得分不能为空");
		}
		zyjdBmService.psMark(bmId, zyjdBm, super.getLoginUserId(request));
		return true;
	}

	/**
	 * 评审打分
	 */
	@RequestMapping("/psMarkDetail")
	@Operation(desc = "打分")
	public Object psMarkDetail(HttpServletRequest request,
						 @RequestParam(required = false) String bmId) {
		if (StringUtils.isEmpty(bmId)) {
			return BizException.withMessage("报名id不能为空");
		}
		return zyjdBmService.psMarkDetail(bmId, super.getLoginUserId(request));
	}

	/**
	 * 打分
	 */
	@RequestMapping("/mark")
	@Operation(desc = "打分")
	public Object mark(HttpServletRequest request,
					   @RequestParam(required = false) String files,
					   @Valid ZcThesisScore zcThesisScore) {
		if (super.getLoginRole(request) != Role.HOST_ORG && super.getLoginRole(request) != Role.TEACHER) {
			return BizException.withMessage("暂无权限");
		}
		if (StringUtils.isEmpty(zcThesisScore.getBmId())) {
			return BizException.withMessage("报名id不能为空");
		}
		zyjdBmService.zcpsMark(zcThesisScore, files, super.getLoginUserId(request));
		return true;
	}


	/**
	 * 打分详情
	 */
	@RequestMapping("/markDetail")
	@Operation(desc = "打分详情")
	public Object mark(HttpServletRequest request,
					   @RequestParam(required = false) String bmId) {
		if (StringUtils.isEmpty(bmId)) {
			return BizException.withMessage("报名id不能为空");
		}
		return zyjdBmService.markDetail(bmId);
	}

	/**
	 * 导出成绩详情
	 */
	@RequestMapping("/exportScore")
	@Operation(desc = "导出成绩详情")
	public void mark(HttpServletRequest request,
					   HttpServletResponse response,
					   ZcpsScoreParams params) {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=student.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			zyjdBmService.exportScore(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}

	/**
	 * 导出报名申报表
	 */
	@RequestMapping("/exportPdf")
	@Operation(desc = "导出报名申报表")
	public void exportPdf(HttpServletRequest request, HttpServletResponse response, ZcpsScoreParams params) {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		response.setContentType("application/zip");
		response.setHeader("content-disposition", "attachment;filename=" + System.currentTimeMillis() + "_bm_info.zip");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			zyjdBmService.exportPdf(params, os);
		} catch (Exception e) {
			LOGGER.error("导出报名申报表失败，原因：" + e);
		}
	}

	/**
	 * 打分明细
	 */
	@RequestMapping("/scoreDetail")
	@Operation(desc = "打分明细")
	public Object scoreDetail(HttpServletRequest request, ZyjdBmQueryParams params) {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return zyjdBmService.scoreDetail(params);
	}

	/**
	 * 打分明细导出excel
	 */
	@RequestMapping("/exportScoreDetail")
	@Operation(desc = "打分明细导出excel")
	public void exportScoreDetail(HttpServletRequest request, HttpServletResponse response, ZyjdBmQueryParams params) {
		response.setContentType("application/octet-stream");
		response.setHeader("content-disposition", "attachment;filename=scoreDetail.xls");
		OutputStream os = null;
		try {
			os = response.getOutputStream();
			params.setHostOrgId(super.getCurrentHostOrgId(request));
			zyjdBmService.exportScoreDetail(params, os);
			os.flush();
		} catch (Exception e) {
			LOGGER.error("导出失败:", e);
		} finally {
			if (os != null) {
				IOUtils.closeQuietly(os);
			}
		}
	}
}
