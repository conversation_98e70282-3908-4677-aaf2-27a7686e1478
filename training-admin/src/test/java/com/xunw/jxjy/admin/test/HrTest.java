package com.xunw.jxjy.admin.test;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.common.Chapter;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.Lesson;
import com.xunw.jxjy.model.inf.entity.Courseware;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.inf.service.CoursewareService;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.learning.entity.CoursewareLearningProgress;
import com.xunw.jxjy.model.learning.entity.CoursewarePhotoLog;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxBmCourse;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmCourseMapper;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxCoursewareLearningProgressService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import com.xunw.jxjy.paper.utils.ModelHelper;
import jxl.read.biff.BiffException;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海尔热水器学员学习进度刷新
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class HrTest {

    @Autowired
    ZypxXmCourseSettingService zypxXmCourseSettingService;
    @Autowired
    CourseService courseService;
    @Autowired
    CoursewareService coursewareService;
    @Autowired
    StudentUserService studentUserService;
    @Autowired
    ZypxBmService zypxBmService;
    @Autowired
    ZypxBmCourseMapper zypxBmCourseMapper;
    @Autowired
    ZypxCoursewareLearningProgressService zypxCoursewareLearningProgressService;
    @Autowired
    ZypxXmService zypxXmService;
    @Autowired
    StudentInfoService studentInfoService;
    @Autowired
    AttConfig attConfig;

    //=============跑之前备份biz_kj_progress，biz_student_learning_score表=============
    @Test
    public void refresh() throws BiffException, IOException {
        String hostOrgId = "hbgtzyzyxy";
        String xmId = "7D75A09D2F484A23866DD70998DC589C";
        String excelUrl = "C:/Users/<USER>/Downloads/武汉热水器3.xls";
        //读取excel文件
        File excelFile = new File(excelUrl);
        List<Map<String, String>> list = OfficeToolExcel.readExcel(excelFile, new String[]{"no", "name", "gender", "sfzh", "remark"});
        //获取项目详情
        ZypxXm zypxXm = zypxXmService.selectById(xmId);
        //获取项目报名数据
        List<ZypxBm> zypxBms = zypxBmService.getByXmId(xmId);
        //获取课程报名数据
        List<ZypxBmCourse> zypxBmCourses = zypxBmCourseMapper.selectList(new EntityWrapper<ZypxBmCourse>()
                .in("bm_id", zypxBms.stream().map(ZypxBm::getId).collect(Collectors.toList())));
        //获取报名的所有的课程
        List<ZypxXmCourseSetting> zypxXmCourseSettings = zypxXmCourseSettingService.selectBatchIds(zypxBmCourses.stream().map(ZypxBmCourse::getCourseSettingId).collect(Collectors.toSet()));
        if (zypxXmCourseSettings.isEmpty()) {
            return;
        }
        //获取课件
        List<Courseware> coursewares = coursewareService.selectList((EntityWrapper<Courseware>) new EntityWrapper<Courseware>()
                .in("course_id", zypxXmCourseSettings.stream().map(ZypxXmCourseSetting::getCourseId).collect(Collectors.toSet())));
        //excel与报名数据比对
        for (Map<String, String> map : list) {
            StudentUser studentUser = studentUserService.findBySfzh(BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map, "sfzh")), hostOrgId);
            if (studentUser == null) {
                continue;
            }
            //没报名项目则跳过
            if (zypxBms.stream().noneMatch(x -> x.getStudentId().equals(studentUser.getId()))) {
                continue;
            }
            //更新学习进度
            for (Courseware courseware : coursewares) {
                this.uploadKjProgress(zypxXm, studentUser.getId(), courseware);
            }
        }
    }

    //更新学习进度
    private void uploadKjProgress(ZypxXm xm, String studentId, Courseware courseware) {
        Courselearn courselearn = (Courselearn) ModelHelper.convertObject(courseware.getContent());
        if (courselearn == null) {
            return;
        }
        if (CollectionUtils.isEmpty(courselearn.getChapters())) {
            return;
        }
        for (Chapter chapter : courselearn.getChapters()) {
            if (CollectionUtils.isEmpty(chapter.getLessons())) {
                continue;
            }
            for (Lesson lesson : chapter.getLessons()) {
                List<CoursewareLearningProgress> coursewareLearningProgresses = zypxCoursewareLearningProgressService.selectList((EntityWrapper<CoursewareLearningProgress>) new EntityWrapper<CoursewareLearningProgress>()
                        .eq("xm_id", xm.getId())
                        .eq("kj_id", courseware.getId())
                        .eq("student_id", studentId)
                        .eq("chapter_id", chapter.getId())
                        .eq("lesson_id", lesson.getId()));
                //待更新
                List<CoursewareLearningProgress> unUpdate = new ArrayList<>();
                //待插入
                List<CoursewareLearningProgress> unInstall = new ArrayList<>();
                //有进度，并且未完成
                if (!coursewareLearningProgresses.isEmpty()) {
                    CoursewareLearningProgress coursewareLearningProgress = coursewareLearningProgresses.get(0);
                    coursewareLearningProgress.setIsFinish(Constants.YES);
                    coursewareLearningProgress.setDuration(lesson.getMinutes());
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(coursewareLearningProgress.getStartTime());
                    calendar.add(Calendar.MINUTE, lesson.getMinutes());
                    coursewareLearningProgress.setEndTime(calendar.getTime());
                    coursewareLearningProgress.setUpdate_time(calendar.getTime());
                    unUpdate.add(coursewareLearningProgress);
                } else {
                    CoursewareLearningProgress coursewareLearningProgress = new CoursewareLearningProgress();
                    coursewareLearningProgress.setId(BaseUtil.generateId());
                    coursewareLearningProgress.setXmId(xm.getId());
                    coursewareLearningProgress.setKjId(courseware.getId());
                    coursewareLearningProgress.setStudentId(studentId);
                    coursewareLearningProgress.setChapterId(chapter.getId());
                    coursewareLearningProgress.setLessonId(lesson.getId());
                    coursewareLearningProgress.setIsFinish(Constants.YES);
                    coursewareLearningProgress.setDuration(lesson.getMinutes());
                    //计算合理的startTime
                    Date time = null;
                    boolean flag = true;
                    while (flag) {
                        Date startTime = this.randomDate(xm.getStartTime(), xm.getEndTime());
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        calendar.add(Calendar.MINUTE, lesson.getMinutes());
                        if (xm.getEndTime().after(calendar.getTime())) {
                            flag = false;
                            time = calendar.getTime();
                        }
                    }
                    //计算endTime
                    coursewareLearningProgress.setStartTime(time);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(coursewareLearningProgress.getStartTime());
                    calendar.add(Calendar.MINUTE, lesson.getMinutes());
                    coursewareLearningProgress.setEndTime(calendar.getTime());
                    coursewareLearningProgress.setUpdate_time(calendar.getTime());
                    unInstall.add(coursewareLearningProgress);
                }
                DBUtils.updateBatchById(unUpdate, CoursewareLearningProgress.class);
                DBUtils.insertBatch(unInstall, CoursewareLearningProgress.class);
                //更新成绩
                List<CoursewareLearningProgress> unUpdateScore = new ArrayList<>();
                unUpdateScore.addAll(unUpdate);
                unUpdateScore.addAll(unInstall);
                for (CoursewareLearningProgress coursewareLearningProgress : unUpdateScore) {
                    zypxCoursewareLearningProgressService.updateCoursewareStudyProgress(coursewareLearningProgress);
                }
            }
        }
    }

    //传入时间范围，随机生成一个处于时间范围之内的时间
    private Date randomDate(Date startTime, Date endTime) {
        //生成随机时间两个时间之间的日期
        return new Date(startTime.getTime() + (long) (Math.random() * (endTime.getTime() - startTime.getTime())));
    }

    @Test
    public void generateDateTest() throws ParseException {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);
        Date startTime = c.getTime();

        Calendar c2 = Calendar.getInstance();
        c2.setTime(new Date());
        c2.add(Calendar.YEAR, 1);
        Date endTime = c2.getTime();

        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.randomDate(startTime, endTime)));
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.randomDate(startTime, endTime)));
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.randomDate(startTime, endTime)));
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.randomDate(startTime, endTime)));
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(this.randomDate(startTime, endTime)));
    }

    /**
     * 更新抓拍照片
     */
    @Test
    public void updatePhoto() throws IOException {
        String hostOrgId = "hbgtzyzyxy";
        String xmId = "7D75A09D2F484A23866DD70998DC589C";
        String photoUrl = "C:/Users/<USER>/Downloads/照片-热水器";
        //读取照片文件
        File dir = new File(photoUrl);
        File[] listFiles = dir.listFiles();//所有学员照片目录

        //按名字分组之后的学员照片
        Map<String, List<File>> listMap = Arrays.stream(listFiles).collect(Collectors.groupingBy(File::getName));

        //获取项目详情
        ZypxXm zypxXm = zypxXmService.selectById(xmId);
        //获取项目报名数据
        List<ZypxBm> zypxBms = zypxBmService.getByXmId(xmId);
        //获取课程报名数据
        List<ZypxBmCourse> zypxBmCourses = zypxBmCourseMapper.selectList(new EntityWrapper<ZypxBmCourse>()
                .in("bm_id", zypxBms.stream().map(ZypxBm::getId).collect(Collectors.toList())));
        //获取报名的所有的课程
        List<ZypxXmCourseSetting> zypxXmCourseSettings = zypxXmCourseSettingService.selectBatchIds(zypxBmCourses.stream().map(ZypxBmCourse::getCourseSettingId).collect(Collectors.toSet()));
        if (zypxXmCourseSettings.isEmpty()) {
            return;
        }
        //获取课件
        List<Courseware> coursewares = coursewareService.selectList((EntityWrapper<Courseware>) new EntityWrapper<Courseware>()
                .in("course_id", zypxXmCourseSettings.stream().map(ZypxXmCourseSetting::getCourseId).collect(Collectors.toSet())));
        //文件与报名数据比对
        for (String name : listMap.keySet()) {
            List<StudentInfo> studentInfos = studentInfoService.selectList((EntityWrapper<StudentInfo>) new EntityWrapper<StudentInfo>()
                    .eq("name", name)
                    .eq("reg_host_org_id", hostOrgId));
            if (CollectionUtils.isEmpty(studentInfos)) {
                continue;
            }
            StudentInfo studentInfo = studentInfos.get(0);
            //没报名项目则跳过
            if (zypxBms.stream().noneMatch(x -> x.getStudentId().equals(studentInfo.getStudentId()))) {
                continue;
            }
            //更新抓拍
            for (Courseware courseware : coursewares) {
                this.uploadPhotoLog(zypxXm, studentInfo.getStudentId(), courseware, listMap.get(name).get(0));
            }
        }
    }

    private void uploadPhotoLog(ZypxXm xm, String studentId, Courseware courseware, File dir) throws IOException {
        Courselearn courselearn = (Courselearn) ModelHelper.convertObject(courseware.getContent());
        if (courselearn == null) {
            return;
        }
        if (CollectionUtils.isEmpty(courselearn.getChapters())) {
            return;
        }
        for (Chapter chapter : courselearn.getChapters()) {
            if (CollectionUtils.isEmpty(chapter.getLessons())) {
                continue;
            }
            for (Lesson lesson : chapter.getLessons()) {
                List<CoursewareLearningProgress> coursewareLearningProgresses = zypxCoursewareLearningProgressService.selectList((EntityWrapper<CoursewareLearningProgress>) new EntityWrapper<CoursewareLearningProgress>()
                        .eq("xm_id", xm.getId())
                        .eq("kj_id", courseware.getId())
                        .eq("student_id", studentId)
                        .eq("chapter_id", chapter.getId())
                        .eq("lesson_id", lesson.getId())
                        .eq("is_finish", Constants.YES));
                if (CollectionUtils.isEmpty(coursewareLearningProgresses)) {
                    continue;
                }
                CoursewareLearningProgress progress = coursewareLearningProgresses.get(0);
                //待插入
                List<CoursewarePhotoLog> unInstall = new ArrayList<>();
                File[] photos = dir.listFiles();
                File photo = null;
                for (int i = 0; i < 2; i++) {
                    //同一个课时，两张照片不能相同
                    File file = null;
                    while (file == photo) {
                        file = photos[new Random().nextInt(photos.length)];
                    }
                    //照片上传
                    String url = this.uploadImg(file);
                    CoursewarePhotoLog coursewarePhotoLog = new CoursewarePhotoLog();
                    coursewarePhotoLog.setId(BaseUtil.generateId());
                    coursewarePhotoLog.setUrl(url);
                    coursewarePhotoLog.setLogTime(randomDate(progress.getStartTime(), progress.getEndTime()));
                    coursewarePhotoLog.setProgressId(progress.getId());
                    unInstall.add(coursewarePhotoLog);
                }
                DBUtils.insertBatch(unInstall, CoursewarePhotoLog.class);
            }
        }
    }

    private String uploadImg(File file) throws IOException {
        String ext = file.getName();
        String path = attConfig.getRootDir() + "/upload/images/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext.toLowerCase();
        return FileHelper.storeFile(path, Files.newInputStream(file.toPath()), newFileName);
    }
}

