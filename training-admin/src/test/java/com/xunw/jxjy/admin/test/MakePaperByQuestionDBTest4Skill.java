package com.xunw.jxjy.admin.test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.common.collect.Lists;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Sjlx;
import com.xunw.jxjy.model.enums.Sjxslx;
import com.xunw.jxjy.model.enums.Sjzt;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.service.QuestionDB2CourseService;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.tk.service.QuestionEntityService;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.service.PlanDetailService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.utils.ModelHelper;

/**
 * 直接使用题库组卷,每50题拆分一次组成一份试卷
 * 注意：springboot中使用junit编写单元测试，@Transactional默认是事物回滚的，这样测试的脏数据不影响数据库
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MakePaperByQuestionDBTest4Skill {
	
	@Autowired
	private QuestionDBService questionDBService;
	@Autowired
	private ZypxXmService xmService;
	@Autowired
	private QuestionEntityService questionEntityService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private PlanDetailService planDetailService;
	
	@Test
	@Transactional(rollbackFor = Exception.class)
	@Rollback(false)//关闭自动回滚，因为springboot中使用junit编写单元测试，@Transactional默认是事物回滚的，这样测试的脏数据不影响数据库
	public void makePaper() throws ParseException {
		String xmSerialNumber = "FA20220602200147";
		String courseCode = "2111000543";
//		String professionId = "4bd2b844-5973-48a4-beae-b0e77dea6d20"; //企业人力资源管理师 三级
//		String professionId = "2A39890B4F2542CF95C02723FF630CAC"; //电子商务师 三级
//		String professionId = "F2AA2836C30644FA84FF57B42EDFAB51"; //互联网营销师（直播销售员） 三级
//		String professionId = "7B0E90DD4F934CA9858ED85CA8907CD1"; //建筑信息模型技术员L 三级
//		String professionId = "E8552B3196F24286AE6CFC16F157D89F"; //汽车电器维修工  三级
//		String professionId = "878f401e-1edc-45d6-8704-6fcb31ebc304"; //电工  三级、四级
//		String professionId = "d08229c1-5850-4654-9c43-755281d97551"; //工业机器人系统操作员  三级
//		String professionId = "fbdbd19b-ba7d-430e-ac9a-4463915d888d"; //网络安全管理员  三级、四级
//		String professionId = "960f48e9-4627-46c8-a2f9-2233f0103b6b"; //不动产测绘  三级
//		String professionId = "b754368c-f079-4ac1-9618-25ff28ecb770"; //工程测量员  三级
//		String professionId = "e7563ee7-59ea-4335-8f05-86999a89fa97"; //摄影测量员  三级
//		String professionId = "05fcaad6-8e63-46e1-a16e-3a50c39574e6"; //地质调查员  三级、四级
//		String professionId = "b754368c-f079-4ac1-9618-25ff28ecb770"; //工程测量员  四级 、五级
		String professionId = "960f48e9-4627-46c8-a2f9-2233f0103b6b"; //不动产测绘  四级 、五级
		TechLevel techLevel = TechLevel.FIVE; //等级
		
		
		ZypxXm zypxXm = xmService.getXmBySerialNumber(xmSerialNumber);
		String xmId = zypxXm.getId();
		Course course = courseService.getCourseByCode(courseCode);//查询题库
		if (course == null) {
			throw BizException.withMessage("课程代码不存在");
		}
		EntityWrapper<QuestionDBEntity> wrapper = new EntityWrapper();
		wrapper.eq("course_id", course.getId());
		List<QuestionDBEntity> questionDBList = questionDBService.getQuestionDBByCourseId(course.getId());
		List<ExamPaper> examPapersAll = new ArrayList<ExamPaper>();
		for (QuestionDBEntity dbEntity : questionDBList) {
			List<ExamPaper> list = this.makerPaperByOneDb(xmId, professionId, techLevel, dbEntity);
			examPapersAll.addAll(list);
		}
		DBUtils.insertBatch(examPapersAll, 50, ExamPaper.class);
	}
	
	//处理一个题库拆分
	private List<ExamPaper> makerPaperByOneDb(String xmId, String professionId, TechLevel techLevel, QuestionDBEntity questionDBEntity) throws ParseException {
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
		wrapper.eq("db_id", questionDBEntity.getId());
		List<QuestionEntity> questionEntityList = questionEntityService.selectList(wrapper);
		//每100题拆分一个数组
		List<List<QuestionEntity>> allQuestions = Lists.partition(questionEntityList,  100);
		int index = 0;
		List<ExamPaper> examPaperList = new ArrayList<ExamPaper>();
		for (List<QuestionEntity> qlist : allQuestions) {
			
			index++;
			
			//四大题型
			List<QuestionEntity> SINGLECHOICE = new ArrayList<QuestionEntity>();
			List<QuestionEntity> MULTIPLECHOICE = new ArrayList<QuestionEntity>();
			List<QuestionEntity> JUDGMENT = new ArrayList<QuestionEntity>();
			List<QuestionEntity> LST = new ArrayList<QuestionEntity>();
			List<QuestionEntity> ESSAY = new ArrayList<QuestionEntity>();
			List<QuestionEntity> BLANKFILL = new ArrayList<QuestionEntity>();
			
			for (QuestionEntity questionEntity : qlist) {
				if (Stlb.SINGLECHOICE == questionEntity.getType()) {
					SINGLECHOICE.add(questionEntity);
				}
				else if(Stlb.LST == questionEntity.getType()) {
					LST.add(questionEntity);
				}
				else if(Stlb.ESSAY == questionEntity.getType()) {
					ESSAY.add(questionEntity);
				}
				else if(Stlb.MULTIPLECHOICE == questionEntity.getType()) {
					MULTIPLECHOICE.add(questionEntity);
				}
				else if(Stlb.JUDGMENT == questionEntity.getType()) {
					JUDGMENT.add(questionEntity);
				}
				else if(Stlb.BLANKFILL == questionEntity.getType()) {
					BLANKFILL.add(questionEntity);
				}
			}
			
			//数据对象
			Paper paper = new Paper();
			
			if (SINGLECHOICE.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-SINGLECHOICE", "单选题", "单选题");
				for (QuestionEntity questionEntity : SINGLECHOICE) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (MULTIPLECHOICE.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-MULTIPLECHOICE", "多选题", "多选题");
				for (QuestionEntity questionEntity : MULTIPLECHOICE) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (JUDGMENT.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-JUDGMENT", "判断题", "判断题");
				for (QuestionEntity questionEntity : JUDGMENT) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (LST.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-LST", "简答题", "简答题");
				for (QuestionEntity questionEntity : LST) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (ESSAY.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-ESSAY", "问答题", "问答题");
				for (QuestionEntity questionEntity : ESSAY) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			
			if (BLANKFILL.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-BLANKFILL", "填空题", "填空题");
				for (QuestionEntity questionEntity : BLANKFILL) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			
			//组卷
			ExamPaper examPaper = new ExamPaper();
	    	examPaper.setId(BaseUtil.generateId2());
	    	examPaper.setName(questionDBEntity.getName()+"-"+index);
	    	examPaper.setXmId(xmId);
	    	examPaper.setProfessionId(professionId);
	    	examPaper.setTechLevel(techLevel);
	    	examPaper.setCategory(PaperCategory.PSZY);
	    	examPaper.setStatus(Sjzt.ZCKY);
	    	examPaper.setStartTime(DateUtils.parse("2022-06-01 08:00","yyyy-MM-dd HH:mm"));
	    	examPaper.setEndTime(DateUtils.parse("2022-06-30 23:59","yyyy-MM-dd HH:mm"));
	    	examPaper.setIsShowAnswer(Constants.YES);
	    	examPaper.setPaperShowType(Sjxslx.ZJZS);
	    	examPaper.setPaperType(Sjlx.PT);
	    	examPaper.setQuesSortType(Stplsx.ZC);
	    	examPaper.setTotalScore(qlist.size() * 1);
	    	examPaper.setPassedScore(Double.valueOf(qlist.size() * 1 * 0.6).intValue());
	        Date createTime = new Date();
	        examPaper.setCreateTime(createTime);
	        
	        //完善信息
			paper.setId(examPaper.getId());
			paper.setName(examPaper.getName());
			paper.setStatus(examPaper.getStatus());
			paper.setStartTime(examPaper.getStartTime());
			paper.setEndTime(examPaper.getEndTime());
			paper.setDuration(examPaper.getDuration());
			paper.setScoreTime(examPaper.getScoreTime());
			paper.setTotalScore(examPaper.getTotalScore());
			paper.setPassedScore(examPaper.getPassedScore());
			paper.setQuesSortType(examPaper.getQuesSortType());
			paper.setPaperType(examPaper.getPaperType());
			paper.setRemark(examPaper.getRemark());
			paper.setIsShowAnswer(examPaper.getIsShowAnswer());
			paper.setPaperShowType(examPaper.getPaperShowType());
			
			String xml = ModelHelper.formatObject(paper);
			examPaper.setData(xml);
	        
			examPaperList.add(examPaper);
		}
		
		return examPaperList;
	}

}
