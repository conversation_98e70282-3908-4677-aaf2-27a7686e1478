package com.xunw.jxjy.admin.test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Sjlx;
import com.xunw.jxjy.model.enums.Sjxslx;
import com.xunw.jxjy.model.enums.Sjzt;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.tk.service.QuestionEntityService;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.service.PlanDetailService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.utils.ModelHelper;

/**
 * 直接使用题库组卷,每50题拆分一次组成一份试卷
 * 注意：springboot中使用junit编写单元测试，@Transactional默认是事物回滚的，这样测试的脏数据不影响数据库
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MakePaperBySingleQuestionDB {
	
	@Autowired
	private QuestionDBService questionDBService;
	@Autowired
	private ZypxXmService xmService;
	@Autowired
	private QuestionEntityService questionEntityService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private PlanDetailService planDetailService;
	
	@Test
	@Transactional(rollbackFor = Exception.class)
	@Rollback(false)//关闭自动回滚，因为springboot中使用junit编写单元测试，@Transactional默认是事物回滚的，这样测试的脏数据不影响数据库
	public void makePaper() throws ParseException {
		String xmSerialNumber = "GV20220530094727";
		ZypxXm zypxXm = xmService.getXmBySerialNumber(xmSerialNumber);
		String xmId = zypxXm.getId();
		QuestionDBEntity dbEntity = questionDBService.selectById("db05dbdb-744a-4370-95f1-3513e7272833");
		List<ExamPaper> list = this.makerPaperByOneDb(xmId, "30607210DC3D48A6BAE8851B753BEC56" , dbEntity);
		DBUtils.insertBatch(list, 50, ExamPaper.class);
	}
	
	//处理一个题库拆分
	private List<ExamPaper> makerPaperByOneDb(String xmId,String courseId, QuestionDBEntity questionDBEntity) throws ParseException {
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
		wrapper.eq("db_id", questionDBEntity.getId());
		List<QuestionEntity> qlist = questionEntityService.selectList(wrapper);
		int index = 0;
		List<ExamPaper> examPaperList = new ArrayList<ExamPaper>();
			
		
			
			//四大题型
			List<QuestionEntity> SINGLECHOICE = new ArrayList<QuestionEntity>();
			List<QuestionEntity> MULTIPLECHOICE = new ArrayList<QuestionEntity>();
			List<QuestionEntity> JUDGMENT = new ArrayList<QuestionEntity>();
			List<QuestionEntity> LST = new ArrayList<QuestionEntity>();
			List<QuestionEntity> ESSAY = new ArrayList<QuestionEntity>();
			List<QuestionEntity> BLANKFILL = new ArrayList<QuestionEntity>();
			
			for (QuestionEntity questionEntity : qlist) {
				if (Stlb.SINGLECHOICE == questionEntity.getType()) {
					SINGLECHOICE.add(questionEntity);
				}
				else if(Stlb.LST == questionEntity.getType()) {
					LST.add(questionEntity);
				}
				else if(Stlb.ESSAY == questionEntity.getType()) {
					ESSAY.add(questionEntity);
				}
				else if(Stlb.MULTIPLECHOICE == questionEntity.getType()) {
					MULTIPLECHOICE.add(questionEntity);
				}
				else if(Stlb.JUDGMENT == questionEntity.getType()) {
					JUDGMENT.add(questionEntity);
				}
				else if(Stlb.BLANKFILL == questionEntity.getType()) {
					BLANKFILL.add(questionEntity);
				}
			}
			
			//数据对象
			Paper paper = new Paper();
			
			if (SINGLECHOICE.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-SINGLECHOICE", "单选题", "单选题");
				for (QuestionEntity questionEntity : SINGLECHOICE) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (MULTIPLECHOICE.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-MULTIPLECHOICE", "多选题", "多选题");
				for (QuestionEntity questionEntity : MULTIPLECHOICE) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (JUDGMENT.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-JUDGMENT", "判断题", "判断题");
				for (QuestionEntity questionEntity : JUDGMENT) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (LST.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-LST", "简答题", "简答题");
				for (QuestionEntity questionEntity : LST) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			if (ESSAY.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-ESSAY", "问答题", "问答题");
				for (QuestionEntity questionEntity : ESSAY) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			
			if (BLANKFILL.size() > 0) {
				PaperSection section = new PaperSection(questionDBEntity.getId()+"-"+index+"-BLANKFILL", "填空题", "填空题");
				for (QuestionEntity questionEntity : BLANKFILL) {
					Question question = new Question();
					question.setId(questionEntity.getId());
					question.setType(questionEntity.getType());
					question.setScore(1);
					question.setContent(questionEntity.getContent());
					question.setAnswer(questionEntity.getAnswer());
					question.setResolve(questionEntity.getResolve());
					section.addQuestion(question);
				}
				section.setRscore(1);
				paper.addSection(section);
			}
			
			//组卷
			ExamPaper examPaper = new ExamPaper();
	    	examPaper.setId(BaseUtil.generateId2());
	    	examPaper.setName(questionDBEntity.getName());
	    	examPaper.setXmId(xmId);
	    	examPaper.setCourseId(courseId);
	    	examPaper.setCategory(PaperCategory.PSZY);
	    	examPaper.setStatus(Sjzt.ZCKY);
	    	examPaper.setStartTime(DateUtils.parse("2022-06-01 08:00","yyyy-MM-dd HH:mm"));
	    	examPaper.setEndTime(DateUtils.parse("2022-06-11 23:59","yyyy-MM-dd HH:mm"));
	    	examPaper.setIsShowAnswer(Constants.YES);
	    	examPaper.setPaperShowType(Sjxslx.ZJZS);
	    	examPaper.setPaperType(Sjlx.PT);
	    	examPaper.setQuesSortType(Stplsx.ZC);
	    	examPaper.setTotalScore(qlist.size() * 1);
	    	examPaper.setPassedScore(Double.valueOf(qlist.size() * 1 * 0.6).intValue());
	        Date createTime = new Date();
	        examPaper.setCreateTime(createTime);
	        
	        //完善信息
			paper.setId(examPaper.getId());
			paper.setName(examPaper.getName());
			paper.setStatus(examPaper.getStatus());
			paper.setStartTime(examPaper.getStartTime());
			paper.setEndTime(examPaper.getEndTime());
			paper.setDuration(examPaper.getDuration());
			paper.setScoreTime(examPaper.getScoreTime());
			paper.setTotalScore(examPaper.getTotalScore());
			paper.setPassedScore(examPaper.getPassedScore());
			paper.setQuesSortType(examPaper.getQuesSortType());
			paper.setCourseId(courseId);
			paper.setPaperType(examPaper.getPaperType());
			paper.setRemark(examPaper.getRemark());
			paper.setIsShowAnswer(examPaper.getIsShowAnswer());
			paper.setPaperShowType(examPaper.getPaperShowType());
			
			String xml = ModelHelper.formatObject(paper);
			examPaper.setData(xml);
	        
			examPaperList.add(examPaper);
		
		return examPaperList;
	}

}
