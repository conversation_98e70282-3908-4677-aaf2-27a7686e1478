package com.xunw.jxjy.admin.test;

import com.alibaba.fastjson.JSON;
import com.aspose.words.*;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.General;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import org.apache.commons.collections4.CollectionUtils;

import java.io.InputStream;
import java.util.List;

import com.xunw.jxjy.model.enums.Stlb;

/**
 * <AUTHOR>
 * @date 2024-08-14 17:56
 */
public class AsposeWordTest {
    static String paperStr = "{\n" +
            "    \"duration\": 120,\n" +
            "    \"endTime\": 1706691960000,\n" +
            "    \"examModel\": \"SDSK\",\n" +
            "    \"id\": \"968bda7e-f863-4e68-88b4-2ce3a692f1bc\",\n" +
            "    \"isAllowMobile\": \"1\",\n" +
            "    \"isOpenCamera\": \"1\",\n" +
            "    \"isShowAnswer\": \"1\",\n" +
            "    \"name\": \"计算机管理员考试（三级）\",\n" +
            "    \"paperShowType\": \"ZJZS\",\n" +
            "    \"paperType\": \"PT\",\n" +
            "    \"passedScore\": 60,\n" +
            "    \"quesSortType\": \"ZC\",\n" +
            "    \"remark\": \"\",\n" +
            "    \"scoreTime\": 1709197560000,\n" +
            "    \"sections\": [\n" +
            "        {\n" +
            "            \"id\": \"1\",\n" +
            "            \"name\": \"1\",\n" +
            "            \"questions\": [\n" +
            "                {\n" +
            "                    \"answer\": \"B\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">直接人工成本项目是指</span></p>\",\n" +
            "                    \"id\": \"2911b14c-6e08-4929-b4e2-0584be4954e8\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">管理人员的薪酬</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">直接参加产品制造的生产工人的薪酬</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">销售人员的薪酬</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">用于职工集体福利设施的开支</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"B\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">由于自然灾害而引起的非正常停工损失，应计入</span></p>\",\n" +
            "                    \"id\": \"42ef7c34-43d9-4ad7-bde7-793d07354fec\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">营业外收入</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">营业外支出</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">管理费用</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">制造费用</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"A\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">甲、乙两种产品共同耗费A材料6000元,按材料定额消耗量比例分配。甲、乙产品的材料单位消耗分别为200千克和300千克。据此计算的A材料消耗量分配率为</span></p>\",\n" +
            "                    \"id\": \"0c642cd2-bcf6-4616-85c0-282b0adb2c2b\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">12</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">20</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">30</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">60</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"B\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">在产晶收发结存的日常核算通常是设置</span></p>\",\n" +
            "                    \"id\": \"ff67ca7c-74ae-4d48-89e5-223e5b8c5997\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产成本明细账</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">在产晶收发结存台账</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">制造费用明细账</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">源材料明细账</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"D\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">在各种成本计算方祛中。必须设立基本生产成本二级账的方法是</span></p>\",\n" +
            "                    \"id\": \"a3b1cb9b-757f-4d43-ab5f-71058c01c745\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">品种法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">分步法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定额法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">简化分批法</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"C\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">最基本的成本计算方法是</span></p>\",\n" +
            "                    \"id\": \"9ea2a860-8f8c-4d75-a165-f8510f2b717b\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定额法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">分批法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">品种法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">分步法</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"A\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">原材料在生产开始时一次投入，月末在产品的投料程度应为</span></p>\",\n" +
            "                    \"id\": \"c2558b62-e53d-42fa-b983-0dd48ecbead0\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">100%</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">50%</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定额耗用量比例</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定帼工时比例</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"C\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">下列属于在产品成本计算方法的是</span></p>\",\n" +
            "                    \"id\": \"c1acbb2c-7181-4de9-9474-fd9dbdbd582f\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">分步法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">直接分配法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">约当产量比例法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">顺序分配法</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"D\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">辅助生产费用的顺序分配法，也称</span></p>\",\n" +
            "                    \"id\": \"eee687f0-58c0-44a3-bdd1-ed9cae6cec66\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">直接分配法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">交互分配法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">代数分配法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">梯形分配法</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"SINGLECHOICE\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"remark\": \"单选\",\n" +
            "            \"rlevel\": 0,\n" +
            "            \"rnum\": 0,\n" +
            "            \"rscore\": 5,\n" +
            "            \"rtype\": 0\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": \"2\",\n" +
            "            \"name\": \"2\",\n" +
            "            \"questions\": [\n" +
            "                {\n" +
            "                    \"answer\": \"ABCD\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本会计的职能有</span></p>\",\n" +
            "                    \"id\": \"283efd7d-9056-4875-9891-e0e699254291\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本预测</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本计划</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本核算</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本分析</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">威本分配</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"ABD\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">分类法下产品分类的标准一般是</span></p>\",\n" +
            "                    \"id\": \"4c85200a-2a49-43ab-add5-1a7e955f90cd\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产工艺相近</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">耗用相同的原材料</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">消耗量相等</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">在同一生产部门加工生产</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">工时相同</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"ACD\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产特点和管理要求对产品成本计算的影响，主要表现在</span></p>\",\n" +
            "                    \"id\": \"cd2dc7fe-6c31-4257-913d-43d62c7c7a01\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本计算对象</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">间接费用的分配方法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">成本计算期</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产费用在完工产品与在产品之间的分配</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产费用在成本核算对象之间的分配</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"AB\",\n" +
            "                    \"content\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">观测水平角时,各测回配置度盘可削弱（&nbsp;&nbsp;&nbsp;&nbsp;）的影响。</span></p>\",\n" +
            "                    \"id\": \"d82ddb6f-0cd8-43af-ac4b-f402a7cad432\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">水平度盘刻划误差</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">水平度盘偏心误差</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">照准误差</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">水平度盘倾斜误差</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">对中误差</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"ABCDE\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">生产费用在完工产品与在产晶之间的分配方法中，常用的分配方法有</span></p>\",\n" +
            "                    \"id\": \"2dcae3e3-b7e3-49b9-8a5b-e553ef7f05a7\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">在产品按完工产品计算法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定额成本法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">不计算在产品成本法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">约当产量比例法</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">定额比例法</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"ACE\",\n" +
            "                    \"content\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">常用投影面有（&nbsp;&nbsp;&nbsp;&nbsp;）。</span></p>\",\n" +
            "                    \"id\": \"f43d2a8b-6a8c-41a1-8441-02f1fc726238\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">水平面</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">水准面</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">高斯投影面</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">大地水准面</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">参考椭球面</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"answer\": \"ACD\",\n" +
            "                    \"content\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">比率分析法的具体形式包括</span></p>\",\n" +
            "                    \"id\": \"32a43625-e488-44d5-9cbf-9f377bee31ec\",\n" +
            "                    \"options\": [\n" +
            "                        {\n" +
            "                            \"alisa\": \"A\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">相关指标比率分析</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"B\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">因素分析</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"C\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">构成比率分析</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"D\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">动态比率分析</span></p>\"\n" +
            "                        },\n" +
            "                        {\n" +
            "                            \"alisa\": \"E\",\n" +
            "                            \"text\": \"<p style=\\\"\\\"><span style=\\\"font-family:Calibri;\\\">连环替代分析</span></p>\"\n" +
            "                        }\n" +
            "                    ],\n" +
            "                    \"resolve\": \"\",\n" +
            "                    \"score\": 5,\n" +
            "                    \"type\": \"MULTIPLECHOICE\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"remark\": \"多选\",\n" +
            "            \"rlevel\": 0,\n" +
            "            \"rnum\": 0,\n" +
            "            \"rscore\": 5,\n" +
            "            \"rtype\": 0\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": \"3\",\n" +
            "            \"name\": \"3\",\n" +
            "            \"questions\": [\n" +
            "                {\n" +
            "                    \"answer\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\"><img src=\\\"http://jxjy-att.whxunw.com/guotu_att/upload/word/20230728/8568EFC1A8D94508ACBCDF3D33358BAE.png\\\" align=\\\"middle\\\" style=\\\"vertical-align:middle;border-width:0px;width:9.119305555555556cm;height:6.0325cm;\\\"></span></p>\",\n" +
            "                    \"content\": \"<p style=\\\"color:#000000;font-size:12pt;\\\"><span style=\\\"color:#000000;font-size:12pt;\\\">某</span><span style=\\\"font-size:12pt;\\\">工</span><span style=\\\"color:#000000;font-size:12pt;\\\">业厂房柱基础，基底尺寸4×6m²，作用在基础顶部的竖向荷载F=&nbsp;3000kN，弯矩M=2100kN·m，地质条件及基础埋深如图所示，地下水位于基础底面处，试验算该地基土承载力是否满足要求。(γw=&nbsp;1OkN/m³，γ</span><span style=\\\"color:#000000;font-size:8pt;\\\"><sub>G</sub></span><span style=\\\"color:#000000;font-size:12pt;\\\">=&nbsp;20kN/&nbsp;m³)&nbsp;</span></p><p style=\\\"color:#000000;\\\"><span style=\\\"color:#000000;\\\"><img src=\\\"http://jxjy-att.whxunw.com/guotu_att/upload/word/20230728/754811CEFB2D4FC08B31A44169DBBB72.jpeg\\\" align=\\\"middle\\\" style=\\\"vertical-align:middle;border-width:0px;width:6.925027777777777cm;height:6.2776805555555555cm;\\\"></span></p>\",\n" +
            "                    \"id\": \"3f056a6c-e7d2-4fa5-b702-9d2c63b4a499\",\n" +
            "                    \"resolve\": \"<p style=\\\"font-size:12pt;\\\"><span style=\\\"font-size:12pt;font-family:Calibri;\\\">见教材P31</span></p>\",\n" +
            "                    \"score\": 20,\n" +
            "                    \"type\": \"JDT\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"remark\": \"简答\",\n" +
            "            \"rlevel\": 0,\n" +
            "            \"rnum\": 0,\n" +
            "            \"rscore\": 20,\n" +
            "            \"rtype\": 0\n" +
            "        }\n" +
            "    ],\n" +
            "    \"startTime\": 1704099960000,\n" +
            "    \"status\": \"ZCKY\",\n" +
            "    \"totalScore\": 100\n" +
            "}";


    private static License aposeLic = new License();
    static {
        getLicense();
    }
    /**
     * 加载aspose License
     */
    private static void getLicense() {
        if (!aposeLic.getIsLicensed()) {
            InputStream is = AsposeWordTest.class.getClassLoader().getResourceAsStream("asp-license.xml");
            try {
                aposeLic.setLicense(is);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    {
/*
        // 复制表格
        Table copyAnswerTable= (Table) answerTable.deepClone(true);

       // 找到第一个表格所在的段落
        Body body = (Body) answerTable.getParentNode();
        // 获取第一个表格下面的节点，在第一个表格下面插入复制的表格
        Node insertAfterNode = answerTable.getNextSibling();
        // 插入一行包含文字的段落
        Paragraph textParagraph = new Paragraph(document);
        Run run = new Run(document, "单选题");
        run.getFont().setSize(11);
        textParagraph.appendChild(run);
        body.insertAfter(textParagraph, insertAfterNode);
        // 加入一行空段落
        Paragraph emptyParagraph1 = new Paragraph(document);
        body.insertAfter(emptyParagraph1, textParagraph);
        // 插入表格
        body.insertAfter(, emptyParagraph1);
        // 加入一行空段落
        Paragraph emptyParagraph2 = new Paragraph(document);
        body.insertAfter(emptyParagraph2, );
        // 插入一行包含文字的段落
        Paragraph textParagraph2 = new Paragraph(document);
        Run run2 = new Run(document, "问答题");
        run2.getFont().setSize(11);
        textParagraph2.appendChild(run2);
        body.insertAfter(textParagraph2, emptyParagraph2);
        // 加入一行空段落
        Paragraph emptyParagraph3 = new Paragraph(document);
        body.insertAfter(emptyParagraph3, textParagraph2);*/


//        // 在文档末尾插入复制的表格
//        Section section = document.getSections().get(0);
//        Body body = section.getBody();
//        body.appendChild(scoreTable.deepClone(true));
//        insertEmptyParagraph(body);
//        insertParagraphWithText(body, "单选题");
//        insertEmptyParagraph(body);
//        body.appendChild(copyAnswerTable);
//        insertEmptyParagraph(body);
//        body.appendChild(scoreTable.deepClone(true));
//        insertEmptyParagraph(body);
//        insertParagraphWithText(body, "问答题");
//        insertEmptyParagraph(body);
//
//        // 动态添加行和内容
//        addRowsToTable(copyAnswerTable, 3); // 添加 3 行内容
//        // 删除第一行表头
//        copyAnswerTable.removeChild(copyAnswerTable.getRows().get(0));

        // 删除模板的表格
//        answerTable.remove();
//        scoreTable.remove();
    }

    public static void main(String[] args) throws Exception {
        Paper paper = JSON.parseObject(paperStr, Paper.class);
        // 加载现有文档
        Document document = new Document("C:\\Users\\<USER>\\Desktop\\template.docx");

        replaceText(document,"{{NAME}}",paper.getName());
        replaceText(document,"{{MINUTES}}",paper.getDuration() + "");

        // 获取表格
        NodeCollection tables = document.getChildNodes(NodeType.TABLE, true);
        Table answerTable = (Table) tables.get(3);  // 答题卡表格 假设需要复制的表格是第三个表格
        Table scoreTable = (Table) tables.get(2);  // 得分表格 假设需要复制的表格是第二个表格
        Table totalTable = (Table) tables.get(1);  // 得分表格 假设需要复制的表格是第二个表格

        List<PaperSection> sections = paper.getSections();
        // 动态删除总分表格 （固定五大题，超过五大题不处理）
        if (sections.size() < 5) {
            int count = totalTable.getRows().get(0).getCells().getCount();
            // 需要删除的列数
            int column = count - sections.size() - 2;
            // 循环删除总分前面的列
            for (int i = 0; i < column; i++) {
                removeColumn(totalTable, count - 3);
            }
        }

        int secIndex = 1;
        int serialNumber = 0;
        for (PaperSection section : sections) {
            List<Question> questions = section.getQuestions();
            int questionsSize = questions.size();
            if (CollectionUtils.isEmpty(questions)) {
                continue;
            }
            int perScore = questions.get(0).getScore();
            Stlb type = questions.get(0).getType();
            int totalScore = questionsSize * perScore;
            String str1= BaseUtil.toCNLowerNum(secIndex++) + "、" + General.XMLEncode(section.getRemark())
                    + "（本大题共" + questionsSize + "小题，每小题:"+	General.formatScore(perScore) + "分"+""
                    + "，共" + General.formatScore(totalScore) + "分"
                    + "）";
            // 在文档末尾插入复制的表格
            Section sec = document.getSections().get(0);
            Body body = sec.getBody();
            insertEmptyParagraph(body);
            // 插入得分表格
            body.appendChild(scoreTable.deepClone(true));
            insertEmptyParagraph(body);
            // 插入大题标题
            insertParagraphWithText(body, str1);
            insertEmptyParagraph(body);
            // 根据试题类型插入内容
            if (Stlb.SINGLECHOICE.equals(type) || Stlb.MULTIPLECHOICE.equals(type) || Stlb.JUDGMENT.equals(type)) {
                // 复制表格
                Table copyAnswerTable= (Table) answerTable.deepClone(true);
                body.appendChild(copyAnswerTable);
                // 动态添加行和内容
                serialNumber = addRowsToTable(copyAnswerTable, questionsSize, serialNumber);
                // 删除第一行模板表头
                copyAnswerTable.removeChild(copyAnswerTable.getRows().get(0));
            } else {
                for (int i = 0; i < questionsSize; i++) {
                    serialNumber++;
                    insertParagraphWithText(body, serialNumber + ".");
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                    insertEmptyParagraph(body);
                }
            }
        }
        // 删除模板表格
        removeTableAndHandleEmptyParagraphs(answerTable);
        removeTableAndHandleEmptyParagraphs(scoreTable);

        // 保存修改后的文档
        document.save("C:\\Users\\<USER>\\Desktop\\output.docx");
    }

    /**
     * 根据表格删除列
     * @param table 表格
     * @param columnIndexToRemove 列索引
     */
    private static void removeColumn(Table table, int columnIndexToRemove) {
        // 遍历表格中的所有行
        for (Row row : table.getRows()) {
            // 确保列索引有效
            if (columnIndexToRemove < row.getCells().getCount()) {
                // 删除指定索引的单元格
                row.getCells().get(columnIndexToRemove).remove();
            }
        }
    }

    /**
     * 删除表格并处理空段落
     * @param table 表格
     */
    private static void removeTableAndHandleEmptyParagraphs(Table table) {
        // 找到表格所在的父节点（Body）
        Node parentNode = table.getParentNode();

        if (parentNode instanceof Body) {
            Body body = (Body) parentNode;
            // 获取表格的父节点（可能是段落）
            Node previousNode = table.getPreviousSibling();
            Node nextNode = table.getNextSibling();

            // 删除表格
            table.remove();

            // 检查并删除空白段落
            if (previousNode instanceof Paragraph) {
                Paragraph previousParagraph = (Paragraph) previousNode;
                if (isEmptyParagraph(previousParagraph)) {
                    previousParagraph.remove();
                }
            }

            if (nextNode instanceof Paragraph) {
                Paragraph nextParagraph = (Paragraph) nextNode;
                if (isEmptyParagraph(nextParagraph)) {
                    nextParagraph.remove();
                }
            }
        }
    }

    /**
     *  检查段落是否为空
     * @param paragraph 段落
     * @return 结果
     */
    private static boolean isEmptyParagraph(Paragraph paragraph) {
        // 检查段落是否为空，包括是否只包含表格等
        return paragraph.getChildNodes(NodeType.TABLE, true).getCount() == 0 &&
                paragraph.getText().trim().isEmpty();
    }

    /**
     * 在段落中插入文本
     * @param body 内容
     * @param text 文本
     * @throws Exception
     */
    private static void insertParagraphWithText(Body body, String text) throws Exception {
        Paragraph paragraph = new Paragraph(body.getDocument());
        Run run = new Run(body.getDocument(), text);
        run.getFont().setSize(11);
        paragraph.appendChild(run);
        body.appendChild(paragraph);
    }

    /**
     * 在段落之间插入空段落
     */
    private static void insertEmptyParagraph(Body body) throws Exception {
        Paragraph emptyParagraph = new Paragraph(body.getDocument());
        Run run = new Run(body.getDocument());
        run.getFont().setSize(11);
        emptyParagraph.appendChild(run);
        body.appendChild(emptyParagraph);
    }


    /**
     * 文本替换
     * @param document 文档
     * @param placeholder 占位符
     * @param newText 替换的文本
     * @throws Exception
     */
    private static void replaceText(Document document, String placeholder, String newText) throws Exception {
        // 获取文档中的范围
        Range range = document.getRange();
        // 查找占位符并替换文本
        range.replace(placeholder, newText, false, false);
    }

    /**
     *  动态添加表格行 固定5列
     * @param table 表
     * @param questionsSize 题目数量
     * @param serialNumber 序号
     * @return
     * @throws Exception
     */
    private static int addRowsToTable(Table table, int questionsSize,int serialNumber) throws Exception {
        // 获取表格的列数
//        int numColumns = table.getFirstRow().getCells().getCount();
        // 计算动态表格行数
        int numRows = questionsSize / 5 + 1;
        // 定义已添加序号值
        int addedSerialNumber = 0;
        for (int i = 0; i < numRows; i++) {
            // 创建新行
            Row newRow = (Row) table.getRows().get(0).deepClone(true);  // 克隆表头行

            for (int j = 0; j < newRow.getCells().getCount(); j++) {
                addedSerialNumber++;
                // 根据题目数量动态替换文本内容或清空文本
                if (addedSerialNumber <= questionsSize) {
                    serialNumber++;
//                replaceTextInRow(newRow.getCells().get(j), "No", (i * numColumns + (j + 1)) + "");
                    replaceTextInRow(newRow.getCells().get(j), "{{NO}}", serialNumber + "");
                } else {
                    newRow.getCells().get(j).getParagraphs().clear();
                }
            }
            // 将新行添加到表格
            table.appendChild(newRow);
        }
        return serialNumber;
    }

    /**
     * 替换表格中指定单元格的文本内容
     * @param cell 表格
     * @param oldText 占位符
     * @param newText 替换文本
     * @throws Exception 异常
     */
    private static void replaceTextInRow(Cell cell, String oldText, String newText) throws Exception {
        // 遍历单元格中的所有段落
        for (Paragraph paragraph : cell.getParagraphs()) {
            // 遍历段落中的所有文本运行
            for (Run run : paragraph.getRuns()) {
                // 替换文本
                run.setText(run.getText().replace(oldText, newText));
            }
        }
    }
}
