<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.xunw.jxjy</groupId>
	<artifactId>training-parent</artifactId>
	<packaging>pom</packaging>

	<version>1.0</version>
	<name>training-parent</name>
	<description>training-parent</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.9.RELEASE</version>
	</parent>

	<modules>
		<module>training-common</module>
		<module>training-module</module>
		<module>training-admin</module>
		<module>training-student</module>
		<module>training-live</module>
		<module>training-cas</module>
	</modules>

	<!-- 属性 -->
	<properties>
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.compiler.source>1.8</maven.compiler.source>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<mysql-connector.version>5.1.38</mysql-connector.version>
		<jwt.version>3.3.0</jwt.version>
		<shiro.version>1.3.2</shiro.version>
		<jwt.version>3.3.0</jwt.version>
		<jedis.version>2.9.0</jedis.version>
		<commons-lang.version>3.4</commons-lang.version>
		<commons-codec.version>1.9</commons-codec.version>
		<commons-io.version>2.4</commons-io.version>
		<commons-fileupload.version>1.3.1</commons-fileupload.version>
		<commons-logging.version>1.2</commons-logging.version>
		<commons-configuration.version>1.10</commons-configuration.version>
		<fastjson.version>1.2.15</fastjson.version>
		<gson.version>2.8.0</gson.version>
		<poi.version>3.15</poi.version>
		<httpclient.version>4.5.3</httpclient.version>
		<httpcore.version>4.4.6</httpcore.version>
		<async-http-client.version>1.8.3</async-http-client.version>
		<jxls.version>1.0.3</jxls.version>
		<jackson-mapper-asl.version>1.5.0</jackson-mapper-asl.version>
		<jackson-annotations.version>2.8.0</jackson-annotations.version>
		<jackson-core.version>2.8.8</jackson-core.version>
		<jackson-databind.version>2.8.8.1</jackson-databind.version>
		<druid-spring-boot-starter.version>1.2.4</druid-spring-boot-starter.version>
		<mybatis-plus-boot-starter.version>2.1.9</mybatis-plus-boot-starter.version>
		<mybatisplus.version>2.2.0</mybatisplus.version>
		<netty.version>3.9.0.Final</netty.version>
		<protobuf-java.version>2.5.0</protobuf-java.version>
		<aliyun-java-sdk.version>3.2.8</aliyun-java-sdk.version>
		<aliyun-java-dysmsapi.version>1.1.0</aliyun-java-dysmsapi.version>
		<junit.version>4.12</junit.version>
		<oracle.version>********</oracle.version>
		<qiniu-java-sdk.version>7.2.5</qiniu-java-sdk.version>
		<qiniu-pili-java-sdk.version>2.1.0</qiniu-pili-java-sdk.version>
		<alipay-sdk-java>4.9.9</alipay-sdk-java>
	</properties>
  
	<!-- 依赖管理 -->
	<dependencyManagement>
		<dependencies>
			<!-- oracle驱动 -->
			<dependency>
				<groupId>com.oracle.ojdbc</groupId>
				<artifactId>ojdbc8</artifactId>
				<version>${oracle.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang.version}</version>
			</dependency>
			<!-- jwt -->
			<dependency>
				<groupId>com.auth0</groupId>
				<artifactId>java-jwt</artifactId>
				<version>${jwt.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.code.gson</groupId>
				<artifactId>gson</artifactId>
				<version>${gson.version}</version>
			</dependency>
			<!-- http client -->
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>${httpclient.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpmime</artifactId>
				<version>${httpclient.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore</artifactId>
				<version>${httpcore.version}</version>
			</dependency>
			<!-- POI 文档操作 -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sf.jxls</groupId>
				<artifactId>jxls-core</artifactId>
				<version>${jxls.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sf.jxls</groupId>
				<artifactId>jxls-reader</artifactId>
				<version>${jxls.version}</version>
			</dependency>
			<!-- UE Editor上传依赖包 -->
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>${commons-codec.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-logging</groupId>
				<artifactId>commons-logging</artifactId>
				<version>${commons-logging.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-configuration</groupId>
				<artifactId>commons-configuration</artifactId>
				<version>${commons-configuration.version}</version>
			</dependency>
			<!-- jackson -->
			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-mapper-asl</artifactId>
				<version>${jackson-mapper-asl.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-annotations</artifactId>
				<version>${jackson-annotations.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-core</artifactId>
				<version>${jackson-core.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson-databind.version}</version>
			</dependency>

			<!-- mybatis-plus begin -->
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis-plus-boot-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus</artifactId>
				<version>${mybatisplus.version}</version>
			</dependency>

			<!-- druid连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.protobuf</groupId>
				<artifactId>protobuf-java</artifactId>
				<version>${protobuf-java.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.shiro</groupId>
				<artifactId>shiro-spring</artifactId>
				<version>${shiro.version}</version>
			</dependency>
			<!-- Redis-Jedis -->
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-core</artifactId>
				<version>${aliyun-java-sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
				<version>${aliyun-java-dysmsapi.version}</version>
			</dependency>
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
			</dependency>
			<dependency>
				<groupId>com.qiniu</groupId>
				<artifactId>qiniu-java-sdk</artifactId>
				<version>${qiniu-java-sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.qiniu.pili</groupId>
				<artifactId>pili-sdk-java</artifactId>
				<version>${qiniu-pili-java-sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sdk</groupId>
				<artifactId>alipay-sdk-java</artifactId>
				<version>${alipay-sdk-java}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- spring boot web支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<!-- spring boot aop支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<!-- spring jdbc 支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<!-- spring redis 支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<!-- oracle驱动 jdk8 -->
		<dependency>
			<groupId>com.oracle.ojdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
		</dependency>
		<!-- POI 文档操作 -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.jxls</groupId>
			<artifactId>jxls-core</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.jxls</groupId>
			<artifactId>jxls-reader</artifactId>
		</dependency>
		<!-- UE Editor上传依赖包 -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vaadin.external.google</groupId>
			<artifactId>android-json</artifactId>
			<version>0.0.20131108.vaadin1</version>
		</dependency>

		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
		</dependency>
		<!-- 条形码依赖 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.2.0</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.2.2</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.2.2</version>
		</dependency>

		<!-- jwt -->
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
		</dependency>

		<!-- Redis-Jedis -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<!-- 阿里云相关 OSS -->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>2.8.1</version>
		</dependency>
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.0</version>
		</dependency>

		<dependency>
			<groupId>com.qiniu</groupId>
			<artifactId>qiniu-java-sdk</artifactId>
		</dependency>

		<dependency>
			<groupId>com.qiniu.pili</groupId>
			<artifactId>pili-sdk-java</artifactId>
		</dependency>

		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.jeuclid</groupId>
			<artifactId>jeuclid-fop</artifactId>
			<version>3.1.9</version>
		</dependency>
		<dependency>
			<groupId>net.arnx</groupId>
			<artifactId>wmf2svg</artifactId>
			<version>0.9.7</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.9.2</version>
		</dependency>
		<dependency>
			<groupId>net.sf.saxon</groupId>
			<artifactId>Saxon-HE</artifactId>
			<version>9.7.0-7</version>
		</dependency>
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>2.1.7</version>
		</dependency>
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext-rtf</artifactId>
			<version>2.1.7</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
		</dependency>
		<dependency>
			<groupId>ws.schild</groupId>
			<artifactId>jave-all-deps</artifactId>
			<version>2.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.deepoove</groupId>
			<artifactId>poi-tl</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>24.1-jre</version>
		</dependency>
		<dependency>
		    <groupId>com.itextpdf</groupId>
		    <artifactId>itext-asian</artifactId>
		    <version>5.2.0</version>
		</dependency>
		<dependency>
		    <groupId>com.itextpdf</groupId>
		    <artifactId>itextpdf</artifactId>
		    <version>5.5.11</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.pdfbox</groupId>
		    <artifactId>pdfbox</artifactId>
		    <version>2.0.9</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.pdfbox</groupId>
		    <artifactId>fontbox</artifactId>
		    <version>2.0.9</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.pdfbox</groupId>
		    <artifactId>pdfbox-tools</artifactId>
		    <version>2.0.9</version>
		</dependency>
		<dependency>
			<groupId>net.lingala.zip4j</groupId>
			<artifactId>zip4j</artifactId>
			<version>2.6.4</version>
		</dependency>
		<dependency>
		    <groupId>com.microsoft.sqlserver</groupId>
		    <artifactId>mssql-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.16</version>
		</dependency>
	</dependencies>
</project>