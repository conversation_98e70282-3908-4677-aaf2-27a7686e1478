-- 理工大驾驶舱视图
-- 学院概况-非学历培训
create or replace view view_driver_survey as
select 1                     is_finished,
       nvl(count(1), 0)      xm_count,
       nvl(sum(pd.COUNT), 0) count
from biz_xm xm
         left join BIZ_PLAN_DETAIL pd on pd.id = xm.id and pd.IS_EXECUTE = '1'
where xm.status != 'APPLY'
  and xm.host_org_id = '49BE9432090B4C52B250362945D20509'
  and sysdate > xm.END_TIME
union
select 0                     is_finished,
       nvl(count(1), 0)      xm_count,
       nvl(sum(pd.COUNT), 0) count
from biz_xm xm
         left join BIZ_PLAN_DETAIL pd on pd.id = xm.id and pd.IS_EXECUTE = '1'
where xm.status != 'APPLY'
  and xm.host_org_id = '49BE9432090B4C52B250362945D20509'
  and sysdate <= xm.END_TIME;

-- 国际化教育
create or replace view view_driver_education as
select COUNTRY_NAME,
       COOPERATIVE_UNITS,
       RANK,
       PROJECT_CATEGORY,
       SPECIALITY,
       APPLY_TIME
from INF_INTERNATIONAL_EDUCATION where host_org_id = '49BE9432090B4C52B250362945D20509';
-- 国家级，省级，行业企业共建基地
create or replace view view_driver_base as
select b.ORG, b.NAME, b.APPROVAL_TIME from INF_BASE b where b.host_org_id='49BE9432090B4C52B250362945D20509';

-- 国家级，省级培训基地项目
create or replace view view_driver_base_xm as
select
    '工信部' ORG,
    xm.title,
    xm.start_time,
    pd.SUPPORT_AMOUNT,
    nvl(pd.COUNT, 0) count
from
    biz_xm xm
        left join BIZ_PLAN_DETAIL pd on pd.ID = xm.ID
        left join INF_BASE b on pd.BASE_ID = b.ID
where b.TYPE != '3' and xm.host_org_id = '49BE9432090B4C52B250362945D20509';
-- 开班信息
create or replace view view_driver_classz as
select pd.name, nvl(pd.count, 0) count, nvl(pd.CONTRACT_AMOUNT, 0) CONTRACT_AMOUNT, '100%' p
from BIZ_PLAN_DETAIL pd
where exists(select 1 from biz_xm xm where xm.id = pd.id and xm.status != 'APPLY' and xm.host_org_id='49BE9432090B4C52B250362945D20509')
order by pd.EXECUTE_TIME desc;

-- 师资概况
create or replace view view_driver_teacher as
select type, count, round(count/sum(count)*100, 2) p
from (select case when ui.IS_OUT = '1' then '校外' when ui.IS_OUT = '0' then '校内' end type,
             count(ui.id)                                                               count
      from SYS_USER u
               left join SYS_USER_INFO ui on ui.USER_ID = u.ID
      where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
        and ui.IS_OUT is not null
        and u.ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by ui.IS_OUT
     ) group by type, count;

-- 培训项目来源
create or replace view view_driver_xm_source as
select type, count, round(count/sum(count)*100, 2) p
from (select nvl(t.type, '其他') type, count
      from (select d.DICT_NAME  type,
                   count(xm.ID) count
            from BIZ_XM xm
                     inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
                     inner join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY and d.DICT_CODE = 'industryCategory'
            where xm.STATUS != 'APPLY'
              and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
              and (d.DICT_NAME = '建材建工' or
                   d.DICT_NAME = '汽车' or
                   d.DICT_NAME = '交通')
            group by d.DICT_NAME
            union
            select null         type,
                   count(xm.ID) count
            from BIZ_XM xm
                     inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
                     inner join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY and d.DICT_CODE = 'industryCategory'
            where xm.STATUS != 'APPLY'
              and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
              and ((d.DICT_NAME != '建材建工' and
                    d.DICT_NAME != '汽车' and
                    d.DICT_NAME != '交通') or d.DICT_NAME is null)) t) group by type, count;

-- 校外师资分布
create or replace view view_driver_out_teacher as
select type, count, round(count / (select count(*) from SYS_USER) * 100, 2) p
from (select case
                 when ui.CATEGORY = 'QY' then '企业'
                 when ui.CATEGORY = 'GX' then '高校'
                 when ui.CATEGORY = 'DZGB' then '党政干部' end type,
             count(ui.id)                                      count
      from SYS_USER U
               left join SYS_USER_INFO UI on UI.USER_ID = U.ID
      where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
        and ui.IS_OUT = 1
        and ui.category is not null
        and u.ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by ui.category) group by type, count;

-- 校内师资分布
create or replace view view_driver_in_teacher as
select type, count, round(count / sum(count) * 100, 2) p
from (select ui.CATEGORY  type,
             count(ui.id) count
      from SYS_USER U
               left join SYS_USER_INFO UI on UI.USER_ID = U.ID
      where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
        and ui.IS_OUT = 0
        and ui.category is not null
        and u.ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by ui.category)
group by type, count;

-- 培训类型
create or replace view view_driver_xm_type as
select name, count, round(count / sum(count) * 100, 2) p
from (select d.DICT_NAME  name,
             count(xm.id) count
      from biz_xm xm
               inner join biz_plan_detail pd on pd.id = xm.id
               left join sys_dict d on d.ID = pd.CLASSZ_TYPE and d.DICT_CODE = 'classzType'
      where d.id is not null
        and xm.STATUS != 'APPLY'
        and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by d.DICT_NAME)
group by name, count;

-- 证书统计
create or replace view view_driver_certi as
select year, sum(count) count
from (select to_char(sc.CREATE_TIME, 'yyyy') year, count(1) count
      from BIZ_STUDENT_CERTI sc
      left join biz_xm xm on xm.id = sc.XM_ID
      where xm.STATUS != 'APPLY' and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by to_char(sc.CREATE_TIME, 'yyyy')
      union
      select to_char(ssc.CREATE_TIME, 'yyyy') year, count(1) count
      from BIZ_STUDENT_SKILL_CERTI ssc
      where ssc.HOST_ORG_ID='49BE9432090B4C52B250362945D20509'
      group by to_char(ssc.CREATE_TIME, 'yyyy'))
group by year
order by year desc;

-- 年收入
create or replace view view_driver_income as
select xm.YEARS, nvl(sum(xm.INCOME_AMOUNT), 0) amount, NVL(sum(pd.CONTRACT_AMOUNT), 0) total_amount
from biz_xm xm
         inner JOIN biz_plan_detail pd on pd.id = xm.id
where xm.STATUS != 'APPLY' and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
group by xm.YEARS
order by xm.YEARS desc;

-- 支出
create or replace view view_driver_expend as
select t.type, round(t.amount / sum(pd.CONTRACT_AMOUNT) * 100, 2) p
from (select d.DICT_NAME    type,
             sum(xe.AMOUNT) amount,
             xm.id
      from BIZ_XM_EXPEND xe
               left join SYS_DICT d on d.id = xe.CONTENT
               left join biz_xm xm on xm.id = xe.XM_ID
      where xm.YEARS = to_char(sysdate, 'yyyy')
        and xm.STATUS != 'APPLY'
        and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509'
      group by d.DICT_NAME, xm.id) t
         inner join BIZ_PLAN_DETAIL pd on pd.id = t.id
group by t.type, t.amount;

-- 行业特色培训项目
create or replace view view_driver_feature_xm as
select d.DICT_NAME CATEGORY, xm.title, pd.COUNT, pd.CONTRACT_AMOUNT
from biz_xm xm
         inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
         inner join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY
where pd.INDUSTRY_CATEGORY is not null and xm.STATUS != 'APPLY' and xm.HOST_ORG_ID = '49BE9432090B4C52B250362945D20509';
