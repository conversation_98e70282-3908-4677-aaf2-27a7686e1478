package com.xunw.jxjy.model.enums;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.enums.IEnum;

/**
 * 职业鉴定报名状态
 */
public enum ZyjdBmStatus implements IEnum {

    YTJ("已提交", "0"),
    BH("驳回", "1"),
    SHTG("审核通过", "2"),
    YBY("已毕业","3"),
    QK("缺考","4"),
    DBK("待补考", "5"),
    TRIALTG("初审通过", "6"),
    DTJ("待提交", "7");

    private String name;

    private String id;

    private ZyjdBmStatus(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static ZyjdBmStatus findById(String id) {
        for (ZyjdBmStatus status : ZyjdBmStatus.values()) {
            if (status.id == id) {
                return status;
            }
        }
        return null;
    }

    public static ZyjdBmStatus findByEnumName(String name) {
        for (ZyjdBmStatus status : ZyjdBmStatus.values()) {
            if (status.name() == name) {
                return status;
            }
        }
        return null;
    }


    public static List<Map<String,String>> toList() {
        List<Map<String,String>> list = new ArrayList<>();
        for (ZyjdBmStatus obj : ZyjdBmStatus.values()) {
            Map<String,String> map = new HashMap<>();
            map.put("value", obj.name());
            map.put("text", obj.getName());
            list.add(map);
        }
        return list;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}
    
}
