package com.xunw.jxjy.model.exam.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.PaperCategory;

/**
 * 卷库表
 */
@TableName("biz_paper_repo")
public class PaperRepo implements Serializable {

	private static final long serialVersionUID = 7450523571535301509L;

	// 主键id
	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 试卷名称
	@TableField("name")
	private String name;

	// 卷面总分
	@TableField("total_score")
	private Integer totalScore;

	// 及格分数
	@TableField("passed_score")
	private Integer passedScore;

	// 数据对象（Paper实例的XML字符串）
	@TableField("data")
	private String data;

	// 试卷分类（1-练习、2- 阶段考核、3-我的练习、4-终极考核）
	@TableField("category")
	private PaperCategory category;

	// 题库id
	@TableField("db_id")
	private String dbId;

	// 主办单位id
	@TableField("host_org_id")
	private String hostOrgId;

	// 创建用户id
	@TableField("creator_id")
	private String creatorId;

	// 创建时间
	@TableField("create_time")
	private Date createTime;

    @TableField(exist = false)
    private String dbName;
    
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}

	public Integer getPassedScore() {
		return passedScore;
	}

	public void setPassedScore(Integer passedScore) {
		this.passedScore = passedScore;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public PaperCategory getCategory() {
		return category;
	}

	public void setCategory(PaperCategory category) {
		this.category = category;
	}

	public String getDbId() {
		return dbId;
	}

	public void setDbId(String dbId) {
		this.dbId = dbId;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

}