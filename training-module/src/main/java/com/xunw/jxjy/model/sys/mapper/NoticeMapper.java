package com.xunw.jxjy.model.sys.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.sys.entity.Notice;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @createTime 2020年04月20日 13:59:00
 */
public interface NoticeMapper extends BaseMapper<Notice> {
	
    /**
     * 列表查询
     */
    public List<Map<String,Object>> pageQuery(Map<String,Object> condition, Page<?> page);
    
    /**
     * 获取资讯详情
     */
    public Map<String, Object> getInfoById(String id);

    List<Notice> getByCategoryName(@Param("name") String name, @Param("hostOrgId") String hostOrgId);
}
