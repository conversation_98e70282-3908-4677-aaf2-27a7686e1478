package com.xunw.jxjy.model.zyjd.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.RenderData;
import com.deepoove.poi.util.BytePictureUtils;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.docx.DocxReader;
import com.xunw.jxjy.common.utils.docx.ResourceInfo;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.inf.mapper.*;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.zyjd.entity.BizBmAuditLog;
import com.xunw.jxjy.model.zyjd.entity.ZcFile;
import com.xunw.jxjy.model.zyjd.entity.Attachment;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.mapper.ZcFileMapper;
import com.xunw.jxjy.model.zyjd.mapper.AttachmentMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmBatchMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zyjd.params.ZyjdStudentBmParams;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ZyjdStudentBmService extends BaseCRUDService<ZyjdBmMapper, ZyjdBm>{

	private static final Logger LOGGER = LoggerFactory.getLogger(ZyjdBmService.class);

	@Autowired
	private ZyjdBmBatchMapper bmBatchMapper;
	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private ZyjdFeeSettingMapper feeSettingMapper;
	@Autowired
	private ZyjdProfessionDirectionMapper directionMapper;
	@Autowired
	private ZyjdProfessionDirectionRelationMapper relationMapper;
	@Autowired
	private ZyjdProfessionConditionMapper conditionMapper;
	@Autowired
	private ZyjdProfessionMapper professionMapper;
    @Autowired
    private AttachmentMapper attachmentMapper;
    @Autowired
	private ZyjdBmScopeMapper bmScopeMapper;
    @Autowired
	private ZcFileMapper zcFileMapper;
    @Autowired
	private BizBmAuditLogService bizBmAuditLogService;

	/**
	 * 保存报名信息
	 */
	@Transactional
	public String createBmInfo(ZyjdStudentBmParams params) {
		ZyjdBmBatch bmBatch = bmBatchMapper.selectById(params.getBmbatchId());
		if (bmBatch == null) {
			throw BizException.withMessage("报名批次不存在");
		}
		params.doValidate();
		if(bmBatch.getStatus() == BmOpenStatus.BLOCK) {
			throw BizException.withMessage("当前批次尚未开放报名！");
		}
		Date startTime = bmBatch.getBmStartTime();
		Date endTime = bmBatch.getBmEndTime();
		if (DateUtils.now().before(startTime) || DateUtils.now().after(endTime)) {
			throw BizException.withMessage(
					"当前时间不在报名时间范围内，系统设置的报名时间范围是:" + DateUtils.format(startTime) + "到" + DateUtils.format(endTime));
		}
		// 答辩评审实际申报人数根据设置的做限制
		if (bmBatch.getType() == ZyjdBmBatchType.ZCPS) {
			EntityWrapper<ZyjdBmScope> zyjdBmScopeEntityWrapper = new EntityWrapper<>();
			zyjdBmScopeEntityWrapper.eq("bmbatch_id", params.getBmbatchId());
			zyjdBmScopeEntityWrapper.eq("industry_id", params.getIndustryId());
			zyjdBmScopeEntityWrapper.eq("profession_id", params.getProfessionId());
			List<ZyjdBmScope> zyjdBmScopes = bmScopeMapper.selectList(zyjdBmScopeEntityWrapper);
			if (CollectionUtils.isNotEmpty(zyjdBmScopes)) {
				EntityWrapper<ZyjdBm> entityWrapper = new EntityWrapper<>();
				entityWrapper.eq("bmbatch_id", params.getBmbatchId());
				entityWrapper.eq("industry_id", params.getIndustryId());
				entityWrapper.eq("profession_id", params.getProfessionId());
				Integer count = mapper.selectCount(entityWrapper);
				if (count != null && zyjdBmScopes.get(0).getMaxPersonNum() != null && count >= zyjdBmScopes.get(0).getMaxPersonNum()) {
					throw BizException.withMessage("报名人数已达到设定上限！");
				}
			}
		}
		//当前报名条件是否需要学历证书校验
		if (StringUtils.isNotEmpty(params.getConditionId())) {
			ZyjdProfessionCondition zyjdProfessionCondition = conditionMapper.selectById(params.getConditionId());
			if (Objects.equals(zyjdProfessionCondition.getIsNeedEduCerti(), Constants.YES)) {
				//校验是否填写了学历证书编号
				if (StringUtils.isEmpty(params.getEduCertiNumber())) {
					throw BizException.withMessage("请填写学历证书编号");
				}
				//校验是否上传了学历证书照片
				if (StringUtils.isEmpty(params.getEduCertiPhoto())) {
					throw BizException.withMessage("请上传学历证书照片");
				}
			}
			if (zyjdProfessionCondition.getQualificationType() == QualificationTypeEnum.ZYJN) {
				if (StringUtils.isEmpty(params.getOldCertiProfession()) || params.getOldTechLevel() == null
						|| StringUtils.isEmpty(params.getOldTechCertiNum()) || StringUtils.isEmpty(params.getOldCertiPhoto())) {
					throw BizException.withMessage("现有的报考条件下，原证书（职称证书）职业、等级、编号，原职业技能等级证书（职称证书）均不能为空");
				}
			}
			if (zyjdProfessionCondition.getQualificationType() == QualificationTypeEnum.ZYJS) {
				if (StringUtils.isEmpty(params.getOldCertiProfession()) || params.getOldTechLevel() == null
						|| StringUtils.isEmpty(params.getOldTechCertiNum()) || StringUtils.isEmpty(params.getZc())
						|| StringUtils.isEmpty(params.getOldCertiPhoto())) {
					throw BizException.withMessage("现有的报考条件下，原证书（职称证书）职业、等级、编号、职称，原职业技能等级证书（职称证书）均不能为空");
				}
			}
		}
		List<StudentUser> studentuserList = studentUserMapper.getBySfzh(params.getSfzh(), bmBatch.getHostOrgId());
		StudentUser studentUser = studentuserList.size() > 0 ? studentuserList.get(0) : null;
		if (studentUser == null) {
			studentuserList = studentUserMapper.getByMobile(params.getMobile(), bmBatch.getHostOrgId());
			studentUser = studentuserList.size() > 0 ? studentuserList.get(0) : null;
		}
		if (studentUser == null) {
			studentUser = new StudentUser();
			String sfzh = params.getSfzh();
			studentUser.setId(BaseUtil.generateId2());
			String password = sfzh.substring(sfzh.length() - 6);
			studentUser.setPassword(DigestUtils.md5Hex(password));
			studentUser.setStatus(AccountStatus.OK);
			studentUser.setRegHostOrgId(params.getHostOrgId());
			studentUser.setCompany(params.getCompany());
			studentUserMapper.insert(studentUser);
		}
		else {
			String sfzh = params.getSfzh();
			String password = sfzh.substring(sfzh.length() - 6);
			studentUser.setPassword(DigestUtils.md5Hex(password));
			studentUser.setCompany(params.getCompany());
			studentUserMapper.updateById(studentUser);
		}
		EntityWrapper<StudentInfo> studentInfoWrapper = new EntityWrapper<>();
		studentInfoWrapper.eq("student_id", studentUser.getId());
		List<StudentInfo> studentInfoList = studentInfoMapper.selectList(studentInfoWrapper);
		StudentInfo studentInfo = studentInfoList.size() > 0 ? studentInfoList.get(0) : null;
		//判断手机号同机构是否重复
		Wrapper<StudentInfo> wrapper = new EntityWrapper<StudentInfo>()
				.eq("mobile", params.getMobile())
				.eq("reg_host_org_id", params.getHostOrgId());
		if (studentInfo != null) {
			wrapper.ne("id", studentInfo.getId());
		}
		Integer count = studentInfoMapper.selectCount(wrapper);
		if (count > 0) {
			throw BizException.withMessage(params.getMobile()+"手机号已存在，不能提交报名信息，请联系管理员处理");
		}
		if (studentInfo != null) {
			studentInfo.setSfzh(params.getSfzh());
			studentInfo.setName(params.getName());
			studentInfo.setPoliticalType(params.getPoliticalType());
			studentInfo.setInstructor(params.getInstructor());
			studentInfo.setGender(params.getGender());
			studentInfo.setNation(params.getNation());
			studentInfo.setMobile(params.getMobile());
			studentInfo.setAddress(params.getAddress());
			studentInfo.setPostCode(params.getPostCode());
			studentInfo.setEducation(params.getEducation());
			studentInfo.setKsly(params.getKsly());
			studentInfo.setBirthday(params.getBirthday());
			studentInfo.setSfzzm(params.getSfzzm());
			studentInfo.setSfzfm(params.getSfzfm());
			if (bmBatch.getType() != ZyjdBmBatchType.TDDZCL) {
				studentInfo.setEduCertiPhoto(params.getEduCertiPhoto());
			}
			studentInfo.setEduCertiNumber(params.getEduCertiNumber());
			studentInfo.setStudentPhoto(params.getStudentPhoto());
			studentInfo.setGraduateSchool(params.getGraduateSchool());
			studentInfo.setCollege(params.getCollege());
			studentInfo.setSpecialty(params.getSpecialty());
			studentInfo.setClassz(params.getClassz());
			studentInfo.setStudentNum(params.getStudentNum());
			studentInfo.setStudentTicket(params.getStudentTicket());
			studentInfo.setGw(params.getGw());
			studentInfo.setCompanyProvinceCode(params.getCompanyProvinceCode());
			studentInfo.setCompanyCityCode(params.getCompanyCityCode());
			studentInfo.setCompanyDistrictCode(params.getCompanyDistrictCode());
			studentInfo.setCertiAddress(params.getCertiAddress());
			studentInfo.setPhone(params.getPhone());
			studentInfo.setEmail(params.getEmail());
			studentInfo.setQq(params.getQq());
			studentInfo.setWxh(params.getWxh());
			studentInfo.setGraduateTime(DateUtils.parse(params.getGraduateTime(), "yyyy-MM-dd"));
			studentInfo.setCertiType(params.getCertiType());
			studentInfo.setResume(params.getResume());
			studentInfo.setSchoolTime(DateUtils.parse(params.getSchoolTime(),"yyyy-MM-dd"));
			studentInfo.setZc(params.getZc());
			studentInfoMapper.updateById(studentInfo);
		} else {
			studentInfo = new StudentInfo();
			studentInfo.setId(BaseUtil.generateId2());
			studentInfo.setStudentId(studentUser.getId());
			studentInfo.setPoliticalType(params.getPoliticalType());
			studentInfo.setSfzh(params.getSfzh());
			studentInfo.setName(params.getName());
			studentInfo.setGender(params.getGender());
			studentInfo.setNation(params.getNation());
			studentInfo.setMobile(params.getMobile());
			studentInfo.setInstructor(params.getInstructor());
			studentInfo.setAddress(params.getAddress());
			studentInfo.setBirthday(params.getBirthday());
			studentInfo.setPostCode(params.getPostCode());
			studentInfo.setEducation(params.getEducation());
			studentInfo.setKsly(params.getKsly());
			studentInfo.setSfzzm(params.getSfzzm());
			studentInfo.setSfzfm(params.getSfzfm());
			studentInfo.setSchoolTime(DateUtils.parse(params.getSchoolTime(),"yyyy-MM-dd"));
			if (bmBatch.getType() != ZyjdBmBatchType.TDDZCL) {
				studentInfo.setEduCertiPhoto(params.getEduCertiPhoto());
			}
			studentInfo.setEduCertiNumber(params.getEduCertiNumber());
			studentInfo.setStudentPhoto(params.getStudentPhoto());
			studentInfo.setCreateTime(DateUtils.now());
			studentInfo.setGraduateSchool(params.getGraduateSchool());
			studentInfo.setCollege(params.getCollege());
			studentInfo.setSpecialty(params.getSpecialty());
			studentInfo.setClassz(params.getClassz());
			studentInfo.setStudentNum(params.getStudentNum());
			studentInfo.setStudentTicket(params.getStudentTicket());
			studentInfo.setGw(params.getGw());
			studentInfo.setCompanyProvinceCode(params.getCompanyProvinceCode());
			studentInfo.setCompanyCityCode(params.getCompanyCityCode());
			studentInfo.setCompanyDistrictCode(params.getCompanyDistrictCode());
			studentInfo.setCertiAddress(params.getCertiAddress());
			studentInfo.setPhone(params.getPhone());
			studentInfo.setEmail(params.getEmail());
			studentInfo.setQq(params.getQq());
			studentInfo.setWxh(params.getWxh());
			studentInfo.setResume(params.getResume());
			studentInfo.setGraduateTime(DateUtils.parse(params.getGraduateTime(), "yyyy-MM-dd"));
			studentInfo.setZc(params.getZc());
			studentInfo.setRegHostOrgId(params.getHostOrgId());
			studentInfoMapper.insert(studentInfo);
		}
		EntityWrapper<ZyjdBm> bmWrapper = new EntityWrapper<>();
		bmWrapper.eq("bmbatch_id", bmBatch.getId());
		bmWrapper.eq("student_id", studentUser.getId());
		List<ZyjdBm> bmList = mapper.selectList(bmWrapper);
		String isAllowMultipleProfession = StringUtils.isEmpty(bmBatch.getIsAllowMultiProfession()) ? Constants.NO : bmBatch.getIsAllowMultiProfession();
		if (bmList.size() > 0 ) {
			Set<String> oldBmProfessionIds = bmList.stream().map(x->x.getProfessionId()).distinct().collect(Collectors.toSet());
			if (oldBmProfessionIds.contains(params.getProfessionId())) {
				throw BizException.withMessage("您在当前批次下已经报名过相同的职业，请勿重复报名！");
			}
			else {
				if (Constants.NO.equals(isAllowMultipleProfession)) {
					throw BizException.withMessage("你已经报名过该批次，请勿重复报名！");
				}
			}
		}
		ZyjdBm zyjdBm = new ZyjdBm();
		zyjdBm.setId(BaseUtil.generateId2());
		zyjdBm.setCertiPost(params.getCertiPost());//新加字段
		zyjdBm.setPerformance(params.getPerformance());//业绩证明
		zyjdBm.setOldCertiProfession(params.getOldCertiProfession());//新加字段
		zyjdBm.setBmbatchId(bmBatch.getId());
		zyjdBm.setKsly(params.getKsly());
		zyjdBm.setCommitment(params.getCommitment());//承诺书
		zyjdBm.setWorkTime(DateUtils.parse(params.getWorkTime(), "yyyy-MM-dd"));
		zyjdBm.setProfession(params.getProfession());//专业技术职务
		zyjdBm.setXrzyjszwpw(params.getXrzyjszwpw());
		zyjdBm.setInvoiceTitle(params.getInvoiceTitle());
		zyjdBm.setTaxpayerNumber(params.getTaxpayerNumber());
		zyjdBm.setWorkYears(params.getWorkYears());
		zyjdBm.setJobYears(params.getJobYears());
		zyjdBm.setIndustryId(params.getIndustryId());
		zyjdBm.setProfessionId(params.getProfessionId());
		zyjdBm.setApplyTechLevel(params.getApplyTechLevel());
		zyjdBm.setDirectionId(params.getDirectionId());
		zyjdBm.setOldTechLevel(StringUtils.isNotEmpty(params.getOldTechLevel())? TechLevel.valueOf(params.getOldTechLevel()) : null);
		zyjdBm.setOldTechLevelTime(DateUtils.parse(params.getOldTechLevelTime(), "yyyy-MM-dd"));

		zyjdBm.setOldTechCertiNum(params.getOldTechCertiNum());
		zyjdBm.setOldCertiPhoto(params.getOldCertiPhoto());
		zyjdBm.setOldCertiName(params.getOldCertiName());

		zyjdBm.setSaveTime(DateUtils.now());
		zyjdBm.setSubmitTime(DateUtils.now());
		//事业单位工人技能报名 默认审核通过  王能祝 2020-11-19
		if (bmBatch.getType() == ZyjdBmBatchType.SYDW) {
			zyjdBm.setStatus(ZyjdBmStatus.SHTG);
		} else if (bmBatch.getType() == ZyjdBmBatchType.TDDZCL) {
			zyjdBm.setStatus(ZyjdBmStatus.DTJ);//地质测绘默认待提交
		} else {
			zyjdBm.setStatus(ZyjdBmStatus.YTJ);
		}
		zyjdBm.setPayStatus(PayStatus.WJ);
		zyjdBm.setConditionId(params.getConditionId());
		zyjdBm.setStudentId(studentUser.getId());
		zyjdBm.setProfessionYears(params.getProfessionYears());
		zyjdBm.setCurrentJobTime(params.getCurrentJobTime());
		zyjdBm.setTestWay(params.getTestWay());
		zyjdBm.setZyjsrzzgzs(params.getZyjsrzzgzs());
		zyjdBm.setCurrentJob(params.getCurrentJob());
		zyjdBm.setFullEducation(params.getFullEducation());
		zyjdBm.setFullSpecialty(params.getFullSpecialty());
		zyjdBm.setFullEduTime(DateUtils.parse(params.getFullEduTime(), "yyyy-MM-dd"));
		zyjdBm.setJobEducation(params.getJobEducation());
		zyjdBm.setJobSpecialty(params.getJobSpecialty());
		zyjdBm.setJobEduTime(DateUtils.parse(params.getJobEduTime(), "yyyy-MM-dd"));
		zyjdBm.setRecommendCode(params.getRecommendCode());
		zyjdBm.setIsReevaluation(params.getIsReevaluation());
		zyjdBm.setIsExceptional(params.getIsExceptional());

		//土地地质测量-论文相关
		if(ZyjdBmBatchType.TDDZCL == bmBatch.getType()) {
			//学历证书附件
			JSONArray eduCertiPhotos = JSONArray.parseArray(params.getEduCertiPhoto());
		    List<Attachment> attachments = eduCertiPhotos.stream().map(e->{
				JSONObject j = (JSONObject) e;
				return new Attachment(BaseUtil.generateId(), j.getString("name"), j.getString("url"), zyjdBm.getId());
			}).collect(Collectors.toList());
			attachmentMapper.delete(new EntityWrapper<Attachment>().eq("yw_id", zyjdBm.getId()));
			DBUtils.insertBatch(attachments, Attachment.class);

			if (TechLevel.ZGAO == params.getApplyTechLevel()) {
				zyjdBm.setArticleTital(params.getArticleTital());
				zyjdBm.setArticlePhotoUrl(params.getArticlePhotoUrl());
				zyjdBm.setIsArticlePublish(params.getIsArticlePublish());
				if (Constants.YES.equals(params.getIsArticlePublish())) {
					zyjdBm.setDdzz(params.getDdzz());
					zyjdBm.setFbqk(params.getFbqk());
					zyjdBm.setNd(params.getNd());
					zyjdBm.setQh(params.getQh());
				}
			}
		}
		if (bmBatch.getType() != ZyjdBmBatchType.ZYJS) {
			ZyjdProfession profession = professionMapper.selectById(params.getProfessionId());
			zyjdBm.setIndustryId(profession.getIndustryId());
		}
		zyjdBm.setOldCertiPhoto(params.getOldCertiPhoto());
		//工作年限证明
		zyjdBm.setGznxzm(params.getGznxzm());
		//报名(类型)
		zyjdBm.setBmlx(params.getBmlx());
		//培训方式
		zyjdBm.setPxfs(params.getPxfs());

		//专业技术人员专用字段
		zyjdBm.setRegionalismCode(params.getRegionalismCode());//行政区划代码
		zyjdBm.setXb(params.getXb());//性别
		zyjdBm.setCertiCategory(params.getCertiCategory());//证件类型
		zyjdBm.setXl(params.getXl());//学历
		zyjdBm.setXw(params.getXw());//学位
		zyjdBm.setSocialCreditCode(params.getSocialCreditCode());//所在单位统一社会信用代码
		zyjdBm.setUnitNature(params.getUnitNature());//所在单位性质
		zyjdBm.setIsProvideJxjy(params.getIsProvideJxjy());//是否由所在单位提供继续教育培训
		zyjdBm.setObtainCertiCategory(params.getObtainCertiCategory());//取得证书类型
		zyjdBm.setZcSeries(params.getZcSeries());//职称系列
		zyjdBm.setZyQualificationName(params.getZyQualificationName());//职业资格名称
		zyjdBm.setMz(params.getMz());
		zyjdBm.setZcLevel(params.getZcLevel());
		zyjdBm.setZyQualificationLevel(params.getZyQualificationLevel());
		zyjdBm.setReceipt(params.getReceipt());//报名回执

		//答辩评审字段
		zyjdBm.setReward(params.getReward());
		zyjdBm.setAchievement(params.getAchievement());
		zyjdBm.setWorkExperience(params.getWorkExperience());
		zyjdBm.setWorkExperienceDl(params.getWorkExperienceDl());
		zyjdBm.setTrainingExperience(params.getTrainingExperience());
		zyjdBm.setJsWorkAchievement(params.getJsWorkAchievement());
		zyjdBm.setSkillSpeciality(params.getSkillSpeciality());
		zyjdBm.setXm(params.getXm());
		zyjdBm.setOpus(params.getOpus());
		zyjdBm.setCompetition(params.getCompetition());
		zyjdBm.setBearContent(params.getBearContent());
		zyjdBm.setHonor(params.getHonor());
		zyjdBm.setTechnicalSummary(params.getTechnicalSummary());
		zyjdBm.setThesis(params.getThesis());
		zyjdBm.setJsTime(DateUtils.parse(params.getJsTime(), "yyyy-MM-dd"));
		zyjdBm.setSpeciality(params.getSpeciality());
		zyjdBm.setUnitName(params.getUnitName());
		zyjdBm.setAppraisalForms(params.getAppraisalForms());
		zyjdBm.setTechnicalSummaryForms(params.getTechnicalSummaryForms());
		zyjdBm.setThesisForms(params.getThesisForms());
		zyjdBm.setJsWorkAchievementForms(params.getJsWorkAchievementForms());
		zyjdBm.setLatentAbilityForms(params.getLatentAbilityForms());
		zyjdBm.setCertificatesForms(params.getCertificatesForms());
		mapper.insert(zyjdBm);
		if (bmBatch.getType() == ZyjdBmBatchType.ZCPS) {
			JSONArray files = JSON.parseArray(params.getAttachments());
			List<ZcFile> list = new ArrayList<>();
			for (Object file : files) {
				JSONObject f = (JSONObject) file;
                ZcFile zcFile = new ZcFile();
				zcFile.setId(BaseUtil.generateId());
				zcFile.setBmId(zyjdBm.getId());
				zcFile.setName(f.getString("name"));
				zcFile.setUrl(f.getString("url"));
				zcFile.setCreatedBy(studentUser.getId());
				zcFile.setCreatedTime(new Date());
				list.add(zcFile);
			}
			DBUtils.insertBatch(list, ZcFile.class);
		}
		return zyjdBm.getId();
	}

	/**
	 * 报名信息修改
	 */
	@Transactional
	public void editBmInfo(ZyjdStudentBmParams params, ZyjdBm zyjdBm) {
		ZyjdBmBatch bmBatch = bmBatchMapper.selectById(params.getBmbatchId());
		params.doValidate();
/*		Date startTime = bmBatch.getBmStartTime();
		Date endTime = bmBatch.getBmEndTime();
		if (DateUtils.now().before(startTime) || DateUtils.now().after(endTime)) {
			throw BizException.withMessage(
					"当前时间不在报名时间范围内，无法修改报名信息,系统设置的报名时间范围是:" + DateUtils.format(startTime) + "到" + DateUtils.format(endTime));
		}*/
		EntityWrapper<ZyjdBm> checkWrapper = new EntityWrapper();
		checkWrapper.eq("bmbatch_id", zyjdBm.getBmbatchId());
		checkWrapper.eq("student_id", zyjdBm.getStudentId());
		checkWrapper.eq("profession_id", params.getProfessionId());
		checkWrapper.ne("id", zyjdBm.getId());
		if (mapper.selectCount(checkWrapper) > 0) {
			throw BizException.withMessage("您在当前批次下已经报名过相同的职业，请勿重复报名！");
		}
		StudentUser studentUser = studentUserMapper.selectById(zyjdBm.getStudentId());
		if(studentUser != null) {
			String sfzh = params.getSfzh();
			String password = sfzh.substring(sfzh.length() - 6);
			studentUser.setPassword(DigestUtils.md5Hex(password));
			studentUser.setCompany(params.getCompany());
			studentUserMapper.updateById(studentUser);
		}

		//身份证号唯一性校验
		EntityWrapper<StudentInfo> wrapper = new EntityWrapper<>();
		wrapper.eq("sfzh", params.getSfzh());
		wrapper.eq("reg_host_org_id", params.getHostOrgId());
		wrapper.ne("student_id", zyjdBm.getStudentId());
		Integer count = studentInfoMapper.selectCount(wrapper);
		if (count>0) {
			throw BizException.withMessage("身份证号在系统中重复，请核对身份证号");
		}
		
		wrapper = new EntityWrapper<>();
		wrapper.eq("mobile", params.getMobile());
		wrapper.eq("reg_host_org_id", params.getHostOrgId());
		wrapper.ne("student_id", zyjdBm.getStudentId());
		count = studentInfoMapper.selectCount(wrapper);
		if (count>0) {
			throw BizException.withMessage("手机号在系统中重复，请核对手机号");
		}

		StudentInfo studentInfo = studentInfoMapper.getByStudentId(zyjdBm.getStudentId());
		if (studentInfo != null) {
			studentInfo.setPoliticalType(params.getPoliticalType());
			studentInfo.setSfzh(params.getSfzh());
			studentInfo.setName(params.getName());
			studentInfo.setGender(params.getGender());
			studentInfo.setNation(params.getNation());
			studentInfo.setMobile(params.getMobile());
			studentInfo.setAddress(params.getAddress());
			studentInfo.setBirthday(params.getBirthday());
			studentInfo.setPostCode(params.getPostCode());
			studentInfo.setEducation(params.getEducation());
			studentInfo.setKsly(params.getKsly());
			studentInfo.setSfzzm(params.getSfzzm());
			studentInfo.setSfzfm(params.getSfzfm());
			if (bmBatch.getType() != ZyjdBmBatchType.TDDZCL) {
				studentInfo.setEduCertiPhoto(params.getEduCertiPhoto());
			}
			studentInfo.setEduCertiNumber(params.getEduCertiNumber());
			studentInfo.setStudentPhoto(params.getStudentPhoto());
			studentInfo.setGraduateSchool(params.getGraduateSchool());
			studentInfo.setCreateTime(DateUtils.now());
			studentInfo.setCollege(params.getCollege());
			studentInfo.setSpecialty(params.getSpecialty());
			studentInfo.setClassz(params.getClassz());
			studentInfo.setStudentNum(params.getStudentNum());
			studentInfo.setStudentTicket(params.getStudentTicket());
			studentInfo.setGw(params.getGw());
			studentInfo.setCompanyProvinceCode(params.getCompanyProvinceCode());
			studentInfo.setCompanyCityCode(params.getCompanyCityCode());
			studentInfo.setCompanyDistrictCode(params.getCompanyDistrictCode());
			studentInfo.setCertiAddress(params.getCertiAddress());
			studentInfo.setPhone(params.getPhone());
			studentInfo.setEmail(params.getEmail());
			studentInfo.setQq(params.getQq());
			studentInfo.setWxh(params.getWxh());
			studentInfo.setGraduateTime(DateUtils.parse(params.getGraduateTime(), "yyyy-MM-dd"));
			studentInfo.setCertiType(params.getCertiType());
			studentInfo.setResume(params.getResume());
			studentInfo.setSchoolTime(DateUtils.parse(params.getSchoolTime(),"yyyy-MM-dd"));
			studentInfo.setInstructor(params.getInstructor());
			studentInfo.setZc(params.getZc());
			studentInfoMapper.updateById(studentInfo);
		}
		//如果之前是驳回状态,那么把状态重置为待审核
		if (zyjdBm.getStatus() == ZyjdBmStatus.BH){
			zyjdBm.setStatus(ZyjdBmStatus.YTJ);
		}
		zyjdBm.setCertiPost(params.getCertiPost());
		zyjdBm.setConditionId(params.getConditionId());
		zyjdBm.setPerformance(params.getPerformance());//业绩证明
		zyjdBm.setOldCertiProfession(params.getOldCertiProfession());//新加字段
		zyjdBm.setBmbatchId(bmBatch.getId());
		zyjdBm.setKsly(params.getKsly());
		zyjdBm.setCommitment(params.getCommitment());//承诺书 田军
		zyjdBm.setWorkTime(DateUtils.parse(params.getWorkTime(), "yyyy-MM-dd"));
		zyjdBm.setProfession(params.getProfession());//专业技术职务
		zyjdBm.setXrzyjszwpw(params.getXrzyjszwpw());
		zyjdBm.setInvoiceTitle(params.getInvoiceTitle());
		zyjdBm.setTaxpayerNumber(params.getTaxpayerNumber());
		zyjdBm.setWorkYears(params.getWorkYears());
		zyjdBm.setJobYears(params.getJobYears());
		zyjdBm.setIndustryId(params.getIndustryId());
		zyjdBm.setProfessionId(params.getProfessionId());
		zyjdBm.setApplyTechLevel(params.getApplyTechLevel());
		zyjdBm.setDirectionId(params.getDirectionId());
		zyjdBm.setOldTechLevel(StringUtils.isNotEmpty(params.getOldTechLevel())? TechLevel.valueOf(params.getOldTechLevel()) : null);
		zyjdBm.setOldTechLevelTime(DateUtils.parse(params.getOldTechLevelTime(), "yyyy-MM-dd"));

		zyjdBm.setOldTechCertiNum(params.getOldTechCertiNum());
		zyjdBm.setOldCertiPhoto(params.getOldCertiPhoto());
		zyjdBm.setOldCertiName(params.getOldCertiName());
		zyjdBm.setSaveTime(DateUtils.now());
		zyjdBm.setSubmitTime(DateUtils.now());
		// 事业单位工人技能报名 默认审核通过  王能祝 2020-11-19
		if (bmBatch.getType() == ZyjdBmBatchType.SYDW) {
			zyjdBm.setStatus(ZyjdBmStatus.SHTG);
		} else if (bmBatch.getType() == ZyjdBmBatchType.TDDZCL) {
			zyjdBm.setStatus(ZyjdBmStatus.DTJ); //地质测绘默认待提交
		} else {
			zyjdBm.setStatus(ZyjdBmStatus.YTJ);
		}
		zyjdBm.setStudentId(studentUser.getId());
		zyjdBm.setJobYears(params.getJobYears());
		zyjdBm.setProfessionYears(params.getProfessionYears());
		zyjdBm.setCurrentJobTime(params.getCurrentJobTime());
		zyjdBm.setTestWay(params.getTestWay());
		zyjdBm.setZyjsrzzgzs(params.getZyjsrzzgzs());
		zyjdBm.setCurrentJob(params.getCurrentJob());
		zyjdBm.setFullEducation(params.getFullEducation());
		zyjdBm.setFullSpecialty(params.getFullSpecialty());

		zyjdBm.setFullEduTime(DateUtils.parse(params.getFullEduTime(), "yyyy-MM-dd"));

		//在职
		zyjdBm.setJobEducation(params.getJobEducation());
		zyjdBm.setJobSpecialty(params.getJobSpecialty());
		zyjdBm.setJobEduTime(DateUtils.parse(params.getJobEduTime(), "yyyy-MM-dd"));
		zyjdBm.setRecommendCode(params.getRecommendCode());

		zyjdBm.setIsReevaluation(params.getIsReevaluation());
		zyjdBm.setIsExceptional(params.getIsExceptional());

		//土地地质测量-论文相关
		if(ZyjdBmBatchType.TDDZCL == bmBatch.getType()) {
			//学历证书附件
			JSONArray eduCertiPhotos = JSONArray.parseArray(params.getEduCertiPhoto());
			List<Attachment> attachments = eduCertiPhotos.stream().map(e -> {
				JSONObject j = (JSONObject) e;
				return new Attachment(BaseUtil.generateId(), j.getString("name"), j.getString("url"), zyjdBm.getId());
			}).collect(Collectors.toList());
			attachmentMapper.delete(new EntityWrapper<Attachment>().eq("yw_id", zyjdBm.getId()));
			DBUtils.insertBatch(attachments, Attachment.class);

			zyjdBm.setArticleTital(params.getArticleTital());
			zyjdBm.setArticlePhotoUrl(params.getArticlePhotoUrl());
			zyjdBm.setIsArticlePublish(params.getIsArticlePublish());
			zyjdBm.setDdzz(params.getDdzz());
			zyjdBm.setFbqk(params.getFbqk());
			zyjdBm.setNd(params.getNd());
			zyjdBm.setQh(params.getQh());
		}
		if (bmBatch.getType() != ZyjdBmBatchType.ZYJS) {
			ZyjdProfession profession = professionMapper.selectById(zyjdBm.getProfessionId());
			zyjdBm.setIndustryId(profession.getIndustryId());
		}
		zyjdBm.setOldCertiPhoto(params.getOldCertiPhoto());
		zyjdBm.setGznxzm(params.getGznxzm());
		zyjdBm.setBmlx(params.getBmlx());
		zyjdBm.setPxfs(params.getPxfs());

		//专业技术人员专用字段
		zyjdBm.setRegionalismCode(params.getRegionalismCode());//行政区划代码
		zyjdBm.setXb(params.getXb());//性别
		zyjdBm.setCertiCategory(params.getCertiCategory());//证件类型
		zyjdBm.setXl(params.getXl());//学历
		zyjdBm.setXw(params.getXw());//学位
		zyjdBm.setSocialCreditCode(params.getSocialCreditCode());//所在单位统一社会信用代码
		zyjdBm.setUnitNature(params.getUnitNature());//所在单位性质
		zyjdBm.setIsProvideJxjy(params.getIsProvideJxjy());//是否由所在单位提供继续教育培训
		zyjdBm.setObtainCertiCategory(params.getObtainCertiCategory());//取得证书类型
		zyjdBm.setZcSeries(params.getZcSeries());//职称系列
		zyjdBm.setZyQualificationName(params.getZyQualificationName());//职业资格名称
		zyjdBm.setMz(params.getMz());
		zyjdBm.setZcLevel(params.getZcLevel());
		zyjdBm.setZyQualificationLevel(params.getZyQualificationLevel());
		zyjdBm.setReceipt(params.getReceipt());//报名回执

		//答辩评审字段
		zyjdBm.setReward(params.getReward());
		zyjdBm.setAchievement(params.getAchievement());
		zyjdBm.setWorkExperience(params.getWorkExperience());
		zyjdBm.setWorkExperienceDl(params.getWorkExperienceDl());
		zyjdBm.setTrainingExperience(params.getTrainingExperience());
		zyjdBm.setJsWorkAchievement(params.getJsWorkAchievement());
		zyjdBm.setSkillSpeciality(params.getSkillSpeciality());
		zyjdBm.setXm(params.getXm());
		zyjdBm.setOpus(params.getOpus());
		zyjdBm.setCompetition(params.getCompetition());
		zyjdBm.setBearContent(params.getBearContent());
		zyjdBm.setHonor(params.getHonor());
		zyjdBm.setTechnicalSummary(params.getTechnicalSummary());
		zyjdBm.setThesis(params.getThesis());
		zyjdBm.setJsTime(DateUtils.parse(params.getJsTime(), "yyyy-MM-dd"));
		zyjdBm.setSpeciality(params.getSpeciality());
		zyjdBm.setUnitName(params.getUnitName());
		zyjdBm.setAppraisalForms(params.getAppraisalForms());
		zyjdBm.setTechnicalSummaryForms(params.getTechnicalSummaryForms());
		zyjdBm.setThesisForms(params.getThesisForms());
		zyjdBm.setJsWorkAchievementForms(params.getJsWorkAchievementForms());
		zyjdBm.setLatentAbilityForms(params.getLatentAbilityForms());
		zyjdBm.setCertificatesForms(params.getCertificatesForms());
		mapper.updateAllColumnById(zyjdBm);
		if (bmBatch.getType() == ZyjdBmBatchType.ZCPS) {
			JSONArray files = JSON.parseArray(params.getAttachments());
			zcFileMapper.delete(new EntityWrapper<ZcFile>().eq("bm_id", zyjdBm.getId()));
			List<ZcFile> list = new ArrayList<>();
			for (Object file : files) {
				JSONObject f = (JSONObject) file;
				ZcFile zcFile = new ZcFile();
				zcFile.setId(BaseUtil.generateId());
				zcFile.setBmId(zyjdBm.getId());
				zcFile.setName(f.getString("name"));
				zcFile.setUrl(f.getString("url"));
				zcFile.setCreatedBy(studentUser.getId());
				zcFile.setCreatedTime(new Date());
				list.add(zcFile);
			}
			DBUtils.insertBatch(list, ZcFile.class);
		}

		// 修改报名信息后重置审核记录的is_finish值
		BizBmAuditLog bizBmAuditLog = new BizBmAuditLog();
		bizBmAuditLog.setIsFinish(0);
		EntityWrapper<BizBmAuditLog> bizBmAuditLogEntityWrapper = new EntityWrapper<>();
		bizBmAuditLogEntityWrapper.eq("BM_ID", zyjdBm.getId());
		bizBmAuditLogService.update(bizBmAuditLog, bizBmAuditLogEntityWrapper);
	}

	/**
	 * 获取报名详情
	 */
	public Map<String, Object> getDetailsById(String id) {
		return this.mapper.getDetailsById(id);
	}

	/**
	 * 获取准考证（考试通知单）
	 */
	public Map<String, Object> getZkzById(String id) {
		return this.mapper.getZkzById(id);
	}
	
	
	/**
	 *  职业技能等级认定报名费用匹配规则优先级：
	 *  1、批次+职业+等级
	 *  2、批次+等级
	 *  3、职业+等级
	 *  4、等级
	 *  5、只根据行业匹配
	 */
	public ZyjdFeeSetting getFeeConfig(String id, String hostOrgId) {
		ZyjdBm zyjdBm = mapper.selectById(id);
		ZyjdProfession profession = professionMapper.selectById(zyjdBm.getProfessionId());
		List<ZyjdFeeSetting> settings = new ArrayList<>();
		if (profession == null) {
			EntityWrapper<ZyjdFeeSetting> wrapper = new EntityWrapper<>();
			wrapper.eq("host_org_id", hostOrgId);
			{
				wrapper.eq("batch_id", zyjdBm.getBmbatchId());
			}
			settings = feeSettingMapper.selectList(wrapper);
		} else {
			EntityWrapper<ZyjdFeeSetting> wrapper = new EntityWrapper<>();
			wrapper.eq("industry_id", profession.getIndustryId());
			wrapper.eq("host_org_id", hostOrgId);
			{
				wrapper.eq("batch_id", zyjdBm.getBmbatchId());
				wrapper.eq("profession_id",  zyjdBm.getProfessionId());
				wrapper.eq("profession_level", zyjdBm.getApplyTechLevel().name());
			}
			settings = feeSettingMapper.selectList(wrapper);
			if (settings.size() == 0) {
				wrapper = new EntityWrapper<>();
				wrapper.eq("industry_id", profession.getIndustryId());
				wrapper.eq("host_org_id", hostOrgId);
				{
					wrapper.isNull("profession_id");
					wrapper.eq("batch_id", zyjdBm.getBmbatchId());
					wrapper.eq("profession_level", zyjdBm.getApplyTechLevel().name());
				}
				settings = feeSettingMapper.selectList(wrapper);
			}
			if (settings.size() == 0) {
				wrapper = new EntityWrapper<>();
				wrapper.eq("industry_id", profession.getIndustryId());
				wrapper.eq("host_org_id", hostOrgId);
				{
					wrapper.isNull("batch_id");
					wrapper.eq("profession_id", zyjdBm.getProfessionId());
					wrapper.eq("profession_level", zyjdBm.getApplyTechLevel().name());
				}
				settings = feeSettingMapper.selectList(wrapper);
			}
			if (settings.size() == 0) {
				wrapper = new EntityWrapper<>();
				wrapper.eq("industry_id", profession.getIndustryId());
				wrapper.eq("host_org_id", hostOrgId);
				{
					wrapper.isNull("batch_id");
					wrapper.isNull("profession_id");
					wrapper.eq("profession_level", zyjdBm.getApplyTechLevel().name());
				}
				settings = feeSettingMapper.selectList(wrapper);
			}
			if (settings.size() == 0) {
				wrapper = new EntityWrapper<>();
				wrapper.eq("industry_id", profession.getIndustryId());
				wrapper.eq("host_org_id", hostOrgId);
				{
					wrapper.isNull("batch_id");
					wrapper.isNull("profession_id");
					wrapper.isNull("profession_level");
				}
				settings = feeSettingMapper.selectList(wrapper);
			}
		}
		return settings.size() > 0 ? settings.get(0) : null;
	}

	/**
	 *  获取报名费用
	 */
	public Double getBmFee(String id, String hostOrgId) {
		ZyjdFeeSetting feeSetting = getFeeConfig(id, hostOrgId);
		return feeSetting != null ? feeSetting.getAmount() : null;
	}
	
	/**
	 *  获取培训费用
	 */
	public Double getUpgradeFee(String id, String hostOrgId) {
		ZyjdFeeSetting feeSetting = getFeeConfig(id, hostOrgId);
		return feeSetting != null ? feeSetting.getUpgradeAmount() : null;
	}

	/**
	 * 土地地质测量报名表下载
	 */
	public File creatTddzlclBmb(Map<String, Object> map, boolean isBs){
		File newDocx = null;
		DocxReader templetDocxReader = null;
		Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
		try {
			{// 拷贝一份模板文件到临时文件夹里面
				FileOutputStream fos = null;
				InputStream is = null;
				try {
					newDocx = FileHelper.createTmpFile();
					fos = new FileOutputStream(newDocx);
					String dir = null;
					if(isBs) {
						dir = "/docxTmps/tddzcl-bishi-sbb.docx";//笔试报名表
					}
					else{
						dir = "/docxTmps/tddzcl-mianshi-sbb.docx";//面试报名表
					}
					is = this.getClass().getResourceAsStream(dir);
					IOUtils.copyLarge(is, fos);
					fos.flush();
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fos);
				}
			}
			templetDocxReader = new DocxReader(newDocx);
			templetDocxReader.doRead();
			{
				needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(), templetDocxReader.getDocumentInfo());
				File document = templetDocxReader.getDocumentInfo().getFile();
				FileOutputStream fosNew = null;
				FileOutputStream fos = null;
				InputStream is = null;
				try {
					is = new FileInputStream(document);
					String docStr = IOUtils.toString(is, "UTF-8");
					if(BaseUtil.isNotEmpty(map)) {
						String name =  BaseUtil.convertNullToEmpty(map.get("name"));
						String sfzh =  BaseUtil.convertNullToEmpty(map.get("sfzh"));
						String gender =  BaseUtil.convertNullToEmpty(map.get("gender"));
						gender = Gender.M.name().equals(gender) ? "男" : "女";
						String jobYears = BaseUtil.convertNullToEmpty(map.get("jobYears"));
						String currentJob = BaseUtil.convertNullToEmpty(map.get("currentJob"));
						String fullEducation = BaseUtil.convertNullToEmpty(map.get("fullEducation"));
						if(StringUtils.isNotEmpty(fullEducation)) {
							fullEducation = Education.valueOf(fullEducation).getName();
						}
						String fullSpecialty = BaseUtil.convertNullToEmpty(map.get("fullSpecialty"));
						String fullEduTime = BaseUtil.convertNullToEmpty(map.get("fullEduTime"));
						//在职毕业时间
						String jobEducation = BaseUtil.convertNullToEmpty(map.get("jobEducation"));
						if(StringUtils.isNotEmpty(jobEducation)) {
							jobEducation = Education.valueOf(jobEducation).getName();
						}
						String jobSpecialty = BaseUtil.convertNullToEmpty(map.get("jobSpecialty"));
						String jobEduTime =  BaseUtil.convertNullToEmpty(map.get("jobEduTime"));

						String profession = BaseUtil.convertNullToEmpty(map.get("profession"));
						String professionYears = BaseUtil.convertNullToEmpty(map.get("professionYears"));
						String currentJobTime = BaseUtil.convertNullToEmpty(map.get("currentJobTime"));
						String professionName = BaseUtil.convertNullToEmpty(map.get("professionName"));
						String applyTechLevel = BaseUtil.convertNullToEmpty(map.get("applyTechLevel"));
						if(BaseUtil.isNotEmpty(applyTechLevel)) {
							applyTechLevel = TechLevel.valueOf(applyTechLevel).getName();
						}
						//论文相关
						String articleTital = BaseUtil.convertNullToEmpty(map.get("articleTital"));
						String isArticlePublish = BaseUtil.convertNullToEmpty(map.get("isArticlePublish"));
						String ddzz = "";
						String fbqk = "";
						String nd = "";
						String qh = "";
						if(Constants.YES.equals(isArticlePublish)) {
							ddzz = BaseUtil.convertNullToEmpty(map.get("ddzz"));
							fbqk = BaseUtil.convertNullToEmpty(map.get("fbqk"));
							nd = BaseUtil.convertNullToEmpty(map.get("nd"));
							qh = BaseUtil.convertNullToEmpty(map.get("qh"));
						}
						docStr = docStr.replace("XM", BaseUtil.toUnicode(name));
						docStr = docStr.replace("SFZH", BaseUtil.toUnicode(sfzh));
						docStr = docStr.replace("XB", BaseUtil.toUnicode(gender));
						docStr = docStr.replace("GZNX", BaseUtil.toUnicode(jobYears));
						docStr = docStr.replace("XCSZY", BaseUtil.toUnicode(currentJob));
						docStr = docStr.replace("QRZZGXL", BaseUtil.toUnicode(fullEducation));
						docStr = docStr.replace("QRZSXZY", BaseUtil.toUnicode(fullSpecialty));
						docStr = docStr.replace("QRZBYSJ", BaseUtil.toUnicode(fullEduTime));
						docStr = docStr.replace("ZZZGXL", BaseUtil.toUnicode(jobEducation));
						docStr = docStr.replace("ZZSXZY", BaseUtil.toUnicode(jobSpecialty));
						docStr = docStr.replace("ZZBYSJ", BaseUtil.toUnicode(jobEduTime));
						docStr = docStr.replace("ZYJSZW", BaseUtil.toUnicode(profession));
						docStr = docStr.replace("ZYNX", BaseUtil.toUnicode(professionYears));
						docStr = docStr.replace("CSZY", BaseUtil.toUnicode(professionName));
						docStr = docStr.replace("RXZSJ", BaseUtil.toUnicode(currentJobTime));
						docStr = docStr.replace("DW", BaseUtil.toUnicode(BaseUtil.convertNullToEmpty(map.get("company"))));
						docStr = docStr.replace("JZD", BaseUtil.toUnicode(BaseUtil.convertNullToEmpty(map.get("address"))));
						docStr = docStr.replace("YB", BaseUtil.toUnicode(BaseUtil.convertNullToEmpty(map.get("postCode"))));
						docStr = docStr.replace("SJH", BaseUtil.toUnicode(BaseUtil.convertNullToEmpty(map.get("mobile"))));
						docStr = docStr.replace("CSJB", BaseUtil.toUnicode(applyTechLevel));
						docStr = docStr.replace("CSFS", BaseUtil.toUnicode(BaseUtil.convertNullToEmpty(map.get("testWay"))));
						//论文相关
						docStr = docStr.replace("LWTM", BaseUtil.toUnicode(articleTital));
						if(StringUtils.isNotEmpty(ddzz)) {
							ddzz = ddzz.replace("ONE", "第一作者");
							ddzz = ddzz.replace("TWO", "第二作者");
							ddzz = ddzz.replace("THREE", "第三作者");
							ddzz = ddzz.replace("FOUR", "第四作者");
						}
						docStr = docStr.replace("DDZZ", BaseUtil.toUnicode(ddzz));
						docStr = docStr.replace("FBQK", BaseUtil.toUnicode(fbqk));
						docStr = docStr.replace("ND", BaseUtil.toUnicode(nd));
						docStr = docStr.replace("QH", BaseUtil.toUnicode(qh));
					}
					fos = new FileOutputStream(document, false);
					IOUtils.write(docStr, fos, "UTF-8");
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fosNew);
					BaseUtil.close(fos);
				}
			}

			{
				DocxReader.updateDocx(newDocx, needToUpdate);
			}
			//Word模板中插入图片，如果插入失败，不影响Word的正常导出
			try {
				//附件一
				String accessory1 = BaseUtil.convertNullToEmpty(map.get("studentPhoto"));
				Map<String, Object> datas = new HashMap<String, Object>();
				if(BaseUtil.isNotEmpty(accessory1)){
					RenderData renderData1 = new PictureRenderData(90, 130,
							accessory1.substring(accessory1.lastIndexOf(".")),
							BytePictureUtils.getUrlByteArray(accessory1));
					datas.put("ACCESSORY1", renderData1);
				}
				XWPFTemplate template = XWPFTemplate.compile(newDocx).render(datas);
				FileOutputStream out = new FileOutputStream(newDocx);
				template.write(out);
				out.flush();
				out.close();
				template.close();
			} catch (Exception e) {
				LOGGER.error("word模板插入图片失败，原因：" + e.getMessage(), e);
			}
			return newDocx;
		} catch (Exception e) {
			LOGGER.error("生成土地地质测量报名表失败，原因：" + e.getMessage(), e);
			throw BizException.withMessage(e.getMessage());
		} finally {
			DocxReader.release(templetDocxReader);
		}
	}

	/**
	 * 土地地质测量准考证(考试通知单)下载
	 */
	public File creatTddzlclZkz(Map<String, Object> map){
		File newDocx = null;
		DocxReader templetDocxReader = null;
		Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
		
		String examTime = BaseUtil.convertNullToEmpty(map.get("examTime"));
		String examRoom = BaseUtil.convertNullToEmpty(map.get("examRoom"));
		String seatNo = BaseUtil.convertNullToEmpty(map.get("seatNo"));
		try {
			{// 拷贝一份模板文件到临时文件夹里面
				FileOutputStream fos = null;
				InputStream is = null;
				try {
					newDocx = FileHelper.createTmpFile();
					newDocx.createNewFile();
					fos = new FileOutputStream(newDocx);
					String dir = "/docxTmps/tddzcl-kaoshi-zkz.docx";
					// 考试时间、考场、座位号都为空则采用模版2
					if (BaseUtil.isEmpty(examTime) && BaseUtil.isEmpty(examRoom) && BaseUtil.isEmpty(seatNo)) {
						dir = "/docxTmps/tddzcl-kaoshi-zkz2.docx";
					}
					is = this.getClass().getResourceAsStream(dir);
					IOUtils.copyLarge(is, fos);
					fos.flush();
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fos);
				}
			}
			templetDocxReader = new DocxReader(newDocx);
			templetDocxReader.doRead();
			{
				needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(), templetDocxReader.getDocumentInfo());
				File document = templetDocxReader.getDocumentInfo().getFile();
				FileOutputStream fosNew = null;
				FileOutputStream fos = null;
				InputStream is = null;
				try {
					is = new FileInputStream(document);
					String docStr = IOUtils.toString(is, "UTF-8");
					if(BaseUtil.isNotEmpty(map)) {
						String studentName =  BaseUtil.convertNullToEmpty(map.get("studentName"));
						String sfzh =  BaseUtil.convertNullToEmpty(map.get("sfzh"));
						String gender =  BaseUtil.convertNullToEmpty(map.get("gender"));
						gender = Gender.M.name().equals(gender) ? "男" : "女";
						String professionName = BaseUtil.convertNullToEmpty(map.get("professionName"));
						String applyTechLevel = BaseUtil.convertNullToEmpty(map.get("applyTechLevel"));
						switch (applyTechLevel){
							case "ONE":applyTechLevel="一级";break;
							case "TWO":applyTechLevel="二级";break;
							case "THREE":applyTechLevel="三级";break;
							case "FOUR":applyTechLevel="四级";break;
							case "FIVE":applyTechLevel="五级";break;
							case "ZHONG":applyTechLevel="中级";break;
							case "GAO":applyTechLevel="高级";break;
							case "FGAO":applyTechLevel="副高级";break;
							case "ZGAO":applyTechLevel="正高级";break;
							default:break;
						}
						String zkz=BaseUtil.convertNullToEmpty(map.get("zkz"));
						String zyzw=BaseUtil.convertNullToEmpty(map.get("profession"));
						String examPoint = BaseUtil.convertNullToEmpty(map.get("examPoint"));
						String busLine = BaseUtil.convertNullToEmpty(map.get("busLine"));
						if(BaseUtil.isEmpty(examPoint)) {
							examPoint = "武汉市武汉经济技术开发区（汉南区）纱帽街育才路399号";
						}
						if(BaseUtil.isEmpty(busLine)) {
							busLine = "各大站点换乘武汉轨道交通 16 号线湾湖站 B 出口";
						}
						docStr = docStr.replace("KSSJ", BaseUtil.toUnicode(examTime));
						docStr = docStr.replace("KSDD", BaseUtil.toUnicode(examPoint));
						docStr = docStr.replace("CCLX", BaseUtil.toUnicode(busLine));
						docStr = docStr.replace("XM", BaseUtil.toUnicode(studentName));
						docStr = docStr.replace("XB", BaseUtil.toUnicode(gender));
						docStr = docStr.replace("SFZH", BaseUtil.toUnicode(sfzh));
						docStr = docStr.replace("ZYZW", BaseUtil.toUnicode(zyzw));
						docStr = docStr.replace("CSZY", BaseUtil.toUnicode(professionName));
						docStr = docStr.replace("CSJB", BaseUtil.toUnicode(applyTechLevel));
						docStr = docStr.replace("KH", BaseUtil.toUnicode(zkz));
						docStr = docStr.replace("KCH", BaseUtil.toUnicode(examRoom));
						docStr = docStr.replace("ZWH", BaseUtil.toUnicode(seatNo));
					}
					fos = new FileOutputStream(document, false);
					IOUtils.write(docStr, fos, "UTF-8");
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fosNew);
					BaseUtil.close(fos);
				}
			}

			{
				DocxReader.updateDocx(newDocx, needToUpdate);
			}
			//Word模板中插入图片，如果插入失败，不影响Word的正常导出
			try {
				//附件一
				String accessory1 = BaseUtil.convertNullToEmpty(map.get("studentPhoto"));
				Map<String, Object> datas = new HashMap<String, Object>();
				if(BaseUtil.isNotEmpty(accessory1)){
					RenderData renderData1 = new PictureRenderData(90, 130,
							accessory1.substring(accessory1.lastIndexOf(".")),
							BytePictureUtils.getUrlByteArray(accessory1));
					datas.put("ACCESSORY1", renderData1);
				}
				XWPFTemplate template = XWPFTemplate.compile(newDocx).render(datas);
				FileOutputStream out = new FileOutputStream(newDocx);
				template.write(out);
				out.flush();
				out.close();
				template.close();
			} catch (Exception e) {
				LOGGER.error("word模板插入图片失败，原因：" + e.getMessage(), e);
			}
			return newDocx;
		} catch (Exception e) {
			LOGGER.error("生成土地地质测量准考证失败，原因：" + e.getMessage(), e);
			throw BizException.withMessage(e.getMessage());
		} finally {
			DocxReader.release(templetDocxReader);
		}
	}

	/**
	 * 获取批次下开放报名的行业
	 */
	public List<ZyjdIndustry> getIndustryByBmbatchId(String bmbatchId){
		return mapper.getIndustryByBmbatchId(bmbatchId);
	}

	/**
	 * 获取批次下开放报名的职业
	 */
	public List<ZyjdProfession> getOpenedProfession(String bmbatchId, String industryId){
		return mapper.getOpenedProfession(bmbatchId, industryId);
	}

	/**
	 * 获取批次下开放报名的职业
	 */
	public List<Map<String, Object>> getOpenedProfession(String bmbatchId, List<String> industryIds){
		return mapper.getOpenedProfessionIn(bmbatchId, industryIds);
	}

	/**
	 * 获取报考条件
	 */
	public List<ZyjdProfessionCondition> getCondition(String professionId, TechLevel techLevel){
		EntityWrapper<ZyjdProfessionCondition> wrapper = new EntityWrapper();
		wrapper.eq("profession_id", professionId);
		wrapper.eq("tech_level", techLevel);
		wrapper.eq("status", Constants.YES);
		wrapper.orderBy("condition_seq", true);
		return conditionMapper.selectList(wrapper);
	}

	/**
	 * 获取职业的职业方向
	 */
	public List<ZyjdProfessionDirection> getDirectionByProfessionId(String professionId){
		EntityWrapper<ZyjdProfessionDirectionRelation> relationWrapper = new EntityWrapper();
		relationWrapper.eq("profession_id", professionId);
		List<ZyjdProfessionDirectionRelation> relations = relationMapper.selectList(relationWrapper);
		if (relations.size() > 0) {
			List<String> directionIds = relations.stream().map(x->x.getDirectionId()).collect(Collectors.toList());
			List<ZyjdProfessionDirection> directions = directionMapper.selectBatchIds(directionIds);
			return directions;
		}
		return Collections.EMPTY_LIST;
	}
	
}
