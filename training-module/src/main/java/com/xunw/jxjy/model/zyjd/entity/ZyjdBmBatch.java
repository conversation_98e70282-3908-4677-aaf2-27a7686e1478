package com.xunw.jxjy.model.zyjd.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.AuditMode;
import com.xunw.jxjy.model.enums.BmOpenStatus;
import com.xunw.jxjy.model.enums.ZyjdApproveStatus;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;

/**
 * 职业鉴定批次表
 * 
 * <AUTHOR>
 */
@TableName("biz_zyjd_bmbatch")
public class ZyjdBmBatch implements Serializable {

	private static final long serialVersionUID = 7765538560450763039L;

	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;
	
	// 年度
	@TableField("years")
	private String years;

	@TableField("name")
	private String name;

	@TableField("bm_start_time")
	private Date bmStartTime;

	@TableField("bm_end_time")
	private Date bmEndTime;

	// 类型
	@TableField("type")
	private ZyjdBmBatchType type;

	// 准考证下载开始时间
	@TableField("zkz_start_time")
	private Date zkzStartTime;

	// 准考证下载结束时间
	@TableField("zkz_end_time")
	private Date zkzEndTime;

	// 主办单位ID
	@TableField("host_org_id")
	private String hostOrgId;

	// 采集表类型
	@TableField("form_id")
	private String formId;

	// 收款方ID
	@TableField("bank_id")
	private String bankId;

	// 批次审核状态
	@TableField("approve_status")
	private ZyjdApproveStatus approveStatus;

	// 审核用户id
	@TableField("approve_user_id")
	private String approveUserId;
	
	// 审核意见
	@TableField("approve_advice")
	private String approveAdvice;

	// 审核时间
	@TableField("approve_time")
	private Date approveTime;

	// 申报用户ID,管理员直接创建的批次(没走技能申报的流程)该值为空
	@TableField("user_id")
	private String userId;
	
	// 报名开放状态
	@TableField("status")
	private BmOpenStatus status;
	
	//当前批次关闭时，是否智能匹配同类型下的其他开放的批次
	@TableField("is_auto_next_batch")
	private String isAutoNextBatch;
	
	// 创建用户id
	@TableField("creator_id")
	private String creatorId;

	// 创建时间
	@TableField("create_time")
	private Date createTime;

	// 修改用户id
	@TableField("updator_id")
	private String updatorId;

	// 修改时间
	@TableField("update_time")
	private Date updateTime;
	
	// 是否允许同考生一个批次报名多个工种，默认不允许
	@TableField("is_allow_multi_profession")
	private String isAllowMultiProfession;
	
	//培训费 报名费是否分开缴纳  默认否
	@TableField("is_fee_separate")
	private String isFeeSeparate;

	//---答辩评审
	//答辩开始时间
	@TableField("reply_start_time")
	private Date replyStartTime;

	//答辩结束时间
	@TableField("reply_end_time")
	private Date replyEndTime;

	//评审开始时间
	@TableField("review_start_time")
	private Date reviewStartTime;

	//评审结束时间
	@TableField("review_end_time")
	private Date reviewEndTime;

	//主任
	@TableField("director")
	private String director;

	//督导员
	@TableField("supervisor")
	private String supervisor;


	//是否开启报名信息初审 1是 0否
	@TableField("is_one_trial")
	private String isOneTrial;

	// 缴费截止时间
	@TableField("jf_end_time")
	private Date jfEndTime;

	@TableField("logo")
	private String logo;

	@TableField("audit_mode")
	private AuditMode auditMode;

	public BmOpenStatus getStatus() {
		return status;
	}

	public void setStatus(BmOpenStatus status) {
		this.status = status;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getBmStartTime() {
		return bmStartTime;
	}

	public void setBmStartTime(Date bmStartTime) {
		this.bmStartTime = bmStartTime;
	}

	public Date getBmEndTime() {
		return bmEndTime;
	}

	public void setBmEndTime(Date bmEndTime) {
		this.bmEndTime = bmEndTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public ZyjdBmBatchType getType() {
		return type;
	}

	public void setType(ZyjdBmBatchType type) {
		this.type = type;
	}

	public Date getZkzStartTime() {
		return zkzStartTime;
	}

	public void setZkzStartTime(Date zkzStartTime) {
		this.zkzStartTime = zkzStartTime;
	}

	public Date getZkzEndTime() {
		return zkzEndTime;
	}

	public void setZkzEndTime(Date zkzEndTime) {
		this.zkzEndTime = zkzEndTime;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getBankId() {
		return bankId;
	}

	public void setBankId(String bankId) {
		this.bankId = bankId;
	}

	public ZyjdApproveStatus getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(ZyjdApproveStatus approveStatus) {
		this.approveStatus = approveStatus;
	}

	public String getApproveUserId() {
		return approveUserId;
	}

	public void setApproveUserId(String approveUserId) {
		this.approveUserId = approveUserId;
	}

	public Date getApproveTime() {
		return approveTime;
	}

	public void setApproveTime(Date approveTime) {
		this.approveTime = approveTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getApproveAdvice() {
		return approveAdvice;
	}

	public void setApproveAdvice(String approveAdvice) {
		this.approveAdvice = approveAdvice;
	}

	public String getYears() {
		return years;
	}

	public void setYears(String years) {
		this.years = years;
	}

	public String getIsAutoNextBatch() {
		return isAutoNextBatch;
	}

	public void setIsAutoNextBatch(String isAutoNextBatch) {
		this.isAutoNextBatch = isAutoNextBatch;
	}

	public String getIsAllowMultiProfession() {
		return isAllowMultiProfession;
	}

	public void setIsAllowMultiProfession(String isAllowMultiProfession) {
		this.isAllowMultiProfession = isAllowMultiProfession;
	}

	public String getIsFeeSeparate() {
		return isFeeSeparate;
	}

	public void setIsFeeSeparate(String isFeeSeparate) {
		this.isFeeSeparate = isFeeSeparate;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public Date getReplyStartTime() {
		return replyStartTime;
	}

	public void setReplyStartTime(Date replyStartTime) {
		this.replyStartTime = replyStartTime;
	}

	public Date getReplyEndTime() {
		return replyEndTime;
	}

	public void setReplyEndTime(Date replyEndTime) {
		this.replyEndTime = replyEndTime;
	}

	public Date getReviewStartTime() {
		return reviewStartTime;
	}

	public void setReviewStartTime(Date reviewStartTime) {
		this.reviewStartTime = reviewStartTime;
	}

	public Date getReviewEndTime() {
		return reviewEndTime;
	}

	public void setReviewEndTime(Date reviewEndTime) {
		this.reviewEndTime = reviewEndTime;
	}

	public String getDirector() {
		return director;
	}

	public void setDirector(String director) {
		this.director = director;
	}

	public String getSupervisor() {
		return supervisor;
	}

	public void setSupervisor(String supervisor) {
		this.supervisor = supervisor;
	}

	public String getIsOneTrial() {
		return isOneTrial;
	}

	public void setIsOneTrial(String isOneTrial) {
		this.isOneTrial = isOneTrial;
	}

	public Date getJfEndTime() {
		return jfEndTime;
	}

	public void setJfEndTime(Date jfEndTime) {
		this.jfEndTime = jfEndTime;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public AuditMode getAuditMode() {
		return auditMode;
	}

	public void setAuditMode(AuditMode auditMode) {
		this.auditMode = auditMode;
	}
}
