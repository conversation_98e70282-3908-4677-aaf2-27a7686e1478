package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021年04月13日
 * 评分指标
 */
@TableName("BIZ_TARGET_RESULT")
public class TargetResult implements Serializable {

	private static final long serialVersionUID = -5249706086244048948L;
    //ID
	@TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

	@TableField("course_setting_id")
    private String courseSettingId;

	//指标设置ID
	@TableField("setting_id")
	private String settingId;

	//星分
	@TableField("star_count")
	private Integer starCount;

	//备注
	@TableField("remark")
	private String remark;

	//创建时间
	@TableField("time")
	private Date time;

	//创建学生id
	@TableField("student_id")
	private String studentId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSettingId() {
		return settingId;
	}

	public void setSettingId(String settingId) {
		this.settingId = settingId;
	}

	public Integer getStarCount() {
		return starCount;
	}

	public void setStarCount(Integer starCount) {
		this.starCount = starCount;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCourseSettingId() {
		return courseSettingId;
	}

	public void setCourseSettingId(String courseSettingId) {
		this.courseSettingId = courseSettingId;
	}
}

    