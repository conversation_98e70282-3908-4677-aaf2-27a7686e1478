package com.xunw.jxjy.model.zypx.service;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.common.coursems.Chapter;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.common.coursems.Lesson;
import com.xunw.jxjy.model.inf.entity.ProfessionAdCourse;
import com.xunw.jxjy.model.inf.entity.Venue;
import com.xunw.jxjy.model.inf.entity.VenueRoom;
import com.xunw.jxjy.model.inf.mapper.ProfessionAdCourseMapper;
import com.xunw.jxjy.model.inf.mapper.VenueMapper;
import com.xunw.jxjy.model.inf.mapper.VenueRoomMapper;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmCourseSettingMapper;
import com.xunw.jxjy.paper.utils.ModelHelper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;

/**
 * 面授教学计划服务
 */
@Service
public class MsTeachPlanService {
	
	@Autowired
	private ZypxXmCourseSettingMapper courseSettingMapper;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private VenueMapper venueMapper;
	@Autowired
	private VenueRoomMapper venueRoomMapper;
	@Autowired
	private ProfessionAdCourseMapper professionAdCourseMapper;
	
	/**
	 * 信息填充
	 */
	public void fillInfo(CourseMs courseMs) {
		if (courseMs!=null &&  courseMs.getChapters()!=null) {
			for (Chapter chapter : courseMs.getChapters()) {
				if (chapter.getLessons()!=null) {
					for (Lesson lesson : chapter.getLessons()) {
						if (StringUtils.isNotEmpty(lesson.getTeacherId())) {
							User teacher = userMapper.selectById(lesson.getTeacherId());
							lesson.setTeacherName(teacher != null ? teacher.getName() : null);
							lesson.setTeacherPhone(teacher != null ? teacher.getMobile() : null);
						}
						if (StringUtils.isNotEmpty(lesson.getVenueId())) {
							Venue venue = venueMapper.selectById(lesson.getVenueId());
							lesson.setVenueName(venue != null ? venue.getName() : null);
							lesson.setMapPosition(venue != null ? venue.getMapPosition() : null);//培训场地经纬度坐标   用于面授签到
						}
						if (StringUtils.isNotEmpty(lesson.getVenueRoomId())) {
							VenueRoom venueRoom = venueRoomMapper.selectById(lesson.getVenueRoomId());
							lesson.setVenueRoomName(venueRoom != null ? venueRoom.getName() : null);
						}
					}
				}
			}
		}
	}
	
	@Transactional(rollbackFor = Throwable.class)
	public void saveMsTeachPlan(ZypxXmCourseSetting courseSetting, JSONObject msContent, String userId)
			throws ParseException {
		String id = BaseUtil.getStringValueFromJson(msContent, "id");
		id = StringUtils.isEmpty(id) ? BaseUtil.generateId2() : id;
		if (!msContent.containsKey("chapters")) {
			throw BizException.withMessage("保存面授教学计划失败，章节信息不能为空");
		}
		JSONArray chaperArray = msContent.getJSONArray("chapters");
		if (chaperArray == null || chaperArray.size() == 0) {
			throw BizException.withMessage("保存面授教学计划失败，章节信息不能为空");
		}
		CourseMs courseMs = new CourseMs();
		courseMs.setId(id);
		courseMs.setCourseId(courseSetting.getCourseId());
		for (int i = 0; i < chaperArray.size(); i++) {
			JSONObject chapterObj = chaperArray.getJSONObject(i);
			String chapterId = BaseUtil.getStringValueFromJson(chapterObj, "id");
			chapterId = StringUtils.isEmpty(chapterId) ? BaseUtil.generateId2() : chapterId;
			String chapterName = BaseUtil.getStringValueFromJson(chapterObj, "name");
			Chapter chapter = new Chapter();
			chapter.setId(chapterId);
			chapter.setName(chapterName);
			courseMs.addChapter(chapter);
			// 保存课时
			if (!chapterObj.containsKey("lessons")) {
				throw BizException.withMessage("保存面授教学计划失败，因为第" + (i + 1) + "章课时信息为空");
			}
			JSONArray lessonArray = chapterObj.getJSONArray("lessons");
			if (lessonArray == null || lessonArray.size() == 0) {
				throw BizException.withMessage("保存面授教学计划失败，因为第" + (i + 1) + "章课时信息为空");
			} else {
				for (int j = 0; j < lessonArray.size(); j++) {
					JSONObject lessonObj = lessonArray.getJSONObject(j);
					String lessonId = BaseUtil.getStringValueFromJson(lessonObj, "id");
					lessonId = StringUtils.isEmpty(lessonId) ? BaseUtil.generateId2() : lessonId;
					String lessonName = BaseUtil.getStringValueFromJson(lessonObj, "name");
					String startTimeStrValue = BaseUtil.getStringValueFromJson(lessonObj, "startTime");
					Date startTime = DateUtils.parse(startTimeStrValue, "yyyy-MM-dd HH:mm:ss");
					String endTimeStrValue = BaseUtil.getStringValueFromJson(lessonObj, "endTime");
					Date endTime = DateUtils.parse(endTimeStrValue, "yyyy-MM-dd HH:mm:ss");
					if (startTime.after(endTime)) {
						throw BizException.withMessage("第" + (i + 1) + "章，第" + (j + 1) + "节的开始时间不能够晚于结束时间");
					}
					String teacherId = BaseUtil.getStringValueFromJson(lessonObj, "teacherId");
					String teacherName = BaseUtil.getStringValueFromJson(lessonObj, "teacherName");
					String teacherPhone = BaseUtil.getStringValueFromJson(lessonObj, "teacherPhone");
//					if (StringUtils.isEmpty(teacherId) || StringUtils.isEmpty(teacherName)) {
//						throw BizException.withMessage("第" + (i + 1) + "章，第" + (j + 1) + "节的讲师名称不能为空");
//					}
					if (StringUtils.isEmpty(teacherName)) {
						throw BizException.withMessage("第" + (i + 1) + "章，第" + (j + 1) + "节的讲师名称不能为空");
					}
					String address = BaseUtil.getStringValueFromJson(lessonObj, "address");
					if (StringUtils.isEmpty(address)) {
						throw BizException.withMessage("第" + (i + 1) + "章，第" + (j + 1) + "节的培训场地不能为空");
					}
					String remark = BaseUtil.getStringValueFromJson(lessonObj, "remark");
					Lesson lesson = new Lesson();
					lesson.setId(lessonId);
					lesson.setName(lessonName);
					lesson.setStartTime(startTime);
					lesson.setEndTime(endTime);
					lesson.setTeacherId(teacherId);
					lesson.setTeacherName(teacherName);
					lesson.setTeacherPhone(teacherPhone);
					lesson.setRemark(remark);
					lesson.setAddress(address);
					String venueRoomId = BaseUtil.getStringValueFromJson(lessonObj, "venueRoomId");
					if(StringUtils.isNotEmpty(venueRoomId)) {
						VenueRoom venueRoom = venueRoomMapper.selectById(venueRoomId);
						Venue venue = venueMapper.selectById(venueRoom.getVenueId());
						lesson.setVenueId(venue.getId());
						lesson.setVenueName(venue.getName());
						lesson.setVenueRoomId(venueRoomId);
						lesson.setVenueRoomName(venueRoom.getName());
					}
					chapter.addLesson(lesson);
				}
			}
		}
		String xml = ModelHelper.formatObject(courseMs);
		courseSetting.setMsContent(xml);
		courseSetting.setUpdatorId(userId);
		courseSetting.setUpdateTime(new Date());
		Integer result = courseSettingMapper.updateById(courseSetting);
		if (result == 0) {
			// 如果修改失败，则为基础数据得课程
			ProfessionAdCourse professionAdCourse = new ProfessionAdCourse();
			BeanUtils.copyProperties(courseSetting, professionAdCourse);
			professionAdCourseMapper.updateById(professionAdCourse);
		}
	}
}


