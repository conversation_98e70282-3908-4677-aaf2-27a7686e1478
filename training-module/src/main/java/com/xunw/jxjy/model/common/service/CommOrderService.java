package com.xunw.jxjy.model.common.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.icbc.api.DefaultIcbcClient;
import com.icbc.api.IcbcConstants;
import com.icbc.api.request.CardbusinessQrcodeConsumptionRequestV1;
import com.icbc.api.response.CardbusinessQrcodeConsumptionResponseV1;
import com.icbc.api.utils.IcbcSignature;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.pay.WeiXinPayUtils;
import com.xunw.jxjy.common.pay.WeiXinPrePay;
import com.xunw.jxjy.common.pay.WeiXinTradeTypeEnum;
import com.xunw.jxjy.common.pay.WeixinTradeStateEnum;
import com.xunw.jxjy.common.utils.*;
import com.xunw.jxjy.model.common.entity.CommOrder;
import com.xunw.jxjy.model.common.entity.CommOrderDetail;
import com.xunw.jxjy.model.common.mapper.CommOrderDetailMapper;
import com.xunw.jxjy.model.common.mapper.CommOrderMapper;
import com.xunw.jxjy.model.common.params.CommOrderQueryParams;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Bank;
import com.xunw.jxjy.model.sys.entity.BankConfig;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.BankConfigMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.service.BankConfigService;
import com.xunw.jxjy.model.sys.service.BankService;
import com.xunw.jxjy.model.utils.RSASig;
import com.xunw.jxjy.model.zypx.entity.PlanDetail;
import com.xunw.jxjy.model.zypx.entity.StudentBmCourse;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxBmCourse;
import com.xunw.jxjy.model.zypx.mapper.*;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务
 * <AUTHOR>
 */
@Service
public class CommOrderService extends BaseCRUDService<CommOrderMapper, CommOrder>{

	private static final String ALI_GATE_WAY = "https://openapi.alipay.com/gateway.do";

	private static final Logger LOGGER = LoggerFactory.getLogger(CommOrderService.class);

	@Autowired
	private CommOrderDetailMapper commOrderDetailMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private BankConfigMapper bankConfigMapper;
	@Autowired
	private CommOrderStatusQueryService commOrderStatusQueryService;
	@Autowired
	private BankService bankService;
	@Autowired
	private BankConfigService bankConfigService;
	@Autowired
	private StudentInfoService studentInfoService;
    @Autowired
    private CourseMapper courseMapper;
    @Autowired
    private StudentBmCourseMapper studentBmCourseMapper;
    @Autowired
    private ZypxBmMapper zypxBmMapper;
    @Autowired
    private PlanDetailMapper planDetailMapper;
    @Autowired
    private ZypxXmCourseSettingMapper zypxXmCourseSettingMapper;
    @Autowired
    private ZypxBmCourseMapper zypxBmCourseMapper;

	/**
	 * 根据业务ID 查询某笔业务的所有订单
	 */

	public List<Map<String, Object>> getOrderByYwId(FeeType feeType, String ywId) {
		List<Map<String, Object>> list = mapper.getOrderByYwId(feeType.name(), ywId);
		return list;
	}
	
	@Transactional
	public CommOrder createOrderByAdmin(FeeCategory category, String userId, String hostOrgId, FeeType[] feeTypes,
			String[] ywIds, Double[] moneys, String product, String remark) {
		return createOrderByAdmin(category, userId, hostOrgId, feeTypes,
				ywIds, moneys, product, remark, null);
	}

	/**
	 * 集体缴费通用下单接口
	 * 
	 * @param category  费用类型大类 必填
	 * @param userId    管理员用户ID 必填
	 * @param hostOrgId 主办单位ID 必填 当前订单数据是属于哪一个主办单位
	 * @param feeTypes  费用类型小类 必填,每一个子项目的具体费用类型
	 * @param ywIds     业务ID 必填,与费用类型一一对应
	 * @param moneys    费用金额 必填,业务ID一一对应
	 * @param product   订单描述信息 必填，不超过128字符
	 * @param remark    备注信息
	 * @param bankId    收款方ID 不指定则使用主办单位的默认收款方
	 */

	@Transactional
	public CommOrder createOrderByAdmin(FeeCategory category, String userId, String hostOrgId, FeeType[] feeTypes,
			String[] ywIds, Double[] moneys, String product, String remark, String bankId) {
		// 参数校验
		if (category == null) {
			throw BizException.withMessage("请传入费用类型大类");
		}
		if (StringUtils.isEmpty(userId)) {
			throw BizException.withMessage("请传入用户ID");
		}
		if (feeTypes == null || feeTypes.length == 0) {
			throw BizException.withMessage("请传入费用类型");
		}
		if (ywIds == null || ywIds.length == 0) {
			throw BizException.withMessage("请传入业务ID");
		}
		if (moneys == null || moneys.length == 0) {
			throw BizException.withMessage("请传入费用金额");
		}
		if (feeTypes.length != ywIds.length) {
			throw BizException.withMessage("业务ID参数与费用类型参数没有一一对应");
		}
		if (moneys.length != ywIds.length) {
			throw BizException.withMessage("业务ID参数与费用金额参数没有一一对应");
		}
		// 计算订单金额
		Double amount = null;
		BigDecimal total = new BigDecimal(0);
		for (Double money : moneys) {
			BigDecimal b = new BigDecimal(String.valueOf(money));
			total = total.add(b);
		}
		total = total.setScale(2, BigDecimal.ROUND_HALF_UP);
		amount = total.doubleValue();
		if (amount == null || amount <= 0) {
			throw BizException.withMessage("请传入正确的订单金额");
		}
		if (StringUtils.isEmpty(product)) {
			throw BizException.withMessage("请传入订单描述信息");
		}
		if (product.length() > 128) {
			throw BizException.withMessage("订单描述信息不能够超过128字符");
		}
		User user = userMapper.selectById(userId);
		String payName = user.getName() != null ? user.getName() : "";
		// 创建订单
		String id = BaseUtil.generateId2().substring(0,9) + "A" + System.currentTimeMillis() + BaseUtil.generateRandomString(2);
		id = id.toUpperCase();
		CommOrder commOrder = new CommOrder();
		commOrder.setId(id);
		commOrder.setCategory(category);
		commOrder.setCreateTime(new Date());
		commOrder.setUserType(UserType.ADMIN);
		commOrder.setPayUserId(userId);
		commOrder.setPayOrgId(user.getOrgId());
		commOrder.setHostOrgId(hostOrgId);
		commOrder.setPayName(payName);
		commOrder.setAmount(amount);
		commOrder.setProduct(product);
		commOrder.setStatus(OrderStatus.WZF);
		remark = StringUtils.isNotEmpty(remark) ? remark : product;
		commOrder.setRemark(remark);
		bankId = StringUtils.isNotEmpty(bankId) ? bankId : bankService.getDefaultBank(hostOrgId).getId();
		commOrder.setBankId(bankId);
		// 保存订单
		this.insert(commOrder);
		// 保存订单详情
		List<CommOrderDetail> commOrderDetails = new ArrayList<>();
		for (int j = 0; j < feeTypes.length; j++) {
			FeeType feeType = feeTypes[j];
			String ywId = ywIds[j];
			CommOrderDetail commOrderDetail = new CommOrderDetail();
			commOrderDetail.setId(BaseUtil.generateId2());
			commOrderDetail.setOrderId(commOrder.getId());
			commOrderDetail.setFeeType(feeType);
			commOrderDetail.setYwId(ywId);
			commOrderDetail.setAmount(moneys[j]);
			commOrderDetails.add(commOrderDetail);
		}
		DBUtils.insertBatch(commOrderDetails, 10, CommOrderDetail.class);
		return commOrder;
	}

	/**
	 * 返回字段信息： 考生姓名、身份证号、费用类型、金额
	 */
	public List<CommOrderDetail> getOrderDetailsByOrderId(String orderNo) {
		EntityWrapper<CommOrderDetail> wrapper = new EntityWrapper();
		wrapper.eq("order_id", orderNo);
		List<CommOrderDetail> commOrderDetails = commOrderDetailMapper.selectList(wrapper);
		return commOrderDetails;
	}

	public Page<Map<String, Object>> pageQuery(CommOrderQueryParams params) {
		List<Map<String, Object>> list = this.mapper.list(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}
	
	@Transactional
	public Map<String, Object> createOrderByStudentWithPayment(FeeCategory category, String studentId, String hostOrgId,
			FeeType[] feeTypes, String[] ywIds, Double[] moneys, String product, PayMethod payMethod, String remark,String wxAuthCode){
		return createOrderByStudentWithPayment(category, studentId, hostOrgId, feeTypes, ywIds, moneys, product, 
				payMethod, remark, wxAuthCode, null);
	}

	/**
	 * 学员用户通用下单、并生成支付信息方法
	 * 
	 * @param feeCategory 费用类型大类 必填
	 * @param studentId   考生ID 必填
	 * @param hostOrgId   主办单位ID 必填，该笔订单是属于哪一个主办单位
	 * @param feeTypes    费用类型小类 必填,每一个子项目的具体费用类型
	 * @param ywIds       业务ID 必填,与费用类型一一对应
	 * @param moneys      费用金额 必填,业务ID一一对应
	 * @param product     商品描述信息 必填，不超过100字符
	 * @param payMethod   支付方式 必填
	 * @param remark      备注信息 非必填 若为空 则取product
	 * @param wxAuthCode  微信OAuth2.0临时Code,微信公众号支付必填
	 * @param bankId    收款方ID 不指定则使用主办单位的默认收款方
	 */
	@Transactional
	public Map<String, Object> createOrderByStudentWithPayment(FeeCategory category, String studentId, String hostOrgId,
			FeeType[] feeTypes, String[] ywIds, Double[] moneys, String product, PayMethod payMethod, String remark,String wxAuthCode,String bankId) {
		CommOrder order = createOrderByStudent(category, studentId, hostOrgId, feeTypes, ywIds, moneys, product, remark, bankId);
		String payment = goPay(order.getId(), payMethod, wxAuthCode, null);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("payment", payment);
		result.put("order", order);
		return result;
	}
	
	@Transactional
	public CommOrder createOrderByStudent(FeeCategory category, String studentId, String hostOrgId, FeeType[] feeTypes,
			String[] ywIds, Double[] moneys, String product, String remark) {
		return createOrderByStudent(category, studentId, hostOrgId, feeTypes, ywIds, moneys, product, remark, null);
	}

	/**
	 * 学员用户通用下单方法
	 * 
	 * @param feeCategory 费用类型大类 必填
	 * @param studentId   考生ID 必填
	 * @param hostOrgId   主办单位ID 必填，该笔订单是属于哪一个主办单位
	 * @param feeTypes    费用类型小类 必填,每一个子项目的具体费用类型
	 * @param ywIds       业务ID 必填,与费用类型一一对应
	 * @param moneys      费用金额 必填,业务ID一一对应
	 * @param product     商品描述信息 必填，不超过100字符
	 * @param remark      备注信息 非必填 若为空 则取product
	 * @param bankId      收款方ID 不指定则使用主办单位的默认收款方
	 */
	@Transactional
	public CommOrder createOrderByStudent(FeeCategory category, String studentId, String hostOrgId, FeeType[] feeTypes,
			String[] ywIds, Double[] moneys, String product, String remark,String bankId) {
		// 参数校验
		if (category == null) {
			throw BizException.withMessage("请传入费用类型大类");
		}
		if (StringUtils.isEmpty(studentId)) {
			throw BizException.withMessage("请传入考生ID");
		}
		if (moneys == null || moneys.length == 0) {
			throw BizException.withMessage("请传入费用类型");
		}
		if (ywIds == null || ywIds.length == 0) {
			throw BizException.withMessage("请传入业务ID");
		}
		if (moneys == null || moneys.length == 0) {
			throw BizException.withMessage("请传入费用金额");
		}
		if (moneys.length != ywIds.length) {
			throw BizException.withMessage("业务ID参数与费用金额参数没有一一对应");
		}
		// 计算订单金额
		Double amount = null;
		BigDecimal total = new BigDecimal(0);
		for (Double money : moneys) {
			BigDecimal b = new BigDecimal(String.valueOf(money));
			total = total.add(b);
		}
		total = total.setScale(2, BigDecimal.ROUND_HALF_UP);
		amount = total.doubleValue();
		if (amount == null || amount <= 0) {
			throw BizException.withMessage("请传入正确的订单金额");
		}
		if (StringUtils.isEmpty(product)) {
			throw BizException.withMessage("请传入订单描述信息");
		}
		if (product.length() > 128) {
			throw BizException.withMessage("订单描述信息不能够超过128个字符");
		}
		StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentId);
		if (studentInfo == null) {
			throw BizException.withMessage("考生不存在");
		}
		// 创建订单
		String id = BaseUtil.generateId2().substring(0,9) + "S" + System.currentTimeMillis() + BaseUtil.generateRandomString(2);
		id = id.toUpperCase();
		CommOrder commOrder = new CommOrder();
		commOrder.setId(id);
		commOrder.setCategory(category);
		commOrder.setCreateTime(new Date());
		commOrder.setUserType(UserType.STUDENT);
		commOrder.setPayUserId(studentId);
		commOrder.setHostOrgId(hostOrgId);
		commOrder.setPayName(studentInfo.getName());
	
		commOrder.setAmount(amount);
		commOrder.setProduct(product);
		commOrder.setStatus(OrderStatus.WZF);
		remark = StringUtils.isNotEmpty(remark) ? remark : product;
		commOrder.setRemark(remark);
		bankId = StringUtils.isNotEmpty(bankId) ? bankId : bankService.getDefaultBank(hostOrgId).getId();
		commOrder.setBankId(bankId);
		// 保存订单
		this.insert(commOrder);
		// 保存订单详情
		List<CommOrderDetail> commOrderDetails = new ArrayList<>();
		for (int j = 0; j < feeTypes.length; j++) {
			FeeType fylx = feeTypes[0];
			String ywId = ywIds[j];
			CommOrderDetail commOrderDetail = new CommOrderDetail();
			commOrderDetail.setId(BaseUtil.generateId2());
			commOrderDetail.setOrderId(commOrder.getId());
			commOrderDetail.setFeeType(fylx);
			commOrderDetail.setYwId(ywId);
			commOrderDetail.setAmount(moneys[j]);
			commOrderDetails.add(commOrderDetail);
		}
		DBUtils.insertBatch(commOrderDetails, 10, CommOrderDetail.class);
		return commOrder;
	}

	@Transactional
	public String goPay(String id, PayMethod payMethod, String wxAuthCode, String openId) {
		CommOrder commOrder = this.selectById(id);
		if (isOrderExpired(commOrder)) {
			throw BizException.withMessage("订单已经超过有效期（" + Constants.ORDER_EXPIRED_TIME + "分钟）,请重新下单");
		}
		if (commOrder == null) {
			throw BizException.withMessage("订单编号不存在:" + id);
		}
		if (commOrder.getStatus() == OrderStatus.YZF) {
			throw BizException.withMessage("订单编号：" + id + "已经支付成功,请勿重复支付");
		}
		String paymentInfo = generatePaymentInfo(commOrder, payMethod, wxAuthCode,openId);
		return paymentInfo;
	}

	/**
	 * 生成支付信息
	 */
	protected String generatePaymentInfo(CommOrder commOrder, PayMethod payMethod, String wxAuthCode, String openId) {
		Bank bank = bankService.selectById(commOrder.getBankId());
		if (bank == null) {
			throw BizException.withMessage("收款方不存在");
		} else if (bank.getStatus() == Zt.BLOCK) {
			throw BizException.withMessage("收款方已经被禁用");
		}
		String[] payPlats = StringUtils.split(payMethod.name(), "_");
		if (payPlats == null || payPlats.length == 0 || PayPlatform.findByEnumName(payPlats[0]) == null) {
			throw BizException.withMessage("根据支付方式获取支付平台失败，请确认支付方式是以支付平台的前缀来命名");
		}
		PayPlatform platform = PayPlatform.findByEnumName(payPlats[0]);
		EntityWrapper<BankConfig> wrapper = new EntityWrapper();
		wrapper.eq("bank_id", bank.getId());
		wrapper.eq("pay_plat", platform);
		List<BankConfig> bankConfigs = bankConfigMapper.selectList(wrapper);
		if (bankConfigs.size() == 0) {
			throw BizException.withMessage("收款方【" + bank.getName() + "】不支持【" + payMethod.getName() + "】");
		}
		BankConfig bankConfig = bankConfigs.get(0);
		JSONObject setting = JSONObject.fromObject(bankConfig.getPaySettting());
		if (PayMethod.ALI_PC_PAY == payMethod) {// 支付宝电脑网站支付
			String app_id = setting.getString("ali_app_id");
			String app_private_key = setting.getString("ali_merchant_private_key");
			String format = "json";
			String charset = "utf-8";
			String ali_public_key = setting.getString("ali_alipay_public_key");
			String sign_type = "RSA2";
			String notify_url = setting.getString("ali_notify_url");
			String return_url = setting.getString("ali_return_url");
			AlipayClient alipayClient = new DefaultAlipayClient(ALI_GATE_WAY, app_id, app_private_key, format, charset,
					ali_public_key, sign_type);
			AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();// 创建API对应的request
			alipayRequest.setReturnUrl(return_url);
			alipayRequest.setNotifyUrl(notify_url);
			Map<String, String> bizContent = new HashMap<String, String>();
			bizContent.put("out_trade_no", commOrder.getId());
			bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			// 四舍五入保留2位小数
			DecimalFormat moneyFormat = new DecimalFormat("0.00");
			bizContent.put("total_amount", moneyFormat.format(money));
			bizContent.put("subject", commOrder.getProduct());
			// 设置订单超时时间 2小时
			bizContent.put("timeout_express", "2h");
			alipayRequest.setBizContent(JSONObject.fromObject(bizContent).toString());
			String form = "";
			try {
				// 支付宝扫码支付表单
				form = alipayClient.pageExecute(alipayRequest).getBody(); // 调用SDK生成表单
				// 修改form表单
				String formName = "alipcpayform_" + commOrder.getId();
				long oid = System.currentTimeMillis();
				//屏蔽支付宝原有的自动表单提交，由客户端自己提交支付表单
				form = form.replace("name=\"punchout_form\"",
						"name=\"" + formName + "\" id=\"" + formName + "\" seq=\"" + oid + "\"");
				form = form.replace("method=\"post\"", "method=\"post\" target=\"_blank\" accept-charset=\"utf-8\"");
				form = form.replace("document.forms[0].submit();", "");
				commOrder.setPayMethod(payMethod);
				// 设置下单时间
				this.updateById(commOrder);
				return form;
			} catch (AlipayApiException e) {
				LOGGER.error("请求创建支付宝电脑支付表单失败：", e);
				throw BizException.withMessage("请求创建支付宝电脑支付表单失败：" + e);
			}
		}
		if (PayMethod.ALI_WAP_PAY == payMethod) {// 支付宝H5支付
			String app_id = setting.getString("ali_app_id");
			String app_private_key = setting.getString("ali_merchant_private_key");
			String format = "json";
			String charset = "utf-8";
			String ali_public_key = setting.getString("ali_alipay_public_key");
			String sign_type = "RSA2";
			String notify_url = setting.getString("ali_notify_url");
			String return_url = setting.getString("ali_return_url");
			AlipayClient alipayClient = new DefaultAlipayClient(ALI_GATE_WAY, app_id, app_private_key, format, charset,
					ali_public_key, sign_type);
			AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();// 创建API对应的request
			alipayRequest.setReturnUrl(return_url);
			alipayRequest.setNotifyUrl(notify_url);
			Map<String, String> bizContent = new HashMap<String, String>();
			bizContent.put("out_trade_no", commOrder.getId());
			bizContent.put("product_code", "QUICK_WAP_PAY");
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			// 四舍五入保留2位小数
			DecimalFormat moneyFormat = new DecimalFormat("0.00");
			bizContent.put("total_amount", moneyFormat.format(money));
			bizContent.put("subject", commOrder.getProduct());
			// 设置订单超时时间 2小时
			bizContent.put("timeout_express", "2h");
			alipayRequest.setBizContent(JSONObject.fromObject(bizContent).toString());
			String form = "";
			try {
				form = alipayClient.pageExecute(alipayRequest).getBody();
				String formName = "aliwapform_" + commOrder.getId();
				long oid = System.currentTimeMillis();
				form = form.replace("name=\"punchout_form\"",
						"name=\"" + formName + "\" id=\"" + formName + "\" seq=\"" + oid + "\"");
				form = form.replace("method=\"post\"", "method=\"post\" target=\"_blank\" accept-charset=\"utf-8\"");
				commOrder.setPayMethod(payMethod);
				// 设置下单时间
				this.updateById(commOrder);
				return form;
			} catch (AlipayApiException e) {
				LOGGER.error("请求创建支付宝H5支付失败：", e);
				throw BizException.withMessage("请求支付宝H5支付失败：" + e);
			}
		}
		if (PayMethod.ALI_APP_PAY == payMethod) {//支付宝APP支付
			String app_id = setting.getString("ali_app_id");
			String app_private_key = setting.getString("ali_merchant_private_key");
			String format = "json";
			String charset = "utf-8";
			String ali_public_key = setting.getString("ali_alipay_public_key");
			String sign_type = "RSA2";
			String notify_url = setting.getString("ali_notify_url");
			AlipayClient alipayClient = new DefaultAlipayClient(ALI_GATE_WAY, app_id, app_private_key, format, charset,
					ali_public_key, sign_type);
			AlipayTradeAppPayRequest alipayRequest = new AlipayTradeAppPayRequest();
			alipayRequest.setNotifyUrl(notify_url);
			Map<String, String> bizContent = new HashMap<String, String>();
			bizContent.put("out_trade_no", commOrder.getId());
			bizContent.put("product_code", "QUICK_MSECURITY_PAY");
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			// 四舍五入保留2位小数
			DecimalFormat moneyFormat = new DecimalFormat("0.00");
			bizContent.put("total_amount", moneyFormat.format(money));
			bizContent.put("subject", commOrder.getProduct());
			// 设置订单超时时间 2小时，此处设置的是绝对超时时间
			bizContent.put("time_expire", DateUtils.format(DateUtils.addMinute(commOrder.getCreateTime(), 120),"yyyy-MM-dd HH:mm:ss"));
			alipayRequest.setBizContent(JSONObject.fromObject(bizContent).toString());
			try {
				AlipayTradeAppPayResponse response = alipayClient.sdkExecute(alipayRequest);
				if (response.isSuccess()) {
					String form = response.getBody();
					form = URLDecoder.decode(form, "UTF-8"); 
					commOrder.setPayMethod(payMethod);
					this.updateById(commOrder);
					return form;
				}
			} catch (Exception e) {
				LOGGER.error("请求创建支付宝APP支付失败：", e);
			}
			throw BizException.withMessage("请求创建支付宝APP支付失败");
		}
		if (PayMethod.WX_PC_PAY == payMethod) {// 微信扫码支付
			String app_id = setting.getString("wx_app_id");
			String mch_id = setting.getString("wx_mch_id");
			String api_key = setting.getString("wx_api_key");
			String notify_url = setting.getString("wx_notify_url");
			WeiXinTradeTypeEnum wXinTradeTypeEnum = WeiXinTradeTypeEnum.NATIVE;
			WeiXinPrePay weiXinPrePay = new WeiXinPrePay();
			weiXinPrePay.setAppid(app_id);
			weiXinPrePay.setMchId(mch_id);
			weiXinPrePay.setBody(commOrder.getProduct());// 商品描述
			weiXinPrePay.setOutTradeNo(commOrder.getId());// 银行订单号
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			money = money.setScale(2, BigDecimal.ROUND_HALF_UP);
			Integer totalFee = money.multiply(BigDecimal.valueOf(100d)).intValue();
			weiXinPrePay.setTotalFee(totalFee);// 订单金额
			// 设置订单失效时间2小时
			Date now = new Date();
			String startTime = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
			Date expire_time = new Date(now.getTime() + 2 * 3600 * 1000);
			String expireTime = new SimpleDateFormat("yyyyMMddHHmmss").format(expire_time);
			weiXinPrePay.setTimeStart(startTime);// 订单开始时间
			weiXinPrePay.setTimeExpire(expireTime);// 订单到期时间
			weiXinPrePay.setNotifyUrl(notify_url);// 通知地址
			weiXinPrePay.setTradeType(wXinTradeTypeEnum);// 交易类型
			weiXinPrePay.setProductId("JXJY_PRODUCT_PAY_CODE");// 商品ID
			weiXinPrePay.setSpbillCreateIp("*************");// 下单IP
			String prePayXml = WeiXinPayUtils.getPrePayXml(weiXinPrePay, api_key);
			// 调用微信支付的功能,获取微信支付code_url
			Map<String, Object> prePayRequest = WeiXinPayUtils.httpXmlRequest(WeiXinPayUtils.UNIFIEDORDER_URL, "POST",
					prePayXml);
			if (WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("return_code"))
					&& WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("result_code"))) {
				// 用于生成扫码支付二维码的链接地址
				String codeUrl = String.valueOf(prePayRequest.get("code_url"));
				commOrder.setPayMethod(payMethod);
				// 设置下单时间
				this.updateById(commOrder);
				String payment;
				try {
					payment = QrCodeUtils.createQrCode(codeUrl, 300, 300);
					return payment;// 支付二维码
				} catch (Exception e) {
					LOGGER.error("ERROR", e);
					throw BizException.withMessage("生成微信支付二维码失败");
				}
			} else {
				throw BizException.withMessage("生成微信支付二维码失败");
			}
		}
		if (PayMethod.WX_JSAPI_PAY == payMethod) {// 微信公众号支付
			String openid = null;
			if (StringUtils.isEmpty(wxAuthCode)) {
				throw BizException.withMessage("请传入微信Oauth2临时授权码");
			}
			openid = this.queryOpenIdByCode(wxAuthCode, bank.getId());
			String app_id = setting.getString("wx_app_id");
			String mch_id = setting.getString("wx_mch_id");
			String api_key = setting.getString("wx_api_key");
			String notify_url = setting.getString("wx_notify_url");
			WeiXinTradeTypeEnum wXinTradeTypeEnum = WeiXinTradeTypeEnum.JSAPI;
			WeiXinPrePay weiXinPrePay = new WeiXinPrePay();
			weiXinPrePay.setAppid(app_id);
			weiXinPrePay.setMchId(mch_id);
			weiXinPrePay.setBody(commOrder.getProduct());// 商品描述
			weiXinPrePay.setOutTradeNo(commOrder.getId());// 银行订单号
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			money = money.setScale(2, BigDecimal.ROUND_HALF_UP);
			Integer totalFee = money.multiply(BigDecimal.valueOf(100d)).intValue();
			weiXinPrePay.setTotalFee(totalFee);// 订单金额
			// 设置订单失效时间2小时
			Date now = new Date();
			String startTime = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
			Date expire_time = new Date(now.getTime() + 2 * 3600 * 1000);
			String expireTime = new SimpleDateFormat("yyyyMMddHHmmss").format(expire_time);
			weiXinPrePay.setTimeStart(startTime);// 订单开始时间
			weiXinPrePay.setTimeExpire(expireTime);// 订单到期时间
			weiXinPrePay.setNotifyUrl(notify_url);// 通知地址
			weiXinPrePay.setOpenid(openid);
			weiXinPrePay.setTradeType(wXinTradeTypeEnum);// 交易类型
			weiXinPrePay.setProductId("JXJY_PRODUCT_PAY_CODE");// 商品ID
			weiXinPrePay.setSpbillCreateIp("*************");// 下单IP
			String prePayXml = WeiXinPayUtils.getPrePayXml(weiXinPrePay, api_key);
			// 调用微信支付的功能,获取微信支付code_url
			Map<String, Object> prePayRequest = WeiXinPayUtils.httpXmlRequest(WeiXinPayUtils.UNIFIEDORDER_URL, "POST",
					prePayXml);
			LOGGER.info("微信响应信息:" + JSONObject.fromObject(prePayRequest).toString());
			if (WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("return_code"))
					&& WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("result_code"))) {
				String prePayId = String.valueOf(prePayRequest.get("prepay_id"));
				commOrder.setPayMethod(payMethod);
				this.updateById(commOrder);
				Map<String, Object> prePayMap = new HashMap<String, Object>();
				prePayMap.put("appId", app_id);
				prePayMap.put("nonceStr", UUID.randomUUID().toString());
				prePayMap.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
				prePayMap.put("package", "prepay_id=" + prePayId);
				prePayMap.put("signType", "MD5");
				String argPreSign = WeiXinPayUtils.getStringByMap(prePayMap) + "&key=" + api_key;
				String preSign = MD5Util.encode(argPreSign).toUpperCase();
				prePayMap.put("paySign", preSign);
				return JSONObject.fromObject(prePayMap).toString();
			} else {
				throw BizException.withMessage("生成微信JSAPI支付信息失败");
			}
		}
		if (PayMethod.WX_APP_PAY == payMethod) {//微信APP支付
			String app_id = setting.getString("wx_app_id");
			String mch_id = setting.getString("wx_mch_id");
			String api_key = setting.getString("wx_api_key");
			String notify_url = setting.getString("wx_notify_url");
			WeiXinTradeTypeEnum wXinTradeTypeEnum = WeiXinTradeTypeEnum.APP;
			WeiXinPrePay weiXinPrePay = new WeiXinPrePay();
			weiXinPrePay.setAppid(app_id);
			weiXinPrePay.setMchId(mch_id);
			weiXinPrePay.setBody(commOrder.getProduct());// 商品描述
			weiXinPrePay.setOutTradeNo(commOrder.getId());// 银行订单号
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			money = money.setScale(2, BigDecimal.ROUND_HALF_UP);
			Integer totalFee = money.multiply(BigDecimal.valueOf(100d)).intValue();
			weiXinPrePay.setTotalFee(totalFee);// 订单金额
			// 设置订单失效时间2小时
			Date now = new Date();
			String startTime = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
			Date expire_time = new Date(now.getTime() + 2 * 3600 * 1000);
			String expireTime = new SimpleDateFormat("yyyyMMddHHmmss").format(expire_time);
			weiXinPrePay.setTimeStart(startTime);// 订单开始时间
			weiXinPrePay.setTimeExpire(expireTime);// 订单到期时间
			weiXinPrePay.setNotifyUrl(notify_url);// 通知地址
			weiXinPrePay.setTradeType(wXinTradeTypeEnum);// 交易类型
			weiXinPrePay.setProductId("JXJY_PRODUCT_PAY_CODE");// 商品ID
			weiXinPrePay.setSpbillCreateIp("*************");// 下单IP
			String prePayXml = WeiXinPayUtils.getPrePayXml(weiXinPrePay, api_key);
			// 调用微信支付的功能,获取微信支付code_url
			Map<String, Object> prePayRequest = WeiXinPayUtils.httpXmlRequest(WeiXinPayUtils.UNIFIEDORDER_URL, "POST",
					prePayXml);
			if (WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("return_code"))
					&& WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("result_code"))) {
				try {
					Map<String, Object> result = new HashMap<String, Object>();
		            String timestamp = String.valueOf(System.currentTimeMillis()/1000);
		            String mchid =  BaseUtil.getStringValueFromMap(prePayRequest, "mch_id");
		            String appid =  BaseUtil.getStringValueFromMap(prePayRequest, "appid");
		            String prepay_id = BaseUtil.getStringValueFromMap(prePayRequest, "prepay_id");
		            String nonce_str =  BaseUtil.getStringValueFromMap(prePayRequest, "nonce_str");
		            // 重新计算APP支付请求签名
					Map<String, Object> prePayMap = new HashMap<String, Object>();
					prePayMap.put("appid", appid);
					prePayMap.put("partnerid", mchid);
					prePayMap.put("prepayid", prepay_id);
					prePayMap.put("package", "Sign=WXPay");
					prePayMap.put("noncestr", nonce_str);
					prePayMap.put("timestamp", timestamp);
					String argPreSign = WeiXinPayUtils.getStringByMap(prePayMap) + "&key=" + api_key;
					String sign = MD5Util.encode(argPreSign).toUpperCase();
					prePayMap.put("sign", sign);
					return JSONObject.fromObject(prePayMap).toString();
		        } catch (Exception e) {
		        	LOGGER.error("ERROR", e);
					throw BizException.withMessage("生成微信APP支付信息失败");
		        }
			} else {
				throw BizException.withMessage("生成微信APP支付信息失败");
			}
		}
		if (PayMethod.WX_APPLET_PAY == payMethod) {//微信小程序支付
			if (StringUtils.isEmpty(openId)) {
				throw BizException.withMessage("请传入微信openid");
			}
			String app_id = setting.getString("wx_app_id");
			String mch_id = setting.getString("wx_mch_id");
			String api_key = setting.getString("wx_api_key");
			String notify_url = setting.getString("wx_notify_url");
			WeiXinTradeTypeEnum wXinTradeTypeEnum = WeiXinTradeTypeEnum.JSAPI;
			WeiXinPrePay weiXinPrePay = new WeiXinPrePay();
			weiXinPrePay.setAppid(app_id);
			weiXinPrePay.setMchId(mch_id);
			weiXinPrePay.setBody(commOrder.getProduct());// 商品描述
			weiXinPrePay.setOutTradeNo(commOrder.getId());// 银行订单号
			BigDecimal money = new BigDecimal(String.valueOf(commOrder.getAmount()));
			money = money.setScale(2, BigDecimal.ROUND_HALF_UP);
			Integer totalFee = money.multiply(BigDecimal.valueOf(100d)).intValue();
			weiXinPrePay.setTotalFee(totalFee);// 订单金额
			// 设置订单失效时间2小时
			Date now = new Date();
			String startTime = new SimpleDateFormat("yyyyMMddHHmmss").format(now);
			Date expire_time = new Date(now.getTime() + 2 * 3600 * 1000);
			String expireTime = new SimpleDateFormat("yyyyMMddHHmmss").format(expire_time);
			weiXinPrePay.setTimeStart(startTime);// 订单开始时间
			weiXinPrePay.setTimeExpire(expireTime);// 订单到期时间
			weiXinPrePay.setNotifyUrl(notify_url);// 通知地址
			weiXinPrePay.setOpenid(openId);
			weiXinPrePay.setTradeType(wXinTradeTypeEnum);// 交易类型
			weiXinPrePay.setProductId("JXJY_PRODUCT_PAY_CODE");// 商品ID
			weiXinPrePay.setSpbillCreateIp("*************");// 下单IP
			String prePayXml = WeiXinPayUtils.getPrePayXml(weiXinPrePay, api_key);
			// 调用微信支付的功能,获取微信支付code_url
			Map<String, Object> prePayRequest = WeiXinPayUtils.httpXmlRequest(WeiXinPayUtils.UNIFIEDORDER_URL, "POST",
					prePayXml);
			LOGGER.info("微信响应信息:" + JSONObject.fromObject(prePayRequest).toString());
			if (WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("return_code"))
					&& WeixinTradeStateEnum.SUCCESS.name().equals(prePayRequest.get("result_code"))) {
				String prePayId = String.valueOf(prePayRequest.get("prepay_id"));
				commOrder.setPayMethod(payMethod);
				this.updateById(commOrder);
				Map<String, Object> prePayMap = new HashMap<String, Object>();
				prePayMap.put("appId", app_id);
				prePayMap.put("nonceStr", UUID.randomUUID().toString().replace("-", ""));
				prePayMap.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
				prePayMap.put("package", "prepay_id=" + prePayId);
				prePayMap.put("signType", "MD5");
				String argPreSign = WeiXinPayUtils.getStringByMap(prePayMap) + "&key=" + api_key;
				String preSign = MD5Util.encode(argPreSign).toUpperCase();
				prePayMap.put("paySign", preSign);
				return JSONObject.fromObject(prePayMap).toString();
			} else {
				throw BizException.withMessage("生成微信小程序支付信息失败");
			}
		}
		if (PayMethod.CCB_JUHE_PC_PAY == payMethod) {// 建行聚合支付-PC
			String MERCHANTID = BaseUtil.getStringValueFromJson(setting, "ccb_merchantid");// 商户代码
			String POSID = BaseUtil.getStringValueFromJson(setting, "ccb_postid");// 商户柜台代码
			String BRANCHID = BaseUtil.getStringValueFromJson(setting, "ccb_branchid");// 分行代码
			String PUB = BaseUtil.getStringValueFromJson(setting, "ccb_pub");// 商户柜台公钥后30位
			String qrUrl = createCCBQrCode(commOrder, MERCHANTID, POSID, BRANCHID, PUB, payMethod);
			try {
				String payment = QrCodeUtils.createQrCode(qrUrl, 300, 300);
				return payment;// 支付二维码
			} catch (Exception e) {
				LOGGER.error("ERROR", e);
				throw BizException.withMessage("生成建行聚合支付-PC二维码失败");
			}
		}
		if (PayMethod.CCB_JUHE_MOBILE_PAY == payMethod) {// 建行聚合支付-移动端
			String MERCHANTID = BaseUtil.getStringValueFromJson(setting, "ccb_merchantid");// 商户代码
			String POSID = BaseUtil.getStringValueFromJson(setting, "ccb_postid");// 商户柜台代码
			String BRANCHID = BaseUtil.getStringValueFromJson(setting, "ccb_branchid");// 分行代码
			String PUB = BaseUtil.getStringValueFromJson(setting, "ccb_pub");// 商户柜台公钥后30位
			String qrUrl = createCCBQrCode(commOrder, MERCHANTID, POSID, BRANCHID, PUB, payMethod);
			return qrUrl;
		}
		if (PayMethod.ICBC_JUHE_PC_PAY == payMethod) {// 工行聚合支付-PC
			String APP_ID = BaseUtil.getStringValueFromJson(setting, "icbc_appid");// appid
			String MERCHANT_ID = BaseUtil.getStringValueFromJson(setting, "icbc_merchantid");// 商户编号
			String MER_PRTCL_NO = BaseUtil.getStringValueFromJson(setting, "icbc_mer_prtcl_no");// 协议编号
			String PUBLIC_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_public_key");// 公钥
			String PRIVATE_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_private_key");// 私钥
			String MER_URL = BaseUtil.getStringValueFromJson(setting, "icbc_mer_url");// 异步通知地址
			String qrUrl = createICBCQrCode(commOrder,  APP_ID,  MERCHANT_ID,  MER_PRTCL_NO, PUBLIC_KEY,  PRIVATE_KEY,  payMethod,  MER_URL);
			try {
				String payment = QrCodeUtils.createQrCode(qrUrl, 300, 300);
				return payment;// 支付二维码
			} catch (Exception e) {
				LOGGER.error("ERROR", e);
				throw BizException.withMessage("生成工商银行聚合支付-PC二维码失败");
			}
		}
		if (PayMethod.ICBC_JUHE_MOBILE_PAY == payMethod) {// 工行聚合支付-移动端
			String APP_ID = BaseUtil.getStringValueFromJson(setting, "icbc_appid");// appid
			String MERCHANT_ID = BaseUtil.getStringValueFromJson(setting, "icbc_merchantid");// 商户编号
			String MER_PRTCL_NO = BaseUtil.getStringValueFromJson(setting, "icbc_mer_prtcl_no");// 协议编号
			String PUBLIC_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_public_key");// 公钥
			String PRIVATE_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_private_key");// 私钥
			String MER_URL = BaseUtil.getStringValueFromJson(setting, "icbc_mer_url");// 异步通知地址
			String qrUrl = createICBCQrCode(commOrder,  APP_ID,  MERCHANT_ID,  MER_PRTCL_NO, PUBLIC_KEY,  PRIVATE_KEY,  payMethod,  MER_URL);
			return qrUrl;
		}
		throw BizException.withMessage("不支持的支付方式:" + commOrder.getPayMethod().getName());
	}

	/**
	 * 支付宝异步通知
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processAliNotify(Map<String, String> params) throws AlipayApiException, ParseException {
		String ordeNo = params.get("out_trade_no");// 订单号
		CommOrder commOrder = this.selectById(ordeNo);
		if (commOrder == null) {
			throw BizException.withMessage("订单编号在系统中不存在:" + ordeNo);
		}
		Bank bank = null;
		if (StringUtils.isNotEmpty(commOrder.getBankId())) {
			bank = bankService.selectById(commOrder.getBankId());
		} else {
			throw BizException.withMessage("订单的收款方未设置");
		}
		if (bank == null) {
			throw BizException.withMessage("订单的收款方在系统中不存在");
		}

		EntityWrapper<BankConfig> wrapper = new EntityWrapper();
		wrapper.eq("bank_id", bank.getId());
		wrapper.eq("pay_plat", PayPlatform.ALI.name());
		List<BankConfig> bankConfigs = bankConfigMapper.selectList(wrapper);
		if (bankConfigs.size() == 0) {
			throw BizException.withMessage("收款方【" + bank.getName() + "】不支持支付宝配置");
		}
		BankConfig bankConfig = bankConfigs.get(0);
		JSONObject config = JSONObject.fromObject(bankConfig.getPaySettting());
		String charset = "utf-8";
		String ali_public_key = config.getString("ali_alipay_public_key");
		// 调用SDK验证签名
		boolean signVerified = AlipaySignature.rsaCheckV1(params, ali_public_key, charset, "RSA2");
		if (signVerified) {
			String tradeStatus = params.get("trade_status");
			if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {// 支付成功
				// 更新订单的交易流水号，支付时间，支付状态,付款账号，以及业务ID对应的后续逻辑
				String tradeNo = params.get("trade_no");// 支付宝交易号
				Date payDate = DateUtils.parse(params.get("gmt_payment"), "yyyy-MM-dd HH:mm:ss");
				String fkzh = "buyer_id=" + (params.get("buyer_id") == null ? "" : params.get("buyer_id"));// 支付宝账号对应的唯一支付宝用户号
				commOrder.setTransNumber(tradeNo);
				commOrder.setPayTime(payDate);
				commOrder.setStatus(OrderStatus.YZF);
				commOrder.setPayAccount(fkzh);
				commOrder.setGetResultBy("NOTIFY[异步通知]");
				this.updateById(commOrder);
				// 订单支付成功之后完成后续的业务逻辑,
				commOrderStatusQueryService.afterOrderPaySuccess(commOrder);
			}
		}
	}

	/**
	 * 微信异步通知
	 * 
	 * @throws ParseException
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processWxNotify(Map<String, String> params) throws ParseException {
		// 签名校验
		String orderNo = params.get("out_trade_no");// 订单号
		CommOrder commOrder = this.selectById(orderNo);
		if (commOrder == null) {
			throw BizException.withMessage("订单编号在系统中不存在:" + orderNo);
		}
		Bank bank = null;
		if (StringUtils.isNotEmpty(commOrder.getBankId())) {
			bank = bankService.selectById(commOrder.getBankId());
		} else {
			throw BizException.withMessage("订单的收款方未设置");
		}
		if (bank == null) {
			throw BizException.withMessage("订单的收款方在系统中不存在");
		}
		EntityWrapper<BankConfig> wrapper = new EntityWrapper();
		wrapper.eq("bank_id", bank.getId());
		wrapper.eq("pay_plat", PayPlatform.WX);
		List<BankConfig> bankConfigs = bankConfigMapper.selectList(wrapper);
		if (bankConfigs.size() == 0) {
			throw BizException.withMessage("收款方【" + bank.getName() + "】不支持支付宝配置");
		}
		BankConfig bankConfig = bankConfigs.get(0);
		JSONObject config = JSONObject.fromObject(bankConfig.getPaySettting());
		String api_key = config.getString("wx_api_key");
		String sign = params.remove("sign");
		if (WeiXinPayUtils.checkNotifySign(params, sign, api_key)) {
			if (WeixinTradeStateEnum.SUCCESS.name().equals(params.get("result_code"))) {// 支付成功
				// 更新订单的交易流水号，支付时间，支付状态,付款账号，以及业务ID对应的后续逻辑
				String tradeNo = params.get("transaction_id").toString();// 交易流水号
				String timeEnd = params.get("time_end").toString();
				Date payDate = null;
				payDate = DateUtils.parse(timeEnd, "yyyyMMddHHmmss");
				String fkzh = "wx_openid=" + (params.get("openid") == null ? "" : params.get("openid"));
				commOrder.setTransNumber(tradeNo);
				commOrder.setPayTime(payDate);
				commOrder.setStatus(OrderStatus.YZF);
				commOrder.setPayAccount(fkzh);
				commOrder.setGetResultBy("NOTIFY[异步通知]");
				this.updateById(commOrder);
				// 订单支付成功之后完成后续的业务逻辑
				commOrderStatusQueryService.afterOrderPaySuccess(commOrder);
			}
		}
	}

	/**
	 * 建行异步通知处理
	 * 
	 * @throws ParseException
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processCCBNotify(Map<String, String> params) throws ParseException {
		LOGGER.info("* 建行异步通知 params: {}", params);
		String POSID = params.get("POSID");
		String BRANCHID = params.get("BRANCHID");
		String ORDERID = params.get("ORDERID");
		String PAYMENT = params.get("PAYMENT");
		String CURCODE = params.get("CURCODE");
		String REMARK1 = params.get("REMARK1");
		String REMARK2 = params.get("REMARK2");
		String ACC_TYPE = params.get("ACC_TYPE");
		String SUCCESS = params.get("SUCCESS");
		String TYPE = params.get("TYPE");
		String REFERER = params.get("REFERER");
		String CLIENTIP = params.get("CLIENTIP");
		String ACCDATE = params.get("ACCDATE");
		String INSTALLNUM = params.get("INSTALLNUM");
		String ERRMSG = params.get("ERRMSG");
		String USRMSG = params.get("USRMSG");
		String USRINFO = params.get("USRINFO");
		String DISCOUNT = params.get("DISCOUNT");
		String ZHJF = params.get("ZHJF");
		String OPENID = params.get("OPENID");
		String SUB_OPENID = params.get("SUB_OPENID");
		String PAYMENT_DETAILS = params.get("PAYMENT_DETAILS");
		String SIGN = params.get("SIGN");

		// 判断订单编号在系统中是否存在
		CommOrder commOrder = getOrderByUnionOrderNo(ORDERID);
		if (commOrder == null) {
			throw BizException.withMessage("聚合支付订单编号[" + ORDERID + "]在系统中不存在");
		}

		StringBuffer signBuffer = new StringBuffer();
		signBuffer.append("POSID").append("=").append(POSID).append("&");
		signBuffer.append("BRANCHID").append("=").append(BRANCHID).append("&");
		signBuffer.append("ORDERID").append("=").append(ORDERID).append("&");
		signBuffer.append("PAYMENT").append("=").append(PAYMENT).append("&");
		signBuffer.append("CURCODE").append("=").append(CURCODE).append("&");
		signBuffer.append("REMARK1").append("=").append(REMARK1).append("&");
		signBuffer.append("REMARK2").append("=").append(REMARK2).append("&");
		signBuffer.append("ACC_TYPE").append("=").append(ACC_TYPE).append("&");
		signBuffer.append("SUCCESS").append("=").append(SUCCESS).append("&");
		if (params.containsKey("TYPE")) {// 有返回时，才参与签名运算
			signBuffer.append("TYPE").append("=").append(TYPE).append("&");
		}
		if (params.containsKey("REFERER")) {
			signBuffer.append("REFERER").append("=").append(REFERER).append("&");
		}
		if (params.containsKey("CLIENTIP")) {
			signBuffer.append("CLIENTIP").append("=").append(CLIENTIP).append("&");
		}
		if (params.containsKey("ACCDATE")) {
			signBuffer.append("ACCDATE").append("=").append(ACCDATE).append("&");
		}
		if (params.containsKey("INSTALLNUM")) {
			signBuffer.append("INSTALLNUM").append("=").append(INSTALLNUM).append("&");
		}
		if (params.containsKey("ERRMSG")) {
			signBuffer.append("ERRMSG").append("=").append(ERRMSG).append("&");
		}
		if (params.containsKey("USRMSG")) {
			signBuffer.append("USRMSG").append("=").append(USRMSG).append("&");
		}
		if (params.containsKey("USRINFO")) {
			signBuffer.append("USRINFO").append("=").append(USRINFO).append("&");
		}
		if (params.containsKey("DISCOUNT")) {
			signBuffer.append("DISCOUNT").append("=").append(DISCOUNT).append("&");
		}
		if (params.containsKey("ZHJF")) {
			signBuffer.append("ZHJF").append("=").append(ZHJF).append("&");
		}
		if (params.containsKey("OPENID")) {
			signBuffer.append("OPENID").append("=").append(OPENID).append("&");
		}
		if (params.containsKey("SUB_OPENID")) {
			signBuffer.append("SUB_OPENID").append("=").append(SUB_OPENID).append("&");
		}
		if (params.containsKey("PAYMENT_DETAILS")) {
			signBuffer.append("PAYMENT_DETAILS").append("=").append(PAYMENT_DETAILS).append("&");
		}
		signBuffer = signBuffer.deleteCharAt(signBuffer.length() - 1);
		JSONObject ccbPayCObject = bankConfigService.getCCBPayConfig(commOrder.getBankId());
		String PUB = BaseUtil.getStringValueFromJson(ccbPayCObject, "ccb_pub");
		RSASig rsa = new RSASig();
		rsa.setPublicKey(PUB);
		if (rsa.verifySigature(SIGN, signBuffer.toString())) {// 签名校验
			if (SUCCESS.equals(Constants.STRING_YES)) {// 支付成功
				commOrder.setPayTime(new Date());
				commOrder.setStatus(OrderStatus.YZF);
				commOrder.setPayAccount(ACC_TYPE);
				commOrder.setGetResultBy("NOTIFY[异步通知]");
				this.updateById(commOrder);
				// 订单支付成功之后完成后续的业务逻辑
				commOrderStatusQueryService.afterOrderPaySuccess(commOrder);
			}
		} else {
			throw BizException.withMessage("签名校验失败");
		}

	}
	
	/**
	 * 工行异步通知
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processICBCNotify(Map<String, String> params, String requestUri, HttpServletResponse response) throws Exception {
		String sign = params.get("sign");
		String bizContent = params.get("biz_content");
		if (StringUtils.isEmpty(sign) || StringUtils.isEmpty(bizContent)) {
			return ;
		}
		JSONObject bizJsonObject = JSONObject.fromObject(bizContent);
		String orderNo = BaseUtil.getStringValueFromJson(bizJsonObject, "out_trade_no");
		if (StringUtils.isEmpty(orderNo)) {
			return ;
		}
		CommOrder commOrder = this.getOrderByUnionOrderNo(orderNo);
		if (commOrder == null) {
			return;
		}
		JSONObject setting = bankConfigService.getICBCPayConfig(commOrder.getBankId());
		//通知延签处理
		String PUBLIC_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_public_key");// 公钥

		params.remove("sign");
		String signParams = requestUri+"?";
		StringBuffer buffer = new StringBuffer(signParams);
		for(Map.Entry<String,String> entry : params.entrySet()){
            String key = entry.getKey();
            String value = entry.getValue();
            value = StringUtils.isEmpty(value)? "" : value;
            buffer.append(key + "=" + value).append("&") ;
		}
		buffer = buffer.deleteCharAt(buffer.length()-1);
		signParams = buffer.toString();
        boolean signVerified = IcbcSignature.verify(signParams, IcbcConstants.SIGN_TYPE_RSA, PUBLIC_KEY, IcbcConstants.CHARSET_UTF8, sign);
        if (signVerified) {//验签通过
			String returnCode = BaseUtil.getStringValueFromJson(bizJsonObject, "return_code");
			if ("0".equals(returnCode)) {
				String payTime = BaseUtil.getStringValueFromJson(bizJsonObject, "pay_time");
				String orderId = BaseUtil.getStringValueFromJson(bizJsonObject, "order_id");
				commOrder.setPayTime(DateUtils.parse(payTime, "yyyyMMddHHmmss"));
				commOrder.setStatus(OrderStatus.YZF);
				commOrder.setTransNumber(orderId);
				commOrder.setGetResultBy("NOTIFY[异步通知]");
				this.updateById(commOrder);
				// 订单支付成功之后完成后续的业务逻辑
				commOrderStatusQueryService.afterOrderPaySuccess(commOrder);
			}
		}
        else {//验签失败
        	bizContent= "{\"return_code\":-12345,\"return_msg\":\"icbc sign not pass.\"}";
        }
        //应答
        PrintWriter out = response.getWriter();
		try {
			String charset = params.get("charset");
			String PRIVATE_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_private_key");// 私钥
			//签名参数
			String ResponseSignStr ="\"response_biz_content\":"+bizContent+","+"\"sign_type\":"+"\"RSA2\"";
			String responseSign = IcbcSignature.sign(ResponseSignStr, "RSA2", PRIVATE_KEY, charset);
			String results="{"+ResponseSignStr+",\"sign\":\""+responseSign+"\"}";
			response.setContentType("application/json; charset=utf-8");
			out.write(results);
			LOGGER.info("ICBC异步通知应答成功");
		} catch (Exception e) {
			LOGGER.error("ICBC异步通知应答处理失败", e);
			out.write(e.getMessage());
		} finally {
			if (out != null) {
				out.flush();
				out.close();
			}
		}
	}

	/**
	 * 同步订单的支付状态,为减轻系统压力，只同步最近5天创建的订单的支付状态
	 */
	public void doSyncOrderStatus() {
		EntityWrapper<CommOrder> wrapper = new EntityWrapper<CommOrder>();
		wrapper.eq("status", OrderStatus.WZF.name());
		wrapper.between("create_time", DateUtils.addDay(new Date(), -5), new Date());
		wrapper.orderBy("create_time", true);
		List<CommOrder> commOrders = mapper.selectList(wrapper);
		long aliCount = 0;
		long wxCount = 0;
		long icbcCount = 0;
		for (CommOrder commOrder : commOrders) {
			if (commOrder.getStatus() == OrderStatus.WZF && commOrder.getStatus() != null
					&& commOrder.getPayMethod() != null) {
				// 同步支付宝订单
				if (commOrder.getPayMethod().name().startsWith(PayPlatform.ALI.name())) {
					try {
						boolean isAliOrderPayed = commOrderStatusQueryService.isAliOrderPayed(commOrder);
						if (isAliOrderPayed) {
							aliCount++;
						}
					} catch (Exception e) {
					}

				}
				// 同步微信的订单状态
				if (commOrder.getPayMethod().name().startsWith(PayPlatform.WX.name())) {
					try {
						boolean isWxOrderPayed = commOrderStatusQueryService.isWxOrderPayed(commOrder);
						if (isWxOrderPayed) {
							wxCount++;
						}
					} catch (Exception e) {
					}
				}
				// 同步工行的订单状态
				if (commOrder.getPayMethod().name().startsWith(PayPlatform.ICBC.name())) {
					try {
						boolean isICBCOrderPayed = commOrderStatusQueryService.isICBCOrderPayed(commOrder);
						if (isICBCOrderPayed) {
							icbcCount++;
						}
					} catch (Exception e) {
					}
				}
			}
		}
		LOGGER.info("同步平台交易订单状态结束，共同步【" + commOrders.size() + "】笔订单，【" + aliCount + "】笔支付宝订单由[未支付]更新为[已支付]，【" + wxCount
				+ "】笔微信订单由[未支付]更新为[已支付]，【" + icbcCount + "】笔工行订单由[未支付]更新为[已支付]");
	}

	/**
	 * 生成建设银行聚合支付的二维码
	 */
	@Transactional
	public String createCCBQrCode(CommOrder order, String MERCHANTID, String POSID, String BRANCHID, String PUB,
			PayMethod payMethod) {
		try {
			String ORDERID = order.getUnionOrderNo();
			if (StringUtils.isEmpty(ORDERID)) {
				ORDERID = MERCHANTID + DateUtils.format(new Date(), "yyyyMMddHHmmss")
						+ BaseUtil.generateRandomEnglishLetterString(1).toUpperCase();
			}
			String PAYMENT = order.getAmount() + "";
			String CURCODE = "01";
			String TXCODE = "530550";
			String REMARK1 = "";
			if (order.getUserType() == UserType.STUDENT) {
				StudentInfo studentInfo = studentInfoService.getByStudentId(order.getPayUserId());
				REMARK1 = "XUNW-ZYPX-" + studentInfo.getSfzh();
			} else {
				REMARK1 = "XUNW-ZYPX-ADMIN-USER";
			}
			String REMARK2 = "whxunw";
			String RETURNTYPE = "3";
			// 设置订单超时时间为2小时
			String TIMEOUT = DateUtils.format(DateUtils.addMinute(new Date(), 120), "yyyyMMddHHmmss");
			PUB = PUB.substring(PUB.length() - 30);// 取商户柜台公钥的后30位
			String MAC = getMac(MERCHANTID, POSID, BRANCHID, ORDERID, PAYMENT, CURCODE, TXCODE, REMARK1, REMARK2,
					RETURNTYPE, TIMEOUT, PUB);
			String url = "https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain?CCB_IBSVersion=V6&MERCHANTID={0}&POSID={1}&BRANCHID={2}&ORDERID={3}&PAYMENT={4}&"
					+ "CURCODE={5}&TXCODE={6}&REMARK1={7}&REMARK2={8}&RETURNTYPE={9}&TIMEOUT={10}&MAC={11}";
			url = MessageFormat.format(url, MERCHANTID, POSID, BRANCHID, ORDERID, PAYMENT, CURCODE, TXCODE, REMARK1,
					REMARK2, RETURNTYPE, TIMEOUT, MAC);
			String payURLResp = HttpKit.post(url);
			LOGGER.info("---response1:\n" + payURLResp);
			JSONObject payURLRespJSON = JSONObject.fromObject(payURLResp);
			if (Constants.STRING_TRUE.equals(BaseUtil.getStringValueFromJson(payURLRespJSON, "SUCCESS"))) {
				String payUrl = BaseUtil.getStringValueFromJson(payURLRespJSON, "PAYURL");
				String qrUrlResponse = HttpKit.get(payUrl);
				JSONObject qrUrlResponseJSON = JSONObject.fromObject(qrUrlResponse);
				if (Constants.STRING_TRUE.equals(BaseUtil.getStringValueFromJson(qrUrlResponseJSON, "SUCCESS"))) {
					String qrUrl = BaseUtil.getStringValueFromJson(qrUrlResponseJSON, "QRURL");
					qrUrl = URLDecoder.decode(qrUrl, "UTF-8");
					LOGGER.info("qrUrl:\n" + qrUrl);
					order.setUnionOrderNo(ORDERID);
					order.setPayMethod(payMethod);
					this.updateById(order);
					return qrUrl;
				}
			}
		} catch (Exception e) {
			LOGGER.error("ERROR:", e);
		}
		throw BizException.withMessage("生成建行聚合支付二维码失败，请联系系统管理人员解决");
	}

	/**
	 * 建设银行 MAC校验域
	 */
	public static String getMac(String MERCHANTID, String POSID, String BRANCHID, String ORDERID, String PAYMENT,
			String CURCODE, String TXCODE, String REMARK1, String REMARK2, String RETURNTYPE, String TIMEOUT,
			String PUB) {
		String format = "MERCHANTID={0}&POSID={1}&BRANCHID={2}&ORDERID={3}&PAYMENT={4}&"
				+ "CURCODE={5}&TXCODE={6}&REMARK1={7}&REMARK2={8}&RETURNTYPE={9}&TIMEOUT={10}&PUB={11}";
		String content = MessageFormat.format(format, MERCHANTID, POSID, BRANCHID, ORDERID, PAYMENT, CURCODE, TXCODE,
				REMARK1, REMARK2, RETURNTYPE, TIMEOUT, PUB);
		return DigestUtils.md5Hex(content);
	}

	/**
	 * 生成工商银行的聚合支付二维码
	 */
	@Transactional
	public String createICBCQrCode(CommOrder order, String APP_ID, String MERCHANT_ID, String MER_PRTCL_NO,
			String PUBLIC_KEY, String PRIVATE_KEY, PayMethod payMethod, String merUrl) {
		try {
			String ORDERID = order.getUnionOrderNo();
			if (StringUtils.isEmpty(ORDERID)) {
				ORDERID = DateUtils.format(new Date(), "yyyyMMddHHmmss") + BaseUtil.generateId2().toUpperCase();
				ORDERID = ORDERID.substring(0, 30);
			}
			BigDecimal money = new BigDecimal(String.valueOf(order.getAmount()));
			money = money.setScale(2, BigDecimal.ROUND_HALF_UP);
			Integer totalFee = money.multiply(BigDecimal.valueOf(100d)).intValue();
			DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, IcbcConstants.SIGN_TYPE_RSA2, PRIVATE_KEY,
					PUBLIC_KEY);
			CardbusinessQrcodeConsumptionRequestV1 request = new CardbusinessQrcodeConsumptionRequestV1();
			request.setServiceUrl("https://gw.open.icbc.com.cn/api/cardbusiness/qrcode/consumption/V1");
			CardbusinessQrcodeConsumptionRequestV1.CardbusinessQrcodeConsumptionRequestV1Biz bizContent = new CardbusinessQrcodeConsumptionRequestV1.CardbusinessQrcodeConsumptionRequestV1Biz();
			request.setBizContent(bizContent);
			bizContent.setOut_trade_no(ORDERID);
			bizContent.setMer_id(MERCHANT_ID);
			bizContent.setMer_prtcl_no(MER_PRTCL_NO);
			bizContent.setAccess_type("4");
			bizContent.setCur_type("001");
			bizContent.setAmount(totalFee + "");// 金额 单位是 分
			bizContent.setIcbc_appid(APP_ID);
			bizContent.setMer_url(merUrl);// 异步通知商户地址
			bizContent.setExpire_time(Constants.ORDER_EXPIRED_TIME * 60 + "");// 订单失效时间，单位为秒
			bizContent.setNotify_type("HS");
			bizContent.setResult_type("1");// 取值“1”，银行只向商户发送交易成功的通知信息
			bizContent.setOrder_date(DateUtils.getCurrentDate("yyyy-MM-dd HH:mm:ss"));
			String goodsName = "";
			String body = "";
			if (order.getUserType() == UserType.STUDENT) {
				StudentInfo studentInfo = studentInfoService.getByStudentId(order.getPayUserId());
				goodsName = "XUNW-ZYPX-" + studentInfo.getSfzh();
				body = order.getProduct();
			} else {
				goodsName = "XUNW-ZYPX-ADMIN-USER";
				body = order.getProduct();
			}
			bizContent.setGoods_name(goodsName);// 长度40
			bizContent.setBody(body);// 长度128
			CardbusinessQrcodeConsumptionResponseV1 response;
			response = client.execute(request, BaseUtil.generateId2());
			if (response.isSuccess()) {
				String qrUrl = response.getCodeUrl();
				order.setUnionOrderNo(ORDERID);
				order.setPayMethod(payMethod);
				this.updateById(order);
				return qrUrl;
			}
		} catch (Exception e) {
			LOGGER.error("ERROR:", e);
		}
		throw BizException.withMessage("生成工商银行聚合支付二维码失败，请联系系统管理人员解决");
	}

	/**
	 * 根据学员ID查询学员的订单
	 */
	public List<CommOrder> getOrdersByStudentId(String studentId, String hostOrgId) {
		EntityWrapper<CommOrder> wrapper = new EntityWrapper<CommOrder>();
		wrapper.eq("pay_user_id", studentId).eq("user_type", UserType.STUDENT).eq("host_org_id", hostOrgId);
		wrapper.orderBy("create_time", false);
		List<CommOrder> commOrders = mapper.selectList(wrapper);
		for (CommOrder commOrder : commOrders) {
			commOrder.setIsExpired(isOrderExpired(commOrder) ? Constants.YES : Constants.NO);
		}
		return commOrders;
	}

	/**
	 * 根据聚合支付的订单编号 获取订单
	 */
	public CommOrder getOrderByUnionOrderNo(String unionOrderNo) {
		EntityWrapper<CommOrder> wrapper = new EntityWrapper();
		wrapper.eq("union_order_no", unionOrderNo);
		List<CommOrder> list = mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}

	/**
	 * 根据临时授权码或者openid
	 */
	public String queryOpenIdByCode(String code, String bankId) {
		JSONObject wxJsonObject = bankConfigService.getWxPayConfig(bankId);
		String url = "https://api.weixin.qq.com/sns/oauth2/access_token";
		String params = "appid=" + wxJsonObject.getString("wx_app_id") + "&secret="
				+ wxJsonObject.getString("wx_app_secret") + "&code=" + code + "&grant_type=authorization_code";
		String reslutStr = HttpKit.post(url, params);
		JSONObject jsonObject = JSONObject.fromObject(reslutStr);
		LOGGER.info("获取openID响应:" + jsonObject.toString());
		String openid = BaseUtil.getStringValueFromJson(jsonObject, "openid");
		if (StringUtils.isEmpty(openid)) {
			throw BizException.withMessage("生成openid失败，请重试");
		}
		return openid;
	}

	/**
	 * 订单是否过期
	 */
	public boolean isOrderExpired(CommOrder commOrder) {
		return System.currentTimeMillis() - commOrder.getCreateTime().getTime() > Constants.ORDER_EXPIRED_TIME * 60
				* 1000;
	}

	/**
	 * 计算订单金额
	 * @param category 费用大类
	 * @param ywIds 业务id列表
	 * @return 总费用
	 */
	public BigDecimal computedOrderAmount(FeeCategory category, String[] ywIds) {
		BigDecimal totalAmount = new BigDecimal(0);
		if (category == FeeCategory.COURSE_BM_FEE) {
			List<StudentBmCourse> studentBmCourses = studentBmCourseMapper.selectBatchIds(Arrays.asList(ywIds));
			List<String> courseIds = studentBmCourses.stream().map(StudentBmCourse::getCourseId).collect(Collectors.toList());
			List<Course> courses = courseMapper.selectBatchIds(courseIds);
			for (Course course : courses) {
				totalAmount = totalAmount.add(BigDecimal.valueOf(course.getAmount()));
			}
		} else if (category == FeeCategory.XM_BM_FEE) {
			//项目报名数据
			List<ZypxBm> zypxBms = zypxBmMapper.selectBatchIds(Arrays.asList(ywIds));
			//选择的课程
			List<ZypxBmCourse> zypxBmCourses = zypxBmCourseMapper.selectByBmId(zypxBms.get(0).getId());
			List<String> courseSettingIds = zypxBmCourses.stream().map(ZypxBmCourse::getCourseSettingId).collect(Collectors.toList());

			List<String> xmIds = zypxBms.stream().map(ZypxBm::getXmId).collect(Collectors.toList());
			List<PlanDetail> planDetails = planDetailMapper.selectBatchIds(xmIds);
			for (PlanDetail pd : planDetails) {
				if (pd.getBuyType() == BuyType.COURSE) {
					List<Map<String, Object>> courseSettings = zypxXmCourseSettingMapper.getAllCourseByXmId(pd.getId());
					//选中的课程设置信息
					List<Map<String, Object>> courses = courseSettings.stream().filter(x -> courseSettingIds.contains(x.get("id").toString())).collect(Collectors.toList());
					//设置的金额
					List<BigDecimal> amount = courses.stream().map(x -> (BigDecimal) x.get("amount")).collect(Collectors.toList());
					totalAmount = totalAmount.add(amount.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
				} else if (pd.getBuyType() == BuyType.XM || pd.getBuyType() == null) {
					totalAmount = totalAmount.add(BigDecimal.valueOf(pd.getAmount()));
				}
			}
		}
		return totalAmount;
	}
}
