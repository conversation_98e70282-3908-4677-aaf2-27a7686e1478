package com.xunw.jxjy.model.zypx.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.thoughtworks.xstream.XStream;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.service.QuestionEntityService;
import com.xunw.jxjy.model.utils.AnswerSheetAsposeWordUtil;
import com.xunw.jxjy.model.utils.OfficeToolWord;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamDataMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamMarkTeacherMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.model.zypx.params.ZypxExamPaperQueryParams;
import com.xunw.jxjy.paper.model.*;
import com.xunw.jxjy.paper.utils.ModelHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理端试卷服务
 * <AUTHOR>
 */
@Service
public class ZypxExamPaperService extends BaseCRUDService<ZypxExamPaperMapper, ExamPaper>{

	private static final Logger logger = LoggerFactory.getLogger(ZypxExamPaperService.class);

	@Autowired
	private QuestionEntityService questionEntityService;
	@Autowired
	private ZypxExamDataMapper examDataMapper;
	@Autowired
	private ZypxExamMarkTeacherMapper examMarkTeacherMapper;
	@Autowired
	private ZypxXmService xmService;

	public Page pageQuery(ZypxExamPaperQueryParams params) throws SQLException, IOException {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 试卷配置,手工组卷
	 */
	@Transactional
	public boolean config(JSONObject join) {
		String id = join.getString("id");
		Integer totalScore = join.getInteger("totalScore");
		Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
		join.put("passedScore", passedScore);
		String data = buildPaper(join);
		ExamPaper examPaper = new ExamPaper();
		examPaper.setId(id);
		examPaper.setTotalScore(totalScore);
		examPaper.setPassedScore(passedScore);
		examPaper.setData(data);
		examPaper.setUpdatorId(join.getString("updatorId"));
		examPaper.setUpdateTime(new Date());
		mapper.updateById(examPaper);
		// 清理掉试卷的缓存
		CacheHelper.removeCache("PaperCache", "P" + id);
		return true;
	}

	/**
	 * 组卷
	 */
	private String buildPaper(JSONObject join) {
		String id = join.getString("id");
		ExamPaper examPaper = mapper.selectById(id);
		Paper paper = new Paper();
		String result = "";
		XStream xstream = new XStream();
		Sjlx sjlx = null;
		try {
			// 基本信息
			paper.setId(id);
			paper.setName(examPaper.getName());
			paper.setStatus(examPaper.getStatus());
			paper.setStartTime(examPaper.getStartTime());
			paper.setEndTime(examPaper.getEndTime());
			paper.setDuration(examPaper.getDuration());
			paper.setScoreTime(examPaper.getScoreTime());

			paper.setTotalScore(join.getInteger("totalScore"));
			paper.setPassedScore(join.getInteger("passedScore"));

			paper.setQuesSortType(examPaper.getQuesSortType());
			paper.setPaperType(sjlx = examPaper.getPaperType());
			paper.setRemark(examPaper.getRemark());
			paper.setIsShowAnswer(examPaper.getIsShowAnswer());
			paper.setPaperShowType(examPaper.getPaperShowType());

			JSONArray questionList = join.getJSONArray("questionList");
			//判断是否存在重复题目
			Set<String> allQuestionIdSet = new HashSet<String>();
			Set<String> allQuestionContentSet = new HashSet<String>();
			if (questionList != null && questionList.size() > 0) {
				for (int i = 0; i < questionList.size(); i++) {
					JSONObject jsonObject = (JSONObject) questionList.get(i);
					String sectionId = i + 1 + ""; // 大题序号
					String sectionName = i + 1 + ""; // 大题名
					String sectionRemark = jsonObject.getString("title"); // 大题说明
					/**
					 * 构建章节
					 */
					PaperSection section = new PaperSection(sectionId, sectionName, sectionRemark);
					if (Sjlx.PT == sjlx) {
						// 大题下子题id
						JSONArray question_ids = jsonObject.getJSONArray("questionIds");
						if (question_ids != null && question_ids.size() > 0) {
							Integer stfz = 0;
							for (int j = 0; j < question_ids.size(); j++) {
								String quesId = question_ids.get(j).toString();
								QuestionEntity ques = questionEntityService.selectById(quesId);
								if(allQuestionIdSet.contains(quesId) || allQuestionContentSet.contains(ques.getContent())) {
									throw BizException.withMessage("<p>试题重复：</p>"+ques.getContent()+"<p>被重复添加。</p>");
								}
								allQuestionIdSet.add(quesId);
								allQuestionContentSet.add(ques.getContent());
								stfz = BaseUtil.parseInteger(jsonObject.getString("score"));
								//套题中每一个小题对应的分数
								JSONObject ttScoreJson = jsonObject.getJSONObject("subQuesScore");

								Stlb type = ques.getType();
								if(type.equals(Stlb.TT)) {
									QuestionTt questionTt = new QuestionTt();
									questionTt.setId(quesId);
									questionTt.setType(type);
									questionTt.setScore(stfz);
									questionTt.setContent(ques.getContent());
									List<Question> childrenList = new ArrayList<>();
									EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
									quesWrapper.eq("parent_id", quesId);
									List<QuestionEntity> questionEntityList = questionEntityService.selectList(quesWrapper);
									if (questionEntityList.size() > 0) {
										for (QuestionEntity questionEntity : questionEntityList) {
											if(allQuestionIdSet.contains(questionEntity.getId())) {
												throw BizException.withMessage("<p>试题重复：</p>"+ques.getContent()+"<p>被重复添加。</p>");
											}
											allQuestionIdSet.add(questionEntity.getId());

											Question children = new Question();
											children.setId(questionEntity.getId());
											children.setType(questionEntity.getType());
											Integer childStfz = ttScoreJson.getInteger(questionEntity.getId());
											childStfz = childStfz == null ? 0 : childStfz;
											children.setScore(childStfz);
											children.setContent(questionEntity.getContent());
											childrenList.add(children);
										}
									}
									questionTt.setChildren(childrenList);
									section.addQuestion(questionTt);
								}else {
									Question question = new Question();
									question.setId(quesId);
									question.setType(type);
									question.setScore(stfz);
									question.setContent(ques.getContent());
									question.setAnswer(ques.getAnswer());
									question.setResolve(ques.getResolve());
									section.addQuestion(question);
								}

							}
							section.setRscore(stfz);
						}
						paper.addSection(section);
					}
				}
			}

			result = xstream.toXML(paper);
		}
		catch (BizException exception) {
			throw exception;
		}
		catch (Exception e) {
			logger.error("Error", e);
			throw BizException.withMessage("配置试卷失败，因为："+e);
		}
		return result;
	}

	@Transactional
	public void exportWord(String id, OutputStream os) {
		if (BaseUtil.isEmpty(id)) {
			return;
		}
		Paper paper = this.getPaper(id);
		if (paper == null) {
			throw BizException.withMessage("试卷为空，无法导出");
		}
		try {
			OfficeToolWord.makePaperDoc(os, paper, true);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error", e);
		}
	}
	@Transactional
	public File exportWordA3(String id, OutputStream os) {
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("请传入id");
		}
		Paper paper = this.getPaper(id);
		if (paper == null) {
			throw BizException.withMessage("试卷为空，无法导出");
		}
		File paperFile = null;
		try {

			paperFile = OfficeToolWord.makePaperDocA3(os, paper);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error", e);
		}
		return paperFile;
	}

	public File exportAnswerSheet(String id, OutputStream os) {
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("请传入id");
		}
		Paper paper = this.getPaper(id);
		if (paper == null) {
			throw BizException.withMessage("试卷为空，无法导出");
		}
		File paperFile = null;
		try {
			paperFile = AnswerSheetAsposeWordUtil.makePaperAnswerSheet(paper);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error", e);
		}
		return paperFile;
	}
	/**
	 * 批量克隆
	 */
	@Transactional
	public void batchClonePaper(String ids, String xmId, String professionId, TechLevel techLevel) {
		if (xmService.isSkill(xmId) && (StringUtils.isEmpty(professionId) || techLevel == null)) {
			throw BizException.withMessage("请选择职业及等级");
		}
		for (String id : StringUtils.split(ids, ",")) {
			ExamPaper examPaper = mapper.selectById(id);
			examPaper.setId(BaseUtil.generateId());
			examPaper.setXmId(xmId);
			examPaper.setProfessionId(professionId);
			examPaper.setTechLevel(techLevel);
			examPaper.setStatus(Sjzt.BKY);
			examPaper.setCreateTime(new Date());
			mapper.insert(examPaper);
		}
	}

	/**
	 * 查看试卷属性
	 */
	public Map<String, Object> viewPaper(String paperId) throws SQLException, IOException {
		Map<String, Object> paperInfo = mapper.getPaperInfoById(paperId);
		paperInfo.remove("data");
		List<String> nameList = new ArrayList<String>();
		//查询试卷设置的阅卷老师，多个用“,”隔开
		for(Map<String, Object> map : examMarkTeacherMapper.getAllExamMarkerTeacherName(paperId)) {
			nameList.add(BaseUtil.getStringValueFromMap(map, "name", ""));
		}
		paperInfo.put("teacher_names", String.join("，", nameList));
		return paperInfo;
	}

	/**
	 * 获取试卷信息
	 */
	public Map<String, Object> getDetailsById(String paperId) throws SQLException, IOException {
		Map<String, Object> modelMap = new HashMap<String, Object>();
		Map<String, Object> paperMap = mapper.getPaperInfoById(paperId);
		Paper paper = this.getPaper(paperId);
		modelMap.put("paper", paper);
		modelMap.put("paperMap", paperMap);
		return modelMap;
	}

	/**
	 * 试卷编辑
	 */
	public boolean edit(ExamPaper examPaper) {
		String id = examPaper.getId();
		examPaper.setData(this.reBuilPaperData(examPaper));

		// 清理掉试卷的缓存
		mapper.updateById(examPaper);
		if (StringUtils.isEmpty(examPaper.getProfessionId())) {
			examPaper = mapper.selectById(id);
			examPaper.setProfessionId(null);
			examPaper.setTechLevel(null);
			mapper.updateAllColumnById(examPaper);
		}
		CacheHelper.removeCache("PaperCache", "P" + id);
		return true;
	}

	/**
	 * 重新构建数据库中存在的试卷对象
	 */
	private String reBuilPaperData(ExamPaper examPaper) {
		String result = "";
		String xml = mapper.selectById(examPaper.getId()).getData();
		if (BaseUtil.isEmpty(xml)) {
			return "";
		}
		try {
			Paper paperModel = ModelHelper.convertObject(xml);
			// 处理不等于空情况下的对象模型,空的代表没有生成过，等人工配置时自动创建
			if (examPaper != null && paperModel != null) {
				// 基本信息
				paperModel.setName(examPaper.getName());
				paperModel.setStatus(examPaper.getStatus());
				paperModel.setStartTime(examPaper.getStartTime());
				paperModel.setEndTime(examPaper.getEndTime());
				paperModel.setDuration(examPaper.getDuration());
				paperModel.setScoreTime(examPaper.getScoreTime());
				paperModel.setExamModel(examPaper.getExamModel());
				paperModel.setQuesSortType(examPaper.getQuesSortType());
				paperModel.setPaperType(examPaper.getPaperType());
				paperModel.setRemark(examPaper.getRemark());
				paperModel.setIsShowAnswer(examPaper.getIsShowAnswer());
				paperModel.setPaperShowType(examPaper.getPaperShowType());

				result = ModelHelper.formatObject(paperModel);
			}
		} catch (Exception e) {
			logger.error("Error", e);
		}

		return result;
	}

	/**
	 * 获取试卷的数据对象
	 */
	public Paper getPaper(String paperId) {
		//缓存不存在，从数据库取基本信息
		Paper paper = getBasicPaperFromDatabase(paperId);
//		//数据库中不存在
		if(paper == null){
			return null;
		}

		//根据类型处理
		if (paper.getPaperType().equals(Sjlx.PT)) {
			//普通试卷，需要进行完整的数据填充
			paper = buildNormalPaper(paper);
		} else {
			//随机试卷不需要填充数据
		}
		return paper;
	}

	private Paper getBasicPaperFromDatabase(String sjId){
		ExamPaper examPaper = mapper.selectById(sjId);
		if(examPaper == null){
			 throw BizException.withMessage("试卷不存在");
		}
		String data = examPaper.getData();
		Paper paper = ModelHelper.convertObject(data);
		if (paper != null) {
			paper.setName(examPaper.getName());
		}
		return paper;
	}

	/**
	 * 从数据库试卷对象来构建完整试卷对象
	 * @param
	 * @return
	 */
	private Paper buildNormalPaper(Paper paper){
		List<PaperSection> sections = paper.getSections();
		if (sections != null) {
			Integer rscore = 0;
			for(PaperSection section : sections){
				List<Question> questions = section.getQuestions();
				if (questions != null) {
					//新的试题列表
					List<Question> newQuestions = new ArrayList<Question>();
					for(Question question : questions){
						rscore = question.getScore();
						Stlb stlb = question.getType();
						if(stlb.equals(Stlb.TT)) {
							QuestionTt tt = (QuestionTt)question;
							List<Question> childrens = tt.getChildren();

							//对套题中的小题进行排序
							sortTTChilren(childrens);

							int score = tt.getScore();
							String id = tt.getId();
							tt = (QuestionTt) getQuestion(id);
							if (tt != null) {
	                        	try {
	                        		tt = (QuestionTt)tt.clone();
	    						} catch (Exception e) {
	    							e.printStackTrace();
	    						}
	                        	tt.setScore(score);

	                        	if (childrens != null) {
									List<Question> newChildrens = new ArrayList<Question>();
									for(Question children : childrens){
										int score1 = children.getScore();
										String id1 = children.getId();
										children = getQuestion(id1);
										if (children != null) {
				                        	try {
				                        		children = (Question)children.clone();
				    						} catch (Exception e) {
				    							e.printStackTrace();
				    						}
				                        	children.setScore(score1);
				                        	newChildrens.add(children);
										} else {
											throw BizException.withMessage("组卷需要的试题不存在：id=" + id1);
										}
									}
									tt.setChildren(newChildrens);
								}

								newQuestions.add(tt);
							} else {
								throw BizException.withMessage("组卷需要的试题不存在：id=" + id);
							}
						} else {
							int score = question.getScore();
							String id = question.getId();
							question = getQuestion(id);
							if (question != null) {
	                        	try {
	                            	question = (Question)question.clone();
	    						} catch (Exception e) {
	    							e.printStackTrace();
	    						}
								question.setScore(score);
								newQuestions.add(question);
							} else {
								throw BizException.withMessage("组卷需要的试题不存在：id=" + id);
							}
						}
					}
					section.setRscore(rscore);
					section.setQuestions(newQuestions);
				}
			}
		}
		return paper;
	}

	/**
	 * 对套题中的子题按照录题的顺序排序
	 */
	private void sortTTChilren(List<Question> children) {
		if(children!=null && children.size() > 0) {
			List<String> stdIds = children.stream().map(x->x.getId()).collect(Collectors.toList());
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<QuestionEntity>();
			wrapper.in("id", stdIds);
			wrapper.orderBy("seq_num", true);
			List<QuestionEntity> bizTkglSts = questionEntityService.selectList(wrapper);
			List<Question> newList = new ArrayList<Question>();
			for (QuestionEntity bizTkglSt : bizTkglSts) {
				for (Question question : children) {
					if(bizTkglSt.getId().equals(question.getId())) {
						newList.add(question);
					}
				}
			}
			children.clear();
			children.addAll(newList);
		}
	}

	/**
	 * 获取试题详情
	 */
	public Question getQuestion(String id) {
		Question question = null;
		if (question == null) {
			try {
				Map<String, Object> condition = new HashMap<String, Object>();
				condition.put("id", id);
				Map<String, Object> map = mapper.getQuestion(id);
				if(map == null){
					throw BizException.withMessage("ID=" + id + "的试题不存在");
				}
				String clobToString = (String)map.get("data");
				String questionData = BaseUtil.isNotEmpty(map.get("data")) ? clobToString : "";
				Stlb questionType = Stlb.valueOf((String)map.get("type"));
				question = ModelHelper.convertObject(questionData);
				question.setId(id);
				question.setResolve(BaseUtil.isNotEmpty(map.get("resolve"))?(String)map.get("resolve"):"");
				question.setAnswer(BaseUtil.isNotEmpty(map.get("answer"))?(String)map.get("answer"):"");
				if(Stlb.SINGLECHOICE.equals(questionType)){
					question = (QuestionSingleChoice)question;
				}else if(Stlb.MULTIPLECHOICE.equals(questionType)){
					question = (QuestionMultipleChoice)question;
				}else if(Stlb.JUDGMENT.equals(questionType)){
					question = (QuestionJudgment)question;
				}else if(Stlb.BLANKFILL.equals(questionType)){
					question = (QuestionBlankFill)question;
				}else if(Stlb.ESSAY.equals(questionType)){
					question = (QuestionEssay)question;
				}else if(Stlb.MCJST.equals(questionType)){
					question = (QuestionMcjst)question;
				}else if(Stlb.LST.equals(questionType)){
					question = (QuestionLst)question;
				}else if(Stlb.JDT.equals(questionType)){
					question = (QuestionJdt)question;
				}else if(Stlb.TT.equals(questionType)){
					question = (QuestionTt)question;
				}
			} catch (Exception e) {
			}
		}

		return question;
	}

	@Transactional
	public void setProperty(String ids, String startTime, String endTime, Sjzt status,
			Stplsx quesSortType,String validCode, String scoreTime, Integer duration,
			String isShowAnswer, String isAllowMobile, boolean isExam, String updatorId)
			throws ParseException {
		for (String id : ids.split(",")) {
			// 获取当前试卷对象,进行xml操作
			Paper paper = this.getPaper(id);
			ExamPaper examPaper = new ExamPaper();
			examPaper.setId(id);
			examPaper.setUpdatorId(updatorId);
			examPaper.setUpdateTime(new Date());
			if (BaseUtil.isNotEmpty(startTime)) {
				Date parseKksj = DateUtils.parse(startTime, "yyyy-MM-dd HH:mm");
				examPaper.setStartTime(parseKksj);
				if (paper != null) {
					paper.setStartTime(parseKksj);
				}
			}
			if (BaseUtil.isNotEmpty(endTime)) {
				Date parseJssj = DateUtils.parse(endTime, "yyyy-MM-dd HH:mm");
				examPaper.setEndTime(parseJssj);
				if (paper != null) {
					paper.setEndTime(parseJssj);
				}
			}
			if (status != null) {
				examPaper.setStatus(status);
				if (paper != null) {
					paper.setStatus(status);
				}
			}
			if (quesSortType != null) {
				examPaper.setQuesSortType(quesSortType);
				if (paper != null) {
					paper.setQuesSortType(quesSortType);
				}
			}
			if (BaseUtil.isNotEmpty(isAllowMobile)) {
				examPaper.setIsAllowMobile(isAllowMobile);
				if (paper != null) {
					paper.setIsAllowMobile(isAllowMobile);
				}
			}
			if (StringUtils.isNotEmpty(isShowAnswer)) {
				examPaper.setIsShowAnswer(isShowAnswer);
				if (paper != null) {
					paper.setIsShowAnswer(isShowAnswer);
				}
			}
			//阶段测验、终极考核才有以下数据
			if (isExam) {
				if (StringUtils.isNotEmpty(scoreTime)) {
					Date publishScoreTime = DateUtils.parse(scoreTime, "yyyy-MM-dd HH:mm");
					examPaper.setScoreTime(publishScoreTime);
					if (paper != null) {
						paper.setScoreTime(publishScoreTime);
					}
				}
				if (BaseUtil.isNotEmpty(duration)) {
					examPaper.setDuration(duration);
					if (paper != null) {
						paper.setDuration(duration);
					}
				}
				if (StringUtils.isNotEmpty(validCode)) {
					examPaper.setValidateCode(validCode);
					if (paper != null) {
						paper.setValidateCode(validCode);
					}
				}
			}
			if (paper != null) {
				String data = ModelHelper.formatObject(paper);
				examPaper.setData(data);
			}
			mapper.updateById(examPaper);
			// 清理掉试卷的缓存
			CacheHelper.removeCache("PaperCache", "P" + id);
		}
	}

	// 删除前校验是否有考试记录
	public void deletePaperById(String id) {
		for (String pid : StringUtils.split(id, ",")) {
			ExamPaper examPaper = mapper.selectById(pid);
			if(examPaper == null){
				throw BizException.withMessage("数据不存在");
			}
			EntityWrapper<ExamData> wrapper = new EntityWrapper<>();
			wrapper.eq("paper_id", pid);
			// 如果试卷存在考试记录则不能删除
			if (examDataMapper.selectCount(wrapper) > 0) {
				throw BizException.withMessage("当前"+(examPaper.getCategory() == PaperCategory.PSZY ? "作业" :"测验")+"已被学生使用，请勿删除");
			} else {
				mapper.deleteById(pid);
			}
		}
	}

	@Transactional
	public void plszip(String ids, String ksip, String xgyhId) {
		//优化为批量更新操作，提高效率
		List<String> idList = new ArrayList<>();
		for (String id : StringUtils.split(ids, ",")) {
			idList.add(id);
		}
		List<ExamPaper> sjPapers = mapper.selectBatchIds(idList);
		for (ExamPaper bizZypxSj : sjPapers) {
			bizZypxSj.setIpRange(ksip);
			bizZypxSj.setUpdatorId(xgyhId);
			bizZypxSj.setUpdateTime(new Date());
		}
		DBUtils.updateBatchById(sjPapers, 10, ExamPaper.class);
	}

	/**
	 * 清除考试验证码
	 */
	@Transactional
	public void clearValidCode(String ids, String updatorId) {
		List<String> idList = new ArrayList<>();
		for (String id : StringUtils.split(ids, ",")) {
			idList.add(id);
		}
		List<ExamPaper> list = mapper.selectBatchIds(idList);
		for (ExamPaper examPaper : list) {
			examPaper.setValidateCode("");
			examPaper.setUpdatorId(updatorId);
			examPaper.setUpdateTime(new Date());
		}
		DBUtils.updateBatchById(list, 10, ExamPaper.class);
	}

	/**
	 * @Description 常规试卷的抽题组卷
	 * <AUTHOR>
	 */
	@Transactional
	public void configByChooseQues(JSONObject jsonParam) {
		String id = jsonParam.getString("id");
		Integer totalScore = jsonParam.getInteger("totalScore");
		if (totalScore == null) {
			throw BizException.withMessage("卷面总分不能为空");
		}
		Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
		jsonParam.put("passedScore", passedScore);
		ExamPaper oldExamPaper = mapper.selectById(id);
		Paper paper = new Paper();
		XStream xstream = new XStream();
		// 基本信息
		paper.setId(id);
		paper.setName(oldExamPaper.getName());
		paper.setStatus(oldExamPaper.getStatus());
		paper.setStartTime(oldExamPaper.getStartTime());
		paper.setEndTime(oldExamPaper.getEndTime());
		paper.setDuration(oldExamPaper.getDuration());
		paper.setScoreTime(oldExamPaper.getScoreTime());

		paper.setTotalScore(jsonParam.getInteger("totalScore"));
		paper.setPassedScore(jsonParam.getInteger("passedScore"));

		paper.setQuesSortType(oldExamPaper.getQuesSortType());
		paper.setPaperType(oldExamPaper.getPaperType());
		paper.setRemark(oldExamPaper.getRemark());
		paper.setIsShowAnswer(oldExamPaper.getIsShowAnswer());
		paper.setPaperShowType(oldExamPaper.getPaperShowType());

		JSONArray questionList = jsonParam.getJSONArray("questionList");
		//判断是否存在重复题目
		if (questionList != null && questionList.size() > 0) {
			for (int i = 0; i < questionList.size(); i++) {
				JSONObject jsonObject = (JSONObject) questionList.get(i);
				String sectionId = i + 1 + ""; // 大题序号
				String sectionName = i + 1 + ""; // 大题名
				/**
				 * 构建章节
				 */
				PaperSection section = new PaperSection(sectionId, sectionName, jsonObject.getString("text"));
				section.setTnum(jsonObject.getInteger("count"));//试题数量
				if (section.getTnum() <= 0) {
					continue;
				}
				section.setTrealType(jsonObject.getString("type"));//题型，此处是枚举题型
				section.setTlevel(jsonObject.getString("difficulty"));
				section.setTdbid(jsonObject.getString("dbId"));
				section.setTscore(jsonObject.getInteger("score"));
				paper.addSection(section);
			}
		}
		questionEntityService.buildPaperByChooseQues(paper);
		String data = xstream.toXML(paper);
		ExamPaper examPaper = new ExamPaper();
		examPaper.setId(id);
		examPaper.setTotalScore(totalScore);
		examPaper.setPassedScore(passedScore);
		examPaper.setData(data);
		examPaper.setUpdatorId(jsonParam.getString("updatorId"));
		examPaper.setUpdateTime(new Date());
		mapper.updateById(examPaper);
		// 清理掉试卷的缓存
		CacheHelper.removeCache("PaperCache", "P" + id);
	}

	public void deleteByWrapper(EntityWrapper<ExamPaper> wrapper) {
		mapper.delete(wrapper);
	}

	public Object configFormPractice(JSONObject json) {
		String id = json.getString("id");
		String pId = json.getString("pId");
		JSONArray typeList = json.getJSONArray("typeList");
		//练习
		ExamPaper p = mapper.selectById(pId);
		//终极考核
		ExamPaper examPaper = mapper.selectById(id);
		if (p == null) {
			throw BizException.withMessage("练习不存在");
		}
		if (StringUtils.isEmpty(p.getData())) {
			throw BizException.withMessage("练习尚未组卷");
		}
		if (examPaper.getCategory() != PaperCategory.ZJKH) {
			throw BizException.withMessage("当前试卷不是终极考核");
		}
		Paper paper = ModelHelper.convertObject(p.getData());
		List<Question> questionList = paper.getSections().stream().flatMap(section -> section.getQuestions().stream()).collect(Collectors.toList());
		Map<String, List<Question>> quesGroup = questionList.stream().collect(Collectors.groupingBy(q->q.getType().name()));

		//抽取的题目
		List<Question> qs = new ArrayList<>();
		for (Object o : typeList) {
			JSONObject t = (JSONObject) o;
			String type = t.getString("type");
			Integer num = t.getInteger("num");
			if (num == 0) {
				continue;
			}
			List<Question> questions = quesGroup.get(type);
			if (Stlb.findByEnumName(type) == null) {
				throw BizException.withMessage(type + "类型不正确");
			}
			//总题量
			int totalQuestionNum = quesGroup.get(type).size();
			if (totalQuestionNum < num) {
				throw BizException.withMessage(Stlb.findByEnumName(type).getName() + "类型试题数量不足。");
			}
			for (int i = 0; i < num; i++) {
				Question question = questions.get(new Random().nextInt(totalQuestionNum));
				while (qs.contains(question)) {
					question = questions.get(new Random().nextInt(totalQuestionNum));
				}
				qs.add(question);
			}
		}

		Paper ep = new Paper();
		//基本信息
		ep.setId(id);
		ep.setName(examPaper.getName());
		ep.setStatus(examPaper.getStatus());
		ep.setStartTime(examPaper.getStartTime());
		ep.setEndTime(examPaper.getEndTime());
		ep.setDuration(examPaper.getDuration());
		ep.setScoreTime(examPaper.getScoreTime());
		ep.setQuesSortType(examPaper.getQuesSortType());
		ep.setPaperType(examPaper.getPaperType());
		ep.setRemark(examPaper.getRemark());
		ep.setIsShowAnswer(examPaper.getIsShowAnswer());
		ep.setPaperShowType(examPaper.getPaperShowType());

		List<PaperSection> epSections = new ArrayList<>();
		Set<String> qIdSet = qs.stream().map(Question::getId).collect(Collectors.toSet());
		int sectionIndex = 0;
		for (PaperSection section : paper.getSections()) {
			List<Question> questions = section.getQuestions().stream().filter(q -> qIdSet.contains(q.getId())).collect(Collectors.toList());
			if (!questions.isEmpty()) {
				PaperSection pSection = new PaperSection();
				pSection.setId(String.valueOf(++sectionIndex));
				pSection.setName(section.getName());
				pSection.setRemark(section.getRemark());
				pSection.setQuestions(questions);
				epSections.add(pSection);
			}
		}
		ep.setSections(epSections);
		return ep;
	}

	public Object getTypeCountById(ExamPaper examPaper) {
		Map<String, Object> result = new HashMap<>();
		if (StringUtils.isNotEmpty(examPaper.getData())) {
			Paper paper = ModelHelper.convertObject(examPaper.getData());
			if (CollectionUtils.isNotEmpty(paper.getSections())) {
				List<Question> questions = paper.getSections().stream().flatMap(s -> s.getQuestions().stream()).collect(Collectors.toList());
				Map<Stlb, List<Question>> questionGroup = questions.stream().collect(Collectors.groupingBy(Question::getType));
				result = questionGroup.entrySet().stream().collect(Collectors.toMap(e->e.getKey().name(), e -> e.getValue().size()));
			}
		}
		return result;
	}
}
