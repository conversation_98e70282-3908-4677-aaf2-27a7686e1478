package com.xunw.jxjy.model.inf.entity;

import java.io.Serializable;
import java.util.Date;

import org.omg.CORBA.PRIVATE_MEMBER;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.CertiType;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.FamilyType;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Ksly;
import com.xunw.jxjy.model.enums.Nation;
import com.xunw.jxjy.model.enums.PoliticalType;

/**
 * 学员信息表
 */
@TableName("sys_student_info")
public class StudentInfo implements Serializable {

	private static final long serialVersionUID = -5195825988283306058L;
	
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;

	//学员用户ID
	@TableField("student_id")
	private String studentId;

	//身份证号
	@TableField("sfzh")
	private String sfzh;

	//姓名
	@TableField("name")
	private String name;

	//性别
	@TableField("gender")
	private Gender gender;

	//民族
	@TableField("nation")
	private Nation nation;
	
	//政治面貌
	@TableField("political_type")
	private PoliticalType politicalType;
	
	//户籍类型
	@TableField("family_type")
	private FamilyType familyType;
	
	//手机号
	@TableField("mobile")
	private String mobile;
	
	//身份证地址
	@TableField("certi_address")
	private String certiAddress;
	
	//籍贯-省
	@TableField("certi_province")
	private String certiProvince;
	
	//籍贯-市
	@TableField("certi_city")
	private String certiCity;
	
	//通讯地址
	@TableField("address")
	private String address;
	
	//邮编
	@TableField("post_code")
	private String postCode;
	
	//职业
	@TableField("profession")
	private String profession;
	
	//职务
	@TableField("zw")
	private String zw;
	
	//职称 见constants 常量定义
	@TableField("zc")
	private String zc;
	
	//学历
	@TableField("education")
	private Education education;

	//学历证书
	@TableField("edu_certi_photo")
	private String eduCertiPhoto;
	
	//学历证书编号
	@TableField("edu_certi_number")
	private String eduCertiNumber;
	
	//毕业学校,若为在读学生，则存储的是就读学校
	@TableField("graduate_school")
	private String graduateSchool;
	
	//所在院系
	@TableField("college")
	private String college;
	
	//所在班级
	@TableField("classz")
	private String classz;
	
	//学号
	@TableField("student_num")
	private String studentNum;
	
	//学生证 图片
	@TableField("student_ticket")
	private String studentTicket;
	
	//入学时间
	@TableField("school_time")
	private Date schoolTime;
	
	//辅导员
	@TableField("instructor")
	private String instructor;
	
	//所学专业
	@TableField("specialty")
	private String specialty;
	
	//是否已毕业   1是 0 否
	@TableField("is_graduated")
	private String isGraduated;
	
	//毕业证书号
	@TableField("graduate_num")
	private String graduateNum;
	
	//毕业时间
	@TableField("graduate_time")
	private Date graduateTime;
	
	//个人工作简历
	@TableField("resume")
	private String resume;
		
	//身份证正面--国徽面
	@TableField("sfzzm")
	private String sfzzm;
	
	//身份证反面--人像面
	@TableField("sfzfm")
	private String sfzfm;
	
	//学员在个人中心上传的登记照片
	@TableField("student_photo")
	private String studentPhoto;
	
	//创建时间
	@TableField("create_time")
	private Date createTime;
	
	//考生来源
	@TableField("ksly")
	private Ksly ksly;
	
	//座机电话
	@TableField("phone")
	private String phone;
	//邮箱
	@TableField("email")
	private String email;
	//qq
	@TableField("qq")
	private String qq;
	//微信
	@TableField("wxh")
	private String wxh;
	//出生日期
	@TableField("birthday")
	private String birthday;
	
	//发票抬头
	@TableField("invoice_title")
	private String invoiceTitle;

	//纳税人识别号
	@TableField("invoice_code")
	private String invoiceCode;
	
	//发票-开户行
	@TableField("invoice_bank")
	private String invoiceBank;
	
	//发票-银行账号
	@TableField("invoice_bank_account")
	private String invoiceBankAccount;
		
	//发票-单位地址
	@TableField("invoice_org_address")
	private String invoiceOrgAddress;
	
	//发票-单位电话
	@TableField("invoice_org_phone")
	private String invoiceOrgPhone;
	
	//单位所在地-省份code编码
	@TableField("company_province_code")
	private String companyProvinceCode;

	//单位所在地-市(县)级code编码
	@TableField("company_city_code")
	private String companyCityCode;

	//单位所在地-县code编码
	@TableField("company_district_code")
	private String companyDistrictCode;
	
	//现在的技能等级
	@TableField("current_tech_level")
	private String currentTechLevel;

	//岗位
	@TableField("gw")
	private  String gw;
	
	//工种
	@TableField("gz")
	private  String gz;
	
	//学徒性质
	@TableField("xtxz")
	private String xtxz;
	
	//学员到职日期
	@TableField("dzrq")
	private String dzrq;
	
	//培养目标
	@TableField("pymb")
	private String pymb;
	
	//备注
	@TableField("remark")
	private String remark;
	
	//导师姓名
	@TableField("teacher_name")
	private String teacherName;
	
	//导师岗位
	@TableField("teacher_gw")
	private String teacherGw;
	
	//导师身份证号
	@TableField("teacher_certino")
	private String teacherCertino;
	
	//证件类型
	@TableField("certi_type")
	private CertiType certiType;

	//单位地址
	@TableField("org_address")
	private String orgAddress;
	
	//注册主办单位ID
	@TableField("reg_host_org_id")
	private String regHostOrgId;

	public String getEduCertiPhoto() {
		return eduCertiPhoto;
	}

	public void setEduCertiPhoto(String eduCertiPhoto) {
		this.eduCertiPhoto = eduCertiPhoto;
	}

	public String getCurrentTechLevel() {
		return currentTechLevel;
	}

	public void setCurrentTechLevel(String currentTechLevel) {
		this.currentTechLevel = currentTechLevel;
	}

	public String getGw() {
		return gw;
	}

	public String getResume() {
		return resume;
	}

	public void setResume(String resume) {
		this.resume = resume;
	}

	public void setGw(String gw) {
		this.gw = gw;
	}

	public String getGz() {
		return gz;
	}

	public void setGz(String gz) {
		this.gz = gz;
	}

	public String getXtxz() {
		return xtxz;
	}

	public void setXtxz(String xtxz) {
		this.xtxz = xtxz;
	}

	public String getDzrq() {
		return dzrq;
	}

	public void setDzrq(String dzrq) {
		this.dzrq = dzrq;
	}

	public String getPymb() {
		return pymb;
	}

	public void setPymb(String pymb) {
		this.pymb = pymb;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getTeacherGw() {
		return teacherGw;
	}

	public void setTeacherGw(String teacherGw) {
		this.teacherGw = teacherGw;
	}

	public String getTeacherCertino() {
		return teacherCertino;
	}

	public void setTeacherCertino(String teacherCertino) {
		this.teacherCertino = teacherCertino;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSfzh() {
		return sfzh;
	}

	public void setSfzh(String sfzh) {
		this.sfzh = sfzh;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public Nation getNation() {
		return nation;
	}

	public void setNation(Nation nation) {
		this.nation = nation;
	}

	public PoliticalType getPoliticalType() {
		return politicalType;
	}

	public void setPoliticalType(PoliticalType politicalType) {
		this.politicalType = politicalType;
	}

	public FamilyType getFamilyType() {
		return familyType;
	}

	public void setFamilyType(FamilyType familyType) {
		this.familyType = familyType;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getCertiAddress() {
		return certiAddress;
	}

	public void setCertiAddress(String certiAddress) {
		this.certiAddress = certiAddress;
	}

	public String getCertiProvince() {
		return certiProvince;
	}

	public String getInstructor() {
		return instructor;
	}

	public void setInstructor(String instructor) {
		this.instructor = instructor;
	}

	public void setCertiProvince(String certiProvince) {
		this.certiProvince = certiProvince;
	}

	public String getCertiCity() {
		return certiCity;
	}

	public void setCertiCity(String certiCity) {
		this.certiCity = certiCity;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getProfession() {
		return profession;
	}

	public void setProfession(String profession) {
		this.profession = profession;
	}

	public String getZw() {
		return zw;
	}

	public void setZw(String zw) {
		this.zw = zw;
	}

	public String getZc() {
		return zc;
	}

	public void setZc(String zc) {
		this.zc = zc;
	}

	public Education getEducation() {
		return education;
	}

	public void setEducation(Education education) {
		this.education = education;
	}

	public String getSpecialty() {
		return specialty;
	}

	public void setSpecialty(String specialty) {
		this.specialty = specialty;
	}

	public String getIsGraduated() {
		return isGraduated;
	}

	public void setIsGraduated(String isGraduated) {
		this.isGraduated = isGraduated;
	}

	public String getGraduateNum() {
		return graduateNum;
	}

	public void setGraduateNum(String graduateNum) {
		this.graduateNum = graduateNum;
	}

	public String getGraduateSchool() {
		return graduateSchool;
	}

	public void setGraduateSchool(String graduateSchool) {
		this.graduateSchool = graduateSchool;
	}

	public Date getGraduateTime() {
		return graduateTime;
	}

	public void setGraduateTime(Date graduateTime) {
		this.graduateTime = graduateTime;
	}

	public String getSfzzm() {
		return sfzzm;
	}

	public void setSfzzm(String sfzzm) {
		this.sfzzm = sfzzm;
	}

	public String getSfzfm() {
		return sfzfm;
	}

	public void setSfzfm(String sfzfm) {
		this.sfzfm = sfzfm;
	}

	public String getStudentPhoto() {
		return studentPhoto;
	}

	public void setStudentPhoto(String studentPhoto) {
		this.studentPhoto = studentPhoto;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getSchoolTime() {
		return schoolTime;
	}

	public void setSchoolTime(Date schoolTime) {
		this.schoolTime = schoolTime;
	}

	public String getStudentNum() {
		return studentNum;
	}

	public void setStudentNum(String studentNum) {
		this.studentNum = studentNum;
	}

	public Ksly getKsly() {
		return ksly;
	}

	public void setKsly(Ksly ksly) {
		this.ksly = ksly;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getWxh() {
		return wxh;
	}

	public void setWxh(String wxh) {
		this.wxh = wxh;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getInvoiceCode() {
		return invoiceCode;
	}

	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}

	public String getInvoiceBank() {
		return invoiceBank;
	}

	public void setInvoiceBank(String invoiceBank) {
		this.invoiceBank = invoiceBank;
	}

	public String getInvoiceBankAccount() {
		return invoiceBankAccount;
	}

	public void setInvoiceBankAccount(String invoiceBankAccount) {
		this.invoiceBankAccount = invoiceBankAccount;
	}

	public String getInvoiceOrgAddress() {
		return invoiceOrgAddress;
	}

	public void setInvoiceOrgAddress(String invoiceOrgAddress) {
		this.invoiceOrgAddress = invoiceOrgAddress;
	}

	public String getInvoiceOrgPhone() {
		return invoiceOrgPhone;
	}

	public void setInvoiceOrgPhone(String invoiceOrgPhone) {
		this.invoiceOrgPhone = invoiceOrgPhone;
	}

	public String getCollege() {
		return college;
	}

	public void setCollege(String college) {
		this.college = college;
	}

	public String getClassz() {
		return classz;
	}

	public void setClassz(String classz) {
		this.classz = classz;
	}

	public String getCompanyProvinceCode() {
		return companyProvinceCode;
	}

	public void setCompanyProvinceCode(String companyProvinceCode) {
		this.companyProvinceCode = companyProvinceCode;
	}

	public String getCompanyCityCode() {
		return companyCityCode;
	}

	public void setCompanyCityCode(String companyCityCode) {
		this.companyCityCode = companyCityCode;
	}

	public String getCompanyDistrictCode() {
		return companyDistrictCode;
	}

	public void setCompanyDistrictCode(String companyDistrictCode) {
		this.companyDistrictCode = companyDistrictCode;
	}

	public String getStudentTicket() {
		return studentTicket;
	}

	public void setStudentTicket(String studentTicket) {
		this.studentTicket = studentTicket;
	}

	public CertiType getCertiType() {
		return certiType;
	}

	public void setCertiType(CertiType certiType) {
		this.certiType = certiType;
	}

	public String getEduCertiNumber() {
		return eduCertiNumber;
	}

	public void setEduCertiNumber(String eduCertiNumber) {
		this.eduCertiNumber = eduCertiNumber;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getOrgAddress() {
		return orgAddress;
	}

	public void setOrgAddress(String orgAddress) {
		this.orgAddress = orgAddress;
	}

	public String getRegHostOrgId() {
		return regHostOrgId;
	}

	public void setRegHostOrgId(String regHostOrgId) {
		this.regHostOrgId = regHostOrgId;
	}
}
