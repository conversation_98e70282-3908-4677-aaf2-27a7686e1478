package com.xunw.jxjy.model.inf.service;

import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.inf.entity.Courseware;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.model.zypx.service.ZypxLiveService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.model.ConvertAnchor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.qiniu.pili.PiliException;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.QiniuZbConstants;
import com.xunw.jxjy.common.utils.QiniuZhiboUtils;
import com.xunw.jxjy.model.common.courselive.Chapter;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.courselive.Lesson;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Zbzt;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.CourseLive;
import com.xunw.jxjy.model.inf.mapper.CourseLiveMapper;
import com.xunw.jxjy.model.inf.params.CourseLiveQueryParams;
import com.xunw.jxjy.model.learning.entity.Live;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.service.UserService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.mapper.ZypxLiveMapper;
import com.xunw.jxjy.model.zypx.params.ZypxLiveQueryParams;
import com.xunw.jxjy.paper.utils.ModelHelper;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 直播课 服务
 *
 * <AUTHOR>
 */
@Service
public class CourseLiveService extends BaseCRUDService<CourseLiveMapper, CourseLive>{

	@Autowired
	private ZypxLiveMapper liveMapper;
	@Autowired
	private CoursewareService coursewareService;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private ZypxBmService bmService;

	public Page pageQuery(CourseLiveQueryParams params) {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		// 计算直播大字段一共有多少章多少节
		for (Map<String, Object> map : list) {
			Set<String> teacherIds = new HashSet<String>();
			int chapterCount = 0;
			int lessonCount = 0;
			if (BaseUtil.isNotEmpty(map.get("content"))) {
				Courselive courselive = ModelHelper.convertObject((String) map.get("content"));
				if (courselive.getChapters() != null) {
					chapterCount = courselive.getChapters().size();
					for (Chapter chapter : courselive.getChapters()) {
						if (chapter.getLessons() != null) {
							lessonCount += chapter.getLessons().size();
							for (Lesson lesson : chapter.getLessons()) {
								teacherIds.add(lesson.getJsid());
							}
						}
					}
				}
			}
			map.put("chapter_lesson_count", "共" + chapterCount + "章，共" + lessonCount + "课时");
			if (teacherIds.size()>0) {
				List<User> users = userMapper.selectBatchIds(teacherIds);
				Set<String> teacherNames = users.stream().map(x -> x.getName()).collect(Collectors.toSet());
				map.put("teacher_names", StringUtils.join(teacherNames, ","));
			}
			if (BaseUtil.isNotEmpty(map.get("content"))){
				Courselive courselive = ModelHelper.convertObject(map.get("content").toString());
				courselive.getChapters().stream().flatMap(x->x.getLessons().stream()).forEach(a ->{
					if (StringUtils.isEmpty(a.getJsxm()) && StringUtils.isNotEmpty(a.getJsid())) {
						User teacher = userMapper.selectById(a.getJsid());
						a.setJsxm(teacher != null ? teacher.getName() : null);
					}
				});
				map.put("courselive",courselive);
			}
		}
		params.setRecords(list);
		return params;
	}

	@Transactional(rollbackFor = Throwable.class)
	public void saveCourseLive(JSONObject coureLiveContent, String userId) throws ParseException, PiliException {
		String id = BaseUtil.getStringValueFromJson(coureLiveContent, "id");
		id = StringUtils.isEmpty(id) ? BaseUtil.generateId2() : id;
		String courseId = BaseUtil.getStringValueFromJson(coureLiveContent, "courseId");
		//一门课程只能够设置一个直播课
		CourseLive check = getByCourseId(courseId);
		if (check!= null && !check.getId().equals(id)) {
			throw BizException.withMessage("保存直播失败，因为当前课程已经设置过直播课了，不可重复设置");
		}
		String introduce = BaseUtil.getStringValueFromJson(coureLiveContent, "introduce");
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("保存直播失败，课程ID不能为空");
		}
		if (!coureLiveContent.containsKey("chapters")) {
			throw BizException.withMessage("保存直播失败，章节信息不能为空");
		}
		JSONArray chaperArray = coureLiveContent.getJSONArray("chapters");
		if (chaperArray == null || chaperArray.size() == 0) {
			throw BizException.withMessage("保存直播失败，章节信息不能为空");
		}
		String isPublic = BaseUtil.getStringValueFromJson(coureLiveContent, "isPublic");
		String isPublicForm = BaseUtil.getStringValueFromJson(coureLiveContent, "isPublicForm", Constants.YES);
		String isTest=BaseUtil.getStringValueFromJson(coureLiveContent, "isTest");
		Courselive courselive = new Courselive();
		courselive.setId(id);
		courselive.setKcid(courseId);
		List<Lesson> allLessons = new ArrayList<Lesson>();
		Set<String> teacherIdList = new HashSet<String>();
		for (int i = 0; i < chaperArray.size(); i++) {
			JSONObject chapterObj = chaperArray.getJSONObject(i);
			String chapterId = BaseUtil.getStringValueFromJson(chapterObj, "id");
			chapterId = StringUtils.isEmpty(chapterId) ? BaseUtil.generateId2() : chapterId;
			String chapterName = BaseUtil.getStringValueFromJson(chapterObj, "name");
			Chapter chapter = new Chapter();
			chapter.setId(chapterId);
			chapter.setName(chapterName);
			courselive.addChapter(chapter);
			// 保存课时
			if (!chapterObj.containsKey("lessons")) {
				throw BizException.withMessage("保存直播失败，因为第" + (i + 1) + "章课时信息为空");
			}
			JSONArray lessonArray = chapterObj.getJSONArray("lessons");
			if (lessonArray == null || lessonArray.size() == 0) {
				throw BizException.withMessage("保存直播失败，因为第" + (i + 1) + "章课时信息为空");
			} else {
				for (int j = 0; j < lessonArray.size(); j++) {
					JSONObject lessonObj = lessonArray.getJSONObject(j);
					String lessonId = BaseUtil.getStringValueFromJson(lessonObj, "id");
					lessonId = StringUtils.isEmpty(lessonId) ? BaseUtil.generateId2() : lessonId;
					String lessonName = BaseUtil.getStringValueFromJson(lessonObj, "zbbt");
					String startTimeStrValue = BaseUtil.getStringValueFromJson(lessonObj, "kssj");
					Date startTime = DateUtils.parse(startTimeStrValue, "yyyy-MM-dd HH:mm:ss");
					String endTimeStrValue = BaseUtil.getStringValueFromJson(lessonObj, "jssj");
					Date endTime = DateUtils.parse(endTimeStrValue, "yyyy-MM-dd HH:mm:ss");
					if (startTime.after(endTime)) {
						throw BizException.withMessage("第" + (i + 1) + "章，第" + (j + 1) + "节的开始时间不能够晚于结束时间");
					}
					String teacherId = BaseUtil.getStringValueFromJson(lessonObj, "jsid");
					String teacherName = BaseUtil.getStringValueFromJson(lessonObj, "jsxm");
					String teacherPhone = BaseUtil.getStringValueFromJson(lessonObj, "jssjh");

					String remark = BaseUtil.getStringValueFromJson(lessonObj, "zbjj");
					String address = BaseUtil.getStringValueFromJson(lessonObj, "address");

					Lesson lesson = new Lesson();
					lesson.setId(lessonId);
					lesson.setZbbt(lessonName);
					lesson.setKcid(courseId);
					lesson.setKssj(startTime);
					lesson.setJssj(endTime);
					lesson.setJsid(teacherId);
					lesson.setJsxm(teacherName);
					lesson.setJssjh(teacherPhone);
					lesson.setZbjj(remark);
					lesson.setAddress(address);
					chapter.addLesson(lesson);
					allLessons.add(lesson);
					teacherIdList.add(teacherId);
				}
			}
		}
		// 编辑课程直播时，如果有直播频道被删除了，需要判断是否有学员学习，有学习记录的就不能够删除
		if (BaseUtil.isNotEmpty(BaseUtil.getStringValueFromJson(coureLiveContent, "id"))) {
			CourseLive courseLive = mapper.selectById(id);
			Courselive checkCourselive = ModelHelper.convertObject(courseLive.getContent());
			List<String> newLessonIds = allLessons.stream().map(x -> x.getId()).collect(Collectors.toList());
			for (Chapter chapter : checkCourselive.getChapters()) {
				for (Lesson lesson : chapter.getLessons()) {
					// 如果原有课时被删除了，需要校验
					if ((!newLessonIds.contains(lesson.getId()))) {
						if (!Constants.YES.equals(courseLive.getIsTest())){
							if(mapper.getLearningCountByLiveId(lesson.getId()) > 0) {
								throw BizException.withMessage("操作失败，因为直播【" + lesson.getZbbt() + "】已经有学员学习了，不能修改");
							}
						}
						liveMapper.deleteById(lesson.getId());
					}
				}
			}
		}
		// 写入到公共直播表
		this.addLive(allLessons, userId, id);
		String xml = ModelHelper.formatObject(courselive);
		String teacherIds = StringUtils.join(teacherIdList, ",");
		if (BaseUtil.isEmpty(BaseUtil.getStringValueFromJson(coureLiveContent, "id"))) {
			CourseLive courseLive = new CourseLive();
			courseLive.setId(id);
			courseLive.setCourseId(courseId);
			courseLive.setIsPublic(isPublic);
			courseLive.setIsTest(isTest);
			courseLive.setContent(xml);
			courseLive.setCreateTime(new Date());
			courseLive.setCreatorId(userId);
			courseLive.setTeacherIds(teacherIds);
			courseLive.setIntroduce(introduce);
			courseLive.setIsPublicForm(isPublicForm);
			mapper.insert(courseLive);
		} else {
			CourseLive courseLive = mapper.selectById(id);
			courseLive.setCourseId(courseId);
			courseLive.setContent(xml);
			courseLive.setIsPublic(isPublic);
			courseLive.setIsTest(isTest);
			courseLive.setTeacherIds(teacherIds);
			courseLive.setUpdateTime(new Date());
			courseLive.setUpdatorId(userId);
			courseLive.setIntroduce(introduce);
			courseLive.setIsPublicForm(isPublicForm);
			mapper.updateById(courseLive);
		}
//		if (Constants.YES.equals(isPublic)) {
//			bmService.autoBmWhenStudyPublicCourse(courseId, id);
//		}
	}

	/**
	 * 创建直播频道,已经存在的频道不会重新创建，但是会更新直播的信息
	 */
	public void addLive(List<Lesson> lessons, String userId, String courseLiveId) throws PiliException {
		if (lessons.size() > 0) {
			List<Live> insertLessons = new ArrayList<Live>();
			List<Live> updateLessons = new ArrayList<Live>();
			for (Lesson lesson : lessons) {
				Live live = liveMapper.selectById(lesson.getId());
				if (live != null) {
					updateLessons.add(live);
					live.setXgyhId(userId);
					live.setXgsj(new Date());
				} else {
					live = new Live();
					live.setId(lesson.getId());
					live.setCjyhId(userId);
					live.setCjsj(new Date());
					live.setZbzt(Zbzt.READY);
					insertLessons.add(live);
					String baseName = "ZYPX" + live.getId().toUpperCase().replace("-", "")
							+ DateUtils.getCurrentDate("yyyyMMddHHmmss");
					String docStreamName = baseName + "A";// 主流名称
					String teacherStreamName = baseName + "B";// 教师流名称
					QiniuZhiboUtils.createStream(docStreamName);
					QiniuZhiboUtils.createStream(teacherStreamName);

					// 主流-生成直播地址以及推流地址
					live.setZbtldza(QiniuZhiboUtils.getRTMPPublishURL(docStreamName));
					live.setTldzgqsj(new Date(System.currentTimeMillis() + QiniuZbConstants.QINIU_TUILIU_GQSC * 1000));
					live.setRtmpdza(QiniuZhiboUtils.getRTMPPlayURL(docStreamName));
					live.setFlvdza(QiniuZhiboUtils.getFLVPlayURL(docStreamName));
					live.setHlsdza(QiniuZhiboUtils.getHLSPlayURL(docStreamName));
					live.setZblmca(docStreamName);

					// 直播教师流-生成直播地址以及推流地址
					live.setZbtldzb(QiniuZhiboUtils.getRTMPPublishURL(teacherStreamName));
					live.setRtmpdzb(QiniuZhiboUtils.getRTMPPlayURL(teacherStreamName));
					live.setFlvdzb(QiniuZhiboUtils.getFLVPlayURL(teacherStreamName));
					live.setHlsdzb(QiniuZhiboUtils.getHLSPlayURL(teacherStreamName));
					live.setZblmcb(teacherStreamName);
				}
				live.setJsId(lesson.getJsid());
				live.setZbbt(lesson.getZbbt());
				live.setZbjj(lesson.getZbjj());
				live.setKssj(lesson.getKssj());
				live.setJssj(lesson.getJssj());
				live.setCourseLiveId(courseLiveId);
				if (StringUtils.isEmpty(lesson.getKcid())) {
					throw BizException.withMessage("课程ID不能够为空");
				}
				live.setKcId(lesson.getKcid());
			}
			if (insertLessons.size() > 0) {
				DBUtils.insertBatch(insertLessons, Live.class);
			}
			if (updateLessons.size() > 0) {
				DBUtils.updateBatchById(updateLessons, Live.class);
			}
		}
	}

	/**
	 * 删除直播
	 */
	@Transactional
	public void deleteById(String id) {
		CourseLive courseLive = mapper.selectById(id);
		Courselive checkCourselive = ModelHelper.convertObject(courseLive.getContent());
		List<String> liveIds = new ArrayList<String>();
		for (Chapter chapter : checkCourselive.getChapters()) {
			for (Lesson lesson : chapter.getLessons()) {
				liveIds.add(lesson.getId());
				if (!Constants.YES.equals(courseLive.getIsTest())){
					if (mapper.getLearningCountByLiveId(lesson.getId()) > 0) {// 如果章节被删除了，需要校验
						throw BizException.withMessage("操作失败，因为课时：" + lesson.getZbbt() + "已经有学员学习了，不能删除");
					}
				}
			}
		}
		// 先删除直播频道
		liveMapper.deleteBatchIds(liveIds);
		mapper.deleteById(id);
	}

	/**
	 * 导出直播，采用课件的导入模板，导出后可以直接导入成课件
	 */
	public void addressExport(CourseLiveQueryParams params, OutputStream os) throws Exception {
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = pageQuery(params);
		List<Map<String, Object>> list = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("已结束直播地址表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "章名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "课时名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "课时介绍", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 30);
			ws.addCell(new Label(i, row, "课件类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "课时长度", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "课件地址", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 100);
		}
		row = 1;
		for (Map<String, Object> map : list) {
			if (map == null) {
				continue;
			} else {
				if (BaseUtil.isEmpty(map.get("content").toString())) {
					continue;
				}
				// 解析大字段
				Courselive courselive = ModelHelper.convertObject(map.get("content").toString());
				for (Chapter chapter : courselive.getChapters()) {
					String zhangjie = chapter.getName();
					List<String> zbidList = new ArrayList<>();
					for (Lesson lesson : chapter.getLessons()) {
						zbidList.add(lesson.getId());
					}
					List<Live> liveList = liveMapper.selectBatchIds(zbidList);
					for (Live live : liveList) {
						if (live.getZbzt() != Zbzt.FINISHED) {
							continue;
						}
						Double shichang = null;
						if (live.getHkspsc() != null) {
							shichang = Math.ceil(live.getHkspsc() / 60);
						}
						int col = 0;
						ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(zhangjie),
								OfficeToolExcel.getNormolCell()));
						ws.addCell(new Label(col++, row,
								BaseUtil.convertNullToEmpty(live.getZbbt() != null ? live.getZbbt() : ""),
								OfficeToolExcel.getNormolCell()));
						ws.addCell(new Label(col++, row,
								BaseUtil.convertNullToEmpty(live.getZbjj() != null ? live.getZbjj() : ""),
								OfficeToolExcel.getNormolCell()));
						ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty("av"),
								OfficeToolExcel.getNormolCell()));
						ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(shichang),
								OfficeToolExcel.getNormolCell()));
						ws.addCell(new Label(col++, row,
								BaseUtil.convertNullToEmpty(live.getHkdza() != null ? live.getHkdza() : ""),
								OfficeToolExcel.getNormolCell()));
						row++;
					}
				}
			}
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	public CourseLive getByCourseId(String courseId) {
		EntityWrapper<CourseLive> wrapper = new EntityWrapper();
		wrapper.eq("course_id", courseId);
		List<CourseLive> courseLives = selectList(wrapper);
		return courseLives.size() > 0 ? courseLives.get(0) : null;
	}

	/**
	 * 信息填充
	 */
	public void fillInfo(Courselive courselive) {
		if (courselive!= null && courselive.getChapters()!=null) {
			for (Chapter chapter : courselive.getChapters()) {
				if (chapter.getLessons()!=null) {
					for (Lesson lesson : chapter.getLessons()) {
						if (StringUtils.isNotEmpty(lesson.getJsid())) {
							User teacher = userMapper.selectById(lesson.getJsid());
							lesson.setJsxm(teacher != null ? teacher.getName() : null);
							lesson.setJssjh(teacher != null ? teacher.getMobile() : null);
						}
					}
				}
			}
		}
	}

	/**
	 * 直播转课件
	 */
	@Transactional
	public void convert(String ids,String creatorId) {
		if (StringUtils.isEmpty(ids)) {
			throw  BizException.withMessage("请选择直播");
		}
		String[] idArray = StringUtils.split(ids, ",");
		if (idArray == null || idArray.length == 0) {
			throw  BizException.withMessage("请选择直播");
		}
		
		for (String id : idArray) {
			CourseLive courseLive = mapper.selectById(id);
			String content = courseLive.getContent();
			Courselive courselive = ModelHelper.convertObject(content);
			//是否已经转过课件判断
			List<Courseware> coursewares = coursewareService.selectList((EntityWrapper<Courseware>) new EntityWrapper<Courseware>()
					.eq("course_live_id",id));
			if (CollectionUtils.isNotEmpty(coursewares)){
				throw BizException.withMessage("直播回放已经转换为课件，请勿重复操作");
			}
			//构造课件的JSON对象
			JSONObject courselearnJson = new JSONObject();
			courselearnJson.put("courseId", courseLive.getCourseId());
			String teacherName = "佚名";
			if (StringUtils.isNoneEmpty(courseLive.getTeacherIds())) {
				EntityWrapper<User> wrapper = new EntityWrapper();
				wrapper.in("id", StringUtils.split(courseLive.getTeacherIds(), ","));
				List<User> users = userMapper.selectList(wrapper);
				Set<String> teacherNames = users.stream().map(x->x.getName()).collect(Collectors.toSet());
				if (teacherNames != null && teacherNames.size() > 0) {
					teacherName = StringUtils.join(teacherNames, ",");
				}
			}
			courselearnJson.put("teacherName", teacherName);
			courselearnJson.put("status", Zt.BLOCK);//转换的课件设置为禁用状态
			JSONArray chapters = new JSONArray();
			for(Chapter chapter : courselive.getChapters()) {
				JSONObject chapterJson = new JSONObject();
				chapterJson.put("name", chapter.getName());
				JSONArray lessons = new JSONArray();
				for (Lesson lesson : chapter.getLessons()) {
					String liveId = lesson.getId();//判断直播回放是否已经生成
					Live live = liveMapper.selectById(liveId);
					if (live.getZbzt() != Zbzt.FINISHED || live.getHkspsc() == null || live.getHkspsc() < 1) {
						throw BizException.withMessage("直播回放尚未生成，课时：" + lesson.getZbbt());
					}
					JSONObject lessonObj = new JSONObject();
					lessonObj.put("lesson_name", lesson.getZbbt());
					lessonObj.put("lesson_remark", lesson.getZbbt());
					lessonObj.put("lesson_filetype", "av");
					lessonObj.put("teacher", teacherName);
					lessonObj.put("explain_incisively", "0");
					lessonObj.put("lesson_minutes", Math.floor(live.getHkspsc() / 60));
					lessonObj.put("lesson_filepath", live.getHkdza());
					lessons.add(lessonObj);
				}
				chapterJson.put("lessons", lessons);
				chapters.add(chapterJson);
			}
			courselearnJson.put("chapters", chapters);
			courselearnJson.put("isLive", Constants.YES);
			coursewareService.add(courselearnJson, id, creatorId);
		}
	}
	
	public Page getLiveViewRecord(CourseLiveQueryParams params){
		params.setRecords(mapper.getLiveViewRecord(params.getCondition(), params));
		return params;
	}

	/**
	 * 导出直播观看记录
	 * @param params 查询参数
	 * @param os 输出流
	 * @throws Exception
	 */
    public void exportViewRecord(CourseLiveQueryParams params, OutputStream os) throws Exception {
		params.setSize(Integer.MAX_VALUE);
		List<Map<String, Object>> liveViewRecord = mapper.getLiveViewRecord(params.getCondition(), params);
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("直播观看记录", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 30);
			ws.addCell(new Label(i, row, "所在单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 30);
			ws.addCell(new Label(i, row, "直播观看时长", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "录播观看时长", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "观看总时长", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
		}
		row = 1;
		for (Map<String, Object> map : liveViewRecord) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"name")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"sfzh")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"mobile")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"company")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"watchLiveDuration")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"watchReplayDuration")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map,"sumWatchDuration")),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
    }
}
