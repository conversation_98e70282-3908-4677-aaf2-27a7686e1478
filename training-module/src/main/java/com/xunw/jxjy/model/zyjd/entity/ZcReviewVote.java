package com.xunw.jxjy.model.zyjd.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 评审打分投票表
 */
@TableName(value = "BIZ_REVIEW_VOTE")
public class ZcReviewVote {
    /**
     * 主键id
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 报名id
     */
    @TableField(value = "BM_ID")
    private String bmId;

    /**
     * 投票老师id
     */
    @TableField(value = "USER_ID")
    private String userId;

    /**
     * 成绩：A、B、C
     */
    @TableField(value = "GRADE")
    private String grade;

    /**
     * 获取主键id
     *
     * @return ID - 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取报名id
     *
     * @return BM_ID - 报名id
     */
    public String getBmId() {
        return bmId;
    }

    /**
     * 设置报名id
     *
     * @param bmId 报名id
     */
    public void setBmId(String bmId) {
        this.bmId = bmId;
    }

    /**
     * 获取投票老师id
     *
     * @return USER_ID - 投票老师id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置投票老师id
     *
     * @param userId 投票老师id
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取成绩：A、B、C
     *
     * @return GRADE - 成绩：A、B、C
     */
    public String getGrade() {
        return grade;
    }

    /**
     * 设置成绩：A、B、C
     *
     * @param grade 成绩：A、B、C
     */
    public void setGrade(String grade) {
        this.grade = grade;
    }
}