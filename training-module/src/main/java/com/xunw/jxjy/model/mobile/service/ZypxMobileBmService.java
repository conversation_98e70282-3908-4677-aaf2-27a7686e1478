package com.xunw.jxjy.model.mobile.service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpSession;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.MD5Util;
import com.xunw.jxjy.model.dto.StudentFormDataDto;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Nation;
import com.xunw.jxjy.model.enums.RequiredType;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.enums.SysSettingEnum;
import com.xunw.jxjy.model.inf.dto.TextValuePair;
import com.xunw.jxjy.model.inf.entity.FormData;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.personal.mapper.StudentBmMapper;
import com.xunw.jxjy.model.personal.params.StudentBmQueryParams;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmBaseService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmArrive;
import com.xunw.jxjy.model.zypx.mapper.ZypxScoreMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmArriveMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;

import net.sf.json.JSONObject;

@Service
public class ZypxMobileBmService extends ZypxStudentBmBaseService {

	@Autowired
	private ZypxXmMapper zypxXmMapper;
	@Autowired
	private StudentBmMapper studentBmMapper;
	@Autowired
	private ZypxXmArriveMapper xmArriveMapper;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private ZypxScoreMapper scoreMapper;
	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private StudentUserService studentUserService;

	
	/**
	 * 查询学员已经报名的培训项目
	 */
	public List<Map<String, Object>> getStudentBmXmList(StudentBmQueryParams params) {
		if (StringUtils.isEmpty(params.getStudentId())) {
			throw BizException.withMessage("请传入考生ID");
		}
		List<Map<String, Object>> list = super.getStudentBmXmList(params);
		for (Map<String, Object> map : list) {
			if (BaseUtil.isEmpty(map.get("arriveRange"))) {
				// 设置允许签到的误差距离
				String distance = systemSettingService.getGlobalSetting(SysSettingEnum.ONLINE_ARRIVE_ALLOW_DISTANCE);
				map.put("arrive_range", distance);
			}
			String xmId = map.get("xmId").toString();
			ZypxXmArrive xmArrive = xmArriveMapper.selectByStudentId(xmId, params.getStudentId());
			map.put("is_arrive", xmArrive != null ? 1 : 0);

			HashMap<String, Object> arriveSetting = new HashMap<>();
			arriveSetting.put("poiaddress", map.get("curpoiaddress"));
			arriveSetting.put("poilatlng", map.get("curpoilatlng"));
			arriveSetting.put("arriveRange", map.get("arriveRange"));
			map.remove("curpoiaddress");
			map.remove("curpoilatlng");
			map.remove("arriveRange");
			map.put("arrive_setting", arriveSetting);
			map.put("sign", MD5Util.encode(MD5Util.encode(BaseUtil.getDoubleValueFromMap(map, "amount").toString())));
		}
		return list;
	}

	/**
	 * 查询学员已经报名的课程信息以及学习进度、最近学习时间
	 */
	public Map<String, Object> getStudentBmCourseDetails(StudentBmQueryParams params) throws ParseException {
		if (StringUtils.isEmpty(params.getXmId())) {
			throw BizException.withMessage("请传入项目ID");
		}
		if (StringUtils.isEmpty(params.getStudentId())) {
			throw BizException.withMessage("请传入考生ID");
		}
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map<String, Object>> bmCourseList = super.getStudentBmCourseList(params.getXmId(), params.getStudentId());
		// 获取每一门课程的最后学习时间
		for (Map<String, Object> bmCourseMap : bmCourseList) {
			if (BaseUtil.isEmpty(bmCourseMap.get("arriveRange"))) {
				// 设置系统默认的允许签到的误差距离
				String distance = systemSettingService.getGlobalSetting(SysSettingEnum.ONLINE_ARRIVE_ALLOW_DISTANCE);
				bmCourseMap.put("arrive_range", distance);
			}
			String courseId = BaseUtil.getStringValueFromMap(bmCourseMap, "courseId");
			if (bmCourseMap.get("isCourseware") != null
					&& Constants.YES.equals(bmCourseMap.get("isCourseware").toString())) {
				Date studyEndTime = studentBmMapper.getCoursewareStudyLatestEndTime(params.getXmId(), courseId,
						params.getStudentId());
				bmCourseMap.put("kjxx_study_end_time", studyEndTime);
			}
			if (bmCourseMap.get("isLive") != null && Constants.YES.equals(bmCourseMap.get("isLive").toString())) {
				String liveContent = BaseUtil.getStringValueFromMap(bmCourseMap, "liveContent");
				if (StringUtils.isNotEmpty(liveContent)) {
					String xmId = BaseUtil.getStringValueFromMap(bmCourseMap, "xmId");
					Date studyEndTime = studentBmMapper.getLiveStudyLatestEndTime(xmId, courseId,
							params.getStudentId());
					bmCourseMap.put("live_study_end_time", studyEndTime);
				}
			}
		}
		result.put("xm", zypxXmMapper.selectById(params.getXmId()));
		result.put("bmCourseList", bmCourseList);// 已报名的课程
		result.put("score", scoreMapper.getTrainingScore(params.getXmId(), params.getStudentId()));// 培训成绩
		return result;
	}

	/**
	 * 查询职业培训可报名的项目
	 */
	public List<Map<String, Object>> getChooseXmList(StudentBmQueryParams params) {
		List<Map<String, Object>> list = super.getChooseXmList(params);
		Iterator<Map<String, Object>> iterator = list.iterator();
		while (iterator.hasNext()) {
			Map<String, Object> map = iterator.next();
			// 计算培训项目的总学时
			String xmId = BaseUtil.getStringValueFromMap(map, "id");
			if (StringUtils.isNotEmpty(params.getStudentId())) {
				EntityWrapper<ZypxBm> wrapper = new EntityWrapper<ZypxBm>();
				wrapper.eq("xm_id", xmId);
				wrapper.eq("student_id", params.getStudentId());
				List<ZypxBm> zypxBms = super.selectList(wrapper);
				if (zypxBms.size() > 0) {
					ZypxBm zypxBm = zypxBms.get(0);
					map.put("bm_id", zypxBm.getId());
					map.put("is_payed", zypxBm.getIsPayed());
				}
			}
		}
		return list;
	}

	/**
	 * 移动端自定义表单提交
	 */
	@Transactional
	public String saveAnswerForm(JSONObject json, String studentId, HttpSession session) throws Exception {
		String xmId = BaseUtil.getStringValueFromJson(json, "xmId");
		String formId = BaseUtil.getStringValueFromJson(json, "formId");
		//2024-9-3 项目报名增加免注册，表单提交自动注册：考虑必须有手机号、身份证信息
		StudentUser studentUser = new StudentUser();
		StudentInfo studentInfo = new StudentInfo();
		boolean isExemptRegister = false;
		if (BaseUtil.isNotEmpty(studentId)) {
			studentUser = studentUserMapper.selectById(studentId);
			studentInfo = studentInfoMapper.getByStudentId(studentId);
		} else {
			isExemptRegister = true;
			studentId = BaseUtil.generateId2();
		}
		List<Map<String, Object>> fieldList = zypxXmMapper.getFormField(xmId, formId);
		List<FormData> formDatas = new ArrayList<FormData>();
		// 获取表单上的学员类型控件的值，该控件可能在表单中不存在
		List<Map<String, Object>> studentTypeList = fieldList.stream()
				.filter(x -> Constants.YES.equals(BaseUtil.getStringValueFromMap(x, "isConstant"))
						&& Constants.SystemField.STUDENT_TYPE
								.equals(BaseUtil.getStringValueFromMap(x, "submitName")))
				.collect(Collectors.toList());
		String idForStudentType = studentTypeList.size() > 0 ? studentTypeList.get(0).get("id").toString() : null;
		String studentType = idForStudentType != null ? BaseUtil.getStringValueFromJson(json, idForStudentType) : null;
		if (StringUtils.isNotEmpty(studentType)) {
			TextValuePair studentTypTtextValuePairs = com.alibaba.fastjson.JSONObject.parseObject(studentType,
					TextValuePair.class);
			studentType = studentTypTtextValuePairs.getValue();
		}
		for (Map<String, Object> map : fieldList) {
			String parameterName = BaseUtil.getStringValueFromMap(map, "id");
			String submitName = BaseUtil.getStringValueFromMap(map, "submitName");
			String isConstant = BaseUtil.getStringValueFromMap(map, "isConstant");
			String fieldName = BaseUtil.getStringValueFromMap(map, "name");
			String requiredType = BaseUtil.getStringValueFromMap(map, "requiredType");

			String inputValuePair = BaseUtil.getStringValueFromJson(json, parameterName);
			TextValuePair textValuePairs = null;
			if (StringUtils.isNotEmpty(inputValuePair)) {
				textValuePairs = com.alibaba.fastjson.JSONObject.parseObject(inputValuePair, TextValuePair.class);
			} else {
				textValuePairs = new TextValuePair();
			}

			RequiredType fieldRequiredType = RequiredType.findByEnumName(requiredType);

			// 判断字段是否是必填项
			if (textValuePairs == null || StringUtils.isEmpty(textValuePairs.getValue())) {
				if (RequiredType.ALL == fieldRequiredType) {// 全部类型的学员必填
					throw BizException.withMessage("缺失必填项:" + fieldName);
				} else if (RequiredType.SCHOOL == fieldRequiredType) {// 在校学员必填
					if (StringUtils.isEmpty(studentType)) {
						throw BizException.withMessage("未选择学员类型");
					}
					if (RequiredType.SCHOOL.name().equals(studentType)) {
						throw BizException.withMessage("在校生必需填写:" + fieldName);
					}
				} else if (RequiredType.SOCIAL == fieldRequiredType) {// 社会在职学员必填
					if (StringUtils.isEmpty(studentType)) {
						throw BizException.withMessage("未选择学员类型");
					}
					if (RequiredType.SOCIAL.name().equals(studentType)) {
						throw BizException.withMessage("在职人员必需填写:" + fieldName);
					}
				}
			}
			
			FormData formData = new FormData();
			formData.setId(BaseUtil.generateId());
			formData.setStudentId(studentId);
			formData.setXmId(xmId);
			formData.setFormId(BaseUtil.getStringValueFromMap(map, "formId"));
			formData.setFieldId(parameterName);

			// base64转图片
			if (StringUtils.isNotEmpty(textValuePairs.getValue())
					&& textValuePairs.getValue().indexOf(";base64,") > -1) {
				textValuePairs.setValue(FileHelper.convertImgBase64SrcStrToUrl(textValuePairs.getValue()));
			}
			formData.setFieldValue(textValuePairs.getValue());
			formData.setFieldText(textValuePairs.getText());

			// 系统预设字段处理
			if (Constants.YES.equals(isConstant)) {
				switch (submitName) {
				case Constants.SystemField.STUDENT_TYPE:
					studentUser.setStudentType(StudentType.findByEnumName(formData.getFieldValue()));
					break;
				case Constants.SystemField.NAME:
					studentInfo.setName(formData.getFieldValue());
					break;
				case Constants.SystemField.GENDER:
					studentInfo.setGender(Gender.findByEnumName(formData.getFieldValue()));
					break;
				case Constants.SystemField.NATION:
					studentInfo.setNation(Nation.findByEnumName(formData.getFieldValue()));
					break;
				case Constants.SystemField.SFZH:
					studentInfo.setSfzh(formData.getFieldValue());
					if (studentInfo.getSfzh()!=null && !BaseUtil.isIDCardNumber(studentInfo.getSfzh())) {
						throw BizException.withMessage("请输入正确的身份证号");
					}
					break;
				case Constants.SystemField.EDUCATION:
					studentInfo.setEducation(Education.findByEnumName(formData.getFieldValue()));
					break;
				case Constants.SystemField.ADDRESS:
					studentInfo.setAddress(formData.getFieldValue());
					break;
				case Constants.SystemField.POST_CODE:
					studentInfo.setPostCode(formData.getFieldValue());
					break;
				case Constants.SystemField.MOBILE:
					studentInfo.setMobile(formData.getFieldValue());
					if (studentInfo.getMobile()!=null && !BaseUtil.isMobile(studentInfo.getMobile())) {
						throw BizException.withMessage("请输入正确的11位手机号");
					}
					break;
				case Constants.SystemField.SFZZM:
					studentInfo.setSfzzm(formData.getFieldValue());
					break;
				case Constants.SystemField.SFZFM:
					studentInfo.setSfzfm(formData.getFieldValue());
					break;
				case Constants.SystemField.STUDENT_PHOTO:
					studentInfo.setStudentPhoto(formData.getFieldValue());
					break;
				case Constants.SystemField.EDU_CERTI_PHOTO:
					studentInfo.setEduCertiPhoto(formData.getFieldValue());
					break;
				case Constants.SystemField.COMPANY:
					studentUser.setCompany(formData.getFieldValue());
					break;
				case Constants.SystemField.GRADUATE_SCHOOL:
					studentInfo.setGraduateSchool(formData.getFieldValue());
					break;
				case Constants.SystemField.COLLEGE:
					studentInfo.setCollege(formData.getFieldValue());
					break;
				case Constants.SystemField.SPECIALTY:
					studentInfo.setSpecialty(formData.getFieldValue());
					break;
				case Constants.SystemField.CLASSZ:
					studentInfo.setClassz(formData.getFieldValue());
					break;
				case Constants.SystemField.STUDENT_TICKET:
					studentInfo.setStudentTicket(formData.getFieldValue());
					break;
				case Constants.SystemField.STUDENT_NUMBER:
					studentInfo.setStudentNum(formData.getFieldValue());
					break;
				default:
					break;
				}
			}
			formDatas.add(formData);
		}
		
		// 学生注册信息入库
		if (isExemptRegister) {
			ZypxXm zypxXm = zypxXmMapper.selectById(xmId);
			String hostOrgId = zypxXm.getHostOrgId();
			String sfzh = studentInfo.getSfzh();
			String mobile = studentInfo.getMobile();
			// 修改密码
			String account = "********";
			if (BaseUtil.isNotEmpty(sfzh)) {
				account = sfzh;
			} else if (BaseUtil.isNotEmpty(mobile)) {
				account = mobile;
			}
			String password = Constants.DEFAULT_PASSWORD_PREFIX + account.substring(account.length() - 6);
			studentUser.setPassword(DigestUtils.md5Hex(password));

			// 报名的信息能找到用户信息
			StudentUser existStudentUser = studentUserService.findBySfzhOrMobile(sfzh, mobile, hostOrgId);
			// 存在的修改信息
			if (existStudentUser != null) {
				studentId = existStudentUser.getId();
				existStudentUser.setStudentType(studentUser.getStudentType());
				existStudentUser.setCompany(studentUser.getCompany());
				if (studentUserMapper.updateById(existStudentUser) == 1) {
					StudentInfo existsStudentInfo = studentInfoMapper.getByStudentId(studentId);
					existsStudentInfo.setName(studentInfo.getName());
					existsStudentInfo.setGender(studentInfo.getGender());
					existsStudentInfo.setNation(studentInfo.getNation());
					// 身份证不等于当前则查询身份证是否被使用
					if (!sfzh.equals(existsStudentInfo.getSfzh())) {
						if (studentUserService.findBySfzh(sfzh, hostOrgId) != null) {
							throw BizException.withMessage("当前身份证号已被使用！");
						}
					}
					existsStudentInfo.setSfzh(sfzh);
					existsStudentInfo.setEducation(studentInfo.getEducation());
					existsStudentInfo.setAddress(studentInfo.getAddress());
					existsStudentInfo.setPostCode(studentInfo.getPostCode());
					// 手机号不等于当前则查询手机号是否被使用
					if (!mobile.equals(existsStudentInfo.getMobile())) {
						if (studentUserService.findByMobile(mobile, hostOrgId) != null) {
							throw BizException.withMessage("当前手机号已被使用！");
						}
					}
					existsStudentInfo.setMobile(mobile);
					existsStudentInfo.setSfzzm(studentInfo.getSfzzm());
					existsStudentInfo.setSfzfm(studentInfo.getSfzfm());
					existsStudentInfo.setStudentPhoto(studentInfo.getStudentPhoto());
					existsStudentInfo.setEduCertiPhoto(studentInfo.getEduCertiPhoto());
					existsStudentInfo.setGraduateSchool(studentInfo.getGraduateSchool());
					existsStudentInfo.setCollege(studentInfo.getCollege());
					existsStudentInfo.setSpecialty(studentInfo.getSpecialty());
					existsStudentInfo.setClassz(studentInfo.getClassz());
					existsStudentInfo.setStudentTicket(studentInfo.getStudentTicket());
					existsStudentInfo.setStudentNum(studentInfo.getStudentNum());
					studentInfoMapper.updateById(existsStudentInfo);
					// 修改表单中studentId
					for (FormData formData : formDatas) {
						formData.setStudentId(studentId);
					}
				}
			} else {// 不存在的新增信息
				// 补全学生用户信息
				studentUser.setId(studentId);
				studentUser.setStatus(AccountStatus.OK);
				studentUser.setIsBindMobile(BaseUtil.isNotEmpty(mobile) ? Constants.YES : Constants.NO);
				studentUser.setRegHostOrgId(hostOrgId);
				if (studentUserMapper.insert(studentUser) == 1) {
					studentInfo.setId(BaseUtil.generateId2());
					studentInfo.setStudentId(studentId);
					studentInfo.setRegHostOrgId(hostOrgId);
					studentInfoMapper.insert(studentInfo);
				}
			}
		}

		StudentFormDataDto studentFormDataDto = new StudentFormDataDto();
		studentFormDataDto.setXmId(xmId);
		studentFormDataDto.setStudentId(studentId);
		studentFormDataDto.setFormId(formId);
		studentFormDataDto.setFormDatas(formDatas);
		studentFormDataDto.setStudentInfo(studentInfo);
		studentFormDataDto.setStudentUser(studentUser);
		String key = "FORMDATA_" + xmId + formId + studentId;
		session.setAttribute(key, studentFormDataDto);
		return isExemptRegister ? studentId : null;
	}
	
}