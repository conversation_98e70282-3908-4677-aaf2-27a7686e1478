package com.xunw.jxjy.model.tk.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBAssign;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.mapper.QuestionDB2CourseEntityMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionDBAssignMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionDBEntityMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper;
import com.xunw.jxjy.model.tk.params.QuestionDBQueryParams;
import com.xunw.jxjy.model.utils.OfficeToolWord;
import com.xunw.jxjy.model.zypx.service.PaperRepoService;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionTt;
import com.xunw.jxjy.paper.utils.ModelHelper;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QuestionDBService extends BaseCRUDService<QuestionDBEntityMapper, QuestionDBEntity>{
	
	@Autowired
	private CourseMapper courseMapper;
	@Autowired
	private QuestionEntityMapper questionEntityMapper;
	@Autowired
	private QuestionDB2CourseEntityMapper question2CourseEntityMapper;
	@Autowired
	private QuestionDBAssignMapper questionAssignMapper;
	@Autowired
	private PaperRepoService paperRepoService;
	
	public Page pageQuery(QuestionDBQueryParams params) {
    	List<Map<String,Object>> list = mapper.list(params.getCondition(), params);
    	params.setRecords(list);
		return params;
	}
	
	public Page pageQueryByAdmin(QuestionDBQueryParams params) {
    	List<Map<String,Object>> list = mapper.listByAdmin(params.getCondition(), params);
    	params.setRecords(list);
		return params;
	}
	
	
	@Transactional
	public void add(JSONObject json,String creatorId){
		String name = BaseUtil.getStringValueFromJson(json,"name");
		String courseId = BaseUtil.getStringValueFromJson(json,"courseId");
		String status = BaseUtil.getStringValueFromJson(json,"status");
		String logo = BaseUtil.getStringValueFromJson(json,"logo");
		String remark = BaseUtil.getStringValueFromJson(json,"remark");
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入题库名称");
		}
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("请选择课程");
		}
		if (StringUtils.isEmpty(status)) {
			throw BizException.withMessage("请选择状态");
		}
		String id = BaseUtil.generateId();
		Course course = courseMapper.selectById(courseId);
		QuestionDBEntity qdb = new QuestionDBEntity(id, name, logo, remark, Zt.findByEnumName(status));
		qdb.setHostOrgId(course.getHostOrgId());
		qdb.setCreatorId(creatorId);
		qdb.setCreateTime(new Date());
		insert(qdb);
		QuestionDB2CourseEntity q2c = new QuestionDB2CourseEntity();
		String q2cId = BaseUtil.generateId();
		q2c.setId(q2cId);
		q2c.setDbId(qdb.getId());
		q2c.setCourseId(courseId);
		question2CourseEntityMapper.insert(q2c);
	}

	public void edit(JSONObject json,String updatorId, String hostOrgId){
		String id = BaseUtil.getStringValueFromJson(json,"id");//课程题库关系表ID
		if(StringUtils.isEmpty(id)){
			throw BizException.withMessage("请选择一条数据");
		}
		String name = BaseUtil.getStringValueFromJson(json,"name");
		String courseId = BaseUtil.getStringValueFromJson(json,"courseId");
		String status = BaseUtil.getStringValueFromJson(json,"status");
		String logo = BaseUtil.getStringValueFromJson(json,"logo");
		String remark = BaseUtil.getStringValueFromJson(json,"remark");
		QuestionDB2CourseEntity question2CourseEntity = question2CourseEntityMapper.selectById(id);
		QuestionDBEntity questionDBEntity = mapper.selectById(question2CourseEntity.getDbId());
		if (StringUtils.isNotEmpty(hostOrgId) && hostOrgId.equals(questionDBEntity.getHostOrgId())) {
			questionDBEntity.setName(name);
			questionDBEntity.setLogo(logo);
			questionDBEntity.setStatus(Zt.findByEnumName(status));
			questionDBEntity.setRemark(remark);
			questionDBEntity.setUpdateTime(new Date());
			questionDBEntity.setUpdatorId(updatorId);
			updateById(questionDBEntity);
		}
		question2CourseEntity.setCourseId(courseId);
		question2CourseEntityMapper.updateById(question2CourseEntity);
	}

	@Transactional
	public void batchDelete(String ids, String hostOrgId) {
		for(String id : StringUtils.split(ids,",")) {
			QuestionDB2CourseEntity q2cEntity = question2CourseEntityMapper.selectById(id);
			QuestionDBEntity questionDBEntity = mapper.selectById(q2cEntity.getDbId());
			if (hostOrgId.equals(questionDBEntity.getHostOrgId())) {
				//若题库下存在试题则不能够删除
				EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<QuestionEntity>();
				wrapper.eq("db_id", q2cEntity.getDbId());
				if (questionEntityMapper.selectCount(wrapper) > 0) {
					throw BizException.withMessage("【" + questionDBEntity.getName() + "】中有试题，无法删除");
				}
				//已经授权给其他机构使用的不要删除题库,只删除关联关系
				EntityWrapper<QuestionDBAssign> assignWrapper = new EntityWrapper();
				assignWrapper.eq("db_id", questionDBEntity.getId());
				if (questionAssignMapper.selectCount(assignWrapper) < 1) {
					mapper.deleteById(q2cEntity.getDbId());
				}
			}
			question2CourseEntityMapper.deleteById(id);
		}
	}
	
	public Map<Stlb, List<Question>> viewQues(String dbId) throws SQLException, IOException {
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
		wrapper.eq("db_id", dbId);
		List<Question> questions = new ArrayList<Question>();
		List<QuestionEntity> list = questionEntityMapper.selectList(wrapper);
		for(QuestionEntity questionEntity:  list){
			String questionData = questionEntity.getData();
			if(Stlb.BLANKFILL == questionEntity.getType()) { //如果是填空题
				questionData = BaseUtil.FormatBlankQuestions(questionData, "____");
			}
			questions.add((Question) ModelHelper.convertObject(questionData));
		}
		questions.sort(Comparator.comparing(x->Integer.parseInt(x.getType().getId())));
		return questions.stream().collect(Collectors.groupingBy(Question::getType));
	}

	/**
	 * 题库授权,多个主办单位的ID使用逗号分开
	 */
	@Transactional
	public void assign(String dbIds, String hostOrgIds, boolean isBatch) {
		String[] dbAaray = org.apache.commons.lang3.StringUtils.split(dbIds, ",");
		if (dbAaray == null || dbAaray.length == 0) {
			throw BizException.withMessage("请选择题库");
		}
		String[] hostOrgIdArray = org.apache.commons.lang3.StringUtils.split(hostOrgIds, ",");
		List<QuestionDBAssign> questionAssigns = new ArrayList<QuestionDBAssign>();
		if (isBatch) {
			for (String dbId : dbAaray) {
				QuestionDBEntity questionDBEntity = mapper.selectById(dbId);
				for (String hid : hostOrgIdArray) {
					EntityWrapper<QuestionDBAssign> wrapper = new EntityWrapper();
					wrapper.eq("db_id", dbId);
					wrapper.eq("host_org_id", hid);
					if (questionAssignMapper.selectCount(wrapper) > 0) {
						continue;//已经授权的无须重复授权
					}
					if (hid.equals(questionDBEntity.getHostOrgId())) {//主办单位自己的题库不需要授权
						continue;
					}
					QuestionDBAssign questionAssign = new QuestionDBAssign();
					questionAssign.setId(BaseUtil.generateId2());
					questionAssign.setHostOrgId(hid);
					questionAssign.setDbId(dbId);
					questionAssign.setTime(new Date());
					questionAssigns.add(questionAssign);
				}
			}
			DBUtils.insertBatch(questionAssigns, QuestionDBAssign.class);
		}
		else {
			//删除之前的授权关系
			EntityWrapper<QuestionDBAssign> wrapper = new EntityWrapper();
			wrapper.in("db_id", dbAaray);
			questionAssignMapper.delete(wrapper);
			for (String dbId : dbAaray) {
				QuestionDBEntity questionDBEntity = mapper.selectById(dbId);
				for (String hid : hostOrgIdArray) {
					if (hid.equals(questionDBEntity.getHostOrgId())) {//主办单位自己的题库不需要授权
						continue;
					}
					QuestionDBAssign questionAssign = new QuestionDBAssign();
					questionAssign.setId(BaseUtil.generateId2());
					questionAssign.setHostOrgId(hid);
					questionAssign.setDbId(dbId);
					questionAssign.setTime(new Date());
					questionAssigns.add(questionAssign);
				}
			}
			DBUtils.insertBatch(questionAssigns, QuestionDBAssign.class);
		}
		
	}

	public List<String> getAssign(String dbId) {
		EntityWrapper<QuestionDBAssign> wrapper = new EntityWrapper();
		wrapper.eq("db_id", dbId);
		List<String> hostOrgIds = questionAssignMapper.selectList(wrapper).stream().map(x->x.getHostOrgId()).
				collect(Collectors.toList());
		return hostOrgIds;
	}

	public List<Map<String, Object>> getAssignedDbList(String assignedHostOrgId){
		return mapper.getAssignedDbList(assignedHostOrgId);
	}
	
	@Transactional
	public void setCourse(String q2cId,String dbId, String courseId,String hostOrgId) {
		if (StringUtils.isEmpty(q2cId)) {
			//一个题库只能绑定当前主办单位下的一门课程
    		if (mapper.getDBBindedCount(dbId, hostOrgId) > 0) {
				throw BizException.withMessage("选择的题库已经绑定其他课程，不可重复绑定");
			}
			QuestionDB2CourseEntity questionDB2CourseEntity = new QuestionDB2CourseEntity();
			questionDB2CourseEntity.setId(BaseUtil.generateId2());
			questionDB2CourseEntity.setCourseId(courseId);
			questionDB2CourseEntity.setDbId(dbId);
			question2CourseEntityMapper.insert(questionDB2CourseEntity);
		}
		else {
			QuestionDB2CourseEntity questionDB2CourseEntity = question2CourseEntityMapper.selectById(q2cId);
			questionDB2CourseEntity.setDbId(dbId);
			questionDB2CourseEntity.setCourseId(courseId);
			question2CourseEntityMapper.updateById(questionDB2CourseEntity);
		}
	}
	
	/**
	 *根据courseId获取题库
	 */
	public List<QuestionDBEntity> getQuestionDBEntity(String courseId){
		return mapper.getQuestionDBByCourseId(courseId);
	}
	
	/**
	 * 查询题库详情
	 */
	public Map<String, Object> getDbDetailsByQ2cId(@Param("q2cId") String q2cId){
		return mapper.getDbDetailsByQ2cId(q2cId);
	}


	@Transactional
	public void adminBatchDelete(String ids) {
		for(String id : StringUtils.split(ids,",")) {
			QuestionDBEntity questionDBEntity = mapper.selectById(id);
			if (StringUtils.isNotEmpty(questionDBEntity.getHostOrgId())) {
				throw BizException.withMessage("操作失败，您没有权限编辑"+questionDBEntity.getName()+",此题库不属于您所在的机构");
			}
			EntityWrapper<QuestionDBAssign> asgWrapper = new EntityWrapper<>();
			asgWrapper.eq("db_id",id);
			if(questionAssignMapper.selectCount(asgWrapper) > 0){
				throw BizException.withMessage("【"+questionDBEntity.getName()+"】已授权给其他机构，无法删除");
			}
			//若题库下存在试题则不能够删除
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<QuestionEntity>();
			wrapper.eq("db_id", id);
			if (questionEntityMapper.selectCount(wrapper) > 0) {
				throw BizException.withMessage("【"+questionDBEntity.getName()+"】中有试题，无法删除");
			}
			mapper.deleteById(id);
		}
	}
	
	/**
	 * 根据课程查询题库
	 */
	public List<QuestionDBEntity> getQuestionDBByCourseId(String courseId){
		return mapper.getQuestionDBByCourseId(courseId);
	}
	
	/**
	 * 题库导出word
	 * @throws Exception 
	 */
	public void exportWord(String dbId, OutputStream os) throws Exception {
		EntityWrapper<QuestionEntity> qEntityWrapper = new EntityWrapper();
		qEntityWrapper.eq("db_id", dbId);
		qEntityWrapper.eq("status", Zt.OK);
		List<QuestionEntity> list = questionEntityMapper.selectList(qEntityWrapper);
		Map<String, List<QuestionEntity>> grouQuestionMap = list.stream()
				.collect(Collectors.groupingBy(q -> String.valueOf(q.getRealType())));
		// 临时组卷
		Paper paper = new Paper();
		String pid = BaseUtil.generateId();
		String name = mapper.selectById(dbId).getName()+"(共" + list.size() + "道试题)";
		paper.setId(pid);
		paper.setName(name);
		paper.setQuesSortType(Stplsx.ZC);
		int sectionId = 0;
		for (String quesionType : grouQuestionMap.keySet()) {
			PaperSection section = new PaperSection(String.valueOf(++sectionId), quesionType, quesionType);
			List<QuestionEntity> questionList = grouQuestionMap.get(quesionType);
			if (BaseUtil.isNotEmpty(questionList)) {
				for (QuestionEntity questionMap : questionList) {
					String question_id = (String) questionMap.getId();
					String p_question_cnt = (String) questionMap.getContent();
					Stlb q_type = questionMap.getType();
					if (Stlb.TT.equals(q_type)) {
						QuestionTt questionTt = new QuestionTt();
						questionTt.setId(question_id);
						questionTt.setType(q_type);
						questionTt.setScore(0);
						questionTt.setContent(p_question_cnt);
						List<Question> childrens = new ArrayList<>();
						EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
						wrapper.eq("parent_id", question_id);
						wrapper.eq("status", Zt.OK);
						wrapper.orderBy("seq_num", true);
						List<QuestionEntity> childList = questionEntityMapper.selectList(wrapper);
						for (QuestionEntity childrenMap : childList) {
							String children_id = String.valueOf(childrenMap.getId());
							Stlb children_type = childrenMap.getType();
							String p_children_cnt = String.valueOf(childrenMap.getContent());
							Question children = new Question();
							children.setId(children_id);
							children.setType(children_type);
							children.setContent(p_children_cnt);
							childrens.add(children);
						}
						questionTt.setChildren(childrens);
						section.addQuestion(questionTt);
					} else {
						Question question = new Question();
						question.setId(question_id);
						question.setType(q_type);
						question.setScore(0);
						question.setContent(p_question_cnt);
						section.addQuestion(question);
					}
				}
				paper.addSection(section);
			}
			paper.setTotalScore(0);
			paper.setPassedScore(0);
		}
		paper = paperRepoService.buildNormalPaper(paper);
		OfficeToolWord.makePaperDoc(os, paper, false);
	}
}
