package com.xunw.jxjy.model.inf.mapper;

import com.baomidou.mybatisplus.mapper.Wrapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.jxjy.model.inf.entity.StudentInfo;

import java.util.List;
import java.util.Map;

public interface StudentInfoMapper extends BaseMapper<StudentInfo>{

	StudentInfo getByStudentId(@Param("studentId") String studentId);

	StudentInfo findBysfzh(@Param("sfzh") String sfzh, @Param("hostOrgId") String hostOrgId);

	List<Map<String, Object>> getZcpsBmList(@Param("groupId") String groupId);

	List<Map<String, Object>> getFinishedStudent(@Param("groupId") String groupId);

	/**
	 * 查询用户下面的订单数量
	 */
	List<Map<String, Object>> queryOrderByStudentIds(List<String> studentIds);

    List<StudentInfo> getStudent(@Param("xmId") String xmId, @Param("orgId") String orgId);

	List<StudentInfo> getByStudentIds(@Param("studentIds") List<String> studentIds);
}
