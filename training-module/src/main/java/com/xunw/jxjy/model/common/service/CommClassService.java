package com.xunw.jxjy.model.common.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.common.entity.CommClass;
import com.xunw.jxjy.model.common.mapper.CommClassMapper;
import com.xunw.jxjy.model.common.params.CommClassQueryParams;
import com.xunw.jxjy.model.core.BaseCRUDService;

/**
 * <AUTHOR>
 */
@Service
public class CommClassService extends BaseCRUDService<CommClassMapper, CommClass>{

	//分页查询
	public Page pageQuery(CommClassQueryParams params) {
		List<Map<String, Object>> page = mapper.pageQuery(params.getCondition(), params);
		params.setRecords(page);
		return params;
	}
	
	public List<CommClass> getClassByHeadermasterId(String headermasterId){
		EntityWrapper<CommClass> wrapper = new EntityWrapper();
		wrapper.eq("headermaster_id", headermasterId);
		wrapper.orderBy("create_time", false);
		List<CommClass> list = mapper.selectList(wrapper);
		return list;
		
	}

	public Object courseStudentSelect(String classId) {
		return mapper.courseStudentSelect(classId);
	}
}
