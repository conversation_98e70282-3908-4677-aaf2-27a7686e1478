package com.xunw.jxjy.model.common.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.common.entity.CommClass;
import org.apache.ibatis.annotations.Param;

public interface CommClassMapper extends BaseMapper<CommClass> {

  //分页查询
  List<Map<String,Object>> pageQuery(Map<String, Object> condition , Page<?> page);

  List<Map<String,Object>> courseStudentSelect(@Param("classId") String classId);

  List<Map<String,Object>> getGroupLeader(@Param(value = "id") String id, @Param(value = "keyword") String keyword);
}
