package com.xunw.jxjy.model.sys.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.sys.entity.User;

public interface UserMapper extends BaseMapper<User>{

	public List<Map<String,Object>> query(Map<String, Object> condition, Page<?> page);
	
	public List<String> getRoleByUserId(@Param("userId") String userId);
	
	public Map<String,Object> getById(@Param("id") String id);

	public List<User> getUserByOrgIdAndRole(@Param("orgId") String orgId, @Param("role") Role role);
	
	public User getUserByRoleAndSfzh(@Param("role") String role,@Param("sfzh") String sfzh);
	
	public Integer countUserByHostOrgId(@Param("hostOrgId") String hostOrgId);
	
}
