package com.xunw.jxjy.model.sys.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.ZipUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Ksly;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.StudentLoginLog;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.StudentLoginLogMapper;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.params.StudentUserQueryParams;
import com.xunw.jxjy.model.utils.OfficeToolExcel;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

@Service
public class StudentUserService extends BaseCRUDService<StudentUserMapper, StudentUser>{

	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private StudentInfoService infoService;
	@Autowired
	private StudentLoginLogMapper studentLoginLogmapper;
	@Autowired
	private OrgService orgService;
	@Autowired
	private AttConfig attConfig;

	public Page pageQuery(StudentUserQueryParams params) {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 根据登录账号查询学员用户
	 */
	public StudentUser findByAccount(String account, String hostOrgId) {
		List<StudentUser> list = mapper.getBySfzhOrMobile(account, hostOrgId);
		if (list.size() == 0) {
			return null;
		} else {
			return list.get(0);
		}
	}

	/**
	 * 根据身份证或手机号查询学员用户
	 */
	public StudentUser findBySfzhOrMobile(String sfzh, String mobile, String hostOrgId) {
		StudentUser studentUser = null;
		if (StringUtils.isNotEmpty(sfzh)) {
			studentUser = this.findBySfzh(sfzh, hostOrgId);
		}
		if (studentUser == null && StringUtils.isNotEmpty(mobile)) {
			studentUser = this.findByMobile(mobile, hostOrgId);
		}
		return studentUser;
	}

	/**
	 * 根据手机号查询学员用户
	 */
	public StudentUser findByMobile(String mobile, String hostOrgId) {
		List<StudentUser> list = mapper.getByMobile(mobile, hostOrgId);
		return list.size() > 0 ? list.get(0) : null;
	}

	/**
	 * 根据身份证号查询学员用户
	 */
	public StudentUser findBySfzh(String sfzh,String hostOrgId) {
		List<StudentUser> list = mapper.getBySfzh(sfzh, hostOrgId);
		return list.size() > 0 ? list.get(0) : null;
	}

	/**
	 * 重设密码
	 */
	@Transactional
	public void resetPasswords(String mobile, String password, String hostOrgId){
		StudentUser studentUser = findByMobile(mobile, hostOrgId);
		if (studentUser == null) {
			throw BizException.withMessage("手机号未注册，请先注册");
		}
		studentUser.setPassword(DigestUtils.md5Hex(password));
		mapper.updateById(studentUser);
	}

	/**
	 * 重置密码
	 */
	@Transactional
	public void resetPasswords(String ids){
		String[] idArray = StringUtils.split(ids, ",");
		List<StudentUser> list = mapper.selectBatchIds(Arrays.asList(idArray));
		for (StudentUser studentUser : list) {
			//获取考生身份证
	        StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentUser.getId());
			String account = StringUtils.isNotEmpty(studentInfo.getSfzh()) ? studentInfo.getSfzh() : studentInfo.getMobile();
			//初始密码
	        String passWord = account.substring(account.length() - 6);
	        studentUser.setPassword(DigestUtils.md5Hex(passWord));
		}
        DBUtils.updateBatchById(list, StudentUser.class);
	}

	/**
	 * 设置培训机构
	 */
	@Transactional
	public void batchSetOrg(String ids, String orgId){
		String[] idArray = StringUtils.split(ids, ",");
		List<StudentUser> list = mapper.selectBatchIds(Arrays.asList(idArray));
		for (StudentUser studentUser : list) {
			studentUser.setOrgId(orgId);
		}
		DBUtils.updateBatchById(list, StudentUser.class);
	}

	@Transactional
	public Map<String, Object> batchImport(MultipartFile file, String hostOrgId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "student_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "NAME", "SFZH", "MOBILE", "GENDER", "TYPE", "COMPANY", "ZW", "EDUCATION", "SCHOOL",
						"COLLEGE", "SPECIALTY", "CLASSZ", "STUDENTNUM" });
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		List<String> sfzList = new ArrayList<String>();
		List<String> mobileList = new ArrayList<String>();

		int row = 0;
		int existsCou = 0;//与系统重复的数据
		int cfCou = 0;//Excel重复数据
		int cwCou = 0;//Excel错误数据
		int success = 0;//注册成功的数据
		for (Map<String, String> map : list) {
			row++;
			// 第一行是标题，放过
			if (row < 2) {
				continue;
			}
			// a、姓名、身份证号、性别、企业代码、年度码、注册时间为必填项。
			String name = StringUtils.trimToNull(map.get("NAME"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);

				// 标记错误数据+1
				cwCou++;
				continue;
			}

			String sfzh = StringUtils.trimToNull(map.get("SFZH"));
			if (StringUtils.isNotEmpty(sfzh)) {
				if (sfzList.contains(sfzh)) {
					String msg = "<span style=\"color:red\">第" + row + "行身份证信息在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				}
				else if (!BaseUtil.isIDCardNumber(sfzh)) {
					String msg = "<span style=\"color:red\">第" + row + "行不是正确的身份证号，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cwCou++;
					continue;
				}
				else {
					sfzList.add(sfzh);
				}
			}

			String mobile = StringUtils.trimToNull(map.get("MOBILE"));
			if (StringUtils.isNotEmpty(mobile)) {
				if (mobileList.contains(mobile)) {
					String msg = "<span style=\"color:red\">第" + row + "行手机号在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				}
				else if (!BaseUtil.isMobile(mobile)) {
					String msg = "<span style=\"color:red\">第" + row + "行不是正确的手机号，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cwCou++;
					continue;
				}
				else {
					mobileList.add(mobile);
				}
			}
			if (StringUtils.isEmpty(sfzh) && StringUtils.isEmpty(mobile)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证、手机号不能同时为空，请仔细检查，忽略这条数据。";
				log.append(msg);
				cwCou++;
				continue;
			}

			String gender =  StringUtils.trimToNull(map.get("GENDER"));
			if (StringUtils.isNotEmpty(gender) && Gender.findByName(gender) == null) {
				log.append("<br>");
				String msg = "第" + row + "行性别填写错误，忽略这条数据。";
				log.append(msg);

				cwCou++;
				continue;
			}
			String studentType = StringUtils.trimToNull(map.get("TYPE"));
			if (StringUtils.isEmpty(studentType)) {
				log.append("<br>");
				String msg = "第" + row + "行学员类型为空，请仔细检查，忽略这条数据。";
				log.append(msg);
				cwCou++;
				continue;
			}
			if (StudentType.findByName(studentType) == null) {
				log.append("<br>");
				String msg = "第" + row + "行学员类型填写错误，忽略这条数据。";
				log.append(msg);

				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String company = StringUtils.trimToNull(map.get("COMPANY"));
			String zw = StringUtils.trimToNull(map.get("ZW"));// 职务
			String education = StringUtils.trimToNull(map.get("EDUCATION"));// 学历
			if (StringUtils.isNotEmpty(education) && Education.findByName(education) == null) {
				log.append("<br>");
				String msg = "第" + row + "行学历填写错误，忽略这条数据。";
				log.append(msg);

				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String studentnum = StringUtils.trimToNull(map.get("STUDENTNUM"));
			String school = StringUtils.trimToNull(map.get("SCHOOL"));// 毕业学校
			String college = StringUtils.trimToNull(map.get("COLLEGE"));// 所在院系
			String specialty = StringUtils.trimToNull(map.get("SPECIALTY"));// 所学专业
			String classz = StringUtils.trimToNull(map.get("CLASSZ"));// 所在班级
			StudentUser studentUser = this.findBySfzhOrMobile(sfzh, mobile, hostOrgId);
			if (studentUser == null) {// 写入学员用户表
				studentUser = new StudentUser();
				studentUser.setId(BaseUtil.generateId2());
				String password = null;
				if (StringUtils.isNotEmpty(sfzh)) {
					password = sfzh.substring(sfzh.length() - 6, sfzh.length());
				} else if (StringUtils.isNotEmpty(mobile)) {
					password = mobile.substring(mobile.length() - 6, mobile.length());
				}
				studentUser.setPassword(DigestUtils.md5Hex(password));
				studentUser.setStatus(AccountStatus.OK);
				studentUser.setStudentType(StudentType.findByName(studentType));
				studentUser.setCompany(company);
				if (StringUtils.isNotEmpty(mobile)) {
					studentUser.setIsBindMobile(Constants.YES);
				} else {
					studentUser.setIsBindMobile(Constants.NO);
				}
				studentUser.setRegHostOrgId(hostOrgId);
				mapper.insert(studentUser);
				// 写入学员信息表
				StudentInfo studentInfo = new StudentInfo();
				studentInfo.setId(BaseUtil.generateId2());
				studentInfo.setStudentId(studentUser.getId());
				studentInfo.setSfzh(sfzh);
				studentInfo.setMobile(mobile);
				studentInfo.setName(name);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender) :
						Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setZw(zw);
				if (studentUser.getStudentType() == StudentType.SOCIAL) {
					studentInfo.setKsly(Ksly.QYZG);
				}
				else if (studentUser.getStudentType() == StudentType.SCHOOL) {
					studentInfo.setKsly(Ksly.YXXS);
				}
				else {
					studentInfo.setKsly(Ksly.QITA);
				}
				studentInfo.setGraduateSchool(school);
				studentInfo.setCollege(college);
				studentInfo.setStudentNum(studentnum);
				studentInfo.setSpecialty(specialty);
				studentInfo.setClassz(classz);
				studentInfo.setCreateTime(new Date());
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfo.setRegHostOrgId(hostOrgId);
				studentInfo.setZw(zw);
				studentInfoMapper.insert(studentInfo);
				success++;
			}
			else {
				//更新已经存在的学员
				studentUser.setStudentType(StudentType.findByName(studentType));
				studentUser.setCompany(company);
				mapper.updateById(studentUser);
				StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentUser.getId());
				studentInfo.setName(name);
				studentInfo.setSfzh(sfzh);
				studentInfo.setMobile(mobile);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender)
						: Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setZw(zw);
				studentInfo.setGraduateSchool(school);
				studentInfo.setCollege(college);
				studentInfo.setStudentNum(studentnum);
				studentInfo.setSpecialty(specialty);
				studentInfo.setClassz(classz);
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfoMapper.updateById(studentInfo);

				existsCou++;
			}
		}
		StringBuffer message = new StringBuffer(
				"<div>总共" + (row - 1) + "条数据，批量注册成功" + success + "条，Excel重复数据" + cfCou + "条，错误数据" + cwCou + "条，系统中已经存在数据"+existsCou+"条</div>");
		message.append(log);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("code", 0);
		map.put("message", message);
		return map;
	}

	public void export(StudentUserQueryParams params, OutputStream os) throws Exception, WriteException {
		params.setCurrent(1);
		params.setSize(Integer.MAX_VALUE);
		List<Map<String, Object>> list = this.pageQuery(params).getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("学员用户表", 0);

		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "学员类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "培训机构", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "所在单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "职务", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "职称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "学历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "毕业学校", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "院系", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "专业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "班级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "学号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "考生来源", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "注册时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
		}

		row = 1;
		for (Map<String, Object> map : list) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("name")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("studentType") != null ? StudentType.valueOf((String) map.get("studentType")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("profession")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty((map.get("orgCode") != null ? map.get("orgCode") : "") + "-"
							+ (map.get("orgName") != null ? map.get("orgName") : "")),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("zw") != null ? map.get("zw") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("zc") != null ? map.get("zc") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("education") != null ? Education.valueOf((String) map.get("education")).getName() : ""),
					OfficeToolExcel.getNormolCell()));

			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("graduateSchool") != null ? map.get("graduateSchool") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("college") != null ? map.get("college") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("specialty") != null ? map.get("specialty") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("classz") != null ? map.get("classz") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentNum") != null ? map.get("studentNum") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("ksly") != null ? Ksly.valueOf((String) map.get("ksly")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("createTime") != null ? map.get("createTime") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	public Object batchImportAvatar(MultipartFile file, String hostOrgId) throws Exception {
		File zipFile = FileHelper.createTmpFile();
		File tmpFolder = FileHelper.createTmpFile();
		file.transferTo(zipFile);
		ZipUtil.uncompress(zipFile.getAbsolutePath(), tmpFolder.getAbsolutePath(), null);
		File[] filelist = tmpFolder.listFiles();
		InputStream is = null;
		int cwCou = 0;
		int success = 0;
		StringBuffer log = new StringBuffer();
		try {
			for (int i = 0; i < filelist.length; i++) {
				File file1 = filelist[i];
				String ext = FileHelper.getExtension(file1.getName());
				if (!(".png").equals(ext) && !(".jpg").equals(ext) && !(".jpeg").equals(ext) && !(".bmp").equals(ext)) {
					log.append("<br>");
					String msg = file1.getName() + "文件格式有误，请您重新确认";
					log.append(msg);
					// 标记错误数据+1
					cwCou++;
					continue;
				}
				String imgName = file1.getName().substring(0, file1.getName().lastIndexOf("."));
				if (imgName.indexOf("-") == -1) {
					log.append("<br>");
					String msg = imgName + "照片命名格式有误，请您重新确认";
					log.append(msg);
					// 标记错误数据+1
					cwCou++;
					continue;
				}
				String sfzh = imgName.substring(0, imgName.indexOf("-"));
				StudentInfo studentInfo = studentInfoMapper.findBysfzh(sfzh, hostOrgId);
				if (studentInfo == null) {
					log.append("<br>");
					String msg = "系统中未检测到学员" + file1.getName().replaceAll("[.][^.]+$", "") + "，请您重新确认";
					log.append(msg);
					// 标记错误数据+1
					cwCou++;
					continue;
				}
				success++;
				is = new FileInputStream(file1);
				String path = attConfig.getRootDir() + "/upload/images/"
						+ new SimpleDateFormat("yyyyMMddHH").format(new Date());
				String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
				String url = FileHelper.storeFile(path, is, newFileName);
				studentInfo.setStudentPhoto(url);
				infoService.updateById(studentInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			BaseUtil.close(is);
			FileHelper.delFile(tmpFolder);
			FileHelper.delFile(zipFile);
		}
		// 提示总共多少条，导入成功多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (filelist.length) + "条，导入成功" + success + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("code", 0);
		map.put("message", message);
		return map;
	}

	@Transactional
	public void deleteByStudentId(String studentId) {
		mapper.deleteById(studentId);
		EntityWrapper<StudentInfo> wrapper = new EntityWrapper();
		wrapper.eq("student_id", studentId);
		studentInfoMapper.delete(wrapper);
	}

	/**
	 * 查询用户下面的订单数量
	 */
	public List<Map<String,Object>> mergePre(String studentId1, String studentId2) {
		List<String> studentIds = new ArrayList<>();
		studentIds.add(studentId1);
		studentIds.add(studentId2);
		return studentInfoMapper.queryOrderByStudentIds(studentIds);
	}
	
	/**
	 * 账号合并
	 * 优先保留身份证号不为空的账号
	 */
	@Transactional
	public void merge(String oldStudentId, String newStudentId) {
		StudentInfo studentInfo1 = studentInfoMapper.getByStudentId(oldStudentId);
		StudentInfo studentInfo2 = studentInfoMapper.getByStudentId(newStudentId);
		if (StringUtils.isNotEmpty(studentInfo1.getSfzh()) && StringUtils.isNotEmpty(studentInfo2.getSfzh())) {
			throw BizException.withMessage("不支持不同身份证号的学员账号合并");//注意身份证号同单位下是唯一索引
		}
/*		// 注释自动选择身份证号不为空的账号，由用户手动去选择需保留的账号
		String oldId = null, newId = null;
		if (StringUtils.isNotEmpty(studentInfo1.getSfzh())) {
			oldId = studentInfo2.getStudentId();
			newId = studentInfo1.getStudentId();
		}
		else {
			oldId = studentInfo1.getStudentId();
			newId = studentInfo2.getStudentId();
		}*/
		mapper.mergeStudentAccount(oldStudentId, newStudentId);
	}
	
	

	@Transactional
	public void insertStudentLoginLog(String studentId, Date lastLoginTime, String client) {
		// 删除之前的登录信息
		studentLoginLogmapper.delete(new EntityWrapper<StudentLoginLog>().eq("student_id", studentId));

		StudentLoginLog studentLoginLog = new StudentLoginLog();
		studentLoginLog.setId(BaseUtil.generateId2());
		studentLoginLog.setStudentId(studentId);
		studentLoginLog.setLastLoginTime(lastLoginTime);
		studentLoginLog.setClient(client);
		studentLoginLogmapper.insert(studentLoginLog);
	}

	public StudentUser selectUserById(String id) {
		StudentUser studentUser = mapper.selectById(id);
		if (StringUtils.isNotEmpty(studentUser.getOrgId())) {
			Org org = orgService.selectById(studentUser.getOrgId());
			studentUser.setOrgName(org.getName());
		}
		return studentUser;
	}

	/**
	 * 扫码看直播添加用户绑定微信并登录
	 */
	@Transactional
	public Map<String,Object> bindStudent(String name, String sfzh, String mobile, String company, String regHostOrgId, String openId) {
		Map<String, Object> map = new HashMap<String, Object>();
		StudentUser studentUser = this.findBySfzhOrMobile(sfzh, mobile, regHostOrgId);
		if (studentUser == null) {
			String studentUserId = this.regist(name, sfzh, mobile, regHostOrgId);
			studentUser = selectById(studentUserId);
			studentUser.setCompany(company);
			studentUser.setOpenId(openId);
			studentUser.setStudentType(StudentType.SOCIAL);
			mapper.updateById(studentUser);
		} else {
			studentUser.setOpenId(openId);
			mapper.updateById(studentUser);
		}
		map.put("studentUser", studentUser);
		StudentInfo studentInfo = infoService.getByStudentId(studentUser.getId());
		map.put("studentInfo", studentInfo);
		return map;
	}

	/**
	 * 通过openId查询学生用户信息
	 */
	public StudentUser getStudentByOpenId(String openId, String hostOrgId) {
		EntityWrapper<StudentUser> wrapper = new EntityWrapper<StudentUser>();
		wrapper.eq("open_id", openId);
		wrapper.eq("reg_host_org_id", hostOrgId);
		StudentUser studentUser = this.selectOne(wrapper);
		return studentUser;
	}

	public StudentUser selectOne(EntityWrapper<StudentUser> wrapper) {
		List<StudentUser> list = mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}

	/**
	 * 学员账号注销，只是状态的变化
	 */
	@Transactional
	public void logicDeleteAccount(String studentId) {
		StudentUser studentUser = new StudentUser();
		studentUser.setId(studentId);
		studentUser.setStatus(AccountStatus.CANCELLATION);
		mapper.updateById(studentUser);
	}

	/**
	 * 学员用户注册
	 */
	@Transactional
	public String regist(String name, String sfzh, String mobile, String regHostOrgId) {
		if (StringUtils.isNotEmpty(sfzh)) {
			if (!BaseUtil.isIDCardNumber(sfzh)) {
				throw BizException.withMessage("请输入正确的身份证号");
			}
			else {
				List<StudentUser> studentUserList = mapper.getBySfzh(sfzh, regHostOrgId);
				if(studentUserList.size() > 0) {
					throw BizException.withMessage("您输入的身份证号码已经被注册，您可以直接使用该身份证号登录或更换身份证号注册");
				}
			}
		}
		if (!BaseUtil.isMobile(mobile)) {
			throw BizException.withMessage("请输入正确的11位手机号码");
		} else {
			//校验手机号是否已经被注册
			List<StudentUser> checkMobileList = mapper.getByMobile(mobile, regHostOrgId);
			if (checkMobileList.size() > 0) {
				throw BizException.withMessage("您输入的手机号码已经被注册，您可以直接使用该手机号登录或更换手机号注册");
			}
		}

		//写入考生用户表
		StudentUser studentUser = new StudentUser();
		studentUser.setId(BaseUtil.generateId2());
		String account = StringUtils.isNotEmpty(sfzh) ? sfzh : mobile;
		String password = Constants.DEFAULT_PASSWORD_PREFIX + account.substring(account.length() - 6);
		studentUser.setPassword(DigestUtils.md5Hex(password));
		studentUser.setIsBindMobile(Constants.YES);
		studentUser.setStatus(AccountStatus.OK);
		studentUser.setRegHostOrgId(regHostOrgId);// 注册主办单位ID
		mapper.insert(studentUser);
		//考生基础信息表
		StudentInfo studentInfo = new StudentInfo();
		studentInfo.setId(BaseUtil.generateId());
		studentInfo.setStudentId(studentUser.getId());
		studentInfo.setName(name);
		studentInfo.setSfzh(sfzh);
		studentInfo.setRegHostOrgId(regHostOrgId);
		String gender = BaseUtil.parseGender(sfzh);
		if (StringUtils.isNotEmpty(gender)) {
			studentInfo.setGender(Gender.findByEnumName(gender));
		}
		studentInfo.setKsly(Ksly.QITA);
		studentInfo.setCreateTime(new Date());
		studentInfo.setMobile(mobile);
		studentInfoMapper.insert(studentInfo);
		return studentUser.getId();
	}

	/**
	 * 根据职工号或学号查询学员用户
	 * @param employeeNum 职工号或学号
	 * @return StudentUser
	 */
	public StudentUser getByEmployeeNum(String employeeNum) {
		List<StudentUser> studentUsers = mapper.selectList(new EntityWrapper<StudentUser>().eq("employee_num", employeeNum));
		return !studentUsers.isEmpty() ? studentUsers.get(0) : null;
	}
}
