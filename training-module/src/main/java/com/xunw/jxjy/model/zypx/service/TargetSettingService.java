package com.xunw.jxjy.model.zypx.service;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.xunw.jxjy.common.utils.Constants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.Target;
import com.xunw.jxjy.model.zypx.entity.TargetSetting;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import com.xunw.jxjy.model.zypx.mapper.TargetMapper;
import com.xunw.jxjy.model.zypx.mapper.TargetResultMapper;
import com.xunw.jxjy.model.zypx.mapper.TargetSettingMapper;
import com.xunw.jxjy.model.zypx.mapper.TargetTypeMapper;
import com.xunw.jxjy.model.zypx.params.TargetSettingQueryParams;

@Service
public class TargetSettingService extends BaseCRUDService<TargetSettingMapper, TargetSetting>{
    private Logger logger = LoggerFactory.getLogger(TargetSettingService.class);

    @Autowired
    private TargetMapper targetMapper;
    @Autowired
    private TargetTypeMapper targetTypeMapper;

    @Autowired
    private TargetResultMapper resultMapper;

    /**
     * 评价查询统计
     *
     * @param params
     * @return
     */
    public Map<String, List<Map<String, String>>> pageQuery(TargetSettingQueryParams params) {
        Map<String, List<Map<String, String>>> map = new HashMap<>();
        try {
            EntityWrapper<TargetSetting> wrapper = new EntityWrapper<TargetSetting>();
            wrapper.eq("xm_id", params.getXmId());
            //获取项目指标
            List<TargetSetting> settingList = mapper.selectList(wrapper);
            for (TargetSetting setting : settingList) {
                String score = resultMapper.getAvgTargetResult(null, setting.getId());
                Target target = targetMapper.selectById(setting.getTargetId());
                TargetType targetType = targetTypeMapper.selectById(target.getTypeId());
                //封装返回数据
                Map<String, String> temp = new HashMap<>();
                temp.put("settingId", setting.getId());
                temp.put("name", target.getName());
                temp.put("type", String.valueOf(setting.getType()));
                temp.put("score", score);
                List<Map<String, String>> list = map.get(targetType.getName());
                if (!CollectionUtils.isEmpty(list)) {
                    list.add(temp);
                } else {
                    list = new ArrayList<>();
                    list.add(temp);
                    map.put(targetType.getName(), list);
                }
            }
            resultMapper.getAvgTargetResult(params.getXmId(), null);
        } catch (BizException e) {
            logger.error("查询错误", e);
        }
        return map;
    }

    /**
     * 根据项目id 或者 项目指标设置表ID 得到平均分数
     *
     * @param xmId
     * @param settingId
     * @return
     */
    public String getAvgTargetResult(String xmId, String settingId) {
        return resultMapper.getAvgTargetResult(xmId, settingId);
    }

    /**
     * 获取项目综合排行榜
     *
     * @return
     */
    public Object getXmRank(String hostOrgId) {
        return resultMapper.getXmRank(hostOrgId);
    }

    /**
     * 获取课件排行榜
     *
     * @return
     */
    public Object getKjRank(String hostOrgId) {
        return resultMapper.getKjRank(hostOrgId);
    }

    /**
     * 获取老师排行榜
     *
     * @return
     */
    public Object getTeacherRank(String hostOrgId) {
        return resultMapper.getTeacherRank(hostOrgId);
    }

    //项目评分指标设置
    public boolean targetSet(TargetSettingQueryParams params, String userId) {
        EntityWrapper<TargetSetting> wrapper = new EntityWrapper<TargetSetting>();
        wrapper.eq("xm_id", params.getXmId());
        //获取项目指标
        mapper.delete(wrapper);
        String[] targetStr = params.getTargetIdList().split(",");
        for (String target : targetStr) {
            TargetSetting setting = new TargetSetting();
            setting.setId(BaseUtil.generateId());
            setting.setXmId(params.getXmId());
            setting.setTargetId(target);
            setting.setCreateTime(new Date());
            setting.setCreatorId(userId);
            insert(setting);
        }
        return true;
    }

    /**
     * 获取指标分类树
     */
    public Object getTargetTree(Integer isShowTarget) {
        EntityWrapper<TargetType> wrapper = new EntityWrapper<TargetType>();
        wrapper.orderBy("sort", true);
        List<TargetType> targetTypeList = targetTypeMapper.selectList(wrapper);
        List<Tree<Object>> trees = TreeUtil.build(targetTypeList, null, null, (treeNode, tree) -> {
            tree.setId(treeNode.getId());
            tree.setParentId(treeNode.getParentId());
            tree.setName(treeNode.getName());
            tree.putExtra("isTarget", Constants.NO);
            if (isShowTarget == 1) {
                if (tree.getChildren() == null) {
                    tree.putExtra("children", new ArrayList<>());
                }
                tree.getChildren().addAll(buildTreeEntity(targetMapper.selectList(new EntityWrapper<Target>().eq("type_id", treeNode.getId()))));
            }
        });
        return trees;
    }

    private List<Tree<Object>> buildTreeEntity(List<Target> targets) {
        List<Tree<Object>> result = new ArrayList<>();
        for (Target target : targets) {
            Tree<Object> tree = new Tree<>();
            tree.setId(target.getId());
            tree.setParentId(target.getTypeId());
            tree.setName(target.getName());
            tree.putExtra("isTarget", Constants.YES);
            result.add(tree);
        }
        return result;
    }

    /**
     * 评价查询统计
     */
    public Map<Object, List<Map<String, Object>>> selectTargetResultList(String xmId, String studentId) {
        Map<Object, List<Map<String, Object>>> map = new HashMap<>();
        try {
            //获取项目指标
            List<Map<String, Object>> targetResultList = mapper.selectTargetResultByStudentId(xmId, studentId);
            map = targetResultList.stream().collect(Collectors.groupingBy(item -> item.get("typeId")));
        } catch (BizException e) {
            logger.error("查询错误", e);
        }
        return map;
    }

    public Object list(String xmId) {
        List<TargetType> types = mapper.list(xmId);
        List<TargetType> targetTypeList = targetTypeMapper.selectList(null);
        List<TargetType> result = new ArrayList<>();
        for (TargetType type : types) {
            this.getTopTypes(targetTypeList, type, result);
        }
        return result;
    }

    private List<TargetType> getTopTypes(List<TargetType> types, TargetType type, List<TargetType> result){
        Optional<TargetType> first = types.stream().filter(x -> x.getId().equals(type.getParentId())).findFirst();
        if (!first.isPresent()) {
            if (!result.contains(type)) {
                result.add(type);
            }
        } else {
            getTopTypes(types, first.get(), result);
        }
        return result;
    }
}
