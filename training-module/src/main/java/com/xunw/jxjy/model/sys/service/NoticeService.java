package com.xunw.jxjy.model.sys.service;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.enums.Receiver;
import com.xunw.jxjy.model.sys.entity.NoticeCategory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.sys.entity.Notice;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.NoticeMapper;
import com.xunw.jxjy.model.sys.params.NoticeQueryParams;
import net.sf.json.JSONObject;
import org.springframework.web.bind.annotation.RequestParam;

@Service
public class NoticeService extends BaseCRUDService<NoticeMapper, Notice>{

	@Autowired
	private NoticeCategoryService noticeCategoryService;

	// 查询
	public Page list(NoticeQueryParams params){
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		for (Map<String, Object> map : list) {
			map.put("content", BaseUtil.getStringValueFromMap(map, "content", StringUtils.EMPTY));
		}
		params.setRecords(list);
		return params;
	}

	// 新增

	public boolean add(JSONObject quesJson, User sysJgyh) throws Exception {
		Notice sysTzggXx = new Notice();
		Zt status = Zt.valueOf(quesJson.getString("status"));// 状态
		if (status == null) {
			throw BizException.withMessage("请选择状态");
		}
		String categoryId = quesJson.getString("categoryId");// 文章分类
		if (StringUtils.isEmpty(categoryId)) {
			throw BizException.withMessage("请选择文章分类");
		}
		String title = quesJson.getString("title");
		if (StringUtils.isEmpty(title)) {
			throw BizException.withMessage("请输入文章标题");
		}
		String isToTop = quesJson.getString("isToTop");
		if (StringUtils.isEmpty(isToTop)) {
			throw BizException.withMessage("请选择是否同类置顶");
		}
		if (!quesJson.containsKey("receiver")) {
			throw BizException.withMessage("请选择接收对象");
		}
		//接收对象
		Receiver receiver=Receiver.findByEnumName(quesJson.getString("receiver"));
		if (receiver == null){
			throw BizException.withMessage("请选择接收人");
		}
		String publishTime = quesJson.getString("publishTime");
		if (StringUtils.isEmpty(publishTime)) {
			throw BizException.withMessage("请选择发布时间");
		}
		String writer = quesJson.getString("writer");
		String source = quesJson.getString("source");
		// 文章内容
		String content = quesJson.getString("content");
		// url
		String url = quesJson.getString("url");
		String isLink = BaseUtil.getStringValueFromJson(quesJson, "isLink", Constants.NO);
		String linkUrl = BaseUtil.getStringValueFromJson(quesJson,"linkUrl");
		sysTzggXx.setId(BaseUtil.generateId());
		sysTzggXx.setTitle(title);
		sysTzggXx.setCategoryId(categoryId);
		sysTzggXx.setIsToTop(isToTop);
		sysTzggXx.setWriter(writer);
		sysTzggXx.setStatus(status);
		sysTzggXx.setSource(source);
		sysTzggXx.setContent(content);
		sysTzggXx.setUrl(url);
		sysTzggXx.setReceiver(receiver);
		sysTzggXx.setIsLink(isLink);
		sysTzggXx.setLinkUrl(linkUrl);
		sysTzggXx.setCreatorId(sysJgyh.getId());
		sysTzggXx.setPublishTime(DateUtils.parse(publishTime));
		sysTzggXx.setCreateTime(new Date());
		mapper.insert(sysTzggXx);
		return true;
	}

	// 修改
	public boolean edit(String id, JSONObject quesJson, User sysJgyh) throws Exception {
		Notice sysTzggXx = mapper.selectById(id);
		if (sysTzggXx == null) {
			throw BizException.PARAMS_ERROR;
		}
		Zt status = Zt.valueOf(quesJson.getString("status"));// 状态
		if (status == null) {
			throw BizException.withMessage("请选择状态");
		}
		String categoryId = quesJson.getString("categoryId");// 文章分类
		if (StringUtils.isEmpty(categoryId)) {
			throw BizException.withMessage("请选择文章分类");
		}
		String title = quesJson.getString("title");
		if (StringUtils.isEmpty(title)) {
			throw BizException.withMessage("请输入文章标题");
		}
		String isToTop = quesJson.getString("isToTop");
		if (StringUtils.isEmpty(isToTop)) {
			throw BizException.withMessage("请选择是否同类置顶");
		}
		String publishTime = quesJson.getString("publishTime");
		if (StringUtils.isEmpty(publishTime)) {
			throw BizException.withMessage("请选择发布时间");
		}
		if (!quesJson.containsKey("receiver")) {
			throw BizException.withMessage("请选择接收人");
		}
		Receiver receiver = Receiver.findByEnumName(quesJson.getString("receiver"));
		String writer = quesJson.getString("writer");
		String source = quesJson.getString("source");
		// 文章内容
		String content = quesJson.getString("content");
		String url = quesJson.getString("url");
		String isLink = BaseUtil.getStringValueFromJson(quesJson, "isLink", Constants.NO);
		String linkUrl = BaseUtil.getStringValueFromJson(quesJson,"linkUrl");
		sysTzggXx.setReceiver(receiver);
		sysTzggXx.setTitle(title);
		sysTzggXx.setCategoryId(categoryId);
		sysTzggXx.setIsToTop(isToTop);
		sysTzggXx.setWriter(writer);
		sysTzggXx.setStatus(status);
		sysTzggXx.setSource(source);
		sysTzggXx.setContent(content);
		sysTzggXx.setUrl(url);
		sysTzggXx.setIsLink(isLink);
		sysTzggXx.setLinkUrl(linkUrl);
		sysTzggXx.setUpdatorId(sysJgyh.getId());
		sysTzggXx.setPublishTime(DateUtils.parse(publishTime));
		sysTzggXx.setCreateTime(new Date());
		mapper.updateAllColumnById(sysTzggXx);
		return true;
	}

	// 获取
	public Map<String, Object> getInfoById(String id) {
		Map<String, Object> map = mapper.getInfoById(id);
		map.put("content", BaseUtil.isNotEmpty(map.get("content")) ? map.get("content") : "");
		return map;
	}

	/**
	 * 接收推送的资讯
	 */
	@Transactional(rollbackFor = Exception.class)
	public void receive(String id, String title, String publisher, Date publishTime, String isTop, String logo, String content) {
		// 查询资讯是否已存在
		EntityWrapper<Notice> wrapper = new EntityWrapper<>();
		wrapper.eq("third_party_id", id);
		List<Notice> notices = selectList(wrapper);
		Notice notice;

		// 如果不包含http and https 说明是base64 图片
		if (!logo.toLowerCase().contains("http://") && !logo.toLowerCase().contains("https://")) {
			try {
				logo = FileHelper.convertImgBase64SrcStrToUrl(logo);
			} catch (Exception e) {
				throw BizException.withMessage("封面图不是base64图片");
			}
		}

		// 存在 更新
		if (notices.size() > 0) {
			notice = notices.get(0);
			notice.setTitle(title);
			notice.setIsToTop(isTop);
			notice.setWriter(publisher);
			notice.setContent(content);
			notice.setUrl(logo);
			notice.setUpdateTime(publishTime);
			updateById(notice);
		} else {
			// 不存在 保存
			notice = new Notice(BaseUtil.generateId(), title, "ZXXW", isTop, publisher, null,
					Zt.OK, "0", content, logo, Constants.NO, null, null, publishTime, Receiver.ALL, id);
			insert(notice);
		}
	}

	public List<Notice> getByCategoryName(String name, String hostOrgId) {
		return mapper.getByCategoryName(name, hostOrgId);
	}

	@Transactional(rollbackFor = Exception.class)
	public void deleteById(String id) {
		mapper.deleteById(id);
	}

}
