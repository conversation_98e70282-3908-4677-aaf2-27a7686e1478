package com.xunw.jxjy.model.inf.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.Zt;

/**
 * 课程表
 */
@TableName("inf_course")
public class Course implements Serializable {
	
	private static final long serialVersionUID = -354116107759164784L;

	//主键id
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	//项目类型ID
	@TableField("type_id")
	private String typeId;

	//课程代码
	@TableField("code")
	private String code;

	//课程名称
	@TableField("name")
	private String name;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;
	
	//创建时间
	@TableField("create_time")
	private Date createTime;
	
	//修改用户id
	@TableField("updator_id")
	private String updatorId;

	//修改时间
	@TableField("update_time")
	private Date updateTime;

	//状态
	@TableField("status")
	private Zt status;
	
	//logo
	@TableField("logo")
	private String logo;
	
	//宣传图
	@TableField("xct")
	private String xct;
	
	//课程性质 0 理论  1 实操,见Constants常量定义
	@TableField("kcxz")
	private String kcxz;
	
	//课程学时
	@TableField("hours")
	private Double hours;
	
	//课程标签
	@TableField("tag")
	private String tag;
	
	//主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;

    //课程费用
	@TableField("amount")
	private Double amount;

    //课程简介
	@TableField("remark")
	private String remark;

	//是否在门户上开放课件
	@TableField("is_open_kj")
	private String isOpenKj;

	//是否在门户上开放直播
	@TableField("is_open_zb")
	private String isOpenZb;

	//是否在门户上开放题库
	@TableField("is_open_tk")
	private String isOpenTk;

	public Course() {
		
	}

	public Course(String id, String code, String name, String creatorId, Date createTime, Zt status) {
		this.id = id;
		this.code = code;
		this.name = name;
		this.creatorId = creatorId;
		this.createTime = createTime;
		this.status = status;
	}

	public Course(String id, String creatorId, Date updateTime, Zt status) {
		super();
		this.id = id;
		this.creatorId = creatorId;
		this.updateTime = updateTime;
		this.status = status;
	}

	public Course(String id, String name, String updatorId, Date updateTime) {
		super();
		this.id = id;
		this.name = name;
		this.updatorId = updatorId;
		this.updateTime = updateTime;
	}

	public String getId() {
		return id;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Zt getStatus() {
		return status;
	}

	public void setStatus(Zt status) {
		this.status = status;
	}

	public String getXct() {
		return xct;
	}

	public void setXct(String xct) {
		this.xct = xct;
	}

	public String getKcxz() {
		return kcxz;
	}

	public void setKcxz(String kcxz) {
		this.kcxz = kcxz;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}
	
	public Double getHours() {
		return hours;
	}

	public void setHours(Double hours) {
		this.hours = hours;
	}

	public String getIsOpenKj() {
		return isOpenKj;
	}

	public void setIsOpenKj(String isOpenKj) {
		this.isOpenKj = isOpenKj;
	}

	public String getIsOpenZb() {
		return isOpenZb;
	}

	public void setIsOpenZb(String isOpenZb) {
		this.isOpenZb = isOpenZb;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getIsOpenTk() {
		return isOpenTk;
	}

	public void setIsOpenTk(String isOpenTk) {
		this.isOpenTk = isOpenTk;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
