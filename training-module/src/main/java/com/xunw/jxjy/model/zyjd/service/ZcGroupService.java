package com.xunw.jxjy.model.zyjd.service;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.config.TencentTUIRoomKitConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.GroupStatus;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.mapper.UserInfoMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zyjd.entity.ZcGroup;
import com.xunw.jxjy.model.zyjd.entity.ZcGroupStudent;
import com.xunw.jxjy.model.zyjd.entity.ZcGroupUser;
import com.xunw.jxjy.model.zyjd.entity.ZcReviewMark;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupStudentMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupUserMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcReviewMarkMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmBatchMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zyjd.params.ZcpsStudentParams;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

@Service
public class ZcGroupService extends BaseCRUDService<ZcGroupMapper, ZcGroup> {

    private static final Logger logger = LoggerFactory.getLogger(ZcGroupService.class);

    @Autowired
    private ZcGroupStudentMapper zcGroupStudentMapper;
    @Autowired
    private ZyjdBmMapper zyjdBmMapper;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private ZcGroupUserMapper zcGroupUserMapper;
    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private ZyjdBmBatchMapper zyjdBmBatchMapper;
    @Autowired
    private TencentTUIRoomKitConfig tencentTUIRoomKitConfig;
    @Autowired
    private TxyService txyService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ZcGroupMapper zcGroupMapper;
    @Autowired
    private ZcReviewMarkMapper zcReviewMarkMapper;
    @Autowired
    private ZyjdProfessionMapper zyjdProfessionMapper;
    @Autowired
    private StudentUserMapper studentUserMapper;

    public Object pageQuery(String bmbatchId, String type, String teacherId, Page page) {
        page.setRecords(mapper.pageQuery(bmbatchId, type, teacherId, page));
        return page;
    }

    public Object getGroupInfoByBatchId(String bmbatchId, String type) {
        Map<String, Object> result = new HashMap<>();
        Integer groupedNum = zcGroupStudentMapper.getGroupStudentCount(bmbatchId, type);
        Integer totalNum = zyjdBmMapper.selectCount(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId)
                .eq("apply_tech_level",TechLevel.ONE.name()).eq("status", ZyjdBmStatus.SHTG.name()));
        List<ZcGroup> dbGroups = mapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "1"));
        Long notReplyNum = 0L;
        if (Objects.equals(type, "2") && !dbGroups.isEmpty()) {
            List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                    .in("group_id", dbGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
//            totalNum = zcGroupStudents.size();
            notReplyNum = zcGroupStudents.stream().filter(s-> Objects.equals(s.getStatus(), Constants.NO)).count();
        }
        result.put("totalNum", totalNum);
        result.put("groupedNum", groupedNum);
        result.put("notGroupNum", totalNum - groupedNum);
        result.put("notReplyNum", notReplyNum);
        return result;
    }

    public Object getGroupPersonInfo(String groupId) {
        List<Map<String, Object>> teachers = new ArrayList<>();
        String leader = null;
        List<ZcGroupUser> zcGroupUsers = zcGroupUserMapper.selectList(new EntityWrapper<ZcGroupUser>().eq("group_id", groupId));
        Map<String, Object> result = new HashMap<>();
        result.put("students", studentInfoMapper.getZcpsBmList(groupId));
        if (zcGroupUsers != null && !zcGroupUsers.isEmpty()) {
            teachers = userInfoMapper.listByUserIds(zcGroupUsers.stream().map(ZcGroupUser::getUserId).collect(Collectors.toList()));
            ZcGroupUser leaderUser = zcGroupUsers.stream().filter(zcGroupUser -> Constants.YES.equals(zcGroupUser.getLeader())).findFirst().orElse(null);
            leader = leaderUser != null ? leaderUser.getUserId() : null;
        }
        result.put("teachers", teachers);
        result.put("leader", leader);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void genNum(String groupId, String type) {
        List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.list(groupId, type);
        for (int i = 0; i < zcGroupStudents.size(); i++) {
            zcGroupStudents.get(i).setNum(i + 1);
        }
        DBUtils.updateBatchById(zcGroupStudents, ZcGroupStudent.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void settingStudent(String id, String studentIds) {
        ZcGroup zcGroup = mapper.selectById(id);
        if (zcGroup.getStatus() == GroupStatus.FINISHED) {
            throw BizException.withMessage("该分组答辩已经结束，不能添加学员");
        }
        if (BaseUtil.isNotEmpty(studentIds) && studentIds.split(",").length > 35) {
            throw BizException.withMessage("每组最多35人");
        }
        zcGroupStudentMapper.delete(new EntityWrapper<ZcGroupStudent>().eq("group_id", id));
        if (BaseUtil.isEmpty(studentIds)) {
            return;
        }
        List<ZcGroupStudent> zcGroupStudents = new ArrayList<>();
        for (String studentId : studentIds.split(",")) {
            ZcGroupStudent student = new ZcGroupStudent();
            student.setId(BaseUtil.generateId2());
            student.setGroupId(id);
            student.setStudentId(studentId);
            student.setStatus(Constants.NO);
            zcGroupStudents.add(student);
        }
        DBUtils.insertBatch(zcGroupStudents, ZcGroupStudent.class);
    }

    public void deleteGroup(String ids) {
        Integer count = zcGroupStudentMapper.selectCount(new EntityWrapper<ZcGroupStudent>().in("group_id", ids.split(",")));
        if (count > 0) {
            throw BizException.withMessage("该分组下已经分配了学员，不能删除");
        }
        for (String id : ids.split(",")) {
            this.editGroupBeforeCheck(id);
        }
        mapper.deleteBatchIds(Arrays.asList(ids.split(",")));
    }

    @Transactional(rollbackFor = Exception.class)
    public void settingTeacher(String id, String teacherIds, String leaderId) {
        ZcGroup zcGroup = mapper.selectById(id);
        if (zcGroup.getStatus() == GroupStatus.FINISHED) {
            throw BizException.withMessage("该分组答辩已经结束，不能添加评委");
        }
        zcGroupUserMapper.delete(new EntityWrapper<ZcGroupUser>().eq("group_id", id));
        List<ZcGroupUser> groupUsers = new ArrayList<>();
        for (String teacherId : teacherIds.split(",")) {
            ZcGroupUser groupUser = new ZcGroupUser();
            groupUser.setGroupId(id);
            groupUser.setUserId(teacherId);
            groupUser.setLeader(Objects.equals(teacherId, leaderId) ? Constants.YES : Constants.NO);
            groupUser.setId(BaseUtil.generateId2());
            groupUsers.add(groupUser);
        }
        DBUtils.insertBatch(groupUsers, ZcGroupUser.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoSettingStudent(String bmbatchId, String type, String userId) {
        List<ZcGroup> groups = mapper.selectList(new EntityWrapper<ZcGroup>().eq("bmbatch_id", bmbatchId).eq("type", type));
        if (groups.stream().anyMatch(x->x.getStatus() == GroupStatus.INPROGRESS || x.getStatus() == GroupStatus.FINISHED)) {
            throw BizException.withMessage("该批次下已经有分组进行答辩了，不能自动分组");
        }
        if ("2".equals(type)) {
            List<ZcGroup> dbGroups = mapper.selectList(new EntityWrapper<ZcGroup>().eq("bmbatch_id", bmbatchId).eq("type", "1"));
            if (!dbGroups.isEmpty()) {
                List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                        .in("group_id", dbGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
                if (!zcGroupStudents.isEmpty()) {
                    List<ZyjdBm> zyjdBms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>()
                            .eq("bmbatch_id", bmbatchId)
                            .eq("apply_tech_level",TechLevel.ONE.name())
                            .eq("status", ZyjdBmStatus.SHTG.name())
                            .in("student_id", zcGroupStudents.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toList())));
                    if (zyjdBms.stream().anyMatch(b->b.getExamScore() != null || b.getSkillScore() != null || b.getSynthesizeScore() != null
                            || b.getJobScore() != null || b.getPotentialScore() != null)) {
                        throw BizException.withMessage("该批次下已经有成绩录入，不能进行自动分组");
                    }
                }
            }
        }
        //清除手动分组
        mapper.delete(new EntityWrapper<ZcGroup>().eq("bmbatch_id", bmbatchId).eq("type", type));
        //未分组的学员
        List<StudentInfo> studentInfos = zcGroupStudentMapper.notGroupStudent(bmbatchId, type);
        List<ZcGroupStudent> students = new ArrayList<>();
        List<ZcGroup> zcGroups = new ArrayList<>();
        int loopNum = studentInfos.size() % 35 == 0 ? studentInfos.size() / 35 : studentInfos.size() / 35 + 1;
        for (int i = 0; i < loopNum; i++) {
            if (i * 35 >= studentInfos.size()) {
                break;
            }
            ZcGroup zcGroup = new ZcGroup();
            String groupId = BaseUtil.generateId2();
            zcGroup.setId(groupId);
            zcGroup.setType(type);
            zcGroup.setBmbatchId(bmbatchId);
            zcGroup.setName("第" + (i + 1) + "组");
            zcGroup.setNum(i + 1);
            zcGroup.setCreatedBy(userId);
            zcGroup.setCreatedTime(new Date());
            zcGroup.setStatus(GroupStatus.NOTSTARTED);
            zcGroups.add(zcGroup);
            for (int j = 0; j < 35; j++) {
                if (i * 35 + j >= studentInfos.size()) {
                    break;
                }
                StudentInfo studentInfo = studentInfos.get(i * 35 + j);
                ZcGroupStudent zcGroupStudent = new ZcGroupStudent();
                zcGroupStudent.setId(BaseUtil.generateId2());
                zcGroupStudent.setStudentId(studentInfo.getStudentId());
                zcGroupStudent.setGroupId(groupId);
                zcGroupStudent.setStatus(Constants.NO);
                students.add(zcGroupStudent);
            }
        }
        DBUtils.insertBatch(zcGroups, ZcGroup.class);
        DBUtils.insertBatch(students, ZcGroupStudent.class);
    }

    public void exportTeacher(String id, String type, OutputStream os) throws IOException, WriteException {
        ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(id);
        if (zyjdBmBatch == null) {
            throw BizException.withMessage("批次不存在");
        }
        List<Map<String, Object>> teachers = mapper.getGroupTeachersByBatchId(id, type);
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("分组评委详情", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "小组", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "单位", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "职务", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "专业类别", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "联系方式", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            if ("2".equals(type)) {
                ws.addCell(new Label(i, row, "备注", OfficeToolExcel.getTitle()));
                ws.setColumnView(i++, 20);
            }
        }
        row = 1;
        int i = 1;
        for (Map<String, Object> map : teachers) {
            int col = 0;
            ws.addCell(new Label(col++, row, String.valueOf(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("groupName") != null ? map.get("groupName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("workUnit") != null ? map.get("workUnit") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(
                    new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
                            OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("zw") != null ? map.get("zw") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
                    map.get("specialityType") != null ? map.get("specialityType") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
                    OfficeToolExcel.getNormolCell()));
            if ("2".equals(type)) {
                ws.addCell(new Label(col++, row, Objects.equals(BaseUtil.getStringValueFromMap(map, "isLeader"), Constants.YES) ? "组长" : "",
                        OfficeToolExcel.getNormolCell()));
            }
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    public void exportStudent(String id, String type, OutputStream os) throws WriteException, IOException {
        ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(id);
        if (zyjdBmBatch == null) {
            throw BizException.withMessage("批次不存在");
        }
        List<Map<String, Object>> teachers = mapper.getGroupStudentsByBatchId(id, type);
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("分组学员详情", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "分组", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "工种", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "单位", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
        }
        row = 1;
        int i = 1;
        for (Map<String, Object> map : teachers) {
            int col = 0;
            ws.addCell(new Label(col++, row, String.valueOf(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("groupName") != null ? map.get("groupName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("professionName") != null ? map.get("professionName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
                    OfficeToolExcel.getNormolCell()));
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    /**
     * 获取批次分组详情
     * @param id 批次id
     * @param type 分组类型 1答辩 2评审
     * @return 返回值
     */
    public List<Map<String, Object>> getGroupTeachersByBatchId(String id, String type) {
        return mapper.getGroupTeachersByBatchId(id, type);
    }

    /**
     * 根据房间号获取分组
     *
     * @param roomId 房间号
     * @return 分组
     */
    public ZcGroup getGroupByRoomId(String roomId) {
        List<ZcGroup> zcGroups = mapper.selectList(new EntityWrapper<ZcGroup>().eq("room_id", roomId));
        return zcGroups.isEmpty() ? null : zcGroups.get(0);
    }

    /**
     * 更新回放url
     *
     * @param videoUrl 回放视屏地址
     * @param roomId   房间号
     */
    public void updatePlayUrl(String videoUrl, String roomId) {
        ZcGroup group = this.getGroupByRoomId(roomId);
        if (group == null) {
            logger.error("roomId:" + roomId + ",更新回放地址失败,未找到分组。");
            return;
        }
        group.setPlaybackUrl(videoUrl);
        mapper.updateById(group);
    }

    /**
     * 更新分组状态
     *
     * @param roomId 房间号
     * @param status 分组状态
     */
    public void updateGroupStatus(String roomId, GroupStatus status) {
        ZcGroup group = this.getGroupByRoomId(roomId);
        if (group == null) {
            logger.error("roomId:" + roomId + ",更新分组状态失败,未找到分组。");
            return;
        }
        group.setStatus(status);
        mapper.updateById(group);
        if (status == GroupStatus.FINISHED) {
            List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                    .eq("group_id", group.getId())
                    .eq("status", "1"));
            zcGroupStudents.forEach(x -> {
                x.setStatus("2");
            });
            DBUtils.updateBatchById(zcGroupStudents, ZcGroupStudent.class);
        }
    }

    /**
     * 更新分组回放任务id
     *
     * @param roomId 房间id
     * @param taskId 任务id
     */
    public void updateGroupTaskId(String roomId, String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            logger.error("roomId:" + roomId + ",更新分组回放任务ID失败,taskId为空。");
            return;
        }
        ZcGroup group = this.getGroupByRoomId(roomId);
        if (group == null) {
            logger.error("roomId:" + roomId + ",更新分组回放任务ID失败,未找到分组。");
            return;
        }
        group.setTaskId(taskId);
        mapper.updateById(group);
    }

    //学生进入房间
    @Transactional
    public JSONObject inRoomStudent(String groupId, String studentId) {
        StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentId);
        ZcGroup zcGroup = mapper.selectById(groupId);
        List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                .eq("group_id", groupId).eq("student_id", studentId));
        if (StringUtils.isNotEmpty(zcGroup.getRoomId())) {
            ZcGroupStudent zcGroupStudent = zcGroupStudents.get(0);
            zcGroupStudent.setStatus(Constants.YES);
            zcGroupStudent.setStartDbTime(new Date());
            zcGroupStudentMapper.updateById(zcGroupStudent);
        }
        JSONObject config = new JSONObject();
        config.put("sdkAppId", Integer.parseInt(tencentTUIRoomKitConfig.getTxySDKAppID()));
        config.put("userId", studentId);
        config.put("userSig", txyService.getUserSig(studentId));
        Integer num = zcGroupStudents.get(0).getNum();
        config.put("userName", studentInfo.getName() + (num != null ? "(" + num + ")" : ""));
        config.put("avatarUrl", studentInfo.getStudentPhoto());
        config.put("roomId", zcGroup.getRoomId());
        config.put("roomName", zcGroup.getName());
        return config;
    }

    //老师进入房间
    public JSONObject inRoomTeacher(String groupId, String userId) {
        User user = userMapper.selectById(userId);
        ZcGroup zcGroup = mapper.selectById(groupId);
        List<ZcGroupUser> zcGroupUsers = zcGroupUserMapper.selectList(new EntityWrapper<ZcGroupUser>().eq("group_id", groupId).eq("user_id", userId));
        if (zcGroupUsers.isEmpty()) {
            throw BizException.withMessage("您不是该组的评委老师，无法进入房间");
        }
        if (zcGroup == null) {
            throw BizException.withMessage("分组不存在");
        }
        ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(zcGroup.getBmbatchId());
        if (new Date().after(zyjdBmBatch.getReplyEndTime())) {
            throw BizException.withMessage("已过答辩时间");
        }
        if (zcGroup.getStatus() == GroupStatus.FINISHED) {
            throw BizException.withMessage("答辩已结束");
        }
        if (zcGroup.getStatus() == GroupStatus.NOTSTARTED) {
            //导入腾讯云
            txyService.accountImport(userId);
            String roomId = txyService.creatRoom(userId);
            zcGroup.setRoomId(roomId);
            zcGroup.setStatus(GroupStatus.INPROGRESS);
            mapper.updateById(zcGroup);
        }

        JSONObject config = new JSONObject();
        config.put("sdkAppId", Integer.parseInt(tencentTUIRoomKitConfig.getTxySDKAppID()));
        config.put("userId", userId);
        config.put("userSig", txyService.getUserSig(userId));
        config.put("userName", user.getName() + "(老师)");
        config.put("avatarUrl", user.getAvatar());
        config.put("roomId", zcGroup.getRoomId());
        config.put("roomName", zcGroup.getName());
        return config;
    }

    //学生进入房间之前的检测
    public void inRoomStudentBeforeCheck(String groupId, String studentId) {
        if (StringUtils.isEmpty(groupId)) {
            throw BizException.withMessage("请选择分组");
        }
        ZcGroup zcGroup = mapper.selectById(groupId);
        ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(zcGroup.getBmbatchId());
        if (new Date().before(zyjdBmBatch.getReplyStartTime()) || new Date().after(zyjdBmBatch.getReplyEndTime())) {
            throw BizException.withMessage("不在答辩时间范围");
        }
        if (zcGroup.getStatus() == GroupStatus.NOTSTARTED) {
            throw BizException.withMessage("答辩房间未创建，请等待评委老师创建房间");
        } else if (zcGroup.getStatus() == GroupStatus.FINISHED) {
            throw BizException.withMessage("答辩已结束");
        }
        List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>().eq("group_id", groupId).eq("student_id", studentId));
        if (zcGroupStudents.isEmpty()) {
            throw BizException.withMessage("您不是该分组的学员，请联系管理员。");
        }
    }


    //分组操作前置校验
    public void commitGroupBeforeCheck(String batchId, String type) {
//        ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(batchId);
//        if (Constants.YES.equals(type)) {
//            if (new Date().before(zyjdBmBatch.getReplyStartTime())) {
//                throw BizException.withMessage("答辩暂未开始");
//            }
//            if (new Date().after(zyjdBmBatch.getReplyEndTime())) {
//                throw BizException.withMessage("答辩已结束");
//            }
//        } else if ("2".equals(type)) {
//            if (new Date().before(zyjdBmBatch.getReviewStartTime())) {
//                throw BizException.withMessage("评审暂未开始");
//            }
//            if (new Date().after(zyjdBmBatch.getReviewEndTime())) {
//                throw BizException.withMessage("评审已结束");
//            }
//        }
    }


    //答辩分组数据更改前置校验
    public void editGroupBeforeCheck(String groupId) {
        ZcGroup zcGroup = mapper.selectById(groupId);
        if (Constants.YES.equals(zcGroup.getType()) && zcGroup.getStatus() != GroupStatus.NOTSTARTED) {
            throw BizException.withMessage("答辩已经开始，无法更改");
        }
    }

    public Object getFinishedStudent(String groupId) {
        return studentInfoMapper.getFinishedStudent(groupId);
    }

    public void resetRoomStatus(String id) {
        ZcGroup zcGroup = mapper.selectById(id);
        if (zcGroup == null) {
            throw BizException.withMessage("分组不存在");
        }
        zcGroup.setStatus(GroupStatus.NOTSTARTED);
        zcGroup.setRoomId(null);
        zcGroup.setTaskId(null);
        zcGroup.setPlaybackUrl(null);
        mapper.updateAllColumnById(zcGroup);
    }

    /**
     * 答辩评审答辩学员分页查询
     */
    public Page studentPage(ZcpsStudentParams params) {
         List<Map<String, Object>> list = zyjdBmMapper.studentPage(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    /**
     * 标记学员的答辩状态
     */
    @Transactional
	public void signReplyStatus(String ids, String status, String remark, String userId) {
		List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectBatchIds(Arrays.asList(ids.split(",")));
		int i = 0;
		for (ZcGroupStudent z : zcGroupStudents) {
			i++;
			z.setStatus(status);
			z.setSignUser(userId);
			z.setSignTime(new Date());
			z.setRemark(remark);
			ZcGroup zcGroup = zcGroupMapper.selectById(z.getGroupId());
			if (zcGroup == null) {
				throw BizException.withMessage("第" + i + "个答辩学员的答辩房间不存在，无法标记！");
			}
			if (zcGroup.getStatus() == GroupStatus.NOTSTARTED) {
				throw BizException.withMessage("第" + i + "个答辩学员的答辩房间未开始，无法标记！");
			}
		}
		DBUtils.updateBatchById(zcGroupStudents, ZcGroupStudent.class);
	}

    public Map<String, Object> psStatistics(String bmbatchId) {
        Map<String, Object> result = new HashMap<>();
        result.put("all", this.all(bmbatchId));
        result.put("byProfession", this.byProfession(bmbatchId));
        result.put("byCompany", this.byCompany(bmbatchId));
//      result.put("byGender", this.byGender(bmbatchId));
        result.put("byJs", this.byJs(bmbatchId));
        return result;
    }
    
	private List<Map<String, Object>> byJs(String bmbatchId) {
		List<Map<String, Object>> result = new ArrayList<>();
		// 报名信息
		List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
		// 评审分组列表
		List<ZcGroup> zcGroups = zcGroupMapper
				.selectList(new EntityWrapper<ZcGroup>().eq("bmbatch_id", bmbatchId).eq("type", "2"));
		// 评审分组学员信息
		List<ZcGroupStudent> zcGroupStudents;
		if (CollectionUtils.isNotEmpty(zcGroups)) {
			zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>().in("group_id",
					zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
		} else {
			zcGroupStudents = new ArrayList<>();
		}

		// 报名等级分组
		Map<TechLevel, List<ZyjdBm>> levelGroup = bms.stream()
				.collect(Collectors.groupingBy(ZyjdBm::getApplyTechLevel));

		// 评审过的报名列表
		List<ZyjdBm> bmList = bms.stream().filter(b -> zcGroupStudents.stream().map(ZcGroupStudent::getStudentId)
				.collect(Collectors.toSet()).contains(b.getStudentId())).collect(Collectors.toList());
		// 评审得分
		List<ZcReviewMark> zcReviewMarks = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(bmList)) {
			zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id",
					bmList.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
		}
		Map<String, List<ZcReviewMark>> zcReviewMarksGroup = zcReviewMarks.stream()
				.collect(Collectors.groupingBy(ZcReviewMark::getBmId));

		for (TechLevel level : levelGroup.keySet()) {
			List<ZyjdBm> list = levelGroup.get(level);
			List<Map.Entry<String, List<ZcReviewMark>>> reviewMarksGroup = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(list)) {
				Set<String> finalBmIdSet = list.stream().map(ZyjdBm::getId).collect(Collectors.toSet());
				reviewMarksGroup = zcReviewMarksGroup.entrySet().stream().filter(m -> finalBmIdSet.contains(m.getKey()))
						.collect(Collectors.toList());
			}
			Integer total = CollectionUtils.isEmpty(list) ? 0 : list.size();
			Map<String, Object> map = new HashMap<>();
			map.put("level", level.name());
			map.put("total", total);

			Map<String, String> scoreMap = reviewMarksGroup.stream()
					.collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));
			Map<String, List<Map.Entry<String, String>>> stringListMap = scoreMap.entrySet().stream()
					.collect(Collectors.groupingBy(Map.Entry::getValue));

			long AA = stringListMap.get("AA") != null ? stringListMap.get("AA").size() : 0;
			long AB = stringListMap.get("AB") != null ? stringListMap.get("AB").size() : 0;
			long AC = stringListMap.get("AC") != null ? stringListMap.get("AC").size() : 0;
			long BB = stringListMap.get("BB") != null ? stringListMap.get("BB").size() : 0;
			long BC = stringListMap.get("BC") != null ? stringListMap.get("BC").size() : 0;

			map.put("passedNum", AA + AB + AC + BB + BC);
			map.put("passedProportion", total == 0 ? 0 : (AA + AB + AC + BB + BC) * 100 / total);
			map.put("notPass", total - (AA + AB + AC + BB + BC));
			map.put("notPassProportion", total == 0 ? 0 : (total - (AA + AB + AC + BB + BC)) * 100 / total);
			result.add(map);
		}
		return result;
	}

    private Map<String, Object> all(String bmbatchId) {
        Map<String, Object> result = new HashMap<>();
        //报名信息
        List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
        //评审分组列表
        List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "2"));
        Integer totalNum = 0;
        //评审得分
        List<ZcReviewMark> zcReviewMarks = new ArrayList<>();
        if (!zcGroups.isEmpty() && !bms.isEmpty()) {
            zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bms.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
            //评审分组学员
            totalNum = zcGroupStudentMapper.selectCount(new EntityWrapper<ZcGroupStudent>().in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
        }

        Map<String, List<ZcReviewMark>> map = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));
        Map<String, String> scoreMap = map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

        Map<String, List<Map.Entry<String, String>>> listMap = scoreMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

        long AA = listMap.get("AA") != null ? listMap.get("AA").size():0;
        long AB = listMap.get("AB") != null ? listMap.get("AB").size():0;
        long AC = listMap.get("AC") != null ? listMap.get("AC").size():0;
        long BB = listMap.get("BB") != null ? listMap.get("BB").size():0;
        long BC = listMap.get("BC") != null ? listMap.get("BC").size():0;
        long CC = listMap.get("CC") != null ? listMap.get("CC").size():0;
        result.put("AA", AA);
        result.put("AAProportion", totalNum == 0 ? 0 : AA*100/totalNum);
        result.put("AB", AB);
        result.put("ABProportion", totalNum == 0 ? 0 : AB*100/totalNum);
        result.put("AC", AC);
        result.put("ACProportion", totalNum == 0 ? 0 : AC*100/totalNum);
        result.put("BB", BB);
        result.put("BBProportion", totalNum == 0 ? 0 : BB*100/totalNum);
        result.put("BC", BC);
        result.put("BCProportion", totalNum == 0 ? 0 : BC*100/totalNum);
        result.put("CC", CC);
        result.put("CCProportion", totalNum == 0 ? 0 : CC*100/totalNum);
        result.put("total", totalNum);
        return result;
    }

    private List<Map<String, Object>> byProfession(String bmbatchId) {
        List<Map<String, Object>> result = new ArrayList<>();
        //所有报名信息
        List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq(StringUtils.isNotEmpty(bmbatchId), "bmbatch_id", bmbatchId));
        if (CollectionUtils.isEmpty(bms)) {
            return result;
        }
        //职业列表
        List<ZyjdProfession> zyjdProfessions = zyjdProfessionMapper.selectBatchIds(bms.stream().map(ZyjdBm::getProfessionId).distinct().collect(Collectors.toList()));
        //评审分组列表
        List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "2"));
        if (zcGroups.isEmpty()) {
            return result;
        }
        //评审分组学员信息
        List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                .in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
        for (ZcGroup zcGroup : zcGroups) {
            //当前组的学员信息
            List<ZcGroupStudent> students = zcGroupStudents.stream().filter(s -> zcGroup.getId().equals(s.getGroupId())).collect(Collectors.toList());
            //当前组的报名信息
            List<ZyjdBm> bmList = bms.stream().filter(x -> students.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toSet()).contains(x.getStudentId())).collect(Collectors.toList());
            //评审得分
            List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bms.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
            Map<String, List<ZcReviewMark>> zcReviewMarksGroup = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));
            //按工种分组的报名信息
            Map<String, List<ZyjdBm>> listMap = bmList.stream().collect(Collectors.groupingBy(ZyjdBm::getProfessionId));
            for (Map.Entry<String, List<ZyjdBm>> entry : listMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("groupName", zcGroup.getName());
                map.put("professionName", zyjdProfessions.stream().filter(x -> x.getId().equals(entry.getKey())).findFirst().get().getName());
                map.put("total", entry.getValue().size());

                Map<String, String> scoreMap = zcReviewMarksGroup.entrySet().stream()
                        .filter(s->entry.getValue().stream().map(ZyjdBm::getId).collect(Collectors.toSet()).contains(s.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

                Map<String, List<Map.Entry<String, String>>> stringListMap = scoreMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

                long AA = stringListMap.get("AA") != null ? stringListMap.get("AA").size():0;
                long AB = stringListMap.get("AB") != null ? stringListMap.get("AB").size():0;
                long AC = stringListMap.get("AC") != null ? stringListMap.get("AC").size():0;
                long BB = stringListMap.get("BB") != null ? stringListMap.get("BB").size():0;

                map.put("AA", AA);
                map.put("AB", AB);
                map.put("AC", AC);
                map.put("BB", BB);
                map.put("AAProportion", AA*100/entry.getValue().size());
                map.put("ABProportion", AB*100/entry.getValue().size());
                map.put("ACProportion", AC*100/entry.getValue().size());
                map.put("BBProportion", BB*100/entry.getValue().size());
                map.put("proportion", (AA + AB)*100/entry.getValue().size());
                result.add(map);
            }
        }
        return result;
    }

    private List<Map<String, Object>> byCompany(String bmbatchId) {
        List<Map<String, Object>> result = new ArrayList<>();
        //报名信息
        List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
        //评审分组列表
        List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "2"));
        if (zcGroups.isEmpty()) {
            return result;
        }
		// 评审分组学员信息
		List<ZcGroupStudent> zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
				.in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
		// 学员直属单位分组
		Map<String, List<ZyjdBm>> unitNameGroup = bms.stream().filter(s -> StringUtils.isNotEmpty(s.getUnitName()))
				.collect(Collectors.groupingBy(ZyjdBm::getUnitName));

        //评审过的报名列表
        List<ZyjdBm> bmList = bms.stream().filter(b -> zcGroupStudents.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toSet()).contains(b.getStudentId())).collect(Collectors.toList());
        //评审得分
        List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bmList.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
        Map<String, List<ZcReviewMark>> zcReviewMarksGroup = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));
        for (String unitName : unitNameGroup.keySet()) {
            //属于当前单位的报名数据
            List<ZyjdBm> zyjdBms = unitNameGroup.get(unitName);
            Set<String> bmIdSet = zyjdBms.stream().map(ZyjdBm::getId).collect(Collectors.toSet());
            List<Map.Entry<String, List<ZcReviewMark>>> reviewMarksGroup = zcReviewMarksGroup.entrySet().stream().filter(m -> bmIdSet.contains(m.getKey())).collect(Collectors.toList());

            int total = zyjdBms.size();
            Map<String, Object> map = new HashMap<>();
            map.put("company", unitName);
            map.put("total", total);

            Map<String, String> scoreMap = reviewMarksGroup.stream().collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

            Map<String, List<Map.Entry<String, String>>> stringListMap = scoreMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

            long AA = stringListMap.get("AA") != null ? stringListMap.get("AA").size():0;
            long AB = stringListMap.get("AB") != null ? stringListMap.get("AB").size():0;
            long AC = stringListMap.get("AC") != null ? stringListMap.get("AC").size():0;
            long BB = stringListMap.get("BB") != null ? stringListMap.get("BB").size():0;
            long BC = stringListMap.get("BC") != null ? stringListMap.get("BC").size():0;

            map.put("passedNum", AA + AB + AC + BB + BC);
            map.put("passedProportion", (AA + AB + AC + BB + BC)*100/total);
            map.put("notPass", total - (AA + AB + AC + BB + BC));
            map.put("notPassProportion", (total - (AA + AB + AC + BB + BC))*100/total);
            result.add(map);
        }
        return result;
    }

    private List<Map<String, Object>> byGender(String bmbatchId) {
        List<Map<String, Object>> result = new ArrayList<>();
        //报名信息
        List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
        //评审分组列表
        List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "2"));
        //评审分组学员信息
        List<ZcGroupStudent> zcGroupStudents;
        if (!zcGroups.isEmpty()) {
            zcGroupStudents = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                    .in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
        } else {
            zcGroupStudents = new ArrayList<>();
        }
        List<StudentInfo> studentInfos = new ArrayList<>();
        if (!zcGroupStudents.isEmpty()) {
            studentInfos = studentInfoMapper.selectList(new EntityWrapper<StudentInfo>()
                    .isNotNull("gender")
                    .in("student_id", zcGroupStudents.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toList())));
        }
        //学员性别分组
        Map<Gender, List<StudentInfo>> genderGroup = studentInfos.stream().collect(Collectors.groupingBy(StudentInfo::getGender));

        //评审过的报名列表
        List<ZyjdBm> bmList = bms.stream().filter(b -> zcGroupStudents.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toSet()).contains(b.getStudentId())).collect(Collectors.toList());
        //评审得分
        List<ZcReviewMark> zcReviewMarks = new ArrayList<>();
        if (!bmList.isEmpty()) {
            zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bmList.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
        }
        Map<String, List<ZcReviewMark>> zcReviewMarksGroup = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));

        Integer allTotal = 0;
        long passedNum = 0;
        long notPass = 0;

        Gender gender = Gender.M;
        //当前性别的学员信息
        List<StudentInfo> infos = genderGroup.get(gender);
        List<ZyjdBm> bs = new ArrayList<>();
        Set<String> bmIdSet = new HashSet<>();
        List<Map.Entry<String, List<ZcReviewMark>>> reviewMarksGroup = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(infos)) {
            //当前性别的报名信息
            bs = bms.stream().filter(b -> infos.stream().map(StudentInfo::getStudentId).collect(Collectors.toSet()).contains(b.getStudentId())).collect(Collectors.toList());
            bmIdSet = bs.stream().map(ZyjdBm::getId).collect(Collectors.toSet());
            Set<String> finalBmIdSet = bmIdSet;
            reviewMarksGroup = zcReviewMarksGroup.entrySet().stream().filter(m -> finalBmIdSet.contains(m.getKey())).collect(Collectors.toList());
        }

        Integer total = genderGroup.get(gender) == null ? 0 : genderGroup.get(gender).size();
        Map<String, Object> mapM = new HashMap<>();
        mapM.put("gender", "男");
        mapM.put("total", total);
        allTotal += total;

        Map<String, String> scoreMap = reviewMarksGroup.stream().collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

        Map<String, List<Map.Entry<String, String>>> stringListMap = scoreMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

        long AA = stringListMap.get("AA") != null ? stringListMap.get("AA").size():0;
        long AB = stringListMap.get("AB") != null ? stringListMap.get("AB").size():0;
        long AC = stringListMap.get("AC") != null ? stringListMap.get("AC").size():0;
        long BB = stringListMap.get("BB") != null ? stringListMap.get("BB").size():0;
        long BC = stringListMap.get("BC") != null ? stringListMap.get("BC").size():0;

        mapM.put("passedNum", AA + AB + AC + BB + BC);
        passedNum += (AA + AB + AC + BB + BC);
        mapM.put("passedProportion",total == 0 ? 0 : (AA + AB + AC + BB + BC)*100/total);
        mapM.put("notPass", total - (AA + AB + AC + BB + BC));
        notPass += (total - (AA + AB + AC + BB + BC));
        mapM.put("notPassProportion",total == 0 ? 0 : (total - (AA + AB + AC + BB + BC))*100/total);
        result.add(mapM);

        gender = Gender.F;
        //当前性别的学员信息
        List<StudentInfo> infosF = genderGroup.get(gender);
        List<ZyjdBm> bsF = new ArrayList<>();
        Set<String> bmIdSetF = new HashSet<>();
        List<Map.Entry<String, List<ZcReviewMark>>> reviewMarksGroupF = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(infosF)) {
            //当前性别的报名信息
            bsF = bms.stream().filter(b -> infosF.stream().map(StudentInfo::getStudentId).collect(Collectors.toSet()).contains(b.getStudentId())).collect(Collectors.toList());
            bmIdSetF = bsF.stream().map(ZyjdBm::getId).collect(Collectors.toSet());
            Set<String> finalBmIdSetF = bmIdSetF;
            reviewMarksGroupF = zcReviewMarksGroup.entrySet().stream().filter(m -> finalBmIdSetF.contains(m.getKey())).collect(Collectors.toList());
        }
        total = genderGroup.get(gender) == null ? 0 : genderGroup.get(gender).size();
        Map<String, Object> mapF = new HashMap<>();
        mapF.put("gender", "女");
        mapF.put("total", total);
        allTotal += total;

        Map<String, String> scoreMap2 = reviewMarksGroupF.stream().collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

        Map<String, List<Map.Entry<String, String>>> stringListMap2 = scoreMap2.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

        long AA2 = stringListMap2.get("AA") != null ? stringListMap2.get("AA").size():0;
        long AB2 = stringListMap2.get("AB") != null ? stringListMap2.get("AB").size():0;
        long AC2 = stringListMap2.get("AC") != null ? stringListMap2.get("AC").size():0;
        long BB2 = stringListMap2.get("BB") != null ? stringListMap2.get("BB").size():0;
        long BC2 = stringListMap2.get("BC") != null ? stringListMap2.get("BC").size():0;

        mapF.put("passedNum", AA2 + AB2 + AC2 + BB2 + BC2);
        passedNum += (AA2 + AB2 + AC2 + BB2 + BC2);
        mapF.put("passedProportion", total == 0 ? 0 : (AA2 + AB2 + AC2 + BB2 + BC2)*100/total);
        mapF.put("notPass", total - (AA2 + AB2 + AC2 + BB2 + BC2));
        notPass += (total - (AA2 + AB2 + AC2 + BB2 + BC2));
        mapF.put("notPassProportion", total == 0 ? 0 : (total - (AA2 + AB2 + AC2 + BB2 + BC2))*100/total);
        result.add(mapF);

        Map<String, Object> map = new HashMap<>();
        map.put("gender", "合计");
        map.put("total", allTotal);
        map.put("passedNum", passedNum);
        map.put("passedProportion", allTotal == 0 ? 0 : passedNum*100/allTotal);
        map.put("notPass", notPass);
        map.put("notPassProportion", allTotal == 0 ? 0 : notPass*100/allTotal);
        result.add(map);
        return result;
    }

    //获取待分组学员信息
    public Object getNotGroupStudent(String bmbatchId, String type) {
        List<StudentInfo> studentInfos = zcGroupStudentMapper.notGroupStudent(bmbatchId, type);
        Map<String, List<StudentInfo>> studentGroup = studentInfos.stream().collect(Collectors.groupingBy(StudentInfo::getStudentId));
        List<ZyjdBm> zyjdBms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
        Set<String> studentIdSet = studentInfos.stream().map(StudentInfo::getStudentId).collect(Collectors.toSet());
        List<ZyjdBm> bms = zyjdBms.stream().filter(b->studentIdSet.contains(b.getStudentId())).collect(Collectors.toList());
        //工种id列表
        List<String> professionIds = bms.stream().map(ZyjdBm::getProfessionId).collect(Collectors.toList());
        //工种列表
        List<ZyjdProfession> zyjdProfessions = new ArrayList<>();
        //工种分组
        Map<String, List<ZyjdProfession>> professionGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(professionIds)) {
            zyjdProfessions = zyjdProfessionMapper.selectBatchIds(professionIds);
            professionGroup = zyjdProfessions.stream().collect(Collectors.groupingBy(ZyjdProfession::getId));
        }
        List<Map<String, Object>> list = new ArrayList<>();
        for (ZyjdBm bm : bms) {
            JSONObject json = (JSONObject) JSONObject.toJSON(bm);
            json.put("studentName", studentGroup.get(bm.getStudentId()).get(0).getName());
            json.put("professionName", professionGroup.isEmpty() ? "" : professionGroup.get(bm.getProfessionId()).get(0).getName());
            json.put("applyTechLevel", bm.getApplyTechLevel().getDesc());
            list.add(json);
        }
        return list;
    }

    /**
     * 分数转换
     */
    private String scoreConvert(List<ZcReviewMark> zcReviewMarks) {
        List<ZcReviewMark> reviewMarks = zcReviewMarks.stream().sorted(Comparator.comparing(ZcReviewMark::getScore).reversed()).collect(Collectors.toList());
        StringBuilder score = new StringBuilder();
        for (int i = 0; i < 2; i++) {
            if (reviewMarks.size() == i) {
                continue;
            }
            ZcReviewMark reviewMark = reviewMarks.get(i);
            if (reviewMark.getScore() >= 80) {
                score = score.append("A");
            } else if (reviewMark.getScore() >= 60 && reviewMark.getScore() < 80) {
                score = score.append("B");
            } else if (reviewMark.getScore() >= 0 && reviewMark.getScore() < 60) {
                score = score.append("C");
            }
        }
        return score.toString();
    }

    /**
     * 成绩转换
     */
    private String gradeConvert(List<ZcReviewMark> zcReviewMarks) {
        List<ZcReviewMark> reviewMarks = zcReviewMarks.stream().sorted(Comparator.comparing(ZcReviewMark::getScore).reversed()).collect(Collectors.toList());
        StringBuilder grade = new StringBuilder();
        for (int i = 0; i < 2; i++) {
            if (reviewMarks.size() == i) {
                continue;
            }
            ZcReviewMark reviewMark = reviewMarks.get(i);
            grade.append(reviewMark.getGrade());
        }
        return grade.toString();
    }

    /**
     * 按工种统计，不分某一组
     * @param bmbatchId 批次id
     */
    public List<Map<String, Object>> byProfessionStatistics(String bmbatchId) {
        List<Map<String, Object>> result = new ArrayList<>();
        //所有报名信息
        List<ZyjdBm> bms = zyjdBmMapper.selectList(new EntityWrapper<ZyjdBm>().eq(StringUtils.isNotEmpty(bmbatchId), "bmbatch_id", bmbatchId));
        if (CollectionUtils.isEmpty(bms)) {
            return result;
        }
        //职业列表
        List<ZyjdProfession> zyjdProfessions = zyjdProfessionMapper.selectBatchIds(bms.stream().map(ZyjdBm::getProfessionId).distinct().collect(Collectors.toList()));
        //评审分组列表
        List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
                .eq("bmbatch_id", bmbatchId)
                .eq("type", "2"));
        if (zcGroups.isEmpty()) {
            return result;
        }
        //评审分组学员信息
        List<ZcGroupStudent> students = zcGroupStudentMapper.selectList(new EntityWrapper<ZcGroupStudent>()
                .in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
        //评审过的报名信息
        bms = bms.stream().filter(x -> students.stream().map(ZcGroupStudent::getStudentId).collect(Collectors.toSet()).contains(x.getStudentId())).collect(Collectors.toList());
        //评审得分
        List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bms.stream().map(ZyjdBm::getId).collect(Collectors.toList())));
        Map<String, List<ZcReviewMark>> zcReviewMarksGroup = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));
        //按工种分组的报名信息
        Map<String, List<ZyjdBm>> listMap = bms.stream().collect(Collectors.groupingBy(ZyjdBm::getProfessionId));
        for (Map.Entry<String, List<ZyjdBm>> entry : listMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("professionName", zyjdProfessions.stream().filter(x -> x.getId().equals(entry.getKey())).findFirst().get().getName());

            Map<String, String> scoreMap = zcReviewMarksGroup.entrySet().stream()
                    .filter(s->entry.getValue().stream().map(ZyjdBm::getId).collect(Collectors.toSet()).contains(s.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> this.gradeConvert(e.getValue())));

            Map<String, List<Map.Entry<String, String>>> stringListMap = scoreMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue));

            long AA = stringListMap.get("AA") != null ? stringListMap.get("AA").size():0;
            long AB = stringListMap.get("AB") != null ? stringListMap.get("AB").size():0;
            long AC = stringListMap.get("AC") != null ? stringListMap.get("AC").size():0;
            long BB = stringListMap.get("BB") != null ? stringListMap.get("BB").size():0;

            map.put("AA", AA);
            map.put("AB", AB);
            map.put("AC", AC);
            map.put("BB", BB);
            map.put("AAProportion", AA*100/entry.getValue().size());
            map.put("ABProportion", AB*100/entry.getValue().size());
            map.put("ACProportion", AC*100/entry.getValue().size());
            map.put("BBProportion", BB*100/entry.getValue().size());
            map.put("total", AA+AB+AC+BB);
            map.put("proportion", (AA + AB)*100/entry.getValue().size());
            result.add(map);
        }
        return result;
    }
}
