package com.xunw.jxjy.model.sys.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.sys.entity.Notice;
import com.xunw.jxjy.model.sys.entity.NoticeCategory;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.NoticeCategoryMapper;
import com.xunw.jxjy.model.sys.mapper.NoticeMapper;
import com.xunw.jxjy.model.sys.params.NoticeCategoryQueryParams;

@Service
public class NoticeCategoryService extends BaseCRUDService<NoticeCategoryMapper, NoticeCategory>{

	@Autowired
	private NoticeMapper noticeMapper;
	// 查询

	public Page<Map<String,Object>> pageQuery(NoticeCategoryQueryParams params) {
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	// 删除
	@Transactional
	public void deleteById(String id) {
		EntityWrapper<NoticeCategory> categoryWrapper = new EntityWrapper<>();
		categoryWrapper.eq("parent_id", id);
		List<NoticeCategory> noticeCategories = mapper.selectList(categoryWrapper);
		if (noticeCategories.size() > 0) {
			throw BizException.withMessage("当前分类下存在子分类，请先删除子分类");
		}
		EntityWrapper<Notice> wrapper = new EntityWrapper<>();
		wrapper.eq("category_id", id);
		List<Notice> list = noticeMapper.selectList(wrapper);
		if (list.size() > 0) {
			throw BizException.withMessage("当前分类下已存在通知公告信息，请勿删除");
		}
		mapper.deleteById(id);
	}

	// 修改
	@Transactional
	public boolean edit(String id, String name, Integer sort, String remark,String status, User user) {
		// 通过id查询对象
		NoticeCategory noticeCategory = mapper.selectById(id);
		if (noticeCategory == null) {
			throw BizException.PARAMS_ERROR;
		}
		noticeCategory.setName(name);
		noticeCategory.setSort(sort);
		noticeCategory.setRemark(remark);
		noticeCategory.setUpdatorId(user.getId());
		noticeCategory.setUpdateTime(new Date());
		noticeCategory.setStatus(Zt.findByEnumName(status));
		mapper.updateById(noticeCategory);
		return true;
	}

	// 新增
	public boolean add(String name, Integer sort, String remark, User user, String parentId,String status,String hostOrgId) {
		NoticeCategory noticeCategory = new NoticeCategory();
		noticeCategory.setId(BaseUtil.generateId());
		noticeCategory.setName(name);
		noticeCategory.setSort(sort);
		noticeCategory.setRemark(remark);
		noticeCategory.setCreatorId(user.getId());
		noticeCategory.setParentId(parentId);
		noticeCategory.setCreateTime(new Date());
		noticeCategory.setHostOrgId(hostOrgId);
		noticeCategory.setStatus(Zt.findByEnumName(status));
		mapper.insert(noticeCategory);
		return true;
	}


	public Object noticeTree(String hostOrgId){
		EntityWrapper<NoticeCategory> wrapper = new EntityWrapper<NoticeCategory>();
		wrapper.eq("host_org_id", hostOrgId);
		wrapper.eq("status", Zt.OK);
		//wrapper.lt("rownum", 4);
		wrapper.orderBy("sort",true);
		List<NoticeCategory> noticeCategoryList = mapper.selectList(wrapper);
		noticeCategoryList = noticeCategoryList.subList(0,noticeCategoryList.size()<3?noticeCategoryList.size():3);
		for (int i = 0; i < noticeCategoryList.size(); i++) {
			EntityWrapper<Notice> noticeWrapper = new EntityWrapper<Notice>();
			noticeWrapper.eq("category_id", noticeCategoryList.get(i).getId());
			noticeWrapper.eq("status", Zt.OK);
			noticeWrapper.orderBy("is_to_top",false).orderBy("create_time",false);

			List<Notice> xmList = noticeMapper.selectList(noticeWrapper);
			xmList = xmList.subList(0,xmList.size()<3?xmList.size():3);

			noticeCategoryList.get(i).setNoticeList(xmList);
		}



		return noticeCategoryList;
	}

}
