package com.xunw.jxjy.model.zypx.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.XmNoticeStatus;
import com.xunw.jxjy.model.zypx.entity.ZypxXmNotice;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmNoticeMapper;
import com.xunw.jxjy.model.zypx.params.ZypxXmCommonQueryParams;

@Service
public class ZypxXmNoticeService extends BaseCRUDService<ZypxXmNoticeMapper, ZypxXmNotice> {

	@Transactional
	public int delete(String ids) {
		int num = 0;
		for (String id : ids.split(",")) {
			num += mapper.deleteById(id);
		}
		return num;
	}

	@Transactional
	public int setStatus(String ids, XmNoticeStatus status, String userId) {
		status = status == null ? XmNoticeStatus.PUBLISH : status;
		int num = 0;
		for (String id : ids.split(",")) {
			ZypxXmNotice zypxXmNotice = mapper.selectById(id);
			zypxXmNotice.setStatus(status);
			num += mapper.updateById(zypxXmNotice);
		}
		return num;
	}

	@SuppressWarnings("unchecked")
	public Page<ZypxXmNotice> pageQuery(ZypxXmCommonQueryParams params) {
		params.setRecords(mapper.pageQuery(params.getCondition(), params));
		return params;
	}

}