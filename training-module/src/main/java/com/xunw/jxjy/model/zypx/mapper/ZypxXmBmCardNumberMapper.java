package com.xunw.jxjy.model.zypx.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ZypxXmBmCardNumberMapper extends BaseMapper<ZypxXmBmCardNumber> {

    List<ZypxXmBmCardNumber> findByBmIdAndCardNumber(@Param("bmId") String bmId, @Param("cardNumber") String cardNumber);

    List<ZypxXmBmCardNumber> findByBmId(@Param("bmId") String bmId);
}
