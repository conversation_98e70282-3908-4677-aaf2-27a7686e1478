package com.xunw.jxjy.model.zypx.service;

import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.NumUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.AnswerStatus;
import com.xunw.jxjy.model.enums.QaQuestionType;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.dto.qa.QaOption;
import com.xunw.jxjy.model.zypx.dto.qa.QaPaper;
import com.xunw.jxjy.model.zypx.dto.qa.QaQuestion;
import com.xunw.jxjy.model.zypx.entity.ZypxQa;
import com.xunw.jxjy.model.zypx.entity.ZypxQaAnswer;
import com.xunw.jxjy.model.zypx.entity.ZypxQaAnswerDetail;
import com.xunw.jxjy.model.zypx.mapper.ZypxQaAnswerMapper;
import com.xunw.jxjy.model.zypx.params.ZypxQaAnswerQueryParams;
import com.xunw.jxjy.paper.utils.ModelHelper;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;


@Service
public class ZypxQaAnswerService extends BaseCRUDService<ZypxQaAnswerMapper, ZypxQaAnswer> {

    @Autowired
    private ZypxQaAnswerDetailService detailService;
    @Autowired
    private ZypxQaService qaService;

    //分页查询
    public Page page(ZypxQaAnswerQueryParams params) {
        Map<String, Object> condition = params.getCondition();
        if (params.getStatus() != null) {
            condition.put("status", params.getStatus().name());
        } else {
            condition.put("status", AnswerStatus.SUBMITTED.name());
        }
        List<Map<String, Object>> page = mapper.pageQuery(condition, params);
        params.setRecords(page);
        return params;
    }

    //分页查询
    public Page answerStuPage(ZypxQaAnswerQueryParams params) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("qaId", params.getQaId());
        condition.put("xmId", params.getXmId());
        condition.put("ksWord", params.getKsWord());
        if (params.getStatus() != null) {
            condition.put("status", params.getStatus().name());
        } else {
            condition.put("status", AnswerStatus.SUBMITTED.name());
        }
        List<Map<String, Object>> page = mapper.answerStuPage(condition, params);
        params.setRecords(page);
        return params;
    }

    //批量导出学员问卷
    public void exportAnswerStu(ZypxQaAnswerQueryParams params, OutputStream os) throws Exception {
        Map<String, Object> condition = new HashMap<>();
        condition.put("qaId", params.getQaId());
        condition.put("xmId", params.getXmId());
        condition.put("ksWord", params.getKsWord());
        condition.put("status", AnswerStatus.SUBMITTED.name());
        params.setSize(Integer.MAX_VALUE);

        ZypxQa qa = qaService.selectById(params.getQaId());
        List<Map<String, Object>> list;
        if ("1".equals(qa.getIsForXm())) {
            list = mapper.answerStuPage(condition, params);
        } else {
            list = mapper.pageQuery(condition, params);
        }

        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            QaPaper qaContent = ModelHelper.convertObject(String.valueOf(map.get("content")));
            int i = 1;
            for (QaQuestion question : qaContent.getContent()) {
                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("xmmc", map.get("title"));
                tempMap.put("xm", map.get("userName"));
                tempMap.put("sfzh", map.get("sfzh"));
                tempMap.put("bh", i);
                tempMap.put("text", question.getText());

                //答案拼接选择内容
                List<QaOption> options = question.getOptions();
                String answer = question.getAnswer();
                if (CollectionUtils.isNotEmpty(options)) {
                    Optional<QaOption> optionOptional = question.getOptions().stream().filter(q -> Objects.equals(q.getAlisa(), question.getAnswer())).findFirst();
                    String option = optionOptional.isPresent() ? optionOptional.get().getText() : "";
                    answer = question.getAnswer() + "、" + option;
                }
                tempMap.put("answer", answer);
                result.add(tempMap);
                i++;
            }
        }

        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("学员问卷答案汇总表", 0);
        int row = 0;
        {
            int i = 0;
            if ("1".equals(qa.getIsForXm())) {
                ws.addCell(new Label(i, row, "项目名称", OfficeToolExcel.getTitle()));
                ws.setColumnView(i++, 30);
            }

            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "题目编号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "题干", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 50);
            ws.addCell(new Label(i, row, "答案", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 30);
        }
        row = 1;
        for (Map<String, Object> map : result) {
            if (map == null) {
                continue;
            } else {
                int col = 0;
                if ("1".equals(qa.getIsForXm())) {
                    ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("xmmc") != null ? map.get("xmmc") : ""),
                            OfficeToolExcel.getNormolCell()));
                }
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("xm") != null ? map.get("xm") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("bh") != null ? map.get("bh") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("text") != null ? map.get("text") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("answer") != null ? map.get("answer") : ""),
                        OfficeToolExcel.getNormolCell()));
                row++;
            }
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    //问卷作答详情
    public Object getByAnswer(String id) {
        //获取答卷信息
        Map<String, Object> answerMap = new HashMap<>();
        ZypxQaAnswer zypxQaAnswer = mapper.selectById(id);
        QaPaper qaContent = ModelHelper.convertObject(zypxQaAnswer.getContent());
        //获取答卷试题详情
        EntityWrapper<ZypxQaAnswerDetail> qaAnswerDetailEntityWrapper = new EntityWrapper<>();
        qaAnswerDetailEntityWrapper.eq("qaa_id", id);
        List<ZypxQaAnswerDetail> qaAnswerDetails = detailService
                .selectList(qaAnswerDetailEntityWrapper);
        if (qaAnswerDetails.size() > 0) {
            qaAnswerDetails.forEach(qaAnswerDetail -> {
                qaContent.getContent().stream().filter(a -> a.getQuesId().equals(qaAnswerDetail.getQuesId())).forEach(question -> {
                    question.setAnswer(qaAnswerDetail.getAnswer());
                });
            });
        }
        answerMap.put("content", qaContent.getContent());
        return answerMap;
    }

    //获取当前用户未作答问卷
    public Object notAnswerList(String userId) {
        return mapper.notAnswerList(userId);
    }

	//保存问卷作答结果
	@Transactional(rollbackFor = Exception.class)
	public Object add(JSONObject json,User user){
		String qaId = json.getString("qaId");
		if (StringUtils.isEmpty(qaId)){
			throw BizException.withMessage("问卷id必填");
		}
		String answerId = json.getString("answerId");
		EntityWrapper<ZypxQaAnswer> answerEntityWrapper = new EntityWrapper<>();
		answerEntityWrapper.eq("qa_id",qaId);
		answerEntityWrapper.eq("user_id",user.getId());
		Integer count = selectCount(answerEntityWrapper);
		if (StringUtils.isEmpty(answerId) && count > 0){
			throw BizException.withMessage("作答记录已存在，请传作答ID");
		}
		ZypxQa zypxqa = qaService.selectById(qaId);
		if (zypxqa.getEndTime().before(new Date())){
			throw BizException.withMessage("已经过了问卷调查截止时间！");
		}
		if (StringUtils.isEmpty(answerId)){
			String qaaId = BaseUtil.generateId2();
			ZypxQaAnswer qaAnswer =
					new ZypxQaAnswer(qaaId,qaId,zypxqa.getContent(),AnswerStatus.SAVED,new Date(),user.getId());
			List<ZypxQaAnswerDetail> list = buildQaAnswerDetail(json,qaId,qaaId);
			
			if (list.size() > 0) {
				QaPaper qaPaperp = ModelHelper.convertObject(zypxqa.getContent());
				qaPaperp.getContent().forEach(question->{
					list.stream().filter(q -> q.getQuesId().equals(question.getQuesId())).forEach(c->{
						question.setAnswer(c.getAnswer());
					});
				});
				qaAnswer.setContent(ModelHelper.formatObject(qaPaperp));
			}
			mapper.insert(qaAnswer);
			if (list.size() > 0){
				DBUtils.insertBatch(list,10,ZypxQaAnswerDetail.class);
			}
		}
		else if (StringUtils.isNotEmpty(answerId)){
			ZypxQaAnswer qaAnswer = selectById(answerId);
			if (qaAnswer == null){
				throw BizException.withMessage("作答ID："+answerId+"的问卷作答对象为空");
			}
			if (qaAnswer.getStatus() == AnswerStatus.SUBMITTED){
				throw BizException.withMessage("该问卷已提交，不能作答！");
			}
			qaAnswer.setSaveTime(new Date());
			qaAnswer.setUserId(user.getId());
			//删除作答详情
			EntityWrapper<ZypxQaAnswerDetail> detailEntityWrapper = new EntityWrapper<>();
			detailEntityWrapper.eq("qaa_id",answerId);
			detailService.deleteList(detailEntityWrapper);
			//重新插入作答详情
			List<ZypxQaAnswerDetail> list = buildQaAnswerDetail(json,qaId,answerId);
			if (list.size()>0) {
				QaPaper qaPaperp = ModelHelper.convertObject(zypxqa.getContent());
				qaPaperp.getContent().forEach(question->{
					list.stream().filter(q -> q.getQuesId().equals(question.getQuesId())).forEach(c->{
						question.setAnswer(c.getAnswer());
					});
				});
				qaAnswer.setContent(ModelHelper.formatObject(qaPaperp));
			}
			mapper.updateById(qaAnswer);
			if (list.size() > 0){
				DBUtils.insertBatch(list,10,ZypxQaAnswerDetail.class);
			}
		}
		return true;
	}
    //获取当前用户已作答问卷
    public Object myAnswerList(ZypxQaAnswerQueryParams params, String userId) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("userId", userId);
        if (params.getStatus() != null) {
            condition.put("status", params.getStatus().name());
        }
        return mapper.myAnswerList(condition);
    }

	//问卷作答提交
	public Object myAnswerSubmit(String answerId){
		ZypxQaAnswer qaAnswer = selectById(answerId);
		if (qaAnswer == null){
			throw BizException.withMessage("作答ID："+answerId+"的问卷作答对象为空");
		}
		ZypxQa qa = qaService.selectById(qaAnswer.getQaId());
		if (qa.getEndTime().before(new Date())){
			throw BizException.withMessage("已经过了问卷调查的提交截止时间！");
		}
		qaAnswer.setSubmitTime(new Date());
		qaAnswer.setStatus(AnswerStatus.SUBMITTED);
		updateById(qaAnswer);
		return true;
	}

    public List<ZypxQaAnswerDetail> buildQaAnswerDetail(JSONObject json, String qaId, String qaaId) {
        ZypxQa qa = qaService.selectById(qaId);
        QaPaper qaContent = ModelHelper.convertObject(qa.getContent());
        JSONArray answerContentArray = json.getJSONArray("answerContent");
        if (answerContentArray == null || answerContentArray.size() == 0) {
            throw BizException.withMessage("缺答题信息");
        }
        List<ZypxQaAnswerDetail> list = new ArrayList<>();
        for (int i = 0; i < answerContentArray.size(); i++) {
            JSONObject answerContent = answerContentArray.getJSONObject(i);
            String answer = answerContent.getString("answer");
            String type = answerContent.getString("type");
            String quesId = answerContent.getString("id");
            ZypxQaAnswerDetail detail = new ZypxQaAnswerDetail();
            detail.setId(BaseUtil.generateId2());
            detail.setQaaId(qaaId);
            detail.setQuesId(quesId);
            if (!answer.equals("null")) {
                detail.setAnswer(answer);
            }
            detail.setQuesType(QaQuestionType.findByEnumName(type));
            list.add(detail);
        }
        //校验必填试题是否作答
        qaContent.getContent().forEach(question -> {
            list.stream().filter(detail -> detail.getQuesId().equals(question.getQuesId())).forEach(a -> {
                if (question.getRequired() && StringUtils.isEmpty(a.getAnswer())) {
                    throw BizException.withMessage("试题" + question.getText() + "是必答题");
                }

            });
        });
        return list;

    }

    /**
     * 统计分析
     */
    public Object statistices(ZypxQaAnswerQueryParams params) {
        Map<String, Object> result = new HashMap<String, Object>();
        ZypxQa qa = qaService.selectById(params.getQaId());
        QaPaper qaContent = ModelHelper.convertObject(qa.getContent());
        //获取统计答案集合
        List<Map<String, String>> statisticesList = mapper.statistices(params.getQaId());

        int qaTotal;
        if ("1".equals(qa.getIsForXm())) {
            qaTotal = mapper.getStuCountByQaId(params.getQaId());//问卷总数量
        } else {
            qaTotal = mapper.getUserCountByQaId(params.getQaId());//问卷总数量
        }

        result.put("title", qa.getName());//问卷名称
        result.put("viewCount", BaseUtil.getInt(qa.getCount(), 0));//浏览量
        result.put("viewRate", NumUtils.toPercent(BaseUtil.getInt(qa.getCount(), 0), qaTotal));//浏览率
        EntityWrapper<ZypxQaAnswer> answerEntityWrapper = new EntityWrapper<>();
        answerEntityWrapper.eq("qa_id", params.getQaId());
        answerEntityWrapper.eq("status", "SUBMITTED");
        Integer recycleCount = selectCount(answerEntityWrapper);
        result.put("recycleCount", recycleCount);//回收量
        //回收率
        result.put("recycleRate", NumUtils.toPercent(recycleCount, qaTotal));

        List<Map<String, Object>> list = new ArrayList<>();
        for (QaQuestion que : qaContent.getContent()) {
            if (que.getQuesType() == QaQuestionType.SINGLECHOICE || que.getQuesType() == QaQuestionType.MULTIPLECHOICE) {
                Map<String, Object> listMap = new HashMap<>();
                listMap.put("id", que.getQuesId());
                listMap.put("content", que.getText());
                listMap.put("quesType", que.getQuesType());

                List<Map<String, String>> mapList = statisticesList.stream().filter(item -> (item.get("quesid").equals(que.getQuesId()) && StringUtils.isNotEmpty(item.get("answer")))).collect(Collectors.toList());
//                int totalCount = mapList.stream().map(obj -> obj.get("answer").replace(Constants.TM_SPLITER, "")).collect(Collectors.joining("")).length();

                List<Map<String, Object>> tempList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(que.getOptions())) {
                    for (QaOption opt : que.getOptions()) {
                        Long count = mapList.stream().filter(item -> item.get("answer").indexOf(opt.getAlisa()) > -1).count();
                        Map<String, Object> tempMap = new HashMap<>();
                        tempMap.put("value", opt.getAlisa());
                        tempMap.put("count", count.toString());
                        tempMap.put("percent", NumUtils.toPercent(count.intValue(), mapList.size()));
                        tempList.add(tempMap);
                    }
                    listMap.put("selectOptions", tempList);
                }
                list.add(listMap);
            }
        }
        result.put("list", list);
        return result;
    }

    //批量导出学员问卷统计分析结果
    public void exportStatistices(ZypxQaAnswerQueryParams params, OutputStream os) throws Exception {
        ZypxQa qa = qaService.selectById(params.getQaId());
        QaPaper qaContent = ModelHelper.convertObject(qa.getContent());
        //获取统计答案集合
        List<Map<String, String>> statisticesList = mapper.statistices(params.getQaId());

        List<Map<String, Object>> result = new ArrayList<>();
        int bh = 1;
        for (QaQuestion que : qaContent.getContent()) {
            if (que.getQuesType() == QaQuestionType.SINGLECHOICE || que.getQuesType() == QaQuestionType.MULTIPLECHOICE) {
                List<Map<String, String>> mapList = statisticesList.stream().filter(item -> (item.get("quesid").equals(que.getQuesId()) && StringUtils.isNotEmpty(item.get("answer")))).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(que.getOptions())) {
                    for (QaOption opt : que.getOptions()) {
                        Long count = mapList.stream().filter(item -> item.get("answer").indexOf(opt.getAlisa()) > -1).count();
                        Map<String, Object> tempMap = new HashMap<String, Object>();
                        tempMap.put("bh", bh);
                        tempMap.put("text", que.getText());
                        tempMap.put("alisa", opt.getAlisa());
                        tempMap.put("count", count.toString());
                        tempMap.put("percent", NumUtils.toPercent(count.intValue(), mapList.size()) + '%');
                        result.add(tempMap);
                    }
                    bh++;
                }
            }
        }

        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("学员问卷统计分析表", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "题目编号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "题干", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 50);
            ws.addCell(new Label(i, row, "选项", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "选项数量", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "选项比率", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
        }
        row = 1;
        for (Map<String, Object> map : result) {
            if (map == null) {
                continue;
            } else {
                int col = 0;
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("bh") != null ? map.get("bh") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("text") != null ? map.get("text") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("alisa") != null ? map.get("alisa") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("count") != null ? map.get("count") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("percent") != null ? map.get("percent") : ""),
                        OfficeToolExcel.getNormolCell()));
                row++;
            }
        }
        wwb.write();
        wwb.close();
        os.flush();
    }
  
}
