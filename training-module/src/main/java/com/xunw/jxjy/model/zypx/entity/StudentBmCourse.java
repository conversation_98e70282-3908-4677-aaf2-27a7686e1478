package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 *学员个人课程购买表
 *免费的课程不在此表内
 */
@TableName("biz_student_bm_course")
public class StudentBmCourse implements Serializable {
	
	private static final long serialVersionUID = -1305133684037501019L;

	//主键id
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	//学员ID
	@TableField("student_id")
	private String studentId;
	
	//课程ID
	@TableField("course_id")
	private String courseId;
	
	//是否已经支付 是 0 否  
	@TableField("is_payed")
	private String isPayed;
	
	//创建用户id
	@TableField("creator_id")
	private String creatorId;
	
	//创建时间
	@TableField("create_time")
	private Date createTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getIsPayed() {
		return isPayed;
	}

	public void setIsPayed(String isPayed) {
		this.isPayed = isPayed;
	}
}
