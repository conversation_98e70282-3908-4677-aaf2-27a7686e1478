package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.dto.PlanXmTreeNode;
import com.xunw.jxjy.model.enums.OpenStudyType;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.enums.SysSettingEnum;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.entity.SystemSetting;
import com.xunw.jxjy.model.sys.mapper.DictMapper;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.utils.XSSFCellStyleUtil;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zypx.dto.ZypxXmDto;
import com.xunw.jxjy.model.zypx.entity.PlanDetail;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.mapper.*;
import com.xunw.jxjy.model.zypx.params.PlanXmTreeQueryParams;
import com.xunw.jxjy.model.zypx.params.ZypxXmQueryParams;
import com.xunw.jxjy.model.zypx.vo.ZypxPlanXmVo;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ZypxXmService extends BaseCRUDService<ZypxXmMapper, ZypxXm>{

	private static final String PUBLIC_COURSE = "公共课";

	@Autowired
	private ZypxBmMapper bmMapper;
	@Autowired
	private ZypxXmCourseSettingMapper xmCourseSettingMapper;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private PlanDetailMapper planDetailMapper;
	@Autowired
	private DictMapper dictMapper;
	@Autowired
	private PxxmXmBmAdviceMapper pxxmXmBmAdviceMapper;
	@Autowired
	private PlanMapper planMapper;
    @Autowired
    private ZyjdBmMapper zyjdBmMapper;
	@Autowired
	private CockpitMapper cockpitMapper;
	/**
	 * 添加项目
	 */
	@Transactional
	public void add(ZypxXmDto xmDto, String userId) {
		this.doXmParamsCheck(xmDto);
		xmDto.setIsOpenCamera(StringUtils.isEmpty(xmDto.getIsOpenCamera()) ? Constants.NO : xmDto.getIsOpenCamera());
		// 创建对象
		ZypxXm zypxXm = new ZypxXm();
		zypxXm.setLimitCount(xmDto.getLimitCount());
		zypxXm.setSerialNumber(xmDto.getSerialNumber());
		zypxXm.setTitle(xmDto.getTitle());
		zypxXm.setStartTime(DateUtils.parse(xmDto.getStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setEndTime(DateUtils.parse(xmDto.getEndTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setTypeId(xmDto.getTypeId());
		zypxXm.setTrainees(xmDto.getTrainees());
		zypxXm.setAmount(xmDto.getAmount());
		zypxXm.setJtbmStartTime(DateUtils.parse(xmDto.getJtbmStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setJtbmEndTime(DateUtils.parse(xmDto.getJtbmEndTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setGrbmStartTime(DateUtils.parse(xmDto.getGrbmStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setGrbmEndTime(DateUtils.parse(xmDto.getGrbmEndTime(), "yyyy-MM-dd HH:mm"));
		//ycw modify 新增给已报名的学员自动推送短信设置
		xmDto.setIsAutoSendSms(StringUtils.isEmpty(xmDto.getIsAutoSendSms()) ? Constants.NO : xmDto.getIsAutoSendSms());
		zypxXm.setIsAutoSendSms(xmDto.getIsAutoSendSms());
		EntityWrapper<ZypxXm> xmbhEntityWrapper = new EntityWrapper<ZypxXm>();
		xmbhEntityWrapper.eq("serial_number", xmDto.getSerialNumber());
		List<ZypxXm> checkList = mapper.selectList(xmbhEntityWrapper);
		if (checkList.size() > 0) {
			throw BizException.withMessage("项目编号重复");
		}
		zypxXm.setIsAllowGrbm(xmDto.getIsAllowGrbm());
		String studyEndTime = StringUtils.isEmpty(xmDto.getStudyEndTime()) ? xmDto.getEndTime() : xmDto.getStudyEndTime();
		Date spxxjssjDate = DateUtils.parse(studyEndTime, "yyyy-MM-dd HH:mm");
		zypxXm.setStudyEndTime(spxxjssjDate);
		zypxXm.setStatus(xmDto.getStatus());
		// 获取用户id
		zypxXm.setCreatorId(userId);
		zypxXm.setCreateTime(new Date());
		// 创建主键id
		String id = BaseUtil.generateId();
		zypxXm.setId(id);
		//获取平台配置信息，注意不是获取主办单位的配置信息
		SystemSetting systemSetting = systemSettingService.getGlobalSetting();
		if (systemSetting == null || StringUtils.isEmpty(systemSetting.getContent())) {
			throw BizException.withMessage("检测到系统参数尚未设置，请仔细检查系统管理中的系统参数是否已经设置完毕");
		}
		JSONObject jsonObject = JSONObject.fromObject(systemSetting.getContent());
		String zypxcjzb = (String) jsonObject.get(SysSettingEnum.ZYPXCJZB.name());
		if (StringUtils.isEmpty(zypxcjzb)) {
			throw BizException.withMessage("系统参数设置中的学习成绩、终极考试成绩占比尚未设置，请先设置，然后继续操作");
		}
		// 字符串切割
		String[] percentage = zypxcjzb.split(":");
		List<String> percentageList = new ArrayList<>();// 存放百分比的集合
		for (String s : percentage) {
			percentageList.add(s);
		}
		// 学习成绩占比
		Double learningScoreZb = Double.parseDouble(percentageList.get(0));
		//练习成绩占比
		Double practiceScoreZb = Double.parseDouble(percentageList.get(1));
		// 终极考试成绩占
		Double finalExamScoreZb = Double.parseDouble(percentageList.get(2));
		zypxXm.setLearningScoreZb(learningScoreZb);
		zypxXm.setPracticeScoreZb(practiceScoreZb);
		zypxXm.setFinalExamScoreZb(finalExamScoreZb);
		zypxXm.setIsPopQues(StringUtils.isEmpty(xmDto.getIsPopQues()) ? Constants.NO : xmDto.getIsPopQues());
		zypxXm.setXct(xmDto.getXct());
		zypxXm.setNotice(xmDto.getNotice());
		zypxXm.setIsOpenCamera(xmDto.getIsOpenCamera());
		if (Constants.YES.equals(xmDto.getIsOpenCamera())) {
			// 开启抓拍
			zypxXm.setPhotoCatchInterval(xmDto.getPhotoCatchInterval());
			zypxXm.setIsOpenVerify(xmDto.getIsOpenVerify());
			if (Constants.YES.equals(xmDto.getIsOpenVerify())) {
				zypxXm.setVerifyThreshold(xmDto.getVerifyThreshold());
			}
		} else {
			zypxXm.setIsOpenVerify(Constants.NO);
		}
		zypxXm.setYears(xmDto.getYears());
		zypxXm.setPlanId(xmDto.getPlanId());
		zypxXm.setIsAllowChooseCourse(xmDto.getIsAllowChooseCourse());
		zypxXm.setIsMustFillPersonalInfo(StringUtils.isEmpty(xmDto.getIsMustFillPersonalInfo()) ? Constants.YES : xmDto.getIsMustFillPersonalInfo());
		zypxXm.setLeaderId(xmDto.getLeaderId());
		zypxXm.setPoiaddress(xmDto.getPoiaddress());
		zypxXm.setBmInviteCode(xmDto.getBmInviteCode());
		zypxXm.setPoilatlng(xmDto.getPoilatlng());
		zypxXm.setArriveRange(xmDto.getArriveRange());
		zypxXm.setCertiTplId(xmDto.getCertiTplId());
		zypxXm.setCertiDate(DateUtils.parse(xmDto.getCertiDate(), "yyyy-MM-dd"));
		zypxXm.setCertiXmName(xmDto.getCertiXmName());
		zypxXm.setIsAlllowStudyBeforePay(StringUtils.isEmpty(xmDto.getIsAlllowStudyBeforePay())? Constants.YES : xmDto.getIsAlllowStudyBeforePay());
		zypxXm.setIsAlllowExamBeforePay(StringUtils.isEmpty(xmDto.getIsAlllowExamBeforePay()) ? Constants.YES : xmDto.getIsAlllowExamBeforePay());
		zypxXm.setCertiHours(xmDto.getCertiHours());
		zypxXm.setLogo(xmDto.getLogo());
		zypxXm.setIsHot(StringUtils.isEmpty(xmDto.getIsHot()) ? Constants.NO : xmDto.getIsHot());
		zypxXm.setIsAllowMobileStudy(StringUtils.isEmpty(xmDto.getIsAllowMobileStudy()) ? Constants.YES : xmDto.getIsAllowMobileStudy());
		zypxXm.setIsNeedApprove(xmDto.getIsNeedApprove());
		zypxXm.setOpenStudyType(xmDto.getOpenStudyType());
		zypxXm.setIsAllowMultiChooseCourse(xmDto.getIsAllowMultiChooseCourse());
		mapper.insert(zypxXm);
	}

	@Transactional(rollbackFor = Exception.class)
	public void edit(ZypxXmDto xmDto, String userId) {
		if (StringUtils.isEmpty(xmDto.getId())) {
			throw BizException.withMessage("请选择一个培训项目");
		}
		this.doXmParamsCheck(xmDto);
		xmDto.setIsOpenCamera(StringUtils.isEmpty(xmDto.getIsOpenCamera()) ? Constants.NO : xmDto.getIsOpenCamera());
		ZypxXm zypxXm = mapper.selectById(xmDto.getId());
		zypxXm.setTitle(xmDto.getTitle());
		zypxXm.setStartTime(DateUtils.parse(xmDto.getStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setEndTime(DateUtils.parse(xmDto.getEndTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setTypeId(xmDto.getTypeId());
		zypxXm.setTrainees(xmDto.getTrainees());
		zypxXm.setAmount(xmDto.getAmount());
		zypxXm.setLimitCount(xmDto.getLimitCount());
		// string转换date
		zypxXm.setJtbmStartTime(DateUtils.parse(xmDto.getJtbmStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setJtbmEndTime(DateUtils.parse(xmDto.getJtbmEndTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setGrbmStartTime(DateUtils.parse(xmDto.getGrbmStartTime(), "yyyy-MM-dd HH:mm"));
		zypxXm.setGrbmEndTime(DateUtils.parse(xmDto.getGrbmEndTime(), "yyyy-MM-dd HH:mm"));
		//ycw modify 新增给已报名的学员自动推送短信设置
		zypxXm.setIsAutoSendSms(StringUtils.isEmpty(xmDto.getIsAutoSendSms()) ? Constants.NO : xmDto.getIsAutoSendSms());
		zypxXm.setIsAllowGrbm(xmDto.getIsAllowGrbm());
		Date studyEndTime = DateUtils.parse(StringUtils.isEmpty(xmDto.getStudyEndTime()) ? xmDto.getEndTime() : xmDto.getStudyEndTime(), "yyyy-MM-dd HH:mm");
		zypxXm.setStudyEndTime(studyEndTime);
		zypxXm.setStatus(xmDto.getStatus());
		// 获取用户id
		zypxXm.setUpdatorId(userId);
		zypxXm.setUpdateTime(new Date());
		zypxXm.setIsPopQues(StringUtils.isEmpty(xmDto.getIsPopQues()) ? Constants.NO : xmDto.getIsPopQues());
		zypxXm.setXct(xmDto.getXct());
		zypxXm.setNotice(xmDto.getNotice());
		zypxXm.setIsOpenCamera(xmDto.getIsOpenCamera());
		if (Constants.YES.equals(xmDto.getIsOpenCamera())) {
			// 开启抓拍
			zypxXm.setPhotoCatchInterval(xmDto.getPhotoCatchInterval());
			zypxXm.setIsOpenVerify(xmDto.getIsOpenVerify());
			if (Constants.YES.equals(xmDto.getIsOpenVerify())) {
				zypxXm.setVerifyThreshold(xmDto.getVerifyThreshold());
			}
		} else {
			zypxXm.setIsOpenVerify(Constants.NO);
		}
		zypxXm.setYears(xmDto.getYears());
		zypxXm.setIsAllowChooseCourse(xmDto.getIsAllowChooseCourse());

		zypxXm.setIsMustFillPersonalInfo(StringUtils.isEmpty(xmDto.getIsMustFillPersonalInfo()) ? Constants.YES : xmDto.getIsMustFillPersonalInfo());
		zypxXm.setLeaderId(xmDto.getLeaderId());
		zypxXm.setPoiaddress(xmDto.getPoiaddress());
		zypxXm.setPoilatlng(xmDto.getPoilatlng());
		zypxXm.setArriveRange(xmDto.getArriveRange());
		zypxXm.setCertiTplId(xmDto.getCertiTplId());
		zypxXm.setCertiDate(DateUtils.parse(xmDto.getCertiDate(), "yyyy-MM-dd"));
		zypxXm.setCertiXmName(xmDto.getCertiXmName());
		zypxXm.setIsAlllowStudyBeforePay(StringUtils.isEmpty(xmDto.getIsAlllowStudyBeforePay()) ? Constants.YES : xmDto.getIsAlllowStudyBeforePay());
		zypxXm.setIsAlllowExamBeforePay(StringUtils.isEmpty(xmDto.getIsAlllowExamBeforePay()) ? Constants.YES : xmDto.getIsAlllowExamBeforePay());
		zypxXm.setCertiHours(xmDto.getCertiHours());
		zypxXm.setLogo(xmDto.getLogo());
		zypxXm.setIsHot(xmDto.getIsHot());
		zypxXm.setBmInviteCode(xmDto.getBmInviteCode());
		zypxXm.setIsAllowMobileStudy(StringUtils.isEmpty(xmDto.getIsAllowMobileStudy()) ? Constants.YES : xmDto.getIsAllowMobileStudy());
		zypxXm.setIsNeedApprove(xmDto.getIsNeedApprove());
		zypxXm.setIsOpenStudy(xmDto.getIsOpenStudy());
		zypxXm.setOpenStudyUrl(xmDto.getOpenStudyUrl());
		zypxXm.setBuyType(xmDto.getBuyType());
		zypxXm.setIsExemptRegister(xmDto.getIsExemptRegister());
		zypxXm.setReportIntroduction(xmDto.getReportIntroduction());
		zypxXm.setTeacherIntroduction(xmDto.getTeacherIntroduction());
		zypxXm.setManagementTeam(xmDto.getManagementTeam());
		zypxXm.setClassCommitteeWork(xmDto.getClassCommitteeWork());
		zypxXm.setGroupWork(xmDto.getGroupWork());
		zypxXm.setStayPlan(xmDto.getStayPlan());
		zypxXm.setEatPlan(xmDto.getEatPlan());
		zypxXm.setEatPoint(xmDto.getEatPoint());
		zypxXm.setIncomeAmount(xmDto.getIncomeAmount());
		zypxXm.setOpenStudyType(xmDto.getOpenStudyType());
		zypxXm.setIsAllowMultiChooseCourse(xmDto.getIsAllowMultiChooseCourse());
		mapper.updateAllColumnById(zypxXm);

		//更新合同金额
		PlanDetail planDetail = planDetailMapper.selectById(xmDto.getId());
		planDetail.setContractAmount(xmDto.getContractAmount());
		planDetail.setIndustryCategory(xmDto.getIndustryCategory());
		planDetail.setCount(xmDto.getCount());
		planDetail.setSupportAmount(xmDto.getSupportAmount());
		planDetail.setBaseId(xmDto.getBaseId());
		planDetail.setClasszType(xmDto.getClasszType());
		planDetailMapper.updateById(planDetail);
	}

	/**
	 * 项目参数设置检查
	 */
	protected void doXmParamsCheck(ZypxXmDto xmDto) {
		if (StringUtils.isEmpty(xmDto.getSerialNumber())) {
			throw BizException.withMessage("项目编号不能够为空");
		}
		if (StringUtils.isEmpty(xmDto.getTitle())) {
			throw BizException.withMessage("培训名称不能够为空");
		}
		if (StringUtils.isEmpty(xmDto.getStartTime())) {
			throw BizException.withMessage("请选择培训开始时间");
		}
		if (StringUtils.isEmpty(xmDto.getEndTime())) {
			throw BizException.withMessage("请选择培训结束时间");
		}
		if (DateUtils.parse(xmDto.getStartTime(),"yyyy-MM-dd HH:mm").getTime() >= DateUtils.parse(xmDto.getEndTime(),"yyyy-MM-dd HH:mm").getTime()){
			throw BizException.withMessage("培训结束时间必须在培训开始时间之后");
		}
		if (StringUtils.isEmpty(xmDto.getTypeId())) {
			throw BizException.withMessage("请选择培训项目分类");
		}
		if (StringUtils.isEmpty(xmDto.getTrainees())) {
			throw BizException.withMessage("请输入培训对象");
		}
		if (StringUtils.isEmpty(xmDto.getJtbmStartTime())) {
			throw BizException.withMessage("请输入集体报名开始时间");
		}
		if (StringUtils.isEmpty(xmDto.getJtbmEndTime())) {
			throw BizException.withMessage("请输入集体报名结束时间");
		}
		if (DateUtils.parse(xmDto.getJtbmStartTime(),"yyyy-MM-dd HH:mm").getTime() >= DateUtils.parse(xmDto.getJtbmEndTime(),"yyyy-MM-dd HH:mm").getTime()){
			throw BizException.withMessage("集体报名结束时间必须在集体报名开始时间之后");
		}
		if(StringUtils.isEmpty(xmDto.getIsNeedApprove())){
			throw BizException.withMessage("请选择是否需要审核");
		}
		if (xmDto.getStatus() == null) {
			throw BizException.withMessage("请选择状态");
		}
		if (xmDto.getAmount() == null) {
			throw BizException.withMessage("请传入培训费用");
		}
		if (Constants.YES.equals(xmDto.getIsOpenCamera())) {
			if (xmDto.getPhotoCatchInterval() == null) {
				throw BizException.withMessage("请设置抓拍间隔时间");
			}
			if (StringUtils.isEmpty(xmDto.getIsOpenVerify())) {
				throw BizException.withMessage("请选择是否开启人脸比对");
			} else {
				if (Constants.YES.equals(xmDto.getIsOpenVerify())) {
					if (xmDto.getVerifyThreshold() == null) {
						throw BizException.withMessage("请设置比对阈值");
					}
				}
			}
		}
		if (StringUtils.isEmpty(xmDto.getIsAllowGrbm())) {
			throw BizException.withMessage("请选择是否开放个人报名");
		}
		if (Constants.YES.equals(xmDto.getIsAllowGrbm() )) {
			if (StringUtils.isEmpty(xmDto.getIsAllowChooseCourse())) {
				throw BizException.withMessage("请选择是否开放个人选课");
			}
			if (StringUtils.isEmpty(xmDto.getGrbmStartTime())) {
				throw BizException.withMessage("请输入个人报名开始时间");
			}
			if (StringUtils.isEmpty(xmDto.getGrbmEndTime())) {
				throw BizException.withMessage("请输入个人报名结束时间");
			}
			if (DateUtils.parse(xmDto.getGrbmStartTime(), "yyyy-MM-dd HH:mm").getTime() >= DateUtils.parse(xmDto.getGrbmEndTime(), "yyyy-MM-dd HH:mm").getTime()) {
				throw BizException.withMessage("个人报名结束时间必须在个人报名开始时间之后");
			}
		}
		if (Objects.equals(xmDto.getIsOpenStudy(), Constants.YES) && xmDto.getOpenStudyType() == OpenStudyType.XWD && StringUtils.isEmpty(xmDto.getOpenStudyUrl())) {
			throw BizException.withMessage("选择使用第三方学习平台(新闻道)必须填写第三方学习平台url地址");
		}
	}

    public Page pageQuery(ZypxXmQueryParams params) {
        List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    public Page statisticByXm(ZypxXmQueryParams params) {
    	List<Map<String, Object>> list = mapper.statisticByXm(params.getCondition(), params);
    	params.setRecords(list);
    	Map<String, Object> result = new HashMap<String, Object>();
    	result.put("data", params);
    	//计算合计汇总数据
    	Integer aonLineBmCount = 0;
    	Integer aoffLineBmCount = 0;
    	Integer atotalCount = 0;
    	Integer atotalVideoStudyTimes = 0;
    	Double atotalHours = 0d;
    	Map<String, Object> totalMap = new HashMap<String, Object>();
    	for (Map<String, Object> map : list) {
    		aonLineBmCount += BaseUtil.getIntValueFromMap(map, "onLineBmCount",0);
    		aoffLineBmCount += BaseUtil.getIntValueFromMap(map, "offLineBmCount",0);
    		atotalCount += BaseUtil.getIntValueFromMap(map, "totalCount",0);
    		atotalHours += BaseUtil.getDoubleValueFromMap(map, "totalHours",0);
    	}
    	totalMap.put("typeName", "合计：");
		totalMap.put("onLineBmCount", aonLineBmCount);
		totalMap.put("offLineBmCount", aoffLineBmCount);
		totalMap.put("totalCount", atotalCount);
		totalMap.put("totalHours", atotalHours);
		totalMap.put("progress", null);
    	list.add(totalMap);
    	return params;
    }

	public Page statisticCostByXm(ZypxXmQueryParams params) {
		List<Map<String, Object>> list = mapper.statisticCostByXm(params.getCondition(), params);
		params.setRecords(list);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("data", params);
		//计算合计汇总数据
		Integer totalCount = 0;
		Double totalAmount = 0.0;
		Double totalCost = 0.0;
		Double totalLeft = 0.0;
		Map<String, Object> totalMap = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			totalCount += BaseUtil.getIntValueFromMap(map, "totalCount",0);
			totalAmount += BaseUtil.getDoubleValueFromMap(map, "totalAmount",0);
			totalCost += BaseUtil.getDoubleValueFromMap(map, "totalCost",0);
			totalLeft += BaseUtil.getDoubleValueFromMap(map, "totalLeft",0);
		}
		totalMap.put("title", "合计：");
		totalMap.put("totalCount", totalCount);
		totalMap.put("totalAmount", totalAmount);
		totalMap.put("totalCost", totalCost);
		totalMap.put("totalLeft", totalLeft);
		list.add(totalMap);
		return params;
	}

	public List<PlanXmTreeNode> getPlanXmTree(PlanXmTreeQueryParams params) {
		Map<String, Object> condition = new HashMap<String, Object>();
		if (StringUtils.isNotEmpty(params.getHostOrgId())) {
			condition.put("hostOrgId", params.getHostOrgId());
		}
		if (StringUtils.isNotEmpty(params.getLeaderId())) {
			condition.put("leaderId", params.getLeaderId());
		}
		if (StringUtils.isNotEmpty(params.getEntrustOrgId())) {
			condition.put("entrustOrgId", params.getEntrustOrgId());
		}
		if (params.getStatus() != null) {
			condition.put("status", params.getStatus().name());
		}
		if (params.getSearchWord() != null) {
			condition.put("searchWord", params.getSearchWord());
		}
		List<ZypxPlanXmVo> list = mapper.getPlanWithXm(condition);

		// 按照年份分组
		Map<String, List<ZypxPlanXmVo>> yearsMap = list.stream().collect(Collectors.groupingBy(ZypxPlanXmVo::getYears));

		// 按照分类分组集合
		Map<String, PlanXmTreeNode> planMap = new LinkedHashMap<String, PlanXmTreeNode>();// 培训计划分类

		for (String years : yearsMap.keySet()) {
			for (ZypxPlanXmVo zypxPlanXmVo : yearsMap.get(years)) {
				int isSkill = BaseUtil.getInt(zypxPlanXmVo.getIsSkill(), 0);
				if (!planMap.containsKey(years + "_" + zypxPlanXmVo.getTypeId() + "_" + isSkill)) {
					PlanXmTreeNode planXmTree = new PlanXmTreeNode();
					planXmTree.setId(zypxPlanXmVo.getTypeId());
					planXmTree.setName(
							zypxPlanXmVo.getTypeName() + (isSkill == 1 ? "(" + zypxPlanXmVo.getBatchName() + ")" : ""));
					planXmTree.setOpen(false);
					planXmTree.setType(1);
					planXmTree.setIsSkill(String.valueOf(isSkill));// 是否只技能类培训
					planMap.put(years + "_" + zypxPlanXmVo.getTypeId() + "_" + isSkill, planXmTree);
				}
				if (StringUtils.isEmpty(zypxPlanXmVo.getXmId())) {
					continue;
				}
				PlanXmTreeNode planTree = planMap.get(years + "_" + zypxPlanXmVo.getTypeId() + "_" + isSkill);

				// 判断是否隐藏项目节点
				if (!Constants.YES.equals(params.getIsHideXm())) {
					PlanXmTreeNode xmNode = new PlanXmTreeNode();
					xmNode.setId(zypxPlanXmVo.getXmId());
					xmNode.setName(zypxPlanXmVo.getXmName());
					xmNode.setType(2);
					xmNode.setOpen(false);
					planTree.getChildren().add(xmNode);

					// 判断是否展示课程,如果展示则查询该项目自己的课程
					if (!Constants.YES.equals(params.getIsHideCourse())) {
						List<Map<String, Object>> privateList = xmCourseSettingMapper
								.getAllCourseByXmId(zypxPlanXmVo.getXmId());
						// 填充项目节点的下的课程
						for (Map<String, Object> map : privateList) {
							PlanXmTreeNode privateCourseNode = new PlanXmTreeNode();
							privateCourseNode.setId(map.get("id").toString());// 课程设置ID
							String courseName = map.get("courseName").toString();
							privateCourseNode.setOriginalName(courseName);
							privateCourseNode.setOpen(false);
							StringBuffer leanringTypeBuffer = new StringBuffer();
							if (Constants.YES.equals(map.get("isLive"))) {
								leanringTypeBuffer.append("直播、");
							}
							if (Constants.YES.equals(map.get("isMs"))) {
								leanringTypeBuffer.append("面授、");
							}
							if (Constants.YES.equals(map.get("isCourseware"))) {
								leanringTypeBuffer.append("课件、");
							}
							if (leanringTypeBuffer.length() > 0) {
								String leanringType = leanringTypeBuffer.deleteCharAt(leanringTypeBuffer.length() - 1)
										.toString();
								courseName += ("(" + leanringType + ")");
							}
							privateCourseNode.setName(courseName);
							privateCourseNode.setType(Constants.PLAN_XM_TREE_NODE_IS_COURSE);
							xmNode.getChildren().add(privateCourseNode);
						}
					}
				}
			}
		}
		List<PlanXmTreeNode> result = new ArrayList<PlanXmTreeNode>();
		for (String years : yearsMap.keySet()) {
			PlanXmTreeNode yearNode = new PlanXmTreeNode();
			yearNode.setId(years);
			yearNode.setName(years);
			yearNode.setType(0);// 年份节点
			List<String> keyList = new ArrayList<String>();
			for (ZypxPlanXmVo zypxPlanXmVo : yearsMap.get(years)) {
				if (!keyList.contains(
						years + "_" + zypxPlanXmVo.getTypeId() + "_" + BaseUtil.getInt(zypxPlanXmVo.getIsSkill(), 0))) {
					yearNode.getChildren().add(planMap.get(years + "_" + zypxPlanXmVo.getTypeId() + "_"
							+ BaseUtil.getInt(zypxPlanXmVo.getIsSkill(), 0)));
					keyList.add(years + "_" + zypxPlanXmVo.getTypeId() + "_"
							+ BaseUtil.getInt(zypxPlanXmVo.getIsSkill(), 0));
				}
			}
			result.add(yearNode);
		}
		return result;
	}

	public List<ZypxXm> getXmByPlanId(String planId){
		EntityWrapper<ZypxXm> wrapper = new EntityWrapper();
		wrapper.eq("plan_id", planId);
		List<ZypxXm> xmList = mapper.selectList(wrapper);
		return xmList;
	}

	public Integer getBmCountByXmId(String xmId) {
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", xmId);
		return bmMapper.selectCount(wrapper);
	}

	public ZypxXm getXmBySerialNumber(String serialNumber) {
		EntityWrapper<ZypxXm> wrapper = new EntityWrapper();
		wrapper.eq("serial_number", serialNumber);
		List<ZypxXm> list = this.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}

	/**
	 * 初始化项目归档信息
	 *
	 * @param xmId
	 * @return
	 */
	@SuppressWarnings("serial")
	public Map<String, Object> initArchive(String xmId) {
		Map<String, Object> initArchive = mapper.getByXmId(xmId);
		if(initArchive == null ) {
			initArchive = new HashMap<String, Object>();
		}
		// 线上学时
		int onlineHours = 0;
		// 线下学时
		int offLineHours = 0;
		for (Map<String, Object> courseSetting : xmCourseSettingMapper.getAllCourseByXmId(xmId)) {
			if (Constants.YES.equals(courseSetting.get("isCourseware"))
					|| Constants.YES.equals(courseSetting.get("isLive"))) {
				onlineHours += BaseUtil.getInt(courseSetting.get("hours"), 0);
			}
			if (Constants.YES.equals(courseSetting.get("isMs"))) {
				offLineHours += BaseUtil.getInt(courseSetting.get("hours"), 0);
			}
		}
		initArchive.put("online_hours", onlineHours);
		initArchive.put("off_line_hours", offLineHours);

		// 组装培训对象:人数
		Map<String, Object> countData = new HashMap<String, Object>() {
			{
				put("企业职工", 0);
				put("党政领导干部", 0);
				put("教师", 0);
				put("农村劳动者", 0);
				put("在校学生", 0);
				put("军人", 0);
				put("老年人", 0);
				put("残疾人", 0);
				put("其他", 0);
			}
		};
		for (Map<String, Object> studentType : bmMapper.getStudentTypeByXmId(xmId)) {
			int count = BaseUtil.getInt(studentType.get("count"), 0);
			if (studentType.get("studentType").equals(StudentType.SCHOOL.name())) {
				countData.put("在校学生", count);
			}
			if (studentType.get("studentType").equals(StudentType.SOCIAL.name())) {
				countData.put("企业职工", count);
			}
		}
		initArchive.put("count_data", countData);
		return initArchive;
	}

	// 获取项目的归档信息
	public Map<String, Object> getDetails(String xmId) {
		Map<String, Object> planDetail = mapper.getByXmId(xmId);
		if (BaseUtil.isNotEmpty(planDetail.get("countData"))) {
			planDetail.put("count_data", JSONObject.fromObject(planDetail.get("countData")));
		}
		return planDetail;
	}

	// 项目归档
	@SuppressWarnings("unchecked")
	@Transactional
	public void archives(ZypxXm zypxXm, PlanDetail planDetail) {
		if (StringUtils.isNotEmpty(planDetail.getCountData())) {
			JSONObject countData = JSONObject.fromObject(planDetail.getCountData());
			Integer count = 0;
			Iterator<String> iterator = countData.keys();
			while (iterator.hasNext()) {
				String value = iterator.next();
				if(StringUtils.isNotEmpty(value)) {
					count += countData.getInt(value);
				}
			}
			planDetail.setCount(count);
		}
		planDetailMapper.updateById(planDetail);
	}

	// 项目归档信息导出
	public XSSFWorkbook archiveExport(String xmIds, OutputStream os)
			throws SQLException, IOException, RowsExceededException, WriteException {
		XSSFWorkbook wb = new XSSFWorkbook();
		XSSFCellStyle style = XSSFCellStyleUtil.getContentStyle(wb);
		XSSFSheet sheet = this.createSheet(wb);
		XSSFRow row;
		XSSFCell cell;
		int rowNum = 2;
		int cellNum = 0;
		int sortNum = 1;
		for (String xmId : xmIds.split(",")) {
			Map<String, Object> planDetail = this.getDetails(xmId);
			row = sheet.createRow(rowNum++);

			cell = row.createCell(cellNum++);
			cell.setCellValue(sortNum++);
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "title")));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(
					BaseUtil.getStringValueFromMap(planDetail, "isFreePublic", "0").equals(Constants.YES) ? "是" : "否"));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(
					BaseUtil.getStringValueFromMap(planDetail, "isGovSubside", "0").equals(Constants.YES) ? "是" : "否"));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "entrustOrgNature")));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getDoubleValueFromMap(planDetail, "contractAmount", 0.0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getDateValueFromMap(planDetail, "contractSignTime")));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getDateValueFromMap(planDetail, "contractFinishTime")));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "receiveOrgName")));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getDoubleValueFromMap(planDetail, "govAmount", 0.0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getDoubleValueFromMap(planDetail, "notGovAmount", 0.0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntValueFromMap(planDetail, "onlineHours", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntValueFromMap(planDetail, "offLineHours", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			String dictIndustry = "";
			if (planDetail.containsKey("dictIndustryId")) {
				Dict dict = dictMapper.selectById(BaseUtil.getStringValueFromMap(planDetail, "dictIndustryId"));
				if (dict != null) {
					dictIndustry = dict.getDictName();
				}
			}
			cell.setCellValue(BaseUtil.convertNullToEmpty(dictIndustry));
			cell.setCellStyle(style);

			JSONObject countData = new JSONObject();
			if (BaseUtil.isNotEmpty(planDetail.get("countData"))) {
				countData = JSONObject.fromObject(planDetail.get("countData"));
			}
			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "企业职工", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "党政领导干部", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "教师", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "农村劳动者", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "在校学生", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "军人", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "老年人", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "残疾人", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntegerValueFromJson(countData, "其他", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getIntValueFromMap(planDetail, "count", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getIntValueFromMap(planDetail, "schoolTeacherCount", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "schoolTeacherName")));

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getIntValueFromMap(planDetail, "outerTeacherCount", 0)));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(
					BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "outerTeacherName")));

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(
					BaseUtil.getStringValueFromMap(planDetail, "isTypical", "0").equals(Constants.YES) ? "是" : "否"));
			cell.setCellStyle(style);

			cell = row.createCell(cellNum++);
			cell.setCellValue(BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(planDetail, "typicalRemark")));

			cellNum = 0;
		}
		return wb;
	}

	private XSSFSheet createSheet(XSSFWorkbook wb) {
		XSSFSheet sheet = wb.createSheet("项目归档信息");
		XSSFCellStyle style = XSSFCellStyleUtil.getTitleStyle(wb);

		// 因为需求一共有2行数据，这里就先创建2个行的空数据，之后分别对这2个行数据进行操作
		XSSFRow row1 = sheet.createRow(0);// 先创建第一行空数据
		XSSFRow row2 = sheet.createRow(1);// 先创建第二行空数据

		// 设置行高
		row1.setHeight((short) 1000);

		// 设置列宽
		int cellNum = 0;
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 3200);
		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 2000);

		sheet.setColumnWidth(cellNum++, 2000);
		sheet.setColumnWidth(cellNum++, 3000);
		sheet.setColumnWidth(cellNum++, 3000);
		sheet.setColumnWidth(cellNum++, 3000);
		sheet.setColumnWidth(cellNum++, 1500);

		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 1700);
		sheet.setColumnWidth(cellNum++, 1000);

		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);

		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 1000);

		sheet.setColumnWidth(cellNum++, 3500);
		sheet.setColumnWidth(cellNum++, 1000);
		sheet.setColumnWidth(cellNum++, 3500);
		sheet.setColumnWidth(cellNum++, 1500);
		sheet.setColumnWidth(cellNum++, 3500);

		/** 设置第一行的数据 */
		// 四个参数分别是：起始行，起始列，结束行，结束列
		// 第1个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
		XSSFCell ce1 = row1.createCell(0);
		ce1.setCellValue("序号");// 表格的第一行第一列显示的数据
		ce1.setCellStyle(style);// 样式，居中

		// 第2个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
		XSSFCell ce2 = row1.createCell(1);
		ce2.setCellValue("培训项目（班次）名称");
		ce2.setCellStyle(style);

		// 第3个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
		XSSFCell ce3 = row1.createCell(2);
		ce3.setCellValue("是否为免费公益项目*");
		ce3.setCellStyle(style);

		// 第4个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
		XSSFCell ce4 = row1.createCell(3);
		ce4.setCellValue("是否为政府补贴性培训项目*");
		ce4.setCellStyle(style);

		// 第5个格子，夸4列
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 7));
		XSSFCell ce5 = row1.createCell(4);
		ce5.setCellValue("合同（协议）情况");
		ce5.setCellStyle(style);

		// 第6个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 8, 8));
		XSSFCell ce6 = row1.createCell(8);
		ce6.setCellValue("实施机构*");
		ce6.setCellStyle(style);

		// 第7个格子，夸2列
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 9, 10));
		XSSFCell ce7 = row1.createCell(9);
		ce7.setCellValue("到账经费（万元）");
		ce7.setCellStyle(style);

		// 第8个格子，夸2列
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 12));
		XSSFCell ce8 = row1.createCell(11);
		ce8.setCellValue("培训学时（个）");
		ce8.setCellStyle(style);

		// 第9个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 13, 13));
		XSSFCell ce9 = row1.createCell(13);
		ce9.setCellValue("面向行业*");
		ce9.setCellStyle(style);

		// 第10个格子，夸10列
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 14, 23));
		XSSFCell ce10 = row1.createCell(14);
		ce10.setCellValue("培训对象（人）");
		ce10.setCellStyle(style);

		// 第11个格子，夸4列
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 24, 27));
		XSSFCell ce11 = row1.createCell(24);
		ce11.setCellValue("承担培训工作教师数（人）");
		ce11.setCellStyle(style);

		// 第12个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 28, 28));
		XSSFCell ce12 = row1.createCell(28);
		ce12.setCellValue("是否为本校典型特色项目*");
		ce12.setCellStyle(style);

		// 第13个格子，夸2行
		sheet.addMergedRegion(new CellRangeAddress(0, 1, 29, 29));
		XSSFCell ce13 = row1.createCell(29);
		ce13.setCellValue("典型特色项目介绍（限300字以内，包括机制模式、做法经验和取得成效等）");
		ce13.setCellStyle(style);

		/** 设置第二行的数据 */
		// 第14个格子
		XSSFCell ce14 = row2.createCell(4);
		ce14.setCellValue("甲方类型*");
		ce14.setCellStyle(style);

		// 第15个格子
		XSSFCell ce15 = row2.createCell(5);
		ce15.setCellValue("合同金额（万元）");
		ce15.setCellStyle(style);

		// 第16个格子
		XSSFCell ce16 = row2.createCell(6);
		ce16.setCellValue("签署时间");
		ce16.setCellStyle(style);

		// 第17个格子
		XSSFCell ce17 = row2.createCell(7);
		ce17.setCellValue("完成时间");
		ce17.setCellStyle(style);

		// 第18个格子
		XSSFCell ce18 = row2.createCell(9);
		ce18.setCellValue("财政资金");
		ce18.setCellStyle(style);

		// 第19个格子
		XSSFCell ce19 = row2.createCell(10);
		ce19.setCellValue("非财政资金");
		ce19.setCellStyle(style);

		// 第20个格子
		XSSFCell ce20 = row2.createCell(11);
		ce20.setCellValue("线上");
		ce20.setCellStyle(style);

		// 第21个格子
		XSSFCell ce21 = row2.createCell(12);
		ce21.setCellValue("线下");
		ce21.setCellStyle(style);

		// 第22个格子
		XSSFCell ce22 = row2.createCell(14);
		ce22.setCellValue("企业职工");
		ce22.setCellStyle(style);

		// 第23个格子
		XSSFCell ce23 = row2.createCell(15);
		ce23.setCellValue("党政领导干部");
		ce23.setCellStyle(style);

		// 第24个格子
		XSSFCell ce24 = row2.createCell(16);
		ce24.setCellValue("教师");
		ce24.setCellStyle(style);

		// 第25个格子
		XSSFCell ce25 = row2.createCell(17);
		ce25.setCellValue("农村劳动者");
		ce25.setCellStyle(style);

		// 第26个格子
		XSSFCell ce26 = row2.createCell(18);
		ce26.setCellValue("在校学生");
		ce26.setCellStyle(style);

		// 第27个格子
		XSSFCell ce27 = row2.createCell(19);
		ce27.setCellValue("军人");
		ce27.setCellStyle(style);

		// 第28个格子
		XSSFCell ce28 = row2.createCell(20);
		ce28.setCellValue("老年人");
		ce28.setCellStyle(style);

		// 第29个格子
		XSSFCell ce29 = row2.createCell(21);
		ce29.setCellValue("残疾人");
		ce29.setCellStyle(style);

		// 第30个格子
		XSSFCell ce30 = row2.createCell(22);
		ce30.setCellValue("其他");
		ce30.setCellStyle(style);

		// 第31个格子
		XSSFCell ce31 = row2.createCell(23);
		ce31.setCellValue("合计");
		ce31.setCellStyle(style);

		// 第32个格子
		XSSFCell ce32 = row2.createCell(24);
		ce32.setCellValue("校内教师人数");
		ce32.setCellStyle(style);

		// 第33个格子
		XSSFCell ce33 = row2.createCell(25);
		ce33.setCellValue("校内教师姓名");
		ce33.setCellStyle(style);

		// 第34个格子
		XSSFCell ce34 = row2.createCell(26);
		ce34.setCellValue("外聘人员人数");
		ce34.setCellStyle(style);

		// 第35个格子
		XSSFCell ce35 = row2.createCell(27);
		ce35.setCellValue("外聘人员姓名");
		ce35.setCellStyle(style);

		return sheet;
	}

    public Object adviceList(ZypxXmQueryParams params) {
		List<Map<String, Object>> list = pxxmXmBmAdviceMapper.adviceList(params.getCondition(), params);
		params.setRecords(list);
		return params;
    }
    
    /**
     * 获取项目下配置的职业
     */
	public List<Map<String, Object>> getProfessionByXmId(String xmId){
		return mapper.getProfessionByXmId(xmId);
	}
	
	  /**
     * 是否是技能类的项目
     */
	public boolean isSkill(String xmId){
		ZypxXm zypxXm = mapper.selectById(xmId);
		return Constants.YES.equals(planMapper.selectById(zypxXm.getPlanId()).getIsSkill());
	}

	/**
	 * 项目学习详情统计
	 * @param id 项目id
	 * @param keyword 学员关键字
	 * @return 统计详情
	 */
	public Page xmStudyDetailStatistic(String id, String keyword, String hostOrgId, Page page) {
		page.setRecords(mapper.xmStudyDetailStatistic(id, keyword, hostOrgId, page));
		return page;
	}

	/**
	 * 项目学习详情导出
	 */
	public void xmStudyDetailExport(String id, String keyword,String hostOrgId, OutputStream os) throws WriteException, IOException {
		List<Map<String, Object>> list = mapper.xmStudyDetailStatistic(id, keyword, hostOrgId, new Page(1, Integer.MAX_VALUE));
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("项目学习详情表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "报名总学时", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "完成学时", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "学习进度", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : list) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("totalHours") != null ? map.get("totalHours") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("totalFinishedHours") != null ? map.get("totalFinishedHours") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("progress") != null ? map.get("progress") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	public void statisticByXmExport(ZypxXmQueryParams params, OutputStream os) throws IOException, WriteException {
		List<Map<String, Object>> list = mapper.statisticByXm(params.getCondition(), new Page<>(1, Integer.MAX_VALUE));
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("项目报名统计表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "分类", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "项目名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "承办单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "线下培训人次", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "线上培训人次", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "总培训人次", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "总学时", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "完成学时进度", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : list) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("typeName") != null ? map.get("typeName") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("title") != null ? map.get("title") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("receiveOrgName") != null ? map.get("receiveOrgName") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("offLineBmCount") != null ? map.get("offLineBmCount") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("onLineBmCount") != null ? map.get("onLineBmCount") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("totalCount") != null ? map.get("totalCount") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("totalHours") != null ? map.get("totalHours") : "0"),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("progress") != null ? map.get("progress") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 当前年度，培训报名人数
	 */
	private Map<String, Object> bmCount(String hostOrgId) {
		List<ZypxBm> bms = zyjdBmMapper.bmListCurYear(hostOrgId, DateUtils.getCurrentYear()+"");
		Map<String, List<ZypxBm>> listMap = bms.stream().collect(Collectors.groupingBy(x -> DateUtils.format(x.getTime(), "MM")));
		//每个月报名人数
		return listMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, m -> m.getValue().size()));
	}

	/**
	 * 年度项目收入统计
	 */
	private List<Map<String, Double>> xmIncomeStatistical(String hostOrgId, Set<Integer> yearList) {
		return zyjdBmMapper.xmIncomeStatistical(hostOrgId, yearList);
	}

	/**
	 * 项目分类统计
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> projectStat(ZypxXmQueryParams params) {
		return mapper.projectStat(params.getCondition());
	}
	
	/**
	 * 承办单位统计
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> orgStat(ZypxXmQueryParams params) {
		return mapper.orgStat(params.getCondition());
	}

	/**
	 * 培训驾驶舱
	 * @param hostOrgId 主办单位id
	 * @return 驾驶舱数据
	 */
	public Object cockpit(String hostOrgId) {
		Map<String, Object> result = new HashMap<>();
		result.put("survey", cockpitMapper.survey(hostOrgId));
		result.put("base", cockpitMapper.base(hostOrgId));
		result.put("baseXm", cockpitMapper.baseXm(hostOrgId));
		result.put("classz", cockpitMapper.classz(hostOrgId));
		result.put("xmType", cockpitMapper.xmType(hostOrgId));
		result.put("certi", cockpitMapper.certi(hostOrgId));
		result.put("teacher", cockpitMapper.teacher(hostOrgId));
		result.put("outTeacher", cockpitMapper.outTeacher(hostOrgId));
		result.put("inTeacher", cockpitMapper.inTeacher(hostOrgId));
		result.put("income", cockpitMapper.income(hostOrgId));
		result.put("education", cockpitMapper.education(hostOrgId));
		result.put("xmSource", cockpitMapper.xmSource(hostOrgId));
		result.put("expend", cockpitMapper.expend(hostOrgId));
		result.put("featureXm", cockpitMapper.featureXm(hostOrgId));
		return result;
	}
}