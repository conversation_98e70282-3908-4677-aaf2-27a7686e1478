package com.xunw.jxjy.model.core;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.SpringBeanUtils;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.enums.Jjkhd;
import com.xunw.jxjy.model.enums.Sjlx;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.personal.service.ZypxStudentCorePaperService;
import com.xunw.jxjy.model.personal.service.ZypxStudentErrorQuestionCollectService;
import com.xunw.jxjy.model.personal.service.ZypxStudentExamDataService;
import com.xunw.jxjy.model.zypx.entity.ZypxErrorQuestionCollect;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperCheckResult;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionBlankFill;
import com.xunw.jxjy.paper.model.QuestionTt;
import com.xunw.jxjy.paper.utils.ModelHelper;
import com.xunw.jxjy.paper.utils.PaperServiceHelper;

import net.sf.json.JSONObject;

/**
 * 试卷批阅
 */
@Service
public class CorePaperCheckService {

	private final static Logger logger = LoggerFactory.getLogger(CorePaperCheckService.class);
	
	@Autowired
	private ZypxStudentExamDataService studentExamDataService;
	@Autowired
	private ZypxStudentCorePaperService studentCorePaperService;
	@Autowired
	private ZypxStudentErrorQuestionCollectService studentErrorQuestionCollectService;

	/**
	 * 单个试卷的批改 paper:试卷实例对象 json:用户答案对象
	 */
	public PaperCheckResult doCheckPaper(Paper paper, JSONObject json) {
		if (paper == null) {
			logger.error("自动批改:试卷对象不存在.");
			return null;
		}
		if (json == null) {
			logger.error("自动批改:用户答案不存在.");
			return null;
		}
		if (paper.getSections() == null || paper.getSections().size() < 1) {
			logger.error("自动批改:试卷不完整.");
			return null;
		}
		// 批改结果
		JSONObject scoreDetail = new JSONObject();
		// 总得分
		int fs = 0;
		try {
			// 开始批改
			for (PaperSection section : paper.getSections()) {
				if (section != null && section.getQuestions() != null && section.getQuestions().size() > 0) {
					for (Question question : section.getQuestions()) {
						if (question == null) {
							continue;
						}
						Stlb qType = question.getType();
						int userScore = 0;
						String userkey = "";
						if (Stlb.TT.equals(qType)) {
							QuestionTt tt = (QuestionTt) question;
							List<Question> childrens = tt.getChildren();
							for (Question children : childrens) {
								qType = children.getType();
								try {
									userkey = json.getString("Q-" + children.getId());
								} catch (Exception e) {
									logger.error("自动批改:获取用户答案失败，答案不存在，可能没作答." + e.getMessage());
								}
								if (Stlb.SINGLECHOICE.equals(qType) || Stlb.MULTIPLECHOICE.equals(qType)
										|| Stlb.JUDGMENT.equals(qType)) {
									// 防止遗漏，确保替换，KEY应该没有间隔符
									String _key = BaseUtil.isEmpty(children.getAnswer()) ? ""
											: children.getAnswer().replace(",", "").replace(Constants.TM_SPLITER, "")
													.replace(" ", "");
									String _userkey = userkey == null ? "" : userkey.replace(Constants.TM_SPLITER, "");
									if (_key.equals(_userkey)) {
										userScore = children.getScore();
									}
								} else if (Stlb.BLANKFILL.equals(qType)) {
									QuestionBlankFill _children = (QuestionBlankFill) children;
									userScore = PaperServiceHelper.BlankFillChecker(_children, userkey);
								}
								scoreDetail.put("Q-" + children.getId(), userScore);
								// 累加总分
								fs = fs + userScore;
							}
						} else {
							try {
								userkey = json.getString("Q-" + question.getId());
							} catch (Exception e) {
								logger.error("自动批改:获取用户答案失败，答案不存在，可能没作答." + e.getMessage());
							}
							if (Stlb.SINGLECHOICE.equals(qType) || Stlb.MULTIPLECHOICE.equals(qType)
									|| Stlb.JUDGMENT.equals(qType)) {
								// 防止遗漏，确保替换，KEY应该没有间隔符
								String _key = BaseUtil.isEmpty(question.getAnswer()) ? ""
										: question.getAnswer().replace(",", "").replace(Constants.TM_SPLITER, "")
												.replace(" ", "");

								String _userkey = userkey == null ? "" : userkey.replace(Constants.TM_SPLITER, "").replace(",", "").replace(" ", "");
								if (_key.equals(_userkey)) {
									userScore = question.getScore();
								}
							} else if (Stlb.BLANKFILL.equals(qType)) {
								QuestionBlankFill _question = (QuestionBlankFill) question;
								userScore = PaperServiceHelper.BlankFillChecker(_question, userkey);
							}
							scoreDetail.put("Q-" + question.getId(), userScore);
							// 累加总分
							fs = fs + userScore;
						}
					}
				}
			}

			String paperId = json.getString("paperId");
			String studentId = json.getString("studentId");
			JSONObject studentAnswerJson = json;
			studentAnswerJson.remove("paperId");
			studentAnswerJson.remove("studentId");

			PaperCheckResult result = new PaperCheckResult();
			result.setScoreDetail(scoreDetail);
			result.setScore(fs);
			result.setPaperId(paperId);
			result.setStudentId(studentId);
			result.setExamData(studentAnswerJson);
			result.setSuccess(true);

			logger.info("自动批改完成，结果对象：" + result.toString());

			return result;

		} catch (Exception e) {
			logger.error("自动批改:批改时发生异常." + e.getMessage());
			e.printStackTrace();
			return null;
		}
	}

	public int doSaveUserPapers(List<PaperCheckResult> list) {
		if (list == null || list.size() < 1) {
			logger.warn("自动批改:保存用户试卷，目标队列为空.");
			return 0;
		}
		List<ExamData> examDataList = new ArrayList<ExamData>();
		for (PaperCheckResult result : list) {
			ExamData examData = new ExamData();
			examData.setData(result.getExamData() == null ? "" : result.getExamData().toString());
			examData.setScoreDetail(result.getScoreDetail() == null ? "" : result.getScoreDetail().toString());
			examData.setScore(result.getScore());
			examData.setStudentId(result.getStudentId());
			examData.setPaperId(result.getPaperId());
			examData.setStatus(ExamDataStatus.YZDPG);
			examDataList.add(examData);
		}
		int result = 0;
		// 批改结果批量入库
		try {
			logger.info("------------------系统成功批阅试卷自动打分:" + examDataList.size());
			result =  this.saveUserPaper(examDataList);
		} catch (Exception e) {
			try {// 再试一次
				Thread.sleep(500);
				result = this.saveUserPaper(examDataList);
			} catch (Exception e2) {
				try {// 再试一次
					Thread.sleep(500);
					result = this.saveUserPaper(examDataList);
				} catch (Exception e3) {
					logger.error("Error", e3);
				}
			}
		}
		//错题自动收藏入库
		try {
			studentErrorQuestionCollectService.saveErrorQues(list);
		} catch (Exception e) {
			logger.error("错题自动收藏失败", e);
		}
		return result;
	}

	/**
	 * 检查用户试卷队列，并逐条批改
	 */

	public int doCheckPaperQueue() {
		Queue<Map<String, Object>> USER_PAPER_QUEUE = TomSystemQueue.USER_PAPER_QUEUE;
		// 每次取60条出来
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < 60; i++) {
			Map<String, Object> map = USER_PAPER_QUEUE.poll();
			if (map == null) {
				continue;
			}
			list.add(map);
		}

		int ips = list.size();
		logger.info("批量批改:获取到[" + ips + "]份答卷数据");
		if (ips < 1) {
			return 0;
		}

		List<PaperCheckResult> results = new ArrayList<PaperCheckResult>();

		for (Map<String, Object> map : list) {
			String paperId = String.valueOf(map.get("paperId"));
			String studentId = String.valueOf(map.get("studentId"));
			if (BaseUtil.isEmpty(paperId) || BaseUtil.isEmpty(studentId)) {
				logger.error("批量批改:用户或试卷信息丢失.paperId=" + paperId + ",studentId=" + studentId);
				continue;
			}
			Paper paper = studentCorePaperService.getPaper(paperId);
			if (paper == null) {
				logger.error("批量批改:试卷不存在!!!.paperId=" + paperId + ",studentId=" + studentId);
				continue;
			}
			JSONObject json = JSONObject.fromObject(map);
			// 批改当前这个试卷，请把结果保存到列表
			PaperCheckResult result = doCheckPaper(paper, json);
			// 批改结果判断
			if (result != null && result.isSuccess()) {
				results.add(result);
			} else {
				logger.error("自动批改发生异常，答卷被存到磁盘文件, map = " + map);
				try {
					String filename = paperId + "_" + studentId + ".dat";
					String separator = System.getProperty("file.separator");
					String basepath = separator + "files" + separator + "data" + separator;
					// 本地临时存储路径
					AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
					String filepath = attConfig.getTempdir() + basepath + filename;

					FileHelper.doWriteFile(filepath, json.toString());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}
		// 批量保存批改结果
		if (results != null && results.size() > 0) {
			return doSaveUserPapers(results);
		}
		return 0;
	}

	public int saveUserPaper(List<ExamData> examDataList) {
		if (examDataList == null || examDataList.isEmpty()) {
			return 0;
		}
		int updatedNum = 0;
		for (ExamData examData : examDataList) {
			EntityWrapper<ExamData> examdataWrapper = new EntityWrapper<ExamData>();
			examdataWrapper.eq("paper_id", examData.getPaperId());
			examdataWrapper.eq("student_id", examData.getStudentId());
			examdataWrapper.eq("status", ExamDataStatus.YJJDPG);
			studentExamDataService.update(examData, examdataWrapper);
			
			//如果当前试卷是平时作业，需要修改学员试卷的最高得分,因为平时作业最后在成绩计算中只取最高分
			studentCorePaperService.updateStudentHomeworkMaxScore(examData.getPaperId(),
					examData.getStudentId(), examData.getScore());
			
			updatedNum++;
		}
		return updatedNum;
	}


	public void checkTimeoutExamData() {
		List<Map<String, Object>> data = studentExamDataService.getTimeoutExamData();
		logger.info("[checkTimeoutExamData]试卷数量:"+data.size());
		if (data != null && data.size() > 0) {
			for (Map<String, Object> exam : data) {
				checkTimeoutPaper("system", (String) exam.get("paperId"), (String) exam.get("studentId"),
						(String) exam.get("id"));
			}
		}
	}

	public int checkTimeoutPaper(String operId, String paperId, String studentId, String dataId) {
		Paper paper = studentCorePaperService.getPaper(paperId);
		int rows = 0;
		if (Sjlx.PT.equals(paper.getPaperType())) {
			logger.info("[" + operId + "强制交卷-单个-普通试卷]开始...");
			try {
				Map<String, Object> condition = new HashMap<String, Object>();
				condition.put("dataId", dataId);
				condition.put("studentId", studentId);
				condition.put("paperId", paperId);
				Map<String, Object> examdata = studentExamDataService.getHistoryDetail(condition);
				if (examdata != null) {
					String data = BaseUtil.isNotEmpty(examdata.get("data")) ? String.valueOf(examdata.get("data")) : null;
					condition.put("data", JSONObject.fromObject(data));
					condition.put("endTime", examdata.get("endTime"));
					// 发送答案到队列
					rows = doSendUserExamData2Queue(condition);
				}
				
			} catch (Exception e) {
				logger.error("Error", e);
			}
			logger.info("[强制交卷-单个-普通试卷]完成，共计：" + rows);

		}
		return rows;
	}

	/******************************** 以下为通用方法 ************************************/

	/**
	 * 将用户答卷送入队列 param list == 相当于tm_examdata表的行集合
	 * 
	 * @return
	 */
	private int doSendUserExamData2Queue(List<Map<String, Object>> list) {
		if (list == null || list.size() < 1)
			return 0;

		int rows = 0;
		// 循环组装试卷并加入队列
		for (Map<String, Object> upaper : list) {
			if (upaper != null) {
				String paperId = String.valueOf(upaper.get("paperId"));
				String studentId = String.valueOf(upaper.get("studentId"));
				String data = String.valueOf(upaper.get("data"));
				try {
					JSONObject json = JSONObject.fromObject(data);

					// 数据库中的原始数据
					Map<String, Object> examdata = ModelHelper.SimpleJSON2Map(json);

					// 填充数据
					examdata.put("paperId", paperId);
					examdata.put("studentId", studentId);

					logger.info("[强制交卷]放入队列：" + examdata.toString());

					/********** 放入批改队列 ********************************/
					TomSystemQueue.addPaper(examdata);
					/********** 放入批改队列 ********************************/

					rows = rows + 1;

				} catch (Exception e) {
					System.err.println(String.format("[强制交卷]发生异常,studentId=%s,paperId=%s", studentId, paperId));
					logger.error("Error", e);
				}
			}
		}

		// 有1条以上的数据加入队列，开始修改试卷状态
		if (rows > 0) {
			int drows = 0;
			if (list != null && list.size() > 0) {
				for (Map<String, Object> studentAnswer : list) {
					try {
						String paperId = String.valueOf(studentAnswer.get("paperId"));
						String studentId = String.valueOf(studentAnswer.get("studentId"));
						String dataId = String.valueOf(studentAnswer.get("dataId"));

						ExamData examData = new ExamData();
						examData.setId(dataId);
						examData.setPaperId(paperId);
						examData.setStudentId(studentId);
						if (studentAnswer.get("endTime") == null) {
							examData.setEndTime(new Date());//设置交卷的时间为当前时间
						}
						examData.setStatus(ExamDataStatus.YJJDPG);
						examData.setScore(0);
						examData.setClient(Jjkhd.PC);

						EntityWrapper<ExamData> examdataWrapper = new EntityWrapper<ExamData>();
						examdataWrapper.eq("id", dataId);
						examdataWrapper.eq("paper_id", paperId);
						examdataWrapper.eq("student_id", studentId);
						studentExamDataService.update(examData, examdataWrapper);
						drows += 1;
					} catch (Exception e) {
						logger.error("Error", e);
					}
				}
			}
			if (drows == rows) {
				logger.info(String.format("[强制交卷]放入队列成功，并更改批改状态，请等待结果,共：%s", rows));
			} else {
				logger.warn(String.format("[强制交卷]放入队列后，更改试卷数量不一致，需要检查,放入：%s，更改：%s", rows, drows));
			}
		}
		return rows;
	}

	/**
	 * 将用户答卷送入队列
	 * 
	 * @return
	 */
	private int doSendUserExamData2Queue(Map<String, Object> map) {
		if (map == null)
			return 0;
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		list.add(map);
		return doSendUserExamData2Queue(list);
	}
	/******************************** 以上为通用方法 ************************************/
//	public static void main(String[] args) {
//		String a="ABC";
//		String b = BaseUtil.isEmpty(a) ? "": a.replace(",", "").replace(Constants.TM_SPLITER, "").replace(" ", "");
//		System.out.println(b);
//	}
}
