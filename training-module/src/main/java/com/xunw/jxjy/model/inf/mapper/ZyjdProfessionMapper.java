package com.xunw.jxjy.model.inf.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.entity.ZyjdProfessionCondition;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 职业
 */
public interface ZyjdProfessionMapper extends BaseMapper<ZyjdProfession> {

    public List<Map<String,Object>> list(Map<String,Object> condition, Page<?> page);

    public List<Map<String,Object>> getHostOrgProfessionList(Map<String,Object> condition, Page<?> page);
    
    public List<ZyjdProfessionCondition> getConditionByProfessionId(Map<String,Object> condition, Page<?> page);

    List<String> selectIdsByNames(@Param("hostOrgId") String hostOrgId, @Param("list")List<String> list);
}

    