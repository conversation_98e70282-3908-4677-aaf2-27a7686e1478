package com.xunw.jxjy.model.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 国际化教育表
 */
@TableName("inf_international_education")
public class InternationalEducation implements Serializable {

	private static final long serialVersionUID = 1L;

	//主键id
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;

	//国别
	@TableField("country_name")
	private String countryName;

	//外方合作单位名称
	@TableField("cooperative_units")
	private String cooperativeUnits;

	//世界排名
	@TableField("rank")
	private String rank;

	//合作项目类别
	@TableField("project_category")
	private String projectCategory;

	//合作专业
	@TableField("speciality")
	private String speciality;

	//计划实施期
	@TableField("apply_time")
	private String applyTime;

	//主办单位id
	@TableField("host_org_id")
	private String hostOrgId;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//修改用户id
	@TableField("updator_id")
	private String updatorId;

	//修改时间
	@TableField("update_time")
	private Date updateTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getCooperativeUnits() {
		return cooperativeUnits;
	}

	public void setCooperativeUnits(String cooperativeUnits) {
		this.cooperativeUnits = cooperativeUnits;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getProjectCategory() {
		return projectCategory;
	}

	public void setProjectCategory(String projectCategory) {
		this.projectCategory = projectCategory;
	}

	public String getSpeciality() {
		return speciality;
	}

	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}

	public String getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public void check() {
		if (StringUtils.isEmpty(this.countryName)) {
			throw BizException.withMessage("国别不能为空");
		}
		if (StringUtils.isEmpty(this.cooperativeUnits)) {
			throw BizException.withMessage("外方合作单位名称不能为空");
		}
		if (StringUtils.isEmpty(this.rank)) {
			throw BizException.withMessage("世界排名不能为空");
		}
		if (StringUtils.isEmpty(this.projectCategory)) {
			throw BizException.withMessage("合作项目类别不能为空");
		}
		if (StringUtils.isEmpty(this.speciality)) {
			throw BizException.withMessage("合作专业不能为空");
		}
		if (StringUtils.isEmpty(this.applyTime)) {
			throw BizException.withMessage("计划实施期不能为空");
		}
	}
}
