package com.xunw.jxjy.model.zyjd.service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.Constants.PayType;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.ZipUtil;
import com.xunw.jxjy.common.utils.ZipUtils;
import com.xunw.jxjy.model.common.entity.CommOrder;
import com.xunw.jxjy.model.common.entity.CommOrderDetail;
import com.xunw.jxjy.model.common.mapper.CommOrderDetailMapper;
import com.xunw.jxjy.model.common.mapper.CommOrderMapper;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.CertiType;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Ksly;
import com.xunw.jxjy.model.enums.Nation;
import com.xunw.jxjy.model.enums.OrderStatus;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.enums.PoliticalType;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;
import com.xunw.jxjy.model.enums.ZyjdPaymentStatus;
import com.xunw.jxjy.model.enums.ZyjdPaymentType;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.entity.UserInfo;
import com.xunw.jxjy.model.sys.mapper.DictMapper;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.mapper.UserInfoMapper;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zyjd.entity.Attachment;
import com.xunw.jxjy.model.zyjd.entity.BizBmAuditLog;
import com.xunw.jxjy.model.zyjd.entity.ZcFile;
import com.xunw.jxjy.model.zyjd.entity.ZcGroup;
import com.xunw.jxjy.model.zyjd.entity.ZcGroupUser;
import com.xunw.jxjy.model.zyjd.entity.ZcReviewMark;
import com.xunw.jxjy.model.zyjd.entity.ZcReviewVote;
import com.xunw.jxjy.model.zyjd.entity.ZcThesisScore;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.mapper.AttachmentMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcFileMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupUserMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcReviewMarkMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcReviewVoteMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcThesisScoreMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmBatchMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zyjd.params.ZcpsScoreParams;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmQueryParams;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmScopeParams;

import jxl.Workbook;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

@Service
public class ZyjdBmService extends BaseCRUDService<ZyjdBmMapper, ZyjdBm> {

	private static final Logger logger = LoggerFactory.getLogger(ZyjdBmService.class);
	
	@Autowired
	private DictService dictService;
	@Autowired
	private AttConfig attConfig;
    @Autowired
    private ZcThesisScoreMapper zcThesisScoreMapper;
	@Autowired
	private ZcFileMapper zcFileMapper;
    @Autowired
    private ZyjdBmMapper zyjdBmMapper;
    @Autowired
    private ZcGroupUserMapper zcGroupUserMapper;
    @Autowired
    private ZcGroupMapper zcGroupMapper;
    @Autowired
    private ZcReviewMarkMapper zcReviewMarkMapper;
    @Autowired
    private ZyjdBmBatchMapper zyjdBmBatchMapper;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private StudentUserMapper studentUserMapper;
    @Autowired
    private ZyjdProfessionMapper zyjdProfessionMapper;
    @Autowired
    private CommOrderDetailMapper commOrderDetailMapper;
    @Autowired
    private CommOrderMapper commOrderMapper;
    @Autowired
    private AttachmentMapper attachmentMapper;
    @Autowired
	private UserInfoMapper userInfoMapper;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private ZyjdBmScopeMapper zyjdBmScopeMapper;
	@Autowired
	private DictMapper dictMapper;
	@Autowired
	private BizBmAuditLogService bizBmAuditLogService;
	
    @Autowired
	private ZcReviewVoteMapper zcReviewVoteMapper;

//	private static final String zcpsTYBmInfoTemplate = "https://jxjy-att.whxunw.com/guotu_att/upload/attchment/2024071710/0F054166F02949E6AFE9AE9D84071BF0.pdf";
	
	private static final String zcpsTYBmInfoTemplate = "https://jxjy-att.whxunw.com/guotu_att/upload/attchment/2024071814/EF9ACA888F0A4CB38FAEF373037E6F10.pdf?fileName=2D9BC6A19511481CB4F2C60582B1F99C.pdf";

//	private static final String zcpsDLBmInfoTemplate = "http://jxjy-att.whxunw.com/guotu_att/upload/attchment/2024050713/B173CE135B5647B89DB1FBA41A826219.pdf?fileName=DL.pdf";
	private static final String zcpsDLBmInfoTemplate = "https://jxjy-att.whxunw.com/guotu_att/upload/attchment/2024071815/F673F5D63847447ABB9AFC8BFFFA05D8.pdf?fileName=B173CE135B5647B89DB1FBA41A826219.pdf";

	/**
	 * 列表查询
	 */
	public Page pageQuery(ZyjdBmQueryParams params) {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
//		List<String> bmIds = list.stream().map(m -> m.get("id").toString()).collect(Collectors.toList());
//		List<Attachment> attachments = new ArrayList<>();
//		Map<String, List<Attachment>> map = new HashMap<>();
//		if (CollectionUtils.isNotEmpty(bmIds)) {
//			attachments = attachmentMapper.selectList(new EntityWrapper<Attachment>().in("yw_id", bmIds));
//			map = attachments.stream().collect(Collectors.groupingBy(Attachment::getYwId));
//			Map<String, List<Attachment>> finalMap = map;
//			list.forEach(l->l.put("attachments", finalMap.isEmpty() ? new ArrayList<>() : finalMap.get(l.get("id").toString())));
//		}
		params.setRecords(list);
		return params;
	}

	/**
	 * 统计分页查询
	 */
	public Page analysis(ZyjdBmQueryParams params) {
		List<Map<String, Object>> list = mapper.analysis(params.getCondition(), params);
		params.setRecords(list);
		Integer peopleCount = 0;
		Map<String, Object> totalMap = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			peopleCount += BaseUtil.getIntValueFromMap(map, "people", 0);
		}
		totalMap.put("bmbatchName", "合计：");
		totalMap.put("people", peopleCount);
		list.add(totalMap);
		return params;
	}

	/**
	 * 导出报名信息
	 */
	public void exportBmList(ZyjdBmQueryParams params, OutputStream os) throws Exception, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("职业技能鉴定报名汇总表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "学历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "所在岗位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "学号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "准考证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "申报职业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "申报工种", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "申报级别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "学员来源", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "学校", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "院系", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "班级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "辅导员", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "所在单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "参加工作时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "订单编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "聚合支付订单号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "支付时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "缴费金额(元)", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "缴费状态", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "缴费类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "简要经历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "满足的申报条件", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "所学专业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "原证书职业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "原证书等级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "原证书编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			// 根据批次类型导出不同的信息
			if (Objects.equals(ZyjdBmBatchType.TDDZCL,params.getType())) {
				// 土地地址测绘管理需要导出报名信息
				ws.addCell(new Label(i, row, "报名批次", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 30);
				ws.addCell(new Label(i, row, "累计专业技术工作年限", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 20);
				ws.addCell(new Label(i, row, "现从事专业", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "全日制最高学历", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "全日制所学专业", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "全日制毕业时间", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
//				ws.addCell(new Label(i, row, "学历证书", OfficeToolExcel.getTitle()));
//				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "申报学历", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "申报学历专业", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "毕业时间", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "现任专业技术职务", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 20);
				ws.addCell(new Label(i, row, "专业工作年限", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "是否转评", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "是否破格", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
			} else if (Objects.equals(ZyjdBmBatchType.ZYJD,params.getType())) {
				// 职业技能报名导出需按上报人社表增加以下字段
				ws.addCell(new Label(i, row, "证书领取", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "从业年限", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "民族", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "政治面貌", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "学历证书编号", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 15);
				ws.addCell(new Label(i, row, "邮政编码", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "通讯地址", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
				ws.addCell(new Label(i, row, "工作年限", OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
			}
			ws.addCell(new Label(i, row, "备注", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("gender") != null ? Gender.valueOf((String) map.get("gender")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("education") != null ? Education.valueOf((String) map.get("education")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("gw") != null ? map.get("gw") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentNum") != null ? map.get("studentNum") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zkz") != null ? map.get("zkz") : ""),
					OfficeToolExcel.getNormolCell()));
			//匹配职业后括号内的职业方向
			String[] strings = BaseUtil.convertNullToEmpty(map.get("professionName") != null
					? map.get("professionName").toString()
					: "").split("（");
			ws.addCell(new Label(col++, row, strings[0], OfficeToolExcel.getNormolCell()));
			String gz = null;
			if (strings.length > 1) {
				gz = strings[1].substring(0, strings[1].length()-1);
			}
			ws.addCell(new Label(col++, row, gz, OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("applyTechLevel") != null
							? TechLevel.valueOf((String) map.get("applyTechLevel")).getName()
							: ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("ksly") != null ? Ksly.valueOf((String) map.get("ksly")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("graduateSchool") != null ? map.get("graduateSchool") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("college") != null ? map.get("college") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("classz") != null ? map.get("classz") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("instructor") != null ? map.get("instructor") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("workTime") != null ? DateUtils.format((Date)map.get("workTime"),"yyyy-MM-dd") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("orderNumber") != null ? map.get("orderNumber") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("unionOrderNo") != null ? map.get("unionOrderNo") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("payTime") != null
							? DateUtils.format((Date) map.get("payTime"), "yyyy-MM-dd HH:mm:ss")
							: ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("amount") != null ? map.get("amount") : ""),
					OfficeToolExcel.getNormolCell()));
			String paymentStatus = "";
			String paymentType = "";
			if (PayStatus.YJ.name().equals(map.get("payStatus"))){
				if (PayType.OFF_LINE.equals(map.get("payType"))){
					paymentType = ZyjdPaymentType.OFFLINE.getName();
				}else {
					paymentType = ZyjdPaymentType.ONLINE.getName();
				}
				paymentStatus = ZyjdPaymentStatus.YJ.getName();
			}
			if (PayStatus.WJ.name().equals(map.get("payStatus"))){
				if (BaseUtil.isNotEmpty(map.get("markRefundUserId"))){
					paymentStatus = ZyjdPaymentStatus.TF.getName();
				}else {
					paymentStatus = ZyjdPaymentStatus.WJ.getName();
				}
			}
			ws.addCell(new Label(col++, row,paymentStatus,OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,paymentType,OfficeToolExcel.getNormolCell()));
			// 行程码 健康码 在缴费类型后面
//			String healthyCode = BaseUtil
//					.convertNullToEmpty(map.get("healthyCode") != null ? map.get("healthyCode") : "");
//			if (StringUtils.isNotEmpty(healthyCode)) {
//				URL url = new URL(healthyCode);
//				URI uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(),
//						url.getQuery(), url.getRef());
//				url = uri.toURL();
//				ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//				BufferedImage bufferImg = ImageIO.read(url);
//				ImageIO.write(bufferImg, "png", byteArrayOut);
//				byte[] imgtypes = byteArrayOut.toByteArray();
//				int ii = col++;
//				ws.addCell(new Label(ii, row, null, OfficeToolExcel.getNormolCell()));
//				WritableImage image = new WritableImage(ii, row, 1, 1, imgtypes);
//				// 把图片插入到sheet
//				ws.setRowView(row, 1500);
//				ws.addImage(image);
//			} else {
//				ws.addCell(new Label(col++, row, null, OfficeToolExcel.getNormolCell()));
//			}
//			String tripCard = BaseUtil.convertNullToEmpty(map.get("tripCard") != null ? map.get("tripCard") : "");
//			if (StringUtils.isNotEmpty(tripCard)) {
//				URL url = new URL(tripCard);
//				URI uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(),
//						url.getQuery(), url.getRef());
//				url = uri.toURL();
//				ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//				BufferedImage bufferImg = ImageIO.read(url);
//				ImageIO.write(bufferImg, "png", byteArrayOut);
//				byte[] imgtypes = byteArrayOut.toByteArray();
//				int ii = col++;
//				ws.addCell(new Label(ii, row, null, OfficeToolExcel.getNormolCell()));
//				WritableImage image = new WritableImage(ii, row, 1, 1, imgtypes);
//				// 把图片插入到sheet
//				ws.setRowView(row, 1500);
//				ws.addImage(image);
//			} else {
//				ws.addCell(new Label(col++, row, null, OfficeToolExcel.getNormolCell()));
//			}
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("resume") != null ? map.get("resume") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("conditionSeq") != null ? map.get("conditionSeq") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("specialty") != null ? map.get("specialty") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("oldCertiProfession") != null ? map.get("oldCertiProfession") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("oldTechLevel") != null ? TechLevel.valueOf(map.get("oldTechLevel").toString()).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("oldTechCertiNum") != null ? map.get("oldTechCertiNum").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			// 根据批次类型导出不同的信息
			if (Objects.equals(ZyjdBmBatchType.TDDZCL,params.getType())) {
				// 土地地址测绘管理需要导出报名信息
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("bmbatchName") != null ? map.get("bmbatchName").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("jobYears") != null ? map.get("jobYears").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("currentJob") != null ? map.get("currentJob").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("fullEducation") != null ? Education.valueOf(map.get("fullEducation").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("fullSpecialty") != null ? map.get("fullSpecialty").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("fullEduTime") != null ? map.get("fullEduTime").toString() : ""),
						OfficeToolExcel.getNormolCell()));
//				List<Attachment> attachments = (List<Attachment>) map.get("attachments");
//				if (CollectionUtils.isNotEmpty(attachments)) {
//					for (Attachment attachment : attachments) {
//						URL url = new URL(attachment.getUrl());
//						URI uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(),
//								url.getQuery(), url.getRef());
//						url = uri.toURL();
//						ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//						BufferedImage bufferImg = ImageIO.read(url);
//						ImageIO.write(bufferImg, "png", byteArrayOut);
//						byte[] imgtypes = byteArrayOut.toByteArray();
//						int ii = col++;
//						ws.addCell(new Label(ii, row, null, OfficeToolExcel.getNormolCell()));
//						WritableImage image = new WritableImage(ii, row, 1, 1, imgtypes);
//						// 把图片插入到sheet
//						ws.setRowView(row, 1500);
//						ws.addImage(image);
//					}
//				} else {
//					ws.addCell(new Label(col++, row, null, OfficeToolExcel.getNormolCell()));
//				}
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("jobEducation") != null ? Education.valueOf(map.get("jobEducation").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("jobSpecialty") != null ? map.get("jobSpecialty").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("jobEduTime") != null ? map.get("jobEduTime").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("profession") != null ? map.get("profession").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("professionYears") != null ? map.get("professionYears").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("isReevaluation") != null && StringUtils.equals(map.get("isReevaluation").toString(),"1") ? "是" : "否"),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("isExceptional") != null && StringUtils.equals(map.get("isExceptional").toString(),"1") ? "是" : "否"),
						OfficeToolExcel.getNormolCell()));
			} else if (Objects.equals(ZyjdBmBatchType.ZYJD,params.getType())) {
				// 职业技能报名导出需按上报人社表增加以下字段
				int certiPost = BaseUtil.getInt(map.get("certiPost"), 0);
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(certiPost == 1 ? "自取" : certiPost == 2 ? "快递到付" : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("workYears") != null ? map.get("workYears").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("nation") != null ? Nation.valueOf(map.get("nation").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("politicalType") != null ? PoliticalType.valueOf(map.get("politicalType").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("eduCertiNumber") != null ? map.get("eduCertiNumber").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("postCode") != null ? map.get("postCode").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("address") != null ? map.get("address").toString() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("jobYears") != null ? map.get("jobYears").toString() : ""),
						OfficeToolExcel.getNormolCell()));
			}
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("remark") != null ? map.get("remark").toString() : ""),
							OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 导出七大员报名信息
	 */
	public void exportQdy(ZyjdBmQueryParams params, OutputStream os) throws Exception, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("七大员报名汇总表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "专业名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "教育年份", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "学历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "企业名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 30);
			ws.addCell(new Label(i, row, "联系方式", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "发证时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "备注", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("gender") != null ? Gender.valueOf((String) map.get("gender")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("professionName") != null ? "继教-" + map.get("professionName").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("years") != null ? map.get("years") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("education") != null ? Education.valueOf((String) map.get("education")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, Constants.EMPTY_STRING, OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, Constants.EMPTY_STRING, OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 导出标准化的注册信息,可以直接导入到省鉴定中心平台、
	 */
	public void exportStandard(ZyjdBmQueryParams params, OutputStream os) throws Exception, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		Map<String, Object> areaMap = dictService.getAllRegionByMap();
		WritableSheet ws = wwb.createSheet("职业技能等级认定报名数据上报表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "证件类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "证件号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "出生日期", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "文化程度", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "考生来源", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "证书领取", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "职业名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "工种名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "级别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "申报条件编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "申报条件", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "手机号码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "参加工作时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "从事专业(所学专业)", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "专业年限(申报职业工龄)", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "民族", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "政治面貌", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "学历证书编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "简要经历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "邮政编码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "所在单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "通讯地址", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "邮寄地址", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "邮箱", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "户籍所在地", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "省份", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "城市", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "原证书职业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "原证书等级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "原证书编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "工龄", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "现从事职业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "专业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "技术职称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "证书编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					map.get("certiType") != null ? CertiType.valueOf(map.get("certiType").toString()).getName() : CertiType.SFZ.getName(),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("gender") != null ? Gender.valueOf((String) map.get("gender")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("sfzh") != null ? BaseUtil.getBirthday(map.get("sfzh").toString()) : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("education") != null ? Education.valueOf((String) map.get("education")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("ksly") != null ? Ksly.valueOf((String) map.get("ksly")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, // 证书领取
					BaseUtil.convertNullToEmpty(
							map.get("certiPost") != null ? ("1".equals(map.get("certiPost").toString()) ? "自取" : "快递到付")
									: ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,//所属职业 工种的category
					BaseUtil.convertNullToEmpty(map.get("category") != null ? map.get("category") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(//工种名称
							map.get("professionName") != null ? map.get("professionName").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("applyTechLevel") != null
							? TechLevel.valueOf((String) map.get("applyTechLevel")).getName()//申报等级
							: ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,//报考条件编号
					BaseUtil.convertNullToEmpty(map.get("conditionSeq") != null ? map.get("conditionSeq") : ""),//报考条件编号
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,//报考条件
					BaseUtil.convertNullToEmpty(map.get("condition") != null ? map.get("condition") : ""),//学历
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("workTime") != null ? DateUtils.format((Date) map.get("workTime"), "yyyy-MM-dd") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("specialty") != null ? map.get("specialty") : ""),//所学专业
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("workYears") != null ? map.get("workYears") : ""),//申报职业的工龄
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("nation") != null ? Nation.valueOf((String) map.get("nation")).getName() : ""),//民族
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("politicalType") != null
							? PoliticalType.valueOf((String) map.get("politicalType")).getName()
							: ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("eduCertiNumber") != null ? map.get("eduCertiNumber").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("resume") != null ? map.get("resume").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("postCode") != null ? map.get("postCode").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("address") != null ? map.get("address").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("address") != null ? map.get("address").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("email") != null ? map.get("email").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("certiAddress") != null ? map.get("certiAddress").toString() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("companyProvinceCode") != null
							? areaMap.get(map.get("companyProvinceCode").toString())
							: ""),
					OfficeToolExcel.getNormolCell()));// 省份
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
					map.get("companyCityCode") != null ? areaMap.get(map.get("companyCityCode").toString()) : ""),
					OfficeToolExcel.getNormolCell()));// 城市
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("oldCertiProfession") != null ? map.get("oldCertiProfession").toString() : ""),
					OfficeToolExcel.getNormolCell()));// 原证书职业
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("oldTechLevel") != null
							? TechLevel.findByEnumName(map.get("oldTechLevel").toString()).getName()
							: ""),
					OfficeToolExcel.getNormolCell()));// 原证书等级
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("oldTechCertiNum") != null ? map.get("oldTechCertiNum").toString() : ""),
					OfficeToolExcel.getNormolCell()));// 原证书编号
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("jobYears") != null ? map.get("jobYears").toString() : ""),
					OfficeToolExcel.getNormolCell()));//工龄
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("currentJob") != null ? map.get("currentJob").toString() : ""),
					OfficeToolExcel.getNormolCell()));//现从事职业
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("profession") != null ? map.get("profession").toString() : ""),
					OfficeToolExcel.getNormolCell()));//专业
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("zc") != null ? Constants.ZC.getName(map.get("zc").toString()) : ""),
					OfficeToolExcel.getNormolCell()));//技术职称
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("oldTechCertiNum") != null ? map.get("oldTechCertiNum").toString() : ""),
					OfficeToolExcel.getNormolCell()));// 证书编号=原证书编号
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();

	}

	/**
	 * 2020年土地地质测绘专业技术水平能力测试报名汇总表
	 */
	public void exportBmhz(ZyjdBmQueryParams params, OutputStream os) throws Exception, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("职业技能鉴定报名汇总表", 0);
		// 建表
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "工作单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "年度", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "组织单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "系列", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "级别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "专业", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "手机号码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);

			ws.setColumnView(i++, 20);
			ws.setColumnView(i++, 10);
		}
		row = 1;
		int i = 1;
		// 写表
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(
							map.get("gender") != null ? Gender.valueOf((String) map.get("gender")).getName() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));

			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(""), OfficeToolExcel.getNormolCell()));

			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(""), OfficeToolExcel.getNormolCell()));

			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(""), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(TechLevel.valueOf(map.get("applyTechLevel").toString())),
					OfficeToolExcel.getNormolCell()));
			// 测试专业
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("professionName") != null ? map.get("professionName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 批量删除
	 */
	@Transactional
	public boolean batchDel(List<String> idList) {
		for (int i = 0; i < idList.size(); i++) {
			ZyjdBm bizZyjdZc = mapper.selectById(idList.get(i));
			PayStatus payStatus = bizZyjdZc.getPayStatus();
			if (PayStatus.YJ == payStatus) {
				throw BizException.withMessage("第" + (i + 1) + "个考生已经缴费，不能够删除");
			}
			ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(bizZyjdZc.getBmbatchId());
			List<Map<String, Object>> students = zcGroupMapper.getGroupStudentsByBatchId(zyjdBmBatch.getId(), null);
			Optional<Map<String, Object>> optional = students.stream().filter(x -> Objects.equals(x.get("studentId").toString(), bizZyjdZc.getStudentId())).findFirst();
			if (optional.isPresent()) {
				throw BizException.withMessage("当前学生"+optional.get().get("sfzh")+optional.get().get("name")+"已经分组，不能删除");
			}
		}
		mapper.deleteBatchIds(idList);
		// 删除报名审核记录
		EntityWrapper<BizBmAuditLog> bmAuditLogEntityWrapper = new EntityWrapper<>();
		bmAuditLogEntityWrapper.in("bm_id",idList);
		bizBmAuditLogService.deleteList(bmAuditLogEntityWrapper);
		return true;
	}

	/**
	 * 批量标记已缴费
	 */
	@Transactional
	public boolean markPayed(String ids, Double amount, PayStatus status, String userId, String remark) {
		List<ZyjdBm> zyjdBms = mapper.selectBatchIds(Arrays.asList(ids.split(",")));
		if (zyjdBms.isEmpty()) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据不存在");
		}
		if (status == PayStatus.YJ && zyjdBms.stream().anyMatch(b->b.getPayStatus() == PayStatus.YJ)) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据中包含已缴费的数据");
		} else if (status == PayStatus.WJ && zyjdBms.stream().anyMatch(b->b.getPayStatus() == PayStatus.WJ)) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据中包含未缴费的数据");
		}
		for (ZyjdBm zyjdBm : zyjdBms) {
			if (status == PayStatus.YJ) {
				zyjdBm.setPayStatus(status);
				zyjdBm.setMarkPaydUserId(userId);
				zyjdBm.setAmount(amount);
				zyjdBm.setRemark(zyjdBm.getRemark() == null ? remark : zyjdBm.getRemark()+";"+remark);
				zyjdBm.setPayType(Constants.PayType.OFF_LINE);
			} else if (status == PayStatus.WJ) {
				zyjdBm.setPayStatus(status);
				zyjdBm.setMarkPaydUserId(null);
				zyjdBm.setAmount(null);
				zyjdBm.setRemark(zyjdBm.getRemark() == null ? remark : zyjdBm.getRemark()+";"+remark);
				zyjdBm.setPayType(null);
			}
		}
		if (zyjdBms.size() > 0) {
			DBUtils.updateAllColumnBatchById(zyjdBms, ZyjdBm.class);
		}
		return true;
	}

	/**
	 * 获取报名详细信息
	 */
	public Map<String, Object> getDetailsById(String id) {
		Map<String, Object> details = this.mapper.getDetailsById(id);
		details.put("select", dictService.getZyjsbmDictList());
		details.put("files", zcFileMapper.selectList(new EntityWrapper<ZcFile>().eq("bm_id", id)));
		details.put("attachments", attachmentMapper.selectList(new EntityWrapper<Attachment>().eq("yw_id", id)));
		List<Map<String, Object>> list = bizBmAuditLogService.queryByBmId(id);
		if (CollectionUtils.isNotEmpty(list)) {
			List<Map<String, Object>> groupedDataList = list.stream()
					.collect(Collectors.groupingBy(e -> BaseUtil.getIntValueFromMap(e, "count")))
					.entrySet().stream()
					.map(entry -> {
						Map<String, Object> map = new HashMap<>();
						map.put("count", entry.getKey());
						String status = BaseUtil.getStringValueFromMap(entry.getValue().get(0), "status");
						map.put("name", "0".equals(status) || "1".equals(status) ? "初审" : "终审");
						map.put("list", entry.getValue());
						return map;
					}).collect(Collectors.toList());
			details.put("auditLogs", groupedDataList);
		} else {
			details.put("auditLogs", Collections.emptyList());
		}
		return details;
	}

	/**
	 * 批量生成准考号
	 */
	@Transactional
	public void batchCteateZkz(List<String> idList, String bmbatchId) {
		DecimalFormat format = new DecimalFormat("00000");
		EntityWrapper<ZyjdBm> wrapper = new EntityWrapper();
		wrapper.eq("bmbatch_id", bmbatchId);
		wrapper.isNotNull("zkz");
		int start = this.mapper.selectCount(wrapper);
		List<ZyjdBm> list = mapper.selectBatchIds(idList);
		for (ZyjdBm zyjdBm : list) {
			String prefix = "T" + new SimpleDateFormat("yyyyMMdd").format(zyjdBm.getSaveTime());
			String serialNumber = format.format(new BigDecimal(++start));
			String zkz = prefix + serialNumber;
			zyjdBm.setZkz(zkz);
			zyjdBm.setZkznoCreateTime(new Date());
		}
		DBUtils.updateBatchById(list, ZyjdBm.class);
	}

	public Map<String, Object> importExaminationRoom(MultipartFile file,String bmbatchId,String hostOrgId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "examination_room_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[]{"xm","sfzh", "kc","zwh","examPoint","examTime","busLine"});
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		Set<String> sfzList = new HashSet<String>();
		int row = 0;
		int cfCou = 0;
		int cwCou = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			// 身份证号不能够为空
			String sfzh = StringUtils.trimToNull(map.get("sfzh"));
			if (StringUtils.isEmpty(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证号为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String kc = StringUtils.trimToNull(map.get("kc"));
//			if (StringUtils.isEmpty(kc)) {
//				log.append("<br>");
//				String msg = "第" + row + "行考场为空，忽略这条数据。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
			String zwh = StringUtils.trimToNull(map.get("zwh"));
//			if (StringUtils.isEmpty(zwh)) {
//				log.append("<br>");
//				String msg = "第" + row + "行座位号为空，忽略这条数据。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}

			if (sfzList.contains(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证重复，请仔细检查，忽略这条数据。";
				log.append(msg);
				// 标记重复数据+1
				cfCou++;
				continue;
			} else {
				sfzList.add(sfzh);
			}

			String examPoint = StringUtils.trimToNull(map.get("examPoint"));
//			if (StringUtils.isEmpty(examPoint)) {
//				log.append("<br>");
//				String msg = "第" + row + "行考试地点为空，忽略这条数据。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
			
			String examTime = StringUtils.trimToNull(map.get("examTime"));
//			if (StringUtils.isEmpty(examTime)) {
//				log.append("<br>");
//				String msg = "第" + row + "行考试时间为空，忽略这条数据。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
			
			String busLine = StringUtils.trimToNull(map.get("busLine"));
//			if (StringUtils.isEmpty(busLine)) {
//				log.append("<br>");
//				String msg = "第" + row + "行乘车路线为空，忽略这条数据。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
			// 根据身份证查询学生id信息 判断学员用户是否存在
			List<StudentUser> studentUserList = studentUserMapper.getBySfzh(sfzh, hostOrgId);
			StudentUser studentUser = studentUserList.size() > 0 ? studentUserList.get(0) : null;
			if (studentUser == null) {
				log.append("<br>");
				String msg = "第" + row + "行查询不到该用户，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			// 根据批次和学生id修改报名表考场和座位号
			ZyjdBm query = new ZyjdBm();
			query.setStudentId(studentUser.getId());
			query.setBmbatchId(bmbatchId);
			ZyjdBm zyjdBm = zyjdBmMapper.selectOne(query);
			if (zyjdBm == null) {
				log.append("<br>");
				String msg = "第" + row + "行查询不到用户报名数据，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			zyjdBm.setExamRoom(kc);
			zyjdBm.setSeatNo(zwh);
			zyjdBm.setBusLine(busLine);
			zyjdBm.setExamTime(examTime);
			zyjdBm.setExamPoint(examPoint);
			DecimalFormat format = new DecimalFormat("00000");
			String prefix = "T" + new SimpleDateFormat("yyyyMMdd").format(zyjdBm.getSaveTime());
			EntityWrapper<ZyjdBm> wrapper = new EntityWrapper();
			wrapper.eq("bmbatch_id", bmbatchId);
			wrapper.isNotNull("zkz");
			int start = this.mapper.selectCount(wrapper);
			zyjdBm.setZkz(prefix + format.format(start + 1));
			zyjdBmMapper.updateById(zyjdBm);
		}
		int sucCount = (list.size() - 1) - cfCou - cwCou;
		// 提示总共多少条，导入成功多少条，重复数据多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (list.size() - 1) + "条，导入成功" + sucCount + "条，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	/**
	 * 林业报名数据导出
	 */
	public void exportForestBmInfo(ZyjdBmQueryParams params, OutputStream os) throws Exception, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setType(ZyjdBmBatchType.FOREST);
		params.setSize(Integer.MAX_VALUE);
		Page pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("林业报名数据导出汇总表", 0);
		// 建表
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "培训期次", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "发票抬头", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "纳税人识别号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "报名时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "缴费状态", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		// 写表
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(
					new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
							OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("bmbatchName") != null ? map.get("bmbatchName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("invoice_title") != null ? map.get("invoice_title") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("taxpayer_number") != null ? map.get("taxpayer_number") : ""),
					OfficeToolExcel.getNormolCell()));
			String bmTime = "";
			if (BaseUtil.isNotEmpty(map.get("submitTime"))) {
				bmTime = DateUtils.format((Date) map.get("submitTime"), "yyyy-MM-dd HH:mm:ss");
			}
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(bmTime), OfficeToolExcel.getNormolCell()));
			String jfzt = "未缴";
			if (BaseUtil.isNotEmpty(map.get("payStatus"))) {
				jfzt = PayStatus.YJ.name().equals(map.get("payStatus").toString()) ? "已缴" : "未缴";
			}
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(jfzt), OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 标记报名状态
	 */
	@Transactional
	public void markBmStatus(String ids, ZyjdBmStatus status, String remark) {
		List<String> bmIds = Lists.newArrayList(StringUtils.split(ids, ","));
		EntityWrapper<ZyjdBm> wrapper = new EntityWrapper();
		wrapper.in("id", bmIds);
		List<ZyjdBm> bmList = mapper.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(bmList)) {
			for (ZyjdBm zyjdBm : bmList) {
				// 批量标记驳回 如果状态不是审核通过，则不操作
				if (status == ZyjdBmStatus.BH && zyjdBm.getStatus() != ZyjdBmStatus.SHTG) {
					continue;
				}
				if (StringUtils.isNotEmpty(remark)) {
					zyjdBm.setRemark(StringUtils.isEmpty(zyjdBm.getRemark()) ? remark : zyjdBm.getRemark() + ";" + remark);
				}
				zyjdBm.setApproveTime(new Date());
				zyjdBm.setStatus(status);
			}
			DBUtils.updateBatchById(bmList, ZyjdBm.class);
		} else {
			throw BizException.withMessage("标记报名状态失败，因为指定的报名信息不存在");
		}
	}

	/**
	 * 导出登记照片
	 */
	public void expStudentPhoto(ZyjdBmQueryParams params, OutputStream os) {
		ZipOutputStream zipOutputStream = null;
		File tmpFolder = null;
		try {
			tmpFolder = FileHelper.createTmpFile();
			tmpFolder.mkdirs();
			zipOutputStream = new ZipOutputStream(os);
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			Set<String> sfzhSet = new HashSet<String>();
			params.setSize(Integer.MAX_VALUE);
			if (BaseUtil.isNotEmpty(params.getIds())) {
				params.setIdList(Arrays.asList(params.getIds().split(",")));
			}
			list = this.pageQuery(params).getRecords();
			int i = 0;
			for (Map<String, Object> map : list) {
				InputStream inputStream = null;
				i++;
				try {
					if (map.get("studentPhoto") != null && StringUtils.isNotEmpty(map.get("studentPhoto").toString())) {
						String sd_photo_url = BaseUtil.convertNullToEmpty(map.get("studentPhoto"));
						if (sd_photo_url.length() == 0) {
							continue;
						}
						if (sfzhSet.contains(BaseUtil.convertNullToEmpty(map.get("sfzh")))) {
							continue;
						} else {
							sfzhSet.add(BaseUtil.convertNullToEmpty(map.get("sfzh")));
						}
						String u_certino = BaseUtil.convertNullToEmpty(map.get("sfzh")) + "_"
								+ BaseUtil.convertNullToEmpty(map.get("studentName"));
						URL url = new URL(sd_photo_url);
						HttpURLConnection connection = (HttpURLConnection) url.openConnection();
						connection.setConnectTimeout(30000);
						connection.setReadTimeout(300000);
						if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
							inputStream = connection.getInputStream();
							int k = sd_photo_url.lastIndexOf(".");
							String ext = "";
							if (k > 0) {
								ext = sd_photo_url.substring(k + 1, sd_photo_url.length());
							}
							String entry_name = u_certino + "." + ext;

							zipOutputStream.putNextEntry(new ZipEntry(entry_name));
							IOUtils.copy(inputStream, zipOutputStream);
							zipOutputStream.closeEntry();
						}
					}
				} finally {
					if (inputStream != null) {
						IOUtils.closeQuietly(inputStream);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw BizException.withMessage(e.getMessage());
		} finally {
			FileHelper.delFile(tmpFolder);
			BaseUtil.close(zipOutputStream);
		}
	}

	/**
	 * 批量导出附件
	 */
	public void expAccessory(ZyjdBmQueryParams params, OutputStream os) throws Exception {
		ZipOutputStream zipOutputStream = null;
		File tmpFolder = FileHelper.createTmpFile();
		tmpFolder.mkdirs();
		zipOutputStream = new ZipOutputStream(os);
		params.setIdList(Arrays.asList(params.getIds().split(",")));
		List<Map<String, Object>> list = this.pageQuery(params).getRecords();
		Map<String, Object> isExist = new HashMap<>();

		try {
			for (Map<String, Object> map : list) {
				String studentName = BaseUtil.getStringValueFromMap(map, "studentName", "");
				String techLevel = BaseUtil.getStringValueFromMap(map, "applyTechLevel", "");
				String sfzh = BaseUtil.getStringValueFromMap(map, "sfzh", "");
				String professionName = BaseUtil.getStringValueFromMap(map, "professionName", "");
				String studentTicket = BaseUtil.convertNullToEmpty(map.get("studentTicket"));
				String sfzzm = BaseUtil.convertNullToEmpty(map.get("sfzzm"));
				String sfzfm = BaseUtil.convertNullToEmpty(map.get("sfzfm"));
				String studentPhoto = BaseUtil.convertNullToEmpty(map.get("studentPhoto"));
				String eduCertiPhoto = BaseUtil.convertNullToEmpty(map.get("eduCertiPhoto"));
				String gznxzm = BaseUtil.convertNullToEmpty(map.get("gznxzm"));
				String oldCertiPhoto = BaseUtil.convertNullToEmpty(map.get("oldCertiPhoto"));
				String newCertificate = BaseUtil.convertNullToEmpty(map.get("newCertificate"));
				String commitment = BaseUtil.convertNullToEmpty(map.get("commitment"));
				if (params.getType() == ZyjdBmBatchType.QDY) {
					professionName = professionName;
				} else {
					professionName = professionName + TechLevel.valueOf(techLevel).getName();
				}
				String u_name = studentName + "_" + sfzh;
				//判断是否有重复数据。有重复数据的话需要去掉。不然压缩成zip的时候会报异常
				if(isExist.containsKey(u_name)) {
					continue;
				}
				isExist.put(u_name, u_name);

				List<String> files = new ArrayList<>();
				files.add(studentPhoto);
				files.add(sfzfm);
				files.add(sfzzm);
				files.add(studentTicket);
				files.add(eduCertiPhoto);
				files.add(gznxzm);
				files.add(oldCertiPhoto);
				files.add(newCertificate);
				files.add(commitment);
				List<String> names = new ArrayList<>();
				names.add(u_name);
				names.add("身份证正面");
				names.add("身份证反面");
				names.add("学生证");
				names.add("学历证书");
				names.add("工作年限证明");
				names.add("原资格证书");
				names.add("新资格证书");
				names.add("承诺书");
				for (int j = 0; j < files.size(); j++) {
					if (StringUtils.isEmpty(files.get(j))) {
						continue;
					}
					InputStream fis = null;
					try {
						URL url = new URL(files.get(j));
						HttpURLConnection connection = (HttpURLConnection) url.openConnection();
						connection.setConnectTimeout(180000);
						connection.setReadTimeout(180000);
						if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
							fis = connection.getInputStream();
							int k = files.get(j).lastIndexOf(".");
							String ext = "";
							if (k > 0) {
								ext = files.get(j).substring(k + 1, files.get(j).length());
							}
							String entryName = "";
							entryName = names.get(j) + "." + ext;
							File sf = new File(professionName + "/" + u_name);
							zipOutputStream.putNextEntry(new ZipEntry(sf.getPath() + "/" + entryName));
							byte[] buffer = new byte[1024];
							int r = 0;
							while ((r = fis.read(buffer)) != -1) {
								zipOutputStream.write(buffer, 0, r);
							}
							zipOutputStream.closeEntry();
						}
					} finally {
						if (fis != null) {
							IOUtils.closeQuietly(fis);
						}
					}

				}
			}
		} finally {
			if (zipOutputStream != null) {
				zipOutputStream.flush();
				IOUtils.closeQuietly(zipOutputStream);
			}
		}
	}

	public Page getAnalysisInfo(ZyjdBmQueryParams params) {
		List<Map<String, Object>> list = mapper.getAnalysisInfo(params.getBmbatchId(), params);
		params.setRecords(list);
		return params;
	}

	public Page statistcCount(ZyjdBmQueryParams params) {
		List<Map<String, Object>> list = mapper.statistcCount(params.getCondition(), params);
		params.setRecords(list);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("data", params);
		// 计算合计汇总数据
		Integer totalCount = 0;
		Map<String, Object> totalMap = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			totalCount += BaseUtil.getIntValueFromMap(map, "totalCount", 0);
		}
		totalMap.put("title", "合计：");
		totalMap.put("totalCount", totalCount);
		list.add(totalMap);
		return params;
	}

	/**
	 * 标记退费
	 */
	@Transactional
	public void refund(String ids, String reason, String userId) {
		List<String> bmIds = Lists.newArrayList(StringUtils.split(ids, ","));
		EntityWrapper<ZyjdBm> wrapper = new EntityWrapper();
		wrapper.in("id", bmIds);
		List<ZyjdBm> bmList = mapper.selectList(wrapper);
		if (bmList.size() > 0) {
			for (ZyjdBm zyjdBm : bmList) {
				if (!PayStatus.YJ.equals(zyjdBm.getPayStatus())) {
					throw BizException.withMessage("操作失败，因为您选择的报名数据中包含未缴费的数据");
				}
				zyjdBm.setRefundAmount(zyjdBm.getAmount());
				zyjdBm.setMarkRefundUserId(userId);
				zyjdBm.setRemark(zyjdBm.getRemark() == null ? reason : zyjdBm.getRemark() + ";"+reason);

				zyjdBm.setPayStatus(PayStatus.REFUND);
				zyjdBm.setMarkPaydUserId(null);
				zyjdBm.setAmount(null);
				zyjdBm.setPayType(null);

				mapper.updateAllColumnById(zyjdBm);
			}
		} else {
			throw BizException.withMessage("标记退费失败，因为指定的报名信息不存在");
		}
	}


	public boolean updateRec(ZyjdBmQueryParams params){
		return mapper.updateRec(params.getCondition());
	}


	public boolean updateRecByIds(ZyjdBmQueryParams params){
		return mapper.updateRecByIds(params.getCondition());
	}

	public boolean updateRecByCode(ZyjdBmQueryParams params){
		return mapper.updateRecByCode(params.getCondition());
	}

	public boolean signRec(ZyjdBmQueryParams params){
		return mapper.signRec(params.getCondition());
	}

	public Double sumAmount(ZyjdBmQueryParams params){return mapper.sumAmount(params.getCondition());}

	public void exportZyjs(ZyjdBmQueryParams params, OutputStream os) throws IOException, WriteException {
		Map<String, List<Dict>> dictList = dictService.getZyjsbmDictList();
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page pageInfo = this.pageQuery(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("专业技术人员报名汇总表", 0);
		// 建表
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "行政区划代码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "证件类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "证件号码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "民族", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "出生日期", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "学历", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "学位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "所在单位统一社会信用代码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "所在单位名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "所在单位性质", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "是否由所在单位提供继续教育培训", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "取得证书类型", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职称系列", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职称名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职称级别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职业资格名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "职业资格等级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);

			ws.setColumnView(i++, 20);
			ws.setColumnView(i++, 10);
		}
		row = 1;
		int i = 1;
		// 写表
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("regionalismCode") != null ? map.get("regionalismCode") : ""),
					OfficeToolExcel.getNormolCell()));
			Optional<Dict> dict = dictList.get("certiCategorys").stream().filter(x -> Objects.equals(x.getDictValue(), String.valueOf(map.get("certiCategory")))).findFirst();
			String certiCategory = dict.isPresent()?dict.get().getDictName():"";
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(certiCategory), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("xb") != null ? map.get("xb") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mz") != null ? map.get("mz") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("birthday") != null ? map.get("birthday") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("xl") != null ? map.get("xl") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("xw") != null ? map.get("xw") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("socialCreditCode") != null ? map.get("socialCreditCode") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("unitNature") != null ? map.get("unitNature") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("isProvideJxjy") != null ? map.get("isProvideJxjy") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("obtainCertiCategory") != null ? map.get("obtainCertiCategory") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zcSeries") != null ? map.get("zcSeries") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zc") != null ? map.get("zc") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zcLevel") != null ? map.get("zcLevel") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zyQualificationName") != null ? map.get("zyQualificationName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("zyQualificationLevel") != null ? map.get("zyQualificationLevel") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	public void exportReceipt(ZyjdBmQueryParams params, OutputStream os) throws IOException {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		String tempDir = attConfig.getTempdir() + "/zyjs/receipt";
		List<File> imgs = new ArrayList<>();
		for (Map<String, Object> map : list) {
			if (map.get("receipt") != null) {
				String receipt = map.get("receipt").toString();
				URL url = new URL(receipt);
				HttpURLConnection connection = (HttpURLConnection) url.openConnection();
				connection.setRequestMethod("GET");
				connection.setDoOutput(true);
				try(InputStream in = connection.getInputStream()) {
					File img = new File(tempDir + "/imgs/" + map.get("sfzh").toString() + ".png");
					if (!img.exists()) {
						img.getParentFile().mkdirs();
					}
					try (FileOutputStream imgOs = new FileOutputStream(img)) {
						IOUtils.copy(in, imgOs);
					}
					imgs.add(img);
				}
			}
		}
		String zip = tempDir + "/receipt_" + System.currentTimeMillis() + ".zip";
		ZipUtils.doZip(new File(zip), imgs);
        try (FileInputStream zipOs = new FileInputStream(zip)) {
            IOUtils.copy(zipOs, os);
        }
	}

	public void exportZcpsBmList(ZyjdBmQueryParams params, OutputStream os) throws IOException, WriteException {
		params.setSize(Integer.MAX_VALUE);
		Page pageInfo = this.pageQuery(params);
		List<Map<String, Object>> bmList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("专业技术人员报名汇总表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "批次", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "证件号码", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "分类", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "等级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "工种", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "提交时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bmList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("bmbatchName") != null ? map.get("bmbatchName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			String industryId = BaseUtil.getStringValueFromMap(map, "industryId");
			String professionCategory = "";
			if ("DL".equals(industryId)) {
				professionCategory = "电力行业";
			} else if ("TY".equals(industryId)) {
				professionCategory = "通用行业";
			}
			ws.addCell(new Label(col++, row, professionCategory, OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					TechLevel.findByEnumName(BaseUtil.getStringValueFromMap(map, "applyTechLevel")).getDesc(),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("professionName") != null ? map.get("professionName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("submitTime") != null ? map.get("submitTime") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	//答辩评审成绩查询
	public Object zcpsScorePage(ZcpsScoreParams params) {
		List<Map<String, Object>> list = mapper.zcpsScorePage(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	public Map<String, Object> zcpsScoreImport(MultipartFile file, String bmbatchId, String userId) throws IOException, BiffException {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (org.apache.commons.lang3.StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "score_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[]{"sfzh", "exam_score", "skill_score","job_score","potential_score", "synthesize_score", "thesis_score"});
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}

		Set<String> sfzhs = new HashSet<>();

		int row = 0;
		int cfCou = 0;
		int cwCou = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			String sfzh = org.apache.commons.lang.StringUtils.trimToNull(map.get("sfzh"));
			if (org.apache.commons.lang.StringUtils.isEmpty(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行证件号为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (sfzhs.contains(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行证件号在excel中重复。";
				log.append(msg);
				cfCou++;
				continue;
			}
			sfzhs.add(sfzh);
			List<ZyjdBm> zyjdBms = mapper.getBmBySfzh(bmbatchId, sfzh);
			if (zyjdBms == null || zyjdBms.size() < 1) {
				log.append("<br>");
				String msg = "第" + row + "行学员未报名该批次。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String examScore = BaseUtil.getStringValueFromMap(map, "exam_score");
			if (StringUtils.isEmpty(examScore)) {
				log.append("<br>");
				String msg = "第" + row + "行理论考试成绩为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (!StringUtils.isNumeric(examScore) || Integer.parseInt(examScore) < 0) {
				log.append("<br>");
				String msg = "第" + row + "行理论考试成绩格式错误。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String skillScore = BaseUtil.getStringValueFromMap(map, "skill_score");
			if (StringUtils.isEmpty(skillScore)) {
				log.append("<br>");
				String msg = "第" + row + "行操作技能成绩为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (!StringUtils.isNumeric(skillScore) || Integer.parseInt(skillScore) < 0) {
				log.append("<br>");
				String msg = "第" + row + "行操作技能成绩格式错误。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String jobScore = BaseUtil.getStringValueFromMap(map, "job_score");
			if (StringUtils.isEmpty(jobScore)) {
				log.append("<br>");
				String msg = "第" + row + "行工作业绩成绩为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (!StringUtils.isNumeric(jobScore) || Integer.parseInt(jobScore) < 0) {
				log.append("<br>");
				String msg = "第" + row + "行工作业绩成绩格式错误。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String potentialScore = BaseUtil.getStringValueFromMap(map, "potential_score");
			if (StringUtils.isEmpty(potentialScore)) {
				log.append("<br>");
				String msg = "第" + row + "行潜在能力成绩为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (!StringUtils.isNumeric(potentialScore) || Integer.parseInt(potentialScore) < 0) {
				log.append("<br>");
				String msg = "第" + row + "行潜在能力成绩格式错误。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String synthesizeScore = BaseUtil.getStringValueFromMap(map, "synthesize_score");
			if (StringUtils.isEmpty(synthesizeScore)) {
				log.append("<br>");
				String msg = "第" + row + "行综合评定成绩为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (!StringUtils.isNumeric(synthesizeScore) || Integer.parseInt(synthesizeScore) < 0) {
				log.append("<br>");
				String msg = "第" + row + "行综合评定成绩格式错误。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String thesisScore = BaseUtil.getStringValueFromMap(map, "thesis_score");
			ZyjdBm zyjdBm = zyjdBms.get(0);
			if (!this.checkIsDbTeacher(zyjdBm.getId(), userId, "2")) {
				throw BizException.withMessage("您不是该组的评委老师，不能进行成绩录入");
			}
			zyjdBm.setExamScore(Double.parseDouble(examScore));
			zyjdBm.setSkillScore(Double.parseDouble(skillScore));
			zyjdBm.setJobScore(Double.parseDouble(jobScore));
			zyjdBm.setPotentialScore(Double.parseDouble(potentialScore));
			zyjdBm.setSynthesizeScore(Double.parseDouble(synthesizeScore));
			zyjdBm.setThesisScore(StringUtils.isNotEmpty(thesisScore)? Double.parseDouble(thesisScore):null);
			mapper.updateById(zyjdBm);
		}
		int sucCount = (list.size() - 1) - cfCou - cwCou;
		// 提示总共多少条，导入成功多少条，重复数据多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (list.size() - 1) + "条，导入成功" + sucCount + "条，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public void zcpsMark(ZcThesisScore zcThesisScore, String files, String userId) {
		ZyjdBm zyjdBm = zyjdBmMapper.selectById(zcThesisScore.getBmId());
		if (zyjdBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		if (!checkIsDbTeacher(zcThesisScore.getBmId(), userId, "1")) {
			throw BizException.withMessage("您不是该组的评委老师，不能进行论文打分");
		}
		double totalScore = 0;
		if (Objects.equals(zyjdBm.getApplyTechLevel(), TechLevel.ONE)) {
			if (zcThesisScore.getLanguageExpressionScore() == null) {
				throw BizException.withMessage("语言表达成绩不能为空");
			}
			if (zcThesisScore.getAnswerCorrectnessScore() == null) {
				throw BizException.withMessage("回答正确性成绩不能为空");
			}
			if (zcThesisScore.getAnswerLogicScore() == null) {
				throw BizException.withMessage("回答逻辑性成绩不能为空");
			}
			totalScore = zcThesisScore.getActualEffectScore() + zcThesisScore.getCorrectnessScore() + zcThesisScore.getInnovateScore() + zcThesisScore.getLanguageExpressionScore()
					+ zcThesisScore.getUtilityScore() + zcThesisScore.getWritingExpressionScore() + zcThesisScore.getAnswerCorrectnessScore() + zcThesisScore.getAnswerLogicScore();
			if (totalScore > 100) {
				throw BizException.withMessage("分数异常，分数之和不能超过100分");
			}
		} else if (Objects.equals(zyjdBm.getApplyTechLevel(), TechLevel.TWO)) {
			totalScore = zcThesisScore.getActualEffectScore() + zcThesisScore.getCorrectnessScore() + zcThesisScore.getInnovateScore()
					+ zcThesisScore.getUtilityScore() + zcThesisScore.getWritingExpressionScore();
			if (totalScore > 100) {
				throw BizException.withMessage("分数异常，分数之和不能超过100分");
			}
		}
		List<ZcThesisScore> thesisScores = zcThesisScoreMapper.selectList(new EntityWrapper<ZcThesisScore>().eq("bm_id", zcThesisScore.getBmId()));
		if (thesisScores.isEmpty()) {
			zcThesisScore.setId(BaseUtil.generateId2());
			zcThesisScoreMapper.insert(zcThesisScore);
		} else {
			zcThesisScoreMapper.update(zcThesisScore, new EntityWrapper<ZcThesisScore>().eq("bm_id", zcThesisScore.getBmId()));
		}
		if (StringUtils.isNotEmpty(files)) {
			for (String url : files.split(",")) {
				zcFileMapper.delete(new EntityWrapper<ZcFile>().eq("bm_id", zcThesisScore.getBmId()));
				ZcFile zcFile = new ZcFile();
				zcFile.setId(BaseUtil.generateId2());
				zcFile.setBmId(zcThesisScore.getBmId());
				zcFile.setUrl(url);
				zcFileMapper.insert(zcFile);
			}
		}
		zyjdBm.setThesisScore(totalScore);
		zyjdBmMapper.updateById(zyjdBm);
	}

	public Object markDetail(String bmId) {
		List<ZcThesisScore> thesisScores = zcThesisScoreMapper.selectList(new EntityWrapper<ZcThesisScore>().eq("bm_id", bmId));
		if (thesisScores.isEmpty()) {
			return null;
		} else {
			return thesisScores.get(0);
		}
	}

	/**
	 * 检测老师是否能够打分
	 * @param bmId 报名id
	 * @param teacherId 老师id
	 * @param type 分组类型
	 * @return 布尔值
	 */
	private Boolean checkIsDbTeacher(String bmId, String teacherId, String type) {
		ZyjdBm zyjdBm = zyjdBmMapper.selectById(bmId);
		List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>().eq("bmbatch_id", zyjdBm.getBmbatchId())
				.eq("type", type));
		Integer count = zcGroupUserMapper.selectCount(new EntityWrapper<ZcGroupUser>().eq("user_id", teacherId)
				.in("group_id", zcGroups.stream().map(ZcGroup::getId).collect(Collectors.toList())));
		return count>0;
	}

	@Transactional(rollbackFor = Exception.class)
	public void psMark(String bmId, ZyjdBm zyjdBm, String userId) {
		if (!this.checkIsDbTeacher(bmId, userId, "2")) {
			throw BizException.withMessage("您不是该组的评委老师，不能进行成绩录入");
		}
		ZyjdBm bm = zyjdBmMapper.selectById(bmId);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		List<ZcGroup> zcGroups = zcGroupMapper.selectList(new EntityWrapper<ZcGroup>()
				.eq("bmbatch_id", bm.getBmbatchId()).eq("type", "2"));
		if (zcGroups.isEmpty()) {
			throw BizException.withMessage("未参与评审不能打分");
		}
		bm.setExamScore(zyjdBm.getExamScore());
		bm.setSkillScore(zyjdBm.getSkillScore());
		bm.setJobScore(zyjdBm.getJobScore());
		bm.setPotentialScore(zyjdBm.getPotentialScore());
		bm.setSynthesizeScore(zyjdBm.getSynthesizeScore());
		bm.setThesisScore(zyjdBm.getThesisScore());
		zyjdBmMapper.updateById(bm);
		// 同一个报名只允许两个打分
		EntityWrapper<ZcReviewMark> wrapper = new EntityWrapper<ZcReviewMark>();
		wrapper.eq("bm_id", bmId);
		List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(wrapper);
		// 非自己评分超过2次的不允许再次评分
		if (zcReviewMarks.stream().filter(x -> !x.getUserId().equals(userId)).count() >= 2) {
			throw BizException.withMessage("评审打分次数超过上限");
		}
		zcReviewMarks = zcReviewMarks.stream().filter(x -> x.getUserId().equals(userId)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(zcReviewMarks)) {
			ZcReviewMark zcReviewMark = new ZcReviewMark();
			zcReviewMark.setId(BaseUtil.generateId2());
			zcReviewMark.setBmId(bmId);
			zcReviewMark.setScore(Double.parseDouble(zyjdBm.getScore()));
			zcReviewMark.setGrade(scoreConversion(Double.parseDouble(zyjdBm.getScore())));
			zcReviewMark.setUserId(userId);
			zcReviewMarkMapper.insert(zcReviewMark);
		} else {
			ZcReviewMark zcReviewMark = zcReviewMarks.get(0);
			zcReviewMark.setBmId(bmId);
			zcReviewMark.setScore(Double.parseDouble(zyjdBm.getScore()));
			zcReviewMark.setGrade(scoreConversion(Double.parseDouble(zyjdBm.getScore())));
			zcReviewMark.setUserId(userId);
			zcReviewMarkMapper.updateById(zcReviewMark);
		}
	}

	/**
	 * 答辩评审导出成绩
	 * @param params
	 * @param os
	 */
	public void exportScore(ZcpsScoreParams params, OutputStream os) throws IOException, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = (Page<Map<String, Object>>) this.zcpsScorePage(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("答辩评审成绩表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "申报", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "理论考试", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "操作技能", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "工作业绩", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "潜在能力", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 30);
			ws.addCell(new Label(i, row, "综合评定", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "论文得分", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "评审得分", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "预评定", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
					OfficeToolExcel.getNormolCell()));
			String industryId = BaseUtil.getStringValueFromMap(map, "industryId");
			String applyTechLevel = BaseUtil.getStringValueFromMap(map, "applyTechLevel");
			String professionName = BaseUtil.getStringValueFromMap(map, "professionName");
			String apply = "";
			if (BaseUtil.isNotEmpty(industryId) && BaseUtil.isNotEmpty(applyTechLevel) && BaseUtil.isNotEmpty(professionName)) {
				if ("TY".equals(industryId)) {
					apply += "通用";
				} else if ("DL".equals(industryId)) {
					apply += "电力";
				}
				if (TechLevel.findByEnumName(applyTechLevel) != null) {
					apply += "-" + TechLevel.findByEnumName(applyTechLevel).getDesc();
				}
				apply += professionName;
			}
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(apply),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("examScore") != null ? map.get("examScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("skillScore") != null ? map.get("skillScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("jobScore") != null ? map.get("jobScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("potentialScore") != null ? map.get("potentialScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("synthesizeScore") != null ? map.get("synthesizeScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("thesisScore") != null ? map.get("thesisScore") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("score") != null ? map.get("score") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(null),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 评审打分详情
	 * @param bmId 报名id
	 * @param loginUserId 登陆用户id
	 * @return 成绩详情
	 */
	public Map<String, Object> psMarkDetail(String bmId, String loginUserId) {
		Map<String, Object> result = new HashMap<>();
		ZyjdBm zyjdBm = mapper.selectById(bmId);
		if (zyjdBm != null) {
			result.put("examScore", zyjdBm.getExamScore());
			result.put("skillScore", zyjdBm.getSkillScore());
			result.put("jobScore", zyjdBm.getJobScore());
			result.put("potentialScore", zyjdBm.getPotentialScore());
			result.put("synthesizeScore", zyjdBm.getSynthesizeScore());
			result.put("thesisScore", zyjdBm.getThesisScore());
		}
		List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>()
				.eq("bm_id", bmId).eq("user_id", loginUserId));
		ZcReviewMark zcReviewMark = zcReviewMarks.isEmpty() ? null : zcReviewMarks.get(0);
		if (zcReviewMark != null) {
			result.put("score", zcReviewMark.getScore());
		}
		return result;
	}

	/**
	 * 导出申报信息pdf
	 */
	public void exportPdf(ZcpsScoreParams params, OutputStream os) throws IOException, DocumentException {
        params.setSize(Integer.MAX_VALUE);
        Page<Map<String, Object>> pageInfo = (Page<Map<String, Object>>) this.zcpsScorePage(params);
		List<Map<String, Object>> bmList = pageInfo.getRecords();
		//pdf
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp_student_bm_info");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		List<File> files = new ArrayList<>();
		for (Map<String, Object> map : bmList) {
			String bmId = BaseUtil.getStringValueFromMap(map, "id");
			String sfzh = BaseUtil.getStringValueFromMap(map, "sfzh");

			ZyjdBm zyjdBm = zyjdBmMapper.selectById(bmId);

			File outPdf = new File(tempdir, sfzh + ".pdf");
			fillCertiInfo(zyjdBm, outPdf);
			files.add(outPdf);
		}
		File zipFile = new File(tempdir, "bmInfo.zip");
		ZipUtils.doZip(zipFile, files);
		IOUtils.copy(new FileInputStream(zipFile), os);
	}

	private void fillCertiInfo(ZyjdBm zyjdBm, File outPdf) throws DocumentException, IOException {
		PdfReader reader = null;
		if (Objects.equals(zyjdBm.getIndustryId(), "TY")) {
			reader = new PdfReader(zcpsTYBmInfoTemplate);
		} else if (Objects.equals(zyjdBm.getIndustryId(), "DL")) {
			reader = new PdfReader(zcpsDLBmInfoTemplate);
		} else {
			throw BizException.withMessage("报考类型异常，请联系管理员处理");
		}
		FileOutputStream out = new FileOutputStream(outPdf);
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		PdfStamper stamper = new PdfStamper(reader, bos);
		AcroFields form = stamper.getAcroFields();

		BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
		StudentUser studentUser = studentUserMapper.selectById(zyjdBm.getStudentId());
		StudentInfo studentInfo = studentInfoMapper.getByStudentId(zyjdBm.getStudentId());
		//设置基础信息
		//通用
		form.setFieldProperty("name", "textfont", baseFont, null);
		form.setField("name", studentInfo.getName());
		form.setFieldProperty("sfzh", "textfont", baseFont, null);
		form.setField("sfzh", studentInfo.getSfzh());
		form.setFieldProperty("zc", "textfont", baseFont, null);
		form.setField("zc", studentInfo.getZc());
		form.setFieldProperty("company", "textfont", baseFont, null);
		form.setField("company", studentUser.getCompany());
		form.setFieldProperty("professionName", "textfont", baseFont, null);
		form.setField("professionName", zyjdProfessionMapper.selectById(zyjdBm.getProfessionId()).getName());
		form.setFieldProperty("techLevel", "textfont", baseFont, null);
		form.setField("techLevel", zyjdBm.getApplyTechLevel().getDesc());
		form.setFieldProperty("gender", "textfont", baseFont, null);
		form.setField("gender", studentInfo.getGender().getName());
		form.setFieldProperty("birthday", "textfont", baseFont, null);
		form.setField("birthday", studentInfo.getBirthday());
		form.setFieldProperty("certiAddress", "textfont", baseFont, null);
		form.setField("certiAddress", studentInfo.getCertiAddress());//籍贯
		form.setFieldProperty("politicalType", "textfont", baseFont, null);
		form.setField("politicalType", studentInfo.getPoliticalType() != null ? studentInfo.getPoliticalType().getName() : null);//政治面貌
		form.setFieldProperty("education", "textfont", baseFont, null);
		form.setField("education", studentInfo.getEducation() != null ? studentInfo.getEducation().getName():null);//文化程度
		form.setFieldProperty("workTime", "textfont", baseFont, null);
		form.setField("workTime", DateUtils.format(zyjdBm.getWorkTime(), "yyyy-MM-dd"));//从业时间
		form.setFieldProperty("profession", "textfont", baseFont, null);
		form.setField("profession", zyjdBm.getProfession());//现职业
		form.setFieldProperty("oldTechLevel", "textfont", baseFont, null);
		form.setField("oldTechLevel", zyjdBm.getOldTechLevel() != null ? zyjdBm.getOldTechLevel().getDesc(): null);//原职业资格等级
		form.setFieldProperty("workYears", "textfont", baseFont, null);
		form.setField("workYears", zyjdBm.getWorkYears());//从事本职业工作年限
		form.setFieldProperty("reward", "textfont", baseFont, null);
		form.setField("reward", BaseUtil.isNotEmpty(zyjdBm.getReward()) ? BaseUtil.delHTMLTag(zyjdBm.getReward()) : null);//奖励
		form.setFieldProperty("examScore", "textfont", baseFont, null);
		form.setField("examScore", BaseUtil.isNotEmpty(zyjdBm.getExamScore()) ? BaseUtil.convertNullToEmpty(zyjdBm.getExamScore()) : null);//理论考试成绩
		form.setFieldProperty("skillScore", "textfont", baseFont, null);
		form.setField("skillScore", BaseUtil.isNotEmpty(zyjdBm.getSkillScore()) ? BaseUtil.convertNullToEmpty(zyjdBm.getSkillScore()) : null);//操作技能成绩
		form.setFieldProperty("synthesizeScore", "textfont", baseFont, null);
		form.setField("synthesizeScore", BaseUtil.isNotEmpty(zyjdBm.getSynthesizeScore()) ? BaseUtil.convertNullToEmpty(zyjdBm.getSynthesizeScore()) : null);//综合评定成绩
		if (Objects.equals(zyjdBm.getIndustryId(), "TY")) {
			JSONArray array = JSON.parseArray(zyjdBm.getWorkExperience());
			if (array != null && array.size() > 0) {
				for (int i = 0; i < array.size(); i++) {
					String content = array.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						if (j > 0) {
							form.setFieldProperty("workExperience" + i + j, "textfont", baseFont, null);
							// 工作经历
							form.setField("workExperience" + i + j,
									j == 1 ? contents[j - 1] + "至" + contents[j] : contents[j]);
						}
					}
				}
			}
		}
		// 电力
		else if (Objects.equals(zyjdBm.getIndustryId(), "DL")) {
			form.setFieldProperty("mobile", "textfont", baseFont, null);
			form.setField("mobile", studentInfo.getMobile());
			form.setFieldProperty("nation", "textfont", baseFont, null);
			form.setField("nation", studentInfo.getNation() != null ? studentInfo.getNation().getName() : null);
			form.setFieldProperty("jsTime", "textfont", baseFont, null);
			form.setField("jsTime", DateUtils.format(zyjdBm.getJsTime(), "yyyy-MM-dd"));
			form.setFieldProperty("gw", "textfont", baseFont, null);
			form.setField("gw", studentInfo.getGw());
			JSONArray wdlList = JSON.parseArray(zyjdBm.getWorkExperienceDl());
			if (wdlList != null && wdlList.size() > 0) {
				for (int i = 0; i < wdlList.size(); i++) {
					String content = wdlList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						if (j > 0) {
							form.setFieldProperty("workExperienceDl" + i + j, "textfont", baseFont, null);
							// 工作经历
							form.setField("workExperienceDl" + i + j,
									j == 1 ? contents[j - 1] + "至" + contents[j] : contents[j]);
						}
					}
				}
			}
			JSONArray trainingExperienceList = JSON.parseArray(zyjdBm.getTrainingExperience());
			if (trainingExperienceList != null && trainingExperienceList.size() > 0) {
				for (int i = 0; i < trainingExperienceList.size(); i++) {
					String content = trainingExperienceList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						if (j > 0) {
							form.setFieldProperty("trainingExperience" + i + j, "textfont", baseFont, null);
							// 主要学习、培训经历
							form.setField("trainingExperience" + i + j,
									j == 1 ? contents[j - 1] + "至" + contents[j] : contents[j]);
						}
					}
				}
			}
			JSONArray jsWorkAchievementList = JSON.parseArray(zyjdBm.getJsWorkAchievement());
			if (jsWorkAchievementList != null && jsWorkAchievementList.size() > 0) {
				for (int i = 0; i < jsWorkAchievementList.size(); i++) {
					String content = jsWorkAchievementList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						if (j > 0) {
							form.setFieldProperty("jsWorkAchievement" + i + j, "textfont", baseFont, null);
							// 取得技师资格后的主要工作业绩
							form.setField("jsWorkAchievement" + i + j,
									j == 1 ? contents[j - 1] + "至" + contents[j] : contents[j]);
						}
					}
				}
			}
			form.setFieldProperty("skillSpeciality", "textfont", baseFont, null);
			form.setField("skillSpeciality", BaseUtil.isNotEmpty(zyjdBm.getSkillSpeciality()) ? BaseUtil.delHTMLTag(zyjdBm.getSkillSpeciality()) : null);//技能专长
	
			JSONArray xmList = JSON.parseArray(zyjdBm.getXm());
			if (xmList != null && xmList.size() > 0) {
				for (int i = 0; i < xmList.size(); i++) {
					String content = xmList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						form.setFieldProperty("xm" + i + j, "textfont", baseFont, null);
						form.setField("xm" + i + j, contents[j]);//技术革新、技术改造、科技成果转化、关键问题处理
					}
				}
			}
			JSONArray opusList = JSON.parseArray(zyjdBm.getOpus());
			if (opusList != null && opusList.size() > 0) {
				for (int i = 0; i < opusList.size(); i++) {
					String content = opusList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						form.setFieldProperty("opus" + i + j, "textfont", baseFont, null);
						form.setField("opus" + i + j, contents[j]);//编写操作规程、规范、标准、教案及发表论文、著作等情况
					}
				}
			}
			JSONArray competitionList = JSON.parseArray(zyjdBm.getCompetition());
			if (competitionList != null && competitionList.size() > 0) {
				for (int i = 0; i < competitionList.size(); i++) {
					String content = competitionList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						form.setFieldProperty("competition" + i + j, "textfont", baseFont, null);
						form.setField("competition" + i + j, contents[j]);//参加技能竞赛获奖情况
					}
				}
			}
			JSONArray bearContentList = JSON.parseArray(zyjdBm.getBearContent());
			if (bearContentList != null && bearContentList.size() > 0) {
				for (int i = 0; i < bearContentList.size(); i++) {
					String content = bearContentList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						form.setFieldProperty("bearContent" + i + j, "textfont", baseFont, null);
						form.setField("bearContent" + i + j, contents[j]);//承担技艺传授、技能培训工作情况
					}
				}
			}
			JSONArray honorList = JSON.parseArray(zyjdBm.getHonor());
			if (honorList != null && honorList.size() > 0) {
				for (int i = 0; i < honorList.size(); i++) {
					String content = honorList.getString(i).replace("|^|", "/**/");
					String[] contents = content.split("/\\*\\*/");
					for (int j = 0; j < contents.length; j++) {
						form.setFieldProperty("honor" + i + j, "textfont", baseFont, null);
						form.setField("honor" + i + j, contents[j]);//获得荣誉称号
					}
				}
			}
	
			JSONObject technicalSummary = JSON.parseObject(zyjdBm.getTechnicalSummary());
			if (technicalSummary != null) {
				for (Map.Entry<String, Object> entry : technicalSummary.entrySet()) {
					form.setFieldProperty("technicalSummary_"+entry.getKey(), "textfont", baseFont, null);
					form.setField("technicalSummary_"+entry.getKey(), (String) entry.getValue());//技术总结
				}
			}
	
			JSONObject thesis = JSON.parseObject(zyjdBm.getThesis());
			if (thesis != null) {
				for (Map.Entry<String, Object> entry : thesis.entrySet()) {
					form.setFieldProperty("thesis_"+entry.getKey(), "textfont", baseFont, null);
					form.setField("thesis_"+entry.getKey(), (String) entry.getValue());//论文
				}
			}
		}
		form.setFieldProperty("jobScore", "textfont", baseFont, null);
		form.setField("jobScore", BaseUtil.convertNullToEmpty(zyjdBm.getJobScore()));//工作业绩得分
		form.setFieldProperty("potentialScore", "textfont", baseFont, null);
		form.setField("potentialScore", BaseUtil.convertNullToEmpty(zyjdBm.getPotentialScore()));//潜在能力得分

        //设置学员头像
        int pageNo = form.getFieldPositions("photo").get(0).page;
        Rectangle signRect = form.getFieldPositions("photo").get(0).position;
        float x = signRect.getLeft();
        float y = signRect.getBottom();
        //根据路径读取图片
        Image image = Image.getInstance(studentInfo.getStudentPhoto());
        //获取图片页面
        PdfContentByte under = stamper.getOverContent(pageNo);
        //图片大小自适应
        image.scaleToFit(signRect.getWidth(), signRect.getHeight());
		image.scaleAbsolute(signRect.getWidth(), signRect.getHeight());
        //添加图片
        image.setAbsolutePosition(x, y);
        under.addImage(image);

        //生成目标PDF文件
        stamper.setFormFlattening(true);
        stamper.close();
        Document doc = new Document();
        PdfCopy copy = new PdfCopy(doc, out);
        doc.open();
        copy.addDocument(new PdfReader(bos.toByteArray()));
        doc.close();
	}

	public Object psList(ZcpsScoreParams params) {
		// 根据配置查询可评选工种得列表
		UserInfo userInfo = userInfoMapper.getByUserId(params.getLoginUserId());
		if (userInfo != null && StringUtils.isNotEmpty(userInfo.getProfessionIds())) {
			 params.setProfessionIds(Arrays.asList(userInfo.getProfessionIds().split(",")));
		}
		List<Map<String, Object>> list = mapper.psList(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 争议列表 只查询成绩为AC和BC
	 * @param params 参数
	 * @return 结果
	 */
	public Object zyList(ZcpsScoreParams params) {
		List<Map<String, Object>> list = mapper.zyList(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 争议投票
	 * @param bmId 报名id
	 * @param grade 投票结果
	 * @param userId 老师id
	 */
	public void zyVote(String bmId, String grade, String userId) {
		EntityWrapper<ZcReviewVote> wrapper = new EntityWrapper<>();
		wrapper.eq("bm_id", bmId);
		wrapper.eq("user_id", userId);
		List<ZcReviewVote> zcReviewVotes = zcReviewVoteMapper.selectList(wrapper);
		if (CollectionUtils.isEmpty(zcReviewVotes)) {
			ZcReviewVote zcReviewVote = new ZcReviewVote();
			zcReviewVote.setId(BaseUtil.generateId());
			zcReviewVote.setBmId(bmId);
			zcReviewVote.setGrade(grade);
			zcReviewVote.setUserId(userId);
			zcReviewVoteMapper.insert(zcReviewVote);
		} else {
			ZcReviewVote zcReviewVote = zcReviewVotes.get(0);
			zcReviewVote.setGrade(grade);
			zcReviewVoteMapper.updateById(zcReviewVote);
		}
	}

	/**
	 * 投票详情
	 * @param bmId 报名id
	 * @return 各种投票数量结果
	 */
	public Map<String, Object> voteDetail(String bmId) {
		List<Map<String, Object>> list = zcReviewVoteMapper.voteDetail(bmId);
		Map<String, Object> map = new HashMap<>();
		map.put("total", list.stream().mapToInt(m -> Integer.parseInt(m.get("num").toString())).sum());

		String[] fixedGrades = {"AA", "AB", "BB", "CC"};
		Map<String, Object> gradeMap = list.stream()
				.collect(Collectors.toMap(
						item -> (String) item.get("grade"),
						item -> item.get("num")
				));

		List<Map<String, Object>> resultList = Arrays.stream(fixedGrades)
				.map(grade -> {
					Map<String, Object> resultItem = new HashMap<>();
					resultItem.put("grade", grade);
					resultItem.put("num", gradeMap.getOrDefault(grade, 0)); // 如果不存在则num为0
					return resultItem;
				})
				.collect(Collectors.toList());
		map.put("list",resultList);
		return map;
	}

	/**
	 * 组长最终打分
	 * @param bmId 报名id
	 * @param grade 最终成绩 AA AB BB CC
	 * @param remark 备注
	 */
	@Transactional(rollbackFor = Exception.class)
	public void finalScore(String bmId, String grade, String remark,String userId) {
		// 判断是否是组长
		if (zcReviewVoteMapper.countLeader(bmId,userId) == 0) {
			 throw BizException.withMessage("您不是组长，无法打分");
		}
		EntityWrapper<ZcReviewMark> wrapper = new EntityWrapper<ZcReviewMark>();
		wrapper.eq("bm_id", bmId);
		List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(wrapper);
		char[] chars = grade.toCharArray();
		List<ZcReviewMark> reviewMarks = zcReviewMarks.stream().sorted(Comparator.comparing(ZcReviewMark::getScore).reversed()).collect(Collectors.toList());
		for (int i = 0; i < 2; i++) {
			if (reviewMarks.size() == i) {
				continue;
			}
			ZcReviewMark zcReviewMark = reviewMarks.get(i);
			zcReviewMark.setGrade(chars[i] + "");
			zcReviewMark.setRemark(remark);
			zcReviewMarkMapper.updateById(zcReviewMark);
		}
	}

	public Object scoreDetail(ZyjdBmQueryParams params) {
		List<Map<String, Object>> list = mapper.scoreDetail(params.getCondition(), params);
		if (list != null && !list.isEmpty()) {
			List<String> bmIds = list.stream().map(x -> x.get("id").toString()).collect(Collectors.toList());
			List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().in("bm_id", bmIds));
			if (CollectionUtils.isNotEmpty(zcReviewMarks)) {
				Map<String, List<ZcReviewMark>> groups = zcReviewMarks.stream().collect(Collectors.groupingBy(ZcReviewMark::getBmId));
				for (Map<String, Object> map : list) {
					List<ZcReviewMark> reviewMarks = groups.get(map.get("id").toString());
					String judges1 = "";
					String judges2 = "";
					String result = "";
					if (CollectionUtils.isNotEmpty(reviewMarks)) {
						reviewMarks.sort(Comparator.comparing(ZcReviewMark::getGrade));
						judges1 = reviewMarks.size() > 0 ? reviewMarks.get(0).getGrade() : "";
						judges2 = reviewMarks.size() > 1 ? reviewMarks.get(1).getGrade() : "";
						result = judges1 + judges2;
					}
					map.put("judges1", judges1);
					map.put("judges2", judges2);
					map.put("result", result);
				}
			}
		}
		params.setRecords(list);
		return params;
	}

	private String scoreConversion(double score){
		String result = "";
		if (score < 60) {
			result = "C";
		} else if (score>=60 && score <80) {
			result = "B";
		} else if (score>=80 && score <=100) {
			result = "A";
		}
		return result;
	}

	public void exportScoreDetail(ZyjdBmQueryParams params, OutputStream os) throws IOException, WriteException {
		List<Map<String, Object>> bizZyjdList = new ArrayList<Map<String, Object>>();
		params.setSize(Integer.MAX_VALUE);
		Page<Map<String, Object>> pageInfo = (Page<Map<String, Object>>) this.scoreDetail(params);
		bizZyjdList = pageInfo.getRecords();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("打分详情表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "级别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "工种", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "评委1", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "评委2", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "结果", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
		}
		row = 1;
		int i = 1;
		for (Map<String, Object> map : bizZyjdList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row,
					BaseUtil.convertNullToEmpty(map.get("applyTechLevel") != null
							? TechLevel.findByEnumName(map.get("applyTechLevel").toString()).getDesc() : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("professionName") != null ? map.get("professionName") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("judges1") != null ? map.get("judges1") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("judges2") != null ? map.get("judges2") : ""),
					OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("result") != null ? map.get("result") : ""),
					OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 根据报名id获取订单信息
	 */
	public Object getOrderByBmId(String id) {
		ZyjdBm zyjdBm = mapper.selectById(id);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		List<CommOrderDetail> commOrderDetails = commOrderDetailMapper.selectList(new EntityWrapper<CommOrderDetail>().eq("yw_id", id));
		if (commOrderDetails.isEmpty()) {
			return new ArrayList<>();
		}
		List<String> orderIds = commOrderDetails.stream().map(CommOrderDetail::getOrderId).collect(Collectors.toList());
		return commOrderMapper.selectList(new EntityWrapper<CommOrder>()
				.in("id", orderIds)
				.eq("status", OrderStatus.YZF));
	}

	/**
	 * 上传发票
	 */
	public void uploadInvoice(String id, String url) {
		ZyjdBm zyjdBm = mapper.selectById(id);
		if (zyjdBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		zyjdBm.setInvoiceUrl(url);
		mapper.updateById(zyjdBm);
	}

	public void batchUploadInvoice(String bmbatchId, MultipartFile file, String hostOrgId) throws IOException {
		List<ZyjdBm> zyjdBms = mapper.selectList(new EntityWrapper<ZyjdBm>().eq("bmbatch_id", bmbatchId));
		if (zyjdBms.isEmpty()) {
			throw BizException.withMessage("当前批次暂无报名数据");
		}
		if (file.isEmpty()) {
			throw BizException.withMessage("请上传发票");
		}
		String tempDir = attConfig.getTempdir() + "/tddzcl";
		File tempFile = new File(tempDir, "invoice.zip");
		if (!tempFile.exists()) tempFile.getParentFile().mkdir();
		InputStream uploadIs = null;
		OutputStream tempOs = null;
		try {
			uploadIs = file.getInputStream();
			tempOs = Files.newOutputStream(tempFile.toPath());
			IOUtils.copy(uploadIs, tempOs);
		} finally {
			if (uploadIs != null) {
				IOUtils.closeQuietly(uploadIs);
			}
			if (tempOs != null) {
				IOUtils.closeQuietly(tempOs);
			}
		}
		ZipUtil.uncompress(tempFile.getAbsolutePath(), tempDir + "uncompressZip", null);
		File uncompressFile = new File(tempDir + "/uncompressZip");
		File[] files = uncompressFile.listFiles();
		if (files == null || file.isEmpty()) {
			throw BizException.withMessage("文件格式错误，压缩包里无发票图片");
		}
		if (Arrays.stream(files).anyMatch(File::isDirectory)) {
			throw BizException.withMessage("文件格式错误，压缩包里应只包含发票图片");
		}
		Map<String, File> fileMap = Arrays.stream(files).collect(Collectors.toMap(x -> x.getName().substring(0, x.getName().lastIndexOf(".")), x -> x));
		List<StudentInfo> infos = studentInfoMapper.selectList(new EntityWrapper<StudentInfo>().eq("reg_host_org_id", hostOrgId).in("sfzh", fileMap.keySet()));
		Map<String, StudentInfo> sfzhMap = infos.stream().collect(Collectors.toMap(StudentInfo::getSfzh, s -> s));

		List<ZyjdBm> updateBms = new ArrayList<>();
		for (Map.Entry<String, File> entry : fileMap.entrySet()) {
			StudentInfo studentInfo = sfzhMap.get(entry.getKey());
			List<ZyjdBm> bms = zyjdBms.stream().filter(b -> b.getStudentId().equals(studentInfo.getStudentId())).collect(Collectors.toList());
			if (bms.isEmpty()) {
				continue;
			}
			ZyjdBm bm = bms.get(0);
			String ext = FileHelper.getExtension(entry.getValue().getName());
			String path = attConfig.getRootDir()+"/upload/tddzcl/resource/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
			String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
			FileInputStream is = null;
			try {
				is = new FileInputStream(entry.getValue());
				if (StringUtils.isEmpty(bm.getInvoiceUrl())) {
					bm.setInvoiceUrl(FileHelper.storeFile(path, is, newFileName));
					updateBms.add(bm);
				}
			} finally {
				if (is != null) {
					IOUtils.closeQuietly(is);
				}

			}
		}
		DBUtils.updateBatchById(updateBms, ZyjdBm.class);
	}

	/**
	 * 培训计划-报名管理-批量注册+报名导入
	 */
	@Transactional
	public Map<String, Object> batchImportAndRegist(MultipartFile file, User user, ZyjdBmBatch zyjdBmBatch,
			String hostOrgId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		/**
		 * 姓名 证件类型 证件号 性别 出生日期 文化程度 所学专业 学历证书编号 考生来源 证书领取 职业名称 申报工种 申报等级 手机号码 参加工作时间
		 * 申报职业工龄 民族 政治面貌 简要经历 邮政编码 所在单位 通讯地址 户籍所在地 省份 城市 原证书（职称证书）职业 原证书（职称证书）等级
		 * 原证书（职称证书）编号 工龄 现从事职业 技术职称
		 */
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "XM", "ZJLX", "ZJH", "XB", "CSRQ", "WHCD", "SXZY", "XLZSBH", "KSLY", "ZSLQ", "ZYMC",
						"SBGZ", "SBDJ", "SJHM", "CJGZSJ", "SBZYGL", "MZ", "ZZMM", "JYJL", "YZBM", "SZDW", "TXDZ",
						"HJSZD", "SF", "CS", "YZSZY", "YZSDJ", "YZSBH", "GL", "XCSZY", "JSZC" });
		logger.info("--待导入数据" + (list.size() - 1) + "条");
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		// 获取当前批次报名范围：行业、职业、等级
		ZyjdBmScopeParams params = new ZyjdBmScopeParams();
		params.setHostOrgId(hostOrgId);
		params.setNotType(ZyjdBmBatchType.ZCPS);
		params.setBmbatchId(zyjdBmBatch.getId());
		params.setSize(Integer.MAX_VALUE);
		List<Map<String, Object>> zyjdBmScopeList = zyjdBmScopeMapper.list(params.getCondition(), params);
		if (CollectionUtils.isEmpty(zyjdBmScopeList)) {
			throw BizException.withMessage("当前报名批次未配置报名范围");
		}

		Map<String, String> companyProvinceMap = new HashMap<String, String>();
		Map<String, String> companyCityMap = new HashMap<String, String>();
		
		int successRegisterCount = 0;// 注册成功的条数
		int cfRegisterCount = 0;// Excel中重复的数据条数
		int errorRegisterCount = 0;// Excel中错误的数据条数
		int alreadyRegisterCount = 0;// 系统中已经存在的条数

		int successBmCount = 0;// 报名成功的条数
		int existsBmCount = 0;// 系统中已经报名的条数

		int row = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			String name = StringUtils.trimToNull(map.get("XM"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String certiType = StringUtils.trimToNull(map.get("ZJLX"));
			if (StringUtils.isEmpty(certiType)) {
				log.append("<br>");
				String msg = "第" + row + "行证件类型为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (CertiType.findByName(certiType) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行证件类型【" + certiType + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String sfzh = StringUtils.trimToNull(map.get("ZJH"));
			if (StringUtils.isEmpty(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行证件号为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (CertiType.SFZ.getName().equals(certiType) && !BaseUtil.isIDCardNumber(sfzh)) {
				String msg = "<span style=\"color:red\">第" + row + "行身份证号【" + sfzh + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String gender = StringUtils.trimToNull(map.get("XB"));
			if (StringUtils.isEmpty(gender)) {
				log.append("<br>");
				String msg = "第" + row + "行性别为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (Gender.findByName(gender) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行性别【" + gender + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String birthday = StringUtils.trimToNull(map.get("CSRQ"));
			if (StringUtils.isEmpty(birthday)) {
				log.append("<br>");
				String msg = "第" + row + "行出生日期为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String education = StringUtils.trimToNull(map.get("WHCD"));
			if (StringUtils.isEmpty(education)) {
				log.append("<br>");
				String msg = "第" + row + "行文化程度为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (Education.findByName(education) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行文化程度【" + education + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String specialty = StringUtils.trimToNull(map.get("SXZY"));
			if (StringUtils.isEmpty(specialty)) {
				log.append("<br>");
				String msg = "第" + row + "行所学专业为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String eduCertiNumber = StringUtils.trimToNull(map.get("XLZSBH"));
			if (StringUtils.isEmpty(eduCertiNumber)) {
				log.append("<br>");
				String msg = "第" + row + "行学历证书编号为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String ksly = StringUtils.trimToNull(map.get("KSLY"));
			if (StringUtils.isEmpty(ksly)) {
				log.append("<br>");
				String msg = "第" + row + "行考生来源为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (Ksly.findByName(ksly) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行考生来源【" + ksly + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String certiPost = StringUtils.trimToNull(map.get("ZSLQ"));
			if (StringUtils.isEmpty(certiPost)) {
				log.append("<br>");
				String msg = "第" + row + "行证书领取为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				certiPost = "自取".equals(certiPost) ? "1" : "快递到付".equals(certiPost) ? "2" : "";
				if (StringUtils.isEmpty(certiPost)) {
					String msg = "<span style=\"color:red\">第" + row + "行证书领取【" + certiPost + "】不合法，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					errorRegisterCount++;
					continue;
				}
			}

			String zymc = StringUtils.trimToNull(map.get("ZYMC"));
			String industryId;
			if (StringUtils.isEmpty(zymc)) {
				log.append("<br>");
				String msg = "第" + row + "行职业名称为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				List<Map<String, Object>> collect = zyjdBmScopeList.stream()
						.filter(x -> (x.get("industryCode") + "-" + x.get("industryName")).equals(zymc)
								|| x.get("industryName").equals(zymc))
						.collect(Collectors.toList());
				if (CollectionUtils.isEmpty(collect)) {
					String msg = "<span style=\"color:red\">第" + row + "行职业名称【" + zymc + "】不存在，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					errorRegisterCount++;
					continue;
				} else {
					industryId = (String) collect.get(0).get("industryId");
				}
			}

			String sbgz = StringUtils.trimToNull(map.get("SBGZ"));
			String professionId;
			if (StringUtils.isEmpty(sbgz)) {
				log.append("<br>");
				String msg = "第" + row + "行申报工种为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				// 职业下的工种是否匹配
				List<Map<String, Object>> collect = zyjdBmScopeList.stream()
						.filter(x -> x.get("industryId").equals(industryId)
								&& ((x.get("professionCode") + "-" + x.get("professionName")).equals(sbgz)
										|| x.get("professionName").equals(sbgz)))
						.collect(Collectors.toList());
				if (CollectionUtils.isEmpty(collect)) {
					String msg = "<span style=\"color:red\">第" + row + "行申报工种【" + sbgz + "】不存在，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					errorRegisterCount++;
					continue;
				} else {
					professionId = (String) collect.get(0).get("professionId");
				}
			}

			String sbdj = StringUtils.trimToNull(map.get("SBDJ"));
			TechLevel applyTechLevel;
			if (StringUtils.isEmpty(sbdj)) {
				log.append("<br>");
				String msg = "第" + row + "行申报等级为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				if (TechLevel.findByNameAndDesc(sbdj) == null) {
					String msg = "<span style=\"color:red\">第" + row + "行申报等级【" + sbdj + "】不合法，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					errorRegisterCount++;
					continue;
				} else {
					applyTechLevel = TechLevel.findByNameAndDesc(sbdj);
					// 职业、工种下等级是否匹配
					List<Map<String, Object>> collect = zyjdBmScopeList.stream()
							.filter(x -> x.get("industryId").equals(industryId)
									&& x.get("professionId").equals(professionId)
									&& x.get("techLevel").toString().contains(applyTechLevel.name()))
							.collect(Collectors.toList());
					if (CollectionUtils.isEmpty(collect)) {
						String msg = "<span style=\"color:red\">第" + row + "行申报等级【" + sbdj + "】不存在，忽略这条数据。</span>";
						log.append("<br>");
						log.append(msg);
						errorRegisterCount++;
						continue;
					}
				}
			}

			String mobile = StringUtils.trimToNull(map.get("SJHM"));
			if (StringUtils.isEmpty(mobile)) {
				log.append("<br>");
				String msg = "第" + row + "行手机号码为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (!BaseUtil.isMobile(mobile)) {
				String msg = "<span style=\"color:red\">第" + row + "行手机号码【" + mobile + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String cjgzsj = StringUtils.trimToNull(map.get("CJGZSJ"));
			Date workTime;
			if (StringUtils.isEmpty(cjgzsj)) {
				log.append("<br>");
				String msg = "第" + row + "行参加工作时间为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				try {
					workTime = DateUtils.parse(cjgzsj, "yyyy年MM月");
				} catch (Exception e) {
					workTime = DateUtils.parse(cjgzsj, "yyyy-MM-dd");
				}
				if (workTime == null) {
					String msg = "<span style=\"color:red\">第" + row + "行参加工作时间【" + cjgzsj + "】不合法，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					errorRegisterCount++;
					continue;
				}
			}

			String workYears = StringUtils.trimToNull(map.get("SBZYGL"));
			if (StringUtils.isEmpty(workYears)) {
				log.append("<br>");
				String msg = "第" + row + "行申报职业工龄为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String nation = StringUtils.trimToNull(map.get("MZ"));
			if (StringUtils.isEmpty(nation)) {
				log.append("<br>");
				String msg = "第" + row + "行民族为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else if (Nation.findByName(nation) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行民族【" + nation + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String politicalType = StringUtils.trimToNull(map.get("ZZMM"));
			if (StringUtils.isNotEmpty(politicalType) && PoliticalType.findByName(politicalType) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行政治面貌【" + politicalType + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String resume = StringUtils.trimToNull(map.get("JYJL"));
			if (StringUtils.isEmpty(resume)) {
				log.append("<br>");
				String msg = "第" + row + "行简要经历为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String postCode = StringUtils.trimToNull(map.get("YZBM"));

			String company = StringUtils.trimToNull(map.get("SZDW"));
			if (StringUtils.isEmpty(company)) {
				log.append("<br>");
				String msg = "第" + row + "行所在单位为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String address = StringUtils.trimToNull(map.get("TXDZ"));

			String certiAddress = StringUtils.trimToNull(map.get("HJSZD"));

			String sf = StringUtils.trimToNull(map.get("SF"));
			String companyProvinceCode;
			if (StringUtils.isEmpty(sf)) {
				log.append("<br>");
				String msg = "第" + row + "行省份为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				if (!companyProvinceMap.containsKey(sf)) {
					EntityWrapper<Dict> dictWrapper = new EntityWrapper<>();
					dictWrapper.eq("dict_code", "province");
					dictWrapper.eq("dict_name", sf);
					List<Dict> dictList = dictMapper.selectList(dictWrapper);
					if (CollectionUtils.isEmpty(dictList)) {
						String msg = "<span style=\"color:red\">第" + row + "行省份【" + sf + "】不合法，忽略这条数据。</span>";
						log.append("<br>");
						log.append(msg);
						errorRegisterCount++;
						continue;
					} else {
						companyProvinceMap.put(sf, companyProvinceCode = dictList.get(0).getDictValue());
					}
				} else {
					companyProvinceCode = companyProvinceMap.get(sf);
				}
			}

			String cs = StringUtils.trimToNull(map.get("CS"));
			String companyCityCode;
			if (StringUtils.isEmpty(cs)) {
				log.append("<br>");
				String msg = "第" + row + "行城市为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			} else {
				if (!companyCityMap.containsKey(cs)) {
					EntityWrapper<Dict> dictWrapper = new EntityWrapper<>();
					dictWrapper.eq("dict_code", "city");
					dictWrapper.eq("dict_name", cs);
					List<Dict> dictList = dictMapper.selectList(dictWrapper);
					if (CollectionUtils.isEmpty(dictList)) {
						String msg = "<span style=\"color:red\">第" + row + "行城市【" + cs + "】不合法，忽略这条数据。</span>";
						log.append("<br>");
						log.append(msg);
						errorRegisterCount++;
						continue;
					} else {
						companyCityMap.put(cs, companyCityCode = dictList.get(0).getDictValue());
					}
				} else {
					companyCityCode = companyCityMap.get(cs);
				}
			}

			String oldCertiProfession = StringUtils.trimToNull(map.get("YZSZY"));

			String oldTechLevel = StringUtils.trimToNull(map.get("YZSDJ"));
			if (StringUtils.isNotEmpty(oldTechLevel) && TechLevel.findByDesc(oldTechLevel) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行原证书（职称证书）等级【" + oldTechLevel
						+ "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String oldTechCertiNum = StringUtils.trimToNull(map.get("YZSBH"));

			String jobYears = StringUtils.trimToNull(map.get("GL"));
			if (StringUtils.isNotEmpty(jobYears) && BaseUtil.parseInteger(jobYears) == null) {
				String msg = "<span style=\"color:red\">第" + row + "行工龄【" + jobYears + "】不合法，忽略这条数据。</span>";
				log.append("<br>");
				log.append(msg);
				errorRegisterCount++;
			}

			String currentJob = StringUtils.trimToNull(map.get("XCSZY"));
			if (StringUtils.isEmpty(currentJob)) {
				log.append("<br>");
				String msg = "第" + row + "行现从事职业为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String zc = StringUtils.trimToNull(map.get("JSZC"));
			if (StringUtils.isEmpty(zc)) {
				log.append("<br>");
				String msg = "第" + row + "行技术职称为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			StudentUser studentUser = studentUserService.findBySfzhOrMobile(sfzh, mobile, hostOrgId);
			if (studentUser == null) {// 新增学员
				studentUser = new StudentUser();
				studentUser.setId(BaseUtil.generateId2());
				String password = null;
				if (StringUtils.isNotEmpty(sfzh)) {
					password = sfzh.length() > 6 ? sfzh.substring(sfzh.length() - 6, sfzh.length()) : sfzh;
				} else if (StringUtils.isNotEmpty(mobile)) {
					password = mobile.length() > 6 ? mobile.substring(mobile.length() - 6, mobile.length()) : mobile;
				}
				studentUser.setPassword(DigestUtils.md5Hex(password));
				studentUser.setStatus(AccountStatus.OK);
				studentUser.setStudentType(StudentType.SOCIAL);
				studentUser.setCompany(company);
				if (StringUtils.isNotEmpty(mobile)) {
					studentUser.setIsBindMobile(Constants.YES);
				} else {
					studentUser.setIsBindMobile(Constants.NO);
				}
				studentUser.setRegHostOrgId(hostOrgId);
				studentUserMapper.insert(studentUser);
				// 写入学员信息表
				StudentInfo studentInfo = new StudentInfo();
				studentInfo.setId(BaseUtil.generateId2());
				studentInfo.setStudentId(studentUser.getId());
				studentInfo.setCertiType(CertiType.findByName(certiType));
				studentInfo.setSfzh(sfzh);
				studentInfo.setName(name);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender)
						: Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setMobile(mobile);
				if (studentUser.getStudentType() == StudentType.SOCIAL) {
					studentInfo.setKsly(Ksly.QYZG);
				} else if (studentUser.getStudentType() == StudentType.SCHOOL) {
					studentInfo.setKsly(Ksly.YXXS);
				} else {
					studentInfo.setKsly(Ksly.QITA);
				}
				studentInfo.setSpecialty(specialty);
				studentInfo.setCreateTime(new Date());
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfo.setRegHostOrgId(hostOrgId);
				studentInfo.setAddress(address);
				studentInfo.setCertiAddress(certiAddress);
				studentInfo.setCompanyProvinceCode(companyProvinceCode);
				studentInfo.setCompanyCityCode(companyCityCode);
				studentInfo.setNation(Nation.findByName(nation));
				studentInfo.setPoliticalType(PoliticalType.findByName(politicalType));
				studentInfo.setBirthday(birthday);
				studentInfo.setEduCertiNumber(eduCertiNumber);
				studentInfo.setResume(resume);
				studentInfo.setPostCode(postCode);
				studentInfoMapper.insert(studentInfo);
				successRegisterCount++;
			} else {
				// 更新已经存在的学员
				studentUser.setStudentType(StudentType.SOCIAL);
				studentUser.setCompany(company);
				studentUserMapper.updateById(studentUser);
				StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentUser.getId());
				if (!sfzh.equals(studentInfo.getSfzh())) {
					if (studentUserService.findBySfzh(sfzh, hostOrgId) != null) {
						log.append("<br>");
						String msg = "第" + row + "行身份证号" + sfzh + "在当前机构已存在，忽略这条数据。";
						log.append(msg);
						existsBmCount++;
						continue;
					}
				}
				if (!mobile.equals(studentInfo.getMobile())) {
					if (studentUserService.findByMobile(mobile, hostOrgId) != null) {
						log.append("<br>");
						String msg = "第" + row + "行手机号" + mobile + "在当前机构已存在，忽略这条数据。";
						log.append(msg);
						existsBmCount++;
						continue;
					}
				}
				studentInfo.setName(name);
				studentInfo.setMobile(mobile);
				studentInfo.setCertiType(CertiType.findByName(certiType));
				studentInfo.setSfzh(sfzh);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender)
						: Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setSpecialty(specialty);
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfo.setAddress(address);
				studentInfo.setCertiAddress(certiAddress);
				studentInfo.setCompanyProvinceCode(companyProvinceCode);
				studentInfo.setCompanyCityCode(companyCityCode);
				studentInfo.setNation(Nation.findByName(nation));
				studentInfo.setPoliticalType(PoliticalType.findByName(politicalType));
				studentInfo.setBirthday(birthday);
				studentInfo.setEduCertiNumber(eduCertiNumber);
				studentInfo.setResume(resume);
				studentInfo.setPostCode(postCode);
				studentInfoMapper.updateById(studentInfo);
//				alreadyRegisterCount++;
			}

			// 同考生同一批次、职业、工种、等级不能重复报名
			EntityWrapper<ZyjdBm> zyjdBmWrapper = new EntityWrapper<>();
			zyjdBmWrapper.eq("bmbatch_id", zyjdBmBatch.getId());
			zyjdBmWrapper.eq("student_id", studentUser.getId());
			zyjdBmWrapper.eq("industry_id", industryId);
			zyjdBmWrapper.eq("profession_id", professionId);
			zyjdBmWrapper.eq("apply_tech_level", applyTechLevel.name());
			if (mapper.selectCount(zyjdBmWrapper) > 0) {
				log.append("<br>");
				String msg = "第" + row + "行考生" + name + "在当前报名批次下已经报名：职业【" + zymc + "】、工种【" + sbgz + "】、等级【" + sbdj
						+ "】，忽略这条数据。";
				log.append(msg);
				existsBmCount++;
				continue;
			}
			// 构造对象
			ZyjdBm zyjdBm = new ZyjdBm();
			zyjdBm.setId(BaseUtil.generateId2());
			zyjdBm.setCertiPost(certiPost);// 新加字段
			zyjdBm.setOldCertiProfession(oldCertiProfession);// 新加字段
			zyjdBm.setBmbatchId(zyjdBmBatch.getId());
			zyjdBm.setKsly(Ksly.findByName(ksly));
			zyjdBm.setWorkTime(workTime);
			zyjdBm.setWorkYears(workYears);
			zyjdBm.setJobYears(jobYears);
			zyjdBm.setIndustryId(industryId);
			zyjdBm.setProfessionId(professionId);
			zyjdBm.setApplyTechLevel(applyTechLevel);
			zyjdBm.setOldTechLevel(StringUtils.isNotEmpty(oldTechLevel) ? TechLevel.findByDesc(oldTechLevel) : null);
			zyjdBm.setOldTechCertiNum(oldTechCertiNum);
			zyjdBm.setSaveTime(DateUtils.now());
			zyjdBm.setSubmitTime(DateUtils.now());
			zyjdBm.setStatus(ZyjdBmStatus.SHTG);
			zyjdBm.setPayStatus(PayStatus.WJ);
			zyjdBm.setStudentId(studentUser.getId());
			zyjdBm.setCurrentJob(currentJob);
			mapper.insert(zyjdBm);
			successBmCount++;
		}
		StringBuffer message = new StringBuffer(
				"<div>总共" + (row - 1) + "条，成功注册学员" + successRegisterCount + "条，系统已经存在的学员" + alreadyRegisterCount
						+ "条；<br>Excel中重复" + cfRegisterCount + "条，错误注册" + errorRegisterCount + "条；"
						+ "<br>成功导入报名" + successBmCount + "条，系统已经存在的报名" + existsBmCount + "条；</div>");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}
	
}