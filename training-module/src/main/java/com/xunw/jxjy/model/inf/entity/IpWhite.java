package com.xunw.jxjy.model.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * IP白名单表
 */
@TableName("INF_IP_WHITE")
public class IpWhite implements Serializable {

	private static final long serialVersionUID = -354116107759164784L;

	//主键id
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;

	//IP地址
	@TableField("IP")
	private String ip;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//修改用户id
	@TableField("updator_id")
	private String updatorId;

	//修改时间
	@TableField("update_time")
	private Date updateTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
