package com.xunw.jxjy.model.zypx.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.toolkit.CollectionUtils;
import com.thoughtworks.xstream.XStream;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.SpringBeanUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.exam.entity.PaperRepo;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.params.QuestionEntityQueryParams;
import com.xunw.jxjy.model.tk.service.QuestionDBService;
import com.xunw.jxjy.model.tk.service.QuestionEntityService;
import com.xunw.jxjy.model.utils.OfficeToolWord;
import com.xunw.jxjy.model.zypx.mapper.PaperRepoMapper;
import com.xunw.jxjy.model.zypx.params.PaperRepoQueryParams;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionTt;
import com.xunw.jxjy.paper.utils.ModelHelper;

/**
 * 管理端试卷服务
 * 
 * <AUTHOR>
 */
@Service
public class PaperRepoService extends BaseCRUDService<PaperRepoMapper, PaperRepo> {

	private static final Logger logger = LoggerFactory.getLogger(PaperRepoService.class);

	@Autowired
	private QuestionDBService questionDBService;
	@Autowired
	private QuestionEntityService questionEntityService;

	/**
	 * 题库组卷
	 */
	@Transactional
	public boolean makePaper(String dbId, PaperCategory category, Integer paperCount, Integer questionCount,
			Integer score, String creatorId, String hostOrgId) {
		// 获取题库信息
		QuestionDBEntity questionDBEntity = questionDBService.selectById(dbId);
		String dbName = questionDBEntity.getName();

		// 获取当前题库已组试卷量，防止再次组卷，卷名后缀累加
		EntityWrapper<PaperRepo> paperRepoWrapper = new EntityWrapper<PaperRepo>();
		paperRepoWrapper.eq("db_id", dbId);
		Integer index = mapper.selectCount(paperRepoWrapper);

		// 先获取该题库下所有开启的试题
		EntityWrapper<QuestionEntity> questionEntityWrapper = new EntityWrapper<QuestionEntity>();
		questionEntityWrapper.eq("db_id", dbId);
		// 暂时不考虑套题
		questionEntityWrapper.isNull("parent_id");
		questionEntityWrapper.ne("type", Stlb.TT.name());
		questionEntityWrapper.eq("status", Zt.OK.name());
		// 待组卷试题
		List<QuestionEntity> questionEntitys = questionEntityService.selectList(questionEntityWrapper);
		if (CollectionUtils.isEmpty(questionEntitys)) {
			throw BizException.withMessage("当前题库未找到可用的试题");
		} else if (questionEntitys.size() < paperCount * questionCount) {
			throw BizException.withMessage("当前题库可用试题不足：" + questionEntitys.size());
		}

		// 轮询组卷
		Date atDate = new Date();
		// 总分
		Integer totalScore = questionCount * score;
		// 及格分
		Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
		List<PaperRepo> paperRepos = new ArrayList<PaperRepo>();
		for (int i = 1; i <= paperCount.intValue(); i++) {
			// 获取组卷试题
			List<QuestionEntity> randomGetQuestions = randomQuestions(questionEntitys, questionCount);
			// 组卷试卷data
			PaperRepo paperRepo = new PaperRepo();
			paperRepo.setId(BaseUtil.generateId());
			paperRepo.setName(dbName + (index + i));
			paperRepo.setTotalScore(totalScore);
			paperRepo.setPassedScore(passedScore);
			paperRepo.setCategory(category);
			paperRepo.setDbId(dbId);
			paperRepo.setHostOrgId(hostOrgId);
			paperRepo.setCreatorId(creatorId);
			paperRepo.setCreateTime(atDate);
			paperRepo.setData(buildPaper(paperRepo, randomGetQuestions, score));
			paperRepos.add(paperRepo);
		}
		return DBUtils.insertBatch(paperRepos, PaperRepo.class);
	}

	/**
	 * 试卷查询
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Page pageQuery(PaperRepoQueryParams params) {
		EntityWrapper<PaperRepo> paperRepoWrapper = new EntityWrapper<>();
		if (BaseUtil.isNotEmpty(params.getDbId())) {
			paperRepoWrapper.like("db_id", params.getDbId());
		}
		if (BaseUtil.isNotEmpty(params.getName())) {
			paperRepoWrapper.like("name", params.getName());
		}
		if (params.getCategory() != null) {
			paperRepoWrapper.eq("category", params.getCategory().name());
		}
		if (BaseUtil.isNotEmpty(params.getHostOrgId())) {
			paperRepoWrapper.eq("host_org_id", params.getHostOrgId());
		}
		paperRepoWrapper.orderDesc(Arrays.asList("create_time", "name"));
		List<PaperRepo> list = mapper.selectPage(params, paperRepoWrapper);
		list.stream().filter(x -> BaseUtil.isNotEmpty(x.getDbId())).forEach(y -> {
			EntityWrapper<QuestionDBEntity> questionDBEntityWrapper = new EntityWrapper<>();
			questionDBEntityWrapper.in("id", y.getDbId().split(","));
			y.setDbName(questionDBService.selectList(questionDBEntityWrapper).stream().map(z -> z.getName())
					.collect(Collectors.joining("\n")));
		});
		return params.setRecords(list);
	}

	/**
	 * 试卷编辑
	 */
	public boolean editPaper(PaperRepo paperRepo) {
		paperRepo.setData(reBuilPaperData(paperRepo));
		mapper.updateById(paperRepo);
		return true;
	}

	/**
	 * 删除、批量删除
	 */
	@Transactional
	public boolean deletePaper(String ids) {
		for (String id : ids.split(",")) {
			mapper.deleteById(id);
		}
		return true;
	}

	/**
	 * 获取试卷信息
	 */
	public Paper getPaperDetailById(String id) throws SQLException, IOException {
		PaperRepo paperRepo = mapper.selectById(id);
		if (paperRepo == null) {
			throw BizException.withMessage("试卷不存在");
		}
		Paper paper = ModelHelper.convertObject(paperRepo.getData());
		return buildNormalPaper(paper);
	}

	/**
	 * 试卷配置
	 */
	public boolean configPaper(JSONObject json) {
		String id = BaseUtil.getStringValueFromJson(json, "id");
		PaperRepo paperRepo = mapper.selectById(id);
		if (paperRepo == null) {
			throw BizException.withMessage("试卷不存在");
		} else if (!paperRepo.getCreatorId().equals(BaseUtil.getStringValueFromJson(json, "updatorId"))) {
			throw BizException.withMessage("无权操作该试卷");
		}
		Integer totalScore = BaseUtil.getIntegerValueFromJson(json, "totalScore");
		Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
		paperRepo.setTotalScore(totalScore);
		paperRepo.setPassedScore(passedScore);
		paperRepo.setData(buildPaper(paperRepo, json));
		mapper.updateById(paperRepo);
		return true;
	}

	/**
	 * 智能组卷
	 */
	public boolean configByChooseQues(JSONObject json) {
		Integer totalScore = BaseUtil.getIntegerValueFromJson(json, "totalScore");
		PaperRepo paperRepo = new PaperRepo();
		Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
		paperRepo.setId(BaseUtil.generateId());
		paperRepo.setName(BaseUtil.getStringValueFromJson(json, "paperName"));
		paperRepo.setTotalScore(totalScore);
		paperRepo.setPassedScore(passedScore);
		paperRepo.setCategory(PaperCategory.valueOf(BaseUtil.getStringValueFromJson(json, "category")));
		paperRepo.setHostOrgId(BaseUtil.getStringValueFromJson(json, "hostOrgId"));
		paperRepo.setCreatorId(BaseUtil.getStringValueFromJson(json, "creatorId"));
		paperRepo.setCreateTime(new Date());
		paperRepo.setData(autoBuildPaper(paperRepo, json));
		mapper.insert(paperRepo);
		return true;
	}

	/**
	 * 重新构建数据库中存在的试卷对象
	 */
	private String reBuilPaperData(PaperRepo paperRepo) {
		String result = "";
		String xml = paperRepo.getData();
		if (BaseUtil.isEmpty(xml)) {
			return "";
		}
		try {
			Paper paper = ModelHelper.convertObject(xml);
			// 处理不等于空情况下的对象模型,空的代表没有生成过，等人工配置时自动创建
			if (paper != null && paper != null) {
				// 基本信息
				BeanUtils.copyProperties(paperRepo, paper);
				result = ModelHelper.formatObject(paper);
			}
		} catch (Exception e) {
			logger.error("Error", e);
		}
		return result;
	}

	/**
	 * 从数据库试卷对象来构建完整试卷对象
	 */
	public Paper buildNormalPaper(Paper paper) {
		List<PaperSection> sections = paper.getSections();
		if (sections != null) {
			Integer rscore = 0;
			for (PaperSection section : sections) {
				List<Question> questions = section.getQuestions();
				if (questions != null) {
					// 新的试题列表
					List<Question> newQuestions = new ArrayList<Question>();
					for (Question question : questions) {
						rscore = question.getScore();
						Stlb type = question.getType();
						if (type.equals(Stlb.TT)) {
							QuestionTt tt = (QuestionTt) question;
							List<Question> childrens = tt.getChildren();
							// 对套题中的小题进行排序
							questionEntityService.sortTTChilren(childrens);
							int score = tt.getScore();
							String id = tt.getId();
							tt = (QuestionTt) questionEntityService.getQuestion(id);
							if (tt != null) {
								try {
									tt = (QuestionTt) tt.clone();
								} catch (Exception e) {
									e.printStackTrace();
								}
								tt.setScore(score);

								if (childrens != null) {
									List<Question> newChildrens = new ArrayList<Question>();
									for (Question children : childrens) {
										int score1 = children.getScore();
										String id1 = children.getId();
										children = questionEntityService.getQuestion(id1);
										if (children != null) {
											try {
												children = (Question) children.clone();
											} catch (Exception e) {
												e.printStackTrace();
											}
											children.setScore(score1);
											newChildrens.add(children);
										} else {
											throw BizException.withMessage("组卷需要的试题不存在：id=" + id1);
										}
									}
									tt.setChildren(newChildrens);
								}
								newQuestions.add(tt);
							} else {
								throw BizException.withMessage("组卷需要的试题不存在：id=" + id);
							}
						} else {
							int score = question.getScore();
							String id = question.getId();
							question = questionEntityService.getQuestion(id);
							if (question != null) {
								try {
									question = (Question) question.clone();
								} catch (Exception e) {
									e.printStackTrace();
								}
								question.setScore(score);
								newQuestions.add(question);
							} else {
								throw BizException.withMessage("组卷需要的试题不存在：id=" + id);
							}
						}
					}
					section.setRscore(rscore);
					section.setQuestions(newQuestions);
				}
			}
		}
		return paper;
	}

	/**
	 * 根据规则抽取试题，并删除待抽题集合中对应数据
	 */
	private List<QuestionEntity> randomQuestions(List<QuestionEntity> questionEntitys, Integer questionCount) {
		List<QuestionEntity> makePaperQuestions = new ArrayList<QuestionEntity>();
		// 随机从待组卷试题中取出试题
		for (int i = 0; i < questionCount.intValue(); i++) {
			int nextInt = new Random().nextInt(questionEntitys.size());
			makePaperQuestions.add(questionEntitys.get(nextInt));
			questionEntitys.remove(nextInt);
		}
		return makePaperQuestions;
	}

	/**
	 * 题库组卷，格式化试卷
	 */
	private String buildPaper(PaperRepo paperRepo, List<QuestionEntity> randomGetQuestions, Integer score) {
		// 按照题型分组
		Map<Stlb, List<QuestionEntity>> grouQuestionMap = randomGetQuestions.stream()
				.collect(Collectors.groupingBy(QuestionEntity::getType));
		int i = 0;
		Paper paper = new Paper();
		String result = "";
		XStream xstream = new XStream();
		EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
		try {
			BeanUtils.copyProperties(paperRepo, paper);
			for (Stlb type : grouQuestionMap.keySet()) {
				i++;
				// 组建章
				PaperSection section = new PaperSection(String.valueOf(i + 1), String.valueOf(i + 1), type.getName());
				grouQuestionMap.get(type).forEach(x -> {
					String quesId = x.getId();
					if (type.equals(Stlb.TT)) {
						QuestionTt questionTt = new QuestionTt();
						questionTt.setId(quesId);
						questionTt.setType(type);
						questionTt.setScore(score);
						questionTt.setContent(x.getContent());
						List<Question> childrenList = new ArrayList<>();
						quesWrapper.eq("parent_id", quesId);
						quesWrapper.eq("status", Zt.OK.name());
						List<QuestionEntity> questionEntityList = questionEntityService.selectList(quesWrapper);
						if (questionEntityList.size() > 0) {
							for (QuestionEntity questionEntity : questionEntityList) {
								Question children = new Question();
								children.setId(questionEntity.getId());
								children.setType(questionEntity.getType());
								children.setScore(score);
								children.setContent(questionEntity.getContent());
								childrenList.add(children);
							}
						}
						questionTt.setChildren(childrenList);
						section.addQuestion(questionTt);
					} else {
						Question question = new Question();
						question.setId(quesId);
						question.setType(type);
						question.setScore(score);
						question.setContent(x.getContent());
						question.setAnswer(x.getAnswer());
						question.setResolve(x.getResolve());
						section.addQuestion(question);
					}
					section.setRscore(score);
				});
				paper.addSection(section);
			}
			result = xstream.toXML(paper);
		} catch (BizException exception) {
			throw exception;
		} catch (Exception e) {
			logger.error("Error", e);
			throw BizException.withMessage("配置试卷失败，因为：" + e);
		}
		return result;
	}

	/**
	 * 试卷配置，格式化试卷
	 */
	private String buildPaper(PaperRepo paperRepo, JSONObject json) {
		Paper paper = new Paper();
		String result = "";
		XStream xstream = new XStream();
		try {
			// 基本信息
			BeanUtils.copyProperties(paperRepo, paper);
			JSONArray questionList = json.getJSONArray("questionList");
			// 判断是否存在重复题目
			Set<String> allQuestionIdSet = new HashSet<String>();
			Set<String> allQuestionContentSet = new HashSet<String>();
			EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
			if (CollectionUtils.isNotEmpty(questionList)) {
				for (int i = 0; i < questionList.size(); i++) {
					JSONObject jsonObject = (JSONObject) questionList.get(i);
					/**
					 * 构建章节
					 */
					PaperSection section = new PaperSection(String.valueOf(i + 1), String.valueOf(i + 1),
							BaseUtil.getStringValueFromJson(jsonObject, "title"));
					// 大题下子题id
					JSONArray question_ids = jsonObject.getJSONArray("questionIds");
					if (question_ids != null && question_ids.size() > 0) {
						Integer score = 0;
						for (int j = 0; j < question_ids.size(); j++) {
							String quesId = question_ids.get(j).toString();
							QuestionEntity ques = questionEntityService.selectById(quesId);
							if (allQuestionIdSet.contains(quesId)
									|| allQuestionContentSet.contains(ques.getContent())) {
								throw BizException
										.withMessage("<p>试题重复：</p>" + ques.getContent() + "<p>被重复添加。</p>");
							}
							allQuestionIdSet.add(quesId);
							allQuestionContentSet.add(ques.getContent());
							score = BaseUtil.getIntegerValueFromJson(jsonObject, "score");
							// 套题中每一个小题对应的分数
							JSONObject ttScoreJson = jsonObject.getJSONObject("subQuesScore");
							Stlb type = ques.getType();
							if (type.equals(Stlb.TT)) {
								QuestionTt questionTt = new QuestionTt();
								questionTt.setId(quesId);
								questionTt.setType(type);
								questionTt.setScore(score);
								questionTt.setContent(ques.getContent());
								List<Question> childrenList = new ArrayList<>();
								quesWrapper.eq("parent_id", quesId);
								quesWrapper.eq("status", Zt.OK.name());
								List<QuestionEntity> questionEntityList = questionEntityService.selectList(quesWrapper);
								if (questionEntityList.size() > 0) {
									for (QuestionEntity questionEntity : questionEntityList) {
										if (allQuestionIdSet.contains(questionEntity.getId())) {
											throw BizException.withMessage(
													"<p>试题重复：</p>" + ques.getContent() + "<p>被重复添加。</p>");
										}
										allQuestionIdSet.add(questionEntity.getId());

										Question children = new Question();
										children.setId(questionEntity.getId());
										children.setType(questionEntity.getType());
										Integer childStfz = BaseUtil.getIntegerValueFromJson(ttScoreJson,
												questionEntity.getId());
										childStfz = childStfz == null ? 0 : childStfz;
										children.setScore(childStfz);
										children.setContent(questionEntity.getContent());
										childrenList.add(children);
									}
								}
								questionTt.setChildren(childrenList);
								section.addQuestion(questionTt);
							} else {
								Question question = new Question();
								question.setId(quesId);
								question.setType(type);
								question.setScore(score);
								question.setContent(ques.getContent());
								question.setAnswer(ques.getAnswer());
								question.setResolve(ques.getResolve());
								section.addQuestion(question);
							}
						}
						section.setRscore(score);
					}
					paper.addSection(section);
				}
			}
			result = xstream.toXML(paper);
		} catch (BizException exception) {
			throw exception;
		} catch (Exception e) {
			logger.error("Error", e);
			throw BizException.withMessage("配置试卷失败，因为：" + e);
		}
		return result;
	}

	/**
	 * 智能组卷，格式化试卷
	 */
	private String autoBuildPaper(PaperRepo paperRepo, JSONObject json) {
		Paper paper = new Paper();
		String result = "";
		XStream xstream = new XStream();
		try {
			// 基本信息
			BeanUtils.copyProperties(paperRepo, paper);
			JSONArray sections = json.getJSONArray("sections");
			// 判断是否存在重复题目
			Set<String> allQuestionIdSet = new HashSet<String>();
			Set<String> allQuestionContentSet = new HashSet<String>();
			EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
			if (CollectionUtils.isNotEmpty(sections)) {
				for (int i = 0; i < sections.size(); i++) {
					JSONObject jsonObject = (JSONObject) sections.get(i);
					// 试题类型
					Stlb type = Stlb.valueOf(BaseUtil.getStringValueFromJson(jsonObject, "type"));
					// 每题分数
					Integer score = BaseUtil.getIntegerValueFromJson(jsonObject, "score");
					// 题库id，多个用逗号隔开
					String dbIds = BaseUtil.getStringValueFromJson(jsonObject, "dbIds");
					// 试题难度，多个用逗号隔开
					String difficultys = BaseUtil.getStringValueFromJson(jsonObject, "difficultys");
					// 抽题数量
					Integer count = BaseUtil.getIntegerValueFromJson(jsonObject, "count");
					// 未选择题量或者题量为0的题型跳过组卷
					if (count == null || count == 0) {
						continue;
					}
					QuestionEntityQueryParams params = new QuestionEntityQueryParams();
					params.setType(type);
					params.setDbIds(dbIds);
					params.setDifficultys(difficultys);
					params.setStatus(Zt.OK);
					List<QuestionEntity> questionEntitys = questionEntityService.list(params);
					if (CollectionUtils.isEmpty(questionEntitys)) {
						throw BizException.withMessage("题型：" + type.getName() + "未找到可用的试题");
					} else if (questionEntitys.size() < count) {
						throw BizException
								.withMessage("题型：" + type.getName() + "可用试题不足：" + questionEntitys.size());
					}
					questionEntitys = randomQuestions(questionEntitys, count);
					/**
					 * 构建章节
					 */
					PaperSection section = new PaperSection(String.valueOf(i + 1), String.valueOf(i + 1),
							type.getName());
					for (QuestionEntity ques : questionEntitys) {
						String quesId = ques.getId();
						if (allQuestionIdSet.contains(quesId) || allQuestionContentSet.contains(ques.getContent())) {
							throw BizException
									.withMessage("<p>试题重复：</p>" + ques.getContent() + "<p>被重复添加。</p>");
						}
						allQuestionIdSet.add(quesId);
						allQuestionContentSet.add(ques.getContent());
						// 套题中每一个小题对应的分数
						JSONObject ttScoreJson = jsonObject.getJSONObject("subQuesScore");
						if (type.equals(Stlb.TT)) {
							QuestionTt questionTt = new QuestionTt();
							questionTt.setId(quesId);
							questionTt.setType(type);
							questionTt.setScore(score);
							questionTt.setContent(ques.getContent());
							List<Question> childrenList = new ArrayList<>();
							quesWrapper.eq("parent_id", quesId);
							quesWrapper.eq("status", Zt.OK.name());
							List<QuestionEntity> questionEntityList = questionEntityService.selectList(quesWrapper);
							if (questionEntityList.size() > 0) {
								for (QuestionEntity questionEntity : questionEntityList) {
									if (allQuestionIdSet.contains(questionEntity.getId())) {
										throw BizException.withMessage(
												"<p>试题重复：</p>" + ques.getContent() + "<p>被重复添加。</p>");
									}
									allQuestionIdSet.add(questionEntity.getId());

									Question children = new Question();
									children.setId(questionEntity.getId());
									children.setType(questionEntity.getType());
									Integer childStfz = BaseUtil.getIntegerValueFromJson(ttScoreJson,
											questionEntity.getId());
									childStfz = childStfz == null ? 0 : childStfz;
									children.setScore(childStfz);
									children.setContent(questionEntity.getContent());
									childrenList.add(children);
								}
							}
							questionTt.setChildren(childrenList);
							section.addQuestion(questionTt);
						} else {
							Question question = new Question();
							question.setId(quesId);
							question.setType(type);
							question.setScore(score);
							question.setContent(ques.getContent());
							question.setAnswer(ques.getAnswer());
							question.setResolve(ques.getResolve());
							section.addQuestion(question);
						}
					}
					section.setRscore(score);
					paper.addSection(section);
				}
			}
			result = xstream.toXML(paper);
		} catch (BizException exception) {
			throw exception;
		} catch (Exception e) {
			logger.error("Error", e);
			throw BizException.withMessage("配置试卷失败，因为：" + e);
		}
		return result;
	}
	
	/**
	 * 题库组卷
	 */
	@Transactional
	public Boolean toMakePaper(JSONObject json) {
		// 基础信息
		String hostOrgId = BaseUtil.getStringValueFromJson(json, "hostOrgId");
		String creatorId = BaseUtil.getStringValueFromJson(json, "creatorId");
		String dbIds = BaseUtil.getStringValueFromJson(json, "dbIds");
		String name = BaseUtil.getStringValueFromJson(json, "name");
		String category = BaseUtil.getStringValueFromJson(json, "category");
		Integer paperCount = BaseUtil.getIntegerValueFromJson(json, "paperCount");
		// 组卷规则
		JSONArray sections = json.getJSONArray("sections");

		// 先获取该题库下所有开启的试题
		EntityWrapper<QuestionEntity> questionEntityWrapper = new EntityWrapper<QuestionEntity>();
		questionEntityWrapper.in("db_id", dbIds.split(","));
		questionEntityWrapper.isNull("parent_id");
		questionEntityWrapper.ne("type", Stlb.TT.name());
		questionEntityWrapper.eq("status", Zt.OK.name());
		// 待组卷试题
		List<QuestionEntity> questionEntitys = questionEntityService.selectList(questionEntityWrapper);
		if (CollectionUtils.isEmpty(questionEntitys)) {
			throw BizException.withMessage("当前未找到可用的试题");
		}

		// 轮询组卷
		Date atDate = new Date();
		List<PaperRepo> paperRepos = new ArrayList<PaperRepo>();
		for (int i = 1; i <= paperCount.intValue(); i++) {
			// 组卷试卷data
			PaperRepo paperRepo = new PaperRepo();
			paperRepo.setId(BaseUtil.generateId());
			paperRepo.setName(name + i);
			paperRepo.setCategory(PaperCategory.findByEnumName(category));
			// 先记录题库id集合吧
			paperRepo.setDbId(dbIds);
			paperRepo.setHostOrgId(hostOrgId);
			paperRepo.setCreatorId(creatorId);
			paperRepo.setCreateTime(atDate);
			paperRepo.setData(this.autoBuildPaper(paperRepo, sections, questionEntitys));
			paperRepos.add(paperRepo);
		}
		return DBUtils.insertBatch(paperRepos, PaperRepo.class);
	}
	
	/**
	 * 智能组卷，格式化试卷
	 */
	private String autoBuildPaper(PaperRepo paperRepo, JSONArray sections, List<QuestionEntity> allQuestionEntity) {
		Paper paper = new Paper();
		String result = "";
		XStream xstream = new XStream();
		try {
			// 基本信息
			BeanUtils.copyProperties(paperRepo, paper);
			EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
			if (CollectionUtils.isNotEmpty(sections)) {
				// 总分
				Integer totalScore = 0;
				for (int i = 0; i < sections.size(); i++) {
					JSONObject jsonObject = (JSONObject) sections.get(i);
					// 试题类型
					Stlb type = Stlb.valueOf(BaseUtil.getStringValueFromJson(jsonObject, "type"));
					// 试题难度，多个用逗号隔开
					String difficultys = BaseUtil.getStringValueFromJson(jsonObject, "difficultys");
					// 抽题数量
					Integer count = BaseUtil.getIntegerValueFromJson(jsonObject, "count");
					// 每题分数
					Integer score = BaseUtil.getIntegerValueFromJson(jsonObject, "score");
					// 未选择题量或者题量为0的题型跳过组卷
					if (count == null || count == 0) {
						continue;
					}
					List<QuestionEntity> questionEntitys = allQuestionEntity.stream().filter(x -> x.getType() == type)
							.collect(Collectors.toList());
					if (BaseUtil.isNotEmpty(difficultys)) {
						questionEntitys = questionEntitys.stream()
								.filter(x -> difficultys.contains(x.getDifficulty().name()))
								.collect(Collectors.toList());
					}
					if (CollectionUtils.isEmpty(questionEntitys)) {
						throw BizException.withMessage("题型：" + type.getName() + "未找到可用的试题");
					} else if (questionEntitys.size() < count) {
						throw BizException.withMessage("题型：" + type.getName() + "可用试题不足：" + questionEntitys.size());
					}
					// 随机抽题
					questionEntitys = randomQuestions(questionEntitys, count);
					/**
					 * 构建章节
					 */
					PaperSection section = new PaperSection(String.valueOf(i + 1), String.valueOf(i + 1),
							type.getName());
					for (QuestionEntity ques : questionEntitys) {
						String quesId = ques.getId();
						// 套题中每一个小题对应的分数
						JSONObject ttScoreJson = jsonObject.getJSONObject("subQuesScore");
						if (type.equals(Stlb.TT)) {
							QuestionTt questionTt = new QuestionTt();
							questionTt.setId(quesId);
							questionTt.setType(type);
							questionTt.setScore(score);
							questionTt.setContent(ques.getContent());
							List<Question> childrenList = new ArrayList<>();
							quesWrapper.eq("parent_id", quesId);
							quesWrapper.eq("status", Zt.OK.name());
							List<QuestionEntity> questionEntityList = questionEntityService.selectList(quesWrapper);
							if (questionEntityList.size() > 0) {
								for (QuestionEntity questionEntity : questionEntityList) {
									Question children = new Question();
									children.setId(questionEntity.getId());
									children.setType(questionEntity.getType());
									Integer childStfz = BaseUtil.getIntegerValueFromJson(ttScoreJson,
											questionEntity.getId());
									childStfz = childStfz == null ? 0 : childStfz;
									children.setScore(childStfz);
									children.setContent(questionEntity.getContent());
									childrenList.add(children);
								}
							}
							questionTt.setChildren(childrenList);
							section.addQuestion(questionTt);
						} else {
							Question question = new Question();
							question.setId(quesId);
							question.setType(type);
							question.setScore(score);
							question.setContent(ques.getContent());
							question.setAnswer(ques.getAnswer());
							question.setResolve(ques.getResolve());
							section.addQuestion(question);
						}
						totalScore += score;
					}
					section.setRscore(score);
					paper.addSection(section);
				}
				// 及格分
				Integer passedScore = new BigDecimal(totalScore).multiply(new BigDecimal(0.6 + "")).intValue();
				paper.setTotalScore(totalScore);
				paper.setPassedScore(passedScore);
				paperRepo.setTotalScore(totalScore);
				paperRepo.setPassedScore(passedScore);
			}
			result = xstream.toXML(paper);
		} catch (BizException exception) {
			throw exception;
		} catch (Exception e) {
			logger.error("Error", e);
			throw BizException.withMessage("配置试卷失败，因为：" + e);
		}
		return result;
	}

	public void exportZip(String ids, OutputStream os) {
		ZipOutputStream zos = null;
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String storepath = attConfig.getTempdir();// 本地临时存储路径
		File dir = new File(storepath, "tmp/" + UUID.randomUUID());
		if (!dir.exists()) {
			dir.mkdirs();
		}
		EntityWrapper<PaperRepo> paperRepoWrapper = new EntityWrapper<PaperRepo>();
		paperRepoWrapper.in("id", ids.split(","));
		List<PaperRepo> paperRepoList = mapper.selectList(paperRepoWrapper);
		List<File> wordFiles = new ArrayList<File>();
		try {
			zos = new ZipOutputStream(os);
			for (PaperRepo paperRepo : paperRepoList) {
				String fileName = paperRepo.getName();
				File doc = new File(dir, fileName + ".doc");
				int ind = 2;
				while (doc.exists()) {
					fileName = fileName + "(" + ind + ")";
					doc = new File(dir, fileName + ".doc");
					ind++;
				}
				wordFiles.add(doc);
				FileOutputStream docFos = null;
				try {
					docFos = new FileOutputStream(doc);
					Paper paper = ModelHelper.convertObject(paperRepo.getData());
					paper.setQuesSortType(Stplsx.ZC);
					OfficeToolWord.makePaperDoc(docFos, buildNormalPaper(paper), false);
					docFos.flush();
				} finally {
					IOUtils.closeQuietly(docFos);
				}

				zos.putNextEntry(new ZipEntry(fileName + ".doc"));
				FileInputStream docFis = null;
				try {
					docFis = new FileInputStream(doc);
					byte[] b = new byte[1024];
					int n = 0;
					while ((n = docFis.read(b)) != -1) {
						zos.write(b, 0, n);
					}
				} finally {
					IOUtils.closeQuietly(docFis);
				}
			}
			zos.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			IOUtils.closeQuietly(zos);
			for (File word : wordFiles) {
				try {
					if (word.exists()) {
						FileUtils.forceDelete(word);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			try {
				if (dir.exists()) {
					FileUtils.forceDelete(dir);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

}