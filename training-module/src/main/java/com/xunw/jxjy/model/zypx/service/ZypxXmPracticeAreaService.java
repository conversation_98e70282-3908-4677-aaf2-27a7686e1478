package com.xunw.jxjy.model.zypx.service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.ZypxXmPracticeArea;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmPracticeAreaMapper;
import com.xunw.jxjy.model.zypx.params.ZypxXmCommonQueryParams;

@Service
public class ZypxXmPracticeAreaService extends BaseCRUDService<ZypxXmPracticeAreaMapper, ZypxXmPracticeArea> {

	public int add(ZypxXmPracticeArea zypxXmPracticeArea) {
		List<ZypxXmPracticeArea> list = this.list(new ZypxXmCommonQueryParams(zypxXmPracticeArea.getXmId()));
		int sortNo = 0;
		// 取最大序号
		if (CollectionUtils.isNotEmpty(list)) {
			Optional<ZypxXmPracticeArea> maxOptional = list.stream()
					.max(Comparator.comparing(ZypxXmPracticeArea::getSortNo));
			if (maxOptional.isPresent()) {
				sortNo = maxOptional.get().getSortNo() + 1;
			}
		}
		zypxXmPracticeArea.setSortNo(sortNo);
		return mapper.insert(zypxXmPracticeArea);
	}

	/**
	 * 删除当前项目，该序号之后的实践区域序号
	 * 
	 * @param zypxXmPracticeArea
	 * @return
	 */
	@Transactional
	public int delete(String id) {
		ZypxXmPracticeArea zypxXmPracticeArea = mapper.selectById(id);
		if (zypxXmPracticeArea == null) {
			throw BizException.withMessage("实践区域不存在");
		}
		this.updateSortNo(zypxXmPracticeArea, -1);
		return mapper.deleteById(id);
	}

	/**
	 * 更新当前项目，该序号之后的实践区域序号
	 * 
	 * @param zypxXmPracticeArea
	 * @return
	 */
	@Transactional
	public int setSortNo(String ids) {
		int num = 0;
		for (String id : ids.split(",")) {
			ZypxXmPracticeArea zypxXmPracticeArea = mapper.selectById(id);
			if (zypxXmPracticeArea != null) {
				zypxXmPracticeArea.setSortNo(num++);
				mapper.updateById(zypxXmPracticeArea);
			}
		}
		return num;
	}

	/**
	 * 公共处理项目中实践区域的序号
	 * 
	 * @param zypxXmPracticeArea
	 * @param change
	 */
	private void updateSortNo(ZypxXmPracticeArea zypxXmPracticeArea, int variate) {
		List<ZypxXmPracticeArea> list = this.list(new ZypxXmCommonQueryParams(zypxXmPracticeArea.getXmId(),
				zypxXmPracticeArea.getSortNo(), zypxXmPracticeArea.getId()));
		if (CollectionUtils.isNotEmpty(list)) {
			list.forEach(x -> {
				x.setSortNo(x.getSortNo() + variate);
				x.setUpdatorId(zypxXmPracticeArea.getUpdatorId());
				x.setUpdateTime(zypxXmPracticeArea.getUpdateTime());
				mapper.updateById(x);
			});
		}
	}

	public List<ZypxXmPracticeArea> list(ZypxXmCommonQueryParams params) {
		EntityWrapper<ZypxXmPracticeArea> wrapper = new EntityWrapper<>();
		if (BaseUtil.isNotEmpty(params.getXmId())) {
			wrapper.eq("xm_id", params.getXmId());
		}
		if (BaseUtil.isNotEmpty(params.getSortNo())) {
			wrapper.ge("sort_no", params.getSortNo());
		}
		if (BaseUtil.isNotEmpty(params.getNotId())) {
			wrapper.ne("id", params.getNotId());
		}
		wrapper.orderBy("sort_no", true);
		return mapper.selectList(wrapper);
	}

}