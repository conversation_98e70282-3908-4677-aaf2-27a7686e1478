package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.AnswerType;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021年03月30日
 * 评分指标
 */
@TableName("BIZ_TARGET")
public class Target implements Serializable {

	private static final long serialVersionUID = -5249706086244048948L;

	@TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

	//指标代码
	@TableField("code")
	private String code;

	//指标名称
	@TableField("name")
	private String name;

	//指标分类ID
	@TableField("type_Id")
	private String typeId;

	//作答类型
	@TableField("answer_type")
	private String answerType;

	//指标最大分值
	@TableField("max_score")
	private Integer maxScore;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public String getAnswerType() {
		return answerType;
	}

	public void setAnswerType(String answerType) {
		this.answerType = answerType;
	}

	public Integer getMaxScore() {
		return maxScore;
	}

	public void setMaxScore(Integer maxScore) {
		this.maxScore = maxScore;
	}
}

    