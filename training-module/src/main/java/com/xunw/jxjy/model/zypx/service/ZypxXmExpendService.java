package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.ZypxXmExpend;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmExpendMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目收支管理服务
 */
@Service
public class ZypxXmExpendService extends BaseCRUDService<ZypxXmExpendMapper, ZypxXmExpend> {

    public List<ZypxXmExpend> getByXmId(String xmId) {
        return mapper.selectList(new EntityWrapper<ZypxXmExpend>().eq("xm_id", xmId));
    }
}


