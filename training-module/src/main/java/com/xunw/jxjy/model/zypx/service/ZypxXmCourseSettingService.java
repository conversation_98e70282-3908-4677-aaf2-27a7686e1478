package com.xunw.jxjy.model.zypx.service;

import java.io.File;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.ProfessionAdCourse;
import com.xunw.jxjy.model.inf.mapper.ProfessionAdCourseMapper;
import com.xunw.jxjy.model.zypx.entity.*;
import com.xunw.jxjy.model.zypx.mapper.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.qiniu.pili.PiliException;
import com.thoughtworks.xstream.XStream;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.courselive.Chapter;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.courselive.Lesson;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.exam.entity.PaperRepo;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.CourseLive;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.inf.service.CourseLiveService;
import com.xunw.jxjy.model.inf.service.CourseService;
import com.xunw.jxjy.model.inf.service.CoursewareService;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.entity.User2Role;
import com.xunw.jxjy.model.sys.mapper.User2RoleMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zypx.dto.LevelCourseSettingItem;
import com.xunw.jxjy.model.zypx.dto.ProfessionCourseSettingItem;
import com.xunw.jxjy.model.zypx.dto.SkillCourseSettingDto;
import com.xunw.jxjy.model.zypx.dto.XmCourseSettingDto;
import com.xunw.jxjy.model.zypx.dto.XmCourseSettingItem;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.utils.ModelHelper;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service
public class ZypxXmCourseSettingService extends BaseCRUDService<ZypxXmCourseSettingMapper, ZypxXmCourseSetting> {

	@Autowired
	private AttConfig attConfig;
	@Autowired
	private CourseMapper courseMapper;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private ZypxMsArriveMapper msArriveMapper;
	@Autowired
	private SystemSettingService settingService;
	@Autowired
	private MsTeachPlanService msTeachPlanService;
	@Autowired
	private CourseLiveService courseLiveService;
	@Autowired
	private ZypxBmCourseMapper bmCourseMapper;
	@Autowired
	private CourseService courseService;
	@Autowired
	private ZypxExamPaperService examPaperService;
	@Autowired
	private User2RoleMapper user2RoleMapper;
	@Autowired
	private PaperRepoService paperRepoService;
	@Autowired
	private CoursewareService coursewareService;
	@Autowired
	private ZypxXm2SkillMapper xm2SkillMapper;
	@Autowired
	private ZyjdProfessionMapper professionMapper;
	@Autowired
	private ZypxBmService bmService;
	@Autowired
	private ZyjdBmMapper bmMapper;
    @Autowired
    private PlanMapper planMapper;
    @Autowired
	private ProfessionAdCourseMapper professionAdCourseMapper;
    @Autowired
	private ZypxExamPaperMapper zypxExamPaperMapper;

	/**
	 * 获取项目的课程设置-非技能类
	 */
	public List<XmCourseSettingItem> getByXmId(String xmId) {
		Map<String, Object> condition = new HashMap<>();
		condition.put("xmId", xmId);
		List<XmCourseSettingItem> list = mapper.query(condition);
		list.stream().filter(a -> StringUtils.isNotEmpty(a.getKjContent())
						|| StringUtils.isNotEmpty(a.getMsContent())
						|| StringUtils.isNotEmpty(a.getLiveContent())).forEach(x -> {
			String courseId = x.getCourseId();
			// 按照不同学习类型填充数据
			if (BaseUtil.getInt(x.getIsMs(), 0) == 1) {
				CourseMs courseMs = ModelHelper.convertObject(String.valueOf(x.getMsContent()));
				if (courseMs != null) {
					List<com.xunw.jxjy.model.common.coursems.Chapter> chaptersMs = courseMs.getChapters();
					Map<String, String> paperIdMap = this.getExamPaperIdToMap(xmId, courseId, chaptersMs.stream()
							.flatMap(y -> y.getLessons().stream().map(z -> z.getId())).collect(Collectors.toList()));
					chaptersMs.stream().flatMap(y -> y.getLessons().stream()).forEach(z -> {
						String key = xmId + "^_^" + courseId + "^_^" + z.getId();
						if (paperIdMap.containsKey(key)) {
							z.setHomeworkId(paperIdMap.get(key));
						}
					});
					// 信息填充
					msTeachPlanService.fillInfo(courseMs);
					x.setCourseMs(courseMs);
					x.setMsContent(null);//不返回给前端
				}
			}
			if (BaseUtil.getInt(x.getIsLive(), 0) == 1) {
				Courselive courselive = ModelHelper.convertObject(String.valueOf(x.getLiveContent()));
				if (courselive != null) {
					List<com.xunw.jxjy.model.common.courselive.Chapter> chaptersLive = courselive.getChapters();
					Map<String, String> paperIdMap = this.getExamPaperIdToMap(xmId, courseId, chaptersLive.stream()
							.flatMap(y -> y.getLessons().stream().map(z -> z.getId())).collect(Collectors.toList()));
					chaptersLive.stream().flatMap(y -> y.getLessons().stream()).forEach(z -> {
						String key = xmId + "^_^" + courseId + "^_^" + z.getId();
						if (paperIdMap.containsKey(key)) {
							z.setHomeworkId(paperIdMap.get(key));
						}
					});
					// 信息填充
					courseLiveService.fillInfo(courselive);
					x.setCourselive(courselive);
					x.setLiveContent(null);
				}
			}
			if (BaseUtil.getInt(x.getIsCourseware(), 0) == 1) {
				Courselearn courselearn = ModelHelper.convertObject(String.valueOf(x.getKjContent()));
				if (courselearn != null) {
					List<com.xunw.jxjy.model.common.Chapter> chaptersLearn = courselearn.getChapters();
					Map<String, String> paperIdMap = this.getExamPaperIdToMap(xmId, courseId, chaptersLearn.stream()
							.flatMap(y -> y.getLessons().stream().map(z -> z.getId())).collect(Collectors.toList()));
					chaptersLearn.stream().flatMap(y -> y.getLessons().stream()).forEach(z -> {
						String key = xmId + "^_^" + courseId + "^_^" + z.getId();
						if (paperIdMap.containsKey(key)) {
							z.setHomeworkId(paperIdMap.get(key));
						}
					});
					x.setCourselearn(courselearn);
					x.setKjContent(null);
				}
			}
		});
		return list;
	}

	/**
	 * 获取项目的课程设置-技能类
	 */
	public SkillCourseSettingDto getBySkillXmId(String xmId) {
		EntityWrapper<ZypxXm2Skill> wrapper = new EntityWrapper<ZypxXm2Skill>();
		wrapper.eq("xm_id", xmId);
		List<ZypxXm2Skill> xm2Skills = xm2SkillMapper.selectList(wrapper);
		List<XmCourseSettingItem> courseList = getByXmId(xmId);
		SkillCourseSettingDto skillCourseSettingDto = new SkillCourseSettingDto();
		skillCourseSettingDto.setId(xmId);
		Set<String> professionIds = new LinkedHashSet();
		xm2Skills.forEach(x -> {
			professionIds.add(x.getProfessionId());
		});
		professionIds.forEach(professionId -> {
			ProfessionCourseSettingItem professionCourseSettingItem = new ProfessionCourseSettingItem();
			professionCourseSettingItem.setId(professionId);
			ZyjdProfession profession = professionMapper.selectById(professionId);
			professionCourseSettingItem.setName(profession.getName());
			skillCourseSettingDto.getProfessions().add(professionCourseSettingItem);
			xm2Skills.stream().filter(xm2Skill -> xm2Skill.getProfessionId().equals(professionId))
					.sorted(Comparator.comparing(x2s->x2s.getTechLevel().getId()))
					.map(t -> t.getTechLevel())
					.forEach(level -> {
						LevelCourseSettingItem levelCourseSettingItem = new LevelCourseSettingItem();
						levelCourseSettingItem.setTechLevel(level);
						professionCourseSettingItem.getTechLevels().add(levelCourseSettingItem);
						// 过滤课程
						List<XmCourseSettingItem> list = courseList.stream()
								.filter(course -> professionId.equals(course.getProfessionId())
										&& level == course.getTechLevel())
								.collect(Collectors.toList());
						levelCourseSettingItem.setCourses(list);
					});
		});
		return skillCourseSettingDto;
	}

	/**
	 * 非技能类项目课程设置保存接口，支持新增+编辑
	 */
	@Transactional(rollbackFor = Exception.class)
	public void saveXmCourseSetting(XmCourseSettingDto xmCourseSettingDto, String hostOrgId, String loginUserId)
			throws ParseException, PiliException {
		String xmId = xmCourseSettingDto.getId();
		if (StringUtils.isEmpty(xmId)) {
			throw BizException.withMessage("请选择培训项目");
		}
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("培训项目不存在");
		}
		if (Constants.YES.equals(zypxXm.getIsCoursePublish())) {
			throw BizException.withMessage("课程设置已经发布，不能修改");
		}
		this.save(xmId, xmCourseSettingDto.getCourses(), hostOrgId, loginUserId);
	}

	/**
	 * 技能类项目课程设置保存接口，支持新增+编辑
	 */
	@Transactional(rollbackFor = Exception.class)
	public void saveXmCourseSetting(SkillCourseSettingDto skillCourseSettingDto, String hostOrgId, String loginUserId)
			throws ParseException, PiliException {
		String xmId = skillCourseSettingDto.getId();
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("培训项目不存在");
		}
		if (Constants.YES.equals(zypxXm.getIsCoursePublish())) {
			throw BizException.withMessage("课程设置已经发布，不能修改");
		}
		if (skillCourseSettingDto != null && skillCourseSettingDto.getProfessions().size() > 0) {
			for (ProfessionCourseSettingItem professionCourseSettingItem : skillCourseSettingDto.getProfessions()) {
				for (LevelCourseSettingItem levelCourseSettingItem : professionCourseSettingItem.getTechLevels()) {
					//保存当前职业、当前等级的课程设置
					ZyjdProfession profession = professionMapper.selectById(professionCourseSettingItem.getId());
					this.save(xmId, profession.getIndustryId(),  profession.getId(), levelCourseSettingItem.getTechLevel(),
							levelCourseSettingItem.getCourses(), hostOrgId, loginUserId);
				}
			}
		} else {
			throw BizException.withMessage("课程设置不能够为空");
		}
	}

	/**
	 * 批量导入课程设置
	 */
	@Transactional
	public Map<String, Object> importCourseSetting(MultipartFile file) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[] { "DATE", "TIME", "COURSE_NAME",
				"LESSON", "HOURS", "LEARNING_TYPE", "TEACHER_NAME", "TEACHER_PHONE", "ADDRESS" });
		if (list == null || list.size() <= 1) {
			throw BizException.withMessage("导入失败，文件中没有数据");
		}
		int row = 0;
		int cfCou = 0;// 重复
		int cwCou = 0;// 错误
		int success = 0;// 成功
		Set<String> repeatCheck = new HashSet<String>();
		Map<String, XmCourseSettingItem> allCourse = new LinkedHashMap();// 所有的课程
		for (Map<String, String> map : list) {
			row++;
			// 第一行是标题，放过
			if (row < 2) {
				continue;
			}

			String courseDate = BaseUtil.getStringValueFromMap(map, "DATE");
			String courseTime = BaseUtil.getStringValueFromMap(map, "TIME");
			String courseName = BaseUtil.getStringValueFromMap(map, "COURSE_NAME", "");
			String lesson = BaseUtil.getStringValueFromMap(map, "LESSON", "");
			String hours = BaseUtil.getStringValueFromMap(map, "HOURS");
			String learningType = BaseUtil.getStringValueFromMap(map, "LEARNING_TYPE");
			String teacherName = BaseUtil.getStringValueFromMap(map, "TEACHER_NAME");
			String teacherPhone = BaseUtil.getStringValueFromMap(map, "TEACHER_PHONE");
			String address = BaseUtil.getStringValueFromMap(map, "ADDRESS");
			if (StringUtils.isEmpty(courseDate)) {
				log.append("<br>");
				String msg = "第" + row + "行日期不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(courseTime)) {
				log.append("<br>");
				String msg = "第" + row + "行时间不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String[] timeArray = StringUtils.split(courseTime, "-");
			if (timeArray == null || timeArray.length != 2) {
				log.append("<br>");
				String msg = "第" + row + "行时间格式错误，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String startTime = courseDate + " " + timeArray[0];
			String endTime = courseDate + " " + timeArray[1];
			if (startTime.equals(endTime)) {
				log.append("<br>");
				String msg = "第" + row + "行起止时间必须不一样，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(courseName)) {
				log.append("<br>");
				String msg = "第" + row + "行课程不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(lesson)) {
				log.append("<br>");
				String msg = "第" + row + "行培训内容不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(hours)) {
				log.append("<br>");
				String msg = "第" + row + "行学时不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(learningType)) {
				log.append("<br>");
				String msg = "第" + row + "行培训方式不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (repeatCheck.contains(courseName + lesson)) {
				log.append("<br>");
				String msg = "第" + row + "行课程重复，忽略这条数据。";
				log.append(msg);
				// 标记重复数据
				cfCou++;
				continue;
			} else {
				repeatCheck.add(courseName + lesson);
			}

			String courseId = null;// 导入的课程一律认为是新加的课程，courseId默认都是null

			EntityWrapper<User> userWrapper = new EntityWrapper<>();
			userWrapper.eq("mobile", teacherPhone).or().eq("username", teacherPhone);
			List<User> users = userMapper.selectList(userWrapper);
			String teacherId = null;
			if (CollectionUtils.isNotEmpty(users)) {
				teacherId = users.get(0).getId();
			}
			if (!allCourse.containsKey(courseName)) {
				XmCourseSettingItem courseSettingItem = new XmCourseSettingItem();
				courseSettingItem.setCourseId(courseId);
				courseSettingItem.setCourseName(courseName);
				courseSettingItem.setHours(Double.valueOf(hours));
				allCourse.put(courseName, courseSettingItem);// 一对一关系
			}
			List<String> learningTypeList = Arrays.stream(learningType.split("\\+")).collect(Collectors.toList());
			// 添加课时
			if (learningTypeList.contains(Constants.TrainingType.ZB)) {
				allCourse.get(courseName).setIsLive(Constants.YES);
				if (allCourse.get(courseName).getCourselive() == null) {
					Courselive courselive = new Courselive();
					courselive.setKcid(courseId);
					courselive.setJsid(teacherId);
					Chapter chapter = new Chapter();
					chapter.setName(courseName);
					courselive.addChapter(chapter);
					allCourse.get(courseName).setCourselive(courselive);
				}
				Lesson lessonObject = new Lesson();
				lessonObject.setZbbt(StringUtils.isEmpty(lesson) ? courseName : lesson);
				lessonObject.setKssj(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setJssj(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setJsid(teacherId);
				lessonObject.setJsxm(teacherName);
				lessonObject.setJssjh(teacherPhone);
				lessonObject.setAddress(address);
				allCourse.get(courseName).getCourselive().getChapters().get(0).addLesson(lessonObject);
			}
			if (learningTypeList.contains(Constants.TrainingType.MS)) {
				allCourse.get(courseName).setIsMs(Constants.YES);
				if (allCourse.get(courseName).getCourseMs() == null) {
					CourseMs courseMs = new CourseMs();
					courseMs.setCourseId(courseId);
					com.xunw.jxjy.model.common.coursems.Chapter chapter = new com.xunw.jxjy.model.common.coursems.Chapter();
					chapter.setName(courseName);
					courseMs.addChapter(chapter);
					allCourse.get(courseName).setCourseMs(courseMs);
				}
				com.xunw.jxjy.model.common.coursems.Lesson lessonObject = new com.xunw.jxjy.model.common.coursems.Lesson();
				lessonObject.setName(StringUtils.isEmpty(lesson) ? courseName : lesson);
				lessonObject.setStartTime(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setEndTime(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setTeacherId(teacherId);
				lessonObject.setTeacherName(teacherName);
				lessonObject.setTeacherPhone(teacherPhone);
				lessonObject.setAddress(address);
				allCourse.get(courseName).getCourseMs().getChapters().get(0).addLesson(lessonObject);
			} else {
				log.append("<br>");
				String msg = "第" + row + "行培训方式错误，仅支持线上直播、线下面授,线上直播+线下面授，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			success++;
		}
		StringBuffer message = new StringBuffer(
				"总共" + (row - 1) + "条，导入成功" + success + "，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("message", message);
		result.put("list", allCourse.values());
		return result;
	}

	/**
	 * 发布课程设置
	 */
	@Transactional
	public void publish(String xmId, String publishUserId) {
		ZypxXm xm = xmMapper.selectById(xmId);
		EntityWrapper<ZypxXmCourseSetting> checkWrapper = new EntityWrapper<>();
		checkWrapper.eq("xm_id", xmId);
		List<ZypxXmCourseSetting> checkCourseList = mapper.selectList(checkWrapper);
		for (ZypxXmCourseSetting zypxXmCourseSetting : checkCourseList) {
			Course course = courseMapper.selectById(zypxXmCourseSetting.getCourseId());
			if (Constants.NO.equals(zypxXmCourseSetting.getIsLive())
					&& Constants.NO.equals(zypxXmCourseSetting.getIsCourseware())
					&& Constants.NO.equals(zypxXmCourseSetting.getIsMs())) {
				throw BizException.withMessage("培训项目：" + xm.getTitle() + "中的课程：" + course.getCode() + "-"
						+ course.getName() + "未设置学习方式,请设置完毕再发布");
			}
		}
		xm.setIsCoursePublish(Constants.YES);
		xm.setCoursePublishUserid(publishUserId);
		xm.setCoursePublishTime(DateUtils.now());
		xmMapper.updateById(xm);
		//更新已经报名学员的课程信息
		if (Constants.YES.equals(xm.getIsAllowChooseCourse())) {
			return ;//开放了个人选课的项目不处理
		}
		else {
			List<ZypxBm> list = bmService.getByXmId(xmId);
			List<ZypxBmCourse> bmCourseList = new ArrayList<>();
			bmCourseMapper.deleteByXmId(xmId);
			for (ZypxBm zypxBm : list) {
				if (StringUtils.isNotEmpty(zypxBm.getZyjdBmId())) {
					ZyjdBm zyjdBm = bmMapper.selectById(zypxBm.getZyjdBmId());
					if (zyjdBm != null) {
						checkCourseList.stream().filter(y->zyjdBm.getProfessionId().equals(y.getProfessionId()) &&
								zyjdBm.getApplyTechLevel() == y.getTechLevel()).forEach(xcs -> {
									ZypxBmCourse bmCourse = new ZypxBmCourse();
									bmCourse.setId(BaseUtil.generateId2());
									bmCourse.setBmId(zypxBm.getId());
									bmCourse.setCourseSettingId(xcs.getId());
									bmCourse.setBmTime(zypxBm.getTime());
									bmCourseList.add(bmCourse);
								});
					}
				}
				else {
					checkCourseList.forEach(x->{
						ZypxBmCourse bmCourse = new ZypxBmCourse();
						bmCourse.setId(BaseUtil.generateId2());
						bmCourse.setBmId(zypxBm.getId());
						bmCourse.setCourseSettingId(x.getId());
						bmCourse.setBmTime(zypxBm.getTime());
						bmCourseList.add(bmCourse);
					});
				}
			}
			DBUtils.insertBatch(bmCourseList, 200, ZypxBmCourse.class);
		}
	}

	/**
	 * 撤销课程设置的发布
	 */
	@Transactional
	public void revoke(String xmId) {
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		if (zypxXm.getBuyType() == BuyType.COURSE) {
			throw BizException.withMessage("按课程购买的项目，课程设置不能更改");
		}
		zypxXm.setIsCoursePublish(Constants.NO);
		zypxXm.setCoursePublishTime(null);
		zypxXm.setCoursePublishUserid(null);
		xmMapper.updateAllColumnById(zypxXm);
	}

	/**
	 * 查询项目下的所有课程
	 */
	public List<Map<String, Object>> getAllCourseByXmId(String xmId) {
		if (StringUtils.isEmpty(xmId)) {
			return Collections.EMPTY_LIST;
		}
		return mapper.getAllCourseByXmId(xmId);
	}

	/**
	 * 根据课程设置获取课程
	 */
	public Course getCourseByCourseSettingId(String courseSettingId) {
		return mapper.getCourseByCourseSettingId(courseSettingId);
	}

	/**
	 * 删除课程设置中的某一门课程
	 */
	@Transactional
	public void deleteById(String id) {
		ZypxXmCourseSetting courseSetting = this.selectById(id);
		if (StringUtils.isNotEmpty(courseSetting.getXmId())) {
			ZypxXm zypxXm = xmMapper.selectById(courseSetting.getXmId());
			if (Constants.YES.equals(zypxXm.getIsCoursePublish())) {
				throw BizException.withMessage("删除课程失败，因为课程设置已经发布,请撤销发布后再操作");
			}
		}

		EntityWrapper<ZypxBmCourse> wrapper = new EntityWrapper<>();
		wrapper.eq("course_setting_id", id);
		if (bmCourseMapper.selectCount(wrapper) > 0) {
			throw BizException.withMessage("删除课程失败，因为该课程已经有学员报名过了");
		};
		mapper.deleteById(id);
	}

	/**
	 * 查询面授学习对象, 传入参数为课程设置ID
	 */
	public Map<String, Object> getMsLearnContent(String id, String studentId) {
		Map<String, Object> learnConent = mapper.getLearnContentById(id);
		CourseMs courseMs = ModelHelper.convertObject((String) learnConent.get("msContent"));
		List<String> arrivedLessonIds = new ArrayList<String>();
		if (courseMs != null) {
			msTeachPlanService.fillInfo(courseMs);
			// 回填签到信息
			for (com.xunw.jxjy.model.common.coursems.Chapter chapter : courseMs.getChapters()) {
				for (com.xunw.jxjy.model.common.coursems.Lesson lesson : chapter.getLessons()) {
					Integer count = msArriveMapper.getMsArriveCountByStudentId(studentId, lesson.getId());
					if (count > 0) {
						arrivedLessonIds.add(lesson.getId());
					}
				}
			}
			learnConent.put("chapters", courseMs.getChapters());
			learnConent.put("arrivedLessonIds", arrivedLessonIds);// 已经签到的课时ID集合
			// 设置允许签到的误差距离
			String distance = settingService.getGlobalSetting(SysSettingEnum.ONLINE_ARRIVE_ALLOW_DISTANCE);
			learnConent.put("distance", distance);
		}
		learnConent.remove("msContent");
		return learnConent;
	}

	/**
	 * 删除直播、面授中的某一个课时
	 */
	@Transactional
	public void deleteLesson(String id, LearningType learningType, String lessonId) {
		ZypxXmCourseSetting courseSetting = this.selectById(id);
		String xmId = courseSetting.getXmId();
		if (StringUtils.isNotEmpty(xmId)) {
			ZypxXm zypxXm = xmMapper.selectById(xmId);
			if (Constants.YES.equals(zypxXm.getIsCoursePublish())) {
				throw BizException.withMessage("删除课时失败，因为课程设置已经发布,请撤销发布后再操作");
			}
		}
		String courseId = courseSetting.getCourseId();
		if (learningType == LearningType.KJ) {
			throw BizException.withMessage("目前暂不支持删除课件资源的课时");
		}
		if (learningType == LearningType.ZB) {
			CourseLive courseLive = courseLiveService.getByCourseId(courseId);
			Courselive courselive = ModelHelper.convertObject(String.valueOf(courseLive.getContent()));
			courselive.deleteLesson(lessonId);
			courseLive.setContent(ModelHelper.formatObject(courselive));
			courseLiveService.updateById(courseLive);
		}
		if (learningType == LearningType.MS) {
			CourseMs courseMs = ModelHelper.convertObject(String.valueOf(courseSetting.getMsContent()));
			courseMs.deleteLesson(lessonId);
			courseSetting.setMsContent(ModelHelper.formatObject(courseMs));
			mapper.updateById(courseSetting);
		}
		// 删除作业
		EntityWrapper<ExamPaper> examPaperWrapper = new EntityWrapper<>();
		examPaperWrapper.eq("xm_id", xmId);
		examPaperWrapper.eq("course_id", courseId);
		examPaperWrapper.eq("lesson_id", lessonId);
		examPaperService.deleteByWrapper(examPaperWrapper);
	}

	@Transactional
	public void addPaper(ExamPaper examPaper, com.alibaba.fastjson.JSONObject json) {
		String id = examPaper.getId();
		// 先將试卷基础数据入库，便于公用试卷配置接口
		if (StringUtils.isEmpty(id)) {
			examPaper.setId(id = BaseUtil.generateId());
			examPaperService.insert(examPaper);
		} else {
			examPaperService.updateById(examPaper);
		}
		json.put("id", id);
		json.put("updatorId", examPaper.getCreatorId());
		examPaperService.config(json);
	}

	@Transactional
	public void randomAddPaper(ExamPaper examPaper, com.alibaba.fastjson.JSONObject json) {
		String id = examPaper.getId();
		// 先將试卷基础数据入库，便于公用试卷配置接口
		if (StringUtils.isEmpty(id)) {
			examPaper.setId(id = BaseUtil.generateId());
			examPaperService.insert(examPaper);
		} else {
			examPaperService.updateById(examPaper);
		}
		json.put("id", examPaper.getId());
		json.put("updatorId", examPaper.getCreatorId());
		examPaperService.configByChooseQues(json);
	}

	/**
	 * 指定试卷
	 */
	public void assignPaper(ExamPaper examPaper, String repoPaperId) {
		// id存在就为修改
		String id = examPaper.getId();
		PaperRepo paperRepo = paperRepoService.selectById(repoPaperId);
		if (paperRepo == null) {
			throw BizException.withMessage("选择的试卷不存在");
		}
		XStream xstream = new XStream();
		Paper paper = ModelHelper.convertObject(paperRepo.getData());
		if (paper == null) {
			paper = new Paper();
		}
		if (StringUtils.isEmpty(id)) {
			examPaper.setId(BaseUtil.generateId());
		}
		examPaper.setTotalScore(paperRepo.getTotalScore());
		examPaper.setPassedScore(paperRepo.getPassedScore());
		BeanUtils.copyProperties(examPaper, paper);
		examPaper.setData(xstream.toXML(paper));
		if (StringUtils.isEmpty(id)) {
			examPaperService.insert(examPaper);
		} else {
			examPaperService.updateById(examPaper);
		}
	}

	/**
	 * 保存课程设置-技能类
	 */
	protected List<ZypxXmCourseSetting> save(String xmId, String industryId, String professionId, TechLevel techLevel,
											 List<XmCourseSettingItem> items, String hostOrgId, String loginUserId)
			throws ParseException, PiliException {
		List<ZypxXmCourseSetting> resultList = this.save(xmId, items, hostOrgId, loginUserId);
		resultList.stream().forEach(x -> {
			x.setIndustryId(industryId);
			x.setProfessionId(professionId);
			x.setTechLevel(techLevel);
		});
		DBUtils.updateBatchById(resultList, ZypxXmCourseSetting.class);
		return resultList;
	}

	/**
	 * 保存课程设置-非技能类
	 */
	protected List<ZypxXmCourseSetting> save(String xmId, List<XmCourseSettingItem> items, String hostOrgId,
											 String loginUserId) throws ParseException, PiliException {
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		Plan plan = planMapper.selectById(zypxXm.getPlanId());
		Set<String> courseNameSet = new HashSet<String>();
		List<ZypxXmCourseSetting> resultList = new ArrayList<ZypxXmCourseSetting>();
		for (XmCourseSettingItem item : items) {
			// 1 课程处理
			if (courseNameSet.contains(item.getCourseName())) {
				throw BizException.withMessage("课程名称重复：" + item.getCourseName());
			} else {
				courseNameSet.add(item.getCourseName());
			}
			Course course = null;
			if (StringUtils.isEmpty(item.getCourseId())) {
				course = new Course();
				course.setId(BaseUtil.generateId2());
				course.setName(item.getCourseName());
				course.setCode(courseService.genCourseCode());
				course.setCreatorId(loginUserId);
				course.setCreateTime(new Date());
				course.setHours(item.getHours());
				course.setHostOrgId(hostOrgId);
				course.setStatus(Zt.OK);
				course.setKcxz(Constants.KCXZ.LI_LUN);
				courseMapper.insert(course);
				item.setCourseId(course.getId());
			} else {
				course = courseService.selectById(item.getCourseId());
				if(!Constants.YES.equals(item.getIsCourseware())) {
					course.setName(item.getCourseName());
					course.setHours(item.getHours());
					course.setUpdatorId(loginUserId);
					course.setUpdateTime(new Date());
					courseMapper.updateById(course);
				}
			}
			// 将面授、直播课时中讲师id、课程id写入
			if (Constants.YES.equals(item.getIsLive())) {
				if (item.getCourselive() == null || CollectionUtils.isEmpty(item.getCourselive().getChapters())) {
					throw BizException.withMessage("当前直播学习方式未设置授课内容");
				}
				item.getCourselive().getChapters().forEach(x -> x.getLessons().forEach(y -> {
					if (StringUtils.isEmpty(y.getJsid())) {
						y.setJsid(this.saveTeacherUser(y.getJsxm(), y.getJssjh(), hostOrgId, loginUserId));
					}
					y.setKcid(item.getCourseId());
				}));

				// 取第一个课时的讲师作为直播课的讲师
				item.setTeacherId(item.getCourselive().getChapters().get(0).getLessons().get(0).getJsid());
				item.setTeacherName(item.getCourselive().getChapters().get(0).getLessons().get(0).getJsxm());
				item.setTeacherPhone(item.getCourselive().getChapters().get(0).getLessons().get(0).getJssjh());
				item.setAddress(item.getCourselive().getChapters().get(0).getLessons().get(0).getAddress());

				// 当前课程的开始时间取课时最早的开始时间，当期课程的结束时间取最晚的结束时间
				List<Date> startTimeList = item.getCourselive().getChapters().stream()
						.flatMap(x -> x.getLessons().stream()).map(y -> y.getKssj()).collect(Collectors.toList());
				startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
				item.setStartTime(startTimeList.get(0));
				List<Date> endTimeList = item.getCourselive().getChapters().stream()
						.flatMap(x -> x.getLessons().stream()).map(y -> y.getJssj()).collect(Collectors.toList());
				endTimeList = endTimeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
				item.setEndTime(endTimeList.get(0));
			}
			if (Constants.YES.equals(item.getIsMs())) {
				if (item.getCourseMs() == null || CollectionUtils.isEmpty(item.getCourseMs().getChapters())) {
					throw BizException.withMessage("当前面授学习方式未设置授课内容");
				}
				item.getCourseMs().getChapters().forEach(x -> x.getLessons().forEach(y -> {
					if (StringUtils.isEmpty(y.getTeacherId())) {
						y.setTeacherId(
								this.saveTeacherUser(y.getTeacherName(), y.getTeacherPhone(), hostOrgId, loginUserId));
					}
				}));

				// 当前面授课程讲师取第一个课时的讲师
				item.setTeacherId(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherId());
				item.setTeacherName(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherName());
				item.setTeacherPhone(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherPhone());
				item.setAddress(item.getCourseMs().getChapters().get(0).getLessons().get(0).getAddress());

				// 当前课程的开始时间取课时最早的开始时间，当期课程的结束时间取最晚的结束时间
				List<Date> startTimeList = item.getCourseMs().getChapters().stream()
						.flatMap(x -> x.getLessons().stream()).map(y -> y.getStartTime()).collect(Collectors.toList());
				startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
				item.setStartTime(startTimeList.get(0));
				List<Date> endTimeList = item.getCourseMs().getChapters().stream().flatMap(x -> x.getLessons().stream())
						.map(y -> y.getEndTime()).collect(Collectors.toList());
				endTimeList = endTimeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
				item.setEndTime(endTimeList.get(0));
			}

			if(!Constants.YES.equals(item.getIsShared())){
//				if ((Constants.YES.equals(item.getIsLive()) || Constants.YES.equals(item.getIsMs()))
//						&& StringUtils.isEmpty(item.getTeacherId())) {
//					throw BizException.withMessage(item.getCourseName() + "未设置讲师");
//				}
				if (Constants.YES.equals(item.getIsLive()) && StringUtils.isEmpty(item.getTeacherId())) {
					throw BizException.withMessage(item.getCourseName() + "未设置讲师");
				}
			}
			if (Constants.YES.equals(item.getIsMs()) && StringUtils.isEmpty(item.getAddress())) {
				throw BizException.withMessage(item.getCourseName() + "面授课程未设置地点");
			}
			//非技能类且是按课程购买，必须填写课程金额
			if (Objects.equals(plan.getIsSkill(), Constants.YES)
					&& zypxXm.getBuyType() == BuyType.COURSE
					&& (item.getAmount() == null || !item.getAmount().matches("\\d+(\\.\\d{1,2})?"))) {
				throw BizException.withMessage("请输入格式正确的课程金额");
			}
			ZypxXmCourseSetting xmCourseSetting = null;
			if (StringUtils.isEmpty(item.getId())) {
				xmCourseSetting = new ZypxXmCourseSetting();
				xmCourseSetting.setId(BaseUtil.generateId2());
				xmCourseSetting.setXmId(xmId);
				xmCourseSetting.setCourseId(item.getCourseId());
				xmCourseSetting.setStartTime(item.getStartTime());
				xmCourseSetting.setEndTime(item.getEndTime());
				xmCourseSetting.setAddress(item.getAddress());
				xmCourseSetting.setTeacherId(item.getTeacherId());
				xmCourseSetting.setTeacherName(item.getTeacherName());
				xmCourseSetting.setIsCourseware(item.getIsCourseware());
				xmCourseSetting.setIsMs(item.getIsMs());
				xmCourseSetting.setIsLive(item.getIsLive());
				xmCourseSetting.setCreatorId(loginUserId);
				xmCourseSetting.setCreateTime(new Date());
				xmCourseSetting.setIsShared(item.getIsShared());
				xmCourseSetting.setAmount(item.getAmount());
				xmCourseSetting.setIsOpenCourseTarget(item.getIsOpenCourseTarget());
				mapper.insert(xmCourseSetting);
				item.setId(xmCourseSetting.getId());
			} else {
				xmCourseSetting = mapper.selectById(item.getId());
				xmCourseSetting.setCourseId(item.getCourseId());
				xmCourseSetting.setStartTime(item.getStartTime());
				xmCourseSetting.setEndTime(item.getEndTime());
				xmCourseSetting.setAddress(item.getAddress());
				xmCourseSetting.setTeacherId(item.getTeacherId());
				xmCourseSetting.setTeacherName(item.getTeacherName());
				if (Constants.YES.equals(xmCourseSetting.getIsMs()) && !Constants.YES.equals(item.getIsMs())) {
					xmCourseSetting.setMsContent(null);
				}
				xmCourseSetting.setIsCourseware(item.getIsCourseware());
				xmCourseSetting.setIsMs(item.getIsMs());
				xmCourseSetting.setIsLive(item.getIsLive());
				xmCourseSetting.setUpdateTime(new Date());
				xmCourseSetting.setUpdatorId(loginUserId);
				xmCourseSetting.setIsShared(item.getIsShared());
				xmCourseSetting.setAmount(item.getAmount());
				xmCourseSetting.setIsOpenCourseTarget(item.getIsOpenCourseTarget());
				mapper.updateAllColumnById(xmCourseSetting);
			}
			resultList.add(xmCourseSetting);
			if(!Constants.YES.equals(item.getIsShared())){
				this.saveLiveTeachPLan(item, loginUserId);// 非共享的直播的教学计划存储
			}
			// 面授的教学计划存储
			this.saveMsTeachPLan(xmCourseSetting, item, loginUserId);
		}
		return resultList;
	}

	/**
	 * 根据姓名、手机号生成用户并返回主键
	 */
	@Transactional
	public String saveTeacherUser(String teacherName, String teacherPhone, String hostOrgId, String loginUserId) {
		if (StringUtils.isEmpty(teacherName)) {
			throw BizException.withMessage("讲师姓名不能为空");
		}
		if (StringUtils.isEmpty(teacherPhone)) {
			return null;
		}
		EntityWrapper<User> userWrapper = new EntityWrapper<>();
		userWrapper.eq("mobile", teacherPhone).or().eq("username", teacherPhone);
		List<User> users = userMapper.selectList(userWrapper);
		if (CollectionUtils.isNotEmpty(users)) {
			User user = users.get(0);
			// 用户存在，角色不存在
			EntityWrapper<User2Role> user2RoleWrapper = new EntityWrapper<>();
			user2RoleWrapper.eq("user_id", user.getId());
			user2RoleWrapper.eq("role", Role.TEACHER);
			List<User2Role> user2Roles = user2RoleMapper.selectList(user2RoleWrapper);
			if (CollectionUtils.isEmpty(user2Roles)) {
				User2Role user2Role = new User2Role();
				user2Role.setId(BaseUtil.generateId2());
				user2Role.setUserId(user.getId());
				user2Role.setRole(Role.TEACHER);
				user2Role.setCreateTime(new Date());
				user2Role.setCreatorId(user.getCreatorId());
				user2RoleMapper.insert(user2Role);
			}
			return user.getId();
		} else {
			User user = new User(BaseUtil.generateId2(), teacherPhone, DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD),
					teacherName, teacherPhone, hostOrgId, Zt.OK, loginUserId, new Date());
			userMapper.insert(user);
			User2Role user2Role = new User2Role();
			user2Role.setId(BaseUtil.generateId2());
			user2Role.setUserId(user.getId());
			user2Role.setRole(Role.TEACHER);
			user2Role.setCreateTime(new Date());
			user2Role.setCreatorId(user.getCreatorId());
			user2RoleMapper.insert(user2Role);
			return user.getId();
		}
	}

	/**
	 * 保存直播的教学计划
	 */
	public void saveLiveTeachPLan(XmCourseSettingItem xmCourseSettingItem, String loginUserId)
			throws ParseException, PiliException {
		if (!Constants.YES.equals(xmCourseSettingItem.getIsLive())) {
			return;
		}
		CourseLive courseLive = courseLiveService.getByCourseId(xmCourseSettingItem.getCourseId());
		List<com.xunw.jxjy.model.common.courselive.Chapter> chaptersLive = xmCourseSettingItem.getCourselive()
				.getChapters();
		if (CollectionUtils.isNotEmpty(chaptersLive)) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("id", courseLive != null ? courseLive.getId() : Constants.EMPTY_STRING);
			jsonObject.put("courseId", xmCourseSettingItem.getCourseId());
			JSONArray chapters = new JSONArray();
			// 页面传入的是几个章节 后台就保存几个章节，因为直播课可能在基础数据管理中章节设置被修改
			for (com.xunw.jxjy.model.common.courselive.Chapter chapterEntity : chaptersLive) {
				if (CollectionUtils.isNotEmpty(chapterEntity.getLessons())) {
					JSONObject chapter = new JSONObject();
					chapter.put("id", StringUtils.isNotEmpty(chapterEntity.getId()) ? chapterEntity.getId()
							: Constants.EMPTY_STRING);
					chapter.put("name", chapterEntity.getName());
					JSONArray lessons = new JSONArray();
					chapterEntity.getLessons().forEach(x -> {
						JSONObject lesson = new JSONObject();
						lesson.put("id", StringUtils.isNotEmpty(x.getId()) ? x.getId() : Constants.EMPTY_STRING);
						lesson.put("zbbt", x.getZbbt());
						lesson.put("kssj", DateUtils.format(x.getKssj()));
						lesson.put("jssj", DateUtils.format(x.getJssj()));
						lesson.put("jsid", x.getJsid());
						lesson.put("jsxm", x.getJsxm());
						lesson.put("address", x.getAddress());
						lessons.add(lesson);
					});
					chapter.put("lessons", lessons);
					chapters.add(chapter);
				}
			}
			jsonObject.put("chapters", chapters);
			courseLiveService.saveCourseLive(jsonObject, loginUserId);
		}
	}

	/**
	 * 保存面授的教学计划
	 */
	public void saveMsTeachPLan(ZypxXmCourseSetting courseSetting, XmCourseSettingItem xmCourseSettingItem,
								 String loginUserId) throws ParseException {
		if (!Constants.YES.equals(xmCourseSettingItem.getIsMs()) || xmCourseSettingItem.getCourseMs() == null) {
			return;
		}
		CourseMs courseMs = ModelHelper.convertObject(String.valueOf(courseSetting.getMsContent()));
		List<com.xunw.jxjy.model.common.coursems.Chapter> chaptersMs = xmCourseSettingItem.getCourseMs().getChapters();
		if (CollectionUtils.isNotEmpty(chaptersMs)) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("id", courseMs != null ? courseMs.getId() : Constants.EMPTY_STRING);
			JSONArray chapters = new JSONArray();
			// 页面传入几个章节 就保存几个章节
			for (com.xunw.jxjy.model.common.coursems.Chapter chapterEntity : chaptersMs) {
				if (CollectionUtils.isNotEmpty(chapterEntity.getLessons())) {
					JSONObject chapter = new JSONObject();
					chapter.put("id", StringUtils.isNotEmpty(chapterEntity.getId()) ? chapterEntity.getId()
							: Constants.EMPTY_STRING);
					chapter.put("name", chapterEntity.getName());
					JSONArray lessons = new JSONArray();
					chapterEntity.getLessons().forEach(x -> {
						JSONObject lesson = new JSONObject();
						lesson.put("id", StringUtils.isNotEmpty(x.getId()) ? x.getId() : Constants.EMPTY_STRING);
						lesson.put("name", x.getName());
						lesson.put("startTime", DateUtils.format(x.getStartTime()));
						lesson.put("endTime", DateUtils.format(x.getEndTime()));
						lesson.put("teacherId", x.getTeacherId());
						lesson.put("teacherName", x.getTeacherName());
						lesson.put("venueRoomId", x.getVenueRoomId());
						lesson.put("address", x.getAddress());
						lessons.add(lesson);
					});
					chapter.put("lessons", lessons);
					chapters.add(chapter);
				}
			}
			jsonObject.put("chapters", chapters);
			msTeachPlanService.saveMsTeachPlan(courseSetting, jsonObject, loginUserId);
		}
	}

	/**
	 * 查询课时上布置的作业
	 */
	private Map<String, String> getExamPaperIdToMap(String xmId, String courseId, List<String> lessonIds) {
		EntityWrapper<ExamPaper> entitWrapper = new EntityWrapper<>();
		entitWrapper.eq("xm_id", xmId);
		entitWrapper.eq("course_id", courseId);
		entitWrapper.in("lesson_id", lessonIds);
		entitWrapper.setSqlSelect("id, xm_id, course_id, lesson_id");
		List<ExamPaper> examPapers = examPaperService.selectList(entitWrapper);
		Map<String, String> result = new HashMap<String, String>();
		if (CollectionUtils.isNotEmpty(examPapers)) {
			Map<String, List<ExamPaper>> collect = examPapers.stream()
					.collect(Collectors.groupingBy(x -> xmId + "^_^" + courseId + "^_^" + x.getLessonId()));
			for (Entry<String, List<ExamPaper>> entry : collect.entrySet()) {
				if (CollectionUtils.isNotEmpty(entry.getValue())) {
					result.put(entry.getKey(), entry.getValue().get(0).getId());
				}
			}
		}
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public void applyBasicData(String xmId, String professionId, TechLevel techLevel, String hostOrgId) {
		// 删除项目课程设置
		mapper.delete(new EntityWrapper<ZypxXmCourseSetting>().eq("xm_id", xmId).eq(techLevel != null, "tech_level", techLevel));
		// 删除项目试卷
		zypxExamPaperMapper.delete(new EntityWrapper<ExamPaper>().eq("xm_id", xmId).eq(techLevel != null, "tech_level", techLevel));
		// 复制当前职业下得课程基础数据
		EntityWrapper<ProfessionAdCourse> professionAdCourseEntityWrapper = new EntityWrapper<>();
		professionAdCourseEntityWrapper.eq("profession_id", professionId);
		professionAdCourseEntityWrapper.eq("host_org_id", hostOrgId);
		professionAdCourseEntityWrapper.eq(techLevel != null, "tech_level", techLevel);
		List<ProfessionAdCourse> professionAdCourses = professionAdCourseMapper.selectList(professionAdCourseEntityWrapper);
		if (CollectionUtils.isNotEmpty(professionAdCourses)) {
			List<ZypxXmCourseSetting> copyList = new ArrayList<>();
			professionAdCourses.forEach(a -> {
				ZypxXmCourseSetting zypxXmCourseSetting = new ZypxXmCourseSetting();
				BeanUtils.copyProperties(a, zypxXmCourseSetting);
				zypxXmCourseSetting.setId(BaseUtil.generateId());
				CourseMs courseMs = ModelHelper.convertObject(a.getMsContent());
				if (courseMs != null) {
					courseMs.setId(zypxXmCourseSetting.getId());
					zypxXmCourseSetting.setMsContent(ModelHelper.formatObject(courseMs));
				}
				zypxXmCourseSetting.setXmId(xmId);
				zypxXmCourseSetting.setCreateTime(new Date());
				zypxXmCourseSetting.setUpdateTime(new Date());
				copyList.add(zypxXmCourseSetting);
			});
			DBUtils.insertBatch(copyList, ZypxXmCourseSetting.class);
		}
		// 复制当前职业下得试卷基础数据
		EntityWrapper<ExamPaper> zypxExamPaperEntityWrapper = new EntityWrapper<>();
		zypxExamPaperEntityWrapper.eq("profession_id", professionId);
		zypxExamPaperEntityWrapper.eq("org_id", hostOrgId);
		professionAdCourseEntityWrapper.eq(techLevel != null, "tech_level", techLevel);
		zypxExamPaperEntityWrapper.isNull("xm_id");
		List<ExamPaper> examPaperList = zypxExamPaperMapper.selectList(zypxExamPaperEntityWrapper);
		if (CollectionUtils.isNotEmpty(examPaperList)) {
			List<ExamPaper> copyList = new ArrayList<>();
			examPaperList.forEach(a -> {
				ExamPaper examPaper = new ExamPaper();
				BeanUtils.copyProperties(a, examPaper);
				examPaper.setId(BaseUtil.generateId());
				examPaper.setName(a.getName() + "-副本");
				Paper paper = ModelHelper.convertObject(a.getData());
				if (paper != null) {
					paper.setId(examPaper.getId());
					examPaper.setData(ModelHelper.formatObject(paper));
				}
				examPaper.setXmId(xmId);
				examPaper.setCreateTime(new Date());
				copyList.add(examPaper);
			});
			DBUtils.insertBatch(copyList, ExamPaper.class);
		}
	}

	//待评价面授课程列表
	public Object doTargetCourseList(String studentId, String xmId) {
		return mapper.doTargetCourseList(studentId, xmId);
	}

	//通过项目id获取培训方联系人
	public List<Map<String, Object>> getXmLinkmanByXmId(String id, String keyword) {
		return mapper.getXmLinkmanByXmId(id, keyword);
	}
}
