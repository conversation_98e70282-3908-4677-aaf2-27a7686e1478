package com.xunw.jxjy.model.utils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.docx.DocxReader;
import com.xunw.jxjy.common.utils.docx.HtmlCreator;
import com.xunw.jxjy.common.utils.docx.ResourceInfo;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stnd;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.paper.model.Option;
import com.xunw.jxjy.paper.model.QBlank;
import com.xunw.jxjy.paper.utils.ModelHelper;

public class QuestionImportWordHelper {
	
	private static final Logger logger = LoggerFactory.getLogger(QuestionImportWordHelper.class);
	
	public static List<Map<String,Object>> questionConverteFromWord(File impFile) throws Exception {
		List<Map<String,Object>> quesList = dealQuestionConverteFromWord(impFile);
		List<Map<String,Object>> questionList = new ArrayList<>();
		ArrayList<Object> contentList = new ArrayList<>();
		for(Map<String,Object> question:quesList) {
			question.put("id", BaseUtil.generateId());
			question.put("difficulty", Stnd.CG);
			question.put("source", "Word");
			question.put("status", Zt.OK);
			if (contentList.contains(question.get("content"))){
				continue;
			}
			contentList.add(question.get("content"));
			QuestionImportTxtHelper.dealQuestionObject(question);
			questionList.add(question);
		}
		return questionList;

	}
	
	/**
	 * Word（docx格式）试题导入处理
	 * 根据模板导入的数据，构建了一个基本的试题对象，由外部方法追加其他的对象属性值，如ID，DBID等
	 * @param lines , 文件内容行
	 * @return
	 * @throws BizException
	 */
	public static List<Map<String,Object>> dealQuestionConverteFromWord(File impFile) throws Exception{
		List<Map<String,Object>> quesList = new ArrayList<Map<String,Object>>();
		DocxReader docxReader = null;
		InputStream is = null;
		try {
			docxReader = new DocxReader(impFile);
			docxReader.doRead();
			File documentFile = docxReader.getDocumentInfo().getFile();
			SAXReader sax = new SAXReader();
			is = new FileInputStream(documentFile);
			Document document = sax.read(is);
			Element root = document.getRootElement();
			Element body = root.element("body");
			@SuppressWarnings("rawtypes")
			List children = body.elements();

			int quesIndex = 0;
			int quesTypeIndex = 0;
			for(int i=0;i<children.size();i++){
				Element ele = (Element)children.get(i);
				String eleName = ele.getName();
				if(!eleName.equals("p") && !eleName.equals("tbl")){
					continue;
				}
				if(eleName.equals("p")){
					String txt = extractText(ele);
					if(txt.startsWith("[题型]")){
						String quesTypeStr = txt.substring("[题型]".length()).trim();
						if(quesTypeStr.length() == 0){
							throw BizException.withMessage("第" + (quesTypeIndex + 1) + "个题型为空，导入失败。");
						}
						quesTypeIndex++;
						
						int quesIndexInType = 0;
						List<Element> quesEles = new ArrayList<Element>();
						for(int j=i+1;j<children.size();j++){
							Element eleJ = (Element)children.get(j);
							String txtJ = extractText(eleJ);
							if(quesEles.size() == 0){
								if(eleJ.getName().equals("p") && txtJ.startsWith("[题干]")){
									quesEles.add(eleJ);
								}else {
									continue;
								}
							}else{
								if(txtJ.startsWith("[题干]") || txtJ.startsWith("[题型]")){
									Map<String,Object> ques = null;
									try{
										logger.debug("正在解析[" + quesTypeStr + "]里面的第" + (quesIndexInType + 1) + "道试题（全局序号：" + (quesIndex + 1) + "）...");
										ques = dealQuesUnit(quesEles, impFile, quesTypeStr);
									}catch(Exception e){
										throw BizException.withMessage("解析[" + quesTypeStr + "]里面的第" + (quesIndexInType + 1) + "道试题"+
										(txtJ.startsWith("[题干]") ?  "（"+extractText(quesEles.get(0))+"）": "" ) +"（全局序号：" + (quesIndex + 1) + "）时出现异常，原因：" + e.getMessage() +"，导入失败。");
									}
									quesIndex++;
									quesIndexInType++;
									quesList.add(ques);
									quesEles = new ArrayList<Element>();
									if(txtJ.startsWith("[题型]")){
										i = j - 1;
										break;
									}else {
										j--;
									}
								}else if(j == children.size()-1) {
									if(!eleJ.getName().equals("sectPr")) {
										quesEles.add(eleJ);
									}
									Map<String,Object> ques = null;
									try{
										logger.debug("正在解析[" + quesTypeStr + "]里面的第" + (quesIndexInType + 1) + "道试题（全局序号：" + (quesIndex + 1) + "）...");
										ques = dealQuesUnit(quesEles, impFile, quesTypeStr);
									}catch(Exception e){
										throw BizException.withMessage("解析[" + quesTypeStr + "]里面的第" + (quesIndexInType + 1) + "道试题（全局序号：" + (quesIndex + 1) + "）时出现异常，原因：" + e.getMessage() +"，导入失败。");
									}
									quesIndex++;
									quesIndexInType++;
									quesList.add(ques);
									quesEles = new ArrayList<Element>();
								}else {
									quesEles.add(eleJ);
								}
							}
						}
//					} else {
//						throw BizException.withMessage("文件格式错误，缺少题型");
					}
				}
				
			}
		} catch (Exception e) {
			throw BizException.withMessage(e.getMessage());
		} finally {
			BaseUtil.close(is);
			DocxReader.release(docxReader);
		}
		return quesList;
	}
		
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static Map<String,Object> dealQuesUnit(List<Element> quesEles,File quesFile,String quesTypeStr) throws BizException{
		Map<String,Object> quesMap = new HashMap<String, Object>();
		List<Option> opts = null;
		List<QBlank> blanks = null;
		String realType = null;
		boolean isOneChoice = false;
		boolean isMultipleChoice = false;
		boolean isJudgment = false;
		boolean isBlankfill = false;
		Stlb type = null;
		if(ModelHelper.FLAG_TYPE_SINGLECHOICE[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_SINGLECHOICE[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_SINGLECHOICE[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_SINGLECHOICE[0];
			type = Stlb.SINGLECHOICE;
			isOneChoice = true;
		}else if(ModelHelper.FLAG_TYPE_MULTIPLECHOICE[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_MULTIPLECHOICE[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_MULTIPLECHOICE[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_MULTIPLECHOICE[0];
			type = Stlb.MULTIPLECHOICE;
			isMultipleChoice = true;
		}else if(ModelHelper.FLAG_TYPE_JUDGMENT[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_JUDGMENT[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_JUDGMENT[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_JUDGMENT[0];
			type = Stlb.JUDGMENT;
			isJudgment = true;
		}else if(ModelHelper.FLAG_TYPE_BLANKFILL[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_BLANKFILL[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_BLANKFILL[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_BLANKFILL[0];
			type = Stlb.BLANKFILL;
			isBlankfill = true;
			blanks = new ArrayList<QBlank>();
//			throw BizException.withMessage("暂时不支持填空题");
		}else if(ModelHelper.FLAG_TYPE_ESSAY[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_ESSAY[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_ESSAY[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_ESSAY[0];
			type = Stlb.ESSAY;
		}else if(ModelHelper.FLAG_TYPE_MCJST[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_MCJST[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_MCJST[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_MCJST[0];
			type = Stlb.MCJST;
		}else if(ModelHelper.FLAG_TYPE_LST[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_LST[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_LST[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_LST[0];
			type = Stlb.LST;
		}else if(ModelHelper.FLAG_TYPE_JDT[0].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_JDT[1].equals(quesTypeStr) || ModelHelper.FLAG_TYPE_JDT[2].equals(quesTypeStr)){
			realType = ModelHelper.FLAG_TYPE_JDT[0];
			type = Stlb.JDT;
		}else{
			realType = quesTypeStr;
			type = Stlb.ESSAY;
		}
		quesMap.put("realType", realType);
		quesMap.put("type", type);
		boolean isXzt = isOneChoice || isMultipleChoice;//是否是选择题
        if(isXzt){
        	opts = new ArrayList<Option>();
        	quesMap.put("options",opts);
        }
		int optIndex = 0;//选项的开始索引值
		int answerIndex = 0;//答案的开始索引值
		int jiexiIndex = 0;//解析的开始索引值
		{//获取题干Word数据
			File newDocx = null;
	        DocxReader templetDocxReader = null;
	        Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
            File newQuesFile = null;
	        try {
	            {// 拷贝一份模板文件到临时文件夹里面
	                FileOutputStream fos = null;
	                InputStream is = null;
	                try {
	                    newDocx = FileHelper.createTmpFile();
	                    fos = new FileOutputStream(newDocx);
	                    is = QuestionImportWordHelper.class.getResourceAsStream("/docxTmps/quesTmp.docx");
	                    IOUtils.copyLarge(is, fos);
	                    fos.flush();
	                } finally {
	                    BaseUtil.close(is);
	                    BaseUtil.close(fos);
	                }
	            }

	            templetDocxReader = new DocxReader(newDocx);
	            templetDocxReader.doRead();
	            needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),templetDocxReader.getDocumentInfo());
	            
	            List bodyEles = new ArrayList();
	            for(int i=0;i<quesEles.size();i++){
	            	Element eleJ = quesEles.get(i);
	            	String txtJ = extractText(eleJ);
	            	if(isXzt){
	            		if(txtJ.startsWith("[选项A]")){
	            			optIndex = i;
	            			break;
	            		}
	            	}else if(txtJ.startsWith("[答案]")){
						answerIndex = i;
						break;
					}
	            	if(txtJ.startsWith("[题干]")){//去掉前缀
	            		removeSortNo(eleJ);
						eleJ = dealContentPrefixWord(eleJ);
					}
					bodyEles.add(eleJ.clone());
	            }
	            trimBlankLine(bodyEles);
	            if(bodyEles.size() == 0){
            		throw BizException.withMessage("题干不能为空");
            	}else if(isBlankfill){
            		dealBlankfillContent(bodyEles);
            	}
	            {
	            	newQuesFile = copyFileIntoTmpDir(quesFile);
	            	DocxReader newQuesDocxReader = null;
	                InputStream newQuesDocFileIs = null;
	                OutputStream newQuesDocFileOs = null;
	                Map<String, ResourceInfo> newQuesDocxNeedToUpdate = new HashMap<String, ResourceInfo>();
	        		try {
	        			newQuesDocxReader = new DocxReader(newQuesFile);
	        			newQuesDocxReader.doRead();
	        			File documentFile = newQuesDocxReader.getDocumentInfo().getFile();
	        			SAXReader sax = new SAXReader();
	        			newQuesDocFileIs = new FileInputStream(documentFile);
	        			Document document = sax.read(newQuesDocFileIs);
	        			Element root = document.getRootElement();
	        			Element body = root.element("body");
	        			body.setContent(bodyEles);
	        			newQuesDocFileOs = new FileOutputStream(documentFile,false);
	        			IOUtils.write(document.asXML(), newQuesDocFileOs, "UTF-8");
	        			newQuesDocxNeedToUpdate.put(newQuesDocxReader.getDocumentInfo().getPathInZip(),newQuesDocxReader.getDocumentInfo());
	        			DocxReader.updateDocx(newQuesFile, newQuesDocxNeedToUpdate);
	        		}finally {
	        			BaseUtil.close(newQuesDocFileIs);
	        			BaseUtil.close(newQuesDocFileOs);
	    	            DocxReader.release(newQuesDocxReader);
	    	        }
	            }
	            {
	            	File document = templetDocxReader.getDocumentInfo().getFile();
	            	InputStream is = null;
	            	FileOutputStream fos = null;
	            	InputStream newQuesFileIs = null;
	            	byte[] newQuesFileBytes = null;
	            	try {
	            		newQuesFileIs = new FileInputStream(newQuesFile);
	            		newQuesFileBytes = IOUtils.toByteArray(newQuesFileIs);
	            		String quesStr = FileHelper.dealWord(newQuesFileBytes, templetDocxReader, needToUpdate);
	            		is = new FileInputStream(document);
	                    String docStr = IOUtils.toString(is, "UTF-8");
	                    docStr = docStr.replace("<w:body>", "<w:body>" + quesStr);
	                    fos = new FileOutputStream(document, false);
	                    IOUtils.write(docStr, fos, "UTF-8");
	                    fos.flush();
	                    DocxReader.updateDocx(newDocx, needToUpdate);
					} finally {
	        			BaseUtil.close(is);
	        			BaseUtil.close(fos);
	        			BaseUtil.close(newQuesFileIs);
					}
	            }
	            {
	            	InputStream is = null;
	            	try {
	            		is = new FileInputStream(newDocx);
	            		String content = HtmlCreator.covertToHtml(IOUtils.toByteArray(is)).toString();
	            		if(isBlankfill) {
	            			content = content.replace("[空", "[BlankArea");
	            			int blankCount = QuestionImportTxtHelper.countBlanks(content);
	            			if(blankCount == 0){
	            				throw BizException.withMessage("题干里面没有发现填空项标识，请按模板要求编写");
	            			}
	            		}
	            		quesMap.put("content",content);
					} finally {
	        			BaseUtil.close(is);
					}
	            }
	            
	        } catch (Exception e) {
	        	e.printStackTrace();
	            throw BizException.withMessage(e.getMessage());
	        } finally {
	        	FileHelper.delFile(newQuesFile);
	        	FileHelper.delFile(newDocx);
	            DocxReader.release(templetDocxReader);
	        }
		}
		if(isXzt){
			if(optIndex == 0){
				throw BizException.withMessage("未发现选项信息");
			}
			char optChar = 'A';
			int i = 0;
			String anStr = null;//答案字符串
			Integer answerCount = 0;
			while(true){//获取选项Word数据
				if(optIndex + i > quesEles.size() - 1){
					break;
				}
            	Option opt = new Option();
            	opt.setAlisa(optChar + "");
				File newDocx = null;
		        DocxReader templetDocxReader = null;
		        Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
	            File optFile = null;
		        try {
	            	List optEles = new ArrayList();
		        	{
						Element eleJ = quesEles.get(optIndex + i);
						if(isBlankLine(eleJ)){
							i++;
							continue;
						}
		            	String txtJ = extractText(eleJ);
		            	if(txtJ.startsWith("[答案]")){
							answerIndex = optIndex + i;
							anStr = txtJ.substring("[答案]".length());
							if(anStr.length() == 0) {
								throw BizException.withMessage("答案为空");
							}
							answerCount++;
							break;
						}
		            	if(!txtJ.startsWith("[选项" + optChar + "]")){
		            		throw BizException.withMessage("无法识别选项数据里面的第" + (i + 1) + "行，预期此行应该是以“[选项" + optChar + "]”开头");
		            	}
		            	optEles.add(dealContentPrefixWord(eleJ));
		            	trimBlankLine(optEles);
		            	if(optEles.size() == 0){
		            		throw BizException.withMessage("选项" + optChar + "的值不能为空");
		            	}
		        	}
		            {// 拷贝一份模板文件到临时文件夹里面
		                FileOutputStream fos = null;
		                InputStream is = null;
		                try {
		                    newDocx = FileHelper.createTmpFile();
		                    fos = new FileOutputStream(newDocx);
		                    is = QuestionImportWordHelper.class.getResourceAsStream("/docxTmps/quesTmp.docx");
		                    IOUtils.copyLarge(is, fos);
		                    fos.flush();
		                } finally {
		                    BaseUtil.close(is);
		                    BaseUtil.close(fos);
		                }
		            }

		            templetDocxReader = new DocxReader(newDocx);
		            templetDocxReader.doRead();
		            needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),templetDocxReader.getDocumentInfo());
		            
		            {
		            	optFile = copyFileIntoTmpDir(quesFile);
		            	DocxReader newQuesDocxReader = null;
		                InputStream newQuesDocFileIs = null;
		                OutputStream newQuesDocFileOs = null;
		                Map<String, ResourceInfo> newQuesDocxNeedToUpdate = new HashMap<String, ResourceInfo>();
		        		try {
		        			newQuesDocxReader = new DocxReader(optFile);
		        			newQuesDocxReader.doRead();
		        			File documentFile = newQuesDocxReader.getDocumentInfo().getFile();
		        			SAXReader sax = new SAXReader();
		        			newQuesDocFileIs = new FileInputStream(documentFile);
		        			Document document = sax.read(newQuesDocFileIs);
		        			Element root = document.getRootElement();
		        			Element body = root.element("body");
		        			body.setContent(optEles);
		        			newQuesDocFileOs = new FileOutputStream(documentFile,false);
		        			IOUtils.write(document.asXML(), newQuesDocFileOs, "UTF-8");
		        			newQuesDocxNeedToUpdate.put(newQuesDocxReader.getDocumentInfo().getPathInZip(),newQuesDocxReader.getDocumentInfo());
		        			DocxReader.updateDocx(optFile, newQuesDocxNeedToUpdate);
		        		}finally {
		        			BaseUtil.close(newQuesDocFileIs);
		        			BaseUtil.close(newQuesDocFileOs);
		    	            DocxReader.release(newQuesDocxReader);
		    	        }
		            }
		            {
		            	File document = templetDocxReader.getDocumentInfo().getFile();
		            	InputStream is = null;
		            	FileOutputStream fos = null;
		            	InputStream newQuesFileIs = null;
		            	byte[] newQuesFileBytes = null;
		            	try {
		            		newQuesFileIs = new FileInputStream(optFile);
		            		newQuesFileBytes = IOUtils.toByteArray(newQuesFileIs);
		            		String quesStr = FileHelper.dealWord(newQuesFileBytes, templetDocxReader, needToUpdate);
		            		is = new FileInputStream(document);
		                    String docStr = IOUtils.toString(is, "UTF-8");
		                    docStr = docStr.replace("<w:body>", "<w:body>" + quesStr);
		                    fos = new FileOutputStream(document, false);
		                    IOUtils.write(docStr, fos, "UTF-8");
		                    fos.flush();
		                    DocxReader.updateDocx(newDocx, needToUpdate);
						} finally {
		        			BaseUtil.close(is);
		        			BaseUtil.close(fos);
		        			BaseUtil.close(newQuesFileIs);
						}
		            }
		            {
		            	InputStream is = null;
		            	try {
		            		is = new FileInputStream(newDocx);
		            		opt.setText(HtmlCreator.covertToHtml(IOUtils.toByteArray(is)).toString());
		                	opts.add(opt);
						} finally {
		        			BaseUtil.close(is);
						}
		            }
		            
		        } catch (BizException e) {
		            throw e;
		        } catch (Exception e) {
		            throw BizException.withMessage(e.getMessage());
		        } finally {
		        	FileHelper.delFile(optFile);
		        	FileHelper.delFile(newDocx);
		            DocxReader.release(templetDocxReader);
		        }
		        i++;
		        optChar = (char)(optChar + 1);
			}
			if (answerCount == 0){
				throw BizException.withMessage("缺少`[答案]`");
			}
			if(isOneChoice){
				if(anStr.length() != 1){
					throw BizException.withMessage("无法解析[答案]" + anStr);
				}
				boolean hasKey = false;
				for(Option opt:opts) {
					if(opt.getAlisa().equals(anStr)) {
						hasKey = true;
						break;
					}
				}
				if(!hasKey){
					throw BizException.withMessage("答案：" + anStr + "超出选项范围");
				}
				quesMap.put("answer", anStr);
			}
			if(isMultipleChoice){
				anStr = anStr.replace(" ", "").replace(",", "").replace("，", "").replace("、", "");
				if(anStr.length() < 2){
					throw BizException.withMessage("至少2个正确选项");
				}
				for(int si=0;si<anStr.length();si++) {
					boolean hasKey = false;
					for(Option opt:opts) {
						if(opt.getAlisa().equals(anStr.charAt(si) + "")) {
							hasKey = true;
							break;
						}
					}
					if(!hasKey){
						throw BizException.withMessage("答案：" + anStr + "超出选项范围或者格式不正确");
					}
				}
				quesMap.put("answer",anStr);
			}
			try{
				for(int j=answerIndex + 1;j<quesEles.size();j++){
	            	Element eleJ = quesEles.get(j);
	            	String txtJ = extractText(eleJ);
					if(txtJ.startsWith("[解析]")){
						jiexiIndex = j;
						break;
					}
	            }
			} catch (Exception e) {
	            throw BizException.withMessage(e.getMessage());
	        }
		}
		if(!isXzt){//获取答案Word数据
			if(answerIndex == 0){
				throw BizException.withMessage("未发现答案信息");
			}
			File newDocx = null;
	        DocxReader templetDocxReader = null;
	        Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
            File anFile = null;
	        try {
	            {// 拷贝一份模板文件到临时文件夹里面
	                FileOutputStream fos = null;
	                InputStream is = null;
	                try {
	                    newDocx = FileHelper.createTmpFile();
	                    fos = new FileOutputStream(newDocx);
	                    is = QuestionImportWordHelper.class.getResourceAsStream("/docxTmps/quesTmp.docx");
	                    IOUtils.copyLarge(is, fos);
	                    fos.flush();
	                } finally {
	                    BaseUtil.close(is);
	                    BaseUtil.close(fos);
	                }
	            }

	            templetDocxReader = new DocxReader(newDocx);
	            templetDocxReader.doRead();
	            needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),templetDocxReader.getDocumentInfo());
	            
	            List answerEles = new ArrayList();
	            boolean isComplex = true;//填空题的混杂模式默认为true
	            for(int i=answerIndex;i<quesEles.size();i++){
	            	Element eleJ = quesEles.get(i);
	            	if(i == answerIndex){
	            		Element eleJ2 = null;
	            		if(extractText(eleJ).startsWith("[答案]")){//去掉前缀
	            			eleJ2 = dealContentPrefixWord(eleJ);
						}else {
							eleJ2 = (Element) eleJ.clone();
						}
	            		answerEles.add(eleJ2);
	            	}else{
		            	String txtJ = extractText(eleJ);
		            	if(isBlankfill && txtJ.startsWith("[混杂]")){
		            		String comStr = txtJ.substring("[混杂]".length());
		            		isComplex = !(comStr.trim().equals("否"));
		            		continue;
		            	}
		            	if(txtJ.startsWith("[解析]")){
							jiexiIndex = i;
							break;
						}
						answerEles.add(eleJ.clone());
	            	}
	            }
	            trimBlankLine(answerEles);
	            if(answerEles.size() == 0){
            		throw BizException.withMessage("答案不能为空");
            	}
	            if(isBlankfill){
	            	String answer = "";
	            	int curOrder = 1;
	            	for(Object eleObj:answerEles) {
	            		Element ele = (Element)eleObj;
	            		String txt = extractText(ele);
	            		if(BaseUtil.isNotEmpty(txt)) {
	            			String preStr = "[空" + curOrder + "]";
	            			if(!txt.startsWith(preStr)) {
	            				throw BizException.withMessage("答案里面的第" + curOrder + "个填空项不符合模板要求");
	            			}
	            			String value = txt.substring(preStr.length()).trim();
	            			if(value.length() == 0) {
	            				throw BizException.withMessage("答案里面的第" + curOrder + "个填空项内容为空");
	            			}
	            			answer = answer + "," + value;
	        				QBlank blank = new QBlank(curOrder, "BLANK" + curOrder, value);
	        				blanks.add(blank);
	        				curOrder++;
	            		}
	            	}
	            	if(answer.length() == 0) {
	            		throw BizException.withMessage("未发现答案里面的填空项");
	            	}else {
	            		answer = answer.substring(1);
	            	}
	            	quesMap.put("answer",answer);
	            	quesMap.put("blanks",blanks);
	            	quesMap.put("isComplex", isComplex);
	            }else{
		            {
		            	anFile = copyFileIntoTmpDir(quesFile);
		            	DocxReader newQuesDocxReader = null;
		                InputStream newQuesDocFileIs = null;
		                OutputStream newQuesDocFileOs = null;
		                Map<String, ResourceInfo> newQuesDocxNeedToUpdate = new HashMap<String, ResourceInfo>();
		        		try {
		        			newQuesDocxReader = new DocxReader(anFile);
		        			newQuesDocxReader.doRead();
		        			File documentFile = newQuesDocxReader.getDocumentInfo().getFile();
		        			SAXReader sax = new SAXReader();
		        			newQuesDocFileIs = new FileInputStream(documentFile);
		        			Document document = sax.read(newQuesDocFileIs);
		        			Element root = document.getRootElement();
		        			Element body = root.element("body");
		        			body.setContent(answerEles);
		        			newQuesDocFileOs = new FileOutputStream(documentFile,false);
		        			IOUtils.write(document.asXML(), newQuesDocFileOs, "UTF-8");
		        			newQuesDocxNeedToUpdate.put(newQuesDocxReader.getDocumentInfo().getPathInZip(),newQuesDocxReader.getDocumentInfo());
		        			DocxReader.updateDocx(anFile, newQuesDocxNeedToUpdate);
		        		}finally {
		        			BaseUtil.close(newQuesDocFileIs);
		        			BaseUtil.close(newQuesDocFileOs);
		    	            DocxReader.release(newQuesDocxReader);
		    	        }
		            }
		            {
		            	File document = templetDocxReader.getDocumentInfo().getFile();
		            	InputStream is = null;
		            	FileOutputStream fos = null;
		            	InputStream newQuesFileIs = null;
		            	byte[] newQuesFileBytes = null;
		            	try {
		            		newQuesFileIs = new FileInputStream(anFile);
		            		newQuesFileBytes = IOUtils.toByteArray(newQuesFileIs);
		            		String quesStr = FileHelper.dealWord(newQuesFileBytes, templetDocxReader, needToUpdate);
		            		is = new FileInputStream(document);
		                    String docStr = IOUtils.toString(is, "UTF-8");
		                    docStr = docStr.replace("<w:body>", "<w:body>" + quesStr);
		                    fos = new FileOutputStream(document, false);
		                    IOUtils.write(docStr, fos, "UTF-8");
		                    fos.flush();
		                    DocxReader.updateDocx(newDocx, needToUpdate);
						} finally {
							BaseUtil.close(is);
		        			BaseUtil.close(fos);
		        			BaseUtil.close(newQuesFileIs);
						}
		            }
		            {
		            	InputStream is = null;
		            	try {
		            		is = new FileInputStream(newDocx);
		            		String keyHtml = HtmlCreator.covertToHtml(IOUtils.toByteArray(is)).toString();
		            		if(isJudgment) {
		            			String keyTxt = BaseUtil.extractText(keyHtml);
		            			if(!ModelHelper.FLAG_VAL_JUDGMENT_YES.contains(keyTxt) && !ModelHelper.FLAG_VAL_JUDGMENT_NO.contains(keyTxt)){
		            				throw BizException.withMessage("不可识别的判断题答案:" + keyTxt);
		            			}
		            			if(ModelHelper.FLAG_VAL_JUDGMENT_YES.contains(keyTxt)){
		            				keyHtml = "Y";
		            			}else{
		            				keyHtml = "N";
		            			}
		            		}
		            		quesMap.put("answer",keyHtml);
						} finally {
		        			BaseUtil.close(is);
						}
		            }
	            }
	        } catch (BizException e) {
	            throw e;
	        } catch (Exception e) {
	            throw BizException.withMessage(e.getMessage());
	        } finally {
	        	FileHelper.delFile(anFile);
	        	FileHelper.delFile(newDocx);
	        	DocxReader.release(templetDocxReader);
	        }
		}
		try{//处理后面的解析，可选项
			if(jiexiIndex > 0){
				File newDocx = null;
		        DocxReader templetDocxReader = null;
		        Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
	            File jiexiFile = null;
		        try {
		            {// 拷贝一份模板文件到临时文件夹里面
		                FileOutputStream fos = null;
		                InputStream is = null;
		                try {
		                    newDocx = FileHelper.createTmpFile();
		                    fos = new FileOutputStream(newDocx);
		                    is = QuestionImportWordHelper.class.getResourceAsStream("/docxTmps/quesTmp.docx");
		                    IOUtils.copyLarge(is, fos);
		                    fos.flush();
		                } finally {
		                    BaseUtil.close(is);
		                    BaseUtil.close(fos);
		                }
		            }

		            templetDocxReader = new DocxReader(newDocx);
		            templetDocxReader.doRead();
		            needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),templetDocxReader.getDocumentInfo());
		            
		            List jiexiEles = new ArrayList();
		            for(int i=jiexiIndex;i<quesEles.size();i++){
		            	Element eleJ = quesEles.get(i);
		            	Element eleJ2 = null;
	            		if(extractText(eleJ).startsWith("[解析]")){//去掉前缀
	            			eleJ2 = dealContentPrefixWord(eleJ);
						}else {
							eleJ2 = (Element) eleJ.clone();
						}
	            		jiexiEles.add(eleJ2);
		            }
		            trimBlankLine(jiexiEles);
		            if(jiexiEles.size() > 0){
		            	jiexiFile = copyFileIntoTmpDir(quesFile);
		            	DocxReader newQuesDocxReader = null;
		                InputStream newQuesDocFileIs = null;
		                OutputStream newQuesDocFileOs = null;
		                Map<String, ResourceInfo> newQuesDocxNeedToUpdate = new HashMap<String, ResourceInfo>();
		        		try {
		        			newQuesDocxReader = new DocxReader(jiexiFile);
		        			newQuesDocxReader.doRead();
		        			File documentFile = newQuesDocxReader.getDocumentInfo().getFile();
		        			SAXReader sax = new SAXReader();
		        			newQuesDocFileIs = new FileInputStream(documentFile);
		        			Document document = sax.read(newQuesDocFileIs);
		        			Element root = document.getRootElement();
		        			Element body = root.element("body");
		        			body.setContent(jiexiEles);
		        			newQuesDocFileOs = new FileOutputStream(documentFile,false);
		        			IOUtils.write(document.asXML(), newQuesDocFileOs, "UTF-8");
		        			newQuesDocxNeedToUpdate.put(newQuesDocxReader.getDocumentInfo().getPathInZip(),newQuesDocxReader.getDocumentInfo());
		        			DocxReader.updateDocx(jiexiFile, newQuesDocxNeedToUpdate);
		        		}finally {
		        			BaseUtil.close(newQuesDocFileIs);
		        			BaseUtil.close(newQuesDocFileOs);
		    	            DocxReader.release(newQuesDocxReader);
		    	        }
		            }
		            {
		            	File document = templetDocxReader.getDocumentInfo().getFile();
		            	InputStream is = null;
		            	FileOutputStream fos = null;
		            	InputStream newQuesFileIs = null;
		            	byte[] newQuesFileBytes = null;
		            	try {
		            		newQuesFileIs = new FileInputStream(jiexiFile);
		            		newQuesFileBytes = IOUtils.toByteArray(newQuesFileIs);
		            		String quesStr = FileHelper.dealWord(newQuesFileBytes, templetDocxReader, needToUpdate);
		            		is = new FileInputStream(document);
		                    String docStr = IOUtils.toString(is, "UTF-8");
		                    docStr = docStr.replace("<w:body>", "<w:body>" + quesStr);
		                    fos = new FileOutputStream(document, false);
		                    IOUtils.write(docStr, fos, "UTF-8");
		                    fos.flush();
		                    DocxReader.updateDocx(newDocx, needToUpdate);
						} finally {
							BaseUtil.close(is);
		        			BaseUtil.close(fos);
		        			BaseUtil.close(newQuesFileIs);
						}
		            }
		            {
		            	InputStream is = null;
		            	try {
		            		is = new FileInputStream(newDocx);
		            		quesMap.put("resolve",HtmlCreator.covertToHtml(IOUtils.toByteArray(is)).toString());
						} finally {
		        			BaseUtil.close(is);
						}
		            }
		            
		        }catch (Exception e) {
		            throw BizException.withMessage(e.getMessage());
		        } finally {
		        	FileHelper.delFile(jiexiFile);
		        	FileHelper.delFile(newDocx);
		        	DocxReader.release(templetDocxReader);
		        }
			}
		} catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw BizException.withMessage(e.getMessage());
        }
		return quesMap;
	}
	
	/**
	 * 截取掉内容的第一行里面的特殊标识（例如：[题干]）前缀，并返回新的克隆节点。
	 * 注意：特殊标识一定是在行的最前面，格式为“[xxx]”
	 * @param ele
	 * @return
	 * @throws IOException
	 * @throws DocumentException
	 */
	private static Element dealContentPrefixWord(Element ele) throws Exception{
		Element eleTmp = (Element)ele.clone();
		@SuppressWarnings("rawtypes")
		List tNodes = eleTmp.selectNodes("w:r/w:t");
		boolean isOk = false;
		for(int i=0;i<tNodes.size();i++){
			Element tNode = (Element)tNodes.get(i);
			String tStr = tNode.getText().trim();
			if(tStr.indexOf(']') < 0){//说明都是“]”前面的内容
				tNode.getParent().getParent().remove(tNode.getParent());
			}else{
				tStr = tStr.substring(tStr.indexOf(']') + 1);
				if(tStr.length() == 0){
					tNode.getParent().getParent().remove(tNode.getParent());
				}else{
					tNode.setText(tStr);
				}
				isOk = true;
				break;
			}
		}
		if(!isOk) {
			 throw BizException.withMessage("未发现标识");
		}
		return eleTmp;
	}
	
	/**
	 * 处理填空题的题干，把分开的标识符合并到一个w:t标签里面去，方便后面的填空标识符处理
	 * @param bodyEles
	 * @throws IOException
	 * @throws DocumentException
	 */
	@SuppressWarnings("rawtypes")
	private static void dealBlankfillContent(List bodyEles) throws Exception{
		for(Object eleObj:bodyEles) {
			Element ele = (Element)eleObj;
			if(extractText(ele).contains("[空")) {//说明该段落里面有填空项
				List tNodes = ele.selectNodes("w:r/w:t");
				for(int i=0;i<tNodes.size();i++){
					Element tNode = (Element)tNodes.get(i);
					String tStr = tNode.getText().trim();
					int flagStartIndex = tStr.lastIndexOf('[');
					if(flagStartIndex >= 0){//说明后面的内容可能是填空项标识符
						int flagEndIndex = tStr.indexOf(']');
						if(flagEndIndex < flagStartIndex) {
							String tStrTmp = tStr.substring(flagStartIndex);
							int endFlagNodeIndex = -1;
							for(int j=i+1;j<tNodes.size();j++) {
								Element tNodeJ = (Element)tNodes.get(j);
								String tStrJ = tNodeJ.getText().trim();
								tStrTmp = tStrTmp + tStrJ;
								if(tStrJ.indexOf(']') >= 0) {
									endFlagNodeIndex = j;
									break;
								}
							}
							if(endFlagNodeIndex > 0 && tStrTmp.startsWith("[空")) {
								for(int j=i+1;j<=endFlagNodeIndex;j++) {
									Element tNodeJ = (Element)tNodes.get(j);
									tNodeJ.setText("");
									tNodeJ.getParent().getParent().remove(tNodeJ.getParent());
								}
								String newTStr = tStr.substring(0, flagStartIndex) + tStrTmp;
								tNode.setText(newTStr);
								i--;
							}
						}
					}
				}
			}
		}
	}
	
	/**
	 * 删除首尾的空行
	 * @param eles
	 * @throws IOException
	 * @throws DocumentException
	 */
	@SuppressWarnings("rawtypes")
	private static void trimBlankLine(List eles) throws IOException, DocumentException{
		while(eles.size() > 0){
			Element tNode = (Element)eles.get(eles.size() - 1);
			if(isBlankLine(tNode)){
				eles.remove(eles.size() - 1);
			}else{
				break;
			}
		}
		while(eles.size() > 0){
			Element tNode = (Element)eles.get(0);
			if(isBlankLine(tNode)){
				eles.remove(0);
			}else{
				break;
			}
		}
	}
	
	/**
	 * 是否是空行
	 * @param tNode
	 * @return
	 * @throws IOException
	 * @throws DocumentException
	 */
	private static boolean isBlankLine(Element tNode) throws IOException, DocumentException{
		return tNode.getName().equals("p") && tNode.elements("r").size() == 0 && extractText(tNode).length() == 0;
	}
	
	/**
	 * 去掉一行前面的序号。必须是Word里面的“编号”
	 * @param ele
	 */
	private static void removeSortNo(Element ele){
		Element pPr = ele.element("pPr");
		if(pPr != null){
			@SuppressWarnings("rawtypes")
			List children = pPr.elements();
			for(int i=0;i<children.size();i++){
				Element child = (Element)children.get(i);
				String tagName = child.getName();
				if(tagName.equals("pStyle") || tagName.equals("numPr") || tagName.equals("ind")){
					children.remove(i);
					i--;
				}
			}
			pPr.setContent(children);
		}
	}
	
	private static File copyFileIntoTmpDir(File file) throws IOException{
		File newFile = FileHelper.createTmpFile();
		FileUtils.copyFile(file, newFile);
		return newFile;
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static String extractText(Element ele) throws IOException, DocumentException {
		String xtStrTmp = Constants.PAPERTMPBODY_START + Constants.PAPERTMPBODY_END;
        SAXReader saxTmp = new SAXReader();
        ByteArrayInputStream isTmp = new ByteArrayInputStream(xtStrTmp.getBytes("UTF-8"));
        Document documentTmp = saxTmp.read(isTmp);
        Element rootTmp = documentTmp.getRootElement();
        List cTmp = rootTmp.elements();
        cTmp.add(ele.clone());
        rootTmp.setContent(cTmp);
        xtStrTmp = documentTmp.asXML();
        int firstWtBeginIndex = xtStrTmp.indexOf("<paperTmpBody>");
    	int firstWtBeginIndex2 = xtStrTmp.indexOf("<paperTmpBody ");
    	firstWtBeginIndex = (firstWtBeginIndex < 0 || (firstWtBeginIndex2 >= 0 && firstWtBeginIndex > firstWtBeginIndex2))?firstWtBeginIndex2:firstWtBeginIndex;
    	int nearestGtIndex = xtStrTmp.indexOf('>', firstWtBeginIndex + 1);
    	
        xtStrTmp = xtStrTmp.substring(nearestGtIndex + 1, xtStrTmp.length() - Constants.PAPERTMPBODY_END.length());
        return BaseUtil.trimBlank(BaseUtil.extractText(xtStrTmp));
	}
}
