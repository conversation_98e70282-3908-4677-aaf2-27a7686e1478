package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

//培训联系人
@TableName("BIZ_XM_LINKMAN")
public class ZypxXmContact {

    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //项目id
    @TableField("XM_ID")
    private String xmId;

    //姓名
    @TableField("name")
    private String name;

    //联系方式
    @TableField("PHONE")
    private String phone;

    //备注
    @TableField("REMARK")
    private String remark;

    public ZypxXmContact() {
    }

    public ZypxXmContact(String id, String xmId, String name, String phone, String remark) {
        this.id = id;
        this.xmId = xmId;
        this.name = name;
        this.phone = phone;
        this.remark = remark;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getXmId() {
        return xmId;
    }

    public void setXmId(String xmId) {
        this.xmId = xmId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
