package com.xunw.jxjy.model.inf.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.inf.entity.IpWhite;
import com.xunw.jxjy.model.inf.mapper.IpWhiteMapper;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

@Service
public class IpWhiteService extends BaseCRUDService<IpWhiteMapper, IpWhite>{

	//ip白名单校验 校验是否在ip白名单中
	public boolean checkIp(HttpServletRequest request) {
		return mapper.selectCount(new EntityWrapper<IpWhite>().eq("ip", BaseUtil.getClientIpAddr(request))) > 0;
	}
}
