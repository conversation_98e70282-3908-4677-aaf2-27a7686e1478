package com.xunw.jxjy.model.zyjd.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.GroupStatus;

import java.io.Serializable;
import java.util.Date;

/**
 * 评审打分表
 */
@TableName("BIZ_REVIEW_MARK")
public class ZcReviewMark implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    private String id;

    //报名id
    @TableField(value = "bm_id")
    private String bmId;

    //老师id
    @TableField(value = "user_id")
    private String userId;

    //成绩
    @TableField(value = "score")
    private Double score;

    @TableField(value = "grade")
    private String grade;

    @TableField(value = "remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBmId() {
        return bmId;
    }

    public void setBmId(String bmId) {
        this.bmId = bmId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
