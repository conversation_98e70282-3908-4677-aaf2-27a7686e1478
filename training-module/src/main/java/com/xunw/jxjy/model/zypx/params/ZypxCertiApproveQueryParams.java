package com.xunw.jxjy.model.zypx.params;

import com.xunw.jxjy.model.core.BaseQueryParams;

public class ZypxCertiApproveQueryParams extends BaseQueryParams {
	
	private String xmId;
	//学员关键字
	private String keyword;

	private String approveType;
	
	private String hostOrgId;
	
	private String leaderId;
	
	private Integer lsStart;
	private Integer lsEnd;
	private Integer psStart;
	private Integer psEnd;
	private Integer zsStart;
	private Integer zsEnd;
	
	public String getApproveType() {
		return approveType;
	}

	public void setApproveType(String approveType) {
		this.approveType = approveType;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}

	public Integer getLsStart() {
		return lsStart;
	}

	public void setLsStart(Integer lsStart) {
		this.lsStart = lsStart;
	}

	public Integer getLsEnd() {
		return lsEnd;
	}

	public void setLsEnd(Integer lsEnd) {
		this.lsEnd = lsEnd;
	}

	public Integer getPsStart() {
		return psStart;
	}

	public void setPsStart(Integer psStart) {
		this.psStart = psStart;
	}

	public Integer getPsEnd() {
		return psEnd;
	}

	public void setPsEnd(Integer psEnd) {
		this.psEnd = psEnd;
	}

	public Integer getZsStart() {
		return zsStart;
	}

	public void setZsStart(Integer zsStart) {
		this.zsStart = zsStart;
	}

	public Integer getZsEnd() {
		return zsEnd;
	}

	public void setZsEnd(Integer zsEnd) {
		this.zsEnd = zsEnd;
	}
}
