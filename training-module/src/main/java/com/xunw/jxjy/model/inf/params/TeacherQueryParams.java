package com.xunw.jxjy.model.inf.params;

import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Zt;

/**
 * 页面查询参数
 */
public class TeacherQueryParams extends BaseQueryParams {

	private static final long serialVersionUID = 7915470138377688575L;

	//用户id
	private String id;
	//类型id
	private String typeId;
	//关键词
	private String keyword;
	//是否外聘
	private String isOut;
	//状态
	private Zt status;

	private String name;

	private String mobile;

	private String sfzh;

	private String email;

	private Gender gender;
	//学历
	private Education education;
	//工作单位
	private String workUnit;

	private String zw;

	private String zc;
	//是否门户展示
	private String isShowPortal;
	//人物简介
	private String brief;

	//用户照片
	private String photo;

	//职称证书扫描件
	private String zcPhoto;

	//银行卡号
	private String bankCardNo;

	//开户行
	private String bank;

	//银行卡扫描件
	private String bankCardPhoto;

	//办公电话
	private String officeTel;

	//推荐指数
	private String recommend;

	//主讲课程
	private String courses;

	//研究方向
	private String studyDirection;

	private String hostOrgId;

	//专业类别
	private String specialityType;

	// 可评选工种筛选
	private String professionId;

	// 可评选工种配置
	private String professionIds;

	//校内 所属学院 校外 师资来源 企业，高校，党政干部
	private String category;

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getIsOut() {
		return isOut;
	}

	public void setIsOut(String isOut) {
		this.isOut = isOut;
	}

	public Zt getStatus() {
		return status;
	}

	public void setStatus(Zt status) {
		this.status = status;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getSfzh() {
		return sfzh;
	}

	public void setSfzh(String sfzh) {
		this.sfzh = sfzh;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public Education getEducation() {
		return education;
	}

	public void setEducation(Education education) {
		this.education = education;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getZw() {
		return zw;
	}

	public void setZw(String zw) {
		this.zw = zw;
	}

	public String getZc() {
		return zc;
	}

	public void setZc(String zc) {
		this.zc = zc;
	}

	public String getIsShowPortal() {
		return isShowPortal;
	}

	public void setIsShowPortal(String isShowPortal) {
		this.isShowPortal = isShowPortal;
	}

	public String getBrief() {
		return brief;
	}

	public void setBrief(String brief) {
		this.brief = brief;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public String getOfficeTel() {
		return officeTel;
	}

	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}

	public String getRecommend() {
		return recommend;
	}

	public void setRecommend(String recommend) {
		this.recommend = recommend;
	}

	public String getCourses() {
		return courses;
	}

	public void setCourses(String courses) {
		this.courses = courses;
	}

	public String getStudyDirection() {
		return studyDirection;
	}

	public void setStudyDirection(String studyDirection) {
		this.studyDirection = studyDirection;
	}

	public String getZcPhoto() {
		return zcPhoto;
	}

	public void setZcPhoto(String zcPhoto) {
		this.zcPhoto = zcPhoto;
	}

	public String getBankCardNo() {
		return bankCardNo;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public String getBank() {
		return bank;
	}

	public void setBank(String bank) {
		this.bank = bank;
	}

	public String getBankCardPhoto() {
		return bankCardPhoto;
	}

	public void setBankCardPhoto(String bankCardPhoto) {
		this.bankCardPhoto = bankCardPhoto;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getSpecialityType() {
		return specialityType;
	}

	public void setSpecialityType(String specialityType) {
		this.specialityType = specialityType;
	}

	public String getProfessionId() {
		return professionId;
	}

	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}

	public String getProfessionIds() {
		return professionIds;
	}

	public void setProfessionIds(String professionIds) {
		this.professionIds = professionIds;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
}
