package com.xunw.jxjy.model.portal.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.plugins.Page;
import org.apache.ibatis.annotations.Param;

public interface ZkMapper {

    List<Map<String, Object>> getSubjects();

    List<Map<String, Object>> getSpecialtys(Map<String, Object> condition, Page<Map<String, Object>> page);

    Map<String, Object> getSpecialty(@Param("s_id") String s_id);

    List<Map<String, Object>> getCourseType(@Param("s_id") String s_id);

    List<Map<String, Object>> getCourseChildType(@Param("spct_id") String spct_id);

    List<Map<String, Object>> getSpecialtyCourse(@Param("spct_id") String spct_id, @Param("spcct_id") String spcct_id);

    Map<String, Object> getCourse(@Param("c_id") String c_id);

    List<Map<String, Object>> getCateogrys();

    Map<String, Object> getCateogry(@Param("c_id") String c_id);

    List<Map<String, Object>> getNews(Map<String, Object> condition, Page<Map<String, Object>> page);

    Map<String, Object> getNew(@Param("n_id") String n_id);

}