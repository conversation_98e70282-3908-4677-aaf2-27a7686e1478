package com.xunw.jxjy.model.zyjd.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.BmType;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Ksly;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.enums.Pxfs;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.ZyjdBmStatus;

/**
 * 职业鉴定报名表
 */
@TableName("biz_zyjd_bm")
public class ZyjdBm implements Serializable {

    private static final long serialVersionUID = 3881170810899665018L;

    // 主键id
    @TableId(type = IdType.INPUT)
    @TableField("id")
    private String id;

    // 批次ID
    @TableField("bmbatch_id")
    private String bmbatchId;

    //学员ID
    @TableField("student_id")
    private String studentId;

    // 考生来源
    @TableField("ksly")
    private Ksly ksly;

    // 参加工作时间
    @TableField("work_time")
    private Date workTime;

    // 现任专业技术职务/现从事职业
    @TableField("profession")
    private String profession;

    // 申报行业ID
    @TableField("industry_id")
    private String industryId;

    // 申报职业ID
    @TableField("profession_id")
    private String professionId;

    // 申报职业方向ID
    @TableField("direction_id")
    private String directionId;

    // 申报等级
    @TableField("apply_tech_level")
    private TechLevel applyTechLevel;

    // 从业年限(申报职业的工作年限)
    @TableField("work_years")
    private String workYears;

    // 保存时间
    @TableField("save_time")
    private Date saveTime;

    // 提交时间
    @TableField("submit_time")
    private Date submitTime;

    //报名状态
    @TableField("status")
    private ZyjdBmStatus status;

    // 缴费状态
    @TableField("pay_status")
    private PayStatus payStatus;

    //备注信息
    @TableField("remark")
    private String remark;

    //缴费方式： 1 线上缴费  0 线下缴费
    @TableField("pay_type")
    private String payType;

    // 审核时间
    @TableField("approve_time")
    private Date approveTime;

    // 审核意见
    @TableField("approve_advice")
    private String approveAdvice;

    // 审核用户ID
    @TableField("approve_user_id")
    private String approveUserId;

    //原证书职业
    @TableField("old_certi_profession")
    private String oldCertiProfession;

    // 原技术等级
    @TableField("old_tech_level")
    private TechLevel oldTechLevel;

    // 取得原技术等级时间
    @TableField("old_tech_level_time")
    private Date oldTechLevelTime;

    // 原证书编号
    @TableField("old_tech_certi_num")
    private String oldTechCertiNum;

    // 原证书相片
    @TableField("old_certi_photo")
    private String oldCertiPhoto;

    // 缴费金额
    @TableField("amount")
    private Double amount;

    // 标记缴费用户ID
    @TableField("mark_payd_user_id")
    private String markPaydUserId;

    // 工作年限/工龄
    @TableField("job_years")
    private String jobYears;

    // 专业工作年限
    @TableField("profession_years")
    private String professionYears;

    // 任现职的时间
    @TableField("current_job_time")
    private String currentJobTime;

    // 测试方式
    @TableField("test_way")
    private String testWay;

    // 原证书名称
    @TableField("old_certi_name")
    private String oldCertiName;

    //准考证号
    @TableField("zkz")
    private String zkz;

    // 准考证号生成时间
    @TableField("zkzno_create_time")
    private Date zkznoCreateTime;

    // 现从事专业
    @TableField("current_job")
    private String currentJob;

    // 全日制最高学历
    @TableField("full_education")
    private Education fullEducation;

    // 全日制所学专业
    @TableField("full_specialty")
    private String fullSpecialty;

    // 全日制毕业时间
    @TableField("full_edu_time")
    private Date fullEduTime;

    // 在职最高学历
    @TableField("job_education")
    private Education jobEducation;

    // 在职所学专业
    @TableField("job_specialty")
    private String jobSpecialty;

    // 在职毕业时间
    @TableField("job_edu_time")
    private Date jobEduTime;

    // 论文题目
    @TableField("article_tital")
    private String articleTital;

    // 第几作者
    @TableField("ddzz")
    private String ddzz;

    // 发表期刊
    @TableField("fbqk")
    private String fbqk;

    // 年度
    @TableField("nd")
    private String nd;

    // 期号
    @TableField("qh")
    private String qh;

    // 论文发表
    @TableField("is_article_publish")
    private String isArticlePublish;

    // 论文图片URL 地址，多个值用逗号分开
    @TableField("article_photo_url")
    private String articlePhotoUrl;

    // 专业技术任职资格证书
    @TableField("zyjsrzzgzs")
    private String zyjsrzzgzs;

    // 已确认的报名表图片
    @TableField("yqrbmb")
    private String yqrbmb;

    // 现任专业技术职务聘文
    @TableField("xrzyjszwpw")
    private String xrzyjszwpw;

    // 报名类型
    @TableField("bmlx")
    private BmType bmlx;

    // 培训方式
    @TableField("pxfs")
    private Pxfs pxfs;

    // 工作年限证明附件
    @TableField("gznxzm")
    private String gznxzm;

    // 发票抬头
    @TableField("invoice_title")
    private String invoiceTitle;

    // 纳税人识别号
    @TableField("taxpayer_number")
    private String taxpayerNumber;

    //推荐代码
    @TableField("recommend_code")
    private String recommendCode;

    //承诺书
    @TableField("commitment")
    private String commitment;

    //新资格证书
    @TableField("new_certificate")
    private String newCertificate;

    //证书邮寄方式
    @TableField("certi_post")
    private String certiPost;

    //业绩证明
    @TableField("performance")
    private String performance;

    //报考条件ID
    @TableField("condition_id")
    private String conditionId;

    //健康码
    @TableField("healthy_code")
    private String healthyCode;

    //行程卡
    @TableField("trip_card")
    private String tripCard;

    //标记退费用户ID
    @TableField("mark_refund_user_id")
    private String markRefundUserId;

    //退费金额
    @TableField("refund_amount")
    private Double refundAmount;

    //是否已入账1 是 0 否
    @TableField("is_rec")
    private String isRec;

    //对账单编号
    @TableField("rec_code")
    private String recCode;

    //专业技术人员专用字段##开始
    @TableField("regionalism_code")
    private String regionalismCode;//行政区划代码

    @TableField("xb")
    private String xb;//性别

    @TableField("certi_category")
    private String certiCategory;//证件类型

    @TableField("xl")
    private String xl;//学历

    @TableField("mz")
    private String mz;//民族

    @TableField("xw")
    private String xw;//学位

    @TableField("social_credit_code")
    private String socialCreditCode;//所在单位统一社会信用代码

    @TableField("unit_nature")
    private String unitNature;//所在单位性质

    @TableField("is_provide_jxjy")
    private String isProvideJxjy;//是否由所在单位提供继续教育培训

    @TableField("obtain_certi_category")
    private String obtainCertiCategory;//取得证书类型

    @TableField("zc_series")
    private String zcSeries;//职称系列

    @TableField("zy_qualification_name")
    private String zyQualificationName;//职业资格名称

    @TableField("zy_qualification_level")
    private String zyQualificationLevel;//职业资格等级

	@TableField("zc_level")
	private String zcLevel;//职称级别

	@TableField("receipt")
	private String receipt;//报名回执

    @TableField("is_reevaluation")
    private String isReevaluation;//是否转评

    @TableField("is_exceptional")
    private String isExceptional;//是否破格

	//##结束

    //--答辩评审
    /**
     * 理论考试得分
     */
    @TableField("exam_score")
    private Double examScore;
    /**
     * 操作技能得分
     */
    @TableField("skill_score")
    private Double skillScore;
    /**
     * 工作业绩得分
     */
    @TableField("job_score")
    private Double jobScore;
    /**
     * 潜在能力得分
     */
    @TableField("potential_score")
    private Double potentialScore;
    /**
     * 综合评定
     */
    @TableField("synthesize_score")
    private Double synthesizeScore;
    /**
     * 评审得分
     */
    @TableField("score")
    private String score;
	/**
	 * 发票地址，用于土地地址测绘学员缴费
	 */
	@TableField("invoice_url")
	private String invoiceUrl;

    /**
     * 论文得分
     */
    @TableField("thesis_score")
    private Double thesisScore;

    /**
     * 奖励
     */
    @TableField("reward")
    private String reward;

	/**
	 * 业绩
	 */
	@TableField("achievement")
	private String achievement;

	/**
	 * 工作经历 json字符串
	 */
	@TableField("work_experience")
	private String workExperience;

    /**
	 * 工作经历 电力行业
	 */
	@TableField("work_experience_dl")
	private String workExperienceDl;

    /**
	 * 培训经历
	 */
	@TableField("training_experience")
	private String trainingExperience;

    /**
	 * 取得技师后的工作业绩
	 */
	@TableField("js_work_achievement")
	private String jsWorkAchievement;


    /**
	 * 主要技术特长、贡献及成果
	 */
	@TableField("skill_speciality")
	private String skillSpeciality;

    /**
	 * 技术革新、技术改造、科技成果转化、关键问题处理
	 */
	@TableField("xm")
	private String xm;


    /**
	 * 编写操作规程、规范、标准、教案及发表论文、著作等情况
	 */
	@TableField("opus")
	private String opus;

    /**
	 * 参加技能竞赛获奖情况
	 */
	@TableField("competition")
	private String competition;

    /**
	 * 承担技艺传授、技能培训工作情况
	 */
	@TableField("bear_content")
	private String bearContent;

    /**
	 * 获得荣誉称号
	 */
	@TableField("honor")
	private String honor;

    /**
	 * 技术总结
	 */
	@TableField("technical_summary")
	private String technicalSummary;

    /**
	 * 论文
	 */
	@TableField("thesis")
	private String thesis;

    /**
	 * 取得技师资格等级时间
	 */
	@TableField("js_time")
	private Date jsTime;

	/**
	 * 特长
	 */
	@TableField("speciality")
	private String speciality;

    /**
     * 直属单位
     */
	@TableField("unit_name")
    private String unitName;
    /**
     * 技师鉴定考评申报表
     */
    @TableField("APPRAISAL_FORMS")
    private String appraisalForms;
    /**
     * 技术总结材料
     */
    @TableField("TECHNICAL_SUMMARY_FORMS")
    private String technicalSummaryForms;
    /**
     * 论文材料
     */
    @TableField("THESIS_FORMS")
    private String thesisForms;
    /**
     * 工作业绩评定材料
     */
    @TableField("JS_WORK_ACHIEVEMENT_FORMS")
    private String jsWorkAchievementForms;
    /**
     * 潜在能力考核材料
     */
    @TableField("LATENT_ABILITY_FORMS")
    private String latentAbilityForms;
    /**
     * 获奖证书及成果证明材料
     */
    @TableField("CERTIFICATES_FORMS")
    private String certificatesForms;

    /**
     * 特长
     */
    @TableField("exam_room")
    private String examRoom;

    /**
     * 特长
     */
    @TableField("seat_no")
    private String seatNo;
    
    @TableField("BUS_LINE")
    private String busLine;//乘车路线
    
    @TableField("EXAM_POINT")
    private String examPoint;//考试地点
    
    @TableField("EXAM_TIME")
    private String examTime;//考试时间 例：2024年5月12日 8:30-10:30

    public String getHealthyCode() {
        return healthyCode;
    }

    public void setHealthyCode(String healthyCode) {
        this.healthyCode = healthyCode;
    }

    public String getTripCard() {
        return tripCard;
    }

    public void setTripCard(String tripCard) {
        this.tripCard = tripCard;
    }

    public String getConditionId() {
        return conditionId;
    }

    public void setConditionId(String conditionId) {
        this.conditionId = conditionId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBmbatchId() {
        return bmbatchId;
    }

    public void setBmbatchId(String bmbatchId) {
        this.bmbatchId = bmbatchId;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public Ksly getKsly() {
        return ksly;
    }

    public void setKsly(Ksly ksly) {
        this.ksly = ksly;
    }

    public Date getWorkTime() {
        return workTime;
    }

    public void setWorkTime(Date workTime) {
        this.workTime = workTime;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getOldCertiProfession() {
        return oldCertiProfession;
    }

    public void setOldCertiProfession(String oldCertiProfession) {
        this.oldCertiProfession = oldCertiProfession;
    }

    public TechLevel getOldTechLevel() {
        return oldTechLevel;
    }

    public void setOldTechLevel(TechLevel oldTechLevel) {
        this.oldTechLevel = oldTechLevel;
    }

    public Date getOldTechLevelTime() {
        return oldTechLevelTime;
    }

    public void setOldTechLevelTime(Date oldTechLevelTime) {
        this.oldTechLevelTime = oldTechLevelTime;
    }

    public String getOldTechCertiNum() {
        return oldTechCertiNum;
    }

    public void setOldTechCertiNum(String oldTechCertiNum) {
        this.oldTechCertiNum = oldTechCertiNum;
    }

    public String getIndustryId() {
        return industryId;
    }

    public void setIndustryId(String industryId) {
        this.industryId = industryId;
    }

    public String getProfessionId() {
        return professionId;
    }

    public void setProfessionId(String professionId) {
        this.professionId = professionId;
    }

    public String getDirectionId() {
        return directionId;
    }

    public void setDirectionId(String directionId) {
        this.directionId = directionId;
    }

    public TechLevel getApplyTechLevel() {
        return applyTechLevel;
    }

    public void setApplyTechLevel(TechLevel applyTechLevel) {
        this.applyTechLevel = applyTechLevel;
    }

    public String getWorkYears() {
        return workYears;
    }

    public void setWorkYears(String workYears) {
        this.workYears = workYears;
    }

    public Date getSaveTime() {
        return saveTime;
    }

    public void setSaveTime(Date saveTime) {
        this.saveTime = saveTime;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public ZyjdBmStatus getStatus() {
        return status;
    }

    public void setStatus(ZyjdBmStatus status) {
        this.status = status;
    }

    public PayStatus getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(PayStatus payStatus) {
        this.payStatus = payStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getApproveAdvice() {
        return approveAdvice;
    }

    public void setApproveAdvice(String approveAdvice) {
        this.approveAdvice = approveAdvice;
    }

    public String getApproveUserId() {
        return approveUserId;
    }

    public void setApproveUserId(String approveUserId) {
        this.approveUserId = approveUserId;
    }

    public String getOldCertiPhoto() {
        return oldCertiPhoto;
    }

    public void setOldCertiPhoto(String oldCertiPhoto) {
        this.oldCertiPhoto = oldCertiPhoto;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getMarkPaydUserId() {
        return markPaydUserId;
    }

    public void setMarkPaydUserId(String markPaydUserId) {
        this.markPaydUserId = markPaydUserId;
    }

    public String getJobYears() {
        return jobYears;
    }

    public void setJobYears(String jobYears) {
        this.jobYears = jobYears;
    }

    public String getCurrentJobTime() {
        return currentJobTime;
    }

    public void setCurrentJobTime(String currentJobTime) {
        this.currentJobTime = currentJobTime;
    }

    public String getTestWay() {
        return testWay;
    }

    public void setTestWay(String testWay) {
        this.testWay = testWay;
    }

    public String getOldCertiName() {
        return oldCertiName;
    }

    public void setOldCertiName(String oldCertiName) {
        this.oldCertiName = oldCertiName;
    }

    public String getZkz() {
        return zkz;
    }

    public void setZkz(String zkz) {
        this.zkz = zkz;
    }

    public Date getZkznoCreateTime() {
        return zkznoCreateTime;
    }

    public void setZkznoCreateTime(Date zkznoCreateTime) {
        this.zkznoCreateTime = zkznoCreateTime;
    }

    public String getCurrentJob() {
        return currentJob;
    }


    public String getMarkRefundUserId() {
        return markRefundUserId;
    }

    public void setMarkRefundUserId(String markRefundUserId) {
        this.markRefundUserId = markRefundUserId;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public void setCurrentJob(String currentJob) {
        this.currentJob = currentJob;
    }

    public Education getFullEducation() {
        return fullEducation;
    }

    public void setFullEducation(Education fullEducation) {
        this.fullEducation = fullEducation;
    }

    public String getFullSpecialty() {
        return fullSpecialty;
    }

    public void setFullSpecialty(String fullSpecialty) {
        this.fullSpecialty = fullSpecialty;
    }

    public Date getFullEduTime() {
        return fullEduTime;
    }

    public void setFullEduTime(Date fullEduTime) {
        this.fullEduTime = fullEduTime;
    }

    public Education getJobEducation() {
        return jobEducation;
    }

    public void setJobEducation(Education jobEducation) {
        this.jobEducation = jobEducation;
    }

    public String getJobSpecialty() {
        return jobSpecialty;
    }

    public void setJobSpecialty(String jobSpecialty) {
        this.jobSpecialty = jobSpecialty;
    }

    public Date getJobEduTime() {
        return jobEduTime;
    }

    public void setJobEduTime(Date jobEduTime) {
        this.jobEduTime = jobEduTime;
    }

    public String getArticleTital() {
        return articleTital;
    }

    public void setArticleTital(String articleTital) {
        this.articleTital = articleTital;
    }

    public String getDdzz() {
        return ddzz;
    }

    public void setDdzz(String ddzz) {
        this.ddzz = ddzz;
    }

    public String getFbqk() {
        return fbqk;
    }

    public void setFbqk(String fbqk) {
        this.fbqk = fbqk;
    }

    public String getNd() {
        return nd;
    }

    public void setNd(String nd) {
        this.nd = nd;
    }

    public String getQh() {
        return qh;
    }

    public void setQh(String qh) {
        this.qh = qh;
    }

    public String getIsArticlePublish() {
        return isArticlePublish;
    }

    public void setIsArticlePublish(String isArticlePublish) {
        this.isArticlePublish = isArticlePublish;
    }

    public String getArticlePhotoUrl() {
        return articlePhotoUrl;
    }

    public void setArticlePhotoUrl(String articlePhotoUrl) {
        this.articlePhotoUrl = articlePhotoUrl;
    }

    public String getZyjsrzzgzs() {
        return zyjsrzzgzs;
    }

    public void setZyjsrzzgzs(String zyjsrzzgzs) {
        this.zyjsrzzgzs = zyjsrzzgzs;
    }

    public String getYqrbmb() {
        return yqrbmb;
    }

    public void setYqrbmb(String yqrbmb) {
        this.yqrbmb = yqrbmb;
    }

    public String getXrzyjszwpw() {
        return xrzyjszwpw;
    }

    public void setXrzyjszwpw(String xrzyjszwpw) {
        this.xrzyjszwpw = xrzyjszwpw;
    }

    public BmType getBmlx() {
        return bmlx;
    }

    public void setBmlx(BmType bmlx) {
        this.bmlx = bmlx;
    }

    public Pxfs getPxfs() {
        return pxfs;
    }

    public void setPxfs(Pxfs pxfs) {
        this.pxfs = pxfs;
    }

    public String getGznxzm() {
        return gznxzm;
    }

    public void setGznxzm(String gznxzm) {
        this.gznxzm = gznxzm;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getTaxpayerNumber() {
        return taxpayerNumber;
    }

    public void setTaxpayerNumber(String taxpayerNumber) {
        this.taxpayerNumber = taxpayerNumber;
    }

    public String getRecommendCode() {
        return recommendCode;
    }

    public void setRecommendCode(String recommendCode) {
        this.recommendCode = recommendCode;
    }

    public String getCommitment() {
        return commitment;
    }

    public void setCommitment(String commitment) {
        this.commitment = commitment;
    }

    public String getNewCertificate() {
        return newCertificate;
    }

    public void setNewCertificate(String newCertificate) {
        this.newCertificate = newCertificate;
    }

    public String getCertiPost() {
        return certiPost;
    }

    public void setCertiPost(String certiPost) {
        this.certiPost = certiPost;
    }

    public String getPerformance() {
        return performance;
    }

    public void setPerformance(String performance) {
        this.performance = performance;
    }

    public String getProfessionYears() {
        return professionYears;
    }

    public void setProfessionYears(String professionYears) {
        this.professionYears = professionYears;
    }

    public String getIsRec() {
        return isRec;
    }

    public void setIsRec(String isRec) {
        this.isRec = isRec;
    }

    public String getRecCode() {
        return recCode;
    }

    public void setRecCode(String recCode) {
        this.recCode = recCode;
    }

    public String getRegionalismCode() {
        return regionalismCode;
    }

    public void setRegionalismCode(String regionalismCode) {
        this.regionalismCode = regionalismCode;
    }

    public String getXb() {
        return xb;
    }

    public void setXb(String xb) {
        this.xb = xb;
    }

    public String getCertiCategory() {
        return certiCategory;
    }

    public void setCertiCategory(String certiCategory) {
        this.certiCategory = certiCategory;
    }

    public String getXl() {
        return xl;
    }

    public void setXl(String xl) {
        this.xl = xl;
    }

    public String getXw() {
        return xw;
    }

    public void setXw(String xw) {
        this.xw = xw;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getUnitNature() {
        return unitNature;
    }

    public void setUnitNature(String unitNature) {
        this.unitNature = unitNature;
    }

    public String getIsProvideJxjy() {
        return isProvideJxjy;
    }

    public void setIsProvideJxjy(String isProvideJxjy) {
        this.isProvideJxjy = isProvideJxjy;
    }

    public String getObtainCertiCategory() {
        return obtainCertiCategory;
    }

    public void setObtainCertiCategory(String obtainCertiCategory) {
        this.obtainCertiCategory = obtainCertiCategory;
    }

    public String getZcSeries() {
        return zcSeries;
    }

    public void setZcSeries(String zcSeries) {
        this.zcSeries = zcSeries;
    }

    public String getZyQualificationName() {
        return zyQualificationName;
    }

    public void setZyQualificationName(String zyQualificationName) {
        this.zyQualificationName = zyQualificationName;
    }

    public String getMz() {
        return mz;
    }

    public void setMz(String mz) {
        this.mz = mz;
    }

    public String getZyQualificationLevel() {
        return zyQualificationLevel;
    }

    public void setZyQualificationLevel(String zyQualificationLevel) {
        this.zyQualificationLevel = zyQualificationLevel;
    }

    public String getZcLevel() {
        return zcLevel;
    }

	public void setZcLevel(String zcLevel) {
		this.zcLevel = zcLevel;
	}

	public String getReceipt() {
		return receipt;
	}

	public void setReceipt(String receipt) {
		this.receipt = receipt;
	}

	public Double getExamScore() {
		return examScore;
	}

	public void setExamScore(Double examScore) {
		this.examScore = examScore;
	}

	public Double getSkillScore() {
		return skillScore;
	}

	public void setSkillScore(Double skillScore) {
		this.skillScore = skillScore;
	}

	public Double getJobScore() {
		return jobScore;
	}

	public void setJobScore(Double jobScore) {
		this.jobScore = jobScore;
	}

	public Double getPotentialScore() {
		return potentialScore;
	}

	public void setPotentialScore(Double potentialScore) {
		this.potentialScore = potentialScore;
	}

	public Double getSynthesizeScore() {
		return synthesizeScore;
	}

	public void setSynthesizeScore(Double synthesizeScore) {
		this.synthesizeScore = synthesizeScore;
	}

	public String getScore() {
		return score;
	}

	public void setScore(String score) {
		this.score = score;
	}

	public String getReward() {
		return reward;
	}

	public void setReward(String reward) {
		this.reward = reward;
	}

	public String getAchievement() {
		return achievement;
	}

	public void setAchievement(String achievement) {
		this.achievement = achievement;
	}

	public String getWorkExperience() {
		return workExperience;
	}

	public void setWorkExperience(String workExperience) {
		this.workExperience = workExperience;
	}

	public Date getJsTime() {
		return jsTime;
	}

	public void setJsTime(Date jsTime) {
		this.jsTime = jsTime;
	}

	public String getSpeciality() {
		return speciality;
	}

	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAppraisalForms() {
        return appraisalForms;
    }

    public void setAppraisalForms(String appraisalForms) {
        this.appraisalForms = appraisalForms;
    }

    public String getTechnicalSummaryForms() {
        return technicalSummaryForms;
    }

    public void setTechnicalSummaryForms(String technicalSummaryForms) {
        this.technicalSummaryForms = technicalSummaryForms;
    }

    public String getThesisForms() {
        return thesisForms;
    }

    public void setThesisForms(String thesisForms) {
        this.thesisForms = thesisForms;
    }

    public String getJsWorkAchievementForms() {
        return jsWorkAchievementForms;
    }

    public void setJsWorkAchievementForms(String jsWorkAchievementForms) {
        this.jsWorkAchievementForms = jsWorkAchievementForms;
    }

    public String getLatentAbilityForms() {
        return latentAbilityForms;
    }

    public void setLatentAbilityForms(String latentAbilityForms) {
        this.latentAbilityForms = latentAbilityForms;
    }

    public String getCertificatesForms() {
        return certificatesForms;
    }

    public void setCertificatesForms(String certificatesForms) {
        this.certificatesForms = certificatesForms;
    }

    public Double getThesisScore() {
        return thesisScore;
    }

    public void setThesisScore(Double thesisScore) {
        this.thesisScore = thesisScore;
    }

    public String getWorkExperienceDl() {
        return workExperienceDl;
    }

    public void setWorkExperienceDl(String workExperienceDl) {
        this.workExperienceDl = workExperienceDl;
    }

    public String getTrainingExperience() {
        return trainingExperience;
    }

    public void setTrainingExperience(String trainingExperience) {
        this.trainingExperience = trainingExperience;
    }

    public String getJsWorkAchievement() {
        return jsWorkAchievement;
    }

    public void setJsWorkAchievement(String jsWorkAchievement) {
        this.jsWorkAchievement = jsWorkAchievement;
    }

    public String getSkillSpeciality() {
        return skillSpeciality;
    }

    public void setSkillSpeciality(String skillSpeciality) {
        this.skillSpeciality = skillSpeciality;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getOpus() {
        return opus;
    }

    public void setOpus(String opus) {
        this.opus = opus;
    }

    public String getCompetition() {
        return competition;
    }

    public void setCompetition(String competition) {
        this.competition = competition;
    }

    public String getBearContent() {
        return bearContent;
    }

    public void setBearContent(String bearContent) {
        this.bearContent = bearContent;
    }

    public String getHonor() {
        return honor;
    }

    public void setHonor(String honor) {
        this.honor = honor;
    }

    public String getTechnicalSummary() {
        return technicalSummary;
    }

    public void setTechnicalSummary(String technicalSummary) {
        this.technicalSummary = technicalSummary;
    }

    public String getThesis() {
        return thesis;
    }

    public void setThesis(String thesis) {
        this.thesis = thesis;
    }

	public String getInvoiceUrl() {
		return invoiceUrl;
	}

	public void setInvoiceUrl(String invoiceUrl) {
		this.invoiceUrl = invoiceUrl;
	}

    public String getIsReevaluation() {
        return isReevaluation;
    }

    public void setIsReevaluation(String isReevaluation) {
        this.isReevaluation = isReevaluation;
    }

    public String getIsExceptional() {
        return isExceptional;
    }

    public void setIsExceptional(String isExceptional) {
        this.isExceptional = isExceptional;
    }

    public String getExamRoom() {
        return examRoom;
    }

    public void setExamRoom(String examRoom) {
        this.examRoom = examRoom;
    }

    public String getSeatNo() {
        return seatNo;
    }

    public void setSeatNo(String seatNo) {
        this.seatNo = seatNo;
    }

    public String getBusLine() {
        return busLine;
    }

    public void setBusLine(String busLine) {
        this.busLine = busLine;
    }

    public String getExamPoint() {
        return examPoint;
    }

    public void setExamPoint(String examPoint) {
        this.examPoint = examPoint;
    }

    public String getExamTime() {
        return examTime;
    }

    public void setExamTime(String examTime) {
        this.examTime = examTime;
    }
}
