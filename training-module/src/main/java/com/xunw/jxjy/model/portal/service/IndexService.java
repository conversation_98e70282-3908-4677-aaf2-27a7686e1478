package com.xunw.jxjy.model.portal.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.common.Chapter;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.Lesson;
import com.xunw.jxjy.model.enums.LearningType;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Sjlx;
import com.xunw.jxjy.model.enums.Sjxslx;
import com.xunw.jxjy.model.enums.Sjzt;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.Courseware;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.inf.mapper.CoursewareMapper;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.learning.entity.StudentLearningScore;
import com.xunw.jxjy.model.personal.service.ZypxStudentCorePaperService;
import com.xunw.jxjy.model.personal.service.ZypxStudentPaperService;
import com.xunw.jxjy.model.portal.mapper.IndexMapper;
import com.xunw.jxjy.model.sys.entity.NoticeCategory;
import com.xunw.jxjy.model.sys.mapper.NoticeCategoryMapper;
import com.xunw.jxjy.model.tk.entity.QuestionDB2CourseEntity;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.mapper.QuestionDBEntityMapper;
import com.xunw.jxjy.model.tk.service.QuestionDB2CourseService;
import com.xunw.jxjy.model.wdxx.entity.MyPracticeRecord;
import com.xunw.jxjy.model.wdxx.mapper.MyPracticeRecordMapper;
import com.xunw.jxjy.model.zyjd.entity.ZcReviewMark;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcGroupUserMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZcReviewMarkMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmBatchMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmCourseMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxStudentCertiMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxStudentLearningScoreMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;
import com.xunw.jxjy.model.zypx.service.ZypxBmService;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.utils.ModelHelper;


@Service
public class IndexService {

    @Autowired
    private IndexMapper indexMapper;
    @Autowired
    private CourseMapper courseMapper;
    @Autowired
    private NoticeCategoryMapper noticeCategoryMapper;
    @Autowired
    private QuestionDBEntityMapper questionDBEntityMapper;
    @Autowired
    private MyPracticeRecordMapper myPracticeRecordMapper;
    @Autowired
    private ZypxExamPaperMapper examPaperMapper;
    @Autowired
    private ZypxStudentCorePaperService studentCorePaperService;
    @Autowired
    private ZypxTypeService typeService;
    @Autowired
    private ZypxBmService bmService;
    @Autowired
    private ZypxStudentCertiMapper certiMapper;
    @Autowired
    private CoursewareMapper coursewareMapper;
    @Autowired
    private ZypxStudentLearningScoreMapper studentLearningScoreMapper;
    @Autowired
    private ZypxXmMapper xmMapper;
    @Autowired
    private ZypxBmCourseMapper bmCourseMapper;
    @Autowired
    private QuestionDB2CourseService question2CourseService;
    @Autowired
    private ZypxStudentPaperService studentPaperService;
    @Autowired
    private ZyjdProfessionMapper zyjdProfessionMapper;
    @Autowired
    private ZyjdBmMapper zyjdBmMapper;
    @Autowired
    private ZyjdBmBatchMapper zyjdBmBatchMapper;
    @Autowired
    private ZcGroupMapper zcGroupMapper;
    @Autowired
    private ZcGroupUserMapper zcGroupUserMapper;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private ZcReviewMarkMapper zcReviewMarkMapper;

    public List<ZypxType> getTopTypeList(String hostOrgId) {
        List<ZypxType> list  = typeService.getTopType(hostOrgId);
        return list;
    }

    public List<Map<String, Object>> getNoticeCategory(String hostOrgId) {
        List<Map<String, Object>> list = indexMapper.getNoticeCategory(hostOrgId);
        return list;
    }

    public Page getNotice(Map<String, Object> condition,
                          int pageNo, int pageSize) {
        Page page = new Page(pageNo, pageSize);
        List<Map<String, Object>> list = indexMapper.getNotice(condition, page);
        page.setRecords(list);
        return page;
    }

    /**
     *	根据课程id得到学习人数
     */
    public Integer getStudyCount(String courseId) {
        return studentLearningScoreMapper.selectCount(
                new EntityWrapper<StudentLearningScore>()
                        .eq("learning_type", LearningType.KJ.getValue())
                        .eq("course_id",courseId));
    }

    /**
     *	根据课程id得到学习人数
     */
    public Integer getZbStudyCount(String courseId) {
        return studentLearningScoreMapper.selectCount(
                new EntityWrapper<StudentLearningScore>()
                        .eq("learning_type", LearningType.ZB.getValue())
                        .eq("course_id",courseId));
    }

    /**
     * 根据课程id获取报名人数
     */
    public Integer getBmCourseCountByCourseId(String courseId){
        return bmCourseMapper.getBmCourseCountByCourseId(courseId);
    }

    public List<Map<String, Object>> getTopType(String hostOrgId, TypeCategory category) {
        return indexMapper.getTopType(hostOrgId, category);
    }

    public List<Map<String, Object>> getChildType(String parentId) {
        return indexMapper.getChildType(parentId);
    }

	public List<Map<String, Object>> getSecondLevelType(String hostOrgId, TypeCategory category){
		 return indexMapper.getSecondLevelType(hostOrgId, category);
	}

    /**
     * 获取第三级分类
     * @param hostOrgId
     * @param category
     * @return
     */
    public List<Map<String, Object>> getThirdLevelType(String hostOrgId, TypeCategory category){
		 return indexMapper.getThirdLevelType(hostOrgId, category);
	}



    public ZypxType getZypxTypeById(String id) {
        return typeService.selectById(id);
    }

    public Page<Map<String, Object>> getXmList(Map<String, Object> condition, int pageNo, int pageSize) {
        Page page = new Page(pageNo, pageSize);
        List<Map<String, Object>> list = indexMapper.getXmList(condition, page);
        page.setRecords(list);
        return page;
    }

    /**
     * 门户查询当前开放的热门项目
     */
    public List<Map<String, Object>> getHotXm(String hostOrgId){
        return xmMapper.getHotXm(hostOrgId, StringUtils.EMPTY);
    }

    /**
     * 查询当前开放的热门项目
     */
    public List<Map<String, Object>> getHotXm( String hostOrgId, String studentId){
        return xmMapper.getHotXm(hostOrgId, studentId);
    }

    public Page<Map<String, Object>> getCourse(Map<String, Object> condition, int pageNo, int pageSize) {
        Page page = new Page(pageNo, pageSize);
        List<Map<String, Object>> list = indexMapper.getCourse(condition, page);
        page.setRecords(list);
        return page;
    }

    /**
     * 首页培训项目下课程设置
     */
    public List<Map<String, Object>> getXmCourseSetting(String xmId) {
        return indexMapper.getXmCourseSetting(xmId);
    }

    /**
     * 门户师资列表
     */
    public Page<Map<String, Object>> getPortalTeacher(Map<String, Object> condition, int pageNo, int pageSize) {
        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
        List<Map<String, Object>> list = indexMapper.getPortalTeacher(condition, page);
        page.setRecords(list);
        return page;
    }

    public NoticeCategory getNoticeCategoryById(String id) {
        return noticeCategoryMapper.selectById(id);
    }

    public List<Map<String, Object>> getCoursewareMaterialByXmId(String xmId) {
        return indexMapper.getCoursewareMaterialByXmId(xmId);
    }

    @Transactional
    public Paper makePaperByQuestionDB(String dbId, String studentId) {
        QuestionDBEntity subject = questionDBEntityMapper.selectById(dbId);
        QuestionDB2CourseEntity question2CourseEntity = question2CourseService.getDetailsByDbId(dbId);
        Course course = courseMapper.selectById(question2CourseEntity.getCourseId());
        Paper paper = null;
        EntityWrapper<MyPracticeRecord> wrapper = new EntityWrapper<MyPracticeRecord>();
        wrapper.eq("student_id", studentId);
        wrapper.eq("db_id", dbId);
        List<MyPracticeRecord> practiceRecords = myPracticeRecordMapper.selectList(wrapper);
        String paperId = null;
        if (practiceRecords.size() > 0) {
            paperId = practiceRecords.get(0).getPaperId();
        } else {
            //创建试卷
            paper = studentPaperService.makerPaperByQuestionDb(dbId, 1);
            //组卷
            ExamPaper examPaper = new ExamPaper();
            examPaper.setId( BaseUtil.generateId2()+ "-paper");
            examPaper.setXmId(Constants.PERSONAL_XM_ID);
            examPaper.setName(subject.getName());
            examPaper.setCourseId(course.getId());
            examPaper.setCategory(PaperCategory.WDLX);
            examPaper.setStatus(Sjzt.ZCKY);
            examPaper.setStartTime(new Date());

            examPaper.setEndTime(new Date(System.currentTimeMillis() + 365 * 24 * 3600 * 1000));
            examPaper.setIsShowAnswer(Constants.YES);
            examPaper.setPaperShowType(Sjxslx.ZJZS);
            examPaper.setPaperType(Sjlx.PT);
            examPaper.setQuesSortType(Stplsx.ZC);
            examPaper.setTotalScore(paper.getTotalScore());
            examPaper.setPassedScore(Double.valueOf(examPaper.getTotalScore() * 0.6).intValue());
            Date createTime = new Date();
            examPaper.setCreateTime(createTime);

            //完善信息
            paper.setId(examPaper.getId());
            paper.setName(examPaper.getName());
            paper.setStatus(examPaper.getStatus());
            paper.setStartTime(examPaper.getStartTime());
            paper.setEndTime(examPaper.getEndTime());
            paper.setDuration(examPaper.getDuration());
            paper.setScoreTime(examPaper.getScoreTime());
            paper.setTotalScore(examPaper.getTotalScore());
            paper.setPassedScore(examPaper.getPassedScore());
            paper.setQuesSortType(examPaper.getQuesSortType());
            paper.setCourseId(question2CourseEntity.getCourseId());
            paper.setPaperType(examPaper.getPaperType());
            paper.setRemark(examPaper.getRemark());
            paper.setIsShowAnswer(examPaper.getIsShowAnswer());
            paper.setPaperShowType(examPaper.getPaperShowType());

            String xml = ModelHelper.formatObject(paper);
            examPaper.setData(xml);

            examPaperMapper.insert(examPaper);

            paperId = examPaper.getId();

            MyPracticeRecord myPracticeRecord = new MyPracticeRecord();
            myPracticeRecord.setId(BaseUtil.generateId2());
            myPracticeRecord.setStudentId(studentId);
            myPracticeRecord.setDbId(dbId);
            myPracticeRecord.setPaperId(paper.getId());
            myPracticeRecordMapper.insert(myPracticeRecord);
        }
        return studentCorePaperService.getPaper(paperId);
    }

    /**
     * 门户轮播图banner
     */
    public Page<Map<String, Object>> getBanner(Map<String, Object> condition, int pageNo, int pageSize) {
        Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);
        List<Map<String, Object>> list = indexMapper.getBanner(condition, page);
        page.setRecords(list);
        return page;
    }

    public Courselearn getCourselearnByCourseId(String courseId) {
        if (StringUtils.isEmpty(courseId)) {
            return null;
        }
        EntityWrapper<Courseware> wrapper = new EntityWrapper();
        wrapper.eq("course_id", courseId);
        wrapper.eq("status", Zt.OK);
        List<Courseware> coursewares = coursewareMapper.selectList(wrapper);
        if (coursewares.size() > 0) {
            Courseware courseware = coursewares.get(0);
            Courselearn courselearn = ModelHelper.convertObject(courseware.getContent());
            //格式化显示章节序号、课时学号
            if(courselearn!=null){
                for (int i = 0; i < courselearn.getChapters().size(); i++) {
                    Chapter chapter = courselearn.getChapters().get(i);
                    chapter.setName("第" + BaseUtil.toCNLowerNum(i + 1) + "章 " + chapter.getName());
                    for (int j = 0; j < chapter.getLessons().size(); j++) {
                        Lesson lesson = chapter.getLessons().get(j);
                        lesson.setName("【第" + BaseUtil.toCNLowerNum(j + 1) + "课时】 " + lesson.getName());
                    }
                }
            }
            return courselearn;
        } else {
            return null;
        }
    }

    public Courselearn getCourselearnById(String kjId) {
        if (StringUtils.isEmpty(kjId)) {
            return null;
        }
        Courseware courseware = coursewareMapper.selectById(kjId);
        Courselearn courselearn = ModelHelper.convertObject(courseware.getContent());
        if (courselearn == null) {
            return null;
        }
        //格式化显示章节序号、课时学号
        for (int i = 0; i < courselearn.getChapters().size(); i++) {
            Chapter chapter = courselearn.getChapters().get(i);
            chapter.setName("第" + BaseUtil.toCNLowerNum(i + 1) + "章 " + chapter.getName());
            for (int j = 0; j < chapter.getLessons().size(); j++) {
                Lesson lesson = chapter.getLessons().get(j);
                lesson.setName("【第" + BaseUtil.toCNLowerNum(j + 1) + "课时】 " + lesson.getName());
            }
        }
        return courselearn;
    }

    public Integer selectCountByXmId(String xmId) {
        return bmService.selectCountByXmId(xmId);
    }

    public List<Map<String, Object>> getCertiByStudent(String certiNum, String sfzh,String name,String hostOrgId) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("certiNum", certiNum);
        params.put("hostOrgId", hostOrgId);
        params.put("sfzh", sfzh);
        params.put("name", name);
        return certiMapper.getCertiByStudent(params);
    }

    public Page<Map<String, Object>> getNoticeByNew() {
        Page page = new Page(1, 9);
        List<Map<String, Object>> list = indexMapper.getNoticeByNew(page);
        page.setRecords(list);
        return page;
    }

    public Page getProfessions(Map<String, Object> condition, Integer pageNum, Integer pageSize) {
        Page page = new Page(pageNum, pageSize);
        page.setRecords(zyjdProfessionMapper.list(condition, page));
        return page;
    }

    //答辩评审首页
    public Map<String, Object> getZcpsIndex(String studentId) {
        Map<String, Object> result = new HashMap<>();
        List<ZyjdBm> bms = zyjdBmMapper.getDetailsByStudentId(studentId);
        if (!bms.isEmpty()) {
            ZyjdBm zyjdBm = bms.get(0);
            ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(zyjdBm.getBmbatchId());
            List<Map<String, Object>> list = zcGroupMapper.getGroupStudentsByBatchId(zyjdBmBatch.getId(), "1");
            Optional<Map<String, Object>> groupStudentP = list.stream().filter(x -> Objects.equals(x.get("studentId").toString(), studentId)).findFirst();
            String teacherNames = "";
            Map<String, Object> groupStudent = new HashMap<>();
            String groupId = "";
            if (groupStudentP.isPresent()) {
                groupStudent = groupStudentP.get();
                groupId = groupStudent.get("groupId").toString();
                teacherNames = zcGroupUserMapper.getTeacherNamesByGroupId(groupId);
            }
            result.put("category", zyjdBm.getIndustryId());
            result.put("studentInfo", studentInfoMapper.getByStudentId(studentId));
            result.put("professionName", zyjdProfessionMapper.selectById(zyjdBm.getProfessionId()).getName());
            result.put("techLevel", zyjdBm.getApplyTechLevel().getDesc());
            result.put("groupStudent", groupStudent);
            result.put("teachers", teacherNames);
            result.put("bmInfo", zyjdBm);
            List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().eq("bm_id", zyjdBm.getId()));
            result.put("score", zcReviewMarks.stream().map(x->x.getScore().toString()).collect(Collectors.joining(",")));
            result.put("bmBatch", zyjdBmBatch);
            result.put("group", zcGroupMapper.selectById(groupId));
        }
        return result;
    }
    
    //答辩评审
    public Map<String, Object> getZcps(String studentId) {
        Map<String, Object> result = new HashMap<>();
        List<ZyjdBm> bms = zyjdBmMapper.getDetailsByStudentIdAndType(studentId, ZyjdBmBatchType.ZCPS.name());
        if (CollectionUtils.isNotEmpty(bms)) {
            ZyjdBm zyjdBm = bms.get(0);
            ZyjdBmBatch zyjdBmBatch = zyjdBmBatchMapper.selectById(zyjdBm.getBmbatchId());
            List<Map<String, Object>> list = zcGroupMapper.getGroupStudentsByBatchId(zyjdBmBatch.getId(), "1");
            Optional<Map<String, Object>> groupStudentP = list.stream().filter(x -> Objects.equals(x.get("studentId").toString(), studentId)).findFirst();
            String teacherNames = "";
            Map<String, Object> groupStudent = new HashMap<>();
            String groupId = "";
            if (groupStudentP.isPresent()) {
                groupStudent = groupStudentP.get();
                groupId = groupStudent.get("groupId").toString();
                teacherNames = zcGroupUserMapper.getTeacherNamesByGroupId(groupId);
            }
            result.put("category", zyjdBm.getIndustryId());
            result.put("studentInfo", studentInfoMapper.getByStudentId(studentId));
            result.put("professionName", zyjdProfessionMapper.selectById(zyjdBm.getProfessionId()).getName());
            result.put("techLevel", zyjdBm.getApplyTechLevel().getDesc());
            result.put("groupStudent", groupStudent);
            result.put("teachers", teacherNames);
            result.put("bmInfo", zyjdBm);
            List<ZcReviewMark> zcReviewMarks = zcReviewMarkMapper.selectList(new EntityWrapper<ZcReviewMark>().eq("bm_id", zyjdBm.getId()));
            result.put("score", zcReviewMarks.stream().map(x->x.getScore().toString()).collect(Collectors.joining(",")));
            result.put("bmBatch", zyjdBmBatch);
            result.put("group", zcGroupMapper.selectById(groupId));
        }
        return result;
    }
    
}