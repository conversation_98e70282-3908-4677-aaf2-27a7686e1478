package com.xunw.jxjy.model.zypx.params;

import com.xunw.jxjy.model.core.BaseQueryParams;

public class XmszRecordQueryParams extends BaseQueryParams {

	private String hostOrgId;

	private String xmId;

	private Integer type;

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}
