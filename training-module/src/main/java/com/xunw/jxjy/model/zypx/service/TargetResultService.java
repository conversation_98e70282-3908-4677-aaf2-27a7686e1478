package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.entity.TargetResult;
import com.xunw.jxjy.model.zypx.entity.TargetSetting;
import com.xunw.jxjy.model.zypx.mapper.TargetResultMapper;
import com.xunw.jxjy.model.zypx.mapper.TargetSettingMapper;
import com.xunw.jxjy.model.zypx.params.TargetResultQueryParams;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TargetResultService extends BaseCRUDService<TargetResultMapper, TargetResult>{

    @Autowired
    private TargetSettingMapper settingMapper;

    /**
     * 培训项目评分-添加
     *
     * @return
     */

    public Object add(TargetResultQueryParams params, String userId) {
        EntityWrapper<TargetSetting> wrapper = new EntityWrapper<TargetSetting>();
        wrapper.eq("xm_id", params.getXmId());
        wrapper.eq("target_id", params.getTargetId());
        //获取项目指标
        List<TargetSetting> settingList = settingMapper.selectList(wrapper);
        if (settingList == null || settingList.size() < 1) {
            throw BizException.withMessage("项目指标不存在");
        }
        TargetSetting setting = settingList.get(0);
        EntityWrapper<TargetResult> objectEntityWrapper = new EntityWrapper<>();
        objectEntityWrapper.eq("setting_id",setting.getId());
        objectEntityWrapper.eq("student_id",userId);
        List<TargetResult> targetResults = mapper.selectList(objectEntityWrapper);
        TargetResult targetResult = new TargetResult();
        targetResult.setStarCount(params.getScore());
        targetResult.setTime(new Date());
        if (CollectionUtils.isEmpty(targetResults)){
            targetResult.setId(BaseUtil.generateId2());
            targetResult.setSettingId(setting.getId());
            targetResult.setStudentId(userId);
            mapper.insert(targetResult);
        } else {
            targetResult.setId(targetResults.get(0).getId());
            mapper.updateById(targetResult);
        }
        return true;
    }
    //根据项目id获取项目的评价指标
    public List<Map<String, Object>> getTargetByXmId(String xmId, String studentId) {
        return mapper.getTargetByXmId(xmId,studentId);
    }

    public Page getCourseTargetResult(String xmId, String courseId, String keyword, Page page) {
        return page.setRecords(mapper.getCourseTargetResult(xmId, courseId, keyword, page));
    }

    //获取课程评价学员详细记录
    public List<Map<String, Object>> getCourseTargetResultByStudent(String xmId, String courseId, String studentId) {
        return mapper.getCourseTargetResultByStudent(xmId, courseId, studentId);
    }

    //导出课程评价学员详细记录
    public void exportCourseTargetResultByStudent(String xmId, String courseId, OutputStream os) throws IOException, WriteException {
        List<Map<String, Object>> list = mapper.exportCourseTargetResultByStudent(xmId, courseId);
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("课程评价学员详细记录表", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "学员姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "学员手机号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "评价指标", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 30);
            ws.addCell(new Label(i, row, "分数", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "反馈或建议", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 30);
        }
        row = 1;
        int i = 1;
        Map<String, List<Map<String, Object>>> listMap = list.stream().collect(Collectors.groupingBy(x -> BaseUtil.getStringValueFromMap(x, "mobile")));
        for (Map<String, Object> map : list) {
            int col = 0;
            int mergeCount = listMap.get(BaseUtil.getStringValueFromMap(map, "mobile")).size();
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
            if (row % mergeCount == 1) {
                ws.mergeCells(col, row, col, row + mergeCount - 1);
            }
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
                    OfficeToolExcel.getNormolCell()));
            if (row % mergeCount == 1) {
                ws.mergeCells(col, row, col, row + mergeCount - 1);
            }
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("targetName") != null ? map.get("targetName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("starCount") != null ? map.get("starCount") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("remark") != null ? map.get("remark") : ""),
                    OfficeToolExcel.getNormolCell()));
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }
}
