package com.xunw.jxjy.model.exam.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 *  试卷最高得分表
 */
@TableName("biz_exam_paper_max_score")
public class ExamPaperMaxScore implements Serializable {

	private static final long serialVersionUID = 2501088414696900223L;
	
	//主键id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;
    
    //试卷id
    @TableField("paper_id")
    private String paperId;

    //考生用户id
    @TableField("student_id")
    private String studentId;
    
    //最高得分
    @TableField("max_score")
    private Integer maxScore;
    
    //最高分对应的考试记录ID,此考试记录可以是biz_exam_dada中的记录，也可以是biz_exam_data_history中的记录
    @TableField("examdata_id")
    private String examDataId;
    
    //最后更新时间
    @TableField("time")
    private Date time;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPaperId() {
		return paperId;
	}

	public void setPaperId(String paperId) {
		this.paperId = paperId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public Integer getMaxScore() {
		return maxScore;
	}

	public void setMaxScore(Integer maxScore) {
		this.maxScore = maxScore;
	}

	public String getExamDataId() {
		return examDataId;
	}

	public void setExamDataId(String examDataId) {
		this.examDataId = examDataId;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}
}
