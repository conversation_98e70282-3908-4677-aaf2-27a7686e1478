package com.xunw.jxjy.model.sys.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.UserResource;

/**
 * 用户-信息表
 * 与用户表是一对一的关系，仅存储用户的非重要信息
 */
@TableName("sys_user_info")
public class UserInfo implements Serializable {
	
	private static final long serialVersionUID = 1863546633205455112L;
	
	//主键
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;

	//用户id
	@TableField("user_id")
	private String userId;
	
	//学历
	@TableField("education")
	private Education education;
		
	//岗位
	@TableField("gw")
	private String gw;

	//职务
	@TableField("zw")
	private String zw;

	//职称
	@TableField("zc")
	private String zc;
		
	//人物简介
	@TableField("brief")
	private String brief;

	//用户照片
	@TableField("photo")
	private String photo;
	
	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//修改用户id
	@TableField("updator_id")
	private String updatorId;

	//修改时间
	@TableField("update_time")
	private Date updateTime;
	
	//性别
	@TableField("gender")
	private Gender gender;
	
	//是否外聘 1是  0 否
	@TableField("is_out")
	private String isOut;

	//工作单位
	@TableField("work_unit")
	private String workUnit;

	//研究方向
	@TableField("study_direction")
	private String studyDirection;

	//主讲课程
	@TableField("courses")
	private String courses;

	//办公电话
	@TableField("office_tel")
	private String officeTel;

	//备注
	@TableField("remark")
	private String remark;

	//邮箱
	@TableField("email")
	private String email;

	//是否门户展示
	@TableField("is_show_portal")
	private String isShowPortal;

	//推荐指数
	@TableField("recommend")
	private String recommend;

	//自定义字段1
	@TableField("custom_one")
	private String customOne;

	//自定义字段2
	@TableField("custom_two")
	private String customTwo;

	//自定义字段3
	@TableField("custom_three")
	private String customThree;

	//职称证书扫描件
	@TableField("zc_photo")
	private String zcPhoto;

   	//银行卡号
	@TableField("bank_card_no")
	private String bankCardNo;

	//开户行
	@TableField("bank")
	private String bank;

	//银行卡扫描件
	@TableField("bank_card_photo")
	private String bankCardPhoto;

	//类型id
	@TableField("teacher_type_id")
	private String teacherTypeId;

	@TableField("speciality_type")
	private String specialityType;

	@TableField("profession_ids")
	private String professionIds;

	//校内 所属学院 校外 师资来源 QY企业，GX高校，DZGB党政干部
	@TableField("category")
	private String category;

	public UserInfo() {

	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getZw() {
		return zw;
	}

	public void setZw(String zw) {
		this.zw = zw;
	}

	public String getBrief() {
		return brief;
	}

	public void setBrief(String brief) {
		this.brief = brief;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getZc() {
		return zc;
	}

	public void setZc(String zc) {
		this.zc = zc;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getGw() {
		return gw;
	}

	public void setGw(String gw) {
		this.gw = gw;
	}

	public Education getEducation() {
		return education;
	}

	public void setEducation(Education education) {
		this.education = education;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getStudyDirection() {
		return studyDirection;
	}

	public void setStudyDirection(String studyDirection) {
		this.studyDirection = studyDirection;
	}

	public String getCourses() {
		return courses;
	}

	public void setCourses(String courses) {
		this.courses = courses;
	}

	public String getOfficeTel() {
		return officeTel;
	}

	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIsOut() {
		return isOut;
	}

	public void setIsOut(String isOut) {
		this.isOut = isOut;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getIsShowPortal() {
		return isShowPortal;
	}

	public void setIsShowPortal(String isShowPortal) {
		this.isShowPortal = isShowPortal;
	}

	public String getRecommend() {
		return recommend;
	}

	public void setRecommend(String recommend) {
		this.recommend = recommend;
	}

	public String getCustomOne() {
		return customOne;
	}

	public void setCustomOne(String customOne) {
		this.customOne = customOne;
	}

	public String getCustomTwo() {
		return customTwo;
	}

	public void setCustomTwo(String customTwo) {
		this.customTwo = customTwo;
	}

	public String getCustomThree() {
		return customThree;
	}

	public void setCustomThree(String customThree) {
		this.customThree = customThree;
	}

	public String getZcPhoto() {
		return zcPhoto;
	}

	public void setZcPhoto(String zcPhoto) {
		this.zcPhoto = zcPhoto;
	}

	public String getBankCardNo() {
		return bankCardNo;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public String getBank() {
		return bank;
	}

	public void setBank(String bank) {
		this.bank = bank;
	}

	public String getBankCardPhoto() {
		return bankCardPhoto;
	}

	public void setBankCardPhoto(String bankCardPhoto) {
		this.bankCardPhoto = bankCardPhoto;
	}

	public String getTeacherTypeId() {
		return teacherTypeId;
	}

	public void setTeacherTypeId(String teacherTypeId) {
		this.teacherTypeId = teacherTypeId;
	}

	public String getSpecialityType() {
		return specialityType;
	}

	public void setSpecialityType(String specialityType) {
		this.specialityType = specialityType;
	}

	public String getProfessionIds() {
		return professionIds;
	}

	public void setProfessionIds(String professionIds) {
		this.professionIds = professionIds;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
}
