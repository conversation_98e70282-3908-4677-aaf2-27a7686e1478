package com.xunw.jxjy.model.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;
import java.util.Objects;

/**
 * 账号状态
 */
public enum AnswerType implements IEnum {

    STAR("星级", "0"), SCORE("评分", "1"), ADVICE("意见反馈", "2");

    private String name;

    private String id;

    private AnswerType(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static AnswerType findById(String id) {
        for (AnswerType status : AnswerType.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

	public static AnswerType findByEnumName(String name){
		for (AnswerType zt : AnswerType.values()) {
			if (zt.name().equals(name)) {
				return zt;
			}
		}
		return null;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}



	@Override
	public Serializable getValue() {
		return this.name();
	}
    
}
