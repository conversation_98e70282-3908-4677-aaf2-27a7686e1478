package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021年03月30日
 * 评分指标分类
 */
@TableName("BIZ_TARGET_TYPE")
public class TargetType implements Serializable {
	
	private static final long serialVersionUID = -5249706086244048948L;

	@TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

	//代码
	@TableField("code")
	private String code;

	//名称
	@TableField("name")
	private String name;

	//父id
	@TableField("parent_id")
	private String parentId;

	//排序
	@TableField("sort")
	private Integer sort;

	//是否是课程评价 1是 0否
	@TableField("is_course_target")
	private String isCourseTarget;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	@TableField(exist = false)
	private List<Map<String, Object>> targets;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getIsCourseTarget() {
		return isCourseTarget;
	}

	public void setIsCourseTarget(String isCourseTarget) {
		this.isCourseTarget = isCourseTarget;
	}

	public List<Map<String, Object>> getTargets() {
		return targets;
	}

	public void setTargets(List<Map<String, Object>> targets) {
		this.targets = targets;
	}
}

    