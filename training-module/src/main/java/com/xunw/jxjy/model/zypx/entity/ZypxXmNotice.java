package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.XmNoticeStatus;

/**
 * 职业培训项目公告
 */
@TableName("biz_xm_notice")
public class ZypxXmNotice implements Serializable {

	private static final long serialVersionUID = 3134692456789993799L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 项目id
	@TableField("xm_id")
	private String xmId;

	//标题
	@TableField("title")
	private String title;

	// 内容
	@TableField("content")
	private String content;

	// 发送班级，多个,隔开
	@TableField("class_ids")
	private String classIds;

	// 状态
	@TableField("status")
	private XmNoticeStatus status;

	// 创建用户id
	@TableField("creator_id")
	private String creatorId;

	// 创建时间
	@TableField("create_time")
	private Date createTime;

	// 修改用户id
	@TableField("updator_id")
	private String updatorId;

	// 修改时间
	@TableField("update_time")
	private Date updateTime;

	public ZypxXmNotice() {
		super();
	}

	public ZypxXmNotice(String id, String xmId, String title, String content, String classIds, XmNoticeStatus status, String creatorId, Date createTime) {
		this.id = id;
		this.xmId = xmId;
		this.title = title;
		this.content = content;
		this.classIds = classIds;
		this.status = status;
		this.creatorId = creatorId;
		this.createTime = createTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getClassIds() {
		return classIds;
	}

	public void setClassIds(String classIds) {
		this.classIds = classIds;
	}

	public XmNoticeStatus getStatus() {
		return status;
	}

	public void setStatus(XmNoticeStatus status) {
		this.status = status;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
}