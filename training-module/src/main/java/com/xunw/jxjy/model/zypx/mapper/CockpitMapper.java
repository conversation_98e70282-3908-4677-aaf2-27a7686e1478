package com.xunw.jxjy.model.zypx.mapper;

import java.util.List;
import java.util.Map;

public interface CockpitMapper {

    List<Map<String, Object>> survey(String hostOrgId);

    List<Map<String, Object>> base(String hostOrgId);

    List<Map<String, Object>> baseXm(String hostOrgId);

    List<Map<String, Object>> classz(String hostOrgId);

    List<Map<String, Object>> xmType(String hostOrgId);

    List<Map<String, Object>> certi(String hostOrgId);

    List<Map<String, Object>> teacher(String hostOrgId);

    List<Map<String, Object>> outTeacher(String hostOrgId);

    List<Map<String, Object>> inTeacher(String hostOrgId);

    List<Map<String, Object>> income(String hostOrgId);

    List<Map<String, Object>> education(String hostOrgId);

    List<Map<String, Object>> xmSource(String hostOrgId);

    List<Map<String, Object>> expend(String hostOrgId);

    List<Map<String, Object>> featureXm(String hostOrgId);
}
