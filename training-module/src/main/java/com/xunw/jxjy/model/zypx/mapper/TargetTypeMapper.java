package com.xunw.jxjy.model.zypx.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 评分指标分类
 */
public interface TargetTypeMapper extends BaseMapper<TargetType> {
	
    List<Map<String,Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<TargetType> getAllChildrens(@Param("id") String id);
}
