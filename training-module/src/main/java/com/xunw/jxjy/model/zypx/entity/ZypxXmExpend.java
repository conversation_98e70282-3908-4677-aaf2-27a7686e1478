package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName biz_xm_expend 项目支出表
 */
@TableName(value = "biz_xm_expend")
public class ZypxXmExpend implements Serializable {

    private static final long serialVersionUID = 1L;

    //主键
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    //项目id
    @TableField(value = "xm_id")
    private String xmId;

    //支出项
    @TableField(value = "content")
    private String content;

    //支出金额
    @TableField(value = "amount")
    private Double amount;

    //时间
    @TableField(value = "time")
    private Date time;

    //经办人
    @TableField(value = "operator")
    private String operator;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getXmId() {
        return xmId;
    }

    public void setXmId(String xmId) {
        this.xmId = xmId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public void check() {
        if (BaseUtil.isEmpty(this.xmId)) {
            throw BizException.withMessage("项目id不能为空");
        }
        if (this.amount == null) {
            throw BizException.withMessage("支出金额不能为空");
        }
        if (BaseUtil.isEmpty(this.content)) {
            throw BizException.withMessage("支出项不能为空");
        }
        if (this.time == null) {
            throw BizException.withMessage("时间不能为空");
        }
        if (this.operator == null) {
            throw BizException.withMessage("经办人不能为空");
        }
    }
}