 package com.xunw.jxjy.model.portal.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.portal.mapper.ZkMapper;

@Service
public class ZkService {

	@Autowired
	private ZkMapper zkMapper;

	/**
	 * 学科列表
	 * 
	 * @return
	 */
	public List<Map<String, Object>> getSubjects() {
		return zkMapper.getSubjects();
	}

	/**
	 * 专业列表
	 * 
	 * @param condition
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	public Page<Map<String, Object>> getSpecialtys(Map<String, Object> condition, int pageNo, int pageSize) {
		Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);
		page.setRecords(zkMapper.getSpecialtys(condition, page));
		return page;
	}

	/**
	 * 专业详情：含专业计划
	 * 
	 * @param s_id
	 * @return
	 */
	public Map<String, Object> getSpecialty(String s_id) {
		Map<String, Object> specialty = zkMapper.getSpecialty(s_id);
		if (specialty != null) {
			List<Map<String, Object>> course_types = zkMapper.getCourseType(s_id); // 获取专业课程类型信息
			for (Map<String, Object> course_type : course_types) {
				String spct_id = (String) course_type.get("spctId");
				List<Map<String, Object>> course_child_types = zkMapper.getCourseChildType(spct_id); // 获取专业课程子类型信息
				for (Map<String, Object> course_child_type : course_child_types) {
					String spcct_id = (String) course_child_type.get("spcctId");
					List<Map<String, Object>> specialty_courses = zkMapper.getSpecialtyCourse(spct_id, spcct_id); // 获取专业课程子类型信息
					for (Map<String, Object> specialty_course : specialty_courses) {
						Map<String, Object> course = zkMapper.getCourse((String) specialty_course.get("cId"));// 课程信息
						specialty_course.put("course", course);
					}
					course_child_type.put("specialty_courses", specialty_courses);
				}
				course_type.put("course_child_types", course_child_types);
			}
			specialty.put("course_types", course_types);
		}
		return specialty;
	}

	/**
	 * 新闻公告类
	 * @return
	 */
	public List<Map<String, Object>> getCateogrys() {
		return zkMapper.getCateogrys();
	}
	
	public Map<String, Object> getCateogry(String c_id) {
		return zkMapper.getCateogry(c_id);
	}
	
	public Page<Map<String, Object>> getNews(Map<String, Object> condition, int pageNo, int pageSize) {
		Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);
		page.setRecords(zkMapper.getNews(condition, page));
		return page;
	}
	
	public Map<String, Object> getNew(String n_id) {
		return zkMapper.getNew(n_id);
	}

}