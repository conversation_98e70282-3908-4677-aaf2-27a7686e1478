package com.xunw.jxjy.model.tk.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.thoughtworks.xstream.XStream;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Stjc;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stnd;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.mapper.QuestionDBEntityMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper;
import com.xunw.jxjy.model.tk.params.QuestionEntityQueryParams;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.utils.QuestionImportExcelHelper;
import com.xunw.jxjy.model.utils.QuestionImportTxtHelper;
import com.xunw.jxjy.model.utils.QuestionImportWordHelper;
import com.xunw.jxjy.paper.model.Option;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.QBlank;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionBlankFill;
import com.xunw.jxjy.paper.model.QuestionEssay;
import com.xunw.jxjy.paper.model.QuestionJdt;
import com.xunw.jxjy.paper.model.QuestionJudgment;
import com.xunw.jxjy.paper.model.QuestionLst;
import com.xunw.jxjy.paper.model.QuestionMcjst;
import com.xunw.jxjy.paper.model.QuestionMultipleChoice;
import com.xunw.jxjy.paper.model.QuestionSingleChoice;
import com.xunw.jxjy.paper.model.QuestionTt;
import com.xunw.jxjy.paper.utils.ModelHelper;

import net.sf.json.JSONObject;

@Service
public class QuestionEntityService extends BaseCRUDService<QuestionEntityMapper, QuestionEntity>{

	// spring boot 标准的日志打印方式
	private static Logger logger = LoggerFactory.getLogger(QuestionEntityService.class);
	@Autowired
	private QuestionDBEntityMapper questionDBMapper;
	 
	public Page pageQuery(QuestionEntityQueryParams params, boolean isAdmin) throws SQLException, IOException {
		List<Map<String, Object>> list = null;
		if (isAdmin) {
			list = mapper.listByAdmin(params.getCondition(), params);
		} else {
			list = mapper.list(params.getCondition(), params);
		}
		//如果是套题则获取相应子题
		for(Map<String, Object> quesMap : list) {
			// 响应试题题干内容
			String content = BaseUtil.trimBlank(BaseUtil.convertNullToEmpty(quesMap.get("content"))).replace(" ", "")
					.replace("\r", "").replace("\n", "").replace("\t", "").toLowerCase();
			String questionStem = BaseUtil.extractText(content);
			quesMap.put("questionStem", questionStem);
			if(Stlb.TT.name().equals(BaseUtil.getStringValueFromMap(quesMap, "type"))) {
				String parentId = (String)quesMap.get("id");
				EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
				wrapper.eq("parent_id", parentId);
				wrapper.orderBy("seq_num", true);
				List<QuestionEntity> children = mapper.selectList(wrapper);
				quesMap.put("childrens", children);
			}
		}
		params.setRecords(list);
		return params;
	}

	@Transactional
	public void batchDelete(String ids) {
		List<String> list = new ArrayList<>();
		for (String id : StringUtils.split(ids, ",")) {
			//如果当前试题为父id 则不能删除
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
			wrapper.eq("parent_id", id);
			if(mapper.selectCount(wrapper) > 0) {
				throw BizException.withMessage("该套题下有子题，请勿删除！");
			}
			list.add(id);
		}
		mapper.deleteBatchIds(list);
	}

	public List<Map<String, Object>> getAllRealType() {
		return mapper.getAllRealType();
	}

	@Transactional
	public boolean add(JSONObject quesJson) {
		QuestionEntity questionEntity = new QuestionEntity();
		Stlb type = Stlb.valueOf(quesJson.getString("type"));
		String realType = BaseUtil.trimBlank(quesJson.getString("realType"));
		if (StringUtils.isEmpty(realType)) {
			throw BizException.withMessage("题型名称不能为空");
		}

		String id = BaseUtil.generateId();
		questionEntity.setId(id);
		questionEntity.setType(type);
		questionEntity.setDifficulty(Stnd.valueOf(quesJson.getString("difficulty")));
		questionEntity.setStatus(Zt.valueOf(quesJson.getString("status")));

		// 如果有父id 就是套题下子题，没有题库id
		if (quesJson.containsKey("parentId") && StringUtils.isNotEmpty(quesJson.getString("parentId"))) {
			String parentId = quesJson.getString("parentId");
			questionEntity.setDbId(null);
			questionEntity.setParentId(parentId);
			questionEntity.setSeqNum(quesJson.getInt("seqNum"));
		}else {
			questionEntity.setDbId(quesJson.getString("dbId"));
			questionEntity.setSource(quesJson.getString("source"));
		}
		// 题干
		String content = quesJson.getString("content");

		//查询当前题库里所有试题的题干，判断题干是否有重复
		EntityWrapper<QuestionEntity> entityWrapper = new EntityWrapper<>();
		entityWrapper.eq("db_id",quesJson.getString("dbId"));
		List<String> contents = mapper.selectList(entityWrapper).stream().map(QuestionEntity::getContent).collect(Collectors.toList());
		if (BaseUtil.cleanRepetition(contents,content) == 100){
			throw BizException.withMessage("试题重复");
		}

		try {
			content = FileHelper.convertQuestionBase64ImgToFile(content);
			quesJson.put("content", content);
		} catch (Exception e) {
			logger.error("ERROR HTML:[" + content + "]");
			logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
			e.printStackTrace();
		}

		questionEntity.setContent(content);
		questionEntity.setRealType(realType);

		// 试题答案
		String answer = null;
		if (Stlb.BLANKFILL == type) {
			String[] blanks = BaseUtil.getValuesFromJson(quesJson, "blanks");
			for (int i = 0; i < blanks.length; i++) {
				blanks[i] = BaseUtil.trimBlank(blanks[i]);
			}
			answer = StringUtils.join(blanks, ",");
		} else if (Stlb.MULTIPLECHOICE == type) {
			answer = StringUtils.join(BaseUtil.getValuesFromJson(quesJson, "answer"), "");
		} else {
			answer = quesJson.getString("answer");
			try {
				answer = FileHelper.convertQuestionBase64ImgToFile(answer);
				quesJson.put("answer", answer);
			} catch (Exception e) {
				logger.error("ERROR HTML:[" + answer + "]");
				logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
				e.printStackTrace();
			}
		}

		questionEntity.setAnswer(answer);

		// 试题解析
		String resolve = quesJson.getString("resolve");
		try {
			resolve = FileHelper.convertQuestionBase64ImgToFile(resolve);
			quesJson.put("resolve", resolve);
		} catch (Exception e) {
			logger.error("ERROR HTML:[" + resolve + "]");
			logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
			e.printStackTrace();
		}
		questionEntity.setResolve(resolve);
		String creatorId = quesJson.getString("creatorId");
		questionEntity.setCreatorId(creatorId);
		questionEntity.setCreateTime(new Date());
		String data = buildQuestion(id, quesJson);
		questionEntity.setData(data);
		mapper.insert(questionEntity);
		Map<String, Object> ques = new HashMap<String, Object>();
		ques.put("id", id);
		ques.put("type", type);
		ques.put("content", content);
		ques.put("answer", answer);
		ques.put("resolve", resolve);
		ques.put("data", data);
		ques.put("creatorId", creatorId);
		checkQues(ques);
		return true;
	}

	public String buildQuestion(String qid, JSONObject quesJson) {
		String result = "";
		XStream xstream = new XStream();
		Stlb type = Stlb.valueOf(quesJson.getString("type"));
		String content = quesJson.getString("content");
		String answer = null;
		String resolve = quesJson.getString("resolve");// 新增于20161028-扩展字段用来存解析

		/********************************************/
		if (Stlb.SINGLECHOICE == type) {
			answer = quesJson.getString("answer");
			QuestionSingleChoice question = new QuestionSingleChoice();
			String[] options = null;
			if ((options = BaseUtil.getValuesFromJson(quesJson, "options")) != null) {
				char alisa = 'A';
				for (String option : options) {
					try {
						option = FileHelper.convertQuestionBase64ImgToFile(option);
					} catch (Exception e) {
						logger.error("ERROR HTML:[" + option + "]");
						logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
						e.printStackTrace();
					}
					question.addOption(new Option(String.valueOf(alisa), option));
					alisa++;
				}
			}
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		} else if (Stlb.MULTIPLECHOICE == type) {
			QuestionMultipleChoice question = new QuestionMultipleChoice();
			String[] options = null;
			if ((options = BaseUtil.getValuesFromJson(quesJson, "options")) != null) {
				char alisa = 'A';
				for (String option : options) {
					try {
						option = FileHelper.convertQuestionBase64ImgToFile(option);
					} catch (Exception e) {
						logger.error("ERROR HTML:[" + option + "]");
						logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
						e.printStackTrace();
					}
					question.addOption(new Option(String.valueOf(alisa), option));
					alisa++;
				}
			}
			question.setId(qid);
			question.setContent(content);
			answer = StringUtils.join(BaseUtil.getValuesFromJson(quesJson, "answer"), "");
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);

			/********************************************/
		} else if (Stlb.JUDGMENT == type) {
			answer = quesJson.getString("answer");
			QuestionJudgment question = new QuestionJudgment();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);

			/********************************************/
		} else if (Stlb.BLANKFILL == type) {
			QuestionBlankFill question = new QuestionBlankFill();
			String[] qids = BaseUtil.getValuesFromJson(quesJson, "blankids");
			String[] qblanks = BaseUtil.getValuesFromJson(quesJson, "blanks");

			for (int i = 0; i < qids.length; i++) {
				int iqid = BaseUtil.getInt(qids[i]);
				String sqblank = BaseUtil.trimBlank(qblanks[i]);
				sqblank = org.springframework.web.util.HtmlUtils.htmlEscape(sqblank);
				question.addBlank(iqid, "BLANK" + iqid, sqblank);
			}

			question.setComplex(
					quesJson.containsKey("q_iscomplex") && "Y".equalsIgnoreCase(quesJson.getString("q_iscomplex"))
							? true
							: false);
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);

			/********************************************/
		} else if (Stlb.ESSAY == type) {
			answer = quesJson.getString("answer");
			QuestionEssay question = new QuestionEssay();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		} else if (Stlb.MCJST == type) {
			answer = quesJson.getString("answer");
			QuestionMcjst question = new QuestionMcjst();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		} else if (Stlb.LST == type) {
			answer = quesJson.getString("answer");
			QuestionLst question = new QuestionLst();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		} else if (Stlb.JDT == type) {
			answer = quesJson.getString("answer");
			QuestionJdt question = new QuestionJdt();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		} else if (Stlb.TT == type) {
			answer = quesJson.getString("answer");
			QuestionTt question = new QuestionTt();
			question.setId(qid);
			question.setContent(content);
			question.setAnswer(answer);
			question.setResolve(resolve);

			// 转换XML
			result = xstream.toXML(question);
		}

		return result;
	}

	public void checkQues(Map<String, Object> quesMap) {
		QuestionEntity questionEntity = null;
		String id = (String) quesMap.get("id");
		String wrongReason = null;
		try {
			String questionData = String.valueOf(quesMap.get("data"));
			String answer = String.valueOf(quesMap.get("answer"));
			Stlb questionType = (Stlb) quesMap.get("type");
			Question question = ModelHelper.convertObject(questionData);
			question.setId(id);
			question.setResolve((String) quesMap.get("resolve"));
			String content = BaseUtil.trimBlank(BaseUtil.convertNullToEmpty(question.getContent())).replace(" ", "")
					.replace("\r", "").replace("\n", "").replace("\t", "").toLowerCase();
			String extTxt = BaseUtil.extractText(content);
			if (BaseUtil.isEmpty(content) || "<p></p>".equals(content) || "<div></div>".equals(content)) {
				wrongReason = "题干内容为空";
			} else if (extTxt.matches("^(\\d+)\\.(.*)") || extTxt.matches("^(\\d+)、(.*)")) {
				wrongReason = "题干前有序号";
			} else if (Stlb.SINGLECHOICE.equals(questionType)) {
				QuestionSingleChoice ques = (QuestionSingleChoice) question;
				if (ques.getOptions() == null || ques.getOptions().size() == 0) {
					wrongReason = "选项为空";
				} else {
					String alisaStr = "";
					for (int i = 0; i < ques.getOptions().size(); i++) {
						Option opt = ques.getOptions().get(i);
						char order = (char) ('A' + i);
						if (!opt.getAlisa().equals(order + "")) {
							wrongReason = "选项序号有误，第" + (i + 1) + "个选项序号应该是：" + order;
							break;
						}
						String txt = BaseUtil.trimBlank(BaseUtil.convertNullToEmpty(opt.getText())).replace(" ", "")
								.replace("\r", "").replace("\n", "").replace("\t", "").toLowerCase();
						if (BaseUtil.isEmpty(txt) || "<p></p>".equals(txt) || "<div></div>".equals(txt)) {
							wrongReason = "选项[" + opt.getAlisa() + "]内容为空";
							break;
						}
						alisaStr = alisaStr + opt.getAlisa();
					}
					if (wrongReason == null) {
						if (BaseUtil.isEmpty(ques.getAnswer())) {
							wrongReason = "答案为空";
						} else if (ques.getAnswer().length() != 1) {
							wrongReason = "答案[" + ques.getAnswer() + "]里面有非选项的字符";
						} else if (ques.getAnswer().charAt(0) < 'A' || ques.getAnswer().charAt(0) > 'Z') {
							wrongReason = "答案[" + ques.getAnswer() + "]无法识别";
						} else if (!alisaStr.contains(ques.getAnswer())) {
							wrongReason = "答案[" + ques.getAnswer() + "]不在选项范围内";
						} else if (!ques.getAnswer().equals(answer)) {
							questionEntity = new QuestionEntity();
							questionEntity.setId(id);
							questionEntity.setAnswer(ques.getAnswer());
							mapper.updateById(questionEntity);
						}
					}
				}
			} else if (Stlb.MULTIPLECHOICE.equals(questionType)) {
				QuestionMultipleChoice ques = (QuestionMultipleChoice) question;
				if (ques.getOptions() == null || ques.getOptions().size() == 0) {
					wrongReason = "选项为空";
				} else {
					String alisaStr = "";
					for (int i = 0; i < ques.getOptions().size(); i++) {
						Option opt = ques.getOptions().get(i);
						char order = (char) ('A' + i);
						if (!opt.getAlisa().equals(order + "")) {
							wrongReason = "选项序号有误，第" + (i + 1) + "个选项序号应该是：" + order;
							break;
						}
						String txt = BaseUtil.trimBlank(BaseUtil.convertNullToEmpty(opt.getText())).replace(" ", "")
								.replace("\r", "").replace("\n", "").replace("\t", "").toLowerCase();
						if (BaseUtil.isEmpty(txt) || "<p></p>".equals(txt) || "<div></div>".equals(txt)) {
							wrongReason = "选项[" + opt.getAlisa() + "]内容为空";
							break;
						}
						alisaStr = alisaStr + opt.getAlisa();
					}
					if (wrongReason == null) {
						if (BaseUtil.isEmpty(ques.getAnswer())) {
							wrongReason = "答案为空";
						}
						for (int i = 0; i < ques.getAnswer().length(); i++) {
							if (ques.getAnswer().charAt(i) < 'A' || ques.getAnswer().charAt(i) > 'Z') {
								wrongReason = "答案[" + ques.getAnswer() + "]里面有无法识别的字符：" + ques.getAnswer().charAt(i);
								break;
							} else if (!alisaStr.contains(ques.getAnswer().charAt(i) + "")) {
								wrongReason = "答案[" + ques.getAnswer().charAt(i) + "]不在选项范围内";
								break;
							}
						}
						if (wrongReason == null && !ques.getAnswer().equals(answer)) {
							questionEntity = new QuestionEntity();
							questionEntity.setId(id);
							questionEntity.setAnswer(ques.getAnswer());
							mapper.updateById(questionEntity);
						}
					}
				}
			} else if (Stlb.JUDGMENT.equals(questionType)) {
				QuestionJudgment ques = (QuestionJudgment) question;
				if (!ques.getAnswer().equals(answer)) {
					questionEntity = new QuestionEntity();
					questionEntity.setId(id);
					questionEntity.setAnswer(ques.getAnswer());
					mapper.updateById(questionEntity);
				}
			} else if (Stlb.BLANKFILL.equals(questionType)) {
				QuestionBlankFill ques = (QuestionBlankFill) question;
			} else if (Stlb.ESSAY.equals(questionType)) {
				QuestionEssay ques = (QuestionEssay) question;
			} else if (Stlb.MCJST.equals(questionType)) {
				QuestionMcjst ques = (QuestionMcjst) question;
			} else if (Stlb.LST.equals(questionType)) {
				QuestionLst ques = (QuestionLst) question;
			} else if (Stlb.JDT.equals(questionType)) {
				QuestionJdt ques = (QuestionJdt) question;
			} else if (Stlb.TT.equals(questionType)) {
				QuestionTt ques = (QuestionTt) question;
			} else {
				wrongReason = "未知题型：" + questionType;
			}
		} catch (Exception e) {
			logger.warn("检查试题[id=" + id + "]时出现异常，原因：" + e.getMessage(), e);
			wrongReason= "未知异常：" + e.getMessage();
		}

		String creatorId = (String) quesMap.get("creatorId");
		if (wrongReason != null) {
			questionEntity = new QuestionEntity();
			questionEntity.setId(id);
			questionEntity.setIsCorrected(Stjc.YSCT);
			questionEntity.setWrongReason("SYS:" + wrongReason);
			questionEntity.setWrongFoundTime(new Date());
			questionEntity.setWrongFoundBy(creatorId);
		} else {
			questionEntity = new QuestionEntity();
			questionEntity.setId(id);
			questionEntity.setIsCorrected(Stjc.ZQST);
			questionEntity.setCorrectedTime(new Date());
			questionEntity.setCorrectedBy(creatorId);
		}
		mapper.updateById(questionEntity);
	}

	@Transactional
	public void batchEditStatus(String ids, Zt status, String updatorId) {
		Date updateTime = new Date();
		for (String id : StringUtils.split(ids, ",")) {
			QuestionEntity questionEntity = new QuestionEntity();
			questionEntity.setId(id);
			questionEntity.setStatus(status);
			questionEntity.setUpdatorId(updatorId);
			questionEntity.setUpdateTime(updateTime);
			mapper.updateById(questionEntity);
		}
	}

	public Map<String, Object> getQuesDetailsById(String id) throws SQLException, IOException {
		Map<String, Object> details = mapper.getQuesDetailsById(id);
		details.put("content", BaseUtil.isEmpty(details.get("content")) ? "" : details.get("content"));
		details.put("answer", BaseUtil.isEmpty(details.get("answer")) ? "" : details.get("answer"));
		details.put("resolve", BaseUtil.isEmpty(details.get("resolve")) ? "" : details.get("resolve"));
		details.put("data", BaseUtil.isEmpty(details.get("data")) ? "" : details.get("data"));
		return details;
	}

	@Transactional
	public boolean edit(JSONObject quesJson) {
		QuestionEntity questionEntity = new QuestionEntity();
		Stlb type = Stlb.valueOf(quesJson.getString("type"));
		String realType = BaseUtil.trimBlank(quesJson.getString("realType"));
		if (BaseUtil.isEmpty(realType)) {
			throw BizException.withMessage("题型名称不能为空");
		}
		String id = quesJson.getString("id");
		questionEntity.setId(id);
		questionEntity.setDifficulty(Stnd.valueOf(quesJson.getString("difficulty")));
		// 如果有父id 就是套题下子题，没有题库id
		if (!quesJson.containsKey("parentId") || StringUtils.isEmpty(quesJson.getString("parentId"))) {
			questionEntity.setDbId(quesJson.getString("dbId"));
			questionEntity.setSource(quesJson.getString("source"));
			questionEntity.setStatus(Zt.valueOf(quesJson.getString("status")));
		}
		String content = quesJson.getString("content");
		try {
			content = FileHelper.convertQuestionBase64ImgToFile(content);
			quesJson.put("content", content);
		} catch (Exception e) {
			logger.error("ERROR HTML:[" + content + "]");
			logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
			e.printStackTrace();
		}
		questionEntity.setContent(content);
		questionEntity.setRealType(realType);

		String answer = null;
		if (Stlb.BLANKFILL == type) {
			String[] blanks = BaseUtil.getValuesFromJson(quesJson, "blanks");
			for (int i = 0; i < blanks.length; i++) {
				blanks[i] = BaseUtil.trimBlank(blanks[i]);
			}
			answer = StringUtils.join(blanks, ",");
		} else if (Stlb.MULTIPLECHOICE == type) {
			answer = StringUtils.join(BaseUtil.getValuesFromJson(quesJson, "answer"), "");
		} else {
			answer = quesJson.getString("answer");
			try {
				answer = FileHelper.convertQuestionBase64ImgToFile(answer);
				quesJson.put("answer", answer);
			} catch (Exception e) {
				logger.error("ERROR HTML:[" + answer + "]");
				logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
				e.printStackTrace();
			}
		}
		questionEntity.setAnswer(answer);
		String resolve = quesJson.getString("resolve");
		{
			try {
				resolve = FileHelper.convertQuestionBase64ImgToFile(resolve);
				quesJson.put("resolve", resolve);
			} catch (Exception e) {
				logger.error("ERROR HTML:[" + resolve + "]");
				logger.error("把html代码里面的base64图片数据转成普通img url失败，原因：" + e.getMessage(), e);
				e.printStackTrace();
			}
		}
		questionEntity.setResolve(resolve);
		String updatorId = quesJson.getString("updatorId");
		questionEntity.setUpdatorId(updatorId);
		questionEntity.setUpdateTime(new Date());
		String data = buildQuestion(id, quesJson);
		questionEntity.setData(data);

		EntityWrapper<QuestionEntity> entityWrapper = new EntityWrapper<>();
		entityWrapper.eq("db_id",questionEntity.getDbId());
		entityWrapper.ne("id",questionEntity.getId());
		List<String> contents = mapper.selectList(entityWrapper).stream().map(QuestionEntity::getContent).collect(Collectors.toList());
		if (BaseUtil.cleanRepetition(contents,questionEntity.getContent()) == 100){
			throw BizException.withMessage("该题库下已有该试题！");
		}

		mapper.updateById(questionEntity);

		Map<String, Object> quesMap = new HashMap<String, Object>();
		quesMap.put("id", id);
		quesMap.put("type", type);
		quesMap.put("content", content);
		quesMap.put("answer", answer);
		quesMap.put("resolve", resolve);
		quesMap.put("data", data);
		quesMap.put("updatorId", updatorId);
		checkQues(quesMap);
		return true;
	}

	/**
	 * 返回套题下子题的序号
	 */
	public int getChildrenSeqNum(String parentId) {
		int seqNum = 0;
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
		wrapper.eq("parent_id", parentId);
		wrapper.orderBy("seq_num", false);
		List<QuestionEntity> list = mapper.selectList(wrapper);
		if(list.size() == 0) {
			seqNum = 1;
		}else {
			seqNum = list.get(0).getSeqNum()+1;
		}
		return seqNum;
	}

	@Transactional(rollbackFor = Exception.class)
	public int txtImport(MultipartFile file, String creatorId, String dbId, String parentId) throws  Exception {
		// 入库字段
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		File tmpFile = null;
		try {
			tmpFile = File.createTempFile("TEMPFILE" + BaseUtil.generateId(), ".tmp");
			file.transferTo(tmpFile);

			List<Map<String, Object>> data = QuestionImportTxtHelper
					.questionConverteFromTxt(getFileContent(tmpFile, "UTF-8"));
			if (data != null && data.size() > 0) {
				for (Map<String, Object> map : data) {
					map.put("id", BaseUtil.isNotEmpty(map.get("id")) ? map.get("id") : BaseUtil.generateId());
					map.put("dbId", dbId);
					list.add(map);
				}
			} else {
				data = QuestionImportTxtHelper.questionConverteFromTxt(getFileContent(tmpFile, "GBK"));
				if (data != null && data.size() > 0) {
					for (Map<String, Object> map : data) {
						map.put("id", BaseUtil.isNotEmpty(map.get("id")) ? map.get("id") : BaseUtil.generateId());
						map.put("dbId", dbId);
						list.add(map);
					}
				} else {
					data = QuestionImportTxtHelper.questionConverteFromTxt(getFileContent(tmpFile, "Unicode"));
					if (data != null && data.size() > 0) {
						for (Map<String, Object> map : data) {
							map.put("id", BaseUtil.isNotEmpty(map.get("id")) ? map.get("id") : BaseUtil.generateId());
							map.put("dbId", dbId);
							list.add(map);
						}
					} else {
						throw BizException.withMessage("文件不是Unicode、UTF-8或者ANSI（即GBK）编码，请检查文件。");
					}
				}
			}

		} catch (Exception e) {
			throw BizException.withMessage(e.getMessage());
		} finally {
			try {
				FileUtils.forceDelete(tmpFile);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		// 入库操作
		List<Map<String, Object>> quesList = importQuestions(list, creatorId, dbId,parentId);
		for (Map<String, Object> quesMap : quesList) {
			checkQues(quesMap);
		}
		return quesList.size();
	}

	@Transactional(rollbackFor = Exception.class)
	public int excelImport(MultipartFile file, String creatorId, String dbId, String parentId) throws  Exception{
		File tmpFile = null;
		try {
			tmpFile = File.createTempFile("TEMPFILE" + BaseUtil.generateId(), ".tmp", FileHelper.getTmpFolder());
			file.transferTo(tmpFile);

			List<Map<String, String>> list = OfficeToolExcel.readExcel(tmpFile,
					new String[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" });

			if (list == null || list.size() < 1)
				return 0;

			// 格式转换
			List<Map<String, Object>> questions = null;
			questions = QuestionImportExcelHelper.questionConverteFromExcel(list);

			if (questions == null || questions.size() < 1)
				return 0;

			// 入库操作
			List<Map<String, Object>> quesList = importQuestions(questions, creatorId, dbId,parentId);
			for (Map<String, Object> quesMap : quesList) {
				checkQues(quesMap);
			}
			return quesList.size();
		} catch (Exception e) {
			throw BizException.withMessage(e.getMessage());
		} finally {
			try {
				// 删除上传的文件
				tmpFile.delete();
			} catch (Exception e) {

			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public int wordImport(MultipartFile file, String cjyhId, String tkId, String parentId) throws  Exception {
		File tmpFile = null;
		try {
			tmpFile = File.createTempFile("TEMPFILE" + BaseUtil.generateId(), ".tmp", FileHelper.getTmpFolder());
			file.transferTo(tmpFile);

			// 格式转换
			List<Map<String, Object>> questions = QuestionImportWordHelper.questionConverteFromWord(tmpFile);

			if (questions == null || questions.size() < 1)
				return 0;

			// 入库操作
			List<Map<String, Object>> quesList = importQuestions(questions, cjyhId, tkId,parentId);
			for (Map<String, Object> quesMap : quesList) {
				checkQues(quesMap);
			}
			return quesList.size();

		} catch (Exception e) {
			throw BizException.withMessage(e.getMessage());
		} finally {
			try {
				// 删除上传的文件
				tmpFile.delete();
			} catch (Exception e) {

			}
		}
	}

	public List<String> getFileContent(File file, String charset) {
		List<String> lines = new ArrayList<String>();
		try {
			// 读取文件内容--开始
			BufferedReader reader = null;
			try {
				InputStreamReader isr = new InputStreamReader(new FileInputStream(file), charset);
				// reader = new BufferedReader(new FileReader(tmpFile));
				reader = new BufferedReader(isr);
				String tempString = null;
				while ((tempString = reader.readLine()) != null) {
					if (tempString.length() > 0) {
						int i = tempString.charAt(0) - 0;
						if (i == 65279) {// 隐形字符65279，即"EF BB BF" 这三个字节就叫BOM，全称是"Byte Order Mard"，是有些UTF-8文件第一个字符，故无视之
							tempString = tempString.substring(1);
						}
					}
					lines.add(tempString);

				}
				reader.close();
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				if (reader != null) {
					try {
						reader.close();
					} catch (IOException e1) {
					}
				}
			}
			// 读取文件内容--结束
		} catch (Exception e) {
			logger.error("获取上传试题模板数据失败:" + e.getMessage(), e);
		}
		return lines;
	}

	public List<Map<String, Object>> importQuestions(List<Map<String, Object>> list, String creatorId, String dbId,String parentId) {
		List<Map<String, Object>> quesList = new ArrayList<Map<String, Object>>();
		if (list == null || list.isEmpty())
			return quesList;
		List<QuestionEntity> questionEntityList = new ArrayList<QuestionEntity>();
		Date createTime = new Date();
		int seqNum = 1; // 查询当前套题得序号
		if (StringUtils.isNotEmpty(parentId)){
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
			wrapper.eq("parent_id", parentId);
			wrapper.orderBy("seq_num", false);
			List<QuestionEntity> children = mapper.selectList(wrapper);
			int a = CollectionUtils.isEmpty(children) ? 0 : children.get(0).getSeqNum();
			seqNum += a;
		}
		for (Map<String, Object> map : list) {
			seqNum++;
			Map<String, Object> quesMap = new HashMap<String, Object>();
			QuestionEntity questionEntity = new QuestionEntity();
			Stlb type = null;
			if (map.get("type") != null) {
				type = (Stlb) map.get("type");
			}
			String answer = String.valueOf(map.get("answer"));

			questionEntity.setId((String) map.get("id"));
			if (StringUtils.isNotEmpty(parentId)) {
				questionEntity.setDbId(null);
				questionEntity.setParentId(parentId);
				questionEntity.setSeqNum(seqNum);
			} else {
				questionEntity.setDbId(dbId);
			}
			questionEntity.setType(type);
			questionEntity.setRealType((String) map.get("realType"));
			questionEntity.setDifficulty((Stnd) map.get("difficulty"));
			questionEntity.setSource((String) map.get("source"));
			questionEntity.setContent((String) map.get("content"));
			questionEntity.setStatus((Zt) map.get("status"));

			/************** key 需要转换(虽然只用于展示) ***************/
			if (Stlb.MULTIPLECHOICE == type) {
				answer = answer.replace(",", "");

			} else if (Stlb.JUDGMENT == type) {
				if (!ModelHelper.FLAG_VAL_JUDGMENT_YES.contains(answer)
						&& !ModelHelper.FLAG_VAL_JUDGMENT_NO.contains(answer)) {
					throw BizException.withMessage("不可识别的判断题答案:" + answer);
				}
				answer = ModelHelper.FLAG_VAL_JUDGMENT_YES.contains(answer) ? "Y" : "N";

			} else if (Stlb.BLANKFILL == type) {
				answer = "";
				try {
					List<QBlank> lis = QuestionImportTxtHelper.formatBlanks(answer);
					if (lis != null) {
						for (QBlank blank : lis) {
							answer = answer + "," + blank.getValue();
						}
					}
					if (answer.startsWith(",")) {
						answer = answer.substring(1);
					}
				} catch (Exception e) {
					throw BizException.withMessage(e.getMessage());
				}
			}
			questionEntity.setAnswer(answer);
			/*****************************/

			questionEntity.setResolve((String) map.get("resolve"));
			questionEntity.setCreatorId(creatorId);
			questionEntity.setCreateTime(createTime);
			questionEntity.setData((String) map.get("data"));
			EntityWrapper<QuestionEntity> entityWrapper = new EntityWrapper<>();
			entityWrapper.eq("db_id",dbId);
			List<String> contents = mapper.selectList(entityWrapper).stream().map(QuestionEntity::getContent).collect(Collectors.toList());
			if (BaseUtil.cleanRepetition(contents,map.get("content").toString()) == 100){
				continue;
			}
			questionEntityList.add(questionEntity);

			quesMap.put("id", map.get("id"));
			quesMap.put("type", type);
			quesMap.put("content", map.get("content"));
			quesMap.put("answer", answer);
			quesMap.put("resolve", map.get("resolve"));
			quesMap.put("data", map.get("data"));
			quesMap.put("creatorId", creatorId);
			quesList.add(quesMap);
		}
		// 入库操作
		DBUtils.insertBatch(questionEntityList, QuestionEntity.class);
		return quesList;
	}

	public void checkIsCorrect(QuestionEntityQueryParams params) {
		try {
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
			if (BaseUtil.isNotEmpty(params.getKeyword())) {
				wrapper.like("content", params.getKeyword());
			}
			if (BaseUtil.isNotEmpty(params.getDbId())) {
				wrapper.eq("db_id", params.getDbId());
			}
			if (BaseUtil.isNotEmpty(params.getType())) {
				wrapper.eq("type", params.getType().name());
			}
			if (BaseUtil.isNotEmpty(params.getRealType())) {
				wrapper.eq("real_type", params.getRealType());
			}
			if (BaseUtil.isNotEmpty(params.getDifficulty())) {
				wrapper.eq("difficulty", params.getDifficulty().name());
			}
			if (BaseUtil.isNotEmpty(params.getStatus())) {
				wrapper.eq("status", params.getStatus().name());
			}
			if (BaseUtil.isNotEmpty(params.getIds())) {
				wrapper.in("id", params.getIds().split(","));
			}
			if (BaseUtil.isNotEmpty(params.getHostOrgId())) {
				EntityWrapper<QuestionDBEntity> dbWrapper = new EntityWrapper<>();
				wrapper.eq("host_org_id", params.getHostOrgId());
				List<QuestionDBEntity> dbList = questionDBMapper.selectList(dbWrapper);
				wrapper.in("db_id", dbList.stream().map(QuestionDBEntity::getId).collect(Collectors.toList()));
			}
			wrapper.eq("is_corrected", Stjc.WJC);
			List<QuestionEntity> list = mapper.selectList(wrapper);
			for (QuestionEntity questionEntity : list) {
				Map<String, Object> ques = new HashMap<String, Object>();
				ques.put("id", questionEntity.getId());
				ques.put("type", questionEntity.getType());
				ques.put("content", questionEntity.getContent());
				ques.put("answer", questionEntity.getAnswer());
				ques.put("resolve", questionEntity.getResolve());
				ques.put("data", questionEntity.getData());
				ques.put("creatorId", params.getUserId());
				checkQues(ques);
			}

		} catch (Exception e) {
			logger.error("检查错题时出现异常，原因：" + e.getMessage(), e);
			throw BizException.withMessage("检查错题时出现异常，原因：" + e.getMessage());
		}
	}

	/**
	 * @Description   随机抽取试题
	 * <AUTHOR>
	 */
	public void buildPaperByChooseQues(Paper paper) {
		List<Question> questions ;
		List<PaperSection> sections = paper.getSections();
		for (PaperSection section : sections) {
			questions = new ArrayList<>();
			List<Map<String, Object>> questionsByRandom = mapper.getQuestionsByRandom(section.getTdbid(), section.getTrealType(), section.getTlevel(), section.getTnum());
			for (Map<String, Object> map : questionsByRandom) {
				String s = map.get("data").toString();
				Question question = ModelHelper.convertObject(s);
				if (question == null) {
				    continue;
                }
				question.setId(map.get("id").toString());
				question.setScore(section.getTscore());

				if (StringUtils.isEmpty(question.getAnswer())) {
                    question.setAnswer((String)map.get("answer"));
                }
				if (Stlb.TT.equals(question.getType())) {
					List<Question> childrenList = new ArrayList<>();
					EntityWrapper<QuestionEntity> quesWrapper = new EntityWrapper<>();
					quesWrapper.eq("parent_id", question.getId());
					List<QuestionEntity> questionEntityList = mapper.selectList(quesWrapper);
					if (questionEntityList.size() > 0) {
						for (QuestionEntity questionEntity : questionEntityList) {
							Question children = new Question();
							children.setId(questionEntity.getId());
							children.setType(questionEntity.getType());
							children.setScore(0);//套题子题默认分数
							children.setContent(questionEntity.getContent());
							childrenList.add(children);
						}
					}
					((QuestionTt) question).setChildren(childrenList);
				}

				questions.add(question);
			}
			section.setQuestions(questions);
		}
	}

	/**
	 * 获取套题下的所有的子题
	 */
	public List<QuestionEntity> getChildrenByParentId(String parentId){
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
		wrapper.eq("parent_id", parentId);
		wrapper.orderBy("seq_num", true);
		return mapper.selectList(wrapper);
	}

	/**
	 * 题库、难度支持多选
	 */
	public List<QuestionEntity> list(QuestionEntityQueryParams params) {
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
		if (params.getType() != null) {
			wrapper.eq("type", params.getType().name());
		}
		if (StringUtils.isNotEmpty(params.getDbIds())) {
			wrapper.in("db_id", params.getDbIds().split(","));
		}
		if (StringUtils.isNotEmpty(params.getDifficultys())) {
			wrapper.in("difficulty", params.getDifficultys().split(","));
		}
		if (params.getStatus() != null) {
			wrapper.eq("status", params.getStatus().name());
		}
		// 过滤子题
		wrapper.isNull("parent_id");
		return mapper.selectList(wrapper);
	}

	/**
	 * 获取试题详情
	 */
	public Question getQuestion(String id) {
		Question question = null;
		if (question == null) {
			try {
				Map<String, Object> condition = new HashMap<String, Object>();
				condition.put("id", id);
				QuestionEntity questionEntity = mapper.selectById(id);
				if (questionEntity == null) {
					throw BizException.withMessage("ID=" + id + "的试题不存在");
				}
				String questionData = BaseUtil.isNotEmpty(questionEntity.getData()) ? questionEntity.getData() : "";
				Stlb questionType = questionEntity.getType();
				question = ModelHelper.convertObject(questionData);
				question.setId(id);
				question.setResolve(
						BaseUtil.isNotEmpty(questionEntity.getResolve()) ? questionEntity.getResolve() : "");
				question.setAnswer(BaseUtil.isNotEmpty(questionEntity.getAnswer()) ? questionEntity.getAnswer() : "");
				if (Stlb.SINGLECHOICE.equals(questionType)) {
					question = (QuestionSingleChoice) question;
				} else if (Stlb.MULTIPLECHOICE.equals(questionType)) {
					question = (QuestionMultipleChoice) question;
				} else if (Stlb.JUDGMENT.equals(questionType)) {
					question = (QuestionJudgment) question;
				} else if (Stlb.BLANKFILL.equals(questionType)) {
					question = (QuestionBlankFill) question;
				} else if (Stlb.ESSAY.equals(questionType)) {
					question = (QuestionEssay) question;
				} else if (Stlb.MCJST.equals(questionType)) {
					question = (QuestionMcjst) question;
				} else if (Stlb.LST.equals(questionType)) {
					question = (QuestionLst) question;
				} else if (Stlb.JDT.equals(questionType)) {
					question = (QuestionJdt) question;
				} else if (Stlb.TT.equals(questionType)) {
					question = (QuestionTt) question;
				}
			} catch (Exception e) {
			}
		}
		return question;
	}

	/**
	 * 对套题中的子题按照录题的顺序排序
	 */
	public void sortTTChilren(List<Question> children) {
		if (children != null && children.size() > 0) {
			List<String> stdIds = children.stream().map(x -> x.getId()).collect(Collectors.toList());
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<QuestionEntity>();
			wrapper.in("id", stdIds);
			wrapper.orderBy("seq_num", true);
			List<QuestionEntity> bizTkglSts = mapper.selectList(wrapper);
			List<Question> newList = new ArrayList<Question>();
			for (QuestionEntity bizTkglSt : bizTkglSts) {
				for (Question question : children) {
					if (bizTkglSt.getId().equals(question.getId())) {
						newList.add(question);
					}
				}
			}
			children.clear();
			children.addAll(newList);
		}
	}

	public List<QuestionEntity> checkIsRepeat(String dbId) {
		List<QuestionEntity> questions = mapper.selectList(new EntityWrapper<QuestionEntity>()
				.eq("db_id", dbId));
		List<QuestionEntity> list = questions.stream().peek(x -> x.setContent(BaseUtil.extractText(x.getContent()))).collect(Collectors.toList());
		Map<String, List<String>> map = new HashMap<>();
		for (QuestionEntity question : list) {
			List<String> ids = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(map.get(question.getContent()))){
				ids = map.get(question.getContent());
			}
			if (CollectionUtils.isNotEmpty(ids)) {
				ids.add(question.getId());
			}
			map.put(question.getContent(),ids);
		}
		List<String> repeatQuestionIds = map.entrySet().stream().filter(x -> x.getValue().size() > 1).flatMap(x -> x.getValue().stream()).collect(Collectors.toList());
		return questions.stream().filter(x->repeatQuestionIds.contains(x.getId())).collect(Collectors.toList());
	}
	
	public int count(QuestionEntityQueryParams params) {
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
		if (params.getType() != null) {
			wrapper.eq("type", params.getType().name());
		}
		if (StringUtils.isNotEmpty(params.getDbIds())) {
			wrapper.in("db_id", params.getDbIds().split(","));
		}
		if (params.getDifficulty() != null) {
			wrapper.eq("difficulty", params.getDifficulty().name());
		}
		if (StringUtils.isNotEmpty(params.getDifficultys())) {
			wrapper.in("difficulty", params.getDifficultys().split(","));
		}
		if (params.getStatus() != null) {
			wrapper.eq("status", params.getStatus().name());
		}
		// 过滤子题
		wrapper.isNull("parent_id");
		return mapper.selectCount(wrapper);
	}
	
	public List<String> typeList(String dbIds) {
		EntityWrapper<QuestionEntity> questionWrapper = new EntityWrapper<>();
		questionWrapper.in("db_id", dbIds.split(","));
		questionWrapper.ne("type", Stlb.TT.name());
		questionWrapper.isNull("parent_id");
		List<QuestionEntity> questionList = mapper.selectList(questionWrapper);
		List<String> typeList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(questionList)) {
			typeList = questionList.stream().map(x -> x.getType().name()).collect(Collectors.toList()).stream()
					.distinct().collect(Collectors.toList());
		}
		return typeList;
	}
	
}