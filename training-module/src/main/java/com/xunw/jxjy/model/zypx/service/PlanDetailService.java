package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.docx.DocxReader;
import com.xunw.jxjy.common.utils.docx.ResourceInfo;
import com.xunw.jxjy.model.common.courselive.Chapter;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.courselive.Lesson;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.common.entity.*;
import com.xunw.jxjy.model.common.mapper.CommApproveUserMapper;
import com.xunw.jxjy.model.common.service.CommApproveItemService;
import com.xunw.jxjy.model.common.service.CommApproveLogService;
import com.xunw.jxjy.model.common.service.CommApproveService;
import com.xunw.jxjy.model.common.service.CommApproveStepService;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.dto.PlanTreeNode;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.inf.entity.*;
import com.xunw.jxjy.model.inf.mapper.Type2UserMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.inf.service.InfXmService;
import com.xunw.jxjy.model.inf.service.Xm2CourseService;
import com.xunw.jxjy.model.inf.service.ZypxTypeService;
import com.xunw.jxjy.model.sys.entity.*;
import com.xunw.jxjy.model.sys.mapper.OrgMapper;
import com.xunw.jxjy.model.sys.mapper.User2RoleMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.service.MessageService;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zypx.dto.ApplyedProfession;
import com.xunw.jxjy.model.zypx.dto.ApplyedProfessionDto;
import com.xunw.jxjy.model.zypx.dto.ApplyedTechLevel;
import com.xunw.jxjy.model.zypx.dto.CourseSettingItem;
import com.xunw.jxjy.model.zypx.dto.plans.PlansApproveStep;
import com.xunw.jxjy.model.zypx.entity.*;
import com.xunw.jxjy.model.zypx.mapper.*;
import com.xunw.jxjy.model.zypx.params.PlanDetailQueryParams;
import com.xunw.jxjy.model.zypx.params.PlanQueryParams;
import com.xunw.jxjy.model.zypx.vo.ZypxPlanVo;
import jxl.read.biff.BiffException;
import net.sf.json.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PlanDetailService extends BaseCRUDService<PlanDetailMapper, PlanDetail> {

	@Autowired
	private PlanMapper planMapper;
	@Autowired
	private OrgMapper orgMapper;
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private CommApproveService approveService;
	@Autowired
	private CommApproveStepService approveStepService;
	@Autowired
	private CommApproveItemService approveItemService;
	@Autowired
	private CommApproveLogService approveLogService;
	@Autowired
	private CommApproveUserMapper commApproveUserMapper;
	@Autowired
	private MessageService messageService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private ZyjdBmMapper bmMapper;
	@Autowired
	private ZyjdBmScopeMapper bmScopeMapper;
	@Autowired
	private ZyjdProfessionMapper professionMapper;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private ZypxXm2SkillMapper xm2SkillMapper;
	@Autowired
	private ZypxXmCourseSettingMapper xmCourseSettingMapper;
	@Autowired
	private ZypxExamPaperMapper examPaperMapper;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private SmsSevice smsSevice;
	@Autowired
	private InfXmService infXmService;
	@Autowired
	private Xm2CourseService xm2CourseService;
	@Autowired
	private ZypxXmCourseSettingService xmCourseSettingService;
	@Autowired
	private Type2UserMapper type2UserMapper;
	@Autowired
	private User2RoleMapper user2RoleMapper;
	@Autowired
	private ZypxTypeService zypxTypeService;

	private static final Logger LOGGER = LoggerFactory.getLogger(PlanDetailService.class);

	/**
	 * 项目申报-列表查询
	 */
	public Page pageQuery(PlanDetailQueryParams params) throws IOException, SQLException {
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		for (Map<String, Object> map : list) {
			// 技能类项目展示工种信息
			if (StringUtils.equals(BaseUtil.getStringValueFromMap(map,"isSkill"),"1")) {
				EntityWrapper<ZypxXm2Skill> xm2skillWarpper = new EntityWrapper();
				xm2skillWarpper.eq("xm_id", BaseUtil.getStringValueFromMap(map,"id"));
				List<ZypxXm2Skill> zypxXm2Skills = xm2SkillMapper.selectList(xm2skillWarpper);
				Map<String, String> professionMap = zypxXm2Skills.stream()
						.collect(Collectors.groupingBy(
								ZypxXm2Skill::getProfessionId,
								Collectors.mapping(
										skill -> String.valueOf(skill.getTechLevel()),
										Collectors.joining(",")
								)
						));
				StringBuilder profession = new StringBuilder();
				for (String professionId : professionMap.keySet()) {
					ZyjdProfession zyjdProfession = professionMapper.selectById(professionId);
					profession.append(zyjdProfession.getName()).append("（");
					String[] scopeLevels = StringUtils.split(professionMap.get(professionId), ",");
					List<TechLevel> scopeLevelArrayList = Arrays.stream(scopeLevels).map(TechLevel::valueOf).sorted(Comparator.comparing(TechLevel::getId)).collect(Collectors.toList());
					profession.append(scopeLevelArrayList.stream().map(TechLevel::getName).collect(Collectors.joining(",")));
					profession.append("）;");
				}
				map.put("profession", StringUtils.isNotEmpty(profession) ? profession.substring(0, profession.toString().length() - 1) : profession);
			}
		}
		params.setRecords(list);
		return params;
	}

	/**
	 * 立项表审核-列表查询
	 */
	public Page getApprovePaperPage(PlanDetailQueryParams params) throws IOException, SQLException {
		List<Map<String, Object>> list = mapper.getApprovePaperPage(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 合同审核-列表查询
	 */
	public Page getApproveContracPage(PlanDetailQueryParams params) throws IOException, SQLException {
		List<Map<String, Object>> list = mapper.getApproveContracPage(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}
	
	/**
	 * 项目审批--列表查询
	 */
	public Page approveItemPage(PlanDetailQueryParams params) {
		List<Map<String, Object>> page = mapper.approveItemPage(params.getCondition(), params);
		params.setRecords(page);
		return params;
	}

	/**
	 * 保存申报信息
	 */
	@Transactional
	public void savePlanDetail(PlanDetail planDetail) {
		Plan plan = planMapper.selectById(planDetail.getPlanId());
		mapper.insert(planDetail);
		ZypxXm zypxXm = new ZypxXm();
		zypxXm.setId(planDetail.getId());
		zypxXm.setSerialNumber(planDetail.getSerialNumber());
		zypxXm.setTitle(planDetail.getName());
		zypxXm.setTypeId(plan.getTypeId());
		zypxXm.setTrainees(planDetail.getTrainees());
		zypxXm.setStartTime(planDetail.getStartTime());
		zypxXm.setEndTime(planDetail.getEndTime());
		zypxXm.setJtbmStartTime(planDetail.getStartTime());
		zypxXm.setJtbmEndTime(planDetail.getEndTime());
		zypxXm.setStudyEndTime(planDetail.getEndTime());
		//项目的状态为申报中
		zypxXm.setStatus(XmStatus.APPLY);
		zypxXm.setIsAutoSendSms(Constants.NO);
		zypxXm.setIsNeedApprove(Constants.NO);

		if (Constants.YES.equals(plan.getIsSkill())) {
			zypxXm.setIsAllowGrbm(Constants.NO);
			zypxXm.setIsAllowChooseCourse(Constants.NO);
		} else {
			zypxXm.setIsAllowGrbm(Constants.YES);//默认开放个人报名
			zypxXm.setIsAllowChooseCourse(Constants.NO);
			zypxXm.setIsMustFillPersonalInfo(Constants.NO);//默认报名前必须完善个人信息
			zypxXm.setGrbmStartTime(planDetail.getStartTime());
			zypxXm.setGrbmEndTime(planDetail.getEndTime());
			if (planDetail.getFeeStandard() != null && !Constants.YES.equals(planDetail.getIsOrgPay())) {
				zypxXm.setAmount(planDetail.getFeeStandard());// 设置学员的报名费用
			} else {
				zypxXm.setAmount(0d);// 设置学员的报名费用
			}
		}
		zypxXm.setHostOrgId(plan.getHostOrgId());
		zypxXm.setIsAlllowExamBeforePay(Constants.NO);
		zypxXm.setIsAlllowStudyBeforePay(Constants.NO);
		zypxXm.setCreatorId(planDetail.getUserId());
		zypxXm.setCreateTime(new Date());
		zypxXm.setYears(plan.getYears());
		zypxXm.setPlanId(plan.getId());
		zypxXm.setLeaderId(planDetail.getLeaderId());
		//获取平台配置信息，注意不是获取主办单位的配置信息
		SystemSetting systemSetting = systemSettingService.getGlobalSetting();
		if (systemSetting == null || StringUtils.isEmpty(systemSetting.getContent())) {
			throw BizException.withMessage("检测到系统参数尚未设置，请仔细检查系统管理中的系统参数是否已经设置完毕");
		}
		JSONObject jsonObject = JSONObject.fromObject(systemSetting.getContent());
		String zypxcjzb = (String) jsonObject.get(SysSettingEnum.ZYPXCJZB.name());
		if (StringUtils.isEmpty(zypxcjzb)) {
			throw BizException.withMessage("系统参数设置中的学习成绩、终极考试成绩占比尚未设置，请先设置，然后继续操作");
		}
		// 字符串切割
		String[] percentage = zypxcjzb.split(":");
		List<String> percentageList = new ArrayList<>();// 存放百分比的集合
		for (String s : percentage) {
			percentageList.add(s);
		}
		Double learningScoreZb = Double.parseDouble(percentageList.get(0));
		Double practiceScoreZb = Double.parseDouble(percentageList.get(1));
		Double finalExamScoreZb = Double.parseDouble(percentageList.get(2));
		zypxXm.setLearningScoreZb(learningScoreZb);
		zypxXm.setPracticeScoreZb(practiceScoreZb);
		zypxXm.setFinalExamScoreZb(finalExamScoreZb);
		zypxXm.setBuyType(planDetail.getBuyType());
		xmMapper.insert(zypxXm);
	}

	/**
	 * 修改项目的申报信息
	 */
	@Transactional
	public void editPlanDetail(PlanDetail planDetail) {
		Plan plan = planMapper.selectById(planDetail.getPlanId());
		mapper.updateAllColumnById(planDetail);
		ZypxXm zypxXm = xmMapper.selectById(planDetail.getId());
		zypxXm.setSerialNumber(planDetail.getSerialNumber());
		zypxXm.setTitle(planDetail.getName());
		zypxXm.setTypeId(plan.getTypeId());
		zypxXm.setTrainees(planDetail.getTrainees());
		zypxXm.setStartTime(planDetail.getStartTime());
		zypxXm.setEndTime(planDetail.getEndTime());
		zypxXm.setJtbmStartTime(planDetail.getStartTime());
		zypxXm.setJtbmEndTime(planDetail.getEndTime());
		zypxXm.setStudyEndTime(planDetail.getEndTime());
		//项目的状态为申报中
		zypxXm.setStatus(XmStatus.APPLY);
		zypxXm.setIsAutoSendSms(Constants.NO);
		zypxXm.setIsNeedApprove(Constants.NO);
		if (Constants.YES.equals(plan.getIsSkill())) {
			zypxXm.setIsAllowGrbm(Constants.NO);
			zypxXm.setIsAllowChooseCourse(Constants.NO);
		} else {
			zypxXm.setIsAllowGrbm(Constants.YES);//默认开放个人报名
			zypxXm.setIsAllowChooseCourse(Constants.NO);
			zypxXm.setIsMustFillPersonalInfo(Constants.YES);//默认报名前必须完善个人信息
			zypxXm.setGrbmStartTime(planDetail.getStartTime());
			zypxXm.setGrbmEndTime(planDetail.getEndTime());
			if (planDetail.getFeeStandard() != null && !Constants.YES.equals(planDetail.getIsOrgPay())) {
				zypxXm.setAmount(planDetail.getFeeStandard());// 设置学员的报名费用
			} else {
				zypxXm.setAmount(0d);// 设置学员的报名费用
			}
		}
		zypxXm.setHostOrgId(plan.getHostOrgId());
		zypxXm.setIsAlllowExamBeforePay(Constants.NO);
		zypxXm.setIsAlllowStudyBeforePay(Constants.NO);
		zypxXm.setCreatorId(planDetail.getUserId());
		zypxXm.setCreateTime(new Date());
		zypxXm.setYears(plan.getYears());
		zypxXm.setPlanId(plan.getId());
		zypxXm.setLeaderId(planDetail.getLeaderId());
		zypxXm.setBuyType(planDetail.getBuyType());
		xmMapper.updateById(zypxXm);
	}

	/**
	 * 删除项目申报信息
	 */
	@Transactional
	public void delete(String planDetailId) {
		mapper.deleteById(planDetailId);
		xmMapper.deleteById(planDetailId);
		EntityWrapper<ZypxXm2Skill> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", planDetailId);
		xm2SkillMapper.delete(wrapper);
		EntityWrapper<ZypxXmCourseSetting> courseWrapper = new EntityWrapper();
		courseWrapper.eq("xm_id", planDetailId);
		xmCourseSettingMapper.delete(courseWrapper);
		EntityWrapper<ExamPaper> paperWrapper = new EntityWrapper();
		paperWrapper.eq("xm_id", planDetailId);
		examPaperMapper.delete(paperWrapper);
	}

	/**
	 * 提交项目申报信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public Object submitPlan(PlanDetail planDetail, String isOpenPreApproval, User user) {
		planDetail.setStatus(AuditStatus.REPORTED);
		planDetail.setSubmitTime(new Date());
		if (Constants.YES.equals(isOpenPreApproval)) {
			Plan plan = planMapper.selectById(planDetail.getPlanId());
			//顶层父类
			ZypxType topType = zypxTypeService.getTopTypeById(plan.getTypeId());
			List<Type2User> type2Users = type2UserMapper.selectList(new EntityWrapper<Type2User>().eq("type_id", topType.getId()));
			if (CollectionUtils.isEmpty(type2Users)) {
				throw BizException.withMessage("该计划分类暂未设置审核员，请联系管理员");
			}
			List<User2Role> user2Roles = user2RoleMapper.selectList(new EntityWrapper<User2Role>().in("user_id", type2Users.stream()
					.map(Type2User::getUserId).collect(Collectors.toSet())));
			if (CollectionUtils.isEmpty(user2Roles) || user2Roles.stream().allMatch(x->x.getRole() != Role.HOST_ORG_CHILD)) {
				throw BizException.withMessage("该计划分类暂未设置审核员，请联系管理员");
			}
			genApproveInfo(planDetail, user, type2Users.stream().map(Type2User::getUserId).collect(Collectors.toList()));
		} else {
			genApproveInfo(planDetail, user);
		}
		mapper.updateById(planDetail);
		return true;
	}

	/**
	 * 生成审批事项
	 */
	public void genApproveInfo(PlanDetail planDetail, User user) {
		EntityWrapper<CommApprove> approveWrapper = new EntityWrapper<>();
		approveWrapper.eq("type", ProcessType.PLAN.name());
		approveWrapper.eq("status", Zt.OK.name());
		Org topOrg = orgService.getTopOrg(user.getOrgId());
		approveWrapper.eq("host_org_id", topOrg.getId());
		List<CommApprove> approves = approveService.selectList(approveWrapper);
		if (approves.size() == 0) {
			throw BizException.withMessage("没有查询到培训项目的审批流程配置，请检查当前主办单位是否已定义培训项目的审批流程！");
		}
		// 获取培训计划审批流程主信息
		CommApprove commApprove = approves.get(0);
		EntityWrapper<CommApproveStep> stepWrapper = new EntityWrapper<>();
		stepWrapper.eq("approve_id", commApprove.getId());
		stepWrapper.orderBy("step_num", true);
		// 获取培训计划审批流程第一环节信息
		CommApproveStep commApproveStep = approveStepService.selectList(stepWrapper).get(0);
		CommApproveItem approveItem = new CommApproveItem();
		String itemId = BaseUtil.generateId2();
		approveItem.setId(itemId);
		approveItem.setName(planDetail.getName());
		approveItem.setApproveId(commApprove.getId());
		approveItem.setStatus(ApproveStatus.Not_STARTED);
		approveItem.setCreatorId(user.getId());
		approveItem.setCreateTime(new Date());
		approveItem.setNextStepId(commApproveStep.getId());
		// 创建审批事项
		approveItemService.insert(approveItem);
		planDetail.setApproveItemId(itemId);
		Plan plan = planMapper.selectById(planDetail.getPlanId());
		// 生成系统消息
		this.createApproveItemMessageNotProcess(commApproveStep.getId(), plan.getHostOrgId(), planDetail.getName());
		//给一级审核人员发送短信提醒
		this.sendXmNeedApproveByMobileMessage(commApproveStep.getId(), plan.getHostOrgId(), planDetail.getName());
	}

	/**
	 * 生成审批事项2（用于开启了前置审批的单位）
	 */
	public void genApproveInfo(PlanDetail planDetail, User user, List<String> userIds) {
		EntityWrapper<CommApprove> approveWrapper = new EntityWrapper<>();
		approveWrapper.eq("type", ProcessType.PLAN.name());
		approveWrapper.eq("status", Zt.OK.name());
		Org topOrg = orgService.getTopOrg(user.getOrgId());
		approveWrapper.eq("host_org_id", topOrg.getId());
		List<CommApprove> approves = approveService.selectList(approveWrapper);
		if (approves.size() == 0) {
			throw BizException.withMessage("没有查询到培训项目的审批流程配置，请检查当前主办单位是否已定义培训项目的审批流程！");
		}
		// 获取培训计划审批流程主信息
		CommApprove commApprove = approves.get(0);
		// 获取培训计划审批流程第一环节信息
		CommApproveItem approveItem = new CommApproveItem();
		String itemId = BaseUtil.generateId2();
		approveItem.setId(itemId);
		approveItem.setName(planDetail.getName());
		approveItem.setApproveId(commApprove.getId());
		approveItem.setStatus(ApproveStatus.Not_STARTED);
		approveItem.setCreatorId(user.getId());
		approveItem.setCreateTime(new Date());
		approveItem.setNextStepId(null);
		approveItem.setPreUserId(userIds.get(0));
		approveItem.setPreStatus("2");//前置审批待审
		// 创建审批事项
		approveItemService.insert(approveItem);
		planDetail.setApproveItemId(itemId);
		Plan plan = planMapper.selectById(planDetail.getPlanId());
		// 生成系统消息
		this.createApproveItemMessageNotProcess(userIds, plan.getHostOrgId(), planDetail.getName());
		//给审核人员发送短信提醒
		this.sendXmNeedApproveByMobileMessage(userIds, plan.getHostOrgId(), planDetail.getName());
	}

	/**
	 * 创建待审批事项系统消息2（用于开启了前置审批的单位）
	 */
	@Transactional
	public void createApproveItemMessageNotProcess(List<String> userIds, String hostOrgId, String name) {
		Message message = new Message();
		message.setId(BaseUtil.generateId2());
		message.setType(MessageType.APPROVE_ITEM_NOT_PROCESS);
		message.setTitle("培训项目-待审批");
		message.setContent("【" + name + "】待您审批，请及时处理。");
		message.setIsMustReceived(Constants.YES);
		message.setCreateTime(new Date());
		message.setHostOrgId(hostOrgId);
		messageService.saveUserMessage(message, userIds);
	}

	/**
	 * 给审核人员发送项目待审批的短信2 （用于开启了前置审批的单位）
	 */
	public void sendXmNeedApproveByMobileMessage(List<String> userIds,String hostOrgId,String name) {
		try {
			List<User> userList = userMapper.selectBatchIds(userIds);
			Org hostOrg = orgService.selectById(hostOrgId);
			String sign = hostOrg.getSmsSign();
			String message = "培训项目审批-" + name;
			for (User user : userList) {
				if (StringUtils.isNotEmpty(user.getMobile())){
					smsSevice.sendByTemplate(sign, "246017", user.getMobile(), new String[] {user.getName(), message.length() > 20 ? message.substring(0,17)+"..." : message});
				}
			}
		} catch (Exception e) {
			LOGGER.error("短信发送失败:", e);
		}
	}

	/**
	 * 创建待审批事项系统消息
	 */
	@Transactional
	public void createApproveItemMessageNotProcess(String stepId, String hostOrgId, String name) {
		EntityWrapper<CommApproveUser> wrapper = new EntityWrapper();
		wrapper.eq("step_id", stepId);
		List<CommApproveUser> commApproveUsers = commApproveUserMapper.selectList(wrapper);
		List<String> userIds = commApproveUsers.stream().map(CommApproveUser::getUserId).collect(Collectors.toList());
		Message message = new Message();
		message.setId(BaseUtil.generateId2());
		message.setType(MessageType.APPROVE_ITEM_NOT_PROCESS);
		message.setTitle("培训项目-待审批");
		message.setContent("【" + name + "】待您审批，请及时处理。");
		message.setIsMustReceived(Constants.YES);
		message.setCreateTime(new Date());
		message.setHostOrgId(hostOrgId);
		messageService.saveUserMessage(message, userIds);
	}

	/**
	 * 给审核人员发送项目待审批的短信
	 */
	public void sendXmNeedApproveByMobileMessage(String stepId,String hostOrgId,String name) {
		try {
			EntityWrapper<CommApproveUser> wrapper = new EntityWrapper();
			wrapper.eq("step_id", stepId);
			List<CommApproveUser> commApproveUsers = commApproveUserMapper.selectList(wrapper);
			List<String> userIds = commApproveUsers.stream().map(x -> x.getUserId()).collect(Collectors.toList());
			List<User> userList = userMapper.selectBatchIds(userIds);
			Org hostOrg = orgService.selectById(hostOrgId);
			String sign = hostOrg.getSmsSign();
			String message = "培训项目审批-" + name;
			for (User user : userList) {
				if (StringUtils.isNotEmpty(user.getMobile())){
					smsSevice.sendByTemplate(sign, "246017", user.getMobile(), new String[] {user.getName(), message.length() > 20 ? message.substring(0,17)+"..." : message});
				}
			}
		} catch (Exception e) {
			LOGGER.error("短信发送失败:", e);
		}
	}
	
	/**
	 * 给申报人员发送培训项目审批结果短信
	 */
	public void sendXmApproveResultByMobileMessage(String userId, String hostOrgId, ApproveResult approveResult) {
		try {
			User user = userMapper.selectById(userId);
			Org hostOrg = orgService.selectById(hostOrgId);
			String sign = hostOrg.getSmsSign();
			if (StringUtils.isNotEmpty(user.getMobile())) {
				smsSevice.sendByTemplate(sign, "264323", user.getMobile(), new String[]{user.getName(), approveResult == ApproveResult.PASS ? "通过" : "不通过"});
			}
		} 
		catch (Exception e) {
			LOGGER.error("短信发送失败:", e);
		}
	}

	/**
	 * 创建已审批事项系统消息
	 */
	@Transactional
	public void createApproveItemMessageProcessed(String userId, String hostOrgId, String name, ApproveResult result) {
		Message message = new Message();
		message.setId(BaseUtil.generateId2());
		message.setType(MessageType.APPROVE_ITEM_PROCESSED);
		message.setTitle("培训项目-已审批");
		message.setContent("【" + name + "】已审批,审批结果【" + result.getName() + "】。");
		message.setIsMustReceived(Constants.YES);
		message.setCreateTime(new Date());
		message.setHostOrgId(hostOrgId);
		List<String> userIds = new ArrayList<String>();
		userIds.add(userId);
		messageService.saveUserMessage(message, userIds);
	}

	/**
	 * 项目审批操作
	 */
	@Transactional
	public Object auditPlan(PlanDetail planDetail, User user, ApproveResult approveResult, String reason, String isOpenPreApproval, Role role) {
		CommApproveItem approveItem = approveItemService.selectById(planDetail.getApproveItemId());
		if (approveItem == null) {
			throw BizException.withMessage("审批事项在系统中不存在");
		}
		CommApproveStep approveStep = approveStepService.selectById(approveItem.getNextStepId());
		if (Constants.YES.equals(isOpenPreApproval)) {
			if ("2".equals(approveItem.getPreStatus())) {
				if (role != Role.HOST_ORG_CHILD) throw BizException.withMessage("不是二级管理员，不能参与前置审核");
				approveItem.setPreStatus(approveResult == ApproveResult.NOT_PASS ? "0":"1");
			}
		} else {
			if (approveStep == null) {
				throw BizException.withMessage("当前审批环节ID：" + approveStep.getId() + "的审批环节数据不存在");
			}
			List<String> userIds = approveStepService.getApproveUserIdByStepId(approveItem.getNextStepId());
			if (!userIds.contains(user.getId())) {
				throw BizException.withMessage("按照审批流程的配置，当前用户不能审批此环节");
			}
			EntityWrapper<CommApproveLog> logWrapper = new EntityWrapper<>();
			logWrapper.eq("item_id", approveItem.getId());
			logWrapper.eq("step_id", approveStep.getId());
			if (approveLogService.selectCount(logWrapper) > 0) {
				throw BizException.withMessage("当前环节已被审核，请勿重复操作！");
			}
		}
		EntityWrapper<CommApproveStep> stepWrapper = new EntityWrapper<>();
		stepWrapper.eq("approve_id", approveItem.getApproveId());
		stepWrapper.orderBy("step_num", false);
		CommApproveStep commApproveStep = approveStepService.selectList(stepWrapper).get(0);// 获取最后一个环节
		Plan plan = planMapper.selectById(planDetail.getPlanId());
		if (commApproveStep.getId().equals(approveItem.getNextStepId()) || approveResult == ApproveResult.NOT_PASS) {
			// 当前环节是最后一个环节或者当前环节审核不通过
			approveItem.setStatus(ApproveStatus.END);
			approveItem.setResult(approveResult);
			approveItem.setNextStepId(null);
			if (approveResult == ApproveResult.NOT_PASS) {
				planDetail.setStatus(AuditStatus.REJECT);
			} else if (approveResult == ApproveResult.PASS) {
				planDetail.setStatus(AuditStatus.PASSED);
				//如果已附带立项表，则立项表标记审核通过
				if(StringUtils.isNotEmpty(planDetail.getPaper())) {
					planDetail.setApproveStatus(ApproveResult.PASS);
					planDetail.setApproveUserId(user.getId());
					planDetail.setApprovalTime(new Date());
				}
				//如果已附带合同，则合同标记为审核通过
				if(StringUtils.isNotEmpty(planDetail.getContract())) {
					planDetail.setContractStatus(ApproveResult.PASS);
					planDetail.setContractUserId(user.getId());
					planDetail.setContractTime(new Date());
				}
			}
			this.createApproveItemMessageProcessed(planDetail.getUserId(), plan.getHostOrgId(), planDetail.getName(), approveResult);
			this.sendXmApproveResultByMobileMessage(planDetail.getUserId(), plan.getHostOrgId(), approveResult);
			mapper.updateAllColumnById(planDetail);
		} else {// 获取下一个待审批环节
			EntityWrapper<CommApproveStep> wrapper = new EntityWrapper<>();
			Integer stepNum = approveStep == null ? 1 : approveStep.getStepNum() + 1;
			wrapper.eq("approve_id", approveItem.getApproveId());
			wrapper.eq("step_num", stepNum);
			CommApproveStep nextStep = approveStepService.selectList(wrapper).get(0);
			approveItem.setStatus(ApproveStatus.UNDER_REVIEW);
			approveItem.setNextStepId(nextStep.getId());
			this.createApproveItemMessageNotProcess(nextStep.getId(), plan.getHostOrgId(), planDetail.getName());
			this.sendXmNeedApproveByMobileMessage(nextStep.getId(), plan.getHostOrgId(), planDetail.getName());
		}
		CommApproveLog log = new CommApproveLog();
		String logId = BaseUtil.generateId2();
		log.setId(logId);
		log.setItemId(approveItem.getId());
		log.setStepId(approveStep == null ? null : approveStep.getId());
		log.setStepNum(approveStep == null ? 0 : approveStep.getStepNum());
		log.setUserId(user.getId());
		log.setResult(approveResult);
		if (StringUtils.isNotEmpty(reason)) {
			log.setReason(reason);
		}
		log.setTime(new Date());
		approveLogService.insert(log);
		approveItemService.updateAllColumnById(approveItem);
		return true;
	}

	/**
	 * 获取审批日志
	 */
	public List<PlansApproveStep> getPlanDetailApproveLog(String planDetailId) {
		// 获取培训计划的所有审批环节
		List<Map<String, Object>> commApproveSteps = mapper.getPlanApproveSteps(planDetailId);
		// 获取培训计划的审批日志
		List<Map<String, Object>> planApproveLogs = mapper.getPlanApproveLogs(planDetailId);
		List<PlansApproveStep> list = new ArrayList<>();
		//处理前置审批
		Optional<Map<String, Object>> optional = planApproveLogs.stream().filter(x -> x.get("id") == null).findFirst();
		if (optional.isPresent()){
			PlansApproveStep approve = new PlansApproveStep();
			// 设置环节的信息
			approve.setId(null);
			approve.setStepNum(0);
			approve.setStepName("前置审批");
			approve.setAudit(true);
			approve.setUserName(BaseUtil.getStringValueFromMap(optional.get(), "userName"));
			approve.setResult(BaseUtil.getStringValueFromMap(optional.get(), "result"));
			approve.setReason(BaseUtil.getStringValueFromMap(optional.get(), "reason"));
			approve.setTime(BaseUtil.getDateValueFromMap(optional.get(), "time"));
			list.add(approve);
		}
		for (Map<String, Object> commApproveStep : commApproveSteps) {
			PlansApproveStep approve = new PlansApproveStep();
			// 设置环节的信息
			approve.setId(BaseUtil.getStringValueFromMap(commApproveStep, "id"));
			approve.setStepNum(BaseUtil.getIntValueFromMap(commApproveStep, "stepNum"));
			approve.setStepName(BaseUtil.getStringValueFromMap(commApproveStep, "stepName"));
			List<Map<String, Object>> currentStepLogs = planApproveLogs.stream()
					.filter(map -> BaseUtil.getStringValueFromMap(map, "id", "")
							.equals(BaseUtil.getStringValueFromMap(commApproveStep, "id")))
					.collect(Collectors.toList());
			// 如果找到了审批日志则对应匹配审批的结果和意见
			if (currentStepLogs != null && currentStepLogs.size() > 0) {
				Map<String, Object> a = currentStepLogs.get(0);
				approve.setAudit(true);
				approve.setUserName(BaseUtil.getStringValueFromMap(a, "userName"));
				approve.setResult(BaseUtil.getStringValueFromMap(a, "result"));
				approve.setReason(BaseUtil.getStringValueFromMap(a, "reason"));
				approve.setTime(BaseUtil.getDateValueFromMap(a, "time"));
			}
			list.add(approve);
		}
		return list;
	}

	/**
	 * 下载立项审批表
	 */
	public File downApproval(String id, String dir) {
		PlanDetail planDetail = mapper.selectById(id);
		Org cooperOrg = StringUtils.isNotEmpty(planDetail.getEntrustOrgId())
				? orgMapper.selectById(planDetail.getEntrustOrgId())
				: null;
		Org organizerOrg = StringUtils.isNotEmpty(planDetail.getReceiveOrgId())
				? orgMapper.selectById(planDetail.getReceiveOrgId())
				: null;
		User leader = StringUtils.isNotEmpty(planDetail.getLeaderId()) ? userMapper.selectById(planDetail.getLeaderId())
				: null;
		File newDocx = null;
		DocxReader templetDocxReader = null;
		Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();
		try {
			{// 拷贝一份模板文件到临时文件夹里面
				FileOutputStream fos = null;
				InputStream is = null;
				URL url = null;
				try {
					url = new URL(dir);
					HttpURLConnection conn = (HttpURLConnection) url.openConnection();
					conn.setConnectTimeout(3 * 1000);
					// 防止屏蔽程序抓取而返回403错误
					conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
					newDocx = FileHelper.createTmpFile();
					newDocx = new File(newDocx + ".docx");
					fos = new FileOutputStream(newDocx);
					is = conn.getInputStream();
					IOUtils.copyLarge(is, fos);
					fos.flush();
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fos);
				}
			}
			templetDocxReader = new DocxReader(newDocx);
			templetDocxReader.doRead();
			{
				needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),
						templetDocxReader.getDocumentInfo());
				File document = templetDocxReader.getDocumentInfo().getFile();
				FileOutputStream fosNew = null;
				FileOutputStream fos = null;
				InputStream is = null;
				try {
					is = new FileInputStream(document);
					String docStr = IOUtils.toString(is, "UTF-8");
					if (planDetail != null) {
						String xm = BaseUtil.convertNullToEmpty(planDetail.getName());
						String serialNumber = BaseUtil.convertNullToEmpty(planDetail.getSerialNumber());
						// 项目来源
						String xmSources = BaseUtil.convertNullToEmpty(
								planDetail.getXmSources() != null ? planDetail.getXmSources().getName() : "");
						// 委托单位
						String cooperOrgName = BaseUtil
								.convertNullToEmpty(cooperOrg != null ? cooperOrg.getName() : "");
						// 联系人
						String contact = BaseUtil.convertNullToEmpty(cooperOrg != null ? cooperOrg.getContact() : "");
						// 联系人电话
						String telephone = BaseUtil
								.convertNullToEmpty(cooperOrg != null ? cooperOrg.getTelephone() : "");
						// 承办单位
						String organizer = BaseUtil
								.convertNullToEmpty(organizerOrg != null ? organizerOrg.getName() : "");
						// 项目负责人
						String leaderName = BaseUtil.convertNullToEmpty(leader != null ? leader.getName() : "");
						// 负责人联系方式
						String leaderPhone = BaseUtil.convertNullToEmpty(leader != null ? leader.getMobile() : "");
						// 培训形式
						String trainingForm = BaseUtil.convertNullToEmpty(planDetail.getTrainingForm());
						// 招生方式
						String enrollWay = BaseUtil.convertNullToEmpty(
								planDetail.getEnrollWay() != null ? planDetail.getEnrollWay().getName() : "");
						// 培训开始时间
						String startTime = BaseUtil
								.convertNullToEmpty(DateUtils.format(planDetail.getStartTime(), "yyyy-MM-dd"));
						// 培训结束时间
						String endTime = BaseUtil
								.convertNullToEmpty(DateUtils.format(planDetail.getEndTime(), "yyyy-MM-dd"));
						// 费用
						String amount = BaseUtil.convertNullToEmpty(planDetail.getAmount());
						// 是否发放证书
						String isIssueCertificate = BaseUtil.convertNullToEmpty(planDetail.getIsIssueCertificate());
						// 课时
						DecimalFormat df = new DecimalFormat("#.##");
						String hours = BaseUtil.convertNullToEmpty(planDetail.getHours() !=null ? df.format(planDetail.getHours()) : null);
						// 收费单位
						String units = BaseUtil.convertNullToEmpty(planDetail.getUnits());
						// 培训地点
						String address = BaseUtil.convertNullToEmpty(planDetail.getAddress());
						// 教师团队
						String teachers = BaseUtil.convertNullToEmpty(planDetail.getTeachers());
						// 学员人数
						String count = BaseUtil.convertNullToEmpty(planDetail.getCount());
						docStr = docStr.replace("1111", BaseUtil.toUnicode(xm));
						docStr = docStr.replace("xmmc", BaseUtil.toUnicode(xm));
						docStr = docStr.replace("xmbh", BaseUtil.toUnicode(serialNumber));
						// 项目来源
						if (XmSources.GOVERNMENT.getName().equals(xmSources)) {
							docStr = docStr.replace("3333", BaseUtil.toUnicode("政府委托（√） 行业企业委托（ ） 其他（　）"));
						} else if (XmSources.ENTERPRISE.getName().equals(xmSources)) {
							docStr = docStr.replace("3333", BaseUtil.toUnicode("政府委托（ ） 行业企业委托（√） 其他（　）"));
						} else if (XmSources.OTHERS.getName().equals(xmSources)) {
							docStr = docStr.replace("3333", BaseUtil.toUnicode("政府委托（ ） 行业企业委托（ ） 其他（√）"));
						} else {
							docStr = docStr.replace("3333", BaseUtil.toUnicode("政府委托（ ） 行业企业委托（ ） 其他（ ）"));
						}
						docStr = docStr.replace("4444", BaseUtil.toUnicode(cooperOrgName));
						docStr = docStr.replace("5555", BaseUtil.toUnicode(organizer));
						docStr = docStr.replace("8888", BaseUtil.toUnicode(leaderName));
						docStr = docStr.replace("9999", BaseUtil.toUnicode(leaderPhone));
						docStr = docStr.replace("0101", BaseUtil.toUnicode(trainingForm));
						// 招生方式
						if (EnrollWay.ENTRUSTED.getName().equals(enrollWay)) {
							docStr = docStr.replace("0102", BaseUtil.toUnicode("单位委托（√）　社会招生（ ）"));
						} else if (EnrollWay.SOCIALENROLLMENT.getName().equals(enrollWay)) {
							docStr = docStr.replace("0102", BaseUtil.toUnicode("单位委托（ ）　社会招生（√）"));
						} else {
							docStr = docStr.replace("0102", BaseUtil.toUnicode("单位委托（ ）　社会招生（ ）"));
						}
						docStr = docStr.replace("0103", BaseUtil.toUnicode(startTime + "至" + endTime));
						// 证书是否发放
						if (isIssueCertificate.equals(Constants.YES)) {
							docStr = docStr.replace("0104", BaseUtil.toUnicode("是（√）　否（　）"));
						} else if (isIssueCertificate.equals(Constants.NO)) {
							docStr = docStr.replace("0104", BaseUtil.toUnicode("是（）　否（√）"));
						} else {
							docStr = docStr.replace("0104", BaseUtil.toUnicode("是（ ） 否（ ）"));
						}
						docStr = docStr.replace("0105", BaseUtil.toUnicode(address));
						docStr = docStr.replace("6666", BaseUtil.toUnicode(contact));
						docStr = docStr.replace("7777", BaseUtil.toUnicode(telephone));
						docStr = docStr.replace("0107", BaseUtil.toUnicode(count));
						docStr = docStr.replace("0106", BaseUtil.toUnicode(hours));
						docStr = docStr.replace("0108", BaseUtil.toUnicode(amount));
						docStr = docStr.replace("0109", BaseUtil.toUnicode(units));
						docStr = docStr.replace("0110", BaseUtil.toUnicode(teachers));
						docStr = docStr.replace("9933", BaseUtil.toUnicode(teachers));
					}
					fos = new FileOutputStream(document, false);
					IOUtils.write(docStr, fos, "UTF-8");
				} finally {
					BaseUtil.close(is);
					BaseUtil.close(fosNew);
					BaseUtil.close(fos);
				}
			}

			{
				DocxReader.updateDocx(newDocx, needToUpdate);
			}
		} catch (Exception e) {
			throw BizException.withMessage(e.getMessage());
		} finally {
			DocxReader.release(templetDocxReader);
		}
		return newDocx;
	}

	/**
	 * 立项表审批操作
	 */
	@Transactional
	public Object auditPaper(PlanDetail planDetail, String userId, ApproveResult approveStatus, String approveAdvice) {
		planDetail.setApproveStatus(approveStatus);
		planDetail.setApproveUserId(userId);
		planDetail.setApprovalTime(new Date());
		planDetail.setApproveAdvice(approveAdvice);
		updateById(planDetail);
		return true;
	}

	/**
	 * 合同审批操作
	 */
	@Transactional
	public Object auditContract(PlanDetail planDetail, String userId, ApproveResult approveStatus, String approveAdvice) {
		planDetail.setContractStatus(approveStatus);
		planDetail.setContractUserId(userId);
		planDetail.setContractTime(new Date());
		planDetail.setContractAdvice(approveAdvice);
		updateById(planDetail);
		return true;
	}

	/**
	 * 获取职业技能项目申报的报名信息
	 */
	public Object getProfessionBmCountList(String batchId, String id) {
		EntityWrapper<ZyjdBmScope> wrapper = new EntityWrapper();
		wrapper.eq("bmbatch_id", batchId);
		List<ZyjdBmScope> bmScopes = bmScopeMapper.selectList(wrapper);
		List<Map<String, Object>> list = bmMapper.getProfessionBmCountList(batchId);
		List<ApplyedProfession> applyedProfessions = new ArrayList<ApplyedProfession>();
		//查询当前项目已经勾选的工种和等级
		List<ZypxXm2Skill> xm2Skills = new ArrayList<ZypxXm2Skill>();
		if(StringUtils.isNotEmpty(id)) {
			EntityWrapper<ZypxXm2Skill> xm2skillWarpper = new EntityWrapper();
			xm2skillWarpper.eq("xm_id", id);
			xm2Skills = xm2SkillMapper.selectList(xm2skillWarpper);
		}
		// 汇总各个工种的报名人数
		for (ZyjdBmScope zyjdBmScope : bmScopes) {
			ZyjdProfession profession = professionMapper.selectById(zyjdBmScope.getProfessionId());
			ApplyedProfession applyedProfession = new ApplyedProfession();
			applyedProfessions.add(applyedProfession);
			applyedProfession.setId(profession.getId());
			applyedProfession.setName(profession.getName());
			List<ApplyedTechLevel> techLevels = new ArrayList<ApplyedTechLevel>();
			applyedProfession.setTechLevels(techLevels);
			boolean a = xm2Skills.stream().filter(x -> x.getProfessionId().equals(profession.getId())).count() > 0;
			applyedProfession.setChecked(a);

			String[] scopeLevels = StringUtils.split(zyjdBmScope.getTechLevel(), ",");
			List<TechLevel> scopeLevelArrayList = Arrays.asList(scopeLevels).stream().map(x->TechLevel.valueOf(x)).collect(Collectors.toList());
			//按照ID排序
			scopeLevelArrayList.sort(Comparator.comparing(y->y.getId()));
			for (TechLevel level : scopeLevelArrayList) {
				List<Map<String, Object>> bmList = list.stream().filter(
						x -> BaseUtil.getStringValueFromMap(x, "professionId").equals(zyjdBmScope.getProfessionId())
								&& BaseUtil.getStringValueFromMap(x, "applyTechLevel").equals(level.name()))
						.collect(Collectors.toList());
				int count = 0;
				//获取批次下的工种和等级的报名人数
				if (bmList.size() > 0) {
					count = BaseUtil.getIntValueFromMap(bmList.get(0), "count");
				}
				ApplyedTechLevel applyedTechLevel = new ApplyedTechLevel();
				applyedTechLevel.setCount(count);
				applyedTechLevel.setTechLevel(level);
				//判断当前工种和等级是否已经勾选
				boolean b = xm2Skills.stream().filter(x -> x.getProfessionId().equals(profession.getId())
						&& x.getTechLevel() == applyedTechLevel.getTechLevel()).count() > 0;
				applyedTechLevel.setChecked(b);
				techLevels.add(applyedTechLevel);
			}
		}
		return applyedProfessions;
	}

	/**
	 * 保存职业、工种的选择信息,并初始化生成一个技能类的培训项目
	 */
	@Transactional
	public String saveProfessions(ApplyedProfessionDto applyedProfessionDto, String userId) {
		Plan plan = planMapper.selectById(applyedProfessionDto.getPlanId());
		PlanDetail planDetail = null;
		if (StringUtils.isEmpty(applyedProfessionDto.getId())) {
			planDetail = new PlanDetail();
			planDetail.setId(BaseUtil.generateId2());
			planDetail.setPlanId(applyedProfessionDto.getPlanId());
			planDetail.setName(plan.getName());
			planDetail.setStatus(AuditStatus.DRAFT);
			planDetail.setSaveTime(new Date());
			planDetail.setUserId(userId);
			this.savePlanDetail(planDetail);
		} else {
			planDetail = mapper.selectById(applyedProfessionDto.getId());
		}
		// 删除之前的项目与技能的关系
		EntityWrapper<ZypxXm2Skill> xm2skillWrapper = new EntityWrapper();
		xm2skillWrapper.eq("xm_id", planDetail.getId());
		xm2SkillMapper.delete(xm2skillWrapper);
		// 写入新的关系
		for (ApplyedProfession applyedProfession : applyedProfessionDto.getProfessions()) {
			if (applyedProfession.isChecked()) {
				String professionId = applyedProfession.getId();
				ZyjdProfession zyjdProfession = professionMapper.selectById(professionId);
				if (applyedProfession.getTechLevels() != null) {
					for (ApplyedTechLevel applyedTechLevel : applyedProfession.getTechLevels()) {
						if (applyedTechLevel.isChecked()) {
							//校验当前职业、等级是否在同计划下的其它项目中被申报
							this.checkProfessionExistsInOtherXm(plan.getId(), zyjdProfession, applyedTechLevel.getTechLevel(), planDetail.getId());
							// 保存项目技能的关联关系
							ZypxXm2Skill xm2Skill = new ZypxXm2Skill();
							xm2Skill.setId(BaseUtil.generateId2());
							xm2Skill.setXmId(planDetail.getId());
							xm2Skill.setIndustryId(zyjdProfession.getIndustryId());
							xm2Skill.setProfessionId(professionId);
							xm2Skill.setTechLevel(applyedTechLevel.getTechLevel());
							xm2SkillMapper.insert(xm2Skill);

							//保存选中的模板项目的课程
							InfXm infXm = infXmService.selectById(applyedProfession.getXmId());
							if (BaseUtil.isNotEmpty(infXm)){
								EntityWrapper<Xm2Course> wrapper = new EntityWrapper<>();
								wrapper.eq("xm_id",infXm.getId());
								List<Xm2Course> xmCourses = xm2CourseService.selectList(wrapper);

								//查询项目的课程id
								EntityWrapper<ZypxXmCourseSetting> xmCourseSetting = new EntityWrapper<>();
								xmCourseSetting.eq("xm_id",planDetail.getId());
								xmCourseSetting.eq("tech_level",applyedTechLevel.getTechLevel());
								List<String> coursesId = xmCourseSettingService.selectList(xmCourseSetting).stream().map(ZypxXmCourseSetting::getCourseId).collect(Collectors.toList());

								ArrayList<ZypxXmCourseSetting> list = new ArrayList<>();
								for (Xm2Course xmCourse: xmCourses) {
									if (coursesId.contains(xmCourse.getCourseId())){
										continue;
									}
									ZypxXmCourseSetting zypxXmCourseSetting = new ZypxXmCourseSetting();
									zypxXmCourseSetting.setId(BaseUtil.generateId());
									zypxXmCourseSetting.setXmId(planDetail.getId());
									zypxXmCourseSetting.setCourseId(xmCourse.getCourseId());
									zypxXmCourseSetting.setProfessionId(applyedProfession.getId());
									zypxXmCourseSetting.setTechLevel(applyedTechLevel.getTechLevel());
									zypxXmCourseSetting.setCreateTime(DateUtils.now());
									zypxXmCourseSetting.setCreatorId(userId);
									zypxXmCourseSetting.setIsCourseware(Constants.YES);
									list.add(zypxXmCourseSetting);
								}
								DBUtils.insertBatch(list,ZypxXmCourseSetting.class);
							}
						}
					}
				}
			}
		}
		return planDetail.getId();
	}

	/**
	 * 导入课程
	 */
	public Map<String, Object> importCourse(MultipartFile file) throws IOException, BiffException {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[] { "DATE", "TIME", "COURSE_NAME",
				"LESSON", "HOURS", "LEARNING_TYPE", "TEACHER_NAME", "TEACHER_PHONE", "ADDRESS" });
		if (list == null || list.size() <= 1) {
			throw BizException.withMessage("导入失败，文件中没有数据");
		}
		int row = 0;
		int cfCou = 0;// 重复
		int cwCou = 0;// 错误
		int success = 0;// 成功
		Set<String> repeatCheck = new HashSet<String>();
		Map<String, CourseSettingItem> allCourse = new LinkedHashMap();// 所有的课程
		for (Map<String, String> map : list) {
			row++;
			// 第一行是标题，放过
			if (row < 2) {
				continue;
			}

			String courseDate = BaseUtil.getStringValueFromMap(map, "DATE");
			String courseTime = BaseUtil.getStringValueFromMap(map, "TIME");
			String courseName = BaseUtil.getStringValueFromMap(map, "COURSE_NAME", "");
			String lesson = BaseUtil.getStringValueFromMap(map, "LESSON", "");
			String hours = BaseUtil.getStringValueFromMap(map, "HOURS");
			String learningType = BaseUtil.getStringValueFromMap(map, "LEARNING_TYPE");
			String teacherName = BaseUtil.getStringValueFromMap(map, "TEACHER_NAME");
			String teacherPhone = BaseUtil.getStringValueFromMap(map, "TEACHER_PHONE");
			String address = BaseUtil.getStringValueFromMap(map, "ADDRESS");
			if (StringUtils.isEmpty(courseDate)) {
				log.append("<br>");
				String msg = "第" + row + "行日期不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(courseTime)) {
				log.append("<br>");
				String msg = "第" + row + "行时间不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String[] timeArray = StringUtils.split(courseTime, "-");
			if (timeArray == null || timeArray.length != 2) {
				log.append("<br>");
				String msg = "第" + row + "行时间格式错误，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String startTime = courseDate + " " + timeArray[0];
			String endTime = courseDate + " " + timeArray[1];
			if (startTime.equals(endTime)) {
				log.append("<br>");
				String msg = "第" + row + "行起止时间必须不一样，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(courseName)) {
				log.append("<br>");
				String msg = "第" + row + "行课程不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(lesson)) {
				log.append("<br>");
				String msg = "第" + row + "行培训内容不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(hours)) {
				log.append("<br>");
				String msg = "第" + row + "行课时不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (StringUtils.isEmpty(learningType)) {
				log.append("<br>");
				String msg = "第" + row + "行培训方式不能为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (repeatCheck.contains(courseName + lesson)) {
				log.append("<br>");
				String msg = "第" + row + "行课程重复，忽略这条数据。";
				log.append(msg);
				// 标记重复数据
				cfCou++;
				continue;
			} else {
				repeatCheck.add(courseName + lesson);
			}

			String courseId = null;// 导入的课程一律认为是新加的课程，courseId默认都是null

			EntityWrapper<User> userWrapper = new EntityWrapper<>();
			userWrapper.eq("name", teacherName);
			userWrapper.eq("mobile", teacherPhone);
			List<User> users = userMapper.selectList(userWrapper);
			String teacherId = null;
			if (CollectionUtils.isNotEmpty(users)) {
				teacherId = users.get(0).getId();
			}

			if (!allCourse.containsKey(courseName)) {
				CourseSettingItem courseSettingItem = new CourseSettingItem();
				courseSettingItem.setCourseId(courseId);
				courseSettingItem.setCourseName(courseName);
				courseSettingItem.setTeacherId(teacherId);
				courseSettingItem.setTeacherName(teacherName);
				courseSettingItem.setTeacherPhone(teacherPhone);
				courseSettingItem.setAddress(address);
				courseSettingItem.setStartTime(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
				courseSettingItem.setEndTime(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
				courseSettingItem.setHours(Double.valueOf(hours));
				courseSettingItem.setTeacherId(teacherId);
				allCourse.put(courseName, courseSettingItem);// 一对一关系
			}
			// 添加课时
			if (Constants.TrainingType.ZB.equals(learningType)) {
				allCourse.get(courseName).setIsLive(Constants.YES);
				if (allCourse.get(courseName).getCourselive() == null) {
					Courselive courselive = new Courselive();
					Chapter chapter = new Chapter();
					chapter.setName(courseName);
					courselive.addChapter(chapter);
					allCourse.get(courseName).setCourselive(courselive);
				}
				Lesson lessonObject = new Lesson();
				lessonObject.setZbbt(StringUtils.isEmpty(lesson) ? courseName : lesson);
				lessonObject.setKssj(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setJssj(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setJsid(teacherId);
				lessonObject.setJsxm(teacherName);
				lessonObject.setJssjh(teacherPhone);
				lessonObject.setAddress(address);
				if (allCourse.get(courseName).getStartTime().after(lessonObject.getKssj())) {
					allCourse.get(courseName).setStartTime(lessonObject.getKssj());
				}
				if (allCourse.get(courseName).getEndTime().before(lessonObject.getJssj())) {
					allCourse.get(courseName).setEndTime(lessonObject.getJssj());
				}
				allCourse.get(courseName).getCourselive().getChapters().get(0).addLesson(lessonObject);
			} else if (Constants.TrainingType.MS.equals(learningType)) {
				allCourse.get(courseName).setIsMs(Constants.YES);
				if (allCourse.get(courseName).getCourseMs() == null) {
					CourseMs courseMs = new CourseMs();
					com.xunw.jxjy.model.common.coursems.Chapter chapter = new com.xunw.jxjy.model.common.coursems.Chapter();
					chapter.setName(courseName);
					courseMs.addChapter(chapter);
					allCourse.get(courseName).setCourseMs(courseMs);
				}
				com.xunw.jxjy.model.common.coursems.Lesson lessonObject = new com.xunw.jxjy.model.common.coursems.Lesson();
				lessonObject.setName(StringUtils.isEmpty(lesson) ? courseName : lesson);
				lessonObject.setStartTime(DateUtils.parse(startTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setEndTime(DateUtils.parse(endTime, "yyyy-MM-dd HH:mm"));
				lessonObject.setTeacherId(teacherId);
				lessonObject.setTeacherName(teacherName);
				lessonObject.setTeacherPhone(teacherPhone);
				lessonObject.setAddress(address);
				if (allCourse.get(courseName).getStartTime().after(lessonObject.getStartTime())) {
					allCourse.get(courseName).setStartTime(lessonObject.getStartTime());
				}
				if (allCourse.get(courseName).getEndTime().before(lessonObject.getEndTime())) {
					allCourse.get(courseName).setEndTime(lessonObject.getEndTime());
				}
				allCourse.get(courseName).getCourseMs().getChapters().get(0).addLesson(lessonObject);
			} else {
				log.append("<br>");
				String msg = "第" + row + "行培训方式错误，仅支持线上直播、线下面授，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			success++;
		}
		StringBuffer message = new StringBuffer(
				"总共" + (row - 1) + "条，导入成功" + success + "，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("message", message);
		result.put("list", allCourse.values());
		return result;
	}

	/**
	 * 生成项目编号
	 */
	public String buildXmSerialNumber(String hostOrgId) {
		String prefix = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.XM_SERIAL_NUMBER_PREFIX, hostOrgId);
		String start = systemSettingService.getSysSettingByHostOrg(HostorgSettingEnum.XM_SERIAL_NUMBER_START, hostOrgId);
		if(StringUtils.isNotEmpty(prefix) && StringUtils.isNotEmpty(start)) {
			String maxSerialNumber = mapper.getMaxSerialNumber(prefix, hostOrgId);
			maxSerialNumber = StringUtils.isEmpty(maxSerialNumber) ? start : maxSerialNumber.substring(prefix.length());
			return BaseUtil.nextSerialNumber(prefix,  maxSerialNumber);
		}
		else {
			String code = BaseUtil.generateRandomEnglishLetterString(2).toUpperCase();
	 		code += new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
	 		return code;
		}
	}
	
	/**
	 * 校验当前培训计划下的同职业、同等级只能够在一个培训项目中申报
	 */
	protected void checkProfessionExistsInOtherXm(String planId, ZyjdProfession profession, TechLevel techLevel, String newXmId) {
		List<String> xmIds = mapper.getXmApplyByProfession(planId, profession.getId(), techLevel);
		if (xmIds.size() > 0 && !xmIds.contains(newXmId)) {
			throw BizException.withMessage(profession.getName()+ "(" + techLevel.getName() + ")已经被其他用户申报，请勿勾选");
		}
	}

	/**
	 * 移动端获取项目列表
	 */
    public Map<String, Object> list(String year, AuditStatus status, String keyword, String hostOrgId, String userId) {
		List<Map<String, Object>> list = mapper.list(year, status, keyword, hostOrgId, userId);
		Map<String, Object> result = new LinkedHashMap<>();
		result.put("notStartList", list.stream().filter(i->i.get("startTime") == null || new Date().before(DateUtils.parse((String) i.get("startTime"),"yyyy-MM-dd")))
				.sorted(Comparator.comparing(o -> o.get("submitTime") != null ? (Date) o.get("submitTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
		result.put("progressingList", list.stream().filter(i->i.get("startTime") != null && i.get("endTime") != null &&new Date().after(DateUtils.parse((String) i.get("startTime"),"yyyy-MM-dd")) && new Date().before(DateUtils.parse((String) i.get("endTime"),"yyyy-MM-dd")))
				.sorted(Comparator.comparing(o -> o.get("submitTime") != null ? (Date) o.get("submitTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
		result.put("endList", list.stream().filter(i->i.get("endTime") != null && new Date().after(DateUtils.parse((String) i.get("endTime"),"yyyy-MM-dd")))
				.sorted(Comparator.comparing(o -> o.get("submitTime") != null ? (Date) o.get("submitTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
		return result;
    }

	/**
	 * 获取年度信息
	 */
	public List<String> getYears() {
		return mapper.getYears();
	}

	public List<PlanTreeNode> getPlanTree(PlanQueryParams params) {
		List<ZypxPlanVo> list = planMapper.queryAll(params.getCondition());

		Map<String, PlanTreeNode> yearsMap = new HashMap<>();
		Map<String, PlanTreeNode> typeNameMap = new HashMap<>();
		Map<String, PlanTreeNode> batchNameMap = new HashMap<>();
		// 先遍历对象列表，创建节点并保存到映射中
		for (ZypxPlanVo zypxPlanVo : list) {
			String years = zypxPlanVo.getYears();
			PlanTreeNode yearNode = yearsMap.computeIfAbsent(years, id -> {
				PlanTreeNode planTreeNode = new PlanTreeNode();
				planTreeNode.setId(id);
				planTreeNode.setName(id);
				return planTreeNode;
			});

			String typeName = zypxPlanVo.getTypeName();
			String isSkill = zypxPlanVo.getIsSkill();
			String planId = zypxPlanVo.getId();
			if (!StringUtils.equals(zypxPlanVo.getIsSkill(), "1")) {
				String typeId = zypxPlanVo.getTypeId();
				PlanTreeNode typeNode = typeNameMap.computeIfAbsent(typeId, id -> {
					PlanTreeNode planTreeNode = new PlanTreeNode();
					planTreeNode.setId(typeId);
					planTreeNode.setName(typeName);
					planTreeNode.setIsSkill(isSkill);
					planTreeNode.setPlanId(planId);
					return planTreeNode;
				});
				if (!yearNode.getChildren().contains(typeNode)) {
					yearNode.getChildren().add(typeNode);
				}
			} else {
				String bmbatchId = zypxPlanVo.getBmbatchId();
				String bmbatchName = zypxPlanVo.getBmbatchName();
				PlanTreeNode batchNode = batchNameMap.computeIfAbsent(bmbatchId, id -> {
					PlanTreeNode planTreeNode = new PlanTreeNode();
					planTreeNode.setId(bmbatchId);
					planTreeNode.setName(typeName + "(" + bmbatchName + ")");
					planTreeNode.setIsSkill(isSkill);
					planTreeNode.setPlanId(planId);
					return planTreeNode;
				});
				if (!yearNode.getChildren().contains(batchNode)) {
					yearNode.getChildren().add(batchNode);
				}
			}
		}
		return yearsMap.values().stream().sorted(Comparator.comparing(PlanTreeNode::getName).reversed()).collect(Collectors.toList());
	}
}
