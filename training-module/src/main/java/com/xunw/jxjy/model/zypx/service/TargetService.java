package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.zypx.entity.Target;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.mapper.*;
import com.xunw.jxjy.model.zypx.params.TargetQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.docx4j.wml.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TargetService extends BaseCRUDService<TargetMapper, Target>{

	@Autowired
	private TargetTypeMapper targetTypeMapper;
	@Autowired
	private TargetResultMapper targetResultMapper;
	@Autowired
	private ZypxXmCourseSettingMapper zypxXmCourseSettingMapper;
	@Autowired
	private CourseMapper courseMapper;
    @Autowired
    private ZypxXmMapper zypxXmMapper;

	// 查询
	public Page pageQuery(TargetQueryParams params) {
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 获取指标详情
	 */
	public Object detail(String id, String xmId, String courseSettingId, String studentId) {
		Map<String, Object> result = new HashMap<>();
		ZypxXm zypxXm = zypxXmMapper.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("项目id参数异常");
		}
		TargetType targetType = targetTypeMapper.selectById(id);
		if (targetType == null) {
			throw BizException.withMessage("分类id参数异常");
		}
		if (Constants.YES.equals(targetType.getIsCourseTarget()) && BaseUtil.isEmpty(courseSettingId)) {
			throw BizException.withMessage("参数缺失：courseSettingId");
		}
		ZypxXmCourseSetting xmCourseSetting = null;
		Course course = null;
		if (BaseUtil.isNotEmpty(courseSettingId)) {
			xmCourseSetting = zypxXmCourseSettingMapper.selectById(courseSettingId);
			if (xmCourseSetting == null) {
				throw BizException.withMessage("courseSettingId参数异常");
			}
			if (!Constants.YES.equals(xmCourseSetting.getIsOpenCourseTarget())) {
				throw BizException.withMessage("课程暂未开启课程评价");
			}
			course = courseMapper.selectById(xmCourseSetting.getCourseId());
		}
		List<TargetType> childrenList = new ArrayList<>();
		List<Map<String, Object>> targets = new ArrayList<>();
		if (Constants.YES.equals(targetType.getIsCourseTarget())) {
			childrenList = targetTypeMapper.selectList(new EntityWrapper<TargetType>()
					.eq("parent_id", id)
					.orderBy("sort", true));
			if (childrenList.isEmpty()) {
				throw BizException.withMessage("尚未设置评价指标");
			}
			childrenList.forEach(c->{
				List<String> typeIds = new ArrayList<>();
				typeIds.add(c.getId());
				c.setTargets(mapper.getAllTargetByTypeIds(typeIds, xmId));
			});
			childrenList = childrenList.stream().filter(c->!c.getTargets().isEmpty()).collect(Collectors.toList());
		} else {
			//获取设置的所有指标
			List<String> typeIds = new ArrayList<>();
			typeIds.add(id);
			targets = mapper.getAllTargetByTypeIds(typeIds, xmId);
			if (CollectionUtils.isEmpty(targets)) {
				throw BizException.withMessage("尚未设置评价指标");
			}
		}
		if (targetResultMapper.checkReDo(id, xmId, courseSettingId, studentId) > 0) {
			throw BizException.withMessage("请勿重复评价");
		}
		result.put("targets", targets);
		result.put("teacherTargets", childrenList);
		result.put("logo", course == null ? null : course.getLogo());
		result.put("courseId", course == null ? null : course.getId());
		result.put("courseName", course == null ? null : course.getName());
		result.put("courseSettingId", courseSettingId);
		return result;
	}
}
