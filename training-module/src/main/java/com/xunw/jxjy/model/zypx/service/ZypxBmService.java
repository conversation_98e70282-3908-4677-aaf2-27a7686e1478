package com.xunw.jxjy.model.zypx.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.google.common.collect.Lists;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.*;
import com.xunw.jxjy.common.utils.Constants.PayType;
import com.xunw.jxjy.model.common.entity.CommClass;
import com.xunw.jxjy.model.common.entity.CommOrder;
import com.xunw.jxjy.model.common.entity.CommOrderDetail;
import com.xunw.jxjy.model.common.mapper.CommClassMapper;
import com.xunw.jxjy.model.common.mapper.CommOrderDetailMapper;
import com.xunw.jxjy.model.common.mapper.CommOrderMapper;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.dto.BandCardNumberDto;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.FormData;
import com.xunw.jxjy.model.inf.entity.FormField;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.CourseLiveMapper;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.service.FormDataService;
import com.xunw.jxjy.model.inf.service.FormFieldService;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.personal.mapper.StudentBmMapper;
import com.xunw.jxjy.model.personal.params.StudentPaperQueryParams;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.personal.service.ZypxStudentPaperService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.service.DictService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.entity.*;
import com.xunw.jxjy.model.zypx.mapper.*;
import com.xunw.jxjy.model.zypx.params.ZypxBmQueryParams;
import com.xunw.jxjy.model.zypx.params.ZypxXmQueryParams;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class ZypxBmService extends BaseCRUDService<ZypxBmMapper, ZypxBm>{

	private static final Logger logger = LoggerFactory.getLogger(ZypxBmService.class);

	private static final String [] tableHeader = {"姓名","身份证","手机号","邮箱","发票抬头","纳税人识别号","发票-开户行","发票-银行账号","发票-单位地址","发票-单位电话"};

	private static final String createProductPayURL = "https://core.shenqimuti.cn/product/CreateProductPay";

	@Autowired
	private ZypxBmCourseMapper bmCourseMapper;
	@Autowired
	private ZypxXmCourseSettingMapper xmCourseSettingMapper;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private CommClassMapper commClassMapper;
	@Autowired
	private ZypxXmService xmService;
	@Autowired
	private StudentBmMapper studentBmMapper;
	@Autowired
	private DictService dictService;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private StudentInfoService infoService;
	@Autowired
	private ZypxStudentBmService studentBmService;
	@Autowired
	private FormDataService formDataService;
	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private FormFieldService formFieldService;
	@Autowired
	private FormService formService;
	@Autowired
	private CourseLiveMapper courseLiveMapper;
	@Autowired
	private PlanMapper planMapper;
	@Autowired
	private ZypxStudentPaperService zypxStudentPaperService;
    @Autowired
    private CommOrderDetailMapper commOrderDetailMapper;
    @Autowired
    private CommOrderMapper commOrderMapper;
    @Autowired
    private ZypxXmBmCardNumberMapper zypxXmBmCardNumberMapper;


	/**
	 * 培训计划-报名管理-分页查询
	 */
	public Page pageQuery(ZypxBmQueryParams params) {
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 报名省市区分页查询
	 */
	public Page areaApplyStatList(ZypxBmQueryParams params) {
		List<Map<String, Object>> list = mapper.areaApplyStatList(params.getCondition(), params);
		params.setRecords(list);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("data", params);
		Integer areaCount = 0;
		Map<String, Object> totalMap = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			areaCount += BaseUtil.getIntValueFromMap(map, "districtCount", 0);
		}
		totalMap.put("district", "合计：");
		totalMap.put("districtCount", areaCount);
		list.add(totalMap);
		return params;
	}

	/**
	 * 培训计划-报名管理-批量确认
	 */

	public Map<String, Object> batchUpdate(List<String> bmIdList) {
		Map<String, Object> result = new HashMap<>();
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper<ZypxBm>();
		wrapper.in("id", bmIdList);
		List<ZypxBm> list = mapper.selectList(wrapper);
		for (ZypxBm bizZypxBm : list) {
			bizZypxBm.setStatus(BmStatus.BMCG);
		}
		DBUtils.updateBatchById(list, 10, ZypxBm.class);
		result.put("code", 0);
		result.put("message", "成功确认报名:" + list.size() + "条");
		return result;
	}

	/**
	 * 培训计划-报名管理-批量删除
	 */
	@Transactional
	public Map<String, Object> batchDel(List<String> bmIdList) {
		Map<String, Object> result = new HashMap<>();
		int count = bmIdList.size();
		if (bmIdList.size() > 0) {
			for (String bmId : bmIdList) {
				ZypxBm zypxBm = mapper.selectById(bmId);
				if (Constants.YES.equals(zypxBm.getIsPayed())) {
					throw BizException.withMessage("已缴费的报名数据不可以删除");
				}
				mapper.deleteById(bmId);
				EntityWrapper<FormData> wrapper = new EntityWrapper();
				wrapper.eq("xm_id", zypxBm.getXmId());
				wrapper.eq("student_id", zypxBm.getStudentId());
				formDataService.deleteList(wrapper);
				EntityWrapper<ZypxBmCourse> courseWrapper = new EntityWrapper();
				courseWrapper.eq("bm_id", bmId);
				bmCourseMapper.delete(courseWrapper);
			}
		}
		result.put("code", 0);
		result.put("message", "已成功删除:" + count + "条报名数据");
		return result;

	}

	public Map<String, Object> getBmDetailsByBmId(String bmId) throws Exception {
		Map<String, Object> result = new HashMap<>();
		ZypxBm zypxBm = mapper.selectById(bmId);
		// 个人信息
		StudentInfo studentInfo = infoService.getByStudentId(zypxBm.getStudentId());
		StudentUser user = studentUserService.selectById(zypxBm.getStudentId());
		ZypxXm zypxXm = xmService.selectById(zypxBm.getXmId());
		result.put("studentInfo", studentInfo);
		result.put("studentUser", user);
		result.put("bmStatus", zypxBm.getStatus());
		result.put("shiftDuty", zypxBm.getShiftDuty());
		result.put("xm", zypxXm);
		//取自定义的采集信息
		String formId = formService.getFormByXmId(zypxXm.getId());
		if (StringUtils.isNotEmpty(formId)) {
			result.put("fields", studentBmService.getFormFilledData(zypxBm.getXmId(), formId, zypxBm.getStudentId()));
		}
		else {
			result.put("fields", Collections.EMPTY_LIST);
		}
		return result;
	}

	@Transactional
	public void doApprove(String bmId, BmStatus bmStatus, String userId, String approveAdvice) {
		ZypxBm zypxBm = selectById(bmId);
		if (zypxBm == null) {
			throw BizException.withMessage("报名信息不存在");
		}
		zypxBm.setApproveUserId(userId);
		zypxBm.setUpdateTime(new Date());
		zypxBm.setStatus(bmStatus);
		zypxBm.setApproveAdvice(approveAdvice);
		updateById(zypxBm);
	}

	/**
	 * 培训计划-报名管理-批量注册+报名导入
	 */
	@Transactional
	public Map<String, Object> batchImportAndRegist(MultipartFile file, String userId, String xmId, String orgId,
			String isOffline, String isSyncChooseCourse, String hostOrgId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		ZypxXm zypxXm = xmService.selectById(xmId);
		List<Map<String, Object>> courseList = xmCourseSettingMapper.getAllCourseByXmId(xmId);
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "NAME", "SFZH", "MOBILE", "GENDER", "TYPE", "COMPANY", "ZW", "EDUCATION", "SCHOOL",
						"COLLEGE", "SPECIALTY", "CLASSZ", "STUDENTNUM" });
		logger.info("--待导入数据"+(list.size() - 1) +"条");
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		Set<String> sfzList = new HashSet<>();
		Set<String> mobileList = new HashSet<>();
		int successRegisterCount = 0;//注册成功的条数
		int cfRegisterCount = 0;//Excel中重复的数据条数
		int errorRegisterCount = 0;//Excel中错误的数据条数
		int alreadyRegisterCount = 0;//系统中已经存在的条数

		int successBmCount = 0;//报名成功的条数
		int existsBmCount = 0;//系统中已经报名的条数

		int row = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			String name = StringUtils.trimToNull(map.get("NAME"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}
			String sfzh = StringUtils.trimToNull(map.get("SFZH"));
			if (StringUtils.isNotEmpty(sfzh)) {
				if (sfzList.contains(sfzh)) {
					log.append("<br>");
					String msg = "<span style=\"color:red\">第" + row + "行身份证在Excel中重复，请仔细检查，忽略这条数据。</span>";
					log.append(msg);
					cfRegisterCount++;
					continue;
				} else {
					sfzList.add(sfzh);
					if (!BaseUtil.isIDCardNumber(sfzh)) {
						String msg = "<span style=\"color:red\">第" + row + "行不是正确的身份证号，忽略这条数据。</span>";
						log.append("<br>");
						log.append(msg);
						errorRegisterCount++;
						continue;
					}
				}
			}
			String mobile = StringUtils.trimToNull(map.get("MOBILE"));
			if (StringUtils.isNotEmpty(mobile)) {
				if (mobileList.contains(mobile)) {
					String msg = "<span style=\"color:red\">第" + row + "行手机号在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfRegisterCount++;
					continue;
				} else {
					mobileList.add(mobile);
					if (!BaseUtil.isMobile(mobile)) {
						String msg = "<span style=\"color:red\">第" + row + "行不是正确的手机号，忽略这条数据。</span>";
						log.append("<br>");
						log.append(msg);
						errorRegisterCount++;
						continue;
					}
				}
			}
			if (StringUtils.isEmpty(sfzh) && StringUtils.isEmpty(mobile)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证、手机号不能同时为空，请仔细检查，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}
			String gender = StringUtils.trimToNull(map.get("GENDER"));
			if (StringUtils.isNotEmpty(gender) && Gender.findByName(gender) == null) {
				log.append("<br>");
				String msg = "第" + row + "行性别填写错误，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}

			String studentType = StringUtils.trimToNull(map.get("TYPE"));
			if (StringUtils.isEmpty(studentType)) {
				log.append("<br>");
				String msg = "第" + row + "行学员类型为空，请仔细检查，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}
			if (StudentType.findByName(studentType) == null) {
				log.append("<br>");
				String msg = "第" + row + "行学员类型填写错误，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}
			String company = StringUtils.trimToNull(map.get("COMPANY"));
			String zw = StringUtils.trimToNull(map.get("ZW"));// 职务
			String education = StringUtils.trimToNull(map.get("EDUCATION"));// 学历
			if (StringUtils.isNotEmpty(education) && Education.findByName(education) == null) {
				log.append("<br>");
				String msg = "第" + row + "行学历填写错误，忽略这条数据。";
				log.append(msg);
				errorRegisterCount++;
				continue;
			}
			StudentUser studentUser = studentUserService.findBySfzhOrMobile(sfzh, mobile, hostOrgId);
			String studentnum = StringUtils.trimToNull(map.get("STUDENTNUM"));
			String school = StringUtils.trimToNull(map.get("SCHOOL"));// 毕业学校
			String college = StringUtils.trimToNull(map.get("COLLEGE"));// 所在院系
			String specialty = StringUtils.trimToNull(map.get("SPECIALTY"));// 所学专业
			String classz = StringUtils.trimToNull(map.get("CLASSZ"));// 所在班级
			if (studentUser == null) {//新增学员
				studentUser = new StudentUser();
				studentUser.setId(BaseUtil.generateId2());
				String password = null;
				if (StringUtils.isNotEmpty(sfzh)) {
					password = sfzh.substring(sfzh.length() - 6, sfzh.length());
				} else if (StringUtils.isNotEmpty(mobile)) {
					password = mobile.substring(mobile.length() - 6, mobile.length());
				}
				studentUser.setPassword(DigestUtils.md5Hex(password));
				studentUser.setStatus(AccountStatus.OK);
				studentUser.setStudentType(StudentType.findByName(studentType));
				studentUser.setCompany(company);
				if (StringUtils.isNotEmpty(mobile)) {
					studentUser.setIsBindMobile(Constants.YES);
				} else {
					studentUser.setIsBindMobile(Constants.NO);
				}
				studentUser.setRegHostOrgId(hostOrgId);
				studentUserMapper.insert(studentUser);
				// 写入学员信息表
				StudentInfo studentInfo = new StudentInfo();
				studentInfo.setId(BaseUtil.generateId2());
				studentInfo.setStudentId(studentUser.getId());
				studentInfo.setSfzh(sfzh);
				studentInfo.setName(name);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender)
						: Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setMobile(mobile);
				studentInfo.setZw(zw);
				if (studentUser.getStudentType() == StudentType.SOCIAL) {
					studentInfo.setKsly(Ksly.QYZG);
				}
				else if (studentUser.getStudentType() == StudentType.SCHOOL) {
					studentInfo.setKsly(Ksly.YXXS);
				}
				else {
					studentInfo.setKsly(Ksly.QITA);
				}
				studentInfo.setGraduateSchool(school);
				studentInfo.setCollege(college);
				studentInfo.setStudentNum(studentnum);
				studentInfo.setSpecialty(specialty);
				studentInfo.setClassz(classz);
				studentInfo.setCreateTime(new Date());
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfo.setRegHostOrgId(hostOrgId);
				studentInfoMapper.insert(studentInfo);

				successRegisterCount++;
			}
			else {
				//更新已经存在的学员
				studentUser.setStudentType(StudentType.findByName(studentType));
				studentUser.setCompany(company);
				studentUserMapper.updateById(studentUser);
				StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentUser.getId());
				studentInfo.setName(name);
				studentInfo.setMobile(mobile);
				studentInfo.setSfzh(sfzh);
				studentInfo.setGender(StringUtils.isNotEmpty(gender) ? Gender.findByName(gender)
						: Gender.findByEnumName(BaseUtil.parseGender(sfzh)));
				studentInfo.setZw(zw);
				studentInfo.setGraduateSchool(school);
				studentInfo.setCollege(college);
				studentInfo.setStudentNum(studentnum);
				studentInfo.setSpecialty(specialty);
				studentInfo.setClassz(classz);
				studentInfo.setEducation(StringUtils.isNotEmpty(education) ? Education.findByName(education) : null);
				studentInfoMapper.updateById(studentInfo);

//				alreadyRegisterCount++;
			}

			// 同考生同一项目不能重复报名
			EntityWrapper<ZypxBm> bizZypxBmEntityWrapper = new EntityWrapper<>();
			bizZypxBmEntityWrapper.eq("xm_id", xmId);
			bizZypxBmEntityWrapper.eq("student_id", studentUser.getId());
			List<ZypxBm> bizZypxBmList = mapper.selectList(bizZypxBmEntityWrapper);
			if (bizZypxBmList.size() > 0) {
				log.append("<br>");
				String msg = "第" + row + "行考生" + name + "在您选择的培训项目下已经报名，忽略这条数据。";
				log.append(msg);
				existsBmCount++;
				continue;
			}
			Integer alreadyBmCount = xmService.getBmCountByXmId(xmId);
			if (zypxXm.getLimitCount() != null && alreadyBmCount >= zypxXm.getLimitCount()) {
				log.append("导入终止，项目报名的学员数量已经超过限制数量【" + zypxXm.getLimitCount() + "】");
				break;
			}
			// 构造对象
			ZypxBm bizZypxBm = new ZypxBm();
			bizZypxBm.setId(BaseUtil.generateId2());
			bizZypxBm.setJtbmOrgId(orgId);
			bizZypxBm.setTime(new Date());
			bizZypxBm.setJtbmUserId(userId);
			bizZypxBm.setStatus(BmStatus.BMCG);
			bizZypxBm.setStudentId(studentUser.getId());
			bizZypxBm.setIsJtbm(Constants.YES);
			bizZypxBm.setIsOffline(isOffline);
			bizZypxBm.setXmId(xmId);
			mapper.insert(bizZypxBm);
			successBmCount++;
			//同步选课信息
			if (Objects.equals(isSyncChooseCourse, Constants.YES)) {
				// 写入报名课程表
				List<ZypxBmCourse> bmCoucrseList = new ArrayList<ZypxBmCourse>();
				for (Map<String, Object> course : courseList) {
					ZypxBmCourse bmCourse = new ZypxBmCourse();
					bmCourse.setId(BaseUtil.generateId2());
					bmCourse.setBmId(bizZypxBm.getId());
					bmCourse.setCourseSettingId(BaseUtil.getStringValueFromMap(course, "id"));
					bmCourse.setBmTime(new Date());
					bmCoucrseList.add(bmCourse);
				}
				DBUtils.insertBatch(bmCoucrseList, ZypxBmCourse.class);
			}
		}
		StringBuffer message = new StringBuffer("<div>总共" + (row - 1) + "条，成功注册学员数据" + successRegisterCount
				+ "条，系统已经存在的学员数据"+alreadyRegisterCount+"条；<br>Excel中重复的数据" + cfRegisterCount + "条，错误注册数据" + errorRegisterCount + "条；" + "<br>成功导入报名数据"
				+ successBmCount + "条，系统已经存在的报名数据" + existsBmCount + "条；</div>");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	/**
	 * 培训计划-报名管理-批量导出
	 */
	public void batchExport(List<Map<String, Object>> records, OutputStream os) throws Exception {
		Map<String, Object> allRegion = dictService.getAllRegionByMap();
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("报名汇总表", 0);
		int row = 0;
		int i = 0;
		ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 20);
		ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 20);
		ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 20);
		ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 20);
		ws.addCell(new Label(i, row, "工作单位", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "单位所在省", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "单位所在市", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "单位所在区", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "职称", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "职务", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		ws.addCell(new Label(i, row, "学校", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 15);
		ws.addCell(new Label(i, row, "院系", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 15);
		ws.addCell(new Label(i, row, "专业", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 15);
		ws.addCell(new Label(i, row, "班级", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 15);
		ws.addCell(new Label(i, row, "报名类型", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费状态", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费金额", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费时间", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "订单编号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "聚合支付订单编号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "报名时间", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		row = 1;
		for (Map<String, Object> map : records) {
			if (map == null) {
				continue;
			} else {
				int col = 0;
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("serialNumber") != null ? map.get("serialNumber") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("title") != null ? map.get("title") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
						map.get("gender") != null ? Gender.findByEnumName(map.get("gender").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("company") != null ? map.get("company") : ""),
						OfficeToolExcel.getNormolCell()));

				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								allRegion.get(BaseUtil.getStringValueFromMap(map, "provinceCode", ""))),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(allRegion.get(BaseUtil.getStringValueFromMap(map, "cityCode", ""))),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								allRegion.get(BaseUtil.getStringValueFromMap(map, "districtCode", ""))),
						OfficeToolExcel.getNormolCell()));

				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								map.get("zc") != null ? Constants.ZC.getName(map.get("zc").toString()) : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(
						new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("zw") != null ? map.get("zw") : ""),
								OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map, "graduateSchool")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map, "college")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map, "specialty")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(BaseUtil.getStringValueFromMap(map, "classz")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "bmlx")) ? "集体" : "个人"),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "isPayed")) ? "已缴费" : "未缴费"),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "amount", "")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "payTime","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "orderNumber","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "unionOrderNo","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("bmTime") != null
								? DateUtils.format((Date) map.get("bmTime"), "yyyy-MM-dd HH:mm:ss")
								: ""),
						OfficeToolExcel.getNormolCell()));
			}
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 培训计划-报名管理-批量导出-含自定义表单
	 */
	public void batchExportWithFormData(String xmId, List<Map<String, Object>> records, OutputStream os) throws Exception {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("报名汇总表", 0);
		int row = 0;
		int i = 0;
		ws.addCell(new Label(i, row, "培训项目", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 25);
		//自定义表单采集的信息
		String formId = formService.getFormByXmId(xmId);
		List<FormField> formFields = StringUtils.isNotEmpty(formId) ? formFieldService.getAllList(formId)
				: new ArrayList<FormField>();
		formFields = formFields.stream().filter(x->x.getType() != FormShowType.IMAGE_UPLOAD && x.getType() != FormShowType.DOC_UPLOAD).collect(Collectors.toList());
		if (formFields!=null && formFields.size() > 0) {
			for (FormField field : formFields) {
				ws.addCell(new Label(i, row, field.getName(), OfficeToolExcel.getTitle()));
				ws.setColumnView(i++, 10);
			}
		}
		ws.addCell(new Label(i, row, "报名类型", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费状态", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费金额", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "缴费时间", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "订单编号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "聚合支付订单编号", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);
		ws.addCell(new Label(i, row, "报名时间", OfficeToolExcel.getTitle()));
		ws.setColumnView(i++, 10);

		row = 1;
		for (Map<String, Object> map : records) {
			if (map == null) {
				continue;
			} else {
				int col = 0;
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("title") != null ? map.get("title") : ""),
						OfficeToolExcel.getNormolCell()));
				String studentId = BaseUtil.getStringValueFromMap(map, "studentId");
				if(formFields!=null && formFields.size() > 0) {
					List<FormData> formDatas = formDataService.getFormFieldData(xmId, studentId);
					if(CollectionUtils.isNotEmpty(formDatas)){
						Map<String, FormData> formDataMap = formDatas.stream().collect(Collectors.toMap(FormData::getFieldId, x -> x, (o1, o2) -> o1));
						for (FormField field : formFields) {
							FormData formData = formDataMap.get(field.getId());
							if (formData==null) {
								ws.addCell(new Label(col++, row, "", OfficeToolExcel.getNormolCell()));
							}
							else {
								if (FormShowType.SELECT == field.getType()
										|| FormShowType.SINGLE_CHOICE == field.getType()
										|| FormShowType.MULTI_CHOICE == field.getType()
										|| FormShowType.DOC_UPLOAD == field.getType()
										|| FormShowType.IMAGE_UPLOAD == field.getType()) {
									ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(formData.getFieldText()),
											OfficeToolExcel.getNormolCell()));
								}
								else if (FormShowType.REGION == field.getType()) {
									String areaCode = formData.getFieldValue();
									ws.addCell(new Label(col++, row, StringUtils.isEmpty(areaCode) ? "" : dictService.getRegionNameByAreaCode(areaCode),
											OfficeToolExcel.getNormolCell()));
								}
								else {
									ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(formData.getFieldValue()),
											OfficeToolExcel.getNormolCell()));
								}
							}
						}
					}
				}
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "bmlx")) ? "集体" : "个人"),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								Constants.YES.equals(BaseUtil.getStringValueFromMap(map, "isPayed")) ? "已缴费" : "未缴费"),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "amount", "")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "payTime","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "orderNumber","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								BaseUtil.getStringValueFromMap(map, "unionOrderNo","")),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("bmTime") != null
								? DateUtils.format((Date) map.get("bmTime"), "yyyy-MM-dd HH:mm:ss")
								: ""),
						OfficeToolExcel.getNormolCell()));
			}
			row++;
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 培训计划-发票申请-批量导出
	 */
	public void exportInvoice(List<Map<String, Object>> bizzypxBmList, OutputStream os) throws Exception {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("职业培训发票申请汇总表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "性别", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "培训机构", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "申请开票时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "发票抬头", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "纳税人识别号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "开户行", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "开户行账号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "单位地址", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "单位电话", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "email", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);

		}
		row = 1;
		for (Map<String, Object> map : bizzypxBmList) {
			if (map == null) {
				continue;
			} else {
				int col = 0;
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("serialNumber") != null ? map.get("serialNumber") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("title") != null ? map.get("title") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("sfzh") != null ? map.get("sfzh") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
						map.get("gender") != null ? Gender.findByEnumName(map.get("gender").toString()).getName() : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("orgName") != null ? map.get("orgName") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("invoiceApplyedTime") != null
								? DateUtils.format((Date) map.get("invoiceApplyedTime"), "yyyy-MM-dd HH:mm:ss")
								: ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("invoiceTitle") != null ? map.get("invoiceTitle") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("invoiceCode") != null ? map.get("invoiceCode") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("invoiceBank") != null ? map.get("invoiceBank") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								map.get("invoiceBankAccount") != null ? map.get("invoiceBankAccount") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								map.get("invoiceOrgAddress") != null ? map.get("invoiceOrgAddress") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(
								map.get("invoiceOrgPhone") != null ? map.get("invoiceOrgPhone") : ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row,
						BaseUtil.convertNullToEmpty(map.get("email") != null ? map.get("email") : ""),
						OfficeToolExcel.getNormolCell()));
				row++;
			}
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

	/**
	 * 批量导出附件
	 */
	public void expAccessory(ZypxBmQueryParams params, OutputStream os) throws Exception {
		ZipOutputStream zipOutputStream = null;
		File tmpFolder = FileHelper.createTmpFile();
		tmpFolder.mkdirs();
		zipOutputStream = new ZipOutputStream(os);
		List<Map<String, Object>> list = pageQuery(params).getRecords();
		try {
				for (Map<String, Object> map : list) {
					String folderName = BaseUtil.getStringValueFromMap(map, "title");
					String studentId = BaseUtil.getStringValueFromMap(map, "studentId");
					String studentName = BaseUtil.getStringValueFromMap(map, "studentName", "");
					String sfzh = BaseUtil.getStringValueFromMap(map, "sfzh", "");
					String studentTicket = BaseUtil.convertNullToEmpty(map.get("studentTicket"));
					String sfzzm = BaseUtil.convertNullToEmpty(map.get("sfzzm"));
					String sfzfm = BaseUtil.convertNullToEmpty(map.get("sfzfm"));
					String studentPhoto = BaseUtil.convertNullToEmpty(map.get("studentPhoto"));
					String eduCertiPhoto = BaseUtil.convertNullToEmpty(map.get("eduCertiPhoto"));
					Map<String, String> attachmentMap = new HashMap<String, String>();
					if (StringUtils.isNotEmpty(studentPhoto)) {
						attachmentMap.put("登记照", studentPhoto);
					}
					if (StringUtils.isNotEmpty(sfzzm)) {
						attachmentMap.put("身份证正面", sfzzm);
					}
					if (StringUtils.isNotEmpty(sfzfm)) {
						attachmentMap.put("身份证反面", sfzfm);
					}
					if (StringUtils.isNotEmpty(studentTicket)) {
						attachmentMap.put("学生证", studentTicket);
					}
					if (StringUtils.isNotEmpty(eduCertiPhoto)) {
						attachmentMap.put("学历证书", eduCertiPhoto);
					}
					String xmId = BaseUtil.getStringValueFromMap(map, "xmId");
					//判断项目是否有自定义表单
					String formId = formService.getFormByXmId(xmId);
					if (StringUtils.isNotEmpty(formId)) {
						List<Map<String, Object>> mapList = formDataService.getAnswer(xmId, formId, studentId);
						for (Map<String, Object> formMap : mapList) {
							if (!Constants.YES.equals(BaseUtil.getStringValueFromMap(formMap, "isConstant")) && FormShowType.IMAGE_UPLOAD.name().equals(BaseUtil.getStringValueFromMap(formMap, "fieldType"))
									|| FormShowType.DOC_UPLOAD.name().equals(BaseUtil.getStringValueFromMap(formMap, "fieldType"))) {
								String attName =  BaseUtil.convertNullToEmpty(formMap.get("fieldName"));
								String attUrl =  BaseUtil.convertNullToEmpty(formMap.get("fieldValue"));
								if (StringUtils.isNotEmpty(attUrl)) {
									attachmentMap.put(attName, attUrl);
								}
							}
						}
					}

					for (String attName : attachmentMap.keySet()) {
						String attUrl = attachmentMap.get(attName);
						InputStream fis = null;
						try {
							URL url = new URL(attUrl);
							HttpURLConnection connection = (HttpURLConnection) url.openConnection();
							connection.setConnectTimeout(180000);
							connection.setReadTimeout(180000);
							if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
								fis = connection.getInputStream();
								String ext = FileHelper.getExtension(attUrl);
								String entryName = "";
								if (Constants.YES.equals(params.getExportType())) {
									entryName = folderName + "/"+ sfzh + "_" + studentName + "/" + attName + "." + ext;
									zipOutputStream.putNextEntry(new ZipEntry(entryName));
								} else {
									entryName = folderName + "/" + sfzh + "_" + studentName + "_"+ attName + "." + ext;
									zipOutputStream.putNextEntry(new ZipEntry(entryName));
								}
								byte[] buffer = new byte[1024];
								int r = 0;
								while ((r = fis.read(buffer)) != -1) {
									zipOutputStream.write(buffer, 0, r);
								}
								zipOutputStream.closeEntry();
							}
						} catch (Exception e) {
							logger.error("附件下载失败，e:" + e.getMessage());
						} finally {
							if (fis != null) {
								IOUtils.closeQuietly(fis);
							}
						}
					}
				}
		} finally {
			if (zipOutputStream != null) {
				zipOutputStream.flush();
				IOUtils.closeQuietly(zipOutputStream);
			}
		}
		logger.info("expEflPicture zip successfully:" + tmpFolder.getAbsolutePath());
	}

	public Page getBmCourseByBmId(String bmId, Page page) {
		page.setRecords(mapper.getBmCourseByBmId(bmId, page));
		return page;
	}

	public Integer getBmCountByXmId(String xmId){
		return mapper.getBmCountByXmId(xmId);
	}

	public Map<String, Object> getInvaoiceDetail(ZypxBmQueryParams params) {
		return mapper.getInvaoiceDetail(params.getCondition());
	}

	@Transactional
	public void setClass(String bmIds, String classId) {
		CommClass commClass = commClassMapper.selectById(classId);
		String[] bmidList = StringUtils.split(bmIds, ",");
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.eq("class_id", classId);
		if (commClass.getLimitCount() != null) {
			if (mapper.selectCount(wrapper) + bmidList.length > commClass.getLimitCount()) {
				throw BizException.withMessage("您所所选择的学员数量已经超过班级人数限制" + commClass.getLimitCount() + "人");
			}
		}
		wrapper = new EntityWrapper();
		wrapper.in("id", bmidList);
		List<ZypxBm> bmList = mapper.selectList(wrapper);
		for (ZypxBm zypxBm : bmList) {
			zypxBm.setClassId(classId);
		}
		DBUtils.updateBatchById(bmList, ZypxBm.class);
	}

	@Transactional
	public void markInvoice(String ids, String isMarkInvoice) {
		List<String> bmIds = Lists.newArrayList(StringUtils.split(ids, ","));
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.in("id", bmIds);
		List<ZypxBm> bmList = mapper.selectList(wrapper);
		if (bmList.size() > 0) {
			for (ZypxBm zypxBm : bmList) {
				zypxBm.setIsMarkInvoice(isMarkInvoice);
				if (Constants.NO.equals(isMarkInvoice)){
					zypxBm.setInvoiceAmount(null);
				}
				if (Constants.YES.equals(isMarkInvoice)){
					zypxBm.setInvoiceAmount(zypxBm.getPayedAmount());
				}
			}
			DBUtils.updateBatchById(bmList, ZypxBm.class);
		} else {
			throw BizException.withMessage("标记开票失败，因为指定的报名信息不存在");
		}
	}

	@Transactional
	public void markPayed(String ids, Double payedAmount, String status, String markPayedReason, String markPayedUserId) {
		List<ZypxBm> bmList = mapper.selectBatchIds(Arrays.asList(ids.split(",")));
		if (bmList.isEmpty()) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据不存在");
		}
		if (Constants.YES.equals(status) && bmList.stream().anyMatch(b->Constants.YES.equals(b.getIsPayed()))) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据中包含已缴费的数据");
		} else if (Constants.NO.equals(status) && bmList.stream().anyMatch(b->Constants.NO.equals(b.getIsPayed()))) {
			throw BizException.withMessage("操作失败，因为您选择的报名数据中包含未缴费的数据");
		}
		for (ZypxBm zypxBm : bmList) {
			if (Constants.YES.equals(status)) {
				zypxBm.setIsPayed(Constants.YES);
				zypxBm.setMarkPayedUserId(markPayedUserId);
				zypxBm.setPayedAmount(payedAmount);
				zypxBm.setPayType(PayType.OFF_LINE);

				zypxBm.setRefundAmount(null);
				zypxBm.setMarkRefundUserId(null);
				zypxBm.setRemark(zypxBm.getRemark() == null ? markPayedReason : zypxBm.getRemark() + ";" + markPayedReason);
				mapper.updateAllColumnById(zypxBm);
			} else if (Constants.NO.equals(status)) {
				zypxBm.setIsPayed(Constants.NO);
				zypxBm.setMarkPayedUserId(null);
				zypxBm.setPayedAmount(null);
				zypxBm.setPayType(null);

				zypxBm.setRefundAmount(null);
				zypxBm.setMarkRefundUserId(null);
				zypxBm.setRemark(zypxBm.getRemark() == null ? markPayedReason : zypxBm.getRemark() + ";" + markPayedReason);
				mapper.updateAllColumnById(zypxBm);
			}
		}
	}

	/**
	 * 标记退费
	 */
	@Transactional
	public void refund(String ids, String reason, User user) {
		List<String> bmIds = Lists.newArrayList(StringUtils.split(ids, ","));
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.in("id", bmIds);
		List<ZypxBm> bmList = mapper.selectList(wrapper);
		if (bmList.size() > 0) {
			for (ZypxBm zypxBm : bmList) {
				if (!Constants.YES.equals(zypxBm.getIsPayed())) {
					throw BizException.withMessage("操作失败，因为您选择的报名数据中包含未缴费的数据");
				}
				// 退费
				zypxBm.setRefundAmount(zypxBm.getPayedAmount());
				zypxBm.setMarkRefundUserId(user.getId());
				zypxBm.setRemark(zypxBm.getRemark() == null ? reason : zypxBm.getRemark() + ";"+reason);
				zypxBm.setRefundRemark(reason + " " + user.getName() + " " + DateUtils.format(new Date()));

				zypxBm.setIsPayed("2");
				zypxBm.setMarkPayedUserId(null);
				zypxBm.setPayedAmount(null);
				zypxBm.setPayType(null);
				mapper.updateAllColumnById(zypxBm);
			}
		} else {
			throw BizException.withMessage("标记退费失败，因为指定的报名信息不存在");
		}
	}

	/**
	 * 培训计划-报名管理-批量导入分班信息
	 *
	 */
	public Map<String, Object> importClass(MultipartFile file, String xmId, String classId, String hostOrgId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[] { "sfzh", "xm" });
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}

		CommClass commClass = commClassMapper.selectById(classId);
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.eq("class_id", classId);
		if (commClass.getLimitCount() != null) {
			if (mapper.selectCount(wrapper) + (list.size() - 1) > commClass.getLimitCount()) {
				throw BizException
						.withMessage("导入文件中的学员数量已经超过班级人数限制，该班级设置的人数上限是：" + commClass.getLimitCount() + "人");
			}
		}

		// 数据集
		List<ZypxBm> bmList = new ArrayList<>();
		Set<String> sfzList = new HashSet<String>();
		int row = 0;
		int cfCou = 0;
		int cwCou = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			// a、姓名、身份证号不能够为空
			String xm = StringUtils.trimToNull(map.get("xm"));
			if (StringUtils.isEmpty(xm)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String sfzh = StringUtils.trimToNull(map.get("sfzh"));
			if (StringUtils.isEmpty(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证为空，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			if (sfzList.contains(sfzh)) {
				log.append("<br>");
				String msg = "第" + row + "行身份证重复，请仔细检查，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cfCou++;
				continue;
			} else {
				sfzList.add(sfzh);
			}
			// 判断学员用户是否存在
			List<StudentUser> studentUserList = studentUserMapper.getBySfzh(sfzh, hostOrgId);
			StudentUser studentUser = studentUserList.size() > 0 ? studentUserList.get(0) : null;
			if (studentUser == null) {
				// 证明还没有该学员用户
				log.append("<br>");
				String msg = "第" + row + "行学员尚未注册(学员用户不存在),请先注册，忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}

			EntityWrapper<ZypxBm> bizZypxBmEntityWrapper = new EntityWrapper<>();
			bizZypxBmEntityWrapper.eq("xm_id", xmId);
			bizZypxBmEntityWrapper.eq("student_id", studentUser.getId());
			List<ZypxBm> bmdList = mapper.selectList(bizZypxBmEntityWrapper);
			if (bmdList.size() == 0) {
				log.append("<br>");
				String msg = "第" + row + "行学员尚未报名选择的培训项目,忽略这条数据。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			} else {
				ZypxBm zypxBm = bmdList.get(0);
				zypxBm.setClassId(classId);
				bmList.add(zypxBm);
			}
		}
		if (bmList.size() > 0) {
			DBUtils.updateBatchById(bmList, 10, ZypxBm.class);
		}
		// 计算总数据条数-重复条数-错误条数是否与成功条数相等
		if (list.size() - 1 - cfCou - cwCou != bmList.size()) {
			throw BizException.withMessage("未知异常");
		}
		// 提示总共多少条，导入成功多少条，重复数据多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (list.size() - 1) + "条，导入成功" + bmList.size() + "条，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append("错误明细：" + log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	/**
	 * 查询该学员是否报名了该项目
	 */
	public List<ZypxBm> getByXmIdAndStudentId(String xmId, String studentId) {
		return mapper.selectList(new EntityWrapper<ZypxBm>().eq("xm_id", xmId).eq("student_id", studentId));
	}

	/**
	 * 根据项目id得到报名人数
	 */
	public Integer selectCountByXmId(String xmId) {
		return mapper.selectCount(new EntityWrapper<ZypxBm>().eq("xm_id", xmId));
	}

	/**
	 * 获取课件最近学习的时间
	 */
	public Date getCoursewareStudyLatestEndTime(String xmId, String courseId,String studentId) {
		return studentBmMapper.getCoursewareStudyLatestEndTime(xmId, courseId, studentId);
	}

	/**
	 * 获取项目下直播课的最近学习的时间
	 */
	public Date getPcLiveStudyLatestEndTime(String xmId, String courseId, String studentId) {
		return studentBmMapper.getLiveStudyLatestEndTime(xmId, courseId, studentId);
	}

	/**
	 * 课程补报-按项目
	 */
	@Transactional
	public void addBmCourseByXm(String xmId, String courseSettingId) {
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", xmId);
		List<ZypxBm> bmList = mapper.selectList(wrapper);
		List<ZypxBmCourse> addList = new ArrayList<ZypxBmCourse>();
		for (ZypxBm zypxBm : bmList) {
			EntityWrapper<ZypxBmCourse> bmCourseWrapper = new EntityWrapper();
			bmCourseWrapper.eq("bm_id", zypxBm.getId());
			bmCourseWrapper.eq("course_setting_id", courseSettingId);
			if (bmCourseMapper.selectCount(bmCourseWrapper) == 0) {
				ZypxBmCourse zypxBmCourse = new ZypxBmCourse();
				zypxBmCourse.setId(BaseUtil.generateId2());
				zypxBmCourse.setBmId(zypxBm.getId());
				zypxBmCourse.setCourseSettingId(courseSettingId);
				zypxBmCourse.setBmTime(new Date());
				addList.add(zypxBmCourse);
			}
		}
		DBUtils.insertBatch(addList, ZypxBmCourse.class);
	}

	/**
	 * 课程补报-按学员
	 */
	@Transactional
	public void addBmCourseByStudent(String bmIds, String courseSettingId) {
		String[] bmIdArray = StringUtils.split(bmIds, ",");
		if (bmIdArray == null || bmIdArray.length == 0) {
			throw BizException.withMessage("请选择学员的报名数据");
		}
		List<ZypxBm> bmList = mapper.selectBatchIds(Arrays.asList(bmIdArray));
		List<ZypxBmCourse> addList = new ArrayList<ZypxBmCourse>();
		for (ZypxBm zypxBm : bmList) {
			EntityWrapper<ZypxBmCourse> bmCourseWrapper = new EntityWrapper();
			bmCourseWrapper.eq("bm_id", zypxBm.getId());
			bmCourseWrapper.eq("course_setting_id", courseSettingId);
			if (bmCourseMapper.selectCount(bmCourseWrapper) == 0) {
				ZypxBmCourse zypxBmCourse = new ZypxBmCourse();
				zypxBmCourse.setId(BaseUtil.generateId2());
				zypxBmCourse.setBmId(zypxBm.getId());
				zypxBmCourse.setCourseSettingId(courseSettingId);
				zypxBmCourse.setBmTime(new Date());
				addList.add(zypxBmCourse);
			}
		}
		DBUtils.insertBatch(addList, ZypxBmCourse.class);
	}

	public Object statisticCost(ZypxXmQueryParams params) {
		List<Map<String, Object>> list = mapper.statisticCost(params.getCondition());
		//计算合计汇总数据
		Integer totalCount = 0;
		BigDecimal totalAmount = new BigDecimal(0.0);
		Map<String, Object> totalMap = new HashMap<String, Object>();
		for (Map<String, Object> map : list) {
			totalCount = totalCount + 1;
			if(null != map && "1".equals(map.get("ispayed"))) {
				System.out.println(map.get("ispayed")+"   "+new BigDecimal(map.get("totalamount").toString()));
				totalAmount = totalAmount.add(new BigDecimal(map.get("totalamount").toString()));
			}
		}
		totalMap.put("totalCount", totalCount);
		totalMap.put("totalAmount", totalAmount);
		return totalMap;
	}

	/**
	 * 观看公开课时自动报名培训项目
	 */
	@Async
	@Transactional
	public void autoBmWhenStudyPublicCourse(String courseId, String courseLiveId) {
		try {
			List<String> studentIdList = courseLiveMapper.getViewedStudentId(courseLiveId);
			for (String studentId : studentIdList) {
				this.autoBmWhenStudentStudyPublicCourse(courseId, studentId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 观看公开课时自动报名培训项目
	 */
	@Transactional
	public void autoBmWhenStudentStudyPublicCourse(String courseId, String studentId) {
		if (StringUtils.isNotEmpty(courseId)) {
			EntityWrapper<ZypxXmCourseSetting> wrapper = new EntityWrapper();
			wrapper.isNotNull("xm_id");
			wrapper.eq("course_id", courseId);
			List<ZypxXmCourseSetting> list = xmCourseSettingMapper.selectList(wrapper);
			//如果多个项目下都有这门课程则给每一个项目都会报名
			Set<String> xmIds = list.stream().map(x->x.getXmId()).collect(Collectors.toSet());
			for (String xmId : xmIds) {
				ZypxXm zypxXm = xmMapper.selectById(xmId);
				Plan plan = planMapper.selectById(zypxXm.getPlanId());
				if (Constants.YES.equals(plan.getIsSkill())) {//如果是技能类型的项目直接跳过
					continue;
				}
//				if (zypxXm.getStatus() != XmStatus.OK) {
//					continue;//已经关闭的项目不报名
//				}
//				if (DateUtils.now().after(zypxXm.getEndTime()) || DateUtils.now().before(zypxXm.getStartTime())) {
//					continue;//不在培训时间范围内的项目不报名
//				}
				//判断学员是否已报名该项目
				ZypxBm zypxBm = studentBmService.getBmInfo(xmId, studentId);
				if (zypxBm != null) {
					continue;
				}
				List<Map<String, Object>> xcsList = xmCourseSettingMapper.getAllCourseByXmId(xmId);
				List<String> coueCourseSettingIds = xcsList.stream().map(y -> BaseUtil.getStringValueFromMap(y, "id")).collect(Collectors.toList());
				if (CollectionUtils.isEmpty(coueCourseSettingIds)) {
					continue;
				}
				//没有报名此项目的学员进行报名
				studentBmService.createStudentBm(studentId, xmId , StringUtils.join(coueCourseSettingIds, ","), null);
			}
		}
	}

	/**
	 * 获取项目的报名信息
	 */
	public List<ZypxBm> getByXmId(String xmId){
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", xmId);
		return mapper.selectList(wrapper);
	}

	public List<Map<String, Object>> getPracticeByStudent(String studentId) {
		StudentPaperQueryParams params = new StudentPaperQueryParams();
		params.setCategory(PaperCategory.PSZY);
		params.setStudentId(studentId);
		params.setSize(Integer.MAX_VALUE);
		params.setCurrent(1);
		return zypxStudentPaperService.getStudentPaperList(params).getRecords();
	}

	/**
	 * 根据报名id获取订单信息
	 */
	public List<CommOrder> getOrderByBmId(String id) {
		ZypxBm zypxBm = mapper.selectById(id);
		if (zypxBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		List<CommOrderDetail> commOrderDetails = commOrderDetailMapper.selectList(new EntityWrapper<CommOrderDetail>().eq("yw_id", id));
		if (commOrderDetails.isEmpty()) {
			return new ArrayList<>();
		}
		List<String> orderIds = commOrderDetails.stream().map(CommOrderDetail::getOrderId).collect(Collectors.toList());
		return commOrderMapper.selectList(new EntityWrapper<CommOrder>()
				.in("id", orderIds)
				.eq("status", OrderStatus.YZF));
	}

	@Transactional(rollbackFor = Exception.class)
	public String batchUploadInvoice(String bmbatchId, MultipartFile file, String currentHostOrgId) throws IOException {
		StringBuffer result = new StringBuffer();

		List<ZypxBm> zypxBms = mapper.selectList(new EntityWrapper<ZypxBm>().eq("xm_id", bmbatchId));
		if (zypxBms.isEmpty()) {
			throw BizException.withMessage("当前项目暂无报名数据");
		}
		if (file.isEmpty()) {
			throw BizException.withMessage("请上传发票");
		}
		String tempDir = attConfig.getTempdir() + "/lgdbm";
		if (new File(tempDir).exists()) {
			FileHelper.delFile(new File(tempDir));
		}
		File tempFile = new File(tempDir, "invoice.zip");
		File uncompressFile;
		if (!tempFile.exists()) {
			tempFile.getParentFile().mkdir();
		}
		InputStream uploadIs = null;
		OutputStream tempOs = null;
		try {
			uploadIs = file.getInputStream();
			tempOs = Files.newOutputStream(tempFile.toPath());
			IOUtils.copy(uploadIs, tempOs);
		} finally {
			if (uploadIs != null) {
				IOUtils.closeQuietly(uploadIs);
			}
			if (tempOs != null) {
				IOUtils.closeQuietly(tempOs);
			}
		}
		ZipUtil.uncompress(tempFile.getAbsolutePath(), tempDir + "/uncompressZip", null);
		uncompressFile = new File(tempDir + "/uncompressZip");
		File[] files = uncompressFile.listFiles();
		if (files == null || file.isEmpty()) {
			throw BizException.withMessage("文件格式错误，压缩包里无发票文件");
		}
		if (Arrays.stream(files).anyMatch(File::isDirectory)) {
			throw BizException.withMessage("文件格式错误");
		}
		List<ZypxBm> zypxBmList = new ArrayList<>();
		for (File fileItem : files) {
			String name = fileItem.getName().substring(0, fileItem.getName().lastIndexOf("."));
			List<ZypxBm> list = mapper.getStudentBmInfo(name,bmbatchId,currentHostOrgId);
			if(list.isEmpty()){
				result.append(name + ":学员报名信息不存在\n");
				continue;
			} else if (list.size() > 1) {
				result.append(name + ":学员报名信息有" + list.size() + "个重复，请使用单个发票导入\n");
				continue;
			}
			//满足条件上传发票文件
			String ext = FileHelper.getExtension(fileItem.getName());
			String path = attConfig.getRootDir() + "/upload/tddzcl/resource/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
			String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
			FileInputStream is = null;
			try {
				is = new FileInputStream(fileItem);
				list.get(0).setInvoicePath(FileHelper.storeFile(path, is, newFileName));
				zypxBmList.add(list.get(0));
			} finally {
				if (is != null) {
					IOUtils.closeQuietly(is);
				}
				tempFile.delete();
				uncompressFile.delete();
			}
		}
		if (!zypxBmList.isEmpty()) {
			DBUtils.updateBatchById(zypxBmList, ZypxBm.class);
		}
		if (StringUtils.isNotEmpty(result.toString())) {
			return result.toString();
		} else {
			return null;
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void uploadInvoice(String id, MultipartFile file) {
		ZypxBm zypxBm = mapper.selectById(id);
		if (zypxBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		try {
			String ext = FileHelper.getExtension(file.getOriginalFilename());
			String path = attConfig.getRootDir() + "/upload/tddzcl/resource/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
			String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext.toLowerCase();
			zypxBm.setInvoicePath(FileHelper.storeFile(path, file.getInputStream(), newFileName));
			mapper.updateById(zypxBm);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public SXSSFWorkbook exportStudentInvoice(HttpServletResponse response, String xmId, String orgId) {
		List<StudentInfo> studentInfoList = studentInfoMapper.getStudent(xmId, orgId);
		//查询学生信息
		SXSSFWorkbook workbook = new SXSSFWorkbook();
		//开启压缩模式
		workbook.setCompressTempFiles(true);
		Sheet sheet = workbook.createSheet();
		Cell cell;
		Row row = sheet.createRow(0);
		for (int i = 0; i < tableHeader.length; i++) {
			cell = row.createCell(i);
			cell.setCellValue(tableHeader[i]);
		}
		int rowNum = 0;
		for (StudentInfo studentInfo : studentInfoList) {
			rowNum++;
			int cloumn = 0;
			row = sheet.createRow(rowNum);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getName());

			int width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getSfzh());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getMobile());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getEmail());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceTitle());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceCode());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceBank());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceBankAccount());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceOrgAddress());

			width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(cloumn) * 12 / 10));
			sheet.setColumnWidth(cloumn, width);
			cell = row.createCell(cloumn++);
			cell.setCellValue(studentInfo.getInvoiceOrgPhone());

		}
		return workbook;
	}

    public List<Map<String, Object>> getAllStudentByXmId(String id, String keyword) {
		return mapper.getAllStudentByXmId(id, keyword);
    }

	public Map<String, Object> getClassLeaderInfoByXmId(String id, String keyword) {
		Map<String, Object> result = new HashMap<>();
		result.put("classCommittee", mapper.getClassLeaderInfoByXmId(id, keyword));
		result.put("group", commClassMapper.getGroupLeader(id, keyword));
		return result;
	}

	/**
	 * 批量绑定学习卡（神奇的考点母题）
	 */
	@Transactional(rollbackFor = Exception.class)
	public void bandCardNumber(BandCardNumberDto bandCardNumberDto) throws IOException {
		List<ZypxXmBmCardNumber> zypxXmBmCardNumbers = zypxXmBmCardNumberMapper.selectList(null);
		//使用过的学习卡
		Set<String> doneCardNumbers = zypxXmBmCardNumbers.stream().map(ZypxXmBmCardNumber::getCardNumber).collect(Collectors.toSet());
		//剩余学习卡
		Set<String> cards = bandCardNumberDto.getCardNumbers().stream().filter(c -> !doneCardNumbers.contains(c)).collect(Collectors.toSet());
		//每个人绑定卡的数量
		int size = bandCardNumberDto.getUseGiveProductIds().size();
		if (bandCardNumberDto.getProductInfoMax() == 0) {
			size = 1;
		}
		if (cards.size() < bandCardNumberDto.getBmIds().split(",").length * size) {
			throw BizException.withMessage("学习卡数量不足，请联系第三方平台人员处理");
		}
		List<ZypxBm> zypxBms = mapper.selectBatchIds(Arrays.asList(bandCardNumberDto.getBmIds().split(",")));
		Map<String, String> map = zypxBms.stream().collect(Collectors.toMap(ZypxBm::getId, ZypxBm::getStudentId));
		List<ZypxXmBmCardNumber> list = new ArrayList<>();
		int i = 0;
		for (String bmId : bandCardNumberDto.getBmIds().split(",")) {
			if (bandCardNumberDto.getProductInfoMax() == 0) {
				String cardNumber = cards.toArray(new String[0])[i++];
				ZypxXmBmCardNumber zypxXmBmCardNumber = new ZypxXmBmCardNumber();
				zypxXmBmCardNumber.setId(BaseUtil.generateId2());
				zypxXmBmCardNumber.setBmId(bmId);
				zypxXmBmCardNumber.setCardNumber(cardNumber);
				zypxXmBmCardNumber.setProductName(bandCardNumberDto.getTitle());
				zypxXmBmCardNumber.setProductId(bandCardNumberDto.getId());
				list.add(zypxXmBmCardNumber);
				//创建订单
				this.createOrder(map.get(bmId), cardNumber, null);
			} else {
				for (String useGiveProductId : bandCardNumberDto.getUseGiveProductIds()) {
					String cardNumber = cards.toArray(new String[0])[i++];
					ZypxXmBmCardNumber zypxXmBmCardNumber = new ZypxXmBmCardNumber();
					zypxXmBmCardNumber.setId(BaseUtil.generateId2());
					zypxXmBmCardNumber.setBmId(bmId);
					zypxXmBmCardNumber.setCardNumber(cardNumber);
					zypxXmBmCardNumber.setProductName(bandCardNumberDto.getTitle());
					zypxXmBmCardNumber.setProductId(bandCardNumberDto.getId());
					list.add(zypxXmBmCardNumber);
					//创建订单
					this.createOrder(map.get(bmId), cardNumber, useGiveProductId);
				}
			}
		}
		DBUtils.insertBatch(list, ZypxXmBmCardNumber.class);
	}

	private void createOrder(String studentId, String cardNumber, String useGiveProductId) throws IOException {
		List<String> useGiveProductIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(useGiveProductId)) {
            useGiveProductIds.add(useGiveProductId);
        }
		StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentId);
		OkHttpClient client = new OkHttpClient().newBuilder()
				.build();
		MediaType mediaType = MediaType.parse("application/json");
		Map<String, Object> req = new HashMap<>();
		req.put("SecretKey", Constants.secretKey);
		req.put("NickName", studentInfo.getName());
		req.put("MobileTel", studentInfo.getMobile());
		req.put("UserId", studentInfo.getStudentId());
		req.put("CardNumebr", cardNumber);
		req.put("UseGiveProductIds", useGiveProductIds);
		RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(req));
		Request request = new Request.Builder()
				.url(createProductPayURL)
				.method("POST", body)
				.addHeader("Content-Type", "application/json")
				.build();
		Response execute = client.newCall(request).execute();
		JSONObject jsonObject = JSON.parseObject(execute.body().string());
		if (!jsonObject.getBoolean("IsTrue")) {
			throw BizException.withMessage(studentInfo.getName() + ":" + BaseUtil.convertNullToEmpty(studentInfo.getSfzh()) + "。" + jsonObject.getString("Message"));
		}
	}
}
