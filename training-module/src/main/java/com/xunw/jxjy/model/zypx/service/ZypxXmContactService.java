package com.xunw.jxjy.model.zypx.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.ZypxXmContact;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmContactMapper;
import org.springframework.stereotype.Service;

@Service
public class ZypxXmContactService extends BaseCRUDService<ZypxXmContactMapper, ZypxXmContact>{

    public boolean checkRepeat(String xmId, String name, String phone) {
        return mapper.selectCount(new EntityWrapper<ZypxXmContact>().eq("xm_id", xmId).eq("name", name).eq("phone", phone)) > 0;
    }
}