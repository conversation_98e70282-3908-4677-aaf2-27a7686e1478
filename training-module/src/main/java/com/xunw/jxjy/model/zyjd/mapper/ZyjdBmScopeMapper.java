package com.xunw.jxjy.model.zyjd.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import org.apache.ibatis.annotations.Param;

/**
 * 报名范围设置
 * <AUTHOR>
 */
public interface ZyjdBmScopeMapper extends BaseMapper<ZyjdBmScope>{

    List<Map<String, Object>> list(Map<String, Object> condition,Page<?> page);

    Integer checkLevel(@Param("hostOrgId") String hostOrgId, @Param("professionId") String professionId, @Param("techLevels") List<String> techLevels);

    Map<String, Object> getDetailById(@Param("id") String id);
}
