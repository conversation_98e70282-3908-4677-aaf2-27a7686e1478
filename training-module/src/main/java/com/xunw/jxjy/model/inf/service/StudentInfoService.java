package com.xunw.jxjy.model.inf.service;

import cn.hutool.core.collection.CollectionUtil;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.AccountStatus;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Ksly;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.OdsXgxtXgJbxxMapper;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 *
 * 考生信息
 */
@Service
public class StudentInfoService extends BaseCRUDService<StudentInfoMapper, StudentInfo> {

	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private OdsXgxtXgJbxxMapper odsXgxtXgJbxxMapper;

	public StudentInfo getByStudentId(String studentId) {
		return mapper.getByStudentId(studentId);
	}

	@Transactional
	public void edit(StudentInfo studentInfo, StudentUser studentUser) {
		mapper.updateById(studentInfo);
		studentUserMapper.updateById(studentUser);
	}

	public boolean isPersonalInfoCompleted(String studentId) {
		StudentUser studentUser = studentUserMapper.selectById(studentId);
		StudentInfo studentInfo = getByStudentId(studentId);
		if (studentInfo.getGender() == null) {// 性别
			return false;
		}
		if (StringUtils.isEmpty(studentInfo.getName())) {// 姓名
			return false;
		}
		if (StringUtils.isEmpty(studentInfo.getSfzh())) {// 身份证号
			return false;
		}
		if (StringUtils.isEmpty(studentInfo.getMobile())) {// 手机号
			return false;
		}
		if (studentUser.getStudentType() == null) {// 学员类型
			return false;
		} else {
			if (studentUser.getStudentType() == StudentType.SCHOOL
					&& (StringUtils.isEmpty(studentInfo.getGraduateSchool())
							|| StringUtils.isEmpty(studentInfo.getCollege())
							|| StringUtils.isEmpty(studentInfo.getSpecialty())
							|| StringUtils.isEmpty(studentInfo.getClassz()))) {
				return false;
			}
			if (studentUser.getStudentType() == StudentType.SOCIAL) {
				boolean a = StringUtils.isNotEmpty(studentUser.getCompany());
				boolean b = StringUtils.isNotEmpty(studentInfo.getZw());
				boolean c = StringUtils.isNotEmpty(studentInfo.getZc());
				return a && b && c;
			}
		}
		return true;
	}

	public StudentInfo getBySfzh(String sfzh, String hostOrgId) {
		List<StudentUser> list = studentUserMapper.getBySfzh(sfzh, hostOrgId);
		if (list.size() > 0) {
			StudentInfo studentInfo = getByStudentId(list.get(0).getId());
			return studentInfo;
		}
		return null;
	}

	public StudentInfo getByMobile(String mobile, String hostOrgId) {
		List<StudentUser> list = studentUserMapper.getByMobile(mobile, hostOrgId);
		if (list.size() > 0) {
			StudentInfo studentInfo = getByStudentId(list.get(0).getId());
			return studentInfo;
		}
		return null;
	}

	/**
	 * 扫码看直播如果没有用户则新注册一个学生用户
	 */
	@Transactional
	public Map<String, Object> registByzbQr(String name, String sfzh, String mobile, String company,
			String regHostOrgId, String openId) {
		// 写入考生用户表
		StudentUser studentUser = new StudentUser();
		studentUser.setId(BaseUtil.generateId2());
		String account = StringUtils.isNotEmpty(sfzh) ? sfzh : mobile;
		String password = account.substring(account.length() - 6);
		studentUser.setPassword(DigestUtils.md5Hex(password));
		studentUser.setIsBindMobile(Constants.YES);
		studentUser.setStudentType(StudentType.SOCIAL);
		studentUser.setCompany(company);
		studentUser.setStatus(AccountStatus.OK);
		studentUser.setRegHostOrgId(regHostOrgId);// 注册主办单位ID
		if (StringUtils.isNotEmpty(openId)) {
			studentUser.setOpenId(openId);
		}
		studentUserMapper.insert(studentUser);
		// 考生基础信息表
		StudentInfo studentInfo = new StudentInfo();
		studentInfo.setId(BaseUtil.generateId());
		studentInfo.setStudentId(studentUser.getId());
		studentInfo.setName(name);
		studentInfo.setSfzh(sfzh);
		String gender = BaseUtil.parseGender(sfzh);
		if (StringUtils.isNotEmpty(gender)) {
			studentInfo.setGender(Gender.findByEnumName(gender));
		}
		studentInfo.setKsly(Ksly.QITA);
		studentInfo.setCreateTime(new Date());
		studentInfo.setMobile(mobile);
		mapper.insert(studentInfo);
		Map<String, Object> map = new HashMap<>();
		map.put("studentInfo", studentInfo);
		map.put("studentUser", studentUser);
		return map;
	}

	/**
	 * 查询生态中间库学生基本信息
	 */
	public Map<String, Object> selectByXh(String employeeNum) {
		return odsXgxtXgJbxxMapper.selectByXh(employeeNum);
	}

	/**
	 * 修改中间库同步状态
	 */
	public void updateSyncStatus(String employeeNum) {
		odsXgxtXgJbxxMapper.updateSyncStatus(employeeNum);
	}

	public List<StudentInfo> getByStudentIds(List<String> studentIds) {
		if (CollectionUtil.isEmpty(studentIds)) {
			return new ArrayList<>();
		}
		return mapper.getByStudentIds(studentIds);
	}
}
