package com.xunw.jxjy.model.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;

/**
 * 师资来源
 */
public enum UserResource implements IEnum {

    QY("企业", "0"), GX("高校", "1"), DZGB("党政干部", "2");

    private String name;

    private String id;

    private UserResource(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static UserResource findById(String id) {
        for (UserResource status : UserResource.values()) {
            if (status.id == id) {
                return status;
            }
        }
        return null;
    }

	public static UserResource findByEnumName(String name){
		for (UserResource zt : UserResource.values()) {
			if (zt.name().equals(name)) {
				return zt;
			}
		}
		return null;
	}

	public static UserResource findByName(String name) {
		for (UserResource xb : UserResource.values()) {
			if (xb.getName().trim().equals(name)) {
				return xb;
			}
		}
		return null;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}
    
}
