package com.xunw.jxjy.model.zypx.params;

import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.BmStatus;
import com.xunw.jxjy.model.enums.XmStatus;

public class ZypxBmQueryParams extends BaseQueryParams {
	
    private String planId;
    private String xmId;
    //学员所属的培训机构id
    private String orgId;
    //学员所属单位
    private String company;
    //培训计划所属的主办单位ID
    private String hostOrgId;
    //报名类型
    private String bmlx;
    //报名状态
    private BmStatus status;
    //是否已经缴费 1是 0 否
    private String isPayed;
    //考生关键字
    private  String ksWord;
    //报名ID
    private String bmId;
    //班主任ID
    private String headermasterId;
    //项目的状态
    private XmStatus xmStatus;
    //是否申请了发票
	private String isApplyedInvoice;
	//开票状态 1 已开票 0 未开票
	private String isMarkInvoice;
	//项目负责人ID
	private String leaderId;
	//年度
	private String year;
	private String provinceCode;
	private String cityCode;
	private String districtCode;
	//导出附件是否带文件夹 1是0否
	private String exportType;
	private String isSkill;
    private String payType;
    //是否上传发票
    private String isInvoice;

	/**
	 * 课程id
	 */
    private String courseId;

	public String getExportType() {
		return exportType;
	}

	public void setExportType(String exportType) {
		this.exportType = exportType;
	}

	public String getXmId() {
		return xmId;
	}
	public void setXmId(String xmId) {
		this.xmId = xmId;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getHostOrgId() {
		return hostOrgId;
	}
	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}
	public String getBmlx() {
		return bmlx;
	}
	public void setBmlx(String bmlx) {
		this.bmlx = bmlx;
	}
	public BmStatus getStatus() {
		return status;
	}
	public void setStatus(BmStatus status) {
		this.status = status;
	}
	public String getIsPayed() {
		return isPayed;
	}
	public void setIsPayed(String isPayed) {
		this.isPayed = isPayed;
	}
	public String getKsWord() {
		return ksWord;
	}
	public void setKsWord(String ksWord) {
		this.ksWord = ksWord;
	}
	public String getBmId() {
		return bmId;
	}
	public void setBmId(String bmId) {
		this.bmId = bmId;
	}
	public String getHeadermasterId() {
		return headermasterId;
	}
	public void setHeadermasterId(String headermasterId) {
		this.headermasterId = headermasterId;
	}
	public XmStatus getXmStatus() {
		return xmStatus;
	}
	public void setXmStatus(XmStatus xmStatus) {
		this.xmStatus = xmStatus;
	}
	public String getIsApplyedInvoice() {
		return isApplyedInvoice;
	}
	public void setIsApplyedInvoice(String isApplyedInvoice) {
		this.isApplyedInvoice = isApplyedInvoice;
	}
	public String getLeaderId() {
		return leaderId;
	}
	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getProvinceCode() {
		return provinceCode;
	}
	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}
	public String getCityCode() {
		return cityCode;
	}
	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}
	public String getDistrictCode() {
		return districtCode;
	}
	public void setDistrictCode(String districtCode) {
		this.districtCode = districtCode;
	}

	public String getIsSkill() {
		return isSkill;
	}

	public void setIsSkill(String isSkill) {
		this.isSkill = isSkill;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getIsMarkInvoice() {
		return isMarkInvoice;
	}

	public void setIsMarkInvoice(String isMarkInvoice) {
		this.isMarkInvoice = isMarkInvoice;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(String isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}
}