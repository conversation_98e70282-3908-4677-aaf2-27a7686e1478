package com.xunw.jxjy.model.zyjd.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 报名审核记录表
 */
@TableName(value = "BIZ_BM_AUDIT_LOG")
public class BizBmAuditLog {
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    /**
     * 报名id：BIZ_ZYJD_BM、BIZ_BM主键
     */
    @TableField(value = "BM_ID")
    private String bmId;

    /**
     * 报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定
     */
    @TableField(value = "BM_TYPE")
    private String bmType;

    /**
     * 审核状态
     */
    @TableField(value = "\"STATUS\"")
    private String status;

    /**
     * 审核用户id
     */
    @TableField(value = "USER_ID")
    private String userId;

    /**
     * 审核意见
     */
    @TableField(value = "ADVICE")
    private String advice;

    /**
     * 审核时间
     */
    @TableField(value = "\"TIME\"")
    private Date time;

    /**
     * 是否审核完成：1是、0否，当学生重新提交该值置0
     */
    @TableField(value = "IS_FINISH")
    private Integer isFinish;

    /**
     * 审核轮次：用于详情查看审核记录
     */
    @TableField(value = "\"COUNT\"")
    private Integer count;

    /**
     * 获取主键
     *
     * @return ID - 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取报名id：BIZ_ZYJD_BM、BIZ_BM主键
     *
     * @return BM_ID - 报名id：BIZ_ZYJD_BM、BIZ_BM主键
     */
    public String getBmId() {
        return bmId;
    }

    /**
     * 设置报名id：BIZ_ZYJD_BM、BIZ_BM主键
     *
     * @param bmId 报名id：BIZ_ZYJD_BM、BIZ_BM主键
     */
    public void setBmId(String bmId) {
        this.bmId = bmId;
    }

    /**
     * 获取报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定
     *
     * @return BM_TYPE - 报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定
     */
    public String getBmType() {
        return bmType;
    }

    /**
     * 设置报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定
     *
     * @param bmType 报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定
     */
    public void setBmType(String bmType) {
        this.bmType = bmType;
    }

    /**
     * 获取审核状态
     *
     * @return STATUS - 审核状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置审核状态 0-初审驳回、1-初审通过、2-终审通过、3-终审驳回
     *
     * @param status 审核状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取审核用户id
     *
     * @return USER_ID - 审核用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置审核用户id
     *
     * @param userId 审核用户id
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取审核意见
     *
     * @return ADVICE - 审核意见
     */
    public String getAdvice() {
        return advice;
    }

    /**
     * 设置审核意见
     *
     * @param advice 审核意见
     */
    public void setAdvice(String advice) {
        this.advice = advice;
    }

    /**
     * 获取审核时间
     *
     * @return TIME - 审核时间
     */
    public Date getTime() {
        return time;
    }

    /**
     * 设置审核时间
     *
     * @param time 审核时间
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * 获取是否审核完成：1是、0否，当学生重新提交该值置0
     *
     * @return IS_FINISH - 是否审核完成：1是、0否，当学生重新提交该值置0
     */
    public Integer getIsFinish() {
        return isFinish;
    }

    /**
     * 设置是否审核完成：1是、0否，当学生重新提交该值置0
     *
     * @param isFinish 是否审核完成：1是、0否，当学生重新提交该值置0
     */
    public void setIsFinish(Integer isFinish) {
        this.isFinish = isFinish;
    }

    /**
     * 获取审核轮次：用于详情查看审核记录
     *
     * @return COUNT - 审核轮次：用于详情查看审核记录
     */
    public Integer getCount() {
        return count;
    }

    /**
     * 设置审核轮次：用于详情查看审核记录
     *
     * @param count 审核轮次：用于详情查看审核记录
     */
    public void setCount(Integer count) {
        this.count = count;
    }
}