package com.xunw.jxjy.model.portal.mapper;

import java.util.List;
import java.util.Map;

import com.xunw.jxjy.model.enums.TypeCategory;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.plugins.Page;

/**
 * 门户首页
 * <AUTHOR>
 *
 */
public interface IndexMapper {

	/**
	 * 查询通知公告分类，门户上不展示所有子分类，只展示一级分类
	 */
	public List<Map<String, Object>> getNoticeCategory(String hostOrgId);

	/**
	 * 查询新闻
	 */
	public List<Map<String, Object>> getNotice(Map<String, Object> condition, Page<?> page);

	/**
	 * 查询顶层类型
	 */
	public List<Map<String, Object>> getTopType(@Param("hostOrgId") String hostOrgId, @Param("category") TypeCategory category);

	/**
	 * 查询子类型
	 */
	public List<Map<String, Object>> getChildType(@Param("parentId") String parentId);

	/**
	 * 查询所有的二级类型
	 */
	public List<Map<String, Object>> getSecondLevelType(@Param("hostOrgId") String hostOrgId, @Param("category") TypeCategory category);

	/**
	 * 查询项目
	 */
	public List<Map<String, Object>> getXmList(Map<String, Object> condition, Page<?> page);

	/**
	 * 课程查询，根据参数区分是查课件、直播、题库的课程
	 */
	public List<Map<String, Object>> getCourse(Map<String, Object> condition, Page<?> page);

	/**
	 * 获取培训项目课程设置
	 */
	public List<Map<String, Object>> getXmCourseSetting(@Param("xmId") String xmId);

	/**
	 * 获取门户师资信息
	 */
	public List<Map<String, Object>> getPortalTeacher(Map<String, Object> condition, Page<?> page);

	/**
	 * 获取课件的学习资料
	 */
	public List<Map<String, Object>> getCoursewareMaterialByXmId(@Param("xmId") String xmId);

	/**
	 * 查询门户的轮播图
	 */
	public List<Map<String, Object>> getBanner(Map<String, Object> condition, Page<?> page);

	/**
	 * 查询门户的新闻 左侧的新闻咨询(置顶以及最新的两条,右侧为7条)
	 */
	List<Map<String, Object>> getNoticeByNew(Page page);

	/**
	 * 查询所有的三级类型
	 */
	List<Map<String, Object>> getThirdLevelType(@Param("hostOrgId") String hostOrgId, @Param("category") TypeCategory category);
}
