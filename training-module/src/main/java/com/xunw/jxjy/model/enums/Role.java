package com.xunw.jxjy.model.enums;

import java.io.Serializable;

import com.baomidou.mybatisplus.enums.IEnum;

/**
 * 角色定义
 * <AUTHOR>
 *
 */
public enum Role implements IEnum {

	ADMIN("admin","超级管理员"),
	HOST_ORG("host_org","主办单位管理员"),
	HOST_ORG_CHILD("host_org_child","二级管理员"),
	ORG("org","培训机构管理员"),
	ENTRUST_ORG("entrust_org","委托单位管理员"),
	HEADERMASTER("headermaster","班主任"),
	XM_LEADER("xm_leader","项目负责人"),
	FINANCE("finance","财务"),
	TEACHER("teacher","讲师");

	private String id;

	private String name;

    private Role(String id,String name){
    	this.id = id;
    	this.name = name;
	}

	public static Role findById(String id) {
		for (Role role : Role.values()) {
			if (role.id == id) {
				return role;
			}
		}
		return null;
	}

	public static Role findByEnumName(String name){
		for (Role role : Role.values()) {
			if (role.name().equals(name)) {
				return role;
			}
		}
		return null;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}
}
