package com.xunw.jxjy.model.inf.service;

import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.inf.entity.Base;
import com.xunw.jxjy.model.inf.mapper.InfBaseMapper;
import com.xunw.jxjy.model.inf.params.InfBaseQueryParams;
import org.springframework.stereotype.Service;

@Service
public class InfBaseService extends BaseCRUDService<InfBaseMapper, Base> {

	public Object pageQuery(InfBaseQueryParams params) {
		params.setRecords(mapper.pageQuery(params.getCondition(), params));
		return params;
	}
}
