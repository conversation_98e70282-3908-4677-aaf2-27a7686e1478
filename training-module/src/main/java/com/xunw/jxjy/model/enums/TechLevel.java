package com.xunw.jxjy.model.enums;

import java.io.Serializable;

import com.baomidou.mybatisplus.enums.IEnum;

/**
 * 技术等级
 * <AUTHOR>
 *
 */
public enum TechLevel implements IEnum {
	
	//职业技能等级认定
    ONE("一级", 1, "高级技师"),
    TWO("二级", 2, "技师"),
    THREE("三级", 3,"高级工"),
    FOUR("四级", 4, "中级工"),
    FIVE("五级", 5, "初级工"),
    //土地地质测绘
    ZHONG("中级", 6, "中级"),
    GAO("高级", 7,  "高级"),
	FGAO("副高级", 8,"副高级"),
	ZGAO("正高级", 9,"正高级");

	private String name;
	private int id;
	private String desc;


	private TechLevel(String name, int id,String desc) {
		this.name = name;
		this.id = id;
		this.desc = desc;
	}

	public static TechLevel findById(int id) {
		for (TechLevel obj : TechLevel.values()) {
			if (obj.id == id) {
				return obj;
			}
		}
		return null;
	}

	public static TechLevel findByEnumName(String name){
		for (TechLevel obj : TechLevel.values()) {
			if (obj.name().equals(name)) {
				return obj;
			}
		}
		return null;
	}
	public static TechLevel findByName(String name){
		for (TechLevel obj : TechLevel.values()) {
			if (obj.getName().equals(name)) {
				return obj;
			}
		}
		return null;
	}
	public static TechLevel findByDesc(String desc){
		for (TechLevel obj : TechLevel.values()) {
			if (obj.getDesc().equals(desc)) {
				return obj;
			}
		}
		return null;
	}
	
	public static TechLevel findByNameAndDesc(String name_desc) {
		for (TechLevel obj : TechLevel.values()) {
			if ((obj.getName() + "/" + obj.getDesc()).equals(name_desc)) {
				return obj;
			}
		}
		return null;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}

}
