package com.xunw.jxjy.model.sys.params;

import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.OrgType;
import com.xunw.jxjy.model.enums.Role;

/**
 * 页面查询参数
 */
public class OrgQueryParams extends BaseQueryParams {
	
	private static final long serialVersionUID = 5150518239478513550L;
   
    private OrgType orgType;
    
    private String parentId;

	//是否是顶层机构  即根节点，根节点没有parent_id, 1是 0否
	private String isParent;
	
    private String hostOrgId;
    //单位性质
    private String nature;

    //查询条件
	private String keyword;
	
	//角色
	private Role role;
	
	private String isCollege;

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public OrgType getOrgType() {
		return orgType;
	}

	public void setOrgType(OrgType orgType) {
		this.orgType = orgType;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getIsParent() {
		return isParent;
	}

	public void setIsParent(String isParent) {
		this.isParent = isParent;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public String getIsCollege() {
		return isCollege;
	}

	public void setIsCollege(String isCollege) {
		this.isCollege = isCollege;
	}
}
