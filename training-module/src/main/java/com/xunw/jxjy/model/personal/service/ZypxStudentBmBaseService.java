package com.xunw.jxjy.model.personal.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.dto.StudentFormDataDto;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.dto.TextValuePair;
import com.xunw.jxjy.model.inf.entity.FormData;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.FormDataMapper;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.inf.service.FormDataService;
import com.xunw.jxjy.model.inf.service.FormService;
import com.xunw.jxjy.model.personal.mapper.StudentBmMapper;
import com.xunw.jxjy.model.personal.params.StudentBmQueryParams;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.DictMapper;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxBmCourse;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmCourseMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;
import com.xunw.jxjy.paper.utils.ModelHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报名服务-抽取公用方法
 */
public abstract class ZypxStudentBmBaseService extends BaseCRUDService<ZypxBmMapper, ZypxBm>  {
	
	@Autowired
	private ZypxBmMapper mapper;
	@Autowired
	private StudentBmMapper studentBmMapper;
	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private ZypxBmCourseMapper bmCourseMapper;
	@Autowired
	private FormDataMapper formDataMapper;
	@Autowired
	private FormDataService formDataService;
	@Autowired
	private FormService formService;
	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private ZypxXmService zypxXmService;
	@Autowired
	private DictMapper dictMapper;
	
	/**
	 * 查询职业培训可报名的项目
	 */
	public List<Map<String, Object>> getChooseXmList(StudentBmQueryParams params) {
		List<Map<String, Object>> list = studentBmMapper.getChooseXmList(params);
		return list;
	}
	
	/**
	 * 查询项目的报名信息
	 */
	public ZypxBm getBmInfo(String xmId, String studentId){
		EntityWrapper<ZypxBm> wrapper = new EntityWrapper<ZypxBm>();
		wrapper.eq("xm_id", xmId);
		wrapper.eq("student_id", studentId);
		List<ZypxBm> list=mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}
	
	/**
	 * 根据项目查询已经发布的课程
	 */
	public List<Map<String, Object>> getCourseByXmId(String xmId) {
		List<Map<String, Object>> courseList = studentBmMapper.getXmCourseList(xmId);
		return courseList;
	}


	/**
	 * 查询学生的报名记录
	 */
	public List<Map<String, Object>> getStudentBmXmList(StudentBmQueryParams params) {
		return studentBmMapper.getStudentBmXmList(params);
	}
	
	/**
	 *查询学生报名成功的项目
	 */
	public List<Map<String, Object>> getStudentBMCGXmList(String studentId){
		StudentBmQueryParams params = new StudentBmQueryParams();
		params.setIsOnlyPass(Constants.YES);
		params.setStudentId(studentId);
		return this.getStudentBmXmList(params);
	}
	
	/**
	 * 查询学生报名的课程
	 */
	public List<Map<String, Object>> getStudentBmCourseList(String xmId, String studentId) {
		return studentBmMapper.getStudentBmCourseList(xmId, studentId);
	}
	
	/**
	 * 提交培训项目报名信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public void createStudentBm(String studentId, String xmId, String courseSettingIds, HttpSession session) {
		String[] courseSettingIdStr = new String[] {};
		if (StringUtils.isNotEmpty(courseSettingIds)) {
			courseSettingIdStr = StringUtils.split(courseSettingIds, ",");
		}
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		if (zypxXm.getBuyType() == BuyType.COURSE && courseSettingIdStr.length == 0) {
			throw BizException.withMessage("按课程购买的项目，报名至少选择一门课程");
		}
		if (zypxXm.getIsAllowMultiChooseCourse() != 1 && courseSettingIdStr.length > 1) {
			throw BizException.withMessage("此项目不支持报名多门课程");
		}
		ZypxBm zypxBm = this.getBmInfo(xmId, studentId);
		if (zypxBm != null) {
			if (Constants.YES.equals(zypxXm.getIsNeedApprove()) && zypxBm.getStatus() != BmStatus.BMCG) {
				zypxBm.setStatus(BmStatus.PENDING);//被驳回的改为待审核
				mapper.updateById(zypxBm);
			}
			deleteAllBmCourseByBmId(zypxBm.getId());
			createStudentBmCourse(zypxBm.getId(), courseSettingIdStr);
		} else {
			zypxBm = new ZypxBm();
			zypxBm.setId(BaseUtil.generateId2());
			zypxBm.setStudentId(studentId);
			zypxBm.setXmId(xmId);
			zypxBm.setIsJtbm(Constants.NO);
			if (!Constants.YES.equals(zypxXm.getIsNeedApprove())) {
				zypxBm.setStatus(BmStatus.BMCG);
			} else {
				zypxBm.setStatus(BmStatus.PENDING);
			}
			zypxBm.setTime(DateUtils.now());
			this.insert(zypxBm);
			createStudentBmCourse(zypxBm.getId(), courseSettingIdStr);
		}
		this.saveStudentFormData(xmId, studentId, session);
	}
	
	/**
	 * 单独的选择课程接口
	 */
	@Transactional
	public void chooseBmCourse(String studentId, String xmId, String courseSettingIds) {
		ZypxBm zypxBm = this.getBmInfo(xmId, studentId);
		if (zypxBm == null) {
			throw BizException.withMessage("学员尚未报名");
		}
		String[] courseSettingIdStr = new String[] {};
		if (StringUtils.isNotEmpty(courseSettingIds)) {
			courseSettingIdStr = StringUtils.split(courseSettingIds, ",");
		}
		else {
			throw BizException.withMessage("请选择课程");
		}
		deleteAllBmCourseByBmId(zypxBm.getId());
		createStudentBmCourse(zypxBm.getId(), courseSettingIdStr);
	}
	
	/**
	 * 保存学员报名的课程
	 */
	@Transactional(rollbackFor = Exception.class)
	public void createStudentBmCourse(String bmId, String[] courseSettingIdStr) {
		if (courseSettingIdStr != null && !"[]".equals(courseSettingIdStr) && courseSettingIdStr.length > 0) {
			ZypxBmCourse entity = new ZypxBmCourse();
			entity.setBmId(bmId);
			entity.setBmTime(DateUtils.now());
			for (String courseSettingId : courseSettingIdStr) {
				entity.setId(BaseUtil.generateId2());
				entity.setCourseSettingId(courseSettingId);
				entity.setBmTime(DateUtils.now());
				bmCourseMapper.insert(entity);
			}
		}
	}
	
	/**
	 * 保存自定义表单填写的信息
	 */
	public void saveStudentFormData(String xmId, String studentId, HttpSession session) {
		String formId = formService.getFormByXmId(xmId);
		if (StringUtils.isNotEmpty(formId)) {
			String key = "FORMDATA_" + xmId + formId + studentId;
			StudentFormDataDto studentFormDataDto = (StudentFormDataDto) session.getAttribute(key);
			if(studentFormDataDto != null) {
				EntityWrapper<FormData> wrapper = new EntityWrapper();
				wrapper.eq("xm_id", xmId);
				wrapper.eq("student_id", studentId);
				formDataMapper.delete(wrapper);
				DBUtils.insertBatch(studentFormDataDto.getFormDatas(), FormData.class);
				studentUserMapper.updateById(studentFormDataDto.getStudentUser());
				studentInfoMapper.updateById(studentFormDataDto.getStudentInfo());
				session.removeAttribute(key);
			}
		}
	}
	
	/**
	 * 删除学员报名的所有课程
	 */
	@Transactional
	public void deleteAllBmCourseByBmId(String bmId) {
		bmCourseMapper.deleteByBmId(bmId);
	}
	
	/**
	 * 撤销项目报名
	 */
	@Transactional
	public void revoke(String bmId, String studentId) {
		ZypxBm zypxBm = mapper.selectById(bmId);
		if (zypxBm == null) {
			throw BizException.withMessage("报名数据不存在");
		}
		if (Constants.YES.equals(zypxBm.getIsPayed())) {
			throw BizException.withMessage("已经缴费的报名数据无法撤销");
		}
		mapper.deleteById(bmId);
		EntityWrapper<FormData> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", zypxBm.getXmId());
		wrapper.eq("student_id", zypxBm.getStudentId());
		formDataService.deleteList(wrapper);
		EntityWrapper<ZypxBmCourse> courseWrapper = new EntityWrapper();
		courseWrapper.eq("bm_id", bmId);
		bmCourseMapper.delete(courseWrapper);
	}
	
	@Transactional
	public void updateStudentInfo(String id, String certiCity, String politicalType, String education,
		String graduateSchool, String mobile, String address, String studentPhoto) {
		StudentInfo entity = studentInfoMapper.selectById(id);
		if (entity == null) {
			throw BizException.withMessage("学生信息不存在");
		}
		entity.setCertiCity(certiCity);
		if (StringUtils.isNotEmpty(politicalType)) {
			entity.setPoliticalType(PoliticalType.valueOf(politicalType));
		}
		if (StringUtils.isNotEmpty(education)) {
			entity.setEducation(Education.valueOf(education));
		}
		entity.setGraduateSchool(graduateSchool);
		entity.setMobile(mobile);
		entity.setAddress(address);
		entity.setStudentPhoto(studentPhoto);
		studentInfoMapper.updateById(entity);
	}

	@Transactional(rollbackFor = Exception.class)
	public StudentUser updateStudentUser(StudentUser user, String orgId, String deptId, String childDeptId) {
		user.setOrgId(orgId);
		studentUserMapper.updateById(user);
		return user;
	}
	
	/**
	 * 获取学员自定义表单的填写值
	 */
	public Map<String, Object> getFormFilledData(String xmId,String formId, String studentId) {
		Map<String, Object> result = new HashMap<>();
		result.put("xmInfo", xmMapper.getFromXmByXmId(xmId));
		StudentUser studentUser = new StudentUser();
		StudentInfo studentInfo = new StudentInfo();
		if (BaseUtil.isNotEmpty(studentId)) {
			studentUser = studentUserMapper.selectById(studentId);
			studentInfo = studentInfoMapper.getByStudentId(studentId);
		}
		List<Map<String, Object>> fieldList = xmMapper.getFormField(xmId, formId);
		for (int i = 0; i < fieldList.size(); i++) {
			Map<String, Object> fieldMap = fieldList.get(i);
			String type = BaseUtil.getStringValueFromMap(fieldMap, "type");
			String submitName = BaseUtil.getStringValueFromMap(fieldMap, "submitName");
			String isConstant = BaseUtil.getStringValueFromMap(fieldMap, "isConstant");
			String fieldId = BaseUtil.getStringValueFromMap(fieldMap, "id");
			FormData formData = formDataService.getFormFieldData(xmId, fieldId, studentId);
			if (FormDatasourceType.STATIC.name().equals(BaseUtil.getStringValueFromMap(fieldMap, "datasourceType"))) {//静态数据源
				List<TextValuePair> mapList  = ModelHelper.convertObject(BaseUtil.getStringValueFromMap(fieldMap, "datasourceValue"));
				fieldMap.put("datasource_value", mapList);
			}
			String fieldValue = null;
			String fieldText = null;
			if (formData != null){
				fieldValue = formData.getFieldValue();
				fieldText = formData.getFieldText();
			}
			if (FormShowType.REGION.name().equals(type) && StringUtils.isNotEmpty(fieldValue)){
				List<Map<String, Object>> dictNames = dictMapper.getRegionNameByAreaCode(fieldValue);
				StringBuilder dictName = new StringBuilder();
				dictNames.forEach(e->{
					dictName.append(e.get("dictName").toString());
				});
				fieldText = dictName.toString();
			}
			if (StringUtils.isEmpty(fieldValue) && Constants.YES.equals(isConstant)) {
				switch (submitName) {
					case Constants.SystemField.STUDENT_TYPE:
						fieldValue = studentUser.getStudentType() != null ? studentUser.getStudentType().name() : null;
						fieldText = studentUser.getStudentType() != null ? studentUser.getStudentType().getName() : null;
						break;
					case Constants.SystemField.NAME:
						fieldValue = studentInfo.getName();
						break;
					case Constants.SystemField.GENDER:
						fieldValue = studentInfo.getGender() != null ? studentInfo.getGender().name() : null;
						fieldText = studentInfo.getGender() != null ? studentInfo.getGender().getName() : null;
						break;
					case Constants.SystemField.NATION:
						fieldValue = studentInfo.getNation() != null ? studentInfo.getNation().name() : null;
						fieldText = studentInfo.getNation() != null ? studentInfo.getNation().getName() : null;
						break;
					case Constants.SystemField.SFZH:
						fieldValue = studentInfo.getSfzh();
						break;
					case Constants.SystemField.EDUCATION:
						fieldValue = studentInfo.getEducation() != null ? studentInfo.getEducation().name() : null;
						fieldText = studentInfo.getEducation() != null ? studentInfo.getEducation().getName() : null;
						break;
					case Constants.SystemField.ADDRESS:
						fieldValue = studentInfo.getAddress();
						break;
					case Constants.SystemField.POST_CODE:
						fieldValue = studentInfo.getPostCode();
						break;
					case Constants.SystemField.MOBILE:
						fieldValue = studentInfo.getMobile();
						break;
					case Constants.SystemField.SFZZM:
						fieldValue = studentInfo.getSfzzm();
						break;
					case Constants.SystemField.SFZFM:
						fieldValue = studentInfo.getSfzfm();
						break;
					case Constants.SystemField.STUDENT_PHOTO:
						fieldValue = studentInfo.getStudentPhoto();
						break;
					case Constants.SystemField.EDU_CERTI_PHOTO:
						fieldValue = studentInfo.getEduCertiPhoto();
						break;
					case Constants.SystemField.COMPANY:
						fieldValue = studentUser.getCompany();
						break;
					case Constants.SystemField.GRADUATE_SCHOOL:
						fieldValue = studentInfo.getGraduateSchool();
						break;
					case Constants.SystemField.COLLEGE:
						fieldValue = studentInfo.getCollege();
						break;
					case Constants.SystemField.SPECIALTY:
						fieldValue = studentInfo.getSpecialty();
						break;
					case Constants.SystemField.CLASSZ:
						fieldValue = studentInfo.getClassz();
						break;
					case Constants.SystemField.STUDENT_TICKET:
						fieldValue = studentInfo.getStudentTicket();
						break;
					case Constants.SystemField.STUDENT_NUMBER:
						fieldValue = studentInfo.getStudentNum();
						break;
					default:
						break;
				}
			}
			if (StringUtils.isNotEmpty(fieldValue)) {
				fieldMap.put("value", fieldValue);
			}
			if (StringUtils.isNotEmpty(fieldText)) {
				fieldMap.put("text", fieldText);
			}
		}
		result.put("formInfo", fieldList);
		return result;
	}
	
	/**
	 * 校验学员报名数据是否可以保存
	 */
	public void checkBmInfoCanBeSaved(String xmId, String studentId, String inviteCode, HttpSession session) {
		ZypxXm zypxXm = xmMapper.selectById(xmId);
		if (zypxXm == null) {
			throw BizException.withMessage("项目不存在");
		}
		if (!Constants.YES.equals(zypxXm.getIsAllowGrbm())) {
			throw BizException.withMessage("当前项目未开放个人报名");
		}
		if (!XmStatus.OK.equals(zypxXm.getStatus())) {
			throw BizException.withMessage("项目已关闭");
		}
		boolean isBmOpen = DateUtils.isBetween(DateUtils.now(), zypxXm.getGrbmStartTime(), zypxXm.getGrbmEndTime());
		if (!isBmOpen) {
			throw BizException.withMessage("操作失败，不在个人报名时间范围之内");
		}
		ZypxBm zypxBm = this.getBmInfo(xmId, studentId);
		if (zypxBm == null) {
			if (zypxXm.getLimitCount() != null) {
				Integer alreadyBmCount = zypxXmService.getBmCountByXmId(xmId);
				if (zypxXm.getLimitCount() - alreadyBmCount <= 0) {
					throw BizException.withMessage("操作失败，总报名人数已满");
				}
			}
		} else {
			if (Constants.YES.equals(zypxXm.getIsNeedApprove()) && zypxBm.getStatus() == BmStatus.BMCG) {
				throw BizException.withMessage("您的报名信息已经审核通过，报名信息不可更改，如需修改，请联系系统管理人员");
			}
		}
		// 校验自定义表单信息是否已填写
		String formId = formService.getFormByXmId(xmId);
		if (StringUtils.isNotEmpty(formId)) {
			String key = "FORMDATA_" + xmId + formId + studentId;
			if (session.getAttribute(key) == null) {
				throw BizException.withMessage("您尚未填写报名信息采集表单，报名信息无法提交");
			}
		}
		// 校验报名口令
		if (StringUtils.isNotEmpty(zypxXm.getBmInviteCode())) {
			if (StringUtils.isEmpty(inviteCode)) {
				throw BizException.withMessage("请填写报名口令");
			}
			if (!inviteCode.equals(zypxXm.getBmInviteCode())) {
				throw BizException.withMessage("您填写的报名口令不正确");
			}
		}
	}
	
	/**
	 * 查询学员已经报名的课程设置ID
	 */
	public List<String> getStudentBmCourseSettingIdList(String xmId, String studentId) {
		List<Map<String, Object>> list = this.getStudentBmCourseList(xmId, studentId);
		List<String> idList = list.stream().map(x -> BaseUtil.getStringValueFromMap(x, "courseSettingId"))
				.collect(Collectors.toList());
		return idList;
	}
	

	
}
