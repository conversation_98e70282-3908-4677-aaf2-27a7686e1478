package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 项目报名和学习卡绑定关联表
 */
@TableName("BIZ_XM_BM_CARD_NUMBER")
public class ZypxXmBmCardNumber implements Serializable {

	private static final long serialVersionUID = 1L;

	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	@TableField("bm_id")
	private String bmId;

	@TableField("card_number")
	private String cardNumber;

	@TableField("product_name")
	private String productName;

	@TableField("product_id")
	private String productId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBmId() {
		return bmId;
	}

	public void setBmId(String bmId) {
		this.bmId = bmId;
	}

	public String getCardNumber() {
		return cardNumber;
	}

	public void setCardNumber(String cardNumber) {
		this.cardNumber = cardNumber;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}
}

    
