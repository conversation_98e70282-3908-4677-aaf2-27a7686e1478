package com.xunw.jxjy.model.personal.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.personal.params.StudentPaperQueryParams;
import com.xunw.jxjy.model.tk.entity.QuestionDBEntity;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.mapper.QuestionDBEntityMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionTt;

/**
 * 职业培训PC端试卷服务  我的练习  阶段测验  结业考试
 */
@Service
public class ZypxStudentPaperService {

	@Autowired
	private ZypxExamPaperMapper mapper;
	@Autowired
	private QuestionEntityMapper questionEntityMapper;
	@Autowired
	private QuestionDBEntityMapper questionDBEntityMapper;

	/**
	 * 学员试卷查询
	 */
	public Page getStudentPaperList(StudentPaperQueryParams params) {
		if (StringUtils.isEmpty(params.getStudentId())) {
			throw BizException.withMessage("studentId不能够为空");
		}
		if (params.getCategory() == null) {
			throw BizException.withMessage("试卷分类不能为空");
		}
		List<Map<String, Object>> list = mapper.studentPaperList(params.getCondition(), params);
		if (CollectionUtils.isEmpty(list)) {
			params.setRecords(list);
			return params;
		}
		//随机抽卷的逻辑
		List<Map<String, Object>> resultList = new ArrayList<>();
		List<Map<String, Object>> chooseList = new ArrayList<>();
		list.forEach(e -> {
			if (StringUtils.isEmpty(BaseUtil.getStringValueFromMap(e, "chooseTag"))) {
				resultList.add(e);
			} else {
				chooseList.add(e);
			}
		});
		// 选考标记相同的试卷，随机抽取一份给考生作答
		if (CollectionUtils.isNotEmpty(chooseList)) {
			Map<String, List<Map<String, Object>>> choosePaperList = chooseList.stream().collect(Collectors.groupingBy(m -> BaseUtil.getStringValueFromMap(m, "chooseTag")));
			for (String tag : choosePaperList.keySet()) {
				List<Map<String, Object>> a = choosePaperList.get(tag);
				List<Map<String, Object>> bList = a.stream().filter(b -> StringUtils.isNotEmpty(BaseUtil.getStringValueFromMap(b, "dataStatus"))).collect(Collectors.toList());
				if (bList != null && bList.size() > 0) {
					resultList.add(bList.get(0));
				} else {
					Random random = new Random();
					Integer index = random.nextInt(a.size());
					resultList.add(a.get(index));
				}
			}
		}
		params.setRecords(resultList);
		return params;
	}

	/**
	 * 根据指定的题库组卷,只设置试卷的大题信息
	 */
	public Paper makerPaperByQuestionDb(String dbId, int score) {
		QuestionDBEntity questionDBEntity = questionDBEntityMapper.selectById(dbId);
		EntityWrapper<QuestionEntity> wrapper = new EntityWrapper();
		wrapper.eq("db_id", questionDBEntity.getId());
		List<QuestionEntity> questionList = questionEntityMapper.selectList(wrapper);
		// 9大题型
		List<QuestionEntity> SINGLECHOICE = new ArrayList<QuestionEntity>();
		List<QuestionEntity> MULTIPLECHOICE = new ArrayList<QuestionEntity>();
		List<QuestionEntity> JUDGMENT = new ArrayList<QuestionEntity>();
		List<QuestionEntity> BLANKFILL = new ArrayList<QuestionEntity>();
		List<QuestionEntity> ESSAY = new ArrayList<QuestionEntity>();
		List<QuestionEntity> MCJST = new ArrayList<QuestionEntity>();
		List<QuestionEntity> LST = new ArrayList<QuestionEntity>();
		List<QuestionEntity> JDT = new ArrayList<QuestionEntity>();
		List<QuestionEntity> TT = new ArrayList<QuestionEntity>();

		for (QuestionEntity questionEntity : questionList) {
			if (Stlb.SINGLECHOICE == questionEntity.getType()) {
				SINGLECHOICE.add(questionEntity);
			} else if (Stlb.MULTIPLECHOICE == questionEntity.getType()) {
				MULTIPLECHOICE.add(questionEntity);
			} else if (Stlb.JUDGMENT == questionEntity.getType()) {
				JUDGMENT.add(questionEntity);
			} else if (Stlb.BLANKFILL == questionEntity.getType()) {
				BLANKFILL.add(questionEntity);
			} else if (Stlb.ESSAY == questionEntity.getType()) {
				ESSAY.add(questionEntity);
			} else if (Stlb.MCJST == questionEntity.getType()) {
				MCJST.add(questionEntity);
			} else if (Stlb.LST == questionEntity.getType()) {
				LST.add(questionEntity);
			} else if (Stlb.JDT == questionEntity.getType()) {
				JDT.add(questionEntity);
			} else if (Stlb.TT == questionEntity.getType()) {
				TT.add(questionEntity);
			}
		}
		// 数据对象
		Paper paper = new Paper();
		this.buildPaperSection(paper, dbId, Stlb.SINGLECHOICE, SINGLECHOICE, score);
		this.buildPaperSection(paper, dbId, Stlb.MULTIPLECHOICE, MULTIPLECHOICE, score);
		this.buildPaperSection(paper, dbId, Stlb.JUDGMENT, JUDGMENT, score);
		this.buildPaperSection(paper, dbId, Stlb.BLANKFILL, BLANKFILL, score);
		this.buildPaperSection(paper, dbId, Stlb.ESSAY, ESSAY, score);
		this.buildPaperSection(paper, dbId, Stlb.MCJST, MCJST, score);
		this.buildPaperSection(paper, dbId, Stlb.LST, LST, score);
		this.buildPaperSection(paper, dbId, Stlb.JDT, JDT, score);
		this.buildPaperSection(paper, dbId, Stlb.TT, TT, score);
		// 设置大题序号、计算卷面总分
		int k = 1;
		int totalScore = 0;
		if(CollectionUtils.isNotEmpty(paper.getSections())) {
			for (PaperSection paperSection : paper.getSections()) {
				paperSection.setName(BaseUtil.toCNLowerNum(k) + "、" + paperSection.getName());
				for (Question question : paperSection.getQuestions()) {
					totalScore += question.getScore();
				}
				k++;
			}
		}
		paper.setTotalScore(totalScore);
		return paper;
	}

	private void buildPaperSection(Paper paper, String dbId, Stlb stlb, List<QuestionEntity> questions, int score) {
		if (questions == null || questions.size() == 0) {
			return;
		}
		PaperSection section = new PaperSection(dbId + "-" + stlb.name(), stlb.getName(), stlb.getName());
		for (QuestionEntity questionEntity : questions) {
			if (stlb.equals(Stlb.TT)) {
				QuestionTt questionTt = new QuestionTt();
				questionTt.setId(questionEntity.getId());
				questionTt.setType(questionEntity.getType());
				questionTt.setContent(questionEntity.getContent());
				// 套题下面的子题
				List<Question> subQuestions = new ArrayList<>();
				EntityWrapper<QuestionEntity> bizTkglStWrapper = new EntityWrapper<>();
				bizTkglStWrapper.eq("parent_id", questionEntity.getId());
				List<QuestionEntity> childQuestions = questionEntityMapper.selectList(bizTkglStWrapper);
				if (childQuestions.size() > 0) {
					for (QuestionEntity child : childQuestions) {
						Question subQues = new Question();
						subQues.setId(child.getId());
						subQues.setType(child.getType());
						subQues.setScore(score);
						subQues.setContent(child.getContent());
						subQuestions.add(subQues);
					}
				}
				questionTt.setScore(score*subQuestions.size());
				questionTt.setChildren(subQuestions);
				section.addQuestion(questionTt);
			} else {
				Question question = new Question();
				question.setId(questionEntity.getId());
				question.setType(questionEntity.getType());
				question.setScore(score);
				question.setContent(questionEntity.getContent());
				question.setAnswer(questionEntity.getAnswer());
				question.setResolve(questionEntity.getResolve());
				section.addQuestion(question);
			}
		}
		section.setRscore(score);
		paper.addSection(section);
	}
	
}
