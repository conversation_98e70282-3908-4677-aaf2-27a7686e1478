package com.xunw.jxjy.model.zyjd.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.toolkit.CollectionUtils;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.enums.ZyjdApproveStatus;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;
import com.xunw.jxjy.model.zyjd.entity.ZcBatchDirector;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmBatch;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.mapper.ZcBatchDirectorMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmBatchMapper;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmBatchQueryParams;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmScopeParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ZyjdBmBatchService extends BaseCRUDService<ZyjdBmBatchMapper, ZyjdBmBatch>{
	
	@Autowired
	private ZyjdBmScopeMapper bmScopeMapper;
    @Autowired
    private ZcBatchDirectorMapper zcBatchDirectorMapper;

	/**
	 * 列表查询
	 */
	public Page list(ZyjdBmBatchQueryParams params){
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}
	
	public List getYears(){
		EntityWrapper<ZyjdBmBatch> wrapper = new EntityWrapper<>();
		wrapper.isNotNull("years");
		wrapper.orderBy("years", true);
		List<ZyjdBmBatch> list = this.selectList(wrapper);
		List<String > listYears = list.stream().map(x -> x.getYears()).distinct().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
		return listYears;
	}
	
	public Page approveList(ZyjdBmBatchQueryParams params){
		List<Map<String, Object>> list = mapper.list(params.getCondition(),params);
		ZyjdBmScopeParams scopeParams;
		for (Map<String, Object> map : list){
			scopeParams = new ZyjdBmScopeParams();
			scopeParams.setBmbatchId(map.get("id").toString());
			List<Map<String,Object>> scopeList = bmScopeMapper.list(scopeParams.getCondition(), scopeParams);
			List<String> professionList = new ArrayList<String>();
			for (Map<String,Object> scope : scopeList) {
				String professionName = BaseUtil.getStringValueFromMap(scope, "professionName");
				String techLevel =  BaseUtil.getStringValueFromMap(scope, "techLevel");
				if (StringUtils.isEmpty(techLevel)) {
					professionList.add(professionName);
					continue;
				}
				String[] array = StringUtils.split(techLevel, ",");
				List<String> nameList = new ArrayList<String>();
				for (String vstr : array) {
					TechLevel sbjsdj = TechLevel.findByEnumName(vstr);
					if(sbjsdj != null) {
						nameList.add(sbjsdj.getName());
					}
				}
				professionName = professionName + "【" + StringUtils.join(nameList,",")+"】";
				professionList.add(professionName);
			}
			map.put("professions", CollectionUtils.isNotEmpty(professionList) ? StringUtils.join(professionList,"<br>"):"");
		}
		params.setRecords(list);
		return params;
	}
	
	@Transactional
	public void doApprove(String id , ZyjdApproveStatus status , String approveAdvice,String userId){
		ZyjdBmBatch bmBatch = mapper.selectById(id);
		bmBatch.setApproveStatus(status);
		bmBatch.setApproveUserId(userId);
		bmBatch.setApproveTime(DateUtils.now());
		bmBatch.setApproveAdvice(approveAdvice);
		mapper.updateById(bmBatch);
	}
	
	/**
	 * 根据类型取最近开放的批次
	 */
	public ZyjdBmBatch getLatestOpenBatch(ZyjdBmBatchType type, String hostOrgId) {
		EntityWrapper<ZyjdBmBatch> wrapper = new EntityWrapper<>();
		wrapper.eq("type", type);
		wrapper.eq("status", Zt.OK);
		wrapper.eq("host_org_id", hostOrgId);
		wrapper.orderBy("create_time", false);
		List<ZyjdBmBatch> list = this.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}
	
	/**
	 * 技能申报保存
	 */
	@Transactional
	public void saveSkillApply(ZyjdBmBatch zyjdBmBatch, List<ZyjdBmScope> list) {
		if (mapper.selectById(zyjdBmBatch.getId()) != null) {
			mapper.updateAllColumnById(zyjdBmBatch);
			EntityWrapper<ZyjdBmScope> wrapper = new EntityWrapper();
			wrapper.eq("bmbatch_id", zyjdBmBatch.getId());
			bmScopeMapper.delete(wrapper);
		}
		else {
			mapper.insert(zyjdBmBatch);
		}
		DBUtils.insertBatch(list, ZyjdBmScope.class);
	}
	
	/**
	 * 获取报名范围
	 */
	public List<ZyjdBmScope> getBmScopeByBatchId(String batchId){
		EntityWrapper<ZyjdBmScope> scopeWrapper = new EntityWrapper();
		scopeWrapper.eq("bmbatch_id", batchId);
		List<ZyjdBmScope> bmScopes = bmScopeMapper.selectList(scopeWrapper);
		return bmScopes;
	}

	public List<ZyjdBmBatch> getOpenBmBatchByProfessionId(String id, String hostOrgId) {
		return mapper.getOpenBmBatchByProfessionId(id, hostOrgId);
	}

	@Transactional(rollbackFor = Exception.class)
	public void setDirector(String id, String directorId, String supervisor, String deputyDirectorIds) {
		ZyjdBmBatch zyjdBmBatch = mapper.selectById(id);
		if (zyjdBmBatch == null) {
			throw BizException.withMessage("批次不存在");
		}
		zyjdBmBatch.setDirector(directorId);
		zyjdBmBatch.setSupervisor(supervisor);
		mapper.updateById(zyjdBmBatch);
		zcBatchDirectorMapper.delete(new EntityWrapper<ZcBatchDirector>().eq("batch_id", id));
		ArrayList<ZcBatchDirector> directors = new ArrayList<>();
		for (String dId : deputyDirectorIds.split(",")) {
			ZcBatchDirector zcBatchDirector = new ZcBatchDirector();
			zcBatchDirector.setId(BaseUtil.generateId2());
			zcBatchDirector.setBatchId(id);
			zcBatchDirector.setUserId(dId);
			directors.add(zcBatchDirector);
		}
		DBUtils.insertBatch(directors, ZcBatchDirector.class);
	}

	public Map<String, Object> directorDetail(String id) {
		ZyjdBmBatch zyjdBmBatch = mapper.selectById(id);
		if (zyjdBmBatch == null) {
			throw BizException.withMessage("批次不存在");
		}
		Map<String, Object> result = new HashMap<>();
		result.put("director", zyjdBmBatch.getDirector());
		result.put("supervisor", zyjdBmBatch.getSupervisor());
		List<ZcBatchDirector> zcBatchDirectors = zcBatchDirectorMapper.selectList(new EntityWrapper<ZcBatchDirector>().eq("batch_id", id));
		result.put("deputyDirector", zcBatchDirectors.stream().map(ZcBatchDirector::getUserId).collect(Collectors.toList()));
		return result;
	}
}
