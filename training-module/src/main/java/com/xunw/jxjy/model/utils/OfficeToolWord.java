package com.xunw.jxjy.model.utils;


import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.xunw.jxjy.common.utils.General;
import com.xunw.jxjy.common.utils.docx.DocxReader;
import com.xunw.jxjy.common.utils.docx.HtmlCreator;
import com.xunw.jxjy.common.utils.docx.ResourceInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Consts;
import org.dom4j.Attribute;
import org.dom4j.DocumentHelper;
import org.dom4j.io.SAXReader;
import org.jsoup.Jsoup;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
import org.springframework.web.util.HtmlUtils;
import com.lowagie.text.*;
import com.lowagie.text.html.simpleparser.HTMLWorker;
import com.lowagie.text.html.simpleparser.StyleSheet;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.rtf.RtfWriter2;
import com.lowagie.text.rtf.field.RtfPageNumber;
import com.lowagie.text.rtf.headerfooter.RtfHeaderFooter;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.common.utils.SpringBeanUtils;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.paper.model.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.HtmlUtils;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.ws.rs.OPTIONS;

public class OfficeToolWord {

	private static final Logger LOGGER = LoggerFactory.getLogger(OfficeToolWord.class);
	private static String TECH_INFO = "";
	private static String FOOT_INFO = "";

	/**
	 * 用于临时使用的body节点开始标签
	 */
	public static final String PAPERTMPBODY_START = "<paperTmpBody xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\" xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\" xmlns:w15=\"http://schemas.microsoft.com/office/word/2012/wordml\" xmlns:wpc=\"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas\" xmlns:mc=\"http://schemas.openxmlformats.org/markup-compatibility/2006\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\" xmlns:m=\"http://schemas.openxmlformats.org/officeDocument/2006/math\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:wp14=\"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing\" xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" xmlns:w10=\"urn:schemas-microsoft-com:office:word\" xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\" xmlns:w14=\"http://schemas.microsoft.com/office/word/2010/wordml\" xmlns:wpg=\"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup\" xmlns:wpi=\"http://schemas.microsoft.com/office/word/2010/wordprocessingInk\" xmlns:wne=\"http://schemas.microsoft.com/office/word/2006/wordml\" xmlns:wps=\"http://schemas.microsoft.com/office/word/2010/wordprocessingShape\" mc:Ignorable=\"w14 wp14\">";

	/**
	 * 普通文本的P标签模板字符串
	 */
	public static final String NORMAL_P_TMP = "<w:p w:rsidR=\"00285782\" w:rsidRDefault=\"00285782\"><w:pPr><w:rPr><w:rFonts w:hint=\"eastAsia\"/></w:rPr></w:pPr><w:r><w:rPr><w:rFonts w:hint=\"eastAsia\"/><w:sz w:val=\"22\"/></w:rPr><w:t xml:space=\"preserve\">XX</w:t></w:r></w:p>";

	/**
	 * 用于临时使用的body节点结束标签
	 */
	public static final String PAPERTMPBODY_END = "</paperTmpBody>";



	@SuppressWarnings("unused")
	private void makeWordDocument(String path, String WTITLE)
			throws DocumentException, IOException {
		Document document = new Document(PageSize.A4);
		RtfWriter2.getInstance(document, new FileOutputStream(path));
		document.open();

		//使用classpath下的字体 田军  2021-07-28
		BaseFont bfChinese = BaseFont.createFont("/fonts/simsun.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

		Font titleFont = new Font(bfChinese, 14.0F, 1);

		Font subTitleFont = new Font(bfChinese, 11.0F, 1);

		Font contextFont = new Font(bfChinese, 10.0F, 0);

		Font headerFooterFont = new Font(bfChinese, 9.0F, 0);

		Table header = new Table(2);
		header.setBorder(0);
		header.setWidth(100.0F);

		Paragraph address = new Paragraph(TECH_INFO);
		address.setFont(headerFooterFont);
		Cell cell01 = new Cell(address);
		cell01.setBorder(0);
		header.addCell(cell01);

		Paragraph date = new Paragraph("生成日期: "
				+ new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
		date.setAlignment(2);
		date.setFont(headerFooterFont);
		cell01 = new Cell(date);
		cell01.setBorder(0);
		header.addCell(cell01);
		document.setHeader(new RtfHeaderFooter(header));

		Table footer = new Table(2);
		footer.setBorder(0);
		footer.setWidth(100.0F);

		Paragraph company = new Paragraph(FOOT_INFO);
		company.setFont(headerFooterFont);
		Cell cell02 = new Cell(company);
		cell02.setBorder(0);
		footer.addCell(cell02);

		Paragraph pageNumber = new Paragraph("第 ");
		pageNumber.add(new RtfPageNumber());
		pageNumber.add(new Chunk(" 页"));
		pageNumber.setAlignment(2);
		pageNumber.setFont(headerFooterFont);
		cell02 = new Cell(pageNumber);
		cell02.setBorder(0);
		footer.addCell(cell02);

		document.setFooter(new RtfHeaderFooter(footer));

		Paragraph title = new Paragraph(WTITLE);
		title.setAlignment(1);
		title.setFont(titleFont);
		document.add(title);

		for (int i = 0; i < 5; ++i) {
			Paragraph subTitle = new Paragraph((i + 1) + "、标题" + (i + 1));
			subTitle.setFont(subTitleFont);
			subTitle.setSpacingBefore(10.0F);
			subTitle.setFirstLineIndent(0.0F);
			document.add(subTitle);

			for (int j = 0; j < 3; ++j) {
				String contextString = (j + 1)
						+ "."
						+ "iText是一个能够快速产生PDF文件的java类库。iText的java类对于那些要产生包含文本，表格，图形的只读文档是很有用的。";
				Paragraph context = new Paragraph(contextString);
				context.setAlignment(0);
				context.setFont(contextFont);
				context.setSpacingBefore(10.0F);
				context.setFirstLineIndent(0.0F);
				document.add(context);

				for (short k = 0; k < 4; k = (short) (k + 1)) {
					char enString = 'A';
					String answerString = (char) (enString + k) + "."
							+ "表格，图形的只读文档是很有用的。";
					Paragraph answer = new Paragraph(answerString);
					answer.setAlignment(0);
					answer.setFont(contextFont);
					answer.setSpacingBefore(10.0F);
					answer.setFirstLineIndent(20.0F);
					document.add(answer);
				}

			}

		}

		document.close();
	}

	public static void makePaperDoc(OutputStream os, Paper paper, boolean isShowScore) throws Exception {
		if (paper == null) {
			throw new Exception("试卷不存在");
		}
		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String storepath = attConfig.getTempdir();//本地临时存储路径
		File tmpFolder = new File(storepath,"tmp");
		tmpFolder = new File(tmpFolder,BaseUtil.generateId());
		if(!tmpFolder.exists()){
			tmpFolder.mkdirs();
		}

		try {
			Document document = new Document(PageSize.A4);
			RtfWriter2.getInstance(document, os);
			document.open();
			//使用classpath下的字体 田军  2021-07-28
			BaseFont bfChinese = BaseFont.createFont("/fonts/simsun.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);


			Font titleFont = new Font(bfChinese, 14.0F, 1);
			Font subTitleFont = new Font(bfChinese, 11.0F, 1);
			Font subTitleFont2 = new Font(bfChinese, 10.0F, 1);
			Font contextFont = new Font(bfChinese, 10.0F, 0);
			Font headerFooterFont = new Font(bfChinese, 9.0F, 0);

			Table header = new Table(2);
			header.setBorder(0);
			header.setWidth(100.0F);

			Paragraph address = new Paragraph(TECH_INFO);
			address.setFont(headerFooterFont);
			Cell cell01 = new Cell(address);
			cell01.setBorder(0);
			header.addCell(cell01);

			Paragraph date = new Paragraph("");
			date.setAlignment(2);
			date.setFont(headerFooterFont);
			cell01 = new Cell(date);
			cell01.setBorder(0);
			header.addCell(cell01);
			document.setHeader(new RtfHeaderFooter(header));

			Table footer = new Table(2);
			footer.setBorder(0);
			footer.setWidth(100.0F);

			Paragraph company = new Paragraph(FOOT_INFO);
			company.setFont(headerFooterFont);
			Cell cell02 = new Cell(company);
			cell02.setBorder(0);
			footer.addCell(cell02);

			Paragraph pageNumber = new Paragraph("第 ");
			pageNumber.add(new RtfPageNumber());
			pageNumber.add(new Chunk(" 页"));
			pageNumber.setAlignment(Element.ALIGN_LEFT);
			pageNumber.setFont(headerFooterFont);
			cell02 = new Cell(pageNumber);
			cell02.setBorder(0);
			footer.addCell(cell02);

			document.setFooter(new RtfHeaderFooter(footer));

			Paragraph title = new Paragraph(HtmlUtils.htmlUnescape(paper.getName()));
			title.setAlignment(Element.ALIGN_CENTER);
			title.setFont(titleFont);
			document.add(title);

			if (isShowScore) {
				Paragraph title2 = new Paragraph("卷面总分 : " + paper.getTotalScore() + "    及格分数 : " + paper.getPassedScore());
				title2.setAlignment(1);
				title2.setFont(subTitleFont2);
				title2.setSpacingBefore(20);
				title2.setSpacingAfter(20);
				document.add(title2);
			}

			List<PaperSection> sections = paper.getSections();
			int rownbr = 0;
			if ((sections != null) && (sections.size() > 0)) {
				int secIndex = 1;
				for (PaperSection section : sections) {
					//获取所有的小题
					List<Question> questions = section.getQuestions();
					//计算大题分数
					if (isShowScore) {
						int quesSize = 0;
						int perScore = 0;
						int totalScore = 0;
						if(questions != null){
							quesSize = questions.size();
							if(quesSize > 0){
								perScore = questions.get(0).getScore();
							}
							totalScore = quesSize * perScore;
						}
						Paragraph subTitle = new Paragraph(BaseUtil.toCNLowerNum(secIndex++) + "、" + section.getRemark() +
								"（本大题共" + quesSize + "小题，每小题" + perScore + "分，共" + totalScore + "分）");
						subTitle.setFont(subTitleFont);
						subTitle.setSpacingBefore(10.0F);
						subTitle.setFirstLineIndent(0.0F);
						document.add(subTitle);
					}
					else {
						Paragraph subTitle = new Paragraph(BaseUtil.toCNLowerNum(secIndex++) + "、" + section.getRemark());
						subTitle.setFont(subTitleFont);
						subTitle.setSpacingBefore(10.0F);
						subTitle.setFirstLineIndent(0.0F);
						document.add(subTitle);
					}
					if ((questions != null) && (questions.size() > 0)) {
						if (paper.getQuesSortType().equals(Stplsx.SJ)) {
							Collections.shuffle(questions);
						}

						for (Question question : questions) {
							//富文本转文本
							question.setContent(getTextAndImg(question.getContent()));

							Stlb stlb = question.getType();
							++rownbr;

							String question_content = "";
							if (stlb.equals(Stlb.BLANKFILL)) {
								question_content = BaseUtil.FormatBlankQuestions(question.getContent(), "_______");
							}else {
								question_content = question.getContent();
							}
							Paragraph context = new Paragraph();
							context.setAlignment(0);
							context.setFont(contextFont);
							context.setSpacingBefore(10.0F);
							context.setFirstLineIndent(0.0F);
	//						question_content = BaseUtil.Html2TextFormat(question_content);
							{
								String html = dealImg(question_content,tmpFolder);
								html = html.replaceAll("<p .*?>", "<p>");//去除所有P标签中的style 田军  2022-06-30修改优化
								if(html.startsWith("<p>")){
									html = "<p>" + rownbr + "." + html.substring("<p>".length());
								}else{//说明不是HTML片段，而是纯文本片段，需要把<>&特殊符号处理一下
									html = "<p>" + rownbr + "." + replaceHTMLChar(html) + "</p>";
								}
								StyleSheet ss = new StyleSheet();
						        List htmlList = HTMLWorker.parseToList(new StringReader(html), ss);
						        for (int i = 0; i < htmlList.size(); i++) {
						            com.lowagie.text.Element e = (com.lowagie.text.Element) htmlList.get(i);
						            context.add(e);
						        }
							}

							StringBuffer OPTIONS = new StringBuffer("");

							if (stlb.equals(Stlb.SINGLECHOICE)) {
								QuestionSingleChoice _question = (QuestionSingleChoice) question;
								List<Option> LIST_OPTIONS = _question.getOptions();
								if ((LIST_OPTIONS != null) && (LIST_OPTIONS.size() > 0)) {
									for (Option option : LIST_OPTIONS){
										option.setText(getTextAndImg(option.getText()));
										String html = dealImg(option.getText(),tmpFolder);
										html = html.replaceAll("<p .*?>", "<p>");//去除所有P标签中的style 田军  2022-06-30修改优化
										if(html.startsWith("<p>")){
											html = "<p>" + option.getAlisa() + "." + html.substring("<p>".length());
										}else{
											html = "<p>" + option.getAlisa() + "." + replaceHTMLChar(html) + "</p>";
										}
										OPTIONS.append(html);
									}
								}
							}else if (stlb.equals(Stlb.MULTIPLECHOICE)) {
								QuestionMultipleChoice _question = (QuestionMultipleChoice) question;
								List<Option> LIST_OPTIONS = _question.getOptions();
								if ((LIST_OPTIONS != null) && (LIST_OPTIONS.size() > 0)) {
									for (Option option : LIST_OPTIONS){
										option.setText(getTextAndImg(option.getText()));
										String html = dealImg(option.getText(),tmpFolder);
										html = html.replaceAll("<p .*?>", "<p>");//去除所有P标签中的style 田军  2022-06-30修改优化
										if(html.startsWith("<p>")){
											html = "<p>" + option.getAlisa() + "." + html.substring("<p>".length());
										}else{
											html = "<p>" + option.getAlisa() + "." + replaceHTMLChar(html) + "</p>";
										}
										OPTIONS.append(html);
									}
								}
							}

							if(OPTIONS.length() > 0){
								String html = OPTIONS.toString();
								StyleSheet ss = new StyleSheet();
						        List htmlList = HTMLWorker.parseToList(new StringReader(html), ss);
						        for (int i = 0; i < htmlList.size(); i++) {
						            com.lowagie.text.Element e = (com.lowagie.text.Element) htmlList.get(i);
						            context.add(e);
						        }
							}

							String answer = null;
							if (stlb.equals(Stlb.JUDGMENT)) {
								answer = ("Y".equals(question.getAnswer())?"正确":"错误");
							}else if (stlb.equals(Stlb.BLANKFILL)) {
								QuestionBlankFill bfQues = (QuestionBlankFill)question;
								if(bfQues.getBlanks() != null){
									for(QBlank blank:bfQues.getBlanks()){
										answer = blank.getValue() + " 、 ";
									}
								}
								if(answer != null){
									answer = answer.substring(0,answer.length() - " 、 ".length());
								}
							}else{
								answer = question.getAnswer();
							}
							answer = "<br/><b>标准答案 :</b>" + replaceHTMLChar(answer);
							{
								String html = dealImg(answer,tmpFolder);
								StyleSheet ss = new StyleSheet();
						        List htmlList = HTMLWorker.parseToList(new StringReader(html), ss);
						        for (int i = 0; i < htmlList.size(); i++) {
						            com.lowagie.text.Element e = (com.lowagie.text.Element) htmlList.get(i);
						            context.add(e);
						        }
							}
					        document.add(context);
						}
					}
				}
			}
			document.close();
		} finally {
			FileHelper.delFile(tmpFolder);
		}
	}


	public static File makePaperDocA3(OutputStream os, Paper paper) throws Exception {
		if (paper == null) {
			throw new Exception("试卷不存在");
		}

		File newDocx = null;
		DocxReader templetDocxReader = null;
		Map<String, ResourceInfo> needToUpdate = new HashMap<String, ResourceInfo>();


		AttConfig attConfig = SpringBeanUtils.getBean(AttConfig.class);
		String storepath = attConfig.getTempdir();//本地临时存储路径

		File tmpFolder = new File(storepath,"tmp");
		tmpFolder = new File(tmpFolder,BaseUtil.generateId());
		if(!tmpFolder.exists()){
			tmpFolder.mkdirs();
		}

		try {
			{// 拷贝一份模板文件到临时文件夹里面
				FileOutputStream fos = null;
				InputStream is = null;
				try {

					newDocx = new File(storepath, UUID.randomUUID().toString().replace("-", ""));
					fos = new FileOutputStream(newDocx);
					//is = OfficeToolWord.class.getResourceAsStream("/docxTmps/paper.docx");
					is = OfficeToolWord.class.getResourceAsStream("/docxTmps/paper.docx");
					IOUtils.copyLarge(is, fos);
					fos.flush();



				} finally {
					General.close(is);
					General.close(fos);
				}
			}
			templetDocxReader = new DocxReader(newDocx);
			templetDocxReader.doRead();
			//needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),templetDocxReader.getDocumentInfo());
			{
				List<ResourceInfo> headersAndFooters = new ArrayList<ResourceInfo>();
				headersAndFooters.addAll(templetDocxReader.getHeaders());
				headersAndFooters.addAll(templetDocxReader.getFooters());
				for (ResourceInfo info : headersAndFooters) {
					FileOutputStream fos = null;
					InputStream is = null;
					try {
						is = new FileInputStream(info.getFile());
						String docStr = IOUtils.toString(is, "UTF-8");
/*								.replace("paperName", General.XMLEncode(paper.getName()))
								.replace("totalScore", General.XMLEncode(paper.getTotalScore()+""))
								.replace("passedScore",General.XMLEncode(paper.getPassedScore()+""));*/
						//System.out.println(IOUtils.toString(is, "UTF-8"));
						fos = new FileOutputStream(info.getFile(), false);
						IOUtils.write(docStr, fos, "UTF-8");
					} catch (Exception e){
						e.printStackTrace();
					}finally {
						General.close(is);
						General.close(fos);
					}
					needToUpdate.put(info.getPathInZip(), info);
				}
			}
			{
				needToUpdate.put(templetDocxReader.getDocumentInfo().getPathInZip(),
						templetDocxReader.getDocumentInfo());
				File document = templetDocxReader.getDocumentInfo().getFile();
				FileOutputStream fos = null;
				InputStream is = null;

					try {
						is = new FileInputStream(document);
						String docStr = IOUtils.toString(is, "UTF-8")
								.replace("paperName", General.XMLEncode(paper.getName()))
								.replace("totalScore", General.XMLEncode(paper.getTotalScore()+""))
								.replace("passedScore",General.XMLEncode(paper.getPassedScore()+""));
						String xmlns_w15 = "xmlns:w15=\"http://schemas.microsoft.com/office/word/2012/wordml\"";
						if (!docStr.contains(xmlns_w15)) {
							docStr = docStr.replace("<w:document ", "<w:document " + xmlns_w15 + " ");
						}
						String xmlns_w14 = "xmlns:w14=\"http://schemas.microsoft.com/office/word/2010/wordml\"";
						if (!docStr.contains(xmlns_w14)) {
							docStr = docStr.replace("<w:document ", "<w:document " + xmlns_w14 + " ");
						}
						String xmlns_a = "xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\"";
						if (!docStr.contains(xmlns_a)) {
							docStr = docStr.replace("<w:document ", "<w:document " + xmlns_a + " ");
						}
						fos = new FileOutputStream(document, false);
						IOUtils.write(docStr, fos, "UTF-8");
					} finally {
						General.close(is);
						General.close(fos);
					}
			}
			{
				File document = templetDocxReader.getDocumentInfo().getFile();
				FileOutputStream fosNew = null;
				FileOutputStream fos = null;
				InputStream is = null;

				try {
					is = new FileInputStream(document);
					String docStr = IOUtils.toString(is, "UTF-8");
					int index1 = docStr.indexOf("##试题开始##");// "##试题开始##"的位置
					int index2 = docStr.substring(0, index1).lastIndexOf("<w:p>");// "##试题开始##"对应的<w:p>标签的开始位置
					int index3 = index1 + docStr.substring(index1).indexOf("</w:p>");// "##试题开始##"对应的<w:p>标签的结束位置

					int index4 = docStr.indexOf("##试题结束##");// "##试题结束##"的位置
					int index5 = docStr.substring(0, index4).lastIndexOf("<w:p>");// "##试题结束##"对应的<w:p>标签的开始位置
					int index6 = index4 + docStr.substring(index4).indexOf("</w:p>");// "##试题结束##"对应的<w:p>标签的结束位置

					String head = docStr.substring(0, index2);// ##试题开始##"前面的部分
					String quTmpStr = docStr.substring(index3 + "</w:p>".length(), index5);// 题目模板
					String tail = docStr.substring(index6 + "</w:p>".length());// "##试题结束##"后面的部分

					int index7 = quTmpStr.indexOf("##小题序号##");
					int index8 = quTmpStr.substring(0, index7).lastIndexOf("<w:p>");// 小题对应的<w:p>标签的开始位置
					int index9 = index7 + quTmpStr.substring(index7).indexOf("</w:p>");// 小题对应的<w:p>标签的结束位置

					int index10 = quTmpStr.indexOf("##答题项##");
					int index12 = index10 + quTmpStr.substring(index10).indexOf("</w:p>");// 答题项对应的<w:p>标签的结束位置


					fosNew = new FileOutputStream(document, false);
					IOUtils.write(head, fosNew, "UTF-8");
					General.close(fosNew);

					fos = new FileOutputStream(document, true);

					String qtTmpHeadStr = quTmpStr.substring(0, index8);
					String qtTmpTailStr = quTmpStr.substring(index12 + "</w:p>".length());
					String xtTmpStr = quTmpStr.substring(index8, index9 + "</w:p>".length());

					int quNo = 0;
					List<PaperSection> sections = paper.getSections();
					int rownbr = 0;
					if ((sections != null) && (sections.size() > 0)) {
						int secIndex = 1;
						for (PaperSection section : sections) {
							int quesSize = 0;
							int perScore = 0;
							int totalScore = 0;
							List<Question> questions = section.getQuestions();
							if(questions != null){
								quesSize = questions.size();
								if(quesSize > 0){
									perScore = questions.get(0).getScore();
								}
								totalScore = quesSize * perScore;
							}

							String str1= BaseUtil.toCNLowerNum(secIndex++) + "、" + General.XMLEncode(section.getRemark())
									+ "（本大题共" + quesSize + "小题，每小题:"+	General.formatScore(perScore) + "分"+""

									+ "，共" + General.formatScore(totalScore) + "分"
									+ "）";
							String qtypStr = qtTmpHeadStr
									.replace("##大题描述##", str1);
							System.out.println(qtypStr);
							IOUtils.write(qtypStr, fos, "UTF-8");

							if ((questions != null) && (questions.size() > 0)) {
								for (Question question : questions) {
									Stlb stlb = question.getType();
									++rownbr;
									String xtStr = null;
									String html = dealImg(question.getContent(), tmpFolder);
									html = html.replaceAll("<p .*?>", "<p>");//去除所有P标签中的style 田军  2022-06-30修改优化
									html = BaseUtil.FormatBlankQuestions(html, "_______");
									html = html.replace("&nbsp;","");
									xtStr = xtTmpStr.replace("##小题序号##", "" + rownbr).replace("##题干##",
											General.XMLEncode(BaseUtil.delHTMLTag(html)).trim());
									{
									byte[] wordBytes = null;
									wordBytes = question.getContent().getBytes();

									int firstWtBeginIndex = xtStr.indexOf("<w:t>");
									int firstWtBeginIndex2 = xtStr.indexOf("<w:t");
									firstWtBeginIndex = (firstWtBeginIndex < 0
											|| (firstWtBeginIndex2 >= 0 && firstWtBeginIndex > firstWtBeginIndex2))
											? firstWtBeginIndex2 : firstWtBeginIndex;

									int nearestGtIndex = xtStr.indexOf('>', firstWtBeginIndex + 1);

									String part1 = xtStr.substring(0, nearestGtIndex + 1).trim();
									String part2 = xtStr.substring(nearestGtIndex + 1).trim();
									//xtStr = part1 + quNo + "．" + part2;
									xtStr = part1 + " " + part2;
								}
									int firstWtBeginIndex1 = xtStr.indexOf("<w:t>");
									int firstWtBeginIndex2 = xtStr.indexOf("<w:t");
									firstWtBeginIndex1 = (firstWtBeginIndex1 < 0
											|| (firstWtBeginIndex2 >= 0 && firstWtBeginIndex1 > firstWtBeginIndex2))
											? firstWtBeginIndex2 : firstWtBeginIndex1;

									StringBuffer OPTIONS = new StringBuffer("");
									List<Option> LIST_OPTIONS = new ArrayList<>();
									if (stlb.equals(Stlb.SINGLECHOICE) || stlb.equals(Stlb.MULTIPLECHOICE)) {
										if(stlb.equals(Stlb.SINGLECHOICE)){
											QuestionSingleChoice _question = (QuestionSingleChoice) question;
											LIST_OPTIONS = _question.getOptions();
										}else if(stlb.equals(Stlb.MULTIPLECHOICE)){
											QuestionMultipleChoice _question = (QuestionMultipleChoice) question;
											LIST_OPTIONS = _question.getOptions();
										}
										if (LIST_OPTIONS.size() > 0) {
											String option_temp = null;
											if (LIST_OPTIONS.size() <= 4) {
												xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr>");
												option_temp = "</w:p>";
												String before_str = StringUtils.substringBeforeLast(xtStr, "</w:p>");
												String after_str = StringUtils.substringAfterLast(xtStr, "</w:p>");
												xtStr = before_str + option_temp + after_str;
											} else {
												xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr>");
//										option_temp = "<w:r><w:tab/></w:r><w:r><w:t>【        】</w:t></w:r></w:p>";
												option_temp = "</w:p>";
												String before_str = StringUtils.substringBeforeLast(xtStr, "</w:p>");
												String after_str = StringUtils.substringAfterLast(xtStr, "</w:p>");
												xtStr = before_str + option_temp + after_str;
											}
										}

									}

									int optSize = LIST_OPTIONS.size();

									if (optSize > 0) {
										String option_temp = null;
										if (optSize <= 4) {
											xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr>");
											option_temp = "</w:p>";
											String before_str = StringUtils.substringBeforeLast(xtStr, "</w:p>");
											String after_str = StringUtils.substringAfterLast(xtStr, "</w:p>");
											xtStr = before_str + option_temp + after_str;
										} else {
											xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr>");
//										option_temp = "<w:r><w:tab/></w:r><w:r><w:t>【        】</w:t></w:r></w:p>";
											option_temp = "</w:p>";
											String before_str = StringUtils.substringBeforeLast(xtStr, "</w:p>");
											String after_str = StringUtils.substringAfterLast(xtStr, "</w:p>");
											xtStr = before_str + option_temp + after_str;
										}
									}
									IOUtils.write(addBeforeLines(xtStr, 0, 0, 0), fos, "UTF-8");

									if (optSize > 0) {
										String optsStr = "";
										for (int m = 0; m < optSize; m++) {
											Option opt = LIST_OPTIONS.get(m);
											String seqNum = String.valueOf(((char) ('A' + m)));
											String prefixStr = "	" + seqNum + "．";
											String anStr = null;
											if ((opt.getText() == null || opt.getText().length() == 0)
													&& General.isEmpty(opt.getText())) {
												String optTxt = General.XMLEncode(opt.getText()).trim();
												anStr = NORMAL_P_TMP.replace("XX", prefixStr + optTxt);
											} else {
												try {
													byte[] wordBytes = null;
													StringBuffer htmlStr = new StringBuffer();
													if ((opt.getText() == null || opt.getText().length() == 0)
															&& !General.isEmpty(opt.getText())) {
														wordBytes = String.valueOf(HtmlCreator.covertToHtml(opt.getText().getBytes())).getBytes();
													} else {
														htmlStr.append("<html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
														htmlStr.append("<style>.wordTd_disabled{border:solid windowtext 1pt;padding:0cm 5.4pt 0cm 5.4pt;} p{white-space:sWrap;word-wrap:break-word;} span{white-space:sWrap;word-wrap:break-word;}</style></head><body>");
														//htmlStr.append(opt.getText().replace("<p>","<p style=\"\"><span style=\"font-family:Calibri;\">").replace("</p>","</span></p>"));
														htmlStr.append(BaseUtil.delHTMLTag(opt.getText()));
														htmlStr.append("</body></html>");
														System.out.println(htmlStr);
														wordBytes = General.htmlToDocx(htmlStr.toString());
														/*String htmlStr1 = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8' /><style>.wordTd_disabled{border:solid windowtext 1pt;padding:0cm 5.4pt 0cm 5.4pt;} p{white-space:sWrap;word-wrap:break-word;} span{white-space:sWrap;word-wrap:break-word;}</style></head><body><p style=''><span style='font-family:Calibri;'>审定制度</span></p></body></html>";
														wordBytes = General.htmlToDocx(htmlStr1);*/
													}
													anStr = General.dealWord(wordBytes, templetDocxReader, needToUpdate);
													int firstWtBeginIndex = -1;
													if (firstWtBeginIndex < 0) {
														int firstWpBeginIndex = anStr.indexOf(">");// 默认第一个节点是<w:p
														// ...>，不支持答题项第一个元素是表格
														String part1 = anStr.substring(0, firstWpBeginIndex + ">".length())
																.trim();
														String part2 = anStr.substring(firstWpBeginIndex + ">".length())
																.replace("\"center\"", "\"left\"")
																.replace("\"right\"", "\"left\"").trim();
														anStr = part1 + part2;
														/*****
														 * 往anStr里面添加
														 * <w:jc w:val="left"/>
														 *****/
														{
															if (!anStr.contains("<w:jc w:val=\"left\"/>")) {
																int pPrIndex = anStr.indexOf("<w:pPr>");
																if (pPrIndex < 0) {
																	anStr = part1 + "<w:pPr><w:jc w:val=\"left\"/></w:pPr>" + part2;
																} else {
																	anStr = anStr.substring(0,
																			pPrIndex + "<w:pPr>".length())
																			+ "<w:jc w:val=\"left\"/>" + anStr.substring(
																			pPrIndex + "<w:pPr>".length());
																}
															}
														}
														anStr = anStr.replaceAll("</w:pPr>", "</w:pPr><w:r><w:t>" + prefixStr + "</w:t></w:r>");
													}
												} catch (Exception e) {
													e.printStackTrace();
												}
											}
											//anStr = dealFormula(anStr);
											optsStr = optsStr + addBeforeLines(anStr, 0, 0, 0);
										}
										optsStr = compressOption(optsStr, optSize);
										IOUtils.write(optsStr, fos, "UTF-8");
									}
									IOUtils.write(qtTmpTailStr, fos, "UTF-8");
								}
							}


						}
					}

					IOUtils.write(tail, fos, "UTF-8");
				} finally {
					General.close(is);
					General.close(fosNew);
					General.close(fos);
				}
			}
			{
				DocxReader.updateDocx(newDocx, needToUpdate);
			}

		} catch (IOException e) {
			e.printStackTrace();
		} finally{
			if (templetDocxReader != null) {
				templetDocxReader.releaseTemporaries();
			}
		}
		return newDocx;
	}





	private static String dealImg(String html,File tmpFolder) throws IOException{
		html = dealImg(html, tmpFolder, "png");
		html = dealImg(html, tmpFolder, "jpg");
		html = dealImg(html, tmpFolder, "jpeg");
		html = dealImg(html, tmpFolder, "gif");
		return html;
	}

	private static String dealImg(String html,File tmpFolder,String imgType) throws IOException{
		String startFlag = "src=\"data:image/" + imgType + ";base64,";
		String imgTagFlag = "data:image/" + imgType + ";base64,";
		String endFlag = "\"";
		int index = html.indexOf(startFlag);
		while(index > 0){
			int endIndex = html.indexOf(endFlag,(index + startFlag.length()));
			String img64Str = html.substring((index + startFlag.length()), endIndex);

			File tmpFile = new File(tmpFolder,BaseUtil.generateId() + "." + imgType);
			FileUtils.writeByteArrayToFile(tmpFile, Base64.decodeBase64(img64Str));
			html = html.replace(imgTagFlag + img64Str, tmpFile.getAbsolutePath());

			index = html.indexOf(startFlag);
		}
		return html;
	}

	/**
	 * 简单判断一下，如果字符串里面没有下面几种标签，说明就是是纯文本片段，需要把<>&特殊符号处理一下
	 * @param
	 * @return
	 */
	private static String replaceHTMLChar(String text){
		String txtTmp = text.toLowerCase();
		if(!txtTmp.contains("</p>") && !txtTmp.contains("<img") && !txtTmp.contains("</i>") && !txtTmp.contains("</b>")&& !txtTmp.contains("</span>")
				&& !txtTmp.contains("</div>") && !txtTmp.contains("</ul>") && !txtTmp.contains("</li>")&& !txtTmp.contains("</table>")
				&& !txtTmp.contains("</pre>") && !txtTmp.contains("</a>") && !txtTmp.contains("<br>") && !txtTmp.contains("<br/>")){
			return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;");
		}else{
			return text;
		}
	}


	/**
	 * 给某段文字添加左侧多少个字符，前、后多少行距。
	 * @param xtStr
	 * @param leftChars leftChars/100=左侧字符数
	 * @param beforeLinesInt beforeLinesInt/100=段前行数
	 * @param afterLinesInt afterLinesInt/100=段后行数
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws org.dom4j.DocumentException
	 */
	@SuppressWarnings("rawtypes")
	private static String addBeforeLines(String xtStr,int leftChars,int beforeLinesInt,int afterLinesInt) throws UnsupportedEncodingException, org.dom4j.DocumentException {
		xtStr = PAPERTMPBODY_START + xtStr + PAPERTMPBODY_END;
		SAXReader sax = new SAXReader();
		ByteArrayInputStream is = new ByteArrayInputStream(xtStr.getBytes("UTF-8"));
		org.dom4j.Document document = sax.read(is);
		org.dom4j.Element root = document.getRootElement();
		{// 设置段前，0.5行
			org.dom4j.Element firstP = root.element("p");
			org.dom4j.Element pPr = firstP.element("pPr");
			if (pPr == null) {
				List children = firstP.elements();
				pPr = DocumentHelper.createElement("w:pPr");
				children.add(0, pPr);
				firstP.setContent(children);
			}
			{//处理spacing
				org.dom4j.Element spacing = pPr.element("spacing");
				if (spacing == null) {
					spacing = pPr.addElement("w:spacing");
				}
				Attribute before = spacing.attribute("before");
				if (before == null) {
					spacing.addAttribute("w:before", "0");
				} else {
					before.setValue("0");
				}
				Attribute beforeLines = spacing.attribute("beforeLines");
				if (beforeLines == null) {
					spacing.addAttribute("w:beforeLines", "" + beforeLinesInt);
				} else {
					beforeLines.setValue("" + beforeLinesInt);
				}
			}
			{//一定居左对齐
				org.dom4j.Element jc = pPr.element("jc");
				if(jc != null){
					pPr.remove(jc);
				}
				jc = pPr.addElement("w:jc");
				jc.addAttribute("w:val", "left");
			}
			{//处理左边距
				org.dom4j.Element ind = pPr.element("ind");
				if(ind != null){
					pPr.remove(ind);
				}
				ind = pPr.addElement("w:ind");
				ind.addAttribute("w:leftChars", leftChars + "");
			}
			{
				org.dom4j.Element firstWt = (org.dom4j.Element) firstP.selectSingleNode("*/w:t");
				if(firstWt != null){
					Attribute spaceAttr = firstWt.attribute("space");
					if(spaceAttr == null){
						firstWt.addAttribute("xml:space", "preserve");
					}else{
						spaceAttr.setValue("preserve");
					}
				}
			}
		}
		{// 设置段后，0.1行
			List pList = root.elements("p");
			org.dom4j.Element lastP = (org.dom4j.Element) pList.get(pList.size() - 1);
			org.dom4j.Element pPr = lastP.element("pPr");
			if (pPr == null) {
				List children = lastP.elements();
				pPr = DocumentHelper.createElement("w:pPr");
				children.add(0, pPr);
				lastP.setContent(children);
			}
			org.dom4j.Element adjustRightInd = pPr.element("adjustRightInd");
			if (adjustRightInd == null) {
				adjustRightInd = pPr.addElement("w:adjustRightInd");
			}
			{
				Attribute val = adjustRightInd.attribute("val");
				if (val == null) {
					adjustRightInd.addAttribute("w:val", "0");
				} else {
					val.setValue("0");
				}
			}
			org.dom4j.Element snapToGrid = pPr.element("snapToGrid");
			if (snapToGrid == null) {
				snapToGrid = pPr.addElement("w:snapToGrid");
			}
			{
				Attribute val = snapToGrid.attribute("val");
				if (val == null) {
					snapToGrid.addAttribute("w:val", "0");
				} else {
					val.setValue("0");
				}
			}
			org.dom4j.Element spacing = pPr.element("spacing");
			if (spacing == null) {
				spacing = pPr.addElement("w:spacing");
			}
			Attribute before = spacing.attribute("after");
			if (before == null) {
				spacing.addAttribute("w:after", "0");
			} else {
				before.setValue("0");
			}
			Attribute afterLines = spacing.attribute("afterLines");
			if (afterLines == null) {
				spacing.addAttribute("w:afterLines", "" + afterLinesInt);
			} else {
				afterLines.setValue("" + afterLinesInt);
			}
			Attribute line = spacing.attribute("line");
			if (line == null) {
				spacing.addAttribute("w:line", "280");
			} else {
				line.setValue("280");
			}
		}
		{
			List allWrNodes = (List) root.selectNodes("*/w:r");
			for(Object wrObj:allWrNodes){
				if(wrObj instanceof org.dom4j.Element){
					org.dom4j.Element wr = (org.dom4j.Element)wrObj;
					org.dom4j.Element rPr = wr.element("rPr");
					if (rPr == null) {
						List children = wr.elements();
						rPr = DocumentHelper.createElement("w:rPr");
						children.add(0, rPr);
						wr.setContent(children);
					}
					{
						org.dom4j.Element szCs  = rPr.element("szCs");
						if(szCs == null){
							List children = rPr.elements();
							szCs = DocumentHelper.createElement("w:szCs");
							children.add(szCs);
							rPr.setContent(children);
						}
						Attribute val = szCs.attribute("val");
						if (val == null) {
							szCs.addAttribute("w:val", "21");
						} else {
							val.setValue("21");
						}
					}
					{
						org.dom4j.Element szCs  = rPr.element("sz");
						if(szCs == null){
							List children = rPr.elements();
							szCs = DocumentHelper.createElement("w:sz");
							children.add(szCs);
							rPr.setContent(children);
						}
						Attribute val = szCs.attribute("val");
						if (val == null) {
							szCs.addAttribute("w:val", "21");
						} else {
							val.setValue("21");
						}
					}
				}
			}
		}
		xtStr = document.asXML();
		xtStr = xtStr.substring(xtStr.indexOf(PAPERTMPBODY_START) + PAPERTMPBODY_START.length(),
				xtStr.length() - PAPERTMPBODY_END.length());
		return xtStr;
	}


	/**
	 * 压缩试题选项
	 * @param xtStr 选项内容
	 * @param optSize 选项个数
	 * <AUTHOR>
	 **/
	@SuppressWarnings("unchecked")
	private static String compressOption(String xtStr,int optSize) throws UnsupportedEncodingException, org.dom4j.DocumentException {
		String xtStr_temp = xtStr;
		xtStr = PAPERTMPBODY_START + xtStr + PAPERTMPBODY_END;
		SAXReader sax = new SAXReader();
		ByteArrayInputStream is = new ByteArrayInputStream(xtStr.getBytes("UTF-8"));
		org.dom4j.Document document = sax.read(is);
		org.dom4j.Element root = document.getRootElement();
		List pList = root.elements("p");
		//获取所有选项元素

		org.dom4j.Element pA = (org.dom4j.Element)pList.get(0);
		org.dom4j.Element pPrA = pA.element("pPr");
		List pArList = pA.elements("r");
		org.dom4j.Element pAr = null;
		if(pArList.size() > 1){
			pAr = (org.dom4j.Element)pArList.get(1);
		}else{

			xtStr_temp = xtStr_temp.replaceAll("<w:pPr>", "<w:pPr><w:tabs>" + "<w:tab w:val=\"left\" w:pos=\"420\"/>"
					+"<w:tab w:val=\"left\" w:pos=\"8810\"/></w:tabs>");
			return xtStr_temp;
		}
		if(pAr.element("t")==null){
			return xtStr_temp;
		}
		int pA_len = pAr.element("t").getTextTrim().length();
		int pA_real_len = getQuestionBodyLength(pAr.element("t").getTextTrim());
		List pA_children = pA.elements();

		org.dom4j.Element pB = (org.dom4j.Element)pList.get(1);
		org.dom4j.Element pPrB = pA.element("pPr");
		List pBrList = pB.elements("r");
		org.dom4j.Element pBr = null;
		if(pBrList.size() > 1){
			pBr = (org.dom4j.Element)pBrList.get(1);
		}else{
			return xtStr_temp;
		}
		if(pBr.element("t")==null){
			return xtStr_temp;
		}
		int pB_len = pBr.element("t").getTextTrim().length();
		int pB_real_len = getQuestionBodyLength(pBr.element("t").getTextTrim());
		List pB_children = pB.elements();

		org.dom4j.Element pC = (org.dom4j.Element)pList.get(2);
		org.dom4j.Element pPrC = pA.element("pPr");
		List pCrList = pC.elements("r");
		org.dom4j.Element pCr = null;
		if(pCrList.size() > 1){
			pCr = (org.dom4j.Element)pCrList.get(1);
		}else{
			return xtStr_temp;
		}
		if(pCr.element("t")==null){
			return xtStr_temp;
		}
		int pC_len = pCr.element("t").getTextTrim().length();
		int pC_real_len = getQuestionBodyLength(pCr.element("t").getTextTrim());
		List pC_children = pC.elements();

		org.dom4j.Element pD = (org.dom4j.Element)pList.get(3);
		org.dom4j.Element pPrD = pA.element("pPr");
		List pDrList = pD.elements("r");
		org.dom4j.Element pDr = null;
		if(pDrList.size() > 1){
			pDr = (org.dom4j.Element)pDrList.get(1);
		}else{
			return xtStr_temp;
		}
		if(pDr.element("t")==null){
			return xtStr_temp;
		}
		int pD_len = pDr.element("t").getTextTrim().length();
		int pD_real_len = getQuestionBodyLength(pDr.element("t").getTextTrim());
		List pD_children = pD.elements();

		int pE_real_len = 0;
		int pE_len = 0;

		org.dom4j.Element rtab = DocumentHelper.createElement("w:r");
		org.dom4j.Element rPr = DocumentHelper.createElement("w:rPr");
		org.dom4j.Element szCs = DocumentHelper.createElement("w:szCs");
		org.dom4j.Element sz = DocumentHelper.createElement("w:sz");
		{
			Attribute val = szCs.attribute("val");
			if (val == null) {
				szCs.addAttribute("w:val", "21");
			} else {
				val.setValue("21");
			}
		}
		{
			Attribute val = sz.attribute("val");
			if (val == null) {
				sz.addAttribute("w:val", "21");
			} else {
				val.setValue("21");
			}
		}
		rPr.add(szCs);
		rPr.add(sz);
		rtab.add(rPr);
		rtab.addElement("w:tab","http://schemas.openxmlformats.org/wordprocessingml/2006/main");

		//处理单选题
		if(optSize == 4){
			//一行4项
        	if((pA_real_len + pB_real_len + pC_real_len + pD_real_len) <= 25 && pA_real_len <= 6 && pB_real_len <= 6 && pC_real_len <= 6 && pD_real_len <= 6){
        		//pA_children.add(1,(Element)rtab.clone());
        		pB_children.add(1,(org.dom4j.Element)rtab.clone());
        		pC_children.add(1,(org.dom4j.Element)rtab.clone());
        		pD_children.add(1,(org.dom4j.Element)rtab.clone());
        		detachPr(pB_children);
        		detachPr(pC_children);
        		detachPr(pD_children);
        		detachAll(pA_children);
        		detachAll(pB_children);
        		detachAll(pC_children);
        		detachAll(pD_children);
        		pA_children.addAll(pB_children);
        		pA_children.addAll(pC_children);
        		pA_children.addAll(pD_children);
        		pA.setContent(pA_children);
        		pB.detach();
        		pC.detach();
        		pD.detach();
        	}
			//一行2项
			else if((pA_real_len + pB_real_len) <= 60 && (pC_real_len + pD_real_len) <= 60 && pA_real_len <= 30 && pB_real_len <= 30 && pC_real_len <= 30 && pD_real_len <= 30){
				//pA_children.add(1,(Element)rtab.clone());
				pB_children.add(1,(org.dom4j.Element)rtab.clone());
				//pC_children.add(1,(Element)rtab.clone());
				pD_children.add(1,(org.dom4j.Element)rtab.clone());
				detachPr(pB_children);
				detachPr(pD_children);
				detachAll(pA_children);
				detachAll(pB_children);
				detachAll(pC_children);
				detachAll(pD_children);
				pA_children.addAll(pB_children);
				pC_children.addAll(pD_children);
				pA.setContent(pA_children);
				pC.setContent(pC_children);
				pB.detach();
				pD.detach();
			}
			//处理多选题
		}else if(optSize == 5){
			org.dom4j.Element pE = (org.dom4j.Element)pList.get(4);
			org.dom4j.Element pPrE = pE.element("pPr");
			List pErList = pE.elements("r");
			org.dom4j.Element pEr = null;
			if(pErList.size() > 1){
				pEr = (org.dom4j.Element)pErList.get(1);
			}else{
				return xtStr_temp;
			}
			if(pEr.element("t") == null){
				return xtStr_temp;
			}
			pE_len = pEr.element("t").getTextTrim().length();
			pE_real_len = getQuestionBodyLength(pEr.element("t").getTextTrim());
			List pE_children = pE.elements();
			//一行2项
			if((pA_real_len + pB_real_len ) <= 60 && (pC_real_len + pD_real_len) <= 60 && pA_real_len <= 30 && pB_real_len <= 30 && pC_real_len <= 30 && pD_real_len <= 30 && pE_real_len <= 30){
				pB_children.add(1,(org.dom4j.Element)rtab.clone());
				pD_children.add(1,(org.dom4j.Element)rtab.clone());
				detachPr(pB_children);
				detachPr(pD_children);
				detachAll(pA_children);
				detachAll(pB_children);
				detachAll(pC_children);
				detachAll(pD_children);
				//detachAll(pE_children);
				pA_children.addAll(pB_children);
				pC_children.addAll(pD_children);
				pA.setContent(pA_children);
				pC.setContent(pC_children);
				pB.detach();
				pD.detach();
			}
		}

		xtStr = document.asXML();
		xtStr = xtStr.substring(xtStr.indexOf(PAPERTMPBODY_START) + PAPERTMPBODY_START.length(),
				xtStr.length() - PAPERTMPBODY_END.length());
		//增加tabs定义
		if(optSize == 4){
			//一行4项
        	if((pA_real_len + pB_real_len + pC_real_len + pD_real_len) <= 25 && pA_real_len <= 6 && pB_real_len <= 6 && pC_real_len <= 6 && pD_real_len <= 6){
				xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>"+ "<w:tab w:val=\"left\" w:pos=\"420\"/>"
						+ "<w:tab w:val=\"left\" w:pos=\"2210\"/>"
						//+ "<w:tab w:val=\"left\" w:pos=\"4410\"/>"+ "<w:tab w:val=\"left\" w:pos=\"6510\"/>"
						+"</w:tabs>");
        	}
			//一行2项
			else if((pA_real_len + pB_real_len) <= 60 && (pC_real_len + pD_real_len) <= 60 && pA_real_len <= 30 && pB_real_len <= 30 && pC_real_len <= 30 && pD_real_len <= 30){
				xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>" + "<w:tab w:val=\"left\" w:pos=\"420\"/>"
						+"<w:tab w:val=\"left\" w:pos=\"4410\"/></w:tabs>");
			} else{
				xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>" + "<w:tab w:val=\"left\" w:pos=\"420\"/>"
						+"<w:tab w:val=\"left\" w:pos=\"8810\"/></w:tabs>");
			}
		}else if(optSize == 5){
			//一行2项
			if((pA_real_len + pB_real_len) <= 60 && (pC_real_len + pD_real_len) <= 60 && pA_real_len <= 30 && pB_real_len <= 30 && pC_real_len <= 30 && pD_real_len <= 30 && pE_real_len <= 30){
				xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>"+ "<w:tab w:val=\"left\" w:pos=\"420\"/>"+"<w:tab w:val=\"left\" w:pos=\"4410\"/>" + "</w:tabs>");
			}else if((pA_real_len + pB_real_len + pC_real_len + pD_real_len) <= 25 && pA_real_len <= 6 && pB_real_len <= 6 && pC_real_len <= 6 && pD_real_len <= 6){
					xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>"+ "<w:tab w:val=\"left\" w:pos=\"420\"/>"
							+ "<w:tab w:val=\"left\" w:pos=\"2210\"/>"
							//+ "<w:tab w:val=\"left\" w:pos=\"4410\"/>"+ "<w:tab w:val=\"left\" w:pos=\"6510\"/>"
							+"</w:tabs>");
			}else{
				xtStr = xtStr.replaceAll("<w:pPr>", "<w:pPr><w:tabs>" + "<w:tab w:val=\"left\" w:pos=\"420\"/>"
						+"<w:tab w:val=\"left\" w:pos=\"8810\"/></w:tabs>");
			}
		}
		return xtStr;
	}

	/**
	 * <p>获取题干实际长度</p>
	 * <AUTHOR>
	 * @throws UnsupportedEncodingException
	 * @date 2016－07-11
	 */
	public static int getQuestionBodyLength(String str) throws UnsupportedEncodingException{
		double real_len = 0;
		String []str_split = str.split("");
		for(String s:str_split){
			double len = s.getBytes("UTF-8").length;
			if(len == 1){
				real_len += len / 2;
			}else if(len >= 2){
				real_len += 1;
			}
		}
		return (int)Math.ceil(real_len);
	}

	public static void detachAll(List<org.dom4j.Element> eList){
		for(org.dom4j.Element e:eList){
			e.detach();
		}
	}

	public static void detachPr(List<org.dom4j.Element> eList){
		Iterator<org.dom4j.Element> iterator = eList.iterator();
		while(iterator.hasNext()){
			org.dom4j.Element e = iterator.next();
			if(e.getName().equals("pPr")){
				iterator.remove();
			}
		}
	}

	//获取题干或者答案里的内容，包含img标签和文字
	private static String getTextAndImg(String content) {
		StringBuilder result = new StringBuilder();
		org.jsoup.nodes.Document document = Jsoup.parse(content);
		Elements elements = document.body().children();
		if (elements.isEmpty()) {
			return content;
		}
		for (org.jsoup.nodes.Element element : elements) {
			if ("p".equals(element.tagName())) {
				// 提取段落中的文本
				String text = element.text();
				if (!text.isEmpty()) {
					result.append(text);
				}
				result.append(element.select("img"));
			}
		}
		return result.toString();
	}

	public static void main(String[] args) {
		String xtStr = "计算图1所示T梁翼板所构成铰接悬臂板的设计内力。桥梁荷载为公路一I&nbsp;级，桥面铺装为80mm厚C50混凝土配φ8&nbsp;@&nbsp;100钢筋网；容重为25kN/m³；下设40mm厚素混凝土找平层；容重为23&nbsp;kN/m³，T梁翼板材料容重为25kN/m³";
		System.out.println(getTextAndImg(xtStr));
	}

}
