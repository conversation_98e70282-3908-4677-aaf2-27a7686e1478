package com.xunw.jxjy.model.personal.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.xunw.jxjy.model.zypx.mapper.ZypxBmMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.dto.StudentFormDataDto;
import com.xunw.jxjy.model.enums.Education;
import com.xunw.jxjy.model.enums.FormShowType;
import com.xunw.jxjy.model.enums.Gender;
import com.xunw.jxjy.model.enums.Nation;
import com.xunw.jxjy.model.enums.RequiredType;
import com.xunw.jxjy.model.enums.StudentType;
import com.xunw.jxjy.model.inf.entity.FormData;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;

/**
 * PC端的报名服务
 */
@Service
public class ZypxStudentBmService extends ZypxStudentBmBaseService  {

	private static final String ZERO = "0";
	private static final String VALUE_TEXT_SPLITTER = "##";

	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private StudentUserService studentUserService;
	@Autowired
	private ZypxBmMapper zypxBmMapper;
	
	/**
	 * 学员自定义表单提交 PC端
	 */
	@Transactional
	public void saveAnswerForm(HttpServletRequest request, String studentId) throws Exception {
		String xmId = request.getParameter("xmId");
		String formId = request.getParameter("formId");
		StudentUser studentUser = studentUserService.selectById(studentId);
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentId);
		//获取表单填写值
		Map<String, String[]> parameterMap = request.getParameterMap();
		List<Map<String, Object>> fieldList = xmMapper.getFormField(xmId, formId);
		List<FormData> formDatas = new ArrayList<FormData>();
		//获取表单上的学员类型控件的值，该控件可能在表单中不存在
		List<Map<String, Object>> studentTypeList = fieldList.stream().filter(x->Constants.YES.equals(BaseUtil.getStringValueFromMap(x,"isConstant")) &&
				Constants.SystemField.STUDENT_TYPE.equals(BaseUtil.getStringValueFromMap(x,"submitName"))).collect(Collectors.toList());
		String idForStudentType = studentTypeList.size() > 0 ? studentTypeList.get(0).get("id").toString() : null;
		String studentType = idForStudentType !=null ? request.getParameter(idForStudentType) : null;
		for (Map<String, Object> map : fieldList){
			String parameterName = BaseUtil.getStringValueFromMap(map, "id");
			String submitName = BaseUtil.getStringValueFromMap(map, "submitName");
			String isConstant = BaseUtil.getStringValueFromMap(map, "isConstant");
			String fieldName = BaseUtil.getStringValueFromMap(map, "name");
			String requiredType = BaseUtil.getStringValueFromMap(map, "requiredType");
			String type = BaseUtil.getStringValueFromMap(map, "type");
			FormShowType showType = StringUtils.isNotEmpty(type) ? FormShowType.valueOf(type) : null;
			
			String inputValue = request.getParameter(parameterName);
			
			RequiredType fieldRequiredType = RequiredType.findByEnumName(requiredType);
			
			//判断字段是否是必填项
			if (parameterMap.get(parameterName) == null || StringUtils.isEmpty(request.getParameter(parameterName)) ||
					parameterMap.get(parameterName).length == 0) {
				if (RequiredType.ALL == fieldRequiredType) {//全部类型的学员必填
					throw BizException.withMessage("缺失必填项:"+fieldName);
				}
				else if (RequiredType.SCHOOL == fieldRequiredType){//在校学员必填
					if (StringUtils.isNotEmpty(studentType)) {
						studentType = StringUtils.splitByWholeSeparator(studentType, VALUE_TEXT_SPLITTER)[0];
					}
					else {
						throw BizException.withMessage("未选择学员类型");
					}
					if (RequiredType.SCHOOL.name().equals(studentType)) {
						throw BizException.withMessage("在校生必需填写:"+fieldName);
					}
				}
				else if (RequiredType.SOCIAL == fieldRequiredType){//社会学员必填
					if (StringUtils.isNotEmpty(studentType)) {
						studentType = StringUtils.splitByWholeSeparator(studentType, VALUE_TEXT_SPLITTER)[0];
					}
					else {
						throw BizException.withMessage("未选择学员类型");
					}
					if (RequiredType.SOCIAL.name().equals(studentType)) {
						throw BizException.withMessage("在职人员必需填写:"+fieldName);
					}
				}
			}
			
			FormData formData = new FormData();
			formData.setId(BaseUtil.generateId());
			formData.setStudentId(studentId);
			formData.setXmId(xmId);
			formData.setFormId(BaseUtil.getStringValueFromMap(map, "formId"));
			formData.setFieldId(parameterName);
			
			switch (showType) {
				case TXT:
					formData.setFieldValue(inputValue);
					break;
				case NUMBER:
					formData.setFieldValue(inputValue);
					break;
				case SINGLE_CHOICE:
					if (StringUtils.isNotEmpty(inputValue)) {
						String[] values = StringUtils.splitByWholeSeparator(inputValue, VALUE_TEXT_SPLITTER);
						if (values.length == 2) {
							formData.setFieldValue(values[0]);
							formData.setFieldText(values[1]);
						}
						else {
							throw BizException.withMessage("传入表单数据异常-【单选】");
						}
					}
					break;
				case MULTI_CHOICE:
					String[] values = parameterMap.get(parameterName);
					List<String> answerList = new ArrayList<String>();
					List<String> textList = new ArrayList<String>();
					if (values != null && values.length > 0) {
						for (String value : values) {
							String[] valuesAttr = StringUtils.splitByWholeSeparator(value, VALUE_TEXT_SPLITTER);
							if (valuesAttr.length == 2) {
								answerList.add(valuesAttr[0]);
								textList.add(valuesAttr[1]);
							}
							else {
								throw BizException.withMessage("传入表单数据异常-【多选】");
							}
						}
					}
					formData.setFieldValue(StringUtils.join(answerList, ","));
					formData.setFieldText(StringUtils.join(textList, ","));
					break;
				case SELECT:
					if (StringUtils.isNotEmpty(inputValue)) {
						String[] selectedValue = StringUtils.splitByWholeSeparator(inputValue, VALUE_TEXT_SPLITTER);
						if (selectedValue.length == 2) {
							formData.setFieldValue(selectedValue[0]);
							formData.setFieldText(selectedValue[1]);
						}
						else {
							throw BizException.withMessage("传入表单数据异常-【下拉选择】");
						}
					}
					break;
				case DATE:
					formData.setFieldValue(inputValue);
					break;
				case DATETIME:
					formData.setFieldValue(inputValue);
					break;
				case IMAGE_UPLOAD:
					if (StringUtils.isNotEmpty(inputValue) && inputValue.indexOf(";base64,") > -1) {//bae64转图片
						inputValue= FileHelper.convertImgBase64SrcStrToUrl(inputValue);
					}
					formData.setFieldValue(inputValue);
					break;
				case DOC_UPLOAD:
					formData.setFieldValue(inputValue);
					break;
				case TEXTAREA:
					formData.setFieldValue(inputValue);
					break;
				default:
					break;
			}
			//个人信息处理
			if (Constants.YES.equals(isConstant)) {
				switch (submitName) {
					case Constants.SystemField.STUDENT_TYPE:
						studentUser.setStudentType(StudentType.findByEnumName(formData.getFieldValue()));
						break;
					case Constants.SystemField.NAME:
						studentInfo.setName(formData.getFieldValue());
						break;
					case Constants.SystemField.GENDER:
						studentInfo.setGender(Gender.findByEnumName(formData.getFieldValue()));
						break;
					case Constants.SystemField.NATION:
						studentInfo.setNation(Nation.findByEnumName(formData.getFieldValue()));
						break;
					case Constants.SystemField.SFZH:
						studentInfo.setSfzh(formData.getFieldValue());
						if (studentInfo.getSfzh()!=null && !BaseUtil.isIDCardNumber(studentInfo.getSfzh())) {
							throw BizException.withMessage("请输入正确的身份证号");
						}
						break;
					case Constants.SystemField.EDUCATION:
						studentInfo.setEducation(Education.findByEnumName(formData.getFieldValue()));
						break;
					case Constants.SystemField.ADDRESS:
						studentInfo.setAddress(formData.getFieldValue());
						break;
					case Constants.SystemField.POST_CODE:
						studentInfo.setPostCode(formData.getFieldValue());
						break;
					case Constants.SystemField.MOBILE:
						studentInfo.setMobile(formData.getFieldValue());
						if (studentInfo.getMobile()!=null && !BaseUtil.isMobile(studentInfo.getMobile())) {
							throw BizException.withMessage("请输入正确的11位手机号");
						}
						break;
					case Constants.SystemField.SFZZM:
						studentInfo.setSfzzm(formData.getFieldValue());
						break;
					case Constants.SystemField.SFZFM:
						studentInfo.setSfzfm(formData.getFieldValue());
						break;
					case Constants.SystemField.STUDENT_PHOTO:
						studentInfo.setStudentPhoto(formData.getFieldValue());
						break;
					case Constants.SystemField.EDU_CERTI_PHOTO:
						studentInfo.setEduCertiPhoto(formData.getFieldValue());
						break;
					case Constants.SystemField.COMPANY:
						studentUser.setCompany(formData.getFieldValue());
						break;
					case Constants.SystemField.GRADUATE_SCHOOL:
						studentInfo.setGraduateSchool(formData.getFieldValue());
						break;
					case Constants.SystemField.COLLEGE:
						studentInfo.setCollege(formData.getFieldValue());
						break;
					case Constants.SystemField.SPECIALTY:
						studentInfo.setSpecialty(formData.getFieldValue());
						break;
					case Constants.SystemField.CLASSZ:
						studentInfo.setClassz(formData.getFieldValue());
						break;
					case Constants.SystemField.STUDENT_TICKET:
						studentInfo.setStudentTicket(formData.getFieldValue());
						break;
					case Constants.SystemField.STUDENT_NUMBER:
						studentInfo.setStudentNum(formData.getFieldValue());
						break;
					default:
						break;
				}
			}
			formDatas.add(formData);
		}
		StudentFormDataDto studentFormDataDto = new StudentFormDataDto();
		studentFormDataDto.setXmId(xmId);
		studentFormDataDto.setStudentId(studentId);
		studentFormDataDto.setFormId(formId);
		studentFormDataDto.setFormDatas(formDatas);
		studentFormDataDto.setStudentInfo(studentInfo);
		studentFormDataDto.setStudentUser(studentUser);
		String key = "FORMDATA_"+xmId+formId+studentId;
		request.getSession().setAttribute(key, studentFormDataDto);
	}

	public boolean existsBmByStudentIdAndXmId(String studentId, String id) {
		return zypxBmMapper.existsBmByStudentIdAndXmId(studentId, id);
	}
}
