package com.xunw.jxjy.model.zyjd.params;

import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.ZyjdBmBatchType;

import java.util.List;

/**
 * 答辩评审成绩查询参数
 */
public class ZcpsScoreParams extends BaseQueryParams {

	private static final long serialVersionUID = -820409814335865361L;

	//批次id
	private String bmbatchId;

	//工种类型
	private String industryId;

	//工种id
	private String professionId;

	//等级
	private TechLevel techLevel;

	//是否通过 1是 0否
	private String passed;

	//学员关键字 身份证号，姓名
	private String keyword;

	private String hostOrgId;

	private String loginUserId;

	private String groupId;//分组id

	private String groupType;//分组类型

	private List<String> professionIds;

	private ZyjdBmBatchType type = ZyjdBmBatchType.ZCPS;

	public String getBmbatchId() {
		return bmbatchId;
	}

	public void setBmbatchId(String bmbatchId) {
		this.bmbatchId = bmbatchId;
	}

	public String getCategory() {
		return industryId;
	}

	public void setCategory(String industryId) {
		this.industryId = industryId;
	}

	public String getProfessionId() {
		return professionId;
	}

	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}

	public TechLevel getTechLevel() {
		return techLevel;
	}

	public void setTechLevel(TechLevel techLevel) {
		this.techLevel = techLevel;
	}

	public String getPassed() {
		return passed;
	}

	public void setPassed(String passed) {
		this.passed = passed;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public ZyjdBmBatchType getType() {
		return type;
	}

	public void setType(ZyjdBmBatchType type) {
		this.type = type;
	}

	public String getIndustryId() {
		return industryId;
	}

	public void setIndustryId(String industryId) {
		this.industryId = industryId;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getLoginUserId() {
		return loginUserId;
	}

	public void setLoginUserId(String loginUserId) {
		this.loginUserId = loginUserId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	public List<String> getProfessionIds() {
		return professionIds;
	}

	public void setProfessionIds(List<String> professionIds) {
		this.professionIds = professionIds;
	}
}
