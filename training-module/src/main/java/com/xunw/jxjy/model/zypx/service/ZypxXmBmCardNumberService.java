package com.xunw.jxjy.model.zypx.service;

import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmBmCardNumberMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ZypxXmBmCardNumberService extends BaseCRUDService<ZypxXmBmCardNumberMapper, ZypxXmBmCardNumber>{

	public List<ZypxXmBmCardNumber> findByBmIdAndCardNumber(String bmId, String cardNumber) {
		return mapper.findByBmIdAndCardNumber(bmId, cardNumber);
	}

	public List<ZypxXmBmCardNumber> findByBmId(String bmId) {
		return mapper.findByBmId(bmId);
	}
}
