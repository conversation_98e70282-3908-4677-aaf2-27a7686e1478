package com.xunw.jxjy.model.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训基地
 */
@TableName("inf_base")
public class Base implements Serializable {

    private static final long serialVersionUID = -354116107759164784L;

    //主键id
    @TableId(type = IdType.INPUT)
    @TableField("id")
    private String id;

    //基地名称
    @TableField("name")
    private String name;

    //主管单位
    @TableField("org")
    private String org;

    //基地类型
    @TableField("type")
    private String type;

    //获批时间 年
    @TableField("approval_time")
    private String approvalTime;

    //创建用户id
    @TableField("creator_id")
    private String creatorId;

    //创建时间
    @TableField("create_time")
    private Date createTime;

    //修改用户id
    @TableField("updator_id")
    private String updatorId;

    //修改时间
    @TableField("update_time")
    private Date updateTime;

    //主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

	public void check() {
		if (BaseUtil.isEmpty(this.name)) {
			throw BizException.withMessage("请输入基地名称");
		}
		if (BaseUtil.isEmpty(this.org)) {
			throw BizException.withMessage("请输入主管单位");
		}
		if (BaseUtil.isEmpty(this.type)) {
			throw BizException.withMessage("请选择基地类型");
		}
		if (BaseUtil.isEmpty(this.approvalTime)) {
			throw BizException.withMessage("请选择获批时间");
		}
	}
}
