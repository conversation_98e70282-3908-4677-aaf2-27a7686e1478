package com.xunw.jxjy.model.zypx.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.zypx.entity.TargetResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 评分指标设置
 */
public interface TargetResultMapper extends BaseMapper<TargetResult> {

    /**
     * 根据项目id 或者 项目指标设置表ID 得到平均分数
     */
    String getAvgTargetResult(@Param("xmId") String xmId, @Param("settingId") String settingId);

    /**
     * 获取项目综合排行榜
     */
    List<Map<String, Object>> getXmRank(@Param("hostOrgId") String hostOrgId);

    /**
     * 获取课件排行榜
     */
    List<Map<String, Object>> getKjRank(@Param("hostOrgId") String hostOrgId);

    /**
     * 获取老师排行榜
     */
    List<Map<String, Object>> getTeacherRank(@Param("hostOrgId") String hostOrgId);

    List<Map<String, Object>> getTargetByXmId(@Param("xmId") String xmId,@Param("studentId")String studentId);

    List<Map<String, Object>> getCourseTargetResult(@Param("xmId") String xmId, @Param("courseId") String courseId, @Param("keyword") String keyword, Page page);

    List<Map<String, Object>> getCourseTargetResultByStudent(@Param("xmId") String xmId, @Param("courseId") String courseId, @Param("studentId") String studentId);

    List<Map<String, Object>> exportCourseTargetResultByStudent(@Param("xmId") String xmId, @Param("courseId") String courseId);

    Integer checkReDo(@Param("id") String id, @Param("xmId") String xmId, @Param("courseSettingId") String courseSettingId, @Param("studentId") String studentId);
}
