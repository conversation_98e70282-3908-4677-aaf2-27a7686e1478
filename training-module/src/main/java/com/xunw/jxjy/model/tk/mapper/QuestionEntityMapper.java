package com.xunw.jxjy.model.tk.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;

public interface QuestionEntityMapper extends BaseMapper<QuestionEntity>{
	
	/**
	 * 试题分页查询
	 */
	public List<Map<String,Object>> list(Map<String,Object> condition, Page<?> page);
	
	/**
	 * 超管试题分页查询
	 */
	public List<Map<String,Object>> listByAdmin(Map<String,Object> condition, Page<?> page);
	
	/**
	 * 获取题型
	 */
	public List<Map<String, Object>> getAllRealType();

	/**
	 * 获取试题详情
	 */
	public Map<String, Object> getQuesDetailsById(String id);


	/**
	 * 从指定的题库中随机抽题
	 */
	public List<Map<String, Object>> getQuestionsByRandom(@Param("dbId") String dbId, @Param("type")String type, @Param("difficulty")String difficulty, @Param("sum")int sum);

}
