package com.xunw.jxjy.model.zyjd.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.xunw.jxjy.model.zyjd.params.ZcpsScoreParams;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmQueryParams;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.inf.entity.ZyjdIndustry;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;

public interface ZyjdBmMapper extends BaseMapper<ZyjdBm> {

	/**
	 * 列表查询
	 */
    public List<Map<String,Object>> list(Map<String,Object> condition, Page<?> page);

	/**
	 * 报名统计查询
	 */
    List<Map<String,Object>> analysis(Map<String, Object> condition,  Page<?> page);

    /**
	 * 获取报名详情
	 */
    public Map<String, Object> getDetailsById(@Param("id") String id);

    /**
     * 获取准考证
     */
    public Map<String, Object> getZkzById(@Param("id") String id);

    /**
     * 获取批次下开放报名的职业
     */
    public List<ZyjdProfession> getOpenedProfession(@Param("bmbatchId") String bmbatchId, @Param("industryId") String industryId);

    /**
     * 获取批次下开放报名的职业
     */
    public List<Map<String, Object>> getOpenedProfessionIn(@Param("bmbatchId") String bmbatchId, @Param("industryIds") List<String> industryIds);

    /**
     * 获取批次下各个工种、等级的报名人数
     */
    List<Map<String, Object>> getProfessionBmCountList(@Param("bmbatchId") String bmbatchId);

    /**
     * 获取批次下
     * @return
     */
    List<ZyjdIndustry> getIndustryByBmbatchId(@Param("bmbatchId") String bmbatchId);

    /**
     * 获取批次的报名数据统计详情
     */
    public List<Map<String,Object>> getAnalysisInfo(@Param("bmbatchId") String bmbatchId, Page<?> page);

    /**
	 * 职业 报名统计
	 */
	List<Map<String, Object>> statistcCount(Map<String, Object> condition, Page<?> page);

    Double sumAmount(Map<String,Object> condition);

    /**
     * 修改
     */
    boolean updateRec(Map<String,Object> condition);

    /**
     * 修改
     */
    boolean updateRecByIds(Map<String,Object> condition);

    /**
     * 修改
     */
    boolean updateRecByCode(Map<String,Object> condition);

    /**
     * 标记入账或取消入账
     */
    boolean signRec(Map<String,Object> condition);

    /**
     * 当前年度报名数据
     */
    List<ZypxBm> bmListCurYear(@Param("hostOrgId") String hostOrgId, @Param("year") String year);

    List<Map<String, Double>> xmIncomeStatistical(@Param("hostOrgId") String hostOrgId, @Param("yearList") Set<Integer> yearList);

    List<Map<String, Object>> zcpsScorePage(Map<String, Object> condition, Page page);

    List<ZyjdBm> getBmBySfzh(@Param("bmbatchId") String bmbatchId, @Param("sfzh") String sfzh);

    List<ZyjdBm> getDetailsByStudentId(@Param("studentId") String studentId);

    List<Map<String, Object>> psList(Map<String, Object> condition, Page page);

    List<Map<String, Object>> zyList(Map<String, Object> condition, Page page);

    List<Map<String, Object>> studentPage(Map<String,Object> condition, Page page);

    List<Map<String, Object>> scoreDetail(Map<String,Object> condition, Page page);
    
    List<ZyjdBm> getDetailsByStudentIdAndType(@Param("studentId") String studentId, @Param("type") String type);
    
}