package com.xunw.jxjy.model.personal.service;

import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.file.Files;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSON;

import com.xunw.jxjy.common.utils.*;
import com.xunw.jxjy.model.zypx.params.ZypxStudentCertiMgrParams;
import net.lingala.zip4j.util.Zip4jUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.google.zxing.WriterException;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.AnswerStatus;
import com.xunw.jxjy.model.enums.QaStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.zypx.dto.qa.QaPaper;
import com.xunw.jxjy.model.zypx.dto.qa.QaQuestion;
import com.xunw.jxjy.model.zypx.entity.ZypxCertiTpl;
import com.xunw.jxjy.model.zypx.entity.ZypxQa;
import com.xunw.jxjy.model.zypx.entity.ZypxQaAnswer;
import com.xunw.jxjy.model.zypx.entity.ZypxQaAnswerDetail;
import com.xunw.jxjy.model.zypx.entity.ZypxStudentCerti;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.entity.ZypxXm2Qa;
import com.xunw.jxjy.model.zypx.mapper.ZypxCertiTplMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxQaAnswerDetailMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxQaAnswerMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxQaMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxScoreMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxStudentCertiMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXm2QaMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;
import com.xunw.jxjy.paper.utils.ModelHelper;

/**
 * @Description 学员结业证书
 * <AUTHOR>
 * @Time 2021/5/18 15:15
 * @Version 1.0
 */
@Service
public class ZypxStudentCertiService extends BaseCRUDService<ZypxStudentCertiMapper, ZypxStudentCerti>{
	
	private static  final Logger LOGGER = LoggerFactory.getLogger(ZypxStudentCertiService.class);

    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private ZypxXmMapper xmMapper;
    @Autowired
    private ZypxCertiTplMapper certiTplMapper;
    @Autowired
    private ZypxQaMapper qaMapper;
    @Autowired
    private ZypxQaAnswerDetailMapper qaAnswerDetailMapper;
    @Autowired
    private ZypxQaAnswerMapper qaAnswerMapper;
    @Autowired
    private AttConfig attConfig;
    @Autowired
    private ZypxScoreMapper zypxScoreMapper;
    @Autowired
    private ZypxStudentCertiMapper zypxStudentCertiMapper;

    /**
     * 查询考生所有证书
     */
    public List<Map<String, Object>> getCertiByStudent(String studentId, String portalWebUrl) {
        Map<String, Object> params = new HashMap<>();
        params.put("studentId", studentId);
        List<Map<String, Object>> studentCerti = mapper.getCertiByStudent(params);
        List<Map<String, Object>> result = new ArrayList<Map<String,Object>>();
        for (Map<String, Object> map : studentCerti) {
        	String certiUrl = BaseUtil.getStringValueFromMap(map, "certiUrl");
        	String certiImgUrl = BaseUtil.getStringValueFromMap(map, "certiImgUrl");
        	if (StringUtils.isEmpty(certiUrl) || StringUtils.isEmpty(certiImgUrl)) {
				String xmId = BaseUtil.getStringValueFromMap(map, "xmId");
				try {
					//判断证书是否已经生成，若没有，则生成证书
					ZypxStudentCerti zypxStudentCerti = buildStudentCerti(xmId, studentId, portalWebUrl);
					if (StringUtils.isNotEmpty(zypxStudentCerti.getCertiUrl()) && 
							StringUtils.isNotEmpty(zypxStudentCerti.getCertiImgUrl())) {
						map.put("certi_url", zypxStudentCerti.getCertiUrl());
						map.put("certi_img_url", zypxStudentCerti.getCertiImgUrl());
						map.put("certi_time", zypxStudentCerti.getCertiTime());
						result.add(map);
					}
				} catch (Exception e) {
					LOGGER.error("结业证书生成错误:", e);
				}
			}
        	else {
        		result.add(map);
        	}
        }
        return result;
    }

    /**
     * 下载结业证书时 验证是否做过调查问卷
     */
    public Map<String, Object> checkQaWhenDowloadCerti(String certiId, String studentId){
        Map<String, Object> result = new HashMap<>();
        ZypxStudentCerti certi = mapper.selectById(certiId);
        result.put("url", certi.getCertiUrl());
        result.put("exist", true);
        result.put("xmId", certi.getXmId());
        //判断培训项目是否设置调查问卷
        Map<String, Object> qaMap = qaMapper.getQaByXmId(certi.getXmId());
        if (qaMap == null) {//未设置调查问卷
        	 return result;
		}
        else {
        	//判断是否已经作答
        	ZypxQaAnswer answer = qaAnswerMapper.getStudentQaAnswer(certi.getXmId(),
        		 BaseUtil.getStringValueFromMap(qaMap, "qaId"), studentId);
             result.put("xmId", certi.getXmId());
             result.put("certiId", certiId);
             if (answer == null) {
                 result.put("exist", false);
                 result.put("isMust", BaseUtil.getStringValueFromMap(qaMap, "isMust"));
                 return result;
             }
        }
        return result;
    }
    
    /**
     * 验证是否作答调查问卷, true  已经作答  false 未作答
     */
    public boolean isQaAnswered(String xmId, String qaId, String studentId){
    	ZypxQaAnswer answer = qaAnswerMapper.getStudentQaAnswer(xmId, qaId, studentId);
        return answer != null;
    }

    /**
     * 根据项目id 获取调查问卷
     */
    public Map<String, Object> toSurveyPaper(String xmId) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("xmId", xmId);
        Map<String, Object> qa = qaMapper.getQaByXmId(xmId);
        if (qa != null) {
            QaPaper qaContent = ModelHelper.convertObject(qa.get("content").toString());
            ZypxQa zypxQa = qaMapper.selectById(qa.get("id").toString());
            if (zypxQa.getCount() == null) {
                zypxQa.setCount(1);
            } else {
                zypxQa.setCount(zypxQa.getCount() + 1);
            }
            qaMapper.updateById(zypxQa);

            result.put("paper", qaContent);
            result.put("paperMap", qa);
        } else {
            result.put("code", -1);
            result.put("msg", "调查问卷不存在");
            return result;
        }
        return result;
    }

    /**
     * 根据项目id 获取调查问卷
     */
    public Map<String, Object> toSurveyPaper(String xmId, String qaId) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("xmId", xmId);
        result.put("qaId", qaId);
        Map<String, Object> qa = new HashMap<>();
        if (BaseUtil.isNotEmpty(xmId)) {
            qa = qaMapper.getQaByXmId(xmId);
        } else if (BaseUtil.isNotEmpty(qaId)) {
            List<Map<String, Object>> list = qaMapper.getSentDownQaById(qaId);
            qa = list.isEmpty() ? null : list.get(0);
        }
        if (qa != null) {
            QaPaper qaContent = ModelHelper.convertObject(qa.get("content").toString());
            ZypxQa zypxQa = qaMapper.selectById(qa.get("id").toString());
            if (zypxQa.getCount() == null) {
                zypxQa.setCount(1);
            } else {
                zypxQa.setCount(zypxQa.getCount() + 1);
            }
            qaMapper.updateById(zypxQa);

            result.put("paper", qaContent);
            result.put("paperMap", qa);
        } else {
            result.put("code", -1);
            result.put("msg", "调查问卷不存在");
            return result;
        }
        return result;
    }

    public Object submitSurveyPaper(HttpServletRequest request, String studentId) {
        List<ZypxQaAnswerDetail> qaAnswerDetails = new ArrayList<>();
        String qaaId = BaseUtil.generateId2();
        Map<String, String[]> parameterMap = request.getParameterMap();
        String qaId = request.getParameter("qaId");
        ZypxQa zypxQa = qaMapper.selectById(qaId);
        String xmId = request.getParameter("xmId");
        if(StringUtils.isEmpty(xmId) && StringUtils.isEmpty(qaId)) {
            throw BizException.withMessage("参数缺失");
        }
        ZypxQaAnswer qaAnswer = qaAnswerMapper.getStudentQaAnswer(xmId, qaId, studentId);
        if (qaAnswer !=null) {
        	throw BizException.withMessage("问卷已经提交,不可重复作答");
        }
        ZypxQaAnswerDetail detail;
        QaPaper qaPaper = ModelHelper.convertObject(zypxQa.getContent());
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            detail = new ZypxQaAnswerDetail();
            detail.setId(BaseUtil.generateId2());

            detail.setQaaId(qaaId);
            if (entry.getKey().startsWith("Q-")) {
                if (StringUtils.isEmpty(StringUtils.join(entry.getValue(), Constants.TM_SPLITER))) {
                    continue;
                }
                List<QaQuestion> content = qaPaper.getContent();
                for (QaQuestion question : content) {
                    if (("Q-"+question.getQuesId()).equals(entry.getKey())) {
                        question.setAnswer(StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
                        detail.setQuesId(question.getQuesId());
                        detail.setQuesType(question.getQuesType());
                        detail.setAnswer(question.getAnswer());
                    }
                }
                qaAnswerDetails.add(detail);
            }
        }
        ZypxQaAnswer answer = new ZypxQaAnswer(qaaId, qaId, ModelHelper.formatObject(qaPaper), AnswerStatus.SUBMITTED, new Date(), null);
        answer.setStudentId(studentId);
        answer.setSubmitTime(new Date());
        answer.setXmId(xmId);
        qaAnswerMapper.insert(answer);
        for (ZypxQaAnswerDetail detail1 : qaAnswerDetails) {
        	qaAnswerDetailMapper.insert(detail1);
        }
        return true;
    }
    
    /**
     * 根据项目配置的证书模板生成学员的证书,返回证书PDF下载地址
     */
    public ZypxStudentCerti buildStudentCerti(String xmId, String studentId, String portalWebUrl) throws IOException, DocumentException, WriterException {
    	ZypxStudentCerti studentCerti = this.getStudentCerti(xmId, studentId);
    	if (StringUtils.isNotEmpty(studentCerti.getCertiUrl()) && StringUtils.isNotEmpty(studentCerti.getCertiImgUrl())) {
			return studentCerti;
		}
    	ZypxXm zypxXm = xmMapper.selectById(xmId);
    	if (StringUtils.isEmpty(zypxXm.getCertiTplId())) {
			throw BizException.withMessage("培训项目未设置结业证书模板");
		}
    	if (zypxXm.getCertiDate() == null) {
    		throw BizException.withMessage("培训项目未设置证书证芯日期");
    	}
    	if (StringUtils.isEmpty(studentCerti.getCertiNum())) {
    		throw BizException.withMessage("该学员尚未设置证书编号，无法生成结业证书");
    	}
    	if (StringUtils.isEmpty(portalWebUrl)) {
    		throw BizException.withMessage("平台未配置学员端门户域名");
		}
    	//准备数据对象
    	ZypxCertiTpl certiTpl = certiTplMapper.selectById(zypxXm.getCertiTplId());
		StudentInfo studentInfo = studentInfoService.getByStudentId(studentId);
		Map<String, Object> data = new HashMap<String, Object>();
		data.put(Constants.CertiTemplateField.name, studentInfo.getName());
		data.put(Constants.CertiTemplateField.sfzh, studentInfo.getSfzh());
		data.put(Constants.CertiTemplateField.certi_num, studentCerti.getCertiNum());
		if (studentInfo.getGender() != null) {
			data.put(Constants.CertiTemplateField.gender, studentInfo.getGender().getName());
		}
		data.put(Constants.CertiTemplateField.photo, studentInfo.getStudentPhoto());
		String validateUrl = portalWebUrl + "/personal/certiValidateQr/"+studentCerti.getId();
		data.put(Constants.CertiTemplateField.qrcode, validateUrl);
		data.put(Constants.CertiTemplateField.xm_name, StringUtils.isNotEmpty(zypxXm.getCertiXmName()) ? zypxXm.getCertiXmName() : zypxXm.getTitle());
		String time = DateUtils.format(zypxXm.getCertiDate(), "yyyy-M-dd");
		String[] times = StringUtils.split(time, "-");
		data.put(Constants.CertiTemplateField.year, times[0]);
		data.put(Constants.CertiTemplateField.month, times[1]);
		data.put(Constants.CertiTemplateField.day, times[2]);
		String hours = "0";//完成的学时
		if (zypxXm.getCertiHours() != null) {
			hours = zypxXm.getCertiHours() + "";
		}
		else {
			Map<String, Object> learningScore = zypxScoreMapper.getLearningScore(xmId, studentId);
			if (learningScore != null) {
				hours = BaseUtil.getStringValueFromMap(learningScore, "totalFinishedHours", "0");
			}
		}
		data.put(Constants.CertiTemplateField.hours, new BigDecimal(hours).intValue());
		
		//生成证书pdf
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "temp_student_certi");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		String randomName = UUID.randomUUID().toString() ;
		File outPdf = new File(tempdir, randomName + ".pdf");
		this.fillCertiInfo(certiTpl.getTemplatePath(), data, outPdf);
		String path = attConfig.getRootDir()+ "/training/certi/" + xmId;
		String newFileName = randomName + ".pdf";
		String certiUrl = FileHelper.storeFile(path, new FileInputStream(outPdf), newFileName);
		
		//生成PDF缩略图
		File outImage = new File(tempdir,randomName + ".png");
		BaseUtil.singlePagePdf2Image(outPdf, outImage);
		String newImageFileName = randomName + ".png";
		String certiImgUrl = FileHelper.storeFile(path, new FileInputStream(outImage), newImageFileName);
		
		studentCerti.setCertiUrl(certiUrl);
		studentCerti.setCertiImgUrl(certiImgUrl);
		studentCerti.setCertiTime(new Date());
		mapper.updateAllColumnById(studentCerti);
		
		return studentCerti;
    }
    
    
    private void fillCertiInfo(String templateUrl, Map<String, Object> data, File outPdf) throws IOException, DocumentException, WriterException {
		PdfReader reader = new PdfReader(templateUrl);
        FileOutputStream out = new FileOutputStream(outPdf);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(reader, bos);
        AcroFields form = stamper.getAcroFields();
        //设置基础信息
        form.setField(Constants.CertiTemplateField.name, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.name, ""));
        form.setField(Constants.CertiTemplateField.sfzh, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.sfzh, ""));
        form.setField(Constants.CertiTemplateField.year, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.year, ""));
        form.setField(Constants.CertiTemplateField.month, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.month, ""));
        form.setField(Constants.CertiTemplateField.day, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.day, ""));
        
        if (form.getField(Constants.CertiTemplateField.gender) != null) {
        	form.setField(Constants.CertiTemplateField.gender, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.gender,""));
		}
        if (form.getField(Constants.CertiTemplateField.certi_num) != null) {
        	form.setField(Constants.CertiTemplateField.certi_num, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.certi_num,""));
		}
        if (form.getField(Constants.CertiTemplateField.xm_name) != null) {
        	form.setField(Constants.CertiTemplateField.xm_name, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.xm_name,""));
        }
        if (form.getField(Constants.CertiTemplateField.hours) != null) {
        	form.setField(Constants.CertiTemplateField.hours, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.hours, ""));
        }
        //设置学员头像
        if (form.getField(Constants.CertiTemplateField.photo) != null && StringUtils.isNotEmpty(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.photo))) {
        	 int pageNo = form.getFieldPositions(Constants.CertiTemplateField.photo).get(0).page;
             Rectangle signRect = form.getFieldPositions(Constants.CertiTemplateField.photo).get(0).position;
             float x = signRect.getLeft();
             float y = signRect.getBottom();
             //根据路径读取图片
             Image image = Image.getInstance(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.photo));
             //获取图片页面
             PdfContentByte under = stamper.getOverContent(pageNo);
             //图片大小自适应
             image.scaleToFit(signRect.getWidth(), signRect.getHeight());
             //添加图片
             image.setAbsolutePosition(x, y);
             under.addImage(image);
		}
        //设置防伪二维码
        if (form.getField(Constants.CertiTemplateField.qrcode) != null && StringUtils.isNotEmpty(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.qrcode))) {
        	int pageNo = form.getFieldPositions(Constants.CertiTemplateField.qrcode).get(0).page;
        	Rectangle signRect = form.getFieldPositions(Constants.CertiTemplateField.qrcode).get(0).position;
        	 float x = signRect.getLeft();
             float y = signRect.getBottom();
            //绘制二维码
            float width = signRect.getRight() - signRect.getLeft();
            BufferedImage bufferedImage = QrCodeUtils.createQrCodeWithNoWhiteSpace(BaseUtil.getStringValueFromMap(data, "QRCODE"), (int)width, (int)width);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", os);
            byte[] byteArray = os.toByteArray();
            Image qrcodeImage = Image.getInstance(byteArray);
            //绘制在第一页
            PdfContentByte cb = stamper.getOverContent(pageNo);
            //图片大小自适应
            qrcodeImage.scaleToFit(signRect.getWidth(), signRect.getHeight());
            //添加图片
            qrcodeImage.setAbsolutePosition(x, y);
            cb.addImage(qrcodeImage);
		}
        //生成目标PDF文件
        stamper.setFormFlattening(true);
        stamper.close();
        Document doc = new Document();
        PdfCopy copy = new PdfCopy(doc, out);
        doc.open();
        PdfImportedPage importPage = copy.getImportedPage(new PdfReader(bos.toByteArray()), 1);
        copy.addPage(importPage);
        doc.close();
    }
    
    public ZypxStudentCerti getStudentCerti(String xmId, String studentId) {
    	EntityWrapper<ZypxStudentCerti> wrapper = new EntityWrapper();
		wrapper.eq("xm_id", xmId);
		wrapper.eq("student_id", studentId);
		List<ZypxStudentCerti> checkCertis = mapper.selectList(wrapper);
		ZypxStudentCerti studentCerti = checkCertis.size() > 0 ? checkCertis.get(0) : null;
		return studentCerti;
    }

    public Integer getCertiByStudentId(String studentId){
        EntityWrapper<ZypxStudentCerti> wrapper = new EntityWrapper();
        wrapper.eq("student_id", studentId);
        wrapper.isNotNull("certi_num");
        return mapper.selectCount(wrapper);
    }

    /**
     * 批量下载证书
     */
    public void batchDownloadCerti(String xmId, String hostOrgId, String portalWebUrl, OutputStream os) throws Exception{
        ZypxStudentCertiMgrParams params = new ZypxStudentCertiMgrParams();
        params.setXmId(xmId);
        params.setHostOrgId(hostOrgId);
        params.setIsOnlyCertiNum(Constants.YES);
        params.setCurrent(1);
        params.setSize(Integer.MAX_VALUE);
        List<Map<String, Object>> certis = zypxStudentCertiMapper.list(params.getCondition(), params);
        List<File> studentCertiFiles = new ArrayList<>();
        File tmpFile = null;
        File zip = null;
        try {
            tmpFile = FileHelper.createTmpFile();
            zip = new File(tmpFile.getPath(), "studentCerti.zip");
            for (Map<String, Object> certi : certis) {
                File certiFile = new File(tmpFile.getPath(), certi.get("studentName").toString() + "_" + certi.get("studentSfzh").toString() + ".pdf");
                if (!certiFile.exists()) {
                    certiFile.getParentFile().mkdirs();
                }
                ZypxStudentCerti studentCerti = this.buildStudentCerti(xmId, certi.get("studentId").toString(), portalWebUrl);
                //下载证书pdf
                URL url = new URL(studentCerti.getCertiUrl());
                HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
                httpConn.setRequestMethod("GET");
                int responseCode = httpConn.getResponseCode();
                if (responseCode == 200) {
                    InputStream inputStream = httpConn.getInputStream();
                    IOUtils.copy(inputStream, new FileOutputStream(certiFile));
                    studentCertiFiles.add(certiFile);
                }
                httpConn.disconnect();
            }
            ZipUtils.doZip(zip, studentCertiFiles);
            IOUtils.copy(Files.newInputStream(zip.toPath()), os);
        } finally {
            FileHelper.delFile(tmpFile);
        }
    }
}
