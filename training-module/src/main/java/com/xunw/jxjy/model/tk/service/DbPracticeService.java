package com.xunw.jxjy.model.tk.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.tk.entity.DbPractice;
import com.xunw.jxjy.model.tk.entity.DbPracticeDetail;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.mapper.DbPracticeDetailMapper;
import com.xunw.jxjy.model.tk.mapper.DbPracticeMapper;
import com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper;
import com.xunw.jxjy.model.zypx.entity.Plan;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.mapper.PlanMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;
import com.xunw.jxjy.model.zypx.vo.ZypxPlanXmVo;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.utils.ModelHelper;
import oracle.jdbc.StructMetaData;
import oracle.jdbc.driver.Const;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【BIZ_DB_PRACTICE】的数据库操作Service实现
* @createDate 2023-11-06 09:51:12
*/
@Service
public class DbPracticeService extends BaseCRUDService<DbPracticeMapper, DbPractice> {

    @Autowired
    private DbPracticeDetailMapper dbPracticeDetailMapper;
    @Autowired
    private QuestionEntityMapper questionEntityMapper;
    @Autowired
    private ZypxXmMapper zypxXmMapper;
    @Autowired
    private PlanMapper planMapper;

    public List<Map<String, Object>> getDbPractice(String xmId, String studentId) {
        return mapper.getDbPractice(xmId, studentId);
    }

    public Object save(String dbPracticeId, String questionId, String answer, String studentId) {
        List<DbPracticeDetail> questions = dbPracticeDetailMapper.selectList(new EntityWrapper<DbPracticeDetail>()
                .eq("db_practice_id", dbPracticeId)
                .eq("student_id", studentId)
                .eq("question_id", questionId));
        DbPracticeDetail dbPracticeDetail = new DbPracticeDetail();
        if (CollectionUtils.isNotEmpty(questions)) {
            dbPracticeDetail = questions.get(0);
            dbPracticeDetail.setAnswer(answer);
            dbPracticeDetail.setCreateTime(new Date());
            dbPracticeDetailMapper.updateById(dbPracticeDetail);
        } else {
            dbPracticeDetail.setId(BaseUtil.generateId());
            dbPracticeDetail.setDbPracticeId(dbPracticeId);
            dbPracticeDetail.setQuestionId(questionId);
            dbPracticeDetail.setAnswer(answer);
            dbPracticeDetail.setStudentId(studentId);
            dbPracticeDetail.setCreateTime(new Date());
            dbPracticeDetailMapper.insert(dbPracticeDetail);
        }
        List<DbPracticeDetail> doneQuestions = dbPracticeDetailMapper.selectList(new EntityWrapper<DbPracticeDetail>()
                .eq("db_practice_id", dbPracticeId)
                .eq("student_id", studentId));
        List<Map<String, Object>> allQuestions = mapper.getPracticeQuestion(dbPracticeId);
        Map<String, Object> map = new HashMap<>();
        map.put("progress", Math.round((float) doneQuestions.size() / allQuestions.size() * 100));
        return map;
    }

    public Object getPracticeQuestions(String dbPracticeId, Integer current, Integer size, String studentId) {
        Page page = new Page<>(current, size);
        List<Map<String, Object>> questions = dbPracticeDetailMapper.getPracticeQuestion(dbPracticeId, studentId, page);
        questions.forEach(x->{
            x.put("question", (Question) ModelHelper.convertObject(x.get("data").toString()));
            //如果是套题则获取相应子题
            //--暂时不考虑套题
            if(Stlb.TT.name().equals(BaseUtil.getStringValueFromMap(x, "type"))) {
                String parentId = (String)x.get("id");
                EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<>();
                wrapper.eq("parent_id", parentId);
                wrapper.orderBy("seq_num", true);
                List<QuestionEntity> children = questionEntityMapper.selectList(wrapper);
                x.put("childrens", children);
            }
        });
        page.setRecords(questions);
        return page;
    }

    @Transactional
    public void setDbPractices(String xmId, String professionId, TechLevel techLevel, String dbIds) {
        ZypxXm zypxXm = zypxXmMapper.selectById(xmId);
        Plan plan = planMapper.selectById(zypxXm.getPlanId());
        if (Constants.YES.equals(plan.getIsSkill())) {
            if (StringUtils.isEmpty(professionId)) {
                throw BizException.withMessage("技能类项目的职业id不能为空");
            }
            if (techLevel == null) {
                throw BizException.withMessage("技能类项目的技能等级不能为空");
            }
        }
        //删除原来的题库练习
        mapper.delete(new EntityWrapper<DbPractice>().eq("xm_id", xmId)
                .eq(StringUtils.isNotEmpty(professionId), "profession_id", professionId)
                .eq(techLevel != null, "tech_level", techLevel));
        List<DbPractice> list = new ArrayList<>();
        for (String dbId : dbIds.split(",")) {
            DbPractice dbPractice = new DbPractice();
            dbPractice.setId(BaseUtil.generateId());
            dbPractice.setXmId(xmId);
            dbPractice.setProfessionId(professionId);
            dbPractice.setTechLevel(techLevel);
            dbPractice.setDbId(dbId);
            list.add(dbPractice);
        }
        DBUtils.insertBatch(list, DbPractice.class);
    }

    public Object progress(String xmId, String keyword, String hostOrgId, Page page) {
        page.setRecords(mapper.progress(xmId, keyword, hostOrgId, page));
        return page;
    }

    public Object dbList(String xmId, String professionId, TechLevel techLevel) {
        return mapper.dbList(xmId, professionId, techLevel);
    }

    /**
     * 题库练习重做
     */
    public void redo(String dbPracticeId, String studentId) {
        dbPracticeDetailMapper.delete(new EntityWrapper<DbPracticeDetail>()
                .eq("DB_PRACTICE_ID", dbPracticeId)
                .eq("STUDENT_ID", studentId));
    }
}




