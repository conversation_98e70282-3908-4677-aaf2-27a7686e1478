package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.BuyType;
import com.xunw.jxjy.model.enums.OpenStudyType;
import com.xunw.jxjy.model.enums.XmStatus;
import com.xunw.jxjy.model.inf.entity.Form;
import com.xunw.jxjy.model.inf.entity.ZypxType;
import com.xunw.jxjy.model.sys.entity.User;

/**
 * 职业培训项目表
 */
@TableName("biz_xm")
public class ZypxXm implements Serializable, Cloneable {

	private static final long serialVersionUID = -2700133747288407047L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 项目编号
	@TableField("serial_number")
	private String serialNumber;

	// 项目名称
	@TableField("title")
	private String title;

	// 项目类型ID
	@TableField("type_id")
	private String typeId;

	// 项目对象
	@TableField("trainees")
	private String trainees;

	// 培训开始时间
	@TableField("start_time")
	private Date startTime;

	// 培训结束时间
	@TableField("end_time")
	private Date endTime;

	// 集体报名开始时间
	@TableField("jtbm_start_time")
	private Date jtbmStartTime;

	// 集体报名结束时间
	@TableField("jtbm_end_time")
	private Date jtbmEndTime;

	// 个人报名开始时间
	@TableField("grbm_start_time")
	private Date grbmStartTime;

	// 个人报名结束时间
	@TableField("grbm_end_time")
	private Date grbmEndTime;

	// 是否允许个人报名 1是 0否
	@TableField("is_allow_grbm")
	private String isAllowGrbm;

	//学习结束时间 控制直播 + 课件 + 面授 的学习时间
	@TableField("study_end_time")
	private Date studyEndTime;

	// 是否开放课件学习弹题 1 是 0 否
	@TableField("is_pop_ques")
	private String isPopQues;

	// 状态 开启 关闭
	@TableField("status")
	private XmStatus status;

	// 培训费用
	@TableField("amount")
	private Double amount;

	//图标
	@TableField("logo")
	private String logo;

	// 宣传图
	@TableField("xct")
	private String xct;

	// 培训通知
	@TableField("notice")
	private String notice;

	// 是否开启抓拍摄像头 1是 0否
	@TableField("is_open_camera")
	private String isOpenCamera;

	// 抓拍间隔时间(单位s)
	@TableField("photo_catch_interval")
	private Integer photoCatchInterval;

	// 是否开启人脸比对 1是 0否
	@TableField("is_open_verify")
	private String isOpenVerify;

	// 人脸对比阈值
	@TableField("verify_threshold")
	private Double verifyThreshold;

	// 年份
	@TableField("years")
	private String years;

	// 培训计划ID
	@TableField("plan_id")
	private String planId;

	//高德地图-报到地点
	@TableField("poiaddress")
	private String poiaddress;

	//高德地图-报到GPS坐标
	@TableField("poilatlng")
	private String poilatlng;

	//高德地图-报到坐标范围
	@TableField("arrive_range")
	private String arriveRange;

	//高德地图-报到简介
	@TableField("report_introduction")
	private String reportIntroduction;

	//是否允许个人选课 1是  0 否
	@TableField("is_allow_choose_course")
	private String isAllowChooseCourse;

	//项目负责人
	@TableField("leader_id")
	private String leaderId;

	//学习成绩占比
	@TableField("learning_score_zb")
	private Double learningScoreZb;

	//练习成绩占比
	@TableField("practice_score_zb")
	private Double practiceScoreZb;

	//项目终极考试成绩占比
	@TableField("final_exam_score_zb")
	private Double finalExamScoreZb;

	//归档操作人
	@TableField("archive_user_id")
	private String archiveUserId;

	//归档时间
	@TableField("archive_time")
	private Date archiveTime;

	//培训人数限制
	@TableField("limit_count")
	private Integer limitCount;

	//结业证书模板id
    @TableField("certi_tpl_id")
    private String certiTplId;

    // 结业证书的证芯日期
    @TableField("certi_date")
    private Date certiDate;

    //结业证书上显示的项目名称
    @TableField("certi_xm_name")
    private String certiXmName;

    //是否允许先学习后缴费    1是   0 否
    @TableField("is_alllow_study_before_pay")
    private String isAlllowStudyBeforePay;

    //是否允许先考试(练习、阶段测验、终极考核)后缴费    1是   0 否
    @TableField("is_alllow_exam_before_pay")
    private String isAlllowExamBeforePay;

    //最低学时要求
    @TableField("certi_hours")
    private Double certiHours;

    //是否是热门项目 1 是 0 否
    @TableField("is_hot")
	private String isHot;

    // 创建用户id
 	@TableField("creator_id")
 	private String creatorId;

 	// 创建时间
 	@TableField("create_time")
 	private Date createTime;

 	// 修改用户id
 	@TableField("updator_id")
 	private String updatorId;

 	// 修改时间
 	@TableField("update_time")
 	private Date updateTime;

	//报名审核结果是否发送短信通知
	@TableField("is_auto_send_sms")
	private String isAutoSendSms;

	//项目报名报名口令
	@TableField("bm_invite_code")
	private String bmInviteCode;

	//线下报名人数
	@TableField("off_line_bm_count")
	private Integer offLineBmCount;

	//线上报名人数
	@TableField("online_bm_count")
	private Integer onlineBmCount;

	//是否报名前必须完善个人信息
	@TableField("is_must_fill_personal_info")
	private String isMustFillPersonalInfo;

	//是否允许手机端学习
	@TableField("is_allow_mobile_study")
	private String isAllowMobileStudy;

	//报名信息是否需要审核0否；1是
	@TableField("is_need_approve")
	private String isNeedApprove;

	//培训课程是否已发布
	@TableField("is_course_publish")
	private String isCoursePublish;

	//培训课程发布时间
	@TableField("course_publish_time")
	private Date coursePublishTime;

	//培训课程发布用户id
	@TableField("course_publish_userid")
	private String coursePublishUserid;

	//主办单位ID
	@TableField("host_org_id")
	private String hostOrgId;

	//是否使用第三方学习平台
	@TableField("is_open_study")
	private String isOpenStudy;

	//第三方学习平台类型
	@TableField("open_study_type")
	private OpenStudyType openStudyType;

	//第三方平台学习链接
	@TableField("open_study_url")
	private String openStudyUrl;

	//是否允许报名多门课程
	@TableField("is_allow_multi_choose_course")
	private Integer isAllowMultiChooseCourse;

	/**
	 * 购买类型，COURSE 按课程购买，XM 按项目购买
	 */
	@TableField("buy_type")
	private BuyType buyType;

	//2024-09-03 三峡电力需求增加该字段：是否免注册
	@TableField("is_exempt_register")
	private Integer isExemptRegister = 0;
			
	// 2024-09-13 理工大移动端需求：学员须知
	@TableField("student_notice")
	private String studentNotice;

	// 2024-10-12 理工大移动端需求 师资简介
	@TableField("teacher_introduction")
	private String teacherIntroduction;

	// 2024-10-12 理工大移动端需求 管理团队及联系方式
	@TableField("management_team")
	private String managementTeam;

	// 2024-10-14 理工大移动端需求 班委职责
	@TableField("class_committee_work")
	private String classCommitteeWork;

	// 2024-10-14 理工大移动端需求 小组工作内容 包含组长职责和值日工作内容
	@TableField("group_work")
	private String groupWork;

	// 2024-10-14 理工大移动端需求 住宿安排
	@TableField("stay_plan")
	private String stayPlan;

	// 2024-10-14 理工大移动端需求 用餐地点坐标
	@TableField("eat_point")
	private String eatPoint;

	// 2024-10-14 理工大移动端需求 用餐安排
	@TableField("eat_plan")
	private String eatPlan;

	@TableField(exist = false)
	private ZypxType zypxType;

	@TableField(exist = false)
	private Plan plan;

	@TableField(exist = false)
	private User leader;

	@TableField(exist = false)
	private Form form;

	@TableField(exist = false)
	private String studentId;

	@TableField(exist = false)
	private String bmId;

	@TableField(exist = false)
	private String qaId;

	@TableField(exist = false)
	private List<Map<String, Object>> courses;

	//收入
	@TableField("income_amount")
	private BigDecimal incomeAmount;

	@TableField(exist = false)
	private PlanDetail planDetail;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getTrainees() {
		return trainees;
	}

	public void setTrainees(String trainees) {
		this.trainees = trainees;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getJtbmStartTime() {
		return jtbmStartTime;
	}

	public void setJtbmStartTime(Date jtbmStartTime) {
		this.jtbmStartTime = jtbmStartTime;
	}

	public Date getJtbmEndTime() {
		return jtbmEndTime;
	}

	public void setJtbmEndTime(Date jtbmEndTime) {
		this.jtbmEndTime = jtbmEndTime;
	}

	public Date getGrbmStartTime() {
		return grbmStartTime;
	}

	public void setGrbmStartTime(Date grbmStartTime) {
		this.grbmStartTime = grbmStartTime;
	}

	public Date getGrbmEndTime() {
		return grbmEndTime;
	}

	public void setGrbmEndTime(Date grbmEndTime) {
		this.grbmEndTime = grbmEndTime;
	}

	public String getIsAllowGrbm() {
		return isAllowGrbm;
	}

	public void setIsAllowGrbm(String isAllowGrbm) {
		this.isAllowGrbm = isAllowGrbm;
	}

	public Date getStudyEndTime() {
		return studyEndTime;
	}

	public void setStudyEndTime(Date studyEndTime) {
		this.studyEndTime = studyEndTime;
	}

	public String getIsPopQues() {
		return isPopQues;
	}

	public void setIsPopQues(String isPopQues) {
		this.isPopQues = isPopQues;
	}

	public XmStatus getStatus() {
		return status;
	}

	public void setStatus(XmStatus status) {
		this.status = status;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public String getXct() {
		return xct;
	}

	public void setXct(String xct) {
		this.xct = xct;
	}

	public String getNotice() {
		return notice;
	}

	public void setNotice(String notice) {
		this.notice = notice;
	}

	public String getIsOpenCamera() {
		return isOpenCamera;
	}

	public void setIsOpenCamera(String isOpenCamera) {
		this.isOpenCamera = isOpenCamera;
	}

	public Integer getPhotoCatchInterval() {
		return photoCatchInterval;
	}

	public void setPhotoCatchInterval(Integer photoCatchInterval) {
		this.photoCatchInterval = photoCatchInterval;
	}

	public String getIsOpenVerify() {
		return isOpenVerify;
	}

	public void setIsOpenVerify(String isOpenVerify) {
		this.isOpenVerify = isOpenVerify;
	}

	public Double getVerifyThreshold() {
		return verifyThreshold;
	}

	public void setVerifyThreshold(Double verifyThreshold) {
		this.verifyThreshold = verifyThreshold;
	}

	public String getYears() {
		return years;
	}

	public void setYears(String years) {
		this.years = years;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getPoiaddress() {
		return poiaddress;
	}

	public void setPoiaddress(String poiaddress) {
		this.poiaddress = poiaddress;
	}

	public String getPoilatlng() {
		return poilatlng;
	}

	public void setPoilatlng(String poilatlng) {
		this.poilatlng = poilatlng;
	}

	public String getArriveRange() {
		return arriveRange;
	}

	public void setArriveRange(String arriveRange) {
		this.arriveRange = arriveRange;
	}

	public String getIsAllowChooseCourse() {
		return isAllowChooseCourse;
	}

	public void setIsAllowChooseCourse(String isAllowChooseCourse) {
		this.isAllowChooseCourse = isAllowChooseCourse;
	}

	public String getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}

	public Double getLearningScoreZb() {
		return learningScoreZb;
	}

	public void setLearningScoreZb(Double learningScoreZb) {
		this.learningScoreZb = learningScoreZb;
	}

	public Double getPracticeScoreZb() {
		return practiceScoreZb;
	}

	public void setPracticeScoreZb(Double practiceScoreZb) {
		this.practiceScoreZb = practiceScoreZb;
	}

	public Double getFinalExamScoreZb() {
		return finalExamScoreZb;
	}

	public void setFinalExamScoreZb(Double finalExamScoreZb) {
		this.finalExamScoreZb = finalExamScoreZb;
	}

	public String getArchiveUserId() {
		return archiveUserId;
	}

	public void setArchiveUserId(String archiveUserId) {
		this.archiveUserId = archiveUserId;
	}

	public Date getArchiveTime() {
		return archiveTime;
	}

	public void setArchiveTime(Date archiveTime) {
		this.archiveTime = archiveTime;
	}

	public Integer getLimitCount() {
		return limitCount;
	}

	public void setLimitCount(Integer limitCount) {
		this.limitCount = limitCount;
	}

	public String getCertiTplId() {
		return certiTplId;
	}

	public void setCertiTplId(String certiTplId) {
		this.certiTplId = certiTplId;
	}

	public Date getCertiDate() {
		return certiDate;
	}

	public void setCertiDate(Date certiDate) {
		this.certiDate = certiDate;
	}

	public String getCertiXmName() {
		return certiXmName;
	}

	public void setCertiXmName(String certiXmName) {
		this.certiXmName = certiXmName;
	}

	public String getIsAlllowStudyBeforePay() {
		return isAlllowStudyBeforePay;
	}

	public void setIsAlllowStudyBeforePay(String isAlllowStudyBeforePay) {
		this.isAlllowStudyBeforePay = isAlllowStudyBeforePay;
	}

	public String getIsAlllowExamBeforePay() {
		return isAlllowExamBeforePay;
	}

	public void setIsAlllowExamBeforePay(String isAlllowExamBeforePay) {
		this.isAlllowExamBeforePay = isAlllowExamBeforePay;
	}

	public Double getCertiHours() {
		return certiHours;
	}

	public void setCertiHours(Double certiHours) {
		this.certiHours = certiHours;
	}

	public String getIsHot() {
		return isHot;
	}

	public void setIsHot(String isHot) {
		this.isHot = isHot;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getIsAutoSendSms() {
		return isAutoSendSms;
	}

	public void setIsAutoSendSms(String isAutoSendSms) {
		this.isAutoSendSms = isAutoSendSms;
	}

	public String getBmInviteCode() {
		return bmInviteCode;
	}

	public void setBmInviteCode(String bmInviteCode) {
		this.bmInviteCode = bmInviteCode;
	}

	public Integer getOffLineBmCount() {
		return offLineBmCount;
	}

	public void setOffLineBmCount(Integer offLineBmCount) {
		this.offLineBmCount = offLineBmCount;
	}

	public String getIsMustFillPersonalInfo() {
		return isMustFillPersonalInfo;
	}

	public void setIsMustFillPersonalInfo(String isMustFillPersonalInfo) {
		this.isMustFillPersonalInfo = isMustFillPersonalInfo;
	}

	public String getIsAllowMobileStudy() {
		return isAllowMobileStudy;
	}

	public void setIsAllowMobileStudy(String isAllowMobileStudy) {
		this.isAllowMobileStudy = isAllowMobileStudy;
	}

	public String getIsNeedApprove() {
		return isNeedApprove;
	}

	public void setIsNeedApprove(String isNeedApprove) {
		this.isNeedApprove = isNeedApprove;
	}

	public ZypxType getZypxType() {
		return zypxType;
	}

	public void setZypxType(ZypxType zypxType) {
		this.zypxType = zypxType;
	}

	public Plan getPlan() {
		return plan;
	}

	public void setPlan(Plan plan) {
		this.plan = plan;
	}

	public User getLeader() {
		return leader;
	}

	public void setLeader(User leader) {
		this.leader = leader;
	}

	public String getIsCoursePublish() {
		return isCoursePublish;
	}

	public void setIsCoursePublish(String isCoursePublish) {
		this.isCoursePublish = isCoursePublish;
	}

	public Date getCoursePublishTime() {
		return coursePublishTime;
	}

	public void setCoursePublishTime(Date coursePublishTime) {
		this.coursePublishTime = coursePublishTime;
	}

	public String getCoursePublishUserid() {
		return coursePublishUserid;
	}

	public void setCoursePublishUserid(String coursePublishUserid) {
		this.coursePublishUserid = coursePublishUserid;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

	public Form getForm() {
		return form;
	}

	public void setForm(Form form) {
		this.form = form;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getBmId() {
		return bmId;
	}

	public void setBmId(String bmId) {
		this.bmId = bmId;
	}

	public String getQaId() {
		return qaId;
	}

	public void setQaId(String qaId) {
		this.qaId = qaId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public String getIsOpenStudy() {
		return isOpenStudy;
	}

	public void setIsOpenStudy(String isOpenStudy) {
		this.isOpenStudy = isOpenStudy;
	}

	public String getOpenStudyUrl() {
		return openStudyUrl;
	}

	public void setOpenStudyUrl(String openStudyUrl) {
		this.openStudyUrl = openStudyUrl;
	}

	public Integer getOnlineBmCount() {
		return onlineBmCount;
	}

	public void setOnlineBmCount(Integer onlineBmCount) {
		this.onlineBmCount = onlineBmCount;
	}

	public BuyType getBuyType() {
		return buyType;
	}

	public void setBuyType(BuyType buyType) {
		this.buyType = buyType;
	}

	public List<Map<String, Object>> getCourses() {
		return courses;
	}

	public void setCourses(List<Map<String, Object>> courses) {
		this.courses = courses;
	}

	public Integer getIsExemptRegister() {
		return isExemptRegister;
	}

	public void setIsExemptRegister(Integer isExemptRegister) {
		this.isExemptRegister = isExemptRegister;
	}

	public String getStudentNotice() {
		return studentNotice;
	}

	public void setStudentNotice(String studentNotice) {
		this.studentNotice = studentNotice;
	}

	public String getReportIntroduction() {
		return reportIntroduction;
	}

	public void setReportIntroduction(String reportIntroduction) {
		this.reportIntroduction = reportIntroduction;
	}

	public String getTeacherIntroduction() {
		return teacherIntroduction;
	}

	public void setTeacherIntroduction(String teacherIntroduction) {
		this.teacherIntroduction = teacherIntroduction;
	}

	public String getManagementTeam() {
		return managementTeam;
	}

	public void setManagementTeam(String managementTeam) {
		this.managementTeam = managementTeam;
	}

	public String getClassCommitteeWork() {
		return classCommitteeWork;
	}

	public void setClassCommitteeWork(String classCommitteeWork) {
		this.classCommitteeWork = classCommitteeWork;
	}

	public String getGroupWork() {
		return groupWork;
	}

	public void setGroupWork(String groupWork) {
		this.groupWork = groupWork;
	}

	public String getStayPlan() {
		return stayPlan;
	}

	public void setStayPlan(String stayPlan) {
		this.stayPlan = stayPlan;
	}

	public String getEatPlan() {
		return eatPlan;
	}

	public void setEatPlan(String eatPlan) {
		this.eatPlan = eatPlan;
	}

	public String getEatPoint() {
		return eatPoint;
	}

	public void setEatPoint(String eatPoint) {
		this.eatPoint = eatPoint;
	}

	public BigDecimal getIncomeAmount() {
		return incomeAmount;
	}

	public void setIncomeAmount(BigDecimal incomeAmount) {
		this.incomeAmount = incomeAmount;
	}

	public PlanDetail getPlanDetail() {
		return planDetail;
	}

	public void setPlanDetail(PlanDetail planDetail) {
		this.planDetail = planDetail;
	}

	public OpenStudyType getOpenStudyType() {
		return openStudyType;
	}

	public void setOpenStudyType(OpenStudyType openStudyType) {
		this.openStudyType = openStudyType;
	}

	public Integer getIsAllowMultiChooseCourse() {
		return isAllowMultiChooseCourse;
	}

	public void setIsAllowMultiChooseCourse(Integer isAllowMultiChooseCourse) {
		this.isAllowMultiChooseCourse = isAllowMultiChooseCourse;
	}
}