package com.xunw.jxjy.model.zypx.service;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.common.Chapter;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.Lesson;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.core.SmsSevice;
import com.xunw.jxjy.model.inf.entity.Courseware;
import com.xunw.jxjy.model.inf.mapper.CoursewareMapper;
import com.xunw.jxjy.model.learning.entity.CoursewareLearningProgress;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmCourseMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxCoursewareLearningProgressMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxStudentLearningScoreMapper;
import com.xunw.jxjy.model.zypx.params.CoursewareGkqkQueryParams;
import com.xunw.jxjy.paper.utils.ModelHelper;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

/**
 * 课件学生学生观看情况
 * <AUTHOR>
 */
@Service
public class ZypxCoursewareGkqkService extends BaseCRUDService<ZypxCoursewareLearningProgressMapper, CoursewareLearningProgress>{
	
	private static final Logger LOGGER = LoggerFactory.getLogger(ZypxCoursewareGkqkService.class);
	//课件学习进度短信提醒
	private static final String KJXX_NOTICE_SMS_TPL = "214895";
	
	@Autowired
	private ZypxBmCourseMapper bmCourseMapper;
	@Autowired
	private CoursewareMapper coursewareMapper;
	@Autowired
	private SystemSettingService sysXtszService;
	@Autowired
	private ZypxStudentLearningScoreMapper learningScoreMapper;
	@Autowired
	private SmsSevice smsSevice;
	
	public Page pageQuery(CoursewareGkqkQueryParams params) throws SQLException, IOException {
        List<Map<String, Object>> bizZypxKjxxjdList = mapper.pageQueryGkqk(params.getCondition(), params);
        //将课件章节数和总时长放入数据list
      	for(Map<String, Object> map:bizZypxKjxxjdList){
  			int kjsc = 0;
  			int gksc = 0;
  			if(BaseUtil.isNotEmpty(map.get("content"))) {
	  			String xmId = (String)map.get("xmId");
	  			String kjId = (String)map.get("id");
				String ksId = (String)map.get("studentId");
				String content = map.get("content").toString();
				map.remove("content");
				Courselearn courselearn = ModelHelper.convertObject(content);
				if(courselearn == null) {
					continue;
				}
	  			List<Chapter> chapters = courselearn.getChapters();
	  			if(BaseUtil.isNotEmpty(chapters)){
	  				for(Chapter chapter:chapters) {
	  					//章节id
	  					String zjId = chapter.getId();
	  					List<Lesson> lessons = chapter.getLessons();
	  					for(Lesson lesson:lessons) {
	  						kjsc += lesson.getMinutes(); //总时长
	  						//课时id
	  						String keshiId = lesson.getId();
	  						EntityWrapper<CoursewareLearningProgress> bizZypxKjxxjdWrapper = new EntityWrapper<>();
	  						bizZypxKjxxjdWrapper.eq("xm_id", xmId);
	  						bizZypxKjxxjdWrapper.eq("kj_id", kjId);
	  						bizZypxKjxxjdWrapper.eq("student_id", ksId);
	  						bizZypxKjxxjdWrapper.eq("chapter_id", zjId);
	  						bizZypxKjxxjdWrapper.eq("lesson_id", keshiId);
	  						List<CoursewareLearningProgress> bizKjxxjds = mapper.selectList(bizZypxKjxxjdWrapper);
	  						CoursewareLearningProgress bizZypxKjxxjd = bizKjxxjds.size() > 0 ? bizKjxxjds.get(0) : null;
	  						gksc += bizZypxKjxxjd != null ? bizZypxKjxxjd.getDuration() : 0;
	  					}
	  				}
	  			}
	  			//总课件时长
	  			map.put("kjsc", kjsc);
	  			//总观看时长
	  			map.put("gksc", gksc);
  			}
      	}
      	params.setRecords(bizZypxKjxxjdList);
		return params;
	}
	
	public void xsgkDc(List<Map<String, Object>> dataList, OutputStream os) throws Exception {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("课件学习学生观看情况", 0);
		
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "课程", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "培训机构", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 25);
			ws.addCell(new Label(i, row, "课件总时长（分钟）", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "观看时长（分钟）", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "总学时", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "已完成学时", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
		}
		row = 1;
		for (Map<String, Object> data : dataList) {
			int col = 0;
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("serialNumber")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("title")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("courseCode") != null?data.get("courseCode"):"" 
					+"-"+data.get("courseName")!= null?data.get("courseName"):""), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("studentName")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("studentSfzh")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("orgCode") != null?data.get("orgCode"):"" +
					"-"+data.get("orgName")!= null?data.get("orgName"):""), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("kjsc")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("gksc")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("totalHours")), OfficeToolExcel.getNormolCell()));
			ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(data.get("finishedHours")), OfficeToolExcel.getNormolCell()));
			row++;
		}
		wwb.write();
		wwb.close();
		
		os.flush();
	}
	
	public List<Map<String, Object>> gkqkDetail(String xmId, String kjId, String studentId) {
		List<Map<String, Object>> gkqkList = new ArrayList<Map<String, Object>>();
		Courseware courseware = coursewareMapper.selectById(kjId);
		if(BaseUtil.isNotEmpty(courseware.getContent())) {
			Courselearn courselearn = ModelHelper.convertObject(courseware.getContent());
			if(courselearn == null) {
				throw BizException.withMessage("课件缺失内容");
			}
			List<Chapter> chapters = courselearn.getChapters();
			if(BaseUtil.isNotEmpty(chapters)){
				for(Chapter chapter:chapters) {
					List<Lesson> lessons = chapter.getLessons();
					for(Lesson lesson:lessons) {
						EntityWrapper<CoursewareLearningProgress> wrapper = new EntityWrapper<>();
						wrapper.eq("xm_id", xmId);
						wrapper.eq("kj_id", kjId);
						wrapper.eq("student_id", studentId);
						wrapper.eq("chapter_id", chapter.getId());
						wrapper.eq("lesson_id", lesson.getId());
						List<CoursewareLearningProgress> bizKjxxjdList = mapper.selectList(wrapper);
						CoursewareLearningProgress bizZypxKjxxjd = bizKjxxjdList.size() > 0 ? bizKjxxjdList.get(0) : null;
						Map<String, Object> gkqkMap = new HashMap<String, Object>();
						gkqkMap.put("chapterName", chapter.getName());
						gkqkMap.put("lessonName", lesson.getName());
						gkqkMap.put("sc", lesson.getMinutes());
						gkqkMap.put("gksc", bizZypxKjxxjd != null ? bizZypxKjxxjd.getDuration() : 0);
						gkqkMap.put("progressId", bizZypxKjxxjd != null ? bizZypxKjxxjd.getId() : "");
						gkqkList.add(gkqkMap);
					}
				}
			}
		}
        return gkqkList;
	}
	
	@Transactional
	public void sendSms(String bmCourseIds) {
		String[] array =  StringUtils.split(bmCourseIds, ",");
		List<Map<String, Object>> list = bmCourseMapper.getBmInfoByBmCourseId(new ArrayList<String>(Arrays.asList(array)));
		for (Map<String, Object> bmInfoMap : list) {
			String studentName = BaseUtil.getStringValueFromMap(bmInfoMap, "studentName", "");
			String xmTitle = BaseUtil.getStringValueFromMap(bmInfoMap, "title", "");
			String courseName = BaseUtil.getStringValueFromMap(bmInfoMap, "courseName", "");
			String smsSign = BaseUtil.getStringValueFromMap(bmInfoMap, "smsSign", "");
			String mobile = BaseUtil.getStringValueFromMap(bmInfoMap, "mobile", "");
			//获取培训项目所属的主办单位
			smsSevice.sendByTemplate(smsSign, "214895", mobile,new String[] {studentName,xmTitle,courseName} );
		}
	}
}
