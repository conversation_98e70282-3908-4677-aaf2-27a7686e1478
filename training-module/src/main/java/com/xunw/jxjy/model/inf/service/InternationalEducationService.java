package com.xunw.jxjy.model.inf.service;

import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.inf.entity.InternationalEducation;
import com.xunw.jxjy.model.inf.mapper.InternationalEducationMapper;
import com.xunw.jxjy.model.inf.params.InternationalEducationQueryParams;
import org.springframework.stereotype.Service;

@Service
public class InternationalEducationService extends BaseCRUDService<InternationalEducationMapper, InternationalEducation>{

	public Object pageQuery(InternationalEducationQueryParams params) {
		params.setRecords(mapper.list(params.getCondition(), params));
		return params;
	}
}
