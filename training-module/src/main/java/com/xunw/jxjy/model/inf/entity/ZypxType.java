package com.xunw.jxjy.model.inf.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.TypeCategory;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.sys.entity.PortalXm;


/**
 * 职业培训类型表
 * <AUTHOR>
 *
 */
@TableName("inf_type")
public class ZypxType implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -354116107759164784L;

	//主键id 也是类型代码具有层级关系
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;

	//类型名称
	@TableField("name")
	private String name;

	//父类型ID
	@TableField("parent_id")
	private String parentId;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//修改用户id
	@TableField("updator_id")
	private String updatorId;

	//修改时间
	@TableField("update_time")
	private Date updateTime;

	//排序
	@TableField("sort")
	private Integer sort;

	@TableField("category")
	private TypeCategory category;

	//主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;

	//小程序排序
	@TableField("applet_sort")
	private Integer appletSort;

	//状态
	@TableField("status")
	private Zt status;

	//项目集合
	@TableField(exist = false)
	private List<Map<String, Object>> portalXmList = new ArrayList();

	//项目集合
	@TableField(exist = false)
	private List<ZypxType> childTypes = new ArrayList();

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public TypeCategory getCategory() {
		return category;
	}

	public void setCategory(TypeCategory category) {
		this.category = category;
	}

	public List<Map<String, Object>> getPortalXmList() {
		return portalXmList;
	}

	public void setPortalXmList(List<Map<String, Object>> portalXmList) {
		this.portalXmList = portalXmList;
	}

	public Integer getAppletSort() {
		return appletSort;
	}

	public void setAppletSort(Integer appletSort) {
		this.appletSort = appletSort;
	}

	public Zt getStatus() {
		return status;
	}

	public void setStatus(Zt status) {
		this.status = status;
	}

	public List<ZypxType> getChildTypes() {
		return childTypes;
	}

	public void setChildTypes(List<ZypxType> childTypes) {
		this.childTypes = childTypes;
	}
}
