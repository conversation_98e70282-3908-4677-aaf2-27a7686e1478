package com.xunw.jxjy.model.htgl.mobile.service;

import com.baomidou.mybatisplus.toolkit.CollectionUtils;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.enums.BuyType;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.htgl.mobile.mapper.HtglMobileMapper;
import com.xunw.jxjy.model.htgl.mobile.params.SkillListParams;
import com.xunw.jxjy.model.htgl.mobile.vo.PlanDetailVO;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmScopeParams;
import com.xunw.jxjy.model.zypx.entity.PlanDetail;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.service.PlanDetailService;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import com.xunw.jxjy.model.zypx.service.ZypxXmService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-18 14:16
 */
@Service
public class AuditService {
    @Autowired
    private PlanDetailService planDetailService;
    @Autowired
    private HtglMobileMapper htglMobileMapper;
    @Autowired
    private OrgService orgService;
    @Autowired
    private ZyjdBmScopeMapper bmScopeMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ZypxXmCourseSettingService zypxXmCourseSettingService;
    @Autowired
    private ZypxXmService zypxXmService;
    
    /**
     * 审核列表
     */
    public List<Map<String, Object>> auditList(String loginUserId, String hostOrgId, String curRole) {
        List<Map<String, Object>> list = htglMobileMapper.queryPlanDetail(loginUserId, hostOrgId, curRole);
        // 只有主办单位才查询
        if (StringUtils.equals(curRole, Role.HOST_ORG.name())) {
            list.addAll(htglMobileMapper.queryPlanContract(hostOrgId));
            list.addAll(htglMobileMapper.queryPlanPaper(hostOrgId));
            List<Map<String, Object>> bmBatchList = htglMobileMapper.queryBmBatch(hostOrgId);
            for (Map<String, Object> map : bmBatchList) {
                String techLevel = BaseUtil.getStringValueFromMap(map, "techLevel");
                String[] array = StringUtils.split(techLevel, ",");
                if (array != null) {
                    List<String> techLevels = Arrays.stream(array).map(TechLevel::valueOf).sorted(Comparator.comparing(TechLevel::getId)).
                            map(TechLevel::getName).collect(Collectors.toList());
                    map.put("profession", map.get("professionName") + "【" + StringUtils.join(techLevels, ",") + "】");
                } else {
                    map.put("profession", map.get("professionName"));
                }
            }
            list.addAll(bmBatchList);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //  按时间排序正序 提交时间为空排到后面
        return list.stream().sorted(Comparator.comparing(o -> o.get("submitTime") != null ? (Date) o.get("submitTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList());
    }


    /**
     * 审核详情
     */
    public Map<String, Object> auditDetail(String id) {
        Map<String, Object> result = new HashMap<String, Object>();
        ZypxXm xm = zypxXmService.selectById(id);
        PlanDetail planDetail = planDetailService.selectById(id);
        // 项目信息vo转换
        PlanDetailVO planDetailVO = new PlanDetailVO();
        planDetailVO.setName(planDetail.getName());
        planDetailVO.setSubmitTime(planDetail.getSubmitTime());
        planDetailVO.setSerialNumber(planDetail.getSerialNumber());
        planDetailVO.setTrainingForm(planDetail.getTrainingForm());
        planDetailVO.setStartTime(xm.getStartTime());
        planDetailVO.setEndTime(xm.getEndTime());
        planDetailVO.setIsOrgPay(planDetail.getIsOrgPay());
        planDetailVO.setAmount(planDetail.getAmount());
        planDetailVO.setUnits(planDetail.getUnits());
        planDetailVO.setIsIssueCertificate(planDetail.getIsIssueCertificate());
        planDetailVO.setHours(planDetail.getHours());
        planDetailVO.setPyfa(planDetail.getPyfa());
        planDetailVO.setContract(planDetail.getContract());
        planDetailVO.setPaper(planDetail.getPaper());
        planDetailVO.setTrainees(planDetail.getTrainees());
        planDetailVO.setCountData(planDetail.getCountData());
        planDetailVO.setRemark(planDetail.getRemark());
        planDetailVO.setFeeStandard(planDetail.getFeeStandard());
        planDetailVO.setBuyType(planDetail.getBuyType() != null ? planDetail.getBuyType().getName() : "");
        planDetailVO.setSource(planDetail.getXmSources() != null ? planDetail.getXmSources().getName() : "");
        planDetailVO.setClasszType(planDetail.getClasszType());
        planDetailVO.setIndustryCategory(planDetail.getIndustryCategory());
        planDetailVO.setSupportAmount(planDetail.getSupportAmount());
        planDetailVO.setBaseId(planDetail.getBaseId());
        planDetailVO.setSurplus(planDetail.getSurplus());
        planDetailVO.setTotalCost(planDetail.getTotalCost());
        planDetailVO.setIncomeAmount(xm.getIncomeAmount());
        if (StringUtils.isNotEmpty(planDetail.getUserId())) {
            User user = userMapper.selectById(planDetail.getUserId());
            if(user != null) {
            	planDetailVO.setCreateUserName(user.getName());
            }
        }
        if (StringUtils.isNotEmpty(planDetail.getReceiveOrgId())) {
            Org org = orgService.selectById(planDetail.getReceiveOrgId());
            if(org != null) {
            	planDetailVO.setReceiveOrgName(org.getName());
            }
        }
        // 立项表和合同 项目分类
        planDetailVO.setTypeName(htglMobileMapper.selectTypeNameByPlanId(planDetail.getPlanId()));
        result.put("planDetail", planDetailVO);
        // 项目按课程购买展示课程
        if (planDetail.getBuyType() == BuyType.COURSE) {
            result.put("courses", zypxXmCourseSettingService.getAllCourseByXmId(id));
        }
        result.put("audit", planDetailService.getPlanDetailApproveLog(id));//审批日志
        return result;
    }

    /**
     * 职业技能详情
     */
    public Map<String, Object> skillsDetail(String id) {
        Map<String, Object> result = htglMobileMapper.selectApproveByBatchId(id);
        ZyjdBmScopeParams scopeParams = new ZyjdBmScopeParams();
        scopeParams.setBmbatchId(id);
        scopeParams.setSize(Integer.MAX_VALUE);
        List<Map<String, Object>> scopeList = bmScopeMapper.list(scopeParams.getCondition(), scopeParams);
        for (Map<String, Object> scope : scopeList) {
            String techLevel = BaseUtil.getStringValueFromMap(scope, "techLevel");
            String[] array = StringUtils.split(techLevel, ",");
            List<String> nameList = new ArrayList<>();
            for (String vstr : array) {
                TechLevel sbjsdj = TechLevel.findByEnumName(vstr);
                if (sbjsdj != null) {
                    nameList.add(sbjsdj.getName());
                }
            }
            scope.put("techLevelName", StringUtils.join(nameList, ","));
        }
        result.put("scopes", scopeList);
        return result;
    }

    /**
     * 职业技能列表年度信息
     */
    public List<String> getYears() {
        return htglMobileMapper.getYears();
    }

    /**
     * 职业技能列表
     */
    public Map<String, Object> skillsList(SkillListParams params) {
        List<Map<String, Object>> list = htglMobileMapper.skillsList(params.getCondition());
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("notStartList", list.stream().filter(i -> i.get("startTime") == null || new Date().before((Date) i.get("startTime")))
                .sorted(Comparator.comparing(o -> o.get("createTime") != null ? (Date) o.get("createTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
        result.put("progressingList", list.stream().filter(i -> i.get("startTime") != null && i.get("endTime") != null && new Date().after((Date) i.get("startTime")) && new Date().before((Date) i.get("endTime")))
                .sorted(Comparator.comparing(o -> o.get("createTime") != null ? (Date) o.get("createTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
        result.put("endList", list.stream().filter(i -> i.get("endTime") != null && new Date().after((Date) i.get("endTime")))
                .sorted(Comparator.comparing(o -> o.get("createTime") != null ? (Date) o.get("createTime") : DateUtils.addYear(new Date(), 1))).collect(Collectors.toList()));
        return result;
    }

    /**
     * 我审批的
     */
    public List<Map<String, Object>> startList(String userId) {
        return htglMobileMapper.startList(userId);
    }
}
