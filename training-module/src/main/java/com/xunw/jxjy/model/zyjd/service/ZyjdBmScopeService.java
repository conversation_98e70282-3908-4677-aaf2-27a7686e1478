package com.xunw.jxjy.model.zyjd.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBmScope;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper;
import com.xunw.jxjy.model.zyjd.params.ZyjdBmScopeParams;
import jxl.read.biff.BiffException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ZyjdBmScopeService extends BaseCRUDService<ZyjdBmScopeMapper, ZyjdBmScope>{

	@Autowired
	private ZyjdProfessionMapper zyjdProfessionMapper;
	@Autowired
	private AttConfig attConfig;
    @Autowired
    private ZyjdBmScopeMapper zyjdBmScopeMapper;

	/**
	 * 列表查询
	 */
	public Page query(ZyjdBmScopeParams params) {
		List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
		for (Map<String, Object> map : list) {
			String techLevel = BaseUtil.getStringValueFromMap(map, "techLevel");
			String[] array = StringUtils.split(techLevel, ",");
			if (array != null) {
				List<String> techLevels = Arrays.stream(array).map(x->TechLevel.valueOf(x)).sorted(Comparator.comparing(TechLevel::getId)).
						map(y->y.getName()).collect(Collectors.toList());
				map.put("tech_level", StringUtils.join(techLevels, ","));
			}
			if (array != null) {
				List<String> techLevels = Arrays.stream(array).map(x->TechLevel.valueOf(x)).sorted(Comparator.comparing(TechLevel::getId)).
						map(y->y.getDesc()).collect(Collectors.toList());
				map.put("tech_level_desc", StringUtils.join(techLevels, ","));
			}
		}
		params.setRecords(list);
		return params;
	}
	
	//校验申报的等级是否已授权
    public Integer checkLevel(String hostOrgId, String professionId, String techLevel) {
		return mapper.checkLevel(hostOrgId, professionId, Arrays.asList(techLevel.split(",")));
	}

	@Transactional
    public void add(JSONObject json, String hostOrgId, String userId) {
		String bmbatchId = json.getString("bmbatchId");
		String industryId = json.getString("industryId");
		JSONArray professions = json.getJSONArray("professions");
		List<ZyjdBmScope> zyjdBmScopeList = new ArrayList<>();
		for (Object profession : professions) {
			JSONObject jsonObject = (JSONObject) profession;
			String professionId = jsonObject.getString("professionId");
			String techLevel = jsonObject.getString("techLevel");
			if (StringUtils.isEmpty(professionId) || StringUtils.isEmpty(techLevel)) {
				continue;
			}
			EntityWrapper<ZyjdBmScope> wrapper = new EntityWrapper<ZyjdBmScope>();
			wrapper.eq("bmbatch_id", bmbatchId);
			wrapper.eq("industry_id", industryId);
			wrapper.eq("profession_id", professionId);
			List<ZyjdBmScope> list = mapper.selectList(wrapper);
			if (list.size() > 0) {
				throw BizException.withMessage("同一个批次下职业不可重复,请勿重复添加");
			}
			ZyjdProfession zyjdProfession = zyjdProfessionMapper.selectById(professionId);
			//校验申报的等级是否已授权
			Integer count = mapper.checkLevel(hostOrgId, professionId, Arrays.asList(techLevel.split(",")));
			if (count == 0){
				throw BizException.withMessage("无权限申报："+zyjdProfession.getCode()+"-"+zyjdProfession.getName()+"，等级:" + techLevel);
			}
			ZyjdBmScope zyjdBmScope = new ZyjdBmScope();
			zyjdBmScope.setId(BaseUtil.generateId2());
			zyjdBmScope.setBmbatchId(bmbatchId);
			zyjdBmScope.setIndustryId(industryId);
			zyjdBmScope.setProfessionId(professionId);
			zyjdBmScope.setTechLevel(techLevel);
			zyjdBmScope.setCreateTime(new Date());
			zyjdBmScope.setCreatorId(userId);
			zyjdBmScopeList.add(zyjdBmScope);
		}
		DBUtils.insertBatch(zyjdBmScopeList, ZyjdBmScope.class);
    }

	public void edit(String id, String bmbatchId, String industryId, String professionId, String techLevel, String hostOrgId) {
		EntityWrapper<ZyjdBmScope> wrapper = new EntityWrapper<ZyjdBmScope>();
		wrapper.eq("bmbatch_id", bmbatchId);
		wrapper.eq("industry_id", industryId);
		wrapper.eq("profession_id", professionId);
		List<ZyjdBmScope> list = mapper.selectList(wrapper);
		if (list.size() > 0 && !list.get(0).getId().equals(id)) {
			throw BizException.withMessage("同一个批次下工种不可重复,请勿重复添加");
		}
		ZyjdProfession zyjdProfession = zyjdProfessionMapper.selectById(professionId);
		//校验申报的等级是否已授权
		Integer count = mapper.checkLevel(hostOrgId, professionId, Arrays.asList(techLevel.split(",")));
		if (count == 0){
			throw BizException.withMessage("无权限申报："+zyjdProfession.getCode()+"-"+zyjdProfession.getName()+"，等级:" + techLevel);
		}
		ZyjdBmScope zyjdBmScope = mapper.selectById(id);
		zyjdBmScope.setBmbatchId(bmbatchId);
		zyjdBmScope.setIndustryId(industryId);
		zyjdBmScope.setProfessionId(professionId);
		zyjdBmScope.setTechLevel(techLevel);
		mapper.updateById(zyjdBmScope);
	}

	public Map<String, Object> batchImport(String bmbatchId, MultipartFile file, String userId) throws IOException, BiffException {
		if (file == null) {
			throw BizException.withMessage("文件为空");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdirFile = new File(attConfig.getTempdir());
		File tmpFile = null;
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		//必须指定附件的临时目录
		tmpFile = File.createTempFile("TEMPFILE" + UUID.randomUUID(), ".tmp", tempdirFile);
		file.transferTo(tmpFile);
		//
		list = OfficeToolExcel.readExcel(tmpFile, new String[]{"industry", "professionCode", "techLevel", "maxPersonNum"});
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		StringBuilder error = new StringBuilder();
		Integer cf = 0;//重复
		Integer cw = 0;//错误
		Integer cg = 0;//成功
		Integer row = 0;//行数
		List<ZyjdBmScope> scopes = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			row = i + 1;
 			Map<String, String> map = list.get(i);
			//跳过第一行
			if (row == 1) {
				continue;
			}
			String industry = BaseUtil.getStringValueFromMap(map, "industry");
			if (industry == null) {
				error.append("<br>");
				error.append("第"+row+"行，工种分类不能为空，忽略这条数据");
				cw++;
				continue;
			}
			if (!"TY".equals(industry) && !"DL".equals(industry)) {
				error.append("<br>");
				error.append("第"+row+"行，工种分类异常无法识别，忽略这条数据");
				cw++;
				continue;
			}
			String professionCode = BaseUtil.getStringValueFromMap(map, "professionCode");
			if (professionCode == null) {
				error.append("<br>");
				error.append("第"+row+"行，工种代码不能为空，忽略这条数据");
				cw++;
				continue;
			}
			List<ZyjdProfession> professions = zyjdProfessionMapper.selectList(new EntityWrapper<ZyjdProfession>().eq("code", professionCode));
			if (CollectionUtils.isEmpty(professions)) {
				error.append("<br>");
				error.append("第"+row+"行，工种代码异常，无法识别，忽略这条数据");
				cw++;
				continue;
			}
			String professionId = professions.get(0).getId();
			if (zyjdProfessionMapper.selectCount(new EntityWrapper<ZyjdProfession>().eq("industry_id", industry)) == 0) {
				error.append("<br>");
				error.append("第"+row+"行，工种分类下无该工种，忽略这条数据");
				cw++;
				continue;
			}
			String techLevel = BaseUtil.getStringValueFromMap(map, "techLevel");
			if (techLevel == null) {
				error.append("<br>");
				error.append("第"+row+"行，级别不能为空，忽略这条数据");
				cw++;
				continue;
			}
			TechLevel level = TechLevel.findByEnumName(techLevel);
			if (level == null) {
				error.append("<br>");
				error.append("第"+row+"行，级别异常，无法识别，忽略这条数据");
				cw++;
				continue;
			}
			String maxPersonNum = BaseUtil.getStringValueFromMap(map, "maxPersonNum");
			if (StringUtils.isEmpty(maxPersonNum)) {
				error.append("<br>");
				error.append("第"+row+"行，人数上限不能为空，忽略这条数据");
				cw++;
				continue;
			}
			if (!StringUtils.isNumeric(maxPersonNum) || Integer.parseInt(maxPersonNum) <= 0) {
				error.append("<br>");
				error.append("第"+row+"行，人数上限格式错误，必须是大于0的整数，忽略这条数据");
				cw++;
				continue;
			}
			Integer count = mapper.selectCount(new EntityWrapper<ZyjdBmScope>()
					.eq("bmbatch_id", bmbatchId)
					.eq("profession_id", professionId)
					.eq("industry_id", industry));
			if (count>0) {
				error.append("<br>");
				error.append("第"+row+"行，同批次同工种类型同工种的报名范围已经存在，忽略这条数据");
				cf++;
				continue;
			}
			ZyjdBmScope zyjdBmScope = new ZyjdBmScope();
			zyjdBmScope.setId(BaseUtil.generateId2());
			zyjdBmScope.setStatus(Zt.BLOCK);
			zyjdBmScope.setIndustryId(industry);
			zyjdBmScope.setBmbatchId(bmbatchId);
			zyjdBmScope.setProfessionId(professionId);
			zyjdBmScope.setTechLevel(techLevel);
			zyjdBmScope.setMaxPersonNum(Integer.parseInt(maxPersonNum));
			zyjdBmScope.setCreatorId(userId);
			zyjdBmScope.setCreateTime(new Date());
			zyjdBmScopeMapper.insert(zyjdBmScope);
			cg++;
		}
		StringBuilder result = new StringBuilder();
		result.append("导入成功，共" + (list.size() - 1) + "条，导入成功"+cg+"条，导入失败"+ (list.size()-1-cg) +"条，已存在"+cf+"条。");
		result.append(error.toString());
		Map<String, Object> map = new HashMap<>();
		map.put("code", "0");
		map.put("message", result.toString());
		return map;
	}

	public Map<String, Object> getDetailById(String id) {
		return mapper.getDetailById(id);
	}
}
