package com.xunw.jxjy.model.sys.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.dto.OrgTree;
import com.xunw.jxjy.model.enums.OrgType;
import com.xunw.jxjy.model.enums.Role;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.OrgRelation;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.mapper.OrgMapper;
import com.xunw.jxjy.model.sys.mapper.OrgRelationMapper;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.sys.params.OrgQueryParams;

@Service
public class OrgService extends BaseCRUDService<OrgMapper, Org>{

	@Autowired
	private UserMapper userMapper;
	@Autowired
	private StudentUserMapper studentUserMapper;
	@Autowired
	private OrgRelationMapper orgRelationMapper;
	
	public Page list(OrgQueryParams params) {
		params.setRecords(mapper.list(params.getCondition(), params));
		return params;
	}
	
	public List<OrgTree> tree(OrgQueryParams params) {
		List<Org> orgs = mapper.tree(params.getCondition());
		return this.buildOrgTree(orgs);
	}
	
	public List<OrgTree> orgUserTree(OrgQueryParams params) {
		List<Org> orgs = mapper.tree(params.getCondition());
		return this.buildOrgUserTree(orgs, params.getRole());
	}
	
	@Transactional
	public void add(Org org, OrgRelation orgRelation) {
		mapper.insert(org);
		if (orgRelation != null) {
			orgRelationMapper.insert(orgRelation);
		}
	}

	@Transactional
	public void batchDelete(String ids, String yhId) {
		List<Org> batch = new ArrayList<>();
		for (String id : ids.split(",")) {
			Org sysJg = mapper.selectById(id);
			if (sysJg == null) {
				throw BizException.withMessage("ID:" + id + "在系统中不存在");
			}
			sysJg.setStatus(Zt.BLOCK);
			sysJg.setUpdatorId(yhId);
			sysJg.setUpdateTime(new Date());
			batch.add(sysJg);
		}
		if (batch.size() > 0) {
			DBUtils.updateBatchById(batch, 20, Org.class);
		}
	}
	
	public boolean validCodeExists(String code) {
		EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
		wrapper.eq("code", code);
		return mapper.selectCount(wrapper) > 0;
	}

	public Org findByCode(String code) {
		EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
		wrapper.eq("code", code);
		List<Org> list = mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}
	
	public Org findByName(String name) {
		EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
		wrapper.eq("name", name);
		List<Org> list = mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
	}
	
	public Org getOrgByHostOrgIdAndName(String hostOrgId, String name) {
		List<Org> orgs = mapper.getOrgByHostOrgIdAndName(hostOrgId, name);
		return orgs.size() > 0 ? orgs.get(0) : null;
	}
	
	//查询直接子部门，非递归查询
	public List<Org> getChildren(String parentId){
		EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
		wrapper.eq("parent_id",parentId);
		wrapper.orderBy("code", true);
		return  mapper.selectList(wrapper);
	}
	
	//递归查询子部门，包括自己
	public List<Org> getChildrenByPrior(String parentId){
		return  mapper.getChildrenByPrior(parentId);
	}
	
	//递归查询父部门，包括自己
	public List<Org> getParentByPrior(String orgId){
		return  mapper.getParentByPrior(orgId);
	}

	
	public List<User> getSysUser(String id){
		EntityWrapper<User> wrapper = new EntityWrapper<User>();
		wrapper.eq("org_id",id);
		return  userMapper.selectList(wrapper);
	}
	
	public List<StudentUser> getStudentUser(String id){
		EntityWrapper<StudentUser> wrapper = new EntityWrapper<StudentUser>();
		wrapper.eq("org_id",id);
		return  studentUserMapper.selectList(wrapper);
	}
	
	/**
	 * 培训机构、委托单位 查询关联的主办单位，注意参数orgId 必须是顶级机构 --tianjun
	 */
	public List<Org> getRelatedHostOrgByOrgId(String orgId){
		return mapper.getRelatedHostOrgByOrgId(orgId);
	}
	
	/**
	 *主办单位查询合作的培训机构 注意参数hostOrgId 必须是顶级机构  --tianjun
	 */
	public List<Org> getRelatedOrgByHostOrgId(String hostOrgId){
		return mapper.getRelatedOrgByHostOrgId(hostOrgId);
	}
	
	/**
	 *主办单位查询委托单位 注意参数hostOrgId 必须是顶级机构  --tianjun
	 */
	public List<Org> getRelatedEntrustOrgByHostOrgId(String hostOrgId){
		return mapper.getRelatedEntrustOrgByHostOrgId(hostOrgId);
	}
	
	/**
	 * 查询用户的主办单位，主办单位用户取所属主办单位的ID，非主办单位用户取关联的主办单位ID
	 */
	public Org getHostOrgByUserId(String userId) {
		User user = userMapper.selectById(userId);
		if (user == null || StringUtils.isEmpty(user.getOrgId())) {
			return null;
		}
		Org org = mapper.selectById(user.getOrgId());
		if (org == null) {
			return null;
		}
		if (org.getOrgType() == OrgType.HOST_ORG) {
			Org topOrg = this.getTopOrg(org.getId());
			return topOrg;
		}
		else {
			Org topOrg = this.getTopOrg(org.getId());
			List<Org> orgList = this.getRelatedHostOrgByOrgId(topOrg.getId());
			if (orgList.size() == 0) {
				throw BizException.withMessage("系统数据错误，当前机构："+topOrg.getName()+"没有设置主办单位");
			}
			else {
				return orgList.get(0);
			}
		}
	}
	
	/**
	 * 组装机构树 --tianjun
	 */
	public List<OrgTree> buildOrgTree(List<Org> orgs) {
		Map<String, OrgTree> map = new LinkedHashMap<String, OrgTree>();
		for (Org org : orgs) {
			OrgTree orgTree = new OrgTree();
			orgTree.setId(org.getId());
			orgTree.setName(org.getName());
			orgTree.setType(org.getOrgType());
			orgTree.setParentId(org.getParentId());
			map.put(org.getId(), orgTree);
		}
		List<String> deleteIds = new ArrayList<String>();
		for(String key : map.keySet()) {
			OrgTree orgTree = map.get(key);
			if(StringUtils.isNotEmpty(orgTree.getParentId())) {
				OrgTree parent = map.get(orgTree.getParentId());
				if(parent != null) {
					parent.getChildren().add(orgTree);
					deleteIds.add(orgTree.getId());
				}
			}
		}
		for (String key : deleteIds) {
			map.remove(key);
		}
		List<OrgTree> result = new ArrayList<OrgTree>();
		for (String key : map.keySet()) {
			result.add(map.get(key));
		}
		return result;
	}

	public List<OrgTree> buildOrgUserTree(List<Org> orgs, Role role) {
		Map<String, OrgTree> map = new LinkedHashMap<String, OrgTree>();
		for (Org org : orgs) {
			OrgTree orgTree = new OrgTree();
			orgTree.setId(org.getId());
			orgTree.setName(org.getName());
			orgTree.setType(org.getOrgType());
			orgTree.setParentId(org.getParentId());
			map.put(org.getId(), orgTree);
		}
		List<String> deleteIds = new ArrayList<String>();
		for(String key : map.keySet()) {
			OrgTree orgTree = map.get(key);
			List<User> userList= userMapper.getUserByOrgIdAndRole(key, role);
			if(userList.size()>0) {
				for (User u : userList) {
					OrgTree deptTree = new OrgTree();
					deptTree.setId(u.getId());
					deptTree.setName(u.getName());
					orgTree.getChildren().add(deptTree);
				}
			}
			if(StringUtils.isNotEmpty(orgTree.getParentId())) {
				OrgTree parent = map.get(orgTree.getParentId());
				if(parent != null) {
					parent.getChildren().add(orgTree);
					deleteIds.add(orgTree.getId());
				}
			}
		}
		for (String key : deleteIds) {
			map.remove(key);
		}
		List<OrgTree> result = new ArrayList<OrgTree>();
		for (String key : map.keySet()) {
			result.add(map.get(key));
		}
		return result;
	}
	
	/**
	 * 根据域名查询主办单位，一个域名只会被一个主办单位使用
	 */
	public Org getHostOrgByDomain(String domain) {
		return mapper.getHostOrgByDomain(domain);
	}
	
	/**
	 * 查询机构的根节点
	 */
	public Org getTopOrg(String orgId) {
		return mapper.getTopOrg(orgId);
	}
	
	/**
	 * 获取所有的主办单位
	 */
	public List<Org> getAllHostOrg(){
		EntityWrapper<Org> wrapper = new EntityWrapper<>();
		wrapper.eq("org_type", OrgType.HOST_ORG);
		wrapper.eq("status", Zt.OK);
		wrapper.eq("is_parent", Constants.YES);
		wrapper.orderBy("create_time", false);
		List<Org> hostOrgs = mapper.selectList(wrapper);
		return hostOrgs;
	}
	
}
