package com.xunw.jxjy.model.zypx.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.zypx.dto.XmCourseSettingItem;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;

/**
 * <AUTHOR>
 * 职业培训-项目课程设置
 */
public interface ZypxXmCourseSettingMapper extends BaseMapper<ZypxXmCourseSetting> {
	
    List<XmCourseSettingItem> query(Map<String, Object> condition);
    
    List<Map<String,Object>> getAllCourseByXmId(@Param("xmId")String xmId);
    
    Course getCourseByCourseSettingId(@Param("courseSettingId") String courseSettingId);

    Map<String, Object> getLearnContentById(String id);

    List<Map<String, Object>> doTargetCourseList(@Param(value = "studentId") String studentId, @Param(value = "xmId") String xmId);

    List<Map<String, Object>> getXmLinkmanByXmId(@Param(value = "id") String id, @Param(value = "keyword") String keyword);
}
