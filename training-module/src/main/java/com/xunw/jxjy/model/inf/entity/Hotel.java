package com.xunw.jxjy.model.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.zypx.dto.HotelRoomTypeDTO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 合作宾馆信息表
 * <AUTHOR>
 * @createTime 2021年3月29日 14:29:00
 */
@TableName("inf_hotel")
public class Hotel implements Serializable {

	private static final long serialVersionUID = -5195825988283306058L;

	//主键id
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	//宾馆编号
	@TableField("serial_number")
	private String serialNumber;

	//宾馆名称
	@TableField("name")
	private String name;
	//楼层数
	@TableField("lc_count")
	private Integer lcCount;
	//容纳人数
	@TableField("total_count")
	private Integer totalCount;

	//联系人
	@TableField("people")
	private String people;
	//联系电话
	@TableField("phone")
	private String phone;

	//宾馆地点
	@TableField("address")
	private String address;

	//宾馆简介
	@TableField("remark")
	private String remark;

	//高德地图经纬度坐标
	@TableField("map_position")
	private String mapPosition;

	//线路说明
	@TableField("address_remak")
	private String addressRemak;

	//主办单位id
	@TableField("host_org_id")
	private String hostOrgId;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//图片链接
	@TableField("img_url")
	private String imgUrl;

	//房间类型
	@TableField("room_types")
	private String roomTypes;

	//房间类型 集合
    @TableField(exist = false)
	private List<HotelRoomTypeDTO> roomTypeList = new ArrayList();

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getLcCount() {
		return lcCount;
	}

	public void setLcCount(Integer lcCount) {
		this.lcCount = lcCount;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public String getPeople() {
		return people;
	}

	public void setPeople(String people) {
		this.people = people;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	public String getMapPosition() {
		return mapPosition;
	}

	public void setMapPosition(String mapPosition) {
		this.mapPosition = mapPosition;
	}

	public String getAddressRemak() {
		return addressRemak;
	}

	public void setAddressRemak(String addressRemak) {
		this.addressRemak = addressRemak;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getRoomTypes() {
		return roomTypes;
	}

	public void setRoomTypes(String roomTypes) {
		this.roomTypes = roomTypes;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public List<HotelRoomTypeDTO> getRoomTypeList() {
		return roomTypeList;
	}

	public void setRoomTypeList(List<HotelRoomTypeDTO> roomTypeList) {
		this.roomTypeList = roomTypeList;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
