package com.xunw.jxjy.model.zypx.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import com.xunw.jxjy.model.zypx.mapper.TargetTypeMapper;
import com.xunw.jxjy.model.zypx.params.TargetTypeQueryParams;

@Service
public class TargetTypeService extends BaseCRUDService<TargetTypeMapper, TargetType>{
	
	// 查询
	public Page pageQuery(TargetTypeQueryParams params) throws IOException, SQLException {
		List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

}
