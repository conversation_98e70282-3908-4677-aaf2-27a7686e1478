package com.xunw.jxjy.model.zyjd.params.validator;

import org.apache.commons.lang3.StringUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.CertiType;
import com.xunw.jxjy.model.zyjd.params.ZyjdStudentBmParams;

/**
 * 职业技能等级认定报名参数校验
 *
 */
public class ZyjdParamsValidtaor {
	
	public void validate(ZyjdStudentBmParams params) {
		if (StringUtils.isEmpty(params.getBmbatchId())) {
			throw BizException.withMessage("报名批次不能够为空");
		}
		if (StringUtils.isEmpty(params.getName())) {
			throw BizException.withMessage("请输入姓名");
		}
		if (params.getCertiType() == null) {
			throw BizException.withMessage("请输入证件类型");
		}
		if (StringUtils.isEmpty(params.getSfzh())) {
			throw BizException.withMessage("请输入证件号");
		}
		if (params.getEducation() == null) {
			throw BizException.withMessage("请输选择文化程度");
		}
		if (params.getKsly() == null) {
			throw BizException.withMessage("请选择考生来源");
		}
		if (StringUtils.isEmpty(params.getProfessionId())) {
			throw BizException.withMessage("请选择申报职业");
		}
		if (params.getApplyTechLevel() == null) {
			throw BizException.withMessage("请选择申报等级");
		}
		if (params.getCertiType() == CertiType.SFZ && StringUtils.isNotEmpty(params.getSfzh())
				&& !BaseUtil.isIDCardNumber(params.getSfzh())) {
			throw BizException.withMessage("请输入正确的身份证号");
		}
		if (StringUtils.isNotEmpty(params.getMobile()) && !BaseUtil.isMobile(params.getMobile())) {
			throw BizException.withMessage("请输入正确的11位手机号码");
		}
	}

}
