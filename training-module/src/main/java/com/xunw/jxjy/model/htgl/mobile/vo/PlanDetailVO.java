package com.xunw.jxjy.model.htgl.mobile.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-18 15:12
 */
public class PlanDetailVO {
    private String name;
    private Date submitTime;
    private String serialNumber;
    private String source;
    private String receiveOrgName;
    private String trainingForm;
    private Date startTime;
    private Date endTime;
    private String isOrgPay;
    private Double amount;
    private String units;
    private String isIssueCertificate;
    private Double hours;
    private String pyfa;
    private String contract;
    private String paper;
    private String typeName;
    private String createUserName;
    private String trainees;
    private String countData;
    private String remark;
    private String buyType;
    private Double feeStandard;
    private String industryCategory;
    private String classzType;
    private BigDecimal supportAmount;
    private String baseId;
    private Double surplus;
    private Double totalCost;
    private BigDecimal incomeAmount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getReceiveOrgName() {
        return receiveOrgName;
    }

    public void setReceiveOrgName(String receiveOrgName) {
        this.receiveOrgName = receiveOrgName;
    }

    public String getTrainingForm() {
        return trainingForm;
    }

    public void setTrainingForm(String trainingForm) {
        this.trainingForm = trainingForm;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getIsOrgPay() {
        return isOrgPay;
    }

    public void setIsOrgPay(String isOrgPay) {
        this.isOrgPay = isOrgPay;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getUnits() {
        return units;
    }

    public void setUnits(String units) {
        this.units = units;
    }

    public String getIsIssueCertificate() {
        return isIssueCertificate;
    }

    public void setIsIssueCertificate(String isIssueCertificate) {
        this.isIssueCertificate = isIssueCertificate;
    }

    public Double getHours() {
        return hours;
    }

    public void setHours(Double hours) {
        this.hours = hours;
    }

    public String getPyfa() {
        return pyfa;
    }

    public void setPyfa(String pyfa) {
        this.pyfa = pyfa;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getPaper() {
        return paper;
    }

    public void setPaper(String paper) {
        this.paper = paper;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getTrainees() {
        return trainees;
    }

    public void setTrainees(String trainees) {
        this.trainees = trainees;
    }

    public String getCountData() {
        return countData;
    }

    public void setCountData(String countData) {
        this.countData = countData;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBuyType() {
        return buyType;
    }

    public void setBuyType(String buyType) {
        this.buyType = buyType;
    }

    public Double getFeeStandard() {
        return feeStandard;
    }

    public void setFeeStandard(Double feeStandard) {
        this.feeStandard = feeStandard;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    public String getClasszType() {
        return classzType;
    }

    public void setClasszType(String classzType) {
        this.classzType = classzType;
    }

    public BigDecimal getSupportAmount() {
        return supportAmount;
    }

    public void setSupportAmount(BigDecimal supportAmount) {
        this.supportAmount = supportAmount;
    }

    public String getBaseId() {
        return baseId;
    }

    public void setBaseId(String baseId) {
        this.baseId = baseId;
    }

    public Double getSurplus() {
        return surplus;
    }

    public void setSurplus(Double surplus) {
        this.surplus = surplus;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }
}
