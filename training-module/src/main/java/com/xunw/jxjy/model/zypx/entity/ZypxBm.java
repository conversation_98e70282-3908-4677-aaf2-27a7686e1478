package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.ApproveResult;
import com.xunw.jxjy.model.enums.BmStatus;

import java.io.Serializable;
import java.util.Date;

/**
 * 职业培训报名表
 */
@TableName("biz_bm")
public class ZypxBm implements Serializable {

	private static final long serialVersionUID = -5249706086244048948L;

	@TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;
    
    //项目ID
    @TableField("xm_id")
    private String xmId;
    
    //考生id
    @TableField("student_id")
    private String studentId;
   
    //是否集体报名  1是  0否
    @TableField("is_jtbm")
    private String isJtbm;
    
    //集体报名用户id
    @TableField("jtbm_user_id")
    private String jtbmUserId;
    
    //集体报名机构id
    @TableField("jtbm_org_id")
    private String jtbmOrgId;
   
    //报名状态
    @TableField("status")
    private BmStatus status;
    
    //班级id
    @TableField("class_id")
    private String classId;
    
    //是否已经缴费  0未缴费  1已缴费 2已退费
    @TableField("is_payed")
    private String isPayed;
  
    //缴费方式 1 线上  0 线下
    @TableField("pay_type")
    private String payType;
    
    //线下标记缴费用户ID
    @TableField("mark_payed_user_id")
    private String markPayedUserId;
    
    //缴费金额
    @TableField("payed_amount")
    private Double payedAmount;
    
    //报名审核操作用户ID
    @TableField("approve_user_id")
    private String approveUserId;
    
    // 报名审核意见
 	@TableField("approve_advice")
 	private String approveAdvice;
 	
 	//证书审核操作用户ID
    @TableField("certi_approve_user_id")
    private String certiApproveUserId;
    
 	 //证书审核结果 枚举 通过 、不通过
    @TableField("certi_approve_result")
    private ApproveResult certiApproveResult;
    
    //证书审核意见
    @TableField("certi_approve_reason")
    private String certiApproveReason;

	//是否已经申请开票  1 是  0 否
	@TableField("is_applyed_invoice")
	private String isApplyedInvoice;

	//申请开票时间
	@TableField("invoice_applyed_time")
	private Date invoiceApplyedTime;

	//申请开票金额
	@TableField("invoice_amount")
	private Double invoiceAmount;
	
	//是否线下培训
    @TableField("is_offline")
    private String isOffline;
	
	//职业技能报名ID
	@TableField("zyjd_bm_id")
	private String zyjdBmId;
	
    //标记退费用户ID
    @TableField("mark_refund_user_id")
    private String markRefundUserId;

    //退费金额
    @TableField("refund_amount")
    private Double refundAmount;
	
	 //备注
    @TableField("remark")
    private String remark;
    
    //是否标记为已开票 0否  1是
  	@TableField("is_mark_invoice")
  	private String isMarkInvoice;
  	
  	//报名时间
    @TableField("time")
    private Date time;
  	
  	 //修改时间
    @TableField("update_time")
    private Date updateTime;
    
    //修改用户id
    @TableField("updator_id")
    private String updatorId;

    @TableField("refund_remark")
    private String refundRemark;
	
	@TableField("invoice_path")
	private String invoicePath;

	//职务 0负责人 1班长 2副班长
	@TableField("shift_duty")
	private Integer shiftDuty;

	public String getApproveAdvice() {
		return approveAdvice;
	}

	public void setApproveAdvice(String approveAdvice) {
		this.approveAdvice = approveAdvice;
	}

	public String getIsMarkInvoice() {
		return isMarkInvoice;
	}

	public void setIsMarkInvoice(String isMarkInvoice) {
		this.isMarkInvoice = isMarkInvoice;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public String getIsJtbm() {
		return isJtbm;
	}

	public void setIsJtbm(String isJtbm) {
		this.isJtbm = isJtbm;
	}

	public String getJtbmUserId() {
		return jtbmUserId;
	}

	public void setJtbmUserId(String jtbmUserId) {
		this.jtbmUserId = jtbmUserId;
	}

	public String getJtbmOrgId() {
		return jtbmOrgId;
	}

	public void setJtbmOrgId(String jtbmOrgId) {
		this.jtbmOrgId = jtbmOrgId;
	}

	public BmStatus getStatus() {
		return status;
	}

	public void setStatus(BmStatus status) {
		this.status = status;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public String getClassId() {
		return classId;
	}

	public void setClassId(String classId) {
		this.classId = classId;
	}

	public String getApproveUserId() {
		return approveUserId;
	}

	public void setApproveUserId(String approveUserId) {
		this.approveUserId = approveUserId;
	}

	public String getIsPayed() {
		return isPayed;
	}

	public void setIsPayed(String isPayed) {
		this.isPayed = isPayed;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getMarkPayedUserId() {
		return markPayedUserId;
	}

	public void setMarkPayedUserId(String markPayedUserId) {
		this.markPayedUserId = markPayedUserId;
	}

	public Double getPayedAmount() {
		return payedAmount;
	}

	public void setPayedAmount(Double payedAmount) {
		this.payedAmount = payedAmount;
	}

	public String getIsApplyedInvoice() {
		return isApplyedInvoice;
	}

	public void setIsApplyedInvoice(String isApplyedInvoice) {
		this.isApplyedInvoice = isApplyedInvoice;
	}

	public Date getInvoiceApplyedTime() {
		return invoiceApplyedTime;
	}

	public void setInvoiceApplyedTime(Date invoiceApplyedTime) {
		this.invoiceApplyedTime = invoiceApplyedTime;
	}

	public String getMarkRefundUserId() {
		return markRefundUserId;
	}

	public void setMarkRefundUserId(String markRefundUserId) {
		this.markRefundUserId = markRefundUserId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getIsOffline() {
		return isOffline;
	}

	public void setIsOffline(String isOffline) {
		this.isOffline = isOffline;
	}

	public String getZyjdBmId() {
		return zyjdBmId;
	}

	public void setZyjdBmId(String zyjdBmId) {
		this.zyjdBmId = zyjdBmId;
	}

	public Double getInvoiceAmount() {
		return invoiceAmount;
	}

	public void setInvoiceAmount(Double invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}

	public String getCertiApproveUserId() {
		return certiApproveUserId;
	}

	public void setCertiApproveUserId(String certiApproveUserId) {
		this.certiApproveUserId = certiApproveUserId;
	}

	public ApproveResult getCertiApproveResult() {
		return certiApproveResult;
	}

	public void setCertiApproveResult(ApproveResult certiApproveResult) {
		this.certiApproveResult = certiApproveResult;
	}

	public String getCertiApproveReason() {
		return certiApproveReason;
	}

	public void setCertiApproveReason(String certiApproveReason) {
		this.certiApproveReason = certiApproveReason;
	}

	public String getRefundRemark() {
		return refundRemark;
	}

	public void setRefundRemark(String refundRemark) {
		this.refundRemark = refundRemark;
	}

	public String getInvoicePath() {
		return invoicePath;
	}

	public void setInvoicePath(String invoicePath) {
		this.invoicePath = invoicePath;
	}

	public Integer getShiftDuty() {
		return shiftDuty;
	}

	public void setShiftDuty(Integer shiftDuty) {
		this.shiftDuty = shiftDuty;
	}
}

    
