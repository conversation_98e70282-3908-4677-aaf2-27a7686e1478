package com.xunw.jxjy.model.zypx.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.jxjy.model.zypx.entity.TargetSetting;
import com.xunw.jxjy.model.zypx.entity.TargetType;
import io.swagger.annotations.Api;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 评分指标设置
 */
public interface TargetSettingMapper extends BaseMapper<TargetSetting> {

    /**
     *  根据学生id+项目id得到指标评分
     */
    List<Map<String, Object>> selectTargetResultByStudentId(@Param("xmId") String xmId, @Param("studentId") String studentId);

    List<TargetType> list(@Param("xmId") String xmId);
}
