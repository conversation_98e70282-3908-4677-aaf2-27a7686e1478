package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 职业培训项目公告已读记录表
 */
@TableName("BIZ_XM_NOTICE_RECORD")
public class ZypxXmNoticeRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 项目id
	@TableField("xm_id")
	private String xmId;

	//项目公告id
	@TableField("xm_notice_id")
	private String xmNoticeId;

	// 学生id
	@TableField("student_id")
	private String studentId;

	// 阅读时间
	@TableField("TIME")
	private Date time;

	public ZypxXmNoticeRecord() {
	}

	public ZypxXmNoticeRecord(String id, String xmId, String xmNoticeId, String studentId, Date time) {
		this.id = id;
		this.xmId = xmId;
		this.xmNoticeId = xmNoticeId;
		this.studentId = studentId;
		this.time = time;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getXmNoticeId() {
		return xmNoticeId;
	}

	public void setXmNoticeId(String xmNoticeId) {
		this.xmNoticeId = xmNoticeId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}
}