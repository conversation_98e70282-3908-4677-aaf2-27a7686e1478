package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.ZyjdIndustry;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.sys.entity.Dict;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.User;

/**
 * <AUTHOR>
 * @createTime 2021年04月06日 培训计划详情
 */
@TableName("biz_plan_detail")
public class PlanDetail implements Serializable, Cloneable {

	private static final long serialVersionUID = -5249706086244048948L;

	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 计划ID
	@TableField("plan_id")
	private String planId;

	// 名称
	@TableField("name")
	private String name;

	// 编号
	@TableField("serial_number")
	private String serialNumber;

	// 状态
	@TableField("status")
	private AuditStatus status;

	// 开始时间
	@TableField("start_time")
	private Date startTime;

	// 结束时间
	@TableField("end_time")
	private Date endTime;

	// 是否已执行
	@TableField("is_execute")
	private String isExecute;

	// 培训计划执行时间
	@TableField("execute_time")
	private Date executeTime;

	// 项目来源
	@TableField("xm_sources")
	private XmSources xmSources;

	// 委托单位ID
	@TableField("entrust_org_id")
	private String entrustOrgId;

	// 承办单位ID
	@TableField("receive_org_id")
	private String receiveOrgId;

	// 培训对象
	@TableField("trainees")
	private String trainees;

	// 关联审批事项id
	@TableField("approve_item_id")
	private String approveItemId;

	// 培训合同
	@TableField("contract")
	private String contract;

	// 培训实施方案
	@TableField("pyfa")
	private String pyfa;

	// 立项审批表
	@TableField("paper")
	private String paper;

	// 合同审核人
	@TableField("contract_user_id")
	private String contractUserId;

	// 合同审批结果
	@TableField("contract_status")
	private ApproveResult contractStatus;

	// 合同审核时间
	@TableField("contract_time")
	private Date contractTime;

	// 合同审核意见
	@TableField("contract_advice")
	private String contractAdvice;

	// 培训形式
	@TableField("training_form")
	private String trainingForm;

	// 是否发放证书 1 是 ； 0 否
	@TableField("is_issue_certificate")
	private String isIssueCertificate;

	// 教师团队
	@TableField("teachers")
	private String teachers;

	// 招生方式
	@TableField("enroll_way")
	private EnrollWay enrollWay;

	// 项目负责人ID
	@TableField("leader_id")
	private String leaderId;

	// 培训地点
	@TableField("address")
	private String address;

	// 计划课时
	@TableField("hours")
	private Double hours;

	// 计划培训人数
	@TableField("count")
	private Integer count;

	// 收费标准
	@TableField("amount")
	private Double amount;

	// 收费标准对应的费用单位
	@TableField("units")
	private String units;

	// 学员报名费用
	@TableField("fee_standard")
	private Double feeStandard;

	// 是否单位统付
	@TableField("is_org_pay")
	private String isOrgPay;

	// 最近保存时间
	@TableField("save_time")
	private Date saveTime;

	// 上报时间
	@TableField("submit_time")
	private Date submitTime;

	// 面向行业
	@TableField("dict_industry_id")
	private String dictIndustryId;

	// 立项表审核人
	@TableField("approve_user_id")
	private String approveUserId;

	// 立项表审批结果
	@TableField("approve_status")
	private ApproveResult approveStatus;

	// 立项表审核时间
	@TableField("approval_time")
	private Date approvalTime;

	// 立项表审核意见
	@TableField("approve_advice")
	private String approveAdvice;

	// 考评实施方案
	@TableField("eval_paper")
	private String evalPaper;

	// 申报该项目的用户ID
	@TableField("user_id")
	private String userId;

	// 是否为免费公益性项目
	@TableField("is_free_public")
	private String isFreePublic;

	// 是否为政府补贴性项目
	@TableField("is_gov_subside")
	private String isGovSubside;

	// 甲方类型
	@TableField("entrust_org_nature")
	private String entrustOrgNature;

	// 合同金额
	@TableField("contract_amount")
	private BigDecimal contractAmount;

	// 合同签署时间
	@TableField("contract_sign_time")
	private Date contractSignTime;

	// 合同完成时间
	@TableField("contract_finish_time")
	private Date contractFinishTime;

	// 财政资金金额
	@TableField("gov_amount")
	private Double govAmount;

	// 非财政资金金额
	@TableField("not_gov_amount")
	private Double notGovAmount;

	// 线上学时
	@TableField("online_hours")
	private Double onlineHours;

	// 线下学时
	@TableField("off_line_hours")
	private Double offLineHours;

	// 培训对象人数
	@TableField("count_data")
	private String countData;

	// 校内教师人数
	@TableField("school_teacher_count")
	private Integer schoolTeacherCount;

	// 校内教师姓名
	@TableField("school_teacher_name")
	private String schoolTeacherName;

	// 外聘教师人数
	@TableField("outer_teacher_count")
	private Integer outerTeacherCount;

	// 外聘教师姓名
	@TableField("outer_teacher_name")
	private String outerTeacherName;

	// 是否为典型特色项目
	@TableField("is_typical")
	private String isTypical;

	// 典型特色项目备注
	@TableField("typical_remark")
	private String typicalRemark;

	// 总支出
	@TableField("total_cost")
	private Double totalCost;
	
	/**
	 * 以下属性用于前端接口VO
	 */
	//结余
	@TableField("surplus")
	private Double surplus;

	/**
	 * 购买类型，COURSE 按课程购买，XM 按项目购买
	 */
	@TableField("buy_type")
	private BuyType buyType;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	//行业特色 字典 industryCategory
	@TableField("industry_category")
	private String industryCategory;

	//资助金额
	@TableField("support_amount")
	private BigDecimal supportAmount;

	//所属基地
	@TableField("base_id")
	private String baseId;

	//培训班类型
	@TableField("classz_type")
	private String classzType;

	// 委托单位
	@TableField(exist = false)
	private Org entrustOrg;

	// 承办单位
	@TableField(exist = false)
	private Org receiveOrg;

	// 项目负责人
	@TableField(exist = false)
	private User leader;
	
	// 面向行业
	@TableField(exist = false)
	private Dict dictIndustry;

	//申报时候的课程设置
	@TableField(exist = false)
	private List<Map<String, Object>> courses;

	public String getApproveAdvice() {
		return approveAdvice;
	}

	public void setApproveAdvice(String approveAdvice) {
		this.approveAdvice = approveAdvice;
	}

	public String getEvalPaper() {
		return evalPaper;
	}

	public void setEvalPaper(String evalPaper) {
		this.evalPaper = evalPaper;
	}

	public String getApproveUserId() {
		return approveUserId;
	}

	public void setApproveUserId(String approveUserId) {
		this.approveUserId = approveUserId;
	}

	public ApproveResult getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(ApproveResult approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Date getApprovalTime() {
		return approvalTime;
	}

	public void setApprovalTime(Date approvalTime) {
		this.approvalTime = approvalTime;
	}

	public String getDictIndustryId() {
		return dictIndustryId;
	}

	public void setDictIndustryId(String dictIndustryId) {
		this.dictIndustryId = dictIndustryId;
	}

	public Dict getDictIndustry() {
		return dictIndustry;
	}

	public void setDictIndustry(Dict dictIndustry) {
		this.dictIndustry = dictIndustry;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public AuditStatus getStatus() {
		return status;
	}

	public void setStatus(AuditStatus status) {
		this.status = status;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getIsExecute() {
		return isExecute;
	}

	public void setIsExecute(String isExecute) {
		this.isExecute = isExecute;
	}

	public Date getExecuteTime() {
		return executeTime;
	}

	public void setExecuteTime(Date executeTime) {
		this.executeTime = executeTime;
	}

	public XmSources getXmSources() {
		return xmSources;
	}

	public void setXmSources(XmSources xmSources) {
		this.xmSources = xmSources;
	}

	public String getReceiveOrgId() {
		return receiveOrgId;
	}

	public void setReceiveOrgId(String receiveOrgId) {
		this.receiveOrgId = receiveOrgId;
	}

	public String getTrainees() {
		return trainees;
	}

	public void setTrainees(String trainees) {
		this.trainees = trainees;
	}

	public String getApproveItemId() {
		return approveItemId;
	}

	public void setApproveItemId(String approveItemId) {
		this.approveItemId = approveItemId;
	}

	public String getContract() {
		return contract;
	}

	public void setContract(String contract) {
		this.contract = contract;
	}

	public String getPyfa() {
		return pyfa;
	}

	public void setPyfa(String pyfa) {
		this.pyfa = pyfa;
	}

	public String getPaper() {
		return paper;
	}

	public void setPaper(String paper) {
		this.paper = paper;
	}

	public String getTrainingForm() {
		return trainingForm;
	}

	public void setTrainingForm(String trainingForm) {
		this.trainingForm = trainingForm;
	}

	public String getIsIssueCertificate() {
		return isIssueCertificate;
	}

	public void setIsIssueCertificate(String isIssueCertificate) {
		this.isIssueCertificate = isIssueCertificate;
	}

	public String getTeachers() {
		return teachers;
	}

	public void setTeachers(String teachers) {
		this.teachers = teachers;
	}

	public EnrollWay getEnrollWay() {
		return enrollWay;
	}

	public void setEnrollWay(EnrollWay enrollWay) {
		this.enrollWay = enrollWay;
	}

	public String getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Double getHours() {
		return hours;
	}

	public void setHours(Double hours) {
		this.hours = hours;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getUnits() {
		return units;
	}

	public void setUnits(String units) {
		this.units = units;
	}

	public Double getFeeStandard() {
		return feeStandard;
	}

	public void setFeeStandard(Double feeStandard) {
		this.feeStandard = feeStandard;
	}

	public String getIsOrgPay() {
		return isOrgPay;
	}

	public void setIsOrgPay(String isOrgPay) {
		this.isOrgPay = isOrgPay;
	}

	public Date getSaveTime() {
		return saveTime;
	}

	public void setSaveTime(Date saveTime) {
		this.saveTime = saveTime;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public Org getEntrustOrg() {
		return entrustOrg;
	}

	public void setEntrustOrg(Org entrustOrg) {
		this.entrustOrg = entrustOrg;
	}

	public Org getReceiveOrg() {
		return receiveOrg;
	}

	public void setReceiveOrg(Org receiveOrg) {
		this.receiveOrg = receiveOrg;
	}

	public User getLeader() {
		return leader;
	}

	public void setLeader(User leader) {
		this.leader = leader;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public String getEntrustOrgId() {
		return entrustOrgId;
	}

	public void setEntrustOrgId(String entrustOrgId) {
		this.entrustOrgId = entrustOrgId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getIsFreePublic() {
		return isFreePublic;
	}

	public void setIsFreePublic(String isFreePublic) {
		this.isFreePublic = isFreePublic;
	}

	public String getIsGovSubside() {
		return isGovSubside;
	}

	public void setIsGovSubside(String isGovSubside) {
		this.isGovSubside = isGovSubside;
	}

	public String getEntrustOrgNature() {
		return entrustOrgNature;
	}

	public void setEntrustOrgNature(String entrustOrgNature) {
		this.entrustOrgNature = entrustOrgNature;
	}

	public BigDecimal getContractAmount() {
		return contractAmount;
	}

	public void setContractAmount(BigDecimal contractAmount) {
		this.contractAmount = contractAmount;
	}

	public Date getContractSignTime() {
		return contractSignTime;
	}

	public void setContractSignTime(Date contractSignTime) {
		this.contractSignTime = contractSignTime;
	}

	public Date getContractFinishTime() {
		return contractFinishTime;
	}

	public void setContractFinishTime(Date contractFinishTime) {
		this.contractFinishTime = contractFinishTime;
	}

	public Double getGovAmount() {
		return govAmount;
	}

	public void setGovAmount(Double govAmount) {
		this.govAmount = govAmount;
	}

	public Double getNotGovAmount() {
		return notGovAmount;
	}

	public void setNotGovAmount(Double notGovAmount) {
		this.notGovAmount = notGovAmount;
	}

	public Double getOnlineHours() {
		return onlineHours;
	}

	public void setOnlineHours(Double onlineHours) {
		this.onlineHours = onlineHours;
	}

	public Double getOffLineHours() {
		return offLineHours;
	}

	public void setOffLineHours(Double offLineHours) {
		this.offLineHours = offLineHours;
	}

	public String getCountData() {
		return countData;
	}

	public void setCountData(String countData) {
		this.countData = countData;
	}

	public Integer getSchoolTeacherCount() {
		return schoolTeacherCount;
	}

	public void setSchoolTeacherCount(Integer schoolTeacherCount) {
		this.schoolTeacherCount = schoolTeacherCount;
	}

	public String getSchoolTeacherName() {
		return schoolTeacherName;
	}

	public void setSchoolTeacherName(String schoolTeacherName) {
		this.schoolTeacherName = schoolTeacherName;
	}

	public Integer getOuterTeacherCount() {
		return outerTeacherCount;
	}

	public void setOuterTeacherCount(Integer outerTeacherCount) {
		this.outerTeacherCount = outerTeacherCount;
	}

	public String getOuterTeacherName() {
		return outerTeacherName;
	}

	public void setOuterTeacherName(String outerTeacherName) {
		this.outerTeacherName = outerTeacherName;
	}

	public String getIsTypical() {
		return isTypical;
	}

	public void setIsTypical(String isTypical) {
		this.isTypical = isTypical;
	}

	public String getTypicalRemark() {
		return typicalRemark;
	}

	public void setTypicalRemark(String typicalRemark) {
		this.typicalRemark = typicalRemark;
	}

	public Double getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(Double totalCost) {
		this.totalCost = totalCost;
	}


	public String getContractUserId() {
		return contractUserId;
	}

	public void setContractUserId(String contractUserId) {
		this.contractUserId = contractUserId;
	}

	public ApproveResult getContractStatus() {
		return contractStatus;
	}

	public void setContractStatus(ApproveResult contractStatus) {
		this.contractStatus = contractStatus;
	}

	public Date getContractTime() {
		return contractTime;
	}

	public void setContractTime(Date contractTime) {
		this.contractTime = contractTime;
	}

	public String getContractAdvice() {
		return contractAdvice;
	}

	public void setContractAdvice(String contractAdvice) {
		this.contractAdvice = contractAdvice;
	}

	public Double getSurplus() {
		return surplus;
	}

	public void setSurplus(Double surplus) {
		this.surplus = surplus;
	}

	public BuyType getBuyType() {
		return buyType;
	}

	public void setBuyType(BuyType buyType) {
		this.buyType = buyType;
	}

	public List<Map<String, Object>> getCourses() {
		return courses;
	}

	public void setCourses(List<Map<String, Object>> courses) {
		this.courses = courses;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIndustryCategory() {
		return industryCategory;
	}

	public void setIndustryCategory(String industryCategory) {
		this.industryCategory = industryCategory;
	}

	public BigDecimal getSupportAmount() {
		return supportAmount;
	}

	public void setSupportAmount(BigDecimal supportAmount) {
		this.supportAmount = supportAmount;
	}

	public String getBaseId() {
		return baseId;
	}

	public void setBaseId(String baseId) {
		this.baseId = baseId;
	}

	public String getClasszType() {
		return classzType;
	}

	public void setClasszType(String classzType) {
		this.classzType = classzType;
	}
}
