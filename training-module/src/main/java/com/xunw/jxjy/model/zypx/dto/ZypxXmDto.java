package com.xunw.jxjy.model.zypx.dto;

import com.xunw.jxjy.model.enums.BuyType;
import com.xunw.jxjy.model.enums.OpenStudyType;
import com.xunw.jxjy.model.enums.XmStatus;

import java.math.BigDecimal;

public class ZypxXmDto {

	private String id;
	private String serialNumber;
	private String title;
	private String startTime;
	private String endTime;
	private String typeId;
	private String trainees;
	private String jtbmStartTime;
	private String jtbmEndTime;
	private String isAllowGrbm;
	private String grbmStartTime;
	private String grbmEndTime;
	private String studyEndTime;
	private String isPopQues;
	private Double amount; // 培训费用
	private String xct;
	private String logo;
	private String notice;
	private String isOpenCamera;
	private Integer photoCatchInterval;
	private String isOpenVerify;
	private Double verifyThreshold;
	private XmStatus status;
	private String years;
	private String planId;
	private String isAllowChooseCourse;
	private String leaderId;
	private String poiaddress;
	private String poilatlng;
	private String arriveRange;
	private Integer limitCount;
	private String certiTplId;
	private String certiDate;
	private String certiXmName;
	private String isAlllowStudyBeforePay;
	private String isAlllowExamBeforePay;
	private Double certiHours;
	private String isHot;
	private String isAutoSendSms;
	private String bmInviteCode;
	private String isMustFillPersonalInfo;
	private String isAllowMobileStudy;
	private String isNeedApprove;
	private String industryId; //行业id
	private String professionId;//职业id
	private String techLevel;//等级
	private String isSkill;
	//是否使用第三方学习平台
	private String isOpenStudy;
	//第三方学习平台类型
	private OpenStudyType openStudyType;
	//第三方平台学习链接
	private String openStudyUrl;
	//是否允许报名多门课程
	private Integer isAllowMultiChooseCourse = 1;
	//是否免注册
	Integer isExemptRegister;
	//购买类型
	private BuyType buyType;
	//报到简介
	private String reportIntroduction;
	//师资简介
	private String teacherIntroduction;
	//管理团队及联系方式
	private String managementTeam;

	//班委职责
	private String classCommitteeWork;
	//小组工作内容 包含组长职责和值日工作内容
	private String groupWork;

	//住宿安排
	private String stayPlan;
	//用餐安排
	private String eatPlan;

	//用餐安排地址坐标
	private String eatPoint;

	//行业特色 字典 industryCategory
	private String industryCategory;

	//收入
	private BigDecimal incomeAmount;

	//资助金额
	private BigDecimal supportAmount;

	//所属基地
	private String baseId;

	//合同金额
	private BigDecimal contractAmount;

	//计划培训人数
	private Integer count;

	//班级类型（标签）
	private String classzType;

	public String getSerialNumber() {
		return serialNumber;
	}
	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getTypeId() {
		return typeId;
	}
	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}
	public String getTrainees() {
		return trainees;
	}
	public void setTrainees(String trainees) {
		this.trainees = trainees;
	}
	public String getJtbmStartTime() {
		return jtbmStartTime;
	}
	public void setJtbmStartTime(String jtbmStartTime) {
		this.jtbmStartTime = jtbmStartTime;
	}
	public String getJtbmEndTime() {
		return jtbmEndTime;
	}
	public void setJtbmEndTime(String jtbmEndTime) {
		this.jtbmEndTime = jtbmEndTime;
	}
	public String getIsAllowGrbm() {
		return isAllowGrbm;
	}
	public void setIsAllowGrbm(String isAllowGrbm) {
		this.isAllowGrbm = isAllowGrbm;
	}
	public String getGrbmStartTime() {
		return grbmStartTime;
	}
	public void setGrbmStartTime(String grbmStartTime) {
		this.grbmStartTime = grbmStartTime;
	}
	public String getGrbmEndTime() {
		return grbmEndTime;
	}
	public void setGrbmEndTime(String grbmEndTime) {
		this.grbmEndTime = grbmEndTime;
	}
	public String getStudyEndTime() {
		return studyEndTime;
	}
	public void setStudyEndTime(String studyEndTime) {
		this.studyEndTime = studyEndTime;
	}
	public String getIsPopQues() {
		return isPopQues;
	}
	public void setIsPopQues(String isPopQues) {
		this.isPopQues = isPopQues;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public String getXct() {
		return xct;
	}
	public void setXct(String xct) {
		this.xct = xct;
	}
	public String getLogo() {
		return logo;
	}
	public void setLogo(String logo) {
		this.logo = logo;
	}
	public String getNotice() {
		return notice;
	}
	public void setNotice(String notice) {
		this.notice = notice;
	}
	public String getIsOpenCamera() {
		return isOpenCamera;
	}
	public void setIsOpenCamera(String isOpenCamera) {
		this.isOpenCamera = isOpenCamera;
	}
	public Integer getPhotoCatchInterval() {
		return photoCatchInterval;
	}
	public void setPhotoCatchInterval(Integer photoCatchInterval) {
		this.photoCatchInterval = photoCatchInterval;
	}
	public String getIsOpenVerify() {
		return isOpenVerify;
	}
	public void setIsOpenVerify(String isOpenVerify) {
		this.isOpenVerify = isOpenVerify;
	}
	public Double getVerifyThreshold() {
		return verifyThreshold;
	}
	public void setVerifyThreshold(Double verifyThreshold) {
		this.verifyThreshold = verifyThreshold;
	}
	public XmStatus getStatus() {
		return status;
	}
	public void setStatus(XmStatus status) {
		this.status = status;
	}
	public String getYears() {
		return years;
	}
	public void setYears(String years) {
		this.years = years;
	}
	public String getPlanId() {
		return planId;
	}
	public void setPlanId(String planId) {
		this.planId = planId;
	}
	public String getIsAllowChooseCourse() {
		return isAllowChooseCourse;
	}
	public void setIsAllowChooseCourse(String isAllowChooseCourse) {
		this.isAllowChooseCourse = isAllowChooseCourse;
	}
	public String getLeaderId() {
		return leaderId;
	}
	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}
	public String getPoiaddress() {
		return poiaddress;
	}
	public void setPoiaddress(String poiaddress) {
		this.poiaddress = poiaddress;
	}
	public String getPoilatlng() {
		return poilatlng;
	}
	public void setPoilatlng(String poilatlng) {
		this.poilatlng = poilatlng;
	}
	public String getArriveRange() {
		return arriveRange;
	}
	public void setArriveRange(String arriveRange) {
		this.arriveRange = arriveRange;
	}
	public Integer getLimitCount() {
		return limitCount;
	}
	public void setLimitCount(Integer limitCount) {
		this.limitCount = limitCount;
	}
	public String getCertiTplId() {
		return certiTplId;
	}
	public void setCertiTplId(String certiTplId) {
		this.certiTplId = certiTplId;
	}
	public String getCertiDate() {
		return certiDate;
	}
	public void setCertiDate(String certiDate) {
		this.certiDate = certiDate;
	}
	public String getCertiXmName() {
		return certiXmName;
	}
	public void setCertiXmName(String certiXmName) {
		this.certiXmName = certiXmName;
	}
	public String getIsAlllowStudyBeforePay() {
		return isAlllowStudyBeforePay;
	}
	public void setIsAlllowStudyBeforePay(String isAlllowStudyBeforePay) {
		this.isAlllowStudyBeforePay = isAlllowStudyBeforePay;
	}
	public String getIsAlllowExamBeforePay() {
		return isAlllowExamBeforePay;
	}
	public void setIsAlllowExamBeforePay(String isAlllowExamBeforePay) {
		this.isAlllowExamBeforePay = isAlllowExamBeforePay;
	}
	public Double getCertiHours() {
		return certiHours;
	}
	public void setCertiHours(Double certiHours) {
		this.certiHours = certiHours;
	}
	public String getIsHot() {
		return isHot;
	}
	public void setIsHot(String isHot) {
		this.isHot = isHot;
	}
	public String getIsAutoSendSms() {
		return isAutoSendSms;
	}
	public void setIsAutoSendSms(String isAutoSendSms) {
		this.isAutoSendSms = isAutoSendSms;
	}
	
	public String getBmInviteCode() {
		return bmInviteCode;
	}
	public void setBmInviteCode(String bmInviteCode) {
		this.bmInviteCode = bmInviteCode;
	}
	public String getIsMustFillPersonalInfo() {
		return isMustFillPersonalInfo;
	}
	public void setIsMustFillPersonalInfo(String isMustFillPersonalInfo) {
		this.isMustFillPersonalInfo = isMustFillPersonalInfo;
	}
	public String getIsAllowMobileStudy() {
		return isAllowMobileStudy;
	}
	public void setIsAllowMobileStudy(String isAllowMobileStudy) {
		this.isAllowMobileStudy = isAllowMobileStudy;
	}
	public String getIsNeedApprove() {
		return isNeedApprove;
	}
	public void setIsNeedApprove(String isNeedApprove) {
		this.isNeedApprove = isNeedApprove;
	}
	public String getIndustryId() {
		return industryId;
	}
	public void setIndustryId(String industryId) {
		this.industryId = industryId;
	}
	public String getProfessionId() {
		return professionId;
	}
	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}
	public String getTechLevel() {
		return techLevel;
	}
	public void setTechLevel(String techLevel) {
		this.techLevel = techLevel;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

	public String getIsSkill() {
		return isSkill;
	}

	public void setIsSkill(String isSkill) {
		this.isSkill = isSkill;
	}

	public String getIsOpenStudy() {
		return isOpenStudy;
	}

	public void setIsOpenStudy(String isOpenStudy) {
		this.isOpenStudy = isOpenStudy;
	}

	public String getOpenStudyUrl() {
		return openStudyUrl;
	}

	public void setOpenStudyUrl(String openStudyUrl) {
		this.openStudyUrl = openStudyUrl;
	}

	public BuyType getBuyType() {
		return buyType;
	}

	public void setBuyType(BuyType buyType) {
		this.buyType = buyType;
	}
	public Integer getIsExemptRegister() {
		return isExemptRegister;
	}
	public void setIsExemptRegister(Integer isExemptRegister) {
		this.isExemptRegister = isExemptRegister;
	}

	public String getReportIntroduction() {
		return reportIntroduction;
	}

	public void setReportIntroduction(String reportIntroduction) {
		this.reportIntroduction = reportIntroduction;
	}

	public String getTeacherIntroduction() {
		return teacherIntroduction;
	}

	public void setTeacherIntroduction(String teacherIntroduction) {
		this.teacherIntroduction = teacherIntroduction;
	}

	public String getManagementTeam() {
		return managementTeam;
	}

	public void setManagementTeam(String managementTeam) {
		this.managementTeam = managementTeam;
	}

	public String getClassCommitteeWork() {
		return classCommitteeWork;
	}

	public void setClassCommitteeWork(String classCommitteeWork) {
		this.classCommitteeWork = classCommitteeWork;
	}

	public String getGroupWork() {
		return groupWork;
	}

	public void setGroupWork(String groupWork) {
		this.groupWork = groupWork;
	}

	public String getStayPlan() {
		return stayPlan;
	}

	public void setStayPlan(String stayPlan) {
		this.stayPlan = stayPlan;
	}

	public String getEatPlan() {
		return eatPlan;
	}

	public void setEatPlan(String eatPlan) {
		this.eatPlan = eatPlan;
	}

	public String getEatPoint() {
		return eatPoint;
	}

	public void setEatPoint(String eatPoint) {
		this.eatPoint = eatPoint;
	}

	public String getIndustryCategory() {
		return industryCategory;
	}

	public void setIndustryCategory(String industryCategory) {
		this.industryCategory = industryCategory;
	}

	public BigDecimal getIncomeAmount() {
		return incomeAmount;
	}

	public void setIncomeAmount(BigDecimal incomeAmount) {
		this.incomeAmount = incomeAmount;
	}

	public BigDecimal getSupportAmount() {
		return supportAmount;
	}

	public void setSupportAmount(BigDecimal supportAmount) {
		this.supportAmount = supportAmount;
	}

	public String getBaseId() {
		return baseId;
	}

	public void setBaseId(String baseId) {
		this.baseId = baseId;
	}

	public BigDecimal getContractAmount() {
		return contractAmount;
	}

	public void setContractAmount(BigDecimal contractAmount) {
		this.contractAmount = contractAmount;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getClasszType() {
		return classzType;
	}

	public void setClasszType(String classzType) {
		this.classzType = classzType;
	}

	public OpenStudyType getOpenStudyType() {
		return openStudyType;
	}

	public void setOpenStudyType(OpenStudyType openStudyType) {
		this.openStudyType = openStudyType;
	}

	public Integer getIsAllowMultiChooseCourse() {
		return isAllowMultiChooseCourse;
	}

	public void setIsAllowMultiChooseCourse(Integer isAllowMultiChooseCourse) {
		this.isAllowMultiChooseCourse = isAllowMultiChooseCourse;
	}
}