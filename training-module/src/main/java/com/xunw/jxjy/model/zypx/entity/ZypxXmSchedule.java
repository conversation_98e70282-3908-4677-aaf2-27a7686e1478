package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.enums.ScheduleSign;

/**
 * 职业培训项目日程
 */
@TableName("biz_xm_schedule")
public class ZypxXmSchedule implements Serializable, Cloneable {

	private static final long serialVersionUID = 2609780857607364548L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	// 项目id
	@TableField("xm_id")
	private String xmId;

	// 日程时间：年月日 时分
	@TableField("schedule_time")
	private Date scheduleTime;

	// 日程安排
	@TableField("arrange")
	private String arrange;

	// 创建用户id
	@TableField("creator_id")
	private String creatorId;

	// 创建时间
	@TableField("create_time")
	private Date createTime;

	// 年月日
	@TableField(exist = false)
	private String scheduleYMD;

	// 时分
	@TableField(exist = false)
	private String scheduleHM;

	// 标记：已完成、当前、未开始
	@TableField(exist = false)
	private ScheduleSign scheduleSign;

	public ZypxXmSchedule() {
		super();
	}

	public ZypxXmSchedule(String id, String xmId, Date scheduleTime, String arrange, String creatorId,
			Date createTime) {
		super();
		this.id = id;
		this.xmId = xmId;
		this.scheduleTime = scheduleTime;
		this.arrange = arrange;
		this.creatorId = creatorId;
		this.createTime = createTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public Date getScheduleTime() {
		return scheduleTime;
	}

	public void setScheduleTime(Date scheduleTime) {
		this.scheduleTime = scheduleTime;
	}

	public String getArrange() {
		return arrange;
	}

	public void setArrange(String arrange) {
		this.arrange = arrange;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getScheduleYMD() {
		return DateUtils.format(scheduleTime, "yyyy-MM-dd");
	}
	
	public void setScheduleYMD(String scheduleYMD) {
		this.scheduleYMD = scheduleYMD;
	}

	public String getScheduleHM() {
		return DateUtils.format(scheduleTime, "HH:mm");
	}

	public void setScheduleHM(String scheduleHM) {
		this.scheduleHM = scheduleHM;
	}

	public ScheduleSign getScheduleSign() {
		return scheduleSign;
	}

	public void setScheduleSign(ScheduleSign scheduleSign) {
		this.scheduleSign = scheduleSign;
	}

}