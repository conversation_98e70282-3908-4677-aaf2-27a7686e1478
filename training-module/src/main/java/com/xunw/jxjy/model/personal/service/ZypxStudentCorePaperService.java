package com.xunw.jxjy.model.personal.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.CacheHelper;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.FileHelper;
import com.xunw.jxjy.model.core.TomSystemQueue;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.enums.ExamModel;
import com.xunw.jxjy.model.enums.Jjkhd;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.enums.Sjlx;
import com.xunw.jxjy.model.enums.Stlb;
import com.xunw.jxjy.model.enums.Stplsx;
import com.xunw.jxjy.model.exam.entity.ExamAnswerCard;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.exam.entity.ExamFaceVerifyLog;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.exam.entity.ExamPaperMaxScore;
import com.xunw.jxjy.model.exam.entity.ExamPhotoLog;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.mapper.StudentInfoMapper;
import com.xunw.jxjy.model.sys.entity.SystemSetting;
import com.xunw.jxjy.model.sys.service.SystemSettingService;
import com.xunw.jxjy.model.tk.entity.QuestionEntity;
import com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper;
import com.xunw.jxjy.model.utils.FaceVerifyUtil;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamAnswerCardMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamDataMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamFaceVerifyLogMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMaxScoreMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPhotoLogMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.model.PaperSection;
import com.xunw.jxjy.paper.model.QBlank;
import com.xunw.jxjy.paper.model.Question;
import com.xunw.jxjy.paper.model.QuestionBlankFill;
import com.xunw.jxjy.paper.model.QuestionEssay;
import com.xunw.jxjy.paper.model.QuestionJdt;
import com.xunw.jxjy.paper.model.QuestionJudgment;
import com.xunw.jxjy.paper.model.QuestionLst;
import com.xunw.jxjy.paper.model.QuestionMcjst;
import com.xunw.jxjy.paper.model.QuestionMultipleChoice;
import com.xunw.jxjy.paper.model.QuestionSingleChoice;
import com.xunw.jxjy.paper.model.QuestionTt;
import com.xunw.jxjy.paper.utils.ModelHelper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 学员 移动端、PC端 试卷核心服务 ----公共方法抽离
 * <AUTHOR>
 */
@Service
public class ZypxStudentCorePaperService {

	@Autowired
	private ZypxExamPaperMapper mapper;
	@Autowired
	private ZypxExamPaperMaxScoreMapper examPaperMaxScoreMapper;
	@Autowired
	private ZypxExamDataMapper examDataMapper;
	@Autowired
	private QuestionEntityMapper questionEntityMapper;
	@Autowired
	private SystemSettingService systemSettingService;
	@Autowired
	private ZypxXmMapper xmMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private ZypxExamFaceVerifyLogMapper examFaceVerifyLogMapper;
	@Autowired
	private ZypxExamPhotoLogMapper examPhotoLogMapper;
	@Autowired
	private ZypxExamAnswerCardMapper examAnswerCardMapper;

	/**
	 * 获取试卷对象
	 */
	public Paper getPaper(String id) {
		Paper paper = CacheHelper.getCache("PaperCache", "P" + id);
		if (paper != null) {
			return paper;
		}
		paper = getBasicPaperFromDatabase(id);
		if (paper == null) {
			throw BizException.withMessage("该试卷尚未组卷，无法作答！");
		} else {
			paper.setId(id);
		}
		if (paper.getPaperType() == Sjlx.PT) {
			paper = buildNormalPaper(paper);
			CacheHelper.setCache("PaperCache", "P" + id, paper);
		}
		// 如果是普通试卷，并要求随机排序
		if (paper.getPaperType() == Sjlx.PT && paper.getQuesSortType() == Stplsx.SJ) {
			for (PaperSection section : paper.getSections()) {
				Collections.shuffle(section.getQuestions());
			}
		}
		return paper;
	}

	/**
	 * 开始考试之前的校验
	 */
	public void paperCheck(String paperId,String studentId, String ip, String examValidCode) {
		Map<String, Object> paperInfo = mapper.getPaperInfoById(paperId);
		// 试卷分类
		PaperCategory paperCategory = PaperCategory.valueOf((String) paperInfo.get("category"));
		// 综合测验 有ip验证码校验的
		if (paperCategory != PaperCategory.PSZY) {
			String ipRange = (String) paperInfo.get("ipRange");
			if (BaseUtil.isNotEmpty(ipRange) && !BaseUtil.isIpAllow(ip, ipRange)) {
				throw BizException.withMessage("您的IP("+ip+")不在允许考试的ip段("+ipRange+")内，无法参加考试！");
			}
			String validateCode = (String) paperInfo.get("validateCode");
			if (BaseUtil.isNotEmpty(validateCode)) {
				if (BaseUtil.isEmpty(examValidCode)) {
					throw BizException.withMessage("请输入验证码！");
				} else if (!examValidCode.equals(validateCode)) {
					throw BizException.withMessage("验证码输入错误！");
				}
			}
		}
		Paper paper = this.getPaper(paperId);
		if (paper == null) {
			throw BizException.withMessage("试卷不存在");
		}
		long nowtime = System.currentTimeMillis();
		// 是否已经结束
		if (BaseUtil.isNotEmpty(paper.getEndTime()) && paper.getEndTime().getTime() < nowtime) {
			throw BizException.withMessage("无法开始考试，因为考试时间已经结束");
		}
		// 是否没有开始
		if (BaseUtil.isNotEmpty(paper.getStartTime()) && paper.getStartTime().getTime() > nowtime) {
			throw BizException.withMessage("未到考试开始时间");
		}
	}

	/**
	 * 开始考试
	 */
	@Transactional
	public void startExam(String studentId,String paperId, String ip, String photoPath) {
		ExamPaper examPaper = mapper.selectById(paperId);
		ExamData checkExamData = this.getExamData(studentId, paperId);
		if (checkExamData == null) {
			ExamData examData = new ExamData(BaseUtil.generateId2(), paperId, studentId, DateUtils.now(), ip);
			examData.setStatus(ExamDataStatus.WJJ);
			examDataMapper.insert(examData);
		} else if (BaseUtil.getInt(examPaper.getIsReexamination(), 0) == 0
				&& checkExamData.getStatus() != ExamDataStatus.WJJ) {
			throw BizException.withMessage("试卷已经提交,请勿重复操作");
		}
		//开考时的抓拍照片存储
		if (StringUtils.isNotEmpty(photoPath)) {
			ExamPhotoLog examPhotoLog = new ExamPhotoLog();
			examPhotoLog.setId(BaseUtil.generateId2());
			examPhotoLog.setExamdataId(checkExamData.getId());
			examPhotoLog.setUrl(BaseUtil.isEmpty(photoPath) ? null : photoPath);
			examPhotoLog.setLogTime(new Date());
			examPhotoLogMapper.insert(examPhotoLog);
		}
	}

	/**
	 * 获取作答结果
	 */
	public JSONObject getStudentAnswer(String studentId, String paperId) {
		ExamData examData = this.getExamData(studentId, paperId);
		return examData != null ? JSONObject.fromObject(examData.getData()) : null;
	}

	/**
	 * 保存作答结果
	 */
	public boolean saveData(HttpServletRequest request, String studentId) {
		String paperId = request.getParameter("paperId");
		EntityWrapper<ExamData> examdataWrapper = new EntityWrapper<ExamData>();
		examdataWrapper.eq("paper_id", paperId);
		examdataWrapper.eq("student_id", studentId);
		List<ExamData> examdataList = examDataMapper.selectList(examdataWrapper);
		ExamData studentExamData = examdataList.get(0);
		if (BaseUtil.isNotEmpty(studentExamData) && !studentExamData.getStatus().equals(ExamDataStatus.WJJ)) {
			throw BizException.withMessage("用户已提交过试卷，不允许继续答题!");
		}

		Map<String, Object> examData = new HashMap<>();
		Map<String, String[]> mapx = request.getParameterMap();
		for (Map.Entry<String, String[]> entry : mapx.entrySet()) {
			if (entry.getKey().startsWith("Q-")) {
				examData.put(entry.getKey(), StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
			}
		}
		JSONObject userDataJson = JSONObject.fromObject(examData);
		String studentAnswer = userDataJson == null ? "" : userDataJson.toString();
		studentExamData = new ExamData();
		studentExamData.setData(studentAnswer);
		studentExamData.setSaveTime(new Date());

		examdataWrapper = new EntityWrapper<ExamData>();
		examdataWrapper.eq("paper_id", paperId);
		examdataWrapper.eq("student_id", studentId);
		examdataWrapper.eq("status", ExamDataStatus.WJJ);
		examDataMapper.update(studentExamData, examdataWrapper);

		return true;
	}

	/**
	 * 历史试卷重做
	 */
	@Transactional
	public void redoPaper(String paperId, String studentId) {
		ExamPaper examPaper = mapper.selectById(paperId);
		if (BaseUtil.isNotEmpty(examPaper.getEndTime())
				&& examPaper.getEndTime().getTime() < System.currentTimeMillis()) {
			throw BizException.withMessage("该试卷已过了结束时间，无法重做");
		} else {
			// 删除之前存储到作答记录历史表中
			ExamData examData = getExamData(studentId, paperId);
			// 删除此作答记录
			examDataMapper.deleteById(examData.getId());
		}
	}

	/**
	 * 提交答卷
	 */
	public boolean submit(HttpServletRequest request, String studentId) {
		String paperId = request.getParameter("paperId");
		ExamPaper examPaper = mapper.selectById(paperId);
		PaperCategory paperCategory = examPaper.getCategory();
		EntityWrapper<ExamData> examdataWrapper = new EntityWrapper<ExamData>();
		examdataWrapper.eq("paper_id", paperId);
		examdataWrapper.eq("student_id", studentId);
		examdataWrapper.orderBy("save_time", false);
		List<ExamData> examDatas = examDataMapper.selectList(examdataWrapper);
		ExamData studentExamData = examDatas.get(0);
		// 默认最短交卷时间单位：秒
		int SHORTEST_DELIVER_TIME = 0;

		SystemSetting sysXtsz = systemSettingService.getGlobalSetting();
		if (StringUtils.isNotEmpty(sysXtsz.getContent())) {
			// 取配置的最短交卷时间
			JSONObject settingContent = JSONObject.fromObject(sysXtsz.getContent());
			SHORTEST_DELIVER_TIME = paperCategory.equals(PaperCategory.PSZY) ? settingContent.getInt("PSZY_MIN_JJSJ")
					: settingContent.getInt("ZHCY_MIN_JJSJ");
		} else {
			// 如果未配置 则取默认值
			if (paperCategory.equals(PaperCategory.PSZY)) {
				SHORTEST_DELIVER_TIME = Constants.HOMEWORK_SHORTEST_DELIVER_TIME * 60;
			} else {
				SHORTEST_DELIVER_TIME = Constants.TEST_SHORTEST_DELIVER_TIME * 60;
			}
		}

		// 获取开始时间
		Date startExamTime = (studentExamData == null || studentExamData.getStartTime() == null) ? (new Date()) : studentExamData.getStartTime();
		// 已经花费的时间，单位：秒
		int doneSeconds =new Long((System.currentTimeMillis() - startExamTime.getTime()) / 1000).intValue();
		// 最短交卷剩余时间，单位：秒
		int SHORTEST_DELIVER_LEFT_TIME = SHORTEST_DELIVER_TIME - doneSeconds;
		if (SHORTEST_DELIVER_LEFT_TIME > 0) {
			String timeStr = ((SHORTEST_DELIVER_LEFT_TIME / 60 > 0) ? ((SHORTEST_DELIVER_LEFT_TIME / 60) + "分") : "")
					+ ((SHORTEST_DELIVER_LEFT_TIME % 60 > 0) ? ((SHORTEST_DELIVER_LEFT_TIME % 60) + "秒") : "");
			throw BizException.withMessage("您现在不能提交，还需要" + timeStr + "钟之后才能提交。");
		}
		if (BaseUtil.isNotEmpty(studentExamData) &&  ExamDataStatus.WJJ != studentExamData.getStatus()) {
			throw BizException.withMessage("用户已提交过试卷，不允许重复提交!");
		}

		// 放入队列的map
		Map<String, Object> queueMap = new HashMap<>();
		// 为保障提交时时最新答案，也存一次答案
		Map<String, Object> examData = new HashMap<>();
		Map<String, String[]> mapx = request.getParameterMap();
		for (Map.Entry<String, String[]> entry : mapx.entrySet()) {
			queueMap.put(entry.getKey(), StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
			if (entry.getKey().startsWith("Q-")) {
				examData.put(entry.getKey(), StringUtils.join(entry.getValue(), Constants.TM_SPLITER));
			}
		}
		queueMap.put("paperId", paperId);
		queueMap.put("studentId", studentId);
		// 放入批改队列
		TomSystemQueue.addPaper(queueMap);

		studentExamData = new ExamData();
		JSONObject userDataJson = JSONObject.fromObject(examData);
		String studentAnswer = userDataJson == null ? "" : userDataJson.toString();
		studentExamData.setData(studentAnswer);
		studentExamData.setScore(0);
		studentExamData.setEndTime(new Date());
		studentExamData.setStatus(ExamDataStatus.YJJDPG);
		studentExamData.setClient(Jjkhd.PC);

		examdataWrapper = new EntityWrapper<ExamData>();
		examdataWrapper.eq("paper_id", paperId);
		examdataWrapper.eq("student_id", studentId);
		examdataWrapper.eq("status", ExamDataStatus.WJJ);
		examDataMapper.update(studentExamData, examdataWrapper);
		return true;
	}

	/**
	 * 更新平时作业的最高分
	 */
	@Transactional
	public void updateStudentHomeworkMaxScore(String paperId, String studentId, Integer score) {
		ExamPaper examPaper = mapper.selectById(paperId);
		if (examPaper.getCategory() != PaperCategory.PSZY) {
			return;
		}
		EntityWrapper<ExamPaperMaxScore> wrapper = new EntityWrapper();
		wrapper.eq("paper_id", paperId);
		wrapper.eq("student_id", studentId);
		List<ExamPaperMaxScore> maxScores = examPaperMaxScoreMapper.selectList(wrapper);
		ExamPaperMaxScore examPaperMaxScore = maxScores.size() > 0 ? maxScores.get(0) : null;
		if (examPaperMaxScore != null) {
			if (score > (examPaperMaxScore.getMaxScore() != null ? examPaperMaxScore.getMaxScore() : -1)) {
				examPaperMaxScore.setMaxScore(score);
				examPaperMaxScore.setTime(new Date());
				examPaperMaxScoreMapper.updateById(examPaperMaxScore);
			}
		}
		else {
			EntityWrapper<ExamData> examdataWrapper = new EntityWrapper();
			examdataWrapper.eq("paper_id", paperId);
			examdataWrapper.eq("student_id", studentId);
			List<ExamData> list = examDataMapper.selectList(examdataWrapper);
			if (list.size() > 0) {
				examPaperMaxScore = new ExamPaperMaxScore();
				examPaperMaxScore.setId(BaseUtil.generateId2());
				examPaperMaxScore.setStudentId(studentId);
				examPaperMaxScore.setPaperId(paperId);
				examPaperMaxScore.setMaxScore(score);
				examPaperMaxScore.setExamDataId(list.get(0).getId());
				examPaperMaxScore.setTime(new Date());
				examPaperMaxScoreMapper.insert(examPaperMaxScore);
			}
		}
	}

	/**
	 * 获取试卷的作答记录
	 */
	public ExamData getExamData(String studentId, String paperId) {
		EntityWrapper<ExamData> examdataWrapper = new EntityWrapper<ExamData>();
		examdataWrapper.eq("paper_id", paperId);
		examdataWrapper.eq("student_id", studentId);
		List<ExamData> examdataList = examDataMapper.selectList(examdataWrapper);
		return examdataList.size() > 0 ? examdataList.get(0) : null;
	}

	/**
	 * 获取试卷的作答记录
	 */
	public ExamData getExamDataById(String dataId) {
		return examDataMapper.selectById(dataId);
	}

	/**
	 * 从数据库构建一个基本的试卷对象
	 */
	private Paper getBasicPaperFromDatabase(String id) {
		ExamPaper examPaper = mapper.selectById(id);
		if (examPaper == null) {
			return null;
		}
		String data = examPaper.getData();
		return ModelHelper.convertObject(data);
	}

	/**
	 * 从数据库试卷对象来构建完整试卷对象
	 */
	private Paper buildNormalPaper(Paper paper) {
		List<PaperSection> sections = paper.getSections();
		if (sections != null) {
			Integer rscore = 0;
			for (PaperSection section : sections) {
				List<Question> questions = section.getQuestions();
				if (questions != null) {
					// 新的试题列表
					List<Question> newQuestions = new ArrayList<Question>();
					for (Question question : questions) {
						rscore = question.getScore();
						Stlb stlb = question.getType();
						if (stlb.equals(Stlb.TT)) {
							QuestionTt tt = (QuestionTt) question;
							List<Question> childrens = tt.getChildren();

							// 对套题中的小题进行排序
							sortTTChilren(childrens);

							int score = tt.getScore();
							String id = tt.getId();
							tt = (QuestionTt) getQuestion(id);
							if (tt != null) {
								try {
									tt = (QuestionTt) tt.clone();
								} catch (Exception e) {
									e.printStackTrace();
								}
								tt.setScore(score);

								if (childrens != null) {
									List<Question> newChildrens = new ArrayList<Question>();
									for (Question child : childrens) {
										int score1 = child.getScore();
										String id1 = child.getId();
										child = getQuestion(id1);
										if (child != null) {
											try {
												child = (Question) child.clone();
											} catch (Exception e) {
												e.printStackTrace();
											}
											child.setScore(score1);
											newChildrens.add(child);
										}
									}
									tt.setChildren(newChildrens);
								}
								else {
									tt.setChildren(new ArrayList<Question>());
								}
								newQuestions.add(tt);
							}
						} else {
							int score = question.getScore();
							String id = question.getId();
							question = getQuestion(id);
							if (question != null) {
								try {
									question = (Question) question.clone();
								} catch (Exception e) {
									e.printStackTrace();
								}
								question.setScore(score);
								newQuestions.add(question);
							} else {
								throw BizException.withMessage("组卷需要的试题不存在：id=" + id);
							}
						}
					}
					section.setRscore(rscore);
					section.setQuestions(newQuestions);
				}
			}
		}
		return paper;
	}

	/**
	 * 对套题中的子题按照录题的顺序排序
	 */
	private void sortTTChilren(List<Question> children) {
		if (children != null && children.size() > 0) {
			List<String> stdIds = children.stream().map(x -> x.getId()).collect(Collectors.toList());
			EntityWrapper<QuestionEntity> wrapper = new EntityWrapper<QuestionEntity>();
			wrapper.in("id", stdIds);
			wrapper.orderBy("seq_num", true);
			List<QuestionEntity> bizTkglSts = questionEntityMapper.selectList(wrapper);
			List<Question> newList = new ArrayList<Question>();
			for (QuestionEntity bizTkglSt : bizTkglSts) {
				for (Question question : children) {
					if (bizTkglSt.getId().equals(question.getId())) {
						newList.add(question);
					}
				}
			}
			children.clear();
			children.addAll(newList);
		}
	}

	/**
	 * 获取试题详情
	 */
	public Question getQuestion(String id) {
		Question question = null;
		if (question == null) {
			try {
				Map<String, Object> map = mapper.getQuestion(id);
				if (map == null) {
					throw BizException.withMessage("ID=" + id + "的试题不存在");
				}
				String clobToString = (String) map.get("data");
				String questionData = BaseUtil.isNotEmpty(map.get("data")) ? clobToString : "";
				Stlb questionType = Stlb.valueOf((String) map.get("type"));
				question = ModelHelper.convertObject(questionData);
				question.setId(id);
				question.setResolve(BaseUtil.isNotEmpty(map.get("resolve")) ? (String) map.get("resolve") : "");
				question.setAnswer(BaseUtil.isNotEmpty(map.get("answer")) ? (String) map.get("answer") : "");
				if (Stlb.SINGLECHOICE.equals(questionType)) {
					question = (QuestionSingleChoice) question;
				} else if (Stlb.MULTIPLECHOICE.equals(questionType)) {
					question = (QuestionMultipleChoice) question;
				} else if (Stlb.JUDGMENT.equals(questionType)) {
					question = (QuestionJudgment) question;
				} else if (Stlb.BLANKFILL.equals(questionType)) {//填空题答案需要单独设置
					question = (QuestionBlankFill) question;
					StringBuffer buffer = new StringBuffer();
					for(QBlank blank : ((QuestionBlankFill)question).getBlanks()){
						buffer.append(BaseUtil.trimBlank(BaseUtil.HTMLDecode(blank.getValue()))).append(";");
					}
					question.setAnswer(buffer.toString());
				} else if (Stlb.ESSAY.equals(questionType)) {
					question = (QuestionEssay) question;
				} else if (Stlb.MCJST.equals(questionType)) {
					question = (QuestionMcjst) question;
				} else if (Stlb.LST.equals(questionType)) {
					question = (QuestionLst) question;
				} else if (Stlb.JDT.equals(questionType)) {
					question = (QuestionJdt) question;
				}
			} catch (Exception e) {
			}
		}

		return question;
	}

	/**
	 * 人脸抓拍 url格式
	 */
	@Transactional
	public boolean savePhotoLog(String dataId, String newUrl) {
		ExamPhotoLog examPhotoLog = new ExamPhotoLog();
		examPhotoLog.setId(BaseUtil.generateId());
		examPhotoLog.setExamdataId(dataId);
		examPhotoLog.setUrl(BaseUtil.isEmpty(newUrl) ? null : newUrl);
		examPhotoLog.setLogTime(new Date());
		examPhotoLogMapper.insert(examPhotoLog);
		return true;
	}

	/**
	 * 考试抓拍存储 base64格式
	 */
	@Transactional
	public void logPhoto(String studentId,String paperId,String base64PhotoPath) throws Exception {
		ExamData checkExamData = this.getExamData(studentId, paperId);
		if (checkExamData == null) {
			throw BizException.withMessage("存储抓拍照片失败，因为考试记录不存在");
		}
		String photoPath = FileHelper.convertImgBase64SrcStrToUrl(base64PhotoPath);
		ExamPhotoLog examPhotoLog = new ExamPhotoLog();
		examPhotoLog.setId(BaseUtil.generateId2());
		examPhotoLog.setExamdataId(checkExamData.getId());
		examPhotoLog.setLogTime(new Date());
		examPhotoLog.setUrl(photoPath);
		examPhotoLogMapper.insert(examPhotoLog);
	}

	/**
	 * 人脸比对
	 */
	public boolean faceVerify(String paperId, String xmId, String newUrl, String studentId) {
		boolean boo = true;
		ZypxXm xm = xmMapper.selectById(xmId);
		// 获取考生的现场照片
		StudentInfo studentInfo = studentInfoMapper.getByStudentId(studentId);
		if (StringUtils.isEmpty(studentInfo.getStudentPhoto())) {
			throw BizException.withMessage("未获取到考生注册照片，请前往个人中心上传照片");
		}
		com.alibaba.fastjson.JSONObject jsonObject = FaceVerifyUtil.faceVerify(newUrl, studentInfo.getStudentPhoto());
		Double confidence = BaseUtil.parseDouble(jsonObject.get("confidence"));
		ExamFaceVerifyLog examFaceVerifyLog = new ExamFaceVerifyLog();
		examFaceVerifyLog.setId(BaseUtil.generateId());
		examFaceVerifyLog.setXmId(xmId);
		examFaceVerifyLog.setPaperId(paperId);
		examFaceVerifyLog.setStudentId(studentId);
		examFaceVerifyLog.setUrl(newUrl);
		examFaceVerifyLog.setResult(confidence);
		examFaceVerifyLog.setLogTime(new Date());
		// 通过项目id查询该项目下的人脸比对阈值
		Double verifyThreshold = xm.getVerifyThreshold();
		int i = confidence.compareTo(verifyThreshold);
		if (i > 0) {// 比对通过
			examFaceVerifyLog.setIsPassed(Constants.YES);// '1' 代表通过
		} else {
			examFaceVerifyLog.setIsPassed(Constants.NO);// '0' 代表不通过
			boo = false;
		}
		examFaceVerifyLogMapper.insert(examFaceVerifyLog);
		return boo;
	}

	/**
	 * 获取试卷
	 */
	public Map<String, Object> getPaperInfoById(String paperId){
		return mapper.getPaperInfoById(paperId);
	}

	/**
	 * 获取考试的剩余时间,时间单位：秒
	 */
	public int getUserPaperLeftTime(String paperId, String studentId) {
		ExamPaper examPaper = mapper.selectById(paperId);
		//作业的考试剩余时间=作业结束时间-当前时间
		//测验的考试剩余时间=考试时长-学员已经作答的时间
		if (examPaper.getCategory() == PaperCategory.PSZY || examPaper.getExamModel() == ExamModel.JZTY) {
			long endTime = examPaper.getEndTime().getTime();
			long nowTime = System.currentTimeMillis();
			return new Long((endTime - nowTime) / 1000).intValue();
		} else {
			ExamData examData = this.getExamData(studentId, paperId);
			// 获取学员开始考试的时间
			Date startExamTime = (examData == null || examData.getStartTime() == null) ? (new Date()) : examData.getStartTime();
			// 已经花费的时间，单位：秒
			int doneSeconds = new Long((System.currentTimeMillis() - startExamTime.getTime()) / 1000).intValue();
			return examPaper.getDuration() * 60 - doneSeconds;
		}
	}

	/**
	 * 保存试卷的答题卡
	 */
	@Transactional
	public ExamAnswerCard saveAnswerCard(String studentId, String paperId, String questionId, String url, String ip) throws Exception{
  		if(StringUtils.isEmpty(studentId)){
  			throw BizException.withMessage("考生ID不能为空");
  		}
  		if(StringUtils.isEmpty(paperId)){
  			throw BizException.withMessage("试卷ID不能为空");
  		}
  		if (StringUtils.isEmpty(url)){
  			throw BizException.withMessage("未获取到主观题答题卡图片");
  		}
  		String newUrl = url;
  		if (url.indexOf(";base64") > -1) {
  			newUrl = FileHelper.convertImgBase64SrcStrToUrl(url);
		}
  		ExamAnswerCard examAnswerCard = new ExamAnswerCard();
  		examAnswerCard.setId(BaseUtil.generateId2());
  		examAnswerCard.setCreateTime(new Date());
  		examAnswerCard.setQuestionId(questionId);
  		examAnswerCard.setExamdataId(this.getExamData(studentId, paperId).getId());
  		examAnswerCard.setIp(ip);
  		examAnswerCard.setStudentId(studentId);
  		examAnswerCard.setUrl(newUrl);
  		examAnswerCardMapper.insert(examAnswerCard);
  		return examAnswerCard;
  	}

	/**
	 * 删除试卷的答题卡
	 */
	@Transactional
	public void deleteAnswerCardById(String id) {
		ExamAnswerCard examAnswerCard = examAnswerCardMapper.selectById(id);
  		if (examAnswerCard == null) {
  			throw BizException.withMessage("选择的照片不存在");
  		}
  		else {
  			examAnswerCardMapper.deleteById(id);
  		}
	}

	/**
	 * 获取试卷的答题卡
	 */
	public List<ExamAnswerCard> getAnswerCard(String dataId){
		if (StringUtils.isEmpty(dataId)) {
			return Collections.EMPTY_LIST;
		}
		EntityWrapper<ExamAnswerCard> examAnswerCardWrapper = new EntityWrapper<ExamAnswerCard>();
		examAnswerCardWrapper.eq("examdata_id", dataId);
		List<ExamAnswerCard> examAnswerCards = examAnswerCardMapper.selectList(examAnswerCardWrapper);
		return examAnswerCards;
	}

	/**
	 * 获取作答记录，用于试卷的初始化
	 */
	public Map<String, Object> getLastSubmit4PaperInit(Paper paper, String studentId, String paperId) {
		ExamData examData = this.getExamData(studentId, paperId);
		JSONObject jsonObject = (examData != null && StringUtils.isNotEmpty(examData.getData()))
				? JSONObject.fromObject(examData.getData())
				: null;
		Map<String, Object> result = new HashMap<>();
		List<Map<String, Object>> answerInitItems = new ArrayList<Map<String, Object>>();
		if (jsonObject != null && jsonObject.size() > 0) {
			Iterator<?> iterator = jsonObject.keys();
			while (iterator.hasNext()) {
				String key = (String) iterator.next();
				String value = jsonObject.getString(key);
				for (PaperSection section : paper.getSections()) {
					for (Question question : section.getQuestions()) {
						if (question.getType() != Stlb.TT) {
							if (key.equals("Q-" + question.getId())) {
								Map<String, Object> map = new HashMap<String, Object>();
								map.put("name", "Q-" + question.getId());
								map.put("type", ModelHelper.getQuestionTypeCodeByNum(question.getType()));
								map.put("value", value.replace("\r", "\\r").replace("\n", "\\n"));
								answerInitItems.add(map);
							}
						}
						else {
							 List<Question> children = ((QuestionTt)question).getChildren();
							 for (Question child : children) {
								 if (key.equals("Q-" + child.getId())) {
									Map<String, Object> map = new HashMap<String, Object>();
									map.put("name", "Q-" + child.getId());
									map.put("type", ModelHelper.getQuestionTypeCodeByNum(child.getType()));
									map.put("value", value.replace("\r", "\\r").replace("\n", "\\n"));
									answerInitItems.add(map);
								 }
							 }

						}
					}
				}
			}
		}
		result.put("items", JSONArray.fromObject(answerInitItems).toString());
		long timestamp = (null == examData || null == examData.getSaveTime()) ? 0l : examData.getSaveTime().getTime();
		result.put("timestamp", timestamp);
		return result;
	}

}
