package com.xunw.jxjy.model.zypx.dto;

import java.math.BigDecimal;
import java.util.Date;
import com.xunw.jxjy.model.common.Courselearn;
import com.xunw.jxjy.model.common.courselive.Courselive;
import com.xunw.jxjy.model.common.coursems.CourseMs;
import com.xunw.jxjy.model.enums.TechLevel;

/**
 * 课程设置参数
 * <AUTHOR>
 */
public class XmCourseSettingItem {

	private String id;// 课程设置ID
	private String courseId;
	private String courseCode;
	private String courseName;
	private String teacherId;// 直播必须设置讲师ID
	private String teacherPhone;// 讲师手机号
	private String teacherName;// 面授 只有讲师的名称
	private String address;//上课地点
	private Double hours = 0d;//学时

	private Date startTime;//开始时间
	private Date endTime;//结束时间

	private String isMs;
	private String isLive;
	private String isCourseware;

	private Courselearn courselearn;//课件内容
	private Courselive courselive;//直播内容
	private CourseMs courseMs;//面授内容

	private String industryId; //行业
	private String professionId;//职业
	private TechLevel techLevel;//等级
	
	private String isShared;//是否是共享的直播课
	private String kjContent;//课件大字段
	private String liveContent;//直播大字段
	private String msContent;//面授大字段
	private String amount;//金额

	private String isOpenCourseTarget;//是否开放课程评价

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getCourseId() {
		return courseId;
	}
	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}
	public String getCourseCode() {
		return courseCode;
	}
	public void setCourseCode(String courseCode) {
		this.courseCode = courseCode;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getTeacherId() {
		return teacherId;
	}
	public void setTeacherId(String teacherId) {
		this.teacherId = teacherId;
	}
	public String getTeacherName() {
		return teacherName;
	}
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	public String getIsMs() {
		return isMs;
	}
	public void setIsMs(String isMs) {
		this.isMs = isMs;
	}
	public String getIsLive() {
		return isLive;
	}
	public void setIsLive(String isLive) {
		this.isLive = isLive;
	}
	public String getIsCourseware() {
		return isCourseware;
	}
	public void setIsCourseware(String isCourseware) {
		this.isCourseware = isCourseware;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public Double getHours() {
		return hours;
	}
	public void setHours(Double hours) {
		this.hours = hours;
	}
	public String getTeacherPhone() {
		return teacherPhone;
	}
	public void setTeacherPhone(String teacherPhone) {
		this.teacherPhone = teacherPhone;
	}
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public Courselearn getCourselearn() {
		return courselearn;
	}
	public void setCourselearn(Courselearn courselearn) {
		this.courselearn = courselearn;
	}
	public Courselive getCourselive() {
		return courselive;
	}
	public void setCourselive(Courselive courselive) {
		this.courselive = courselive;
	}
	public CourseMs getCourseMs() {
		return courseMs;
	}
	public void setCourseMs(CourseMs courseMs) {
		this.courseMs = courseMs;
	}
	public String getIndustryId() {
		return industryId;
	}
	public void setIndustryId(String industryId) {
		this.industryId = industryId;
	}
	public String getProfessionId() {
		return professionId;
	}
	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}
	public TechLevel getTechLevel() {
		return techLevel;
	}
	public void setTechLevel(TechLevel techLevel) {
		this.techLevel = techLevel;
	}

	public String getIsShared() {
		return isShared;
	}

	public void setIsShared(String isShared) {
		this.isShared = isShared;
	}

	public String getLiveContent() {
		return liveContent;
	}

	public void setLiveContent(String liveContent) {
		this.liveContent = liveContent;
	}

	public String getMsContent() {
		return msContent;
	}

	public void setMsContent(String msContent) {
		this.msContent = msContent;
	}

	public String getKjContent() {
		return kjContent;
	}

	public void setKjContent(String kjContent) {
		this.kjContent = kjContent;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getIsOpenCourseTarget() {
		return isOpenCourseTarget;
	}

	public void setIsOpenCourseTarget(String isOpenCourseTarget) {
		this.isOpenCourseTarget = isOpenCourseTarget;
	}
}
