package com.xunw.jxjy.model.inf.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.inf.entity.Hotel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface HotelMapper extends BaseMapper<Hotel>{

    public List <Map <String,Object>> pageQuery(Map<String, Object> condition, Page <?> page);

    List<Map<String, Object>> getByXmId(@Param(value = "id") String id);
}
