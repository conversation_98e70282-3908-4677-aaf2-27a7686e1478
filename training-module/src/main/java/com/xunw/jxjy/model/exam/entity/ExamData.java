package com.xunw.jxjy.model.exam.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.enums.Jjkhd;

/**
 *  考试记录表
 */
@TableName("biz_exam_data")
public class ExamData implements Serializable {
	
	private static final long serialVersionUID = -7984531628092707551L;

	//主键id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //试卷id
    @TableField("paper_id")
    private String paperId;

    //考生用户id
    @TableField("student_id")
    private String studentId;
    
    //开始时间
    @TableField("start_time")
    private Date startTime;
    
    //结束时间
    @TableField("end_time")
    private Date endTime;
    
    //ip地址
    @TableField("ip")
    private String ip;
    
    //分数
    @TableField("score")
    private Integer score;
    
    //答卷状态 枚举:0未交卷，1已交卷待批改，2已自动批改，3已手动批改 -1批改失败
    @TableField("status")
    private ExamDataStatus status;
    
    //答卷内容
    @TableField("data")
    private String data;
    
    //得分详情
    @TableField("score_detail")
    private String scoreDetail;
    
    //交卷客户端 枚举：客户端PC 手机端MOBILE
    @TableField("client")
    private Jjkhd client;
    
    //评卷老师id
    @TableField("mark_teacher_id")
    private String markTeacherId;
    
    //评卷时间
    @TableField("mark_time")
    private Date markTime;
    
    //生效时间
    @TableField("effect_time")
    private Date effectTime;
    
    //生效操作用户id
    @TableField("effect_user_id")
    private String effectUserId;
    
    //试卷最后一次答题保存时间
    @TableField("save_time")
    private Date saveTime;

    public ExamData() {
    	
    }
    
	public ExamData(String id, String paperId, String studentId, Date startTime, String ip) {
		super();
		this.id = id;
		this.paperId = paperId;
		this.studentId = studentId;
		this.startTime = startTime;
		this.ip = ip;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPaperId() {
		return paperId;
	}

	public void setPaperId(String paperId) {
		this.paperId = paperId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public ExamDataStatus getStatus() {
		return status;
	}

	public void setStatus(ExamDataStatus status) {
		this.status = status;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getScoreDetail() {
		return scoreDetail;
	}

	public void setScoreDetail(String scoreDetail) {
		this.scoreDetail = scoreDetail;
	}

	public Jjkhd getClient() {
		return client;
	}

	public void setClient(Jjkhd client) {
		this.client = client;
	}

	public String getMarkTeacherId() {
		return markTeacherId;
	}

	public void setMarkTeacherId(String markTeacherId) {
		this.markTeacherId = markTeacherId;
	}

	public Date getMarkTime() {
		return markTime;
	}

	public void setMarkTime(Date markTime) {
		this.markTime = markTime;
	}

	public Date getEffectTime() {
		return effectTime;
	}

	public void setEffectTime(Date effectTime) {
		this.effectTime = effectTime;
	}

	public String getEffectUserId() {
		return effectUserId;
	}

	public void setEffectUserId(String effectUserId) {
		this.effectUserId = effectUserId;
	}

	public Date getSaveTime() {
		return saveTime;
	}

	public void setSaveTime(Date saveTime) {
		this.saveTime = saveTime;
	}
}
