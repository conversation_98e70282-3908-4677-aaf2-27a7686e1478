package com.xunw.jxjy.model.inf.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.inf.entity.Hotel;
import com.xunw.jxjy.model.inf.mapper.HotelMapper;
import com.xunw.jxjy.model.inf.params.HotelQueryParams;

/**
 * 合作宾馆
 */

@Service
public class HotelService extends BaseCRUDService<HotelMapper, Hotel>{

	/**
	 * 查询
	 */
	public Page pageQuery(HotelQueryParams params) throws IOException, SQLException {
		Map <String, Object> condition = new HashMap <>();
		if (StringUtils.isNotEmpty(params.getKeyword())) {
		    condition.put("keyword", params.getKeyword());
		}
		if (StringUtils.isNotEmpty(params.getHostOrgId())) {
		    condition.put("hostOrgId", params.getHostOrgId());
		}
		List<Map<String, Object>> list = mapper.pageQuery(condition, params);
		params.setRecords(list);
		return params;
	}

	public List getAllList(HotelQueryParams params) throws IOException, SQLException {
		EntityWrapper<Hotel> wrapper = new EntityWrapper<>();
		wrapper.eq("host_org_id", params.getHostOrgId());
		return mapper.selectList(wrapper);
	}

	public List<Map<String, Object>> getByXmId(String id) {
		return mapper.getByXmId(id);
	}
}
