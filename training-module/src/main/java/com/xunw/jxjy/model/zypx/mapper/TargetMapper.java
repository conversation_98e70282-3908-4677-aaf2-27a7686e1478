package com.xunw.jxjy.model.zypx.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.zypx.entity.Target;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 评分指标
 */
public interface TargetMapper extends BaseMapper<Target> {
	

    List<Map<String,Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> getAllTargetByTypeIds(@Param("typeIds") List<String> typeIds, @Param("xmId") String xmId);
}
