package com.xunw.jxjy.model.inf.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.core.DBUtils;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.enums.Zt;
import com.xunw.jxjy.model.exam.entity.ExamPaper;
import com.xunw.jxjy.model.inf.entity.Course;
import com.xunw.jxjy.model.inf.entity.ProfessionAdCourse;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.entity.ZyjdProfessionAssign;
import com.xunw.jxjy.model.inf.entity.ZyjdProfessionAssignDetail;
import com.xunw.jxjy.model.inf.entity.ZyjdProfessionCondition;
import com.xunw.jxjy.model.inf.mapper.CourseMapper;
import com.xunw.jxjy.model.inf.mapper.ProfessionAdCourseMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionAssignDetailMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionAssignMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionConditionMapper;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.inf.params.ZyjdProfessionQueryParams;
import com.xunw.jxjy.model.zypx.dto.LevelCourseSettingItem;
import com.xunw.jxjy.model.zypx.dto.ProfessionCourseSettingItem;
import com.xunw.jxjy.model.zypx.dto.SkillCourseSettingDto;
import com.xunw.jxjy.model.zypx.dto.XmCourseSettingItem;
import com.xunw.jxjy.model.zypx.entity.ZypxXmCourseSetting;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.model.zypx.service.ZypxXmCourseSettingService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ZyjdProfessionService extends BaseCRUDService<ZyjdProfessionMapper, ZyjdProfession>{

	@Autowired
	private ZyjdProfessionAssignMapper assignMapper;
	@Autowired
	private ZyjdProfessionConditionMapper conditionMapper;
	@Autowired
	private ZyjdProfessionAssignDetailMapper zyjdProfessionAssignDetailMapper;
	@Autowired
	private ZyjdProfessionMapper professionMapper;
	@Autowired
	private CourseService courseService;
	@Autowired
	private CourseMapper courseMapper;
	@Autowired
	private ZypxXmCourseSettingService zypxXmCourseSettingService;
	@Autowired
	private ProfessionAdCourseMapper professionAdCourseMapper;
	@Autowired
	private ProfessionAdCourseService professionAdCourseService;

    public Page pageQuery(ZyjdProfessionQueryParams params) {
        List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

	public ZyjdProfession getByCode(String code) {
		EntityWrapper<ZyjdProfession> wrapper = new EntityWrapper<ZyjdProfession>();
		wrapper.eq("code", code);
		List<ZyjdProfession> list = mapper.selectList(wrapper);
		return list.size() > 0? list.get(0) : null;
	}

	/**
	 * 工种授权,多个主办单位的ID使用逗号分开
	 */
	@Transactional
	public void assign(String professionIds, String hostOrgIds, boolean isBatch) {
		String[] professionAaray = StringUtils.split(professionIds, ",");
		if (professionAaray == null || professionAaray.length == 0) {
			throw BizException.withMessage("请传工种ID");
		}
		String[] hostOrgIdArray = StringUtils.split(hostOrgIds, ",");
		if (isBatch) {//批量授权
			List<ZyjdProfessionAssign> professionAssigns = new ArrayList<ZyjdProfessionAssign>();
			List<ZyjdProfessionAssignDetail> zyjdProfessionAssignDetails = new ArrayList<>();
			for (String pid : professionAaray) {
				for (String hid : hostOrgIdArray) {
					EntityWrapper<ZyjdProfessionAssign> wrapper = new EntityWrapper();
					wrapper.eq("profession_id", pid);
					wrapper.eq("host_org_id", hid);
					if (assignMapper.selectCount(wrapper) > 0) {
						continue;
					}
					ZyjdProfessionAssign professionAssign = new ZyjdProfessionAssign();
					String assignId = BaseUtil.generateId2();
					professionAssign.setId(assignId);
					professionAssign.setHostOrgId(hid);
					professionAssign.setProfessionId(pid);
					professionAssigns.add(professionAssign);
					for (TechLevel techLevel : TechLevel.values()) {
						ZyjdProfessionAssignDetail professionAssignDetail = new ZyjdProfessionAssignDetail();
						professionAssignDetail.setId(BaseUtil.generateId());
						professionAssignDetail.setAssignId(assignId);
						professionAssignDetail.setTechLevel(techLevel);
						zyjdProfessionAssignDetails.add(professionAssignDetail);
					}
				}
			}
			DBUtils.insertBatch(zyjdProfessionAssignDetails, ZyjdProfessionAssignDetail.class);
			DBUtils.insertBatch(professionAssigns, ZyjdProfessionAssign.class);
		}
		else {
			//删除之前的授权关系
			EntityWrapper<ZyjdProfessionAssign> wrapper = new EntityWrapper();
			wrapper.in("profession_id", professionAaray);
			List<ZyjdProfessionAssign> zyjdProfessionAssigns = assignMapper.selectList(wrapper);
			if (!zyjdProfessionAssigns.isEmpty()) {
				//删除授权的等级
				zyjdProfessionAssignDetailMapper.delete(new EntityWrapper<ZyjdProfessionAssignDetail>()
						.in("assign_id", zyjdProfessionAssigns.stream().map(ZyjdProfessionAssign::getId).collect(Collectors.toList())));
			}
			//删除授权的关系
			assignMapper.delete(wrapper);
			List<ZyjdProfessionAssign> professionAssigns = new ArrayList<ZyjdProfessionAssign>();
			List<ZyjdProfessionAssignDetail> zyjdProfessionAssignDetails = new ArrayList<>();
			for (String pid : professionAaray) {
				for (String hid : hostOrgIdArray) {
					ZyjdProfessionAssign professionAssign = new ZyjdProfessionAssign();
					String assignId = BaseUtil.generateId2();
					professionAssign.setId(assignId);
					professionAssign.setHostOrgId(hid);
					professionAssign.setProfessionId(pid);
					professionAssigns.add(professionAssign);
					for (TechLevel techLevel : TechLevel.values()) {
						ZyjdProfessionAssignDetail professionAssignDetail = new ZyjdProfessionAssignDetail();
						professionAssignDetail.setId(BaseUtil.generateId());
						professionAssignDetail.setAssignId(assignId);
						professionAssignDetail.setTechLevel(techLevel);
						zyjdProfessionAssignDetails.add(professionAssignDetail);
					}
				}
			}
			DBUtils.insertBatch(zyjdProfessionAssignDetails, ZyjdProfessionAssignDetail.class);
			DBUtils.insertBatch(professionAssigns, ZyjdProfessionAssign.class);
		}
	}

	public List<String> getAssign(String professionId) {
		EntityWrapper<ZyjdProfessionAssign> wrapper = new EntityWrapper();
		wrapper.eq("profession_id", professionId);
		List<String> hostOrgIds = assignMapper.selectList(wrapper).stream().map(x->x.getHostOrgId()).
				collect(Collectors.toList());
		return hostOrgIds;
	}

	/**
	 *  设职业的承办单位
	 */
	@Transactional
	public void setReceiveOrg(String professionIds, String hostOrgId, String receiveOrgId) {
		String[] professionIdArray = StringUtils.split(professionIds,",");
		EntityWrapper<ZyjdProfessionAssign> wrapper = new EntityWrapper();
		wrapper.in("profession_id", Arrays.asList(professionIdArray));
		wrapper.eq("host_org_id", hostOrgId);
		List<ZyjdProfessionAssign> list = assignMapper.selectList(wrapper);
		for (ZyjdProfessionAssign zyjdProfessionAssign : list) {
			zyjdProfessionAssign.setReceiveOrgId(receiveOrgId);
		}
		DBUtils.updateAllColumnBatchById(list, ZyjdProfessionAssign.class);
	}

	/**
	 * 获取主办单位职业列表
	 */
	public Page getHostOrgProfessionList(ZyjdProfessionQueryParams params) {
		List<Map<String, Object>> list = mapper.getHostOrgProfessionList(params.getCondition(), params);
		params.setRecords(list);
		return params;
	}

	/**
	 * 查询报考条件
	 */
	public Page queryConditionList(ZyjdProfessionQueryParams params) {
		List<ZyjdProfessionCondition> conditions = mapper.getConditionByProfessionId(params.getCondition(),params);
		params.setRecords(conditions);
		return params;
	}


	/**
	 * 保存报考条件
	 */
	public void saveConditon(ZyjdProfessionCondition condition) {
		conditionMapper.insert(condition);
	}

	/**
	 * 获取报考条件
	 */
	public ZyjdProfessionCondition getConditonById(String id) {
		return conditionMapper.selectById(id);
	}

	/**
	 * 编辑报考条件
	 */
	public void editConditonById(ZyjdProfessionCondition condition) {
		conditionMapper.updateAllColumnById(condition);
	}

	/**
	 * 删除报考条件
	 */
	public void delConditonById(String id) {
		conditionMapper.deleteById(id);
	}

	/**
	 * 克隆报考条件
	 */
	@Transactional
	public void cloneCondition(String targetId, String sourceId) {
		List<ZyjdProfessionCondition> sourceCondition = conditionMapper.selectList(new EntityWrapper<ZyjdProfessionCondition>()
				.eq("profession_id", sourceId));
		if (CollectionUtils.isEmpty(sourceCondition)) {
			throw BizException.withMessage("源工种无报考条件，请重新选择");
		}
		//删除目标工种的所有报考条件
		if (StringUtils.isNotEmpty(targetId)) {
			conditionMapper.delete(new EntityWrapper<ZyjdProfessionCondition>()
					.eq("profession_id", targetId));
		}
		//克隆进目标工种
		List<ZyjdProfessionCondition> targetList = new ArrayList<>();
		for (ZyjdProfessionCondition source : sourceCondition) {
			ZyjdProfessionCondition target = new ZyjdProfessionCondition();
			BeanUtils.copyProperties(source, target);
			target.setId(BaseUtil.generateId());
			target.setProfessionId(targetId);
			targetList.add(target);
		}
		DBUtils.insertBatch(targetList, ZyjdProfessionCondition.class);
	}

	public SkillCourseSettingDto courseList(String hostOrgId, String professionId) {
		SkillCourseSettingDto skillCourseSettingDto = new SkillCourseSettingDto();
		ProfessionCourseSettingItem professionCourseSettingItem = new ProfessionCourseSettingItem();
		professionCourseSettingItem.setId(professionId);
		ZyjdProfession profession = professionMapper.selectById(professionId);
		professionCourseSettingItem.setName(profession.getName());
		// 查询职业下对应的课程数据
		List<XmCourseSettingItem> courseList = professionAdCourseService.getXmCourseSettingItems(hostOrgId, professionId, null);
		for (TechLevel level : TechLevel.values()) {
			LevelCourseSettingItem levelCourseSettingItem = new LevelCourseSettingItem();
			levelCourseSettingItem.setTechLevel(level);
			professionCourseSettingItem.getTechLevels().add(levelCourseSettingItem);
			// 过滤课程
			List<XmCourseSettingItem> list = courseList.stream()
					.filter(course -> professionId.equals(course.getProfessionId())
							&& level == course.getTechLevel())
					.collect(Collectors.toList());
			levelCourseSettingItem.setCourses(list);
		}
		skillCourseSettingDto.getProfessions().add(professionCourseSettingItem);
		return skillCourseSettingDto;
	}

	public void saveCourseSetting(SkillCourseSettingDto skillCourseSettingDto, String hostOrgId, String loginUserId) throws Exception {
		if (skillCourseSettingDto != null && skillCourseSettingDto.getProfessions().size() > 0) {
			for (ProfessionCourseSettingItem professionCourseSettingItem : skillCourseSettingDto.getProfessions()) {
				for (LevelCourseSettingItem levelCourseSettingItem : professionCourseSettingItem.getTechLevels()) {
					//保存当前职业、当前等级的课程设置
					ZyjdProfession profession = professionMapper.selectById(professionCourseSettingItem.getId());
					Set<String> courseNameSet = new HashSet<String>();
					List<ProfessionAdCourse> resultList = new ArrayList<>();
					for (XmCourseSettingItem item : levelCourseSettingItem.getCourses()) {
						// 1 课程处理
						if (courseNameSet.contains(item.getCourseName())) {
							throw BizException.withMessage("课程名称重复：" + item.getCourseName());
						} else {
							courseNameSet.add(item.getCourseName());
						}
						Course course = null;
						if (StringUtils.isEmpty(item.getCourseId())) {
							course = new Course();
							course.setId(BaseUtil.generateId2());
							course.setName(item.getCourseName());
							course.setCode(courseService.genCourseCode());
							course.setCreatorId(loginUserId);
							course.setCreateTime(new Date());
							course.setHours(item.getHours());
							course.setHostOrgId(hostOrgId);
							course.setStatus(Zt.OK);
							course.setKcxz(Constants.KCXZ.LI_LUN);
							courseMapper.insert(course);
							item.setCourseId(course.getId());
						} else {
							course = courseService.selectById(item.getCourseId());
							if(!Constants.YES.equals(item.getIsCourseware())) {
								course.setName(item.getCourseName());
								course.setHours(item.getHours());
								course.setUpdatorId(loginUserId);
								course.setUpdateTime(new Date());
								courseMapper.updateById(course);
							}
						}
						// 将面授、直播课时中讲师id、课程id写入
						if (Constants.YES.equals(item.getIsLive())) {
							if (item.getCourselive() == null || org.apache.commons.collections4.CollectionUtils.isEmpty(item.getCourselive().getChapters())) {
								throw BizException.withMessage("当前直播学习方式未设置授课内容");
							}
							item.getCourselive().getChapters().forEach(x -> x.getLessons().forEach(y -> {
								if (StringUtils.isEmpty(y.getJsid())) {
									y.setJsid(zypxXmCourseSettingService.saveTeacherUser(y.getJsxm(), y.getJssjh(), hostOrgId, loginUserId));
								}
								y.setKcid(item.getCourseId());
							}));

							// 取第一个课时的讲师作为直播课的讲师
							item.setTeacherId(item.getCourselive().getChapters().get(0).getLessons().get(0).getJsid());
							item.setTeacherName(item.getCourselive().getChapters().get(0).getLessons().get(0).getJsxm());
							item.setTeacherPhone(item.getCourselive().getChapters().get(0).getLessons().get(0).getJssjh());
							item.setAddress(item.getCourselive().getChapters().get(0).getLessons().get(0).getAddress());

							// 当前课程的开始时间取课时最早的开始时间，当期课程的结束时间取最晚的结束时间
							List<Date> startTimeList = item.getCourselive().getChapters().stream()
									.flatMap(x -> x.getLessons().stream()).map(y -> y.getKssj()).collect(Collectors.toList());
							startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
							item.setStartTime(startTimeList.get(0));
							List<Date> endTimeList = item.getCourselive().getChapters().stream()
									.flatMap(x -> x.getLessons().stream()).map(y -> y.getJssj()).collect(Collectors.toList());
							endTimeList = endTimeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
							item.setEndTime(endTimeList.get(0));
						}
						if (Constants.YES.equals(item.getIsMs())) {
							if (item.getCourseMs() == null || org.apache.commons.collections4.CollectionUtils.isEmpty(item.getCourseMs().getChapters())) {
								throw BizException.withMessage("当前面授学习方式未设置授课内容");
							}
							item.getCourseMs().getChapters().forEach(x -> x.getLessons().forEach(y -> {
								if (StringUtils.isEmpty(y.getTeacherId())) {
									y.setTeacherId(
											zypxXmCourseSettingService.saveTeacherUser(y.getTeacherName(), y.getTeacherPhone(), hostOrgId, loginUserId));
								}
							}));

							// 当前面授课程讲师取第一个课时的讲师
							item.setTeacherId(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherId());
							item.setTeacherName(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherName());
							item.setTeacherPhone(item.getCourseMs().getChapters().get(0).getLessons().get(0).getTeacherPhone());
							item.setAddress(item.getCourseMs().getChapters().get(0).getLessons().get(0).getAddress());

							// 当前课程的开始时间取课时最早的开始时间，当期课程的结束时间取最晚的结束时间
							List<Date> startTimeList = item.getCourseMs().getChapters().stream()
									.flatMap(x -> x.getLessons().stream()).map(y -> y.getStartTime()).collect(Collectors.toList());
							startTimeList = startTimeList.stream().sorted().collect(Collectors.toList());
							item.setStartTime(startTimeList.get(0));
							List<Date> endTimeList = item.getCourseMs().getChapters().stream().flatMap(x -> x.getLessons().stream())
									.map(y -> y.getEndTime()).collect(Collectors.toList());
							endTimeList = endTimeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
							item.setEndTime(endTimeList.get(0));
						}

						if(!Constants.YES.equals(item.getIsShared())){
							if ((Constants.YES.equals(item.getIsLive()) || Constants.YES.equals(item.getIsMs()))
									&& StringUtils.isEmpty(item.getTeacherId())) {
								throw BizException.withMessage(item.getCourseName() + "未设置讲师");
							}
						}
						if (Constants.YES.equals(item.getIsMs()) && StringUtils.isEmpty(item.getAddress())) {
							throw BizException.withMessage(item.getCourseName() + "面授课程未设置地点");
						}
//						//非技能类且是按课程购买，必须填写课程金额
//						if (Objects.equals(plan.getIsSkill(), Constants.YES)
//								&& zypxXm.getBuyType() == BuyType.COURSE
//								&& (item.getAmount() == null || !item.getAmount().matches("\\d+(\\.\\d{1,2})?"))) {
//							throw BizException.withMessage("请输入格式正确的课程金额");
//						}
						ProfessionAdCourse professionAdCourse = null;
						if (StringUtils.isEmpty(item.getId())) {
							professionAdCourse = new ProfessionAdCourse();
							professionAdCourse.setId(BaseUtil.generateId2());
							professionAdCourse.setCourseId(item.getCourseId());
							professionAdCourse.setStartTime(item.getStartTime());
							professionAdCourse.setEndTime(item.getEndTime());
							professionAdCourse.setAddress(item.getAddress());
							professionAdCourse.setTeacherId(item.getTeacherId());
							professionAdCourse.setTeacherName(item.getTeacherName());
							professionAdCourse.setIsCourseware(item.getIsCourseware());
							professionAdCourse.setIsMs(item.getIsMs());
							professionAdCourse.setIsLive(item.getIsLive());
							professionAdCourse.setCreatorId(loginUserId);
							professionAdCourse.setCreateTime(new Date());
							professionAdCourse.setIsShared(item.getIsShared());
							professionAdCourse.setAmount(item.getAmount());
							professionAdCourse.setIndustryId(profession.getIndustryId());
							professionAdCourse.setProfessionId(profession.getId());
							professionAdCourse.setTechLevel(levelCourseSettingItem.getTechLevel());
							professionAdCourse.setHostOrgId(hostOrgId);
							professionAdCourseMapper.insert(professionAdCourse);
							item.setId(professionAdCourse.getId());
						} else {
							professionAdCourse = professionAdCourseMapper.selectById(item.getId());
							professionAdCourse.setCourseId(item.getCourseId());
							professionAdCourse.setStartTime(item.getStartTime());
							professionAdCourse.setEndTime(item.getEndTime());
							professionAdCourse.setAddress(item.getAddress());
							professionAdCourse.setTeacherId(item.getTeacherId());
							professionAdCourse.setTeacherName(item.getTeacherName());
							if (Constants.YES.equals(professionAdCourse.getIsMs()) && !Constants.YES.equals(item.getIsMs())) {
								professionAdCourse.setMsContent(null);
							}
							professionAdCourse.setIsCourseware(item.getIsCourseware());
							professionAdCourse.setIsMs(item.getIsMs());
							professionAdCourse.setIsLive(item.getIsLive());
							professionAdCourse.setUpdateTime(new Date());
							professionAdCourse.setUpdatorId(loginUserId);
							professionAdCourse.setIsShared(item.getIsShared());
							professionAdCourse.setAmount(item.getAmount());
							professionAdCourse.setIndustryId(profession.getIndustryId());
							professionAdCourse.setProfessionId(profession.getId());
							professionAdCourse.setTechLevel(levelCourseSettingItem.getTechLevel());
							professionAdCourse.setHostOrgId(hostOrgId);
							professionAdCourseMapper.updateAllColumnById(professionAdCourse);
						}
						resultList.add(professionAdCourse);
						if(!Constants.YES.equals(item.getIsShared())){
							zypxXmCourseSettingService.saveLiveTeachPLan(item, loginUserId);// 非共享的直播的教学计划存储
						}
						// 面授的教学计划存储
						ZypxXmCourseSetting zypxXmCourseSetting = new ZypxXmCourseSetting();
						BeanUtils.copyProperties(professionAdCourse,zypxXmCourseSetting);
						zypxXmCourseSettingService.saveMsTeachPLan(zypxXmCourseSetting, item, loginUserId);
					}

				}
			}
		} else {
			throw BizException.withMessage("课程设置不能够为空");
		}
	}

	public void deleteById(String id) {
		professionAdCourseMapper.deleteById(id);
	}

}

