package com.xunw.jxjy.model.zypx.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021年03月30日
 * 评分指标
 */
@TableName("BIZ_TARGET_SETTING")
public class TargetSetting implements Serializable {

	private static final long serialVersionUID = -5249706086244048948L;
    //ID
	@TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

	//项目id
	@TableField("xm_id")
	private String xmId;

	//指标id
	@TableField("target_id")
	private String targetId;

	//指标设置类型 0项目评价指标 1课程评价指标
	@TableField("type")
	private Integer type;

	//创建时间
	@TableField("create_time")
	private Date createTime;

	//创建用户id
	@TableField("creator_id")
	private String creatorId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getTargetId() {
		return targetId;
	}

	public void setTargetId(String targetId) {
		this.targetId = targetId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}

    