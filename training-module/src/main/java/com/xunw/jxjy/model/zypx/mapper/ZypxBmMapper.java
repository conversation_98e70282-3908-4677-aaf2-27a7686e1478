package com.xunw.jxjy.model.zypx.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;

/**
 * 职业培训-培训计划-报名管理
 */
public interface ZypxBmMapper extends BaseMapper<ZypxBm> {

    List<Map<String,Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> getBmCourseByBmId(@Param("bmId") String bmId, Page<?> page);

    Integer getBmCountByPlanId(@Param("planId") String planId);

    Integer getBmCountByXmId(@Param("xmid") String xmId);

    Map<String,Object>  getInvaoiceDetail(Map<String, Object> condition);

    Map<String,Object> getStudentByBmId(@Param("bmId")String bmId);

    List<Map<String,Object>> areaApplyStatList(Map<String, Object> condition, Page<?> page);

	List<Map<String,Object>> getStudentTypeByXmId(@Param("xmId")String xmId);

    /**
     *项目费用统计数据查询
     */
    List<Map<String, Object>> statisticCost(Map<String, Object> condition);

    List<ZypxBm> getStudentBmInfo(@Param("name") String name, @Param("xmId") String xmId,@Param("currentHostOrgId") String currentHostOrgId);

    List<Map<String, Object>> getAllStudentByXmId(@Param(value = "id") String id, @Param(value = "keyword") String keyword);

    List<Map<String, Object>> getClassLeaderInfoByXmId(@Param(value = "id") String id, @Param(value = "keyword") String keyword);

    List<Map<String, Double>> xmBmCountStatistical(@Param(value = "hostOrgId") String hostOrgId, @Param(value = "yearList") Set<Integer> yearList);

    List<Map<String, Object>> areaEconomyStatistical(@Param(value = "hostOrgId") String hostOrgId);

    List<Map<String, Object>> threeTypeStatistical(@Param(value = "hostOrgId") String hostOrgId);

    boolean existsBmByStudentIdAndXmId(@Param(value = "studentId") String studentId, @Param(value = "id") String id);
}
