package com.xunw.jxjy.model.zypx.params;

import java.util.Date;

import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.XmStatus;

/**
 * 职业培训 项目表
 */
public class ZypxXmQueryParams extends BaseQueryParams {

	private static final long serialVersionUID = -5176576135756791236L;
	
	// 关键字 项目编号或者名称；项目分类统计里面该字段为项目分类关键字；承办单位统计该字段为单位关键字
	private String keyword;
	private String serialNumber;
	//项目状态
	private XmStatus status;
	private String typeId;
	private String planId;
	// 是否只查询已归档的项目，默认 0 否
	private String isOnlyArchive = Constants.NO;
	// 主办单位ID
	private String hostOrgId;
	private String xmId;
	private String leaderId;
	private String year;
	private String entrustOrgId;
	// 行业id
	private String industryId;
	// 职业id
	private String professionId;
	// 等级
	private String techLevel;
	// 承办单位
	private String receiveOrgId;
	private String orgId;
	//3 近三个月   6 近半年
	private String monthArea;
	//创建时间
	private Date createTime;
	
	/**
	 *  1近一周 
		2近一月 
		3近一季 
		4近半年 
		5近一年
	 */
	private String dateType;

	//标签 （培训班类型）
	private String classzType;

	//行业特色
	private String industryCategory;

	//所属基地
	private String baseId;
	
	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getReceiveOrgId() {
		return receiveOrgId;
	}

	public void setReceiveOrgId(String receiveOrgId) {
		this.receiveOrgId = receiveOrgId;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public XmStatus getStatus() {
		return status;
	}

	public void setStatus(XmStatus status) {
		this.status = status;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getIsOnlyArchive() {
		return isOnlyArchive;
	}

	public void setIsOnlyArchive(String isOnlyArchive) {
		this.isOnlyArchive = isOnlyArchive;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(String leaderId) {
		this.leaderId = leaderId;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getIndustryId() {
		return industryId;
	}

	public void setIndustryId(String industryId) {
		this.industryId = industryId;
	}

	public String getProfessionId() {
		return professionId;
	}

	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}

	public String getTechLevel() {
		return techLevel;
	}

	public void setTechLevel(String techLevel) {
		this.techLevel = techLevel;
	}

	public String getEntrustOrgId() {
		return entrustOrgId;
	}

	public void setEntrustOrgId(String entrustOrgId) {
		this.entrustOrgId = entrustOrgId;
	}

	public String getMonthArea() {
		return monthArea;
	}

	public void setMonthArea(String monthArea) {
		this.monthArea = monthArea;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDateType() {
		return dateType;
	}

	public void setDateType(String dateType) {
		this.dateType = dateType;
	}

	public String getClasszType() {
		return classzType;
	}

	public void setClasszType(String classzType) {
		this.classzType = classzType;
	}

	public String getIndustryCategory() {
		return industryCategory;
	}

	public void setIndustryCategory(String industryCategory) {
		this.industryCategory = industryCategory;
	}

	public String getBaseId() {
		return baseId;
	}

	public void setBaseId(String baseId) {
		this.baseId = baseId;
	}
}