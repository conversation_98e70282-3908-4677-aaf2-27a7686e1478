package com.xunw.jxjy.model.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;

/**
 * 第三方学习平台类型
 */
public enum OpenStudyType implements IEnum {

    XWD(1, "新闻道"),
    SQKD(2, "神奇的考点母题");

    private Integer code;
    private String name;
    private OpenStudyType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Serializable getValue() {
        return this.name();
    }
}
