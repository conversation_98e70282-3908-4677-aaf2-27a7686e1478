package com.xunw.jxjy.model.sys.params;

import com.xunw.jxjy.model.core.BaseQueryParams;
import com.xunw.jxjy.model.enums.Zt;

/**
 * 通知公告分类页面查询
 */
public class NoticeCategoryQueryParams extends BaseQueryParams {

	private static final long serialVersionUID = 5150518239478513550L;

	private String parentId;
	private String hostOrgId;
	private String id;
	private String name;
	private Zt status;

	public Zt getStatus() {
		return status;
	}

	public void setStatus(Zt status) {
		this.status = status;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
