package com.xunw.jxjy.model.common.service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.zypx.entity.StudentBmCourse;
import com.xunw.jxjy.model.zypx.mapper.StudentBmCourseMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.icbc.api.DefaultIcbcClient;
import com.icbc.api.IcbcApiException;
import com.icbc.api.IcbcConstants;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz;
import com.icbc.api.response.CardbusinessAggregatepayB2cOnlineOrderqryResponseV1;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.pay.WeiXinPayUtils;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.common.entity.CommOrder;
import com.xunw.jxjy.model.common.entity.CommOrderDetail;
import com.xunw.jxjy.model.common.mapper.CommOrderDetailMapper;
import com.xunw.jxjy.model.common.mapper.CommOrderMapper;
import com.xunw.jxjy.model.enums.FeeType;
import com.xunw.jxjy.model.enums.OrderStatus;
import com.xunw.jxjy.model.enums.PayPlatform;
import com.xunw.jxjy.model.enums.PayStatus;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.sys.entity.Bank;
import com.xunw.jxjy.model.sys.entity.BankConfig;
import com.xunw.jxjy.model.sys.mapper.BankConfigMapper;
import com.xunw.jxjy.model.sys.service.BankConfigService;
import com.xunw.jxjy.model.sys.service.BankService;
import com.xunw.jxjy.model.zyjd.entity.ZyjdBm;
import com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper;
import com.xunw.jxjy.model.zypx.entity.ZypxBm;
import com.xunw.jxjy.model.zypx.entity.ZypxRear;
import com.xunw.jxjy.model.zypx.mapper.ZypxBmMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxRearMapper;

import net.sf.json.JSONObject;

/**
 * 订单支付状态查询
 * <AUTHOR>
 */
@Service
public class CommOrderStatusQueryService {

	private static final String ALI_GATE_WAY = "https://openapi.alipay.com/gateway.do";

	private static final Logger LOGGER = LoggerFactory.getLogger(CommOrderStatusQueryService.class);
	
	@Autowired
	private CommOrderMapper commOrderMapper;
	@Autowired
	private CommOrderDetailMapper commOrderDetailMapper;
	@Autowired
	private BankConfigMapper bankConfigMapper;
	@Autowired
	private BankService bankService;
	@Autowired
	private ZyjdBmMapper zyjdBmMapper;
	@Autowired
	private ZypxBmMapper zypxBmMapper;
	@Autowired
	private ZypxRearMapper rearMapper;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private BankConfigService bankConfigService;
	@Autowired
	private StudentBmCourseMapper studentBmCourseMapper;

	/**
	 * 该接口提供所有支付宝订单的支付状态查询，商户可以通过该接口主动查询订单状态，完成下一步的业务逻辑
	 */

	@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
	public boolean isAliOrderPayed(CommOrder commOrder) {
		Bank bank = null;
		if (StringUtils.isNotEmpty(commOrder.getBankId())) {
			bank = bankService.selectById(commOrder.getBankId());
		}
		else {
			throw BizException.withMessage("订单的收款方未设置");
		}
		if (bank == null) {
			throw BizException.withMessage("订单的收款方在系统中不存在");
		}
    	EntityWrapper<BankConfig> wrapper = new EntityWrapper();
    	wrapper.eq("bank_id", bank.getId());
    	wrapper.eq("pay_plat", PayPlatform.ALI);
    	List<BankConfig> bankConfigs = bankConfigMapper.selectList(wrapper);
    	if (bankConfigs.size() == 0) {
    		throw BizException.withMessage("收款方【"+bank.getName()+"】尚未配置支付宝支付");
		}
    	BankConfig bankConfig = bankConfigs.get(0);
		JSONObject payConfig = JSONObject.fromObject(bankConfig.getPaySettting());
		if (commOrder.getPayMethod().name().startsWith(PayPlatform.ALI.name())) {
			String app_id = payConfig.getString("ali_app_id");
			String app_private_key = payConfig.getString("ali_merchant_private_key");
			String format = "json";
			String charset = "utf-8";
			String ali_public_key = payConfig.getString("ali_alipay_public_key");
			String sign_type = "RSA2";
			AlipayClient alipayClient = new DefaultAlipayClient(ALI_GATE_WAY, app_id, app_private_key, format, charset,
					ali_public_key, sign_type);
			AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
			Map<String, String> bizContent = new HashMap<>();
			bizContent.put("out_trade_no", commOrder.getId());
			request.setBizContent(JSONObject.fromObject(bizContent).toString());
			AlipayTradeQueryResponse response = null;
			try {
				response = alipayClient.execute(request);
			} catch (AlipayApiException e) {
				LOGGER.error("查询支付宝交易状态失败[AlipayApiException]:", e);
				throw BizException.withMessage("查询支付宝订单支付状态失败,订单编号:" + commOrder.getId());
			}
			if (response.isSuccess()) {
				if ("TRADE_SUCCESS".equals(response.getTradeStatus())
						|| "TRADE_FINISHED".equals(response.getTradeStatus())) {// 支付成功
					// 更新订单的交易流水号，支付时间，支付状态,付款账号，以及业务ID对应的后续逻辑
					String tradeNo = response.getTradeNo();
					Date payDate = response.getSendPayDate() != null ? response.getSendPayDate() : new Date();
					String fkzh = "ali_buyer_logon_id="
							+ (response.getBuyerLogonId() == null ? "" : response.getBuyerLogonId());// 买家账号
					commOrder.setTransNumber(tradeNo);
					commOrder.setPayTime(payDate);
					commOrder.setStatus(OrderStatus.YZF);
					commOrder.setPayAccount(fkzh);
					commOrder.setGetResultBy("QUERY[接口查询]");
					commOrderMapper.updateById(commOrder);
					// 订单支付成功之后完成后续的业务逻辑,
					afterOrderPaySuccess(commOrder);
					return true;
				}
			} else {
				// 未支付
				return false;
			}
		} else {
			throw BizException.withMessage("该订单不是支付宝订单,订单编号:" + commOrder.getId());
		}
		return false;
	}

	/**
	 * 该接口提供所有微信支付订单的查询，商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
	public boolean isWxOrderPayed(CommOrder commOrder) {
		Bank bank = null;
		if (StringUtils.isNotEmpty(commOrder.getBankId())) {
			bank = bankService.selectById(commOrder.getBankId());
		}
		else {
			throw BizException.withMessage("订单的收款方未设置");
		}
		if (bank == null) {
			throw BizException.withMessage("订单的收款方在系统中不存在");
		}
		
    	EntityWrapper<BankConfig> wrapper = new EntityWrapper();
    	wrapper.eq("bank_id", bank.getId());
    	wrapper.eq("pay_plat", PayPlatform.ALI);
    	List<BankConfig> bankConfigs = bankConfigMapper.selectList(wrapper);
    	if (bankConfigs.size() == 0) {
    		throw BizException.withMessage("收款方【"+bank.getName()+"】尚未配置微信支付");
		}
    	BankConfig bankConfig = bankConfigs.get(0);
		JSONObject payConfig = JSONObject.fromObject(bankConfig.getPaySettting());
		if (commOrder.getPayMethod().name().startsWith(PayPlatform.WX.name())) {
			String app_id = payConfig.getString("wx_app_id");
			String mch_id = payConfig.getString("wx_mch_id");
			String api_key = payConfig.getString("wx_api_key");
			String nonce_str = UUID.randomUUID().toString().replace("-", "");
			String sign = WeiXinPayUtils.geWeiXinOrderQuerySign(app_id, mch_id, commOrder.getId(), api_key, nonce_str);
			String xml = "<xml>" + "<appid>{0}</appid>" + "<mch_id>{1}</mch_id>" + "<nonce_str>{2}</nonce_str>"
					+ "<out_trade_no>{3}</out_trade_no>" + "<sign>{4}</sign>" + "</xml>";
			xml = MessageFormat.format(xml, app_id, mch_id, nonce_str, commOrder.getId(), sign);
			Map<String, Object> responseMap = WeiXinPayUtils
					.httpXmlRequest("https://api.mch.weixin.qq.com/pay/orderquery", "POST", xml);
			LOGGER.info("#query weixin order pay status:" + responseMap.toString());
			if (!"SUCCESS".equals(responseMap.get("return_code").toString())) {
				throw BizException.withMessage("获取微信订单交易状态失败,微信返回信息:" + responseMap.toString());
			}
			if ("OK".equals(responseMap.get("return_msg").toString())
					&& "SUCCESS".equals(responseMap.get("trade_state").toString())) {// 支付成功
				// 更新订单的交易流水号，支付时间，支付状态,付款账号，以及业务ID对应的后续逻辑
				String tradeNo = responseMap.get("transaction_id").toString();
				String timeEnd = responseMap.get("time_end").toString();
				Date payDate = null;
				payDate = DateUtils.parse(timeEnd, "yyyyMMddHHmmss");
			
				String fkzh = "wx_openid=" + responseMap.get("openid").toString();
				commOrder.setTransNumber(tradeNo);
				commOrder.setPayTime(payDate);
				commOrder.setStatus(OrderStatus.YZF);
				commOrder.setPayAccount(fkzh);
				commOrder.setGetResultBy("QUERY[接口查询]");
				commOrderMapper.updateById(commOrder);
				// 订单支付成功之后完成后续的业务逻辑,s
				afterOrderPaySuccess(commOrder);
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 该接口提供工行订单的查询，商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑
	 */
	@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
	public boolean isICBCOrderPayed(CommOrder commOrder) {
		JSONObject setting = bankConfigService.getICBCPayConfig(commOrder.getBankId());
		String APP_ID = BaseUtil.getStringValueFromJson(setting, "icbc_appid");// appid
		String MERCHANT_ID = BaseUtil.getStringValueFromJson(setting, "icbc_merchantid");// 商户编号
		String PUBLIC_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_public_key");// 公钥
		String PRIVATE_KEY = BaseUtil.getStringValueFromJson(setting, "icbc_private_key");// 私钥
		DefaultIcbcClient client = new DefaultIcbcClient(APP_ID,IcbcConstants.SIGN_TYPE_RSA2, PRIVATE_KEY, PUBLIC_KEY);
		CardbusinessAggregatepayB2cOnlineOrderqryRequestV1 request = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1();
		request.setServiceUrl("https://gw.open.icbc.com.cn/api/cardbusiness/aggregatepay/b2c/online/orderqry/V1");
		CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz bizContent = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz();
		request.setBizContent(bizContent);
        bizContent.setMer_id(MERCHANT_ID);
		bizContent.setOut_trade_no(commOrder.getUnionOrderNo());
		bizContent.setDeal_flag("0");
		bizContent.setIcbc_appid(APP_ID);
		CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 response;
		try {
			response = client.execute(request, BaseUtil.generateId2());//msgId消息通讯唯一编号，要求每次调用独立生成，APP级唯一
			if (response.getReturnCode() == 0) {
				if ("0".equals(response.getPay_status())) {
					String payTime = response.getPay_time();
					String orderId = response.getOrder_id();
					commOrder.setPayTime(DateUtils.parse(payTime, "yyyyMMddHHmmss"));
					commOrder.setStatus(OrderStatus.YZF);
					commOrder.setTransNumber(orderId);
					commOrder.setGetResultBy("NOTIFY[异步通知]");
					commOrderMapper.updateById(commOrder);
					// 订单支付成功之后完成后续的业务逻辑
					this.afterOrderPaySuccess(commOrder);
					return true;
				}
			}
		} catch (IcbcApiException e) {
			LOGGER.error("查询工行订单交易状态失败[IcbcApiException]:", e);
		}
		return false;
	}

	/**
	 * 订单支付成功之后的业务逻辑
	 */
	public void afterOrderPaySuccess(CommOrder commOrder) {
		System.out.println(commOrder.toString());
		EntityWrapper<CommOrderDetail> wrapper = new EntityWrapper<>();
		wrapper.eq("order_id", commOrder.getId());
		List<CommOrderDetail> commOrderDetails = commOrderDetailMapper.selectList(wrapper);
		for (CommOrderDetail orderDetail : commOrderDetails) {
			if (orderDetail.getFeeType() == FeeType.ZYJDZC) {// 职业鉴定注册费
				ZyjdBm zyjdBm = zyjdBmMapper.selectById(orderDetail.getYwId());
				zyjdBm.setPayStatus(PayStatus.YJ);
				zyjdBm.setPayType(Constants.ON_LINE);
				zyjdBm.setAmount(orderDetail.getAmount());
				zyjdBmMapper.updateById(zyjdBm);
			} else if (orderDetail.getFeeType() == FeeType.HOTEL_FEE) {// 住宿费用
				ZypxRear zypxRear = rearMapper.selectById(orderDetail.getYwId());
				zypxRear.setIsPayed(Constants.YES);
				zypxRear.setRearType(Constants.ON_LINE);
				zypxRear.setAmount(orderDetail.getAmount());
				rearMapper.updateById(zypxRear);
			}
			else if (orderDetail.getFeeType() == FeeType.XM_BM_FEE) {//培训项目报名费
				ZypxBm zypxBm = zypxBmMapper.selectById(orderDetail.getYwId());
				zypxBm.setIsPayed(Constants.YES);
				zypxBm.setPayType(Constants.ON_LINE);
				zypxBm.setPayedAmount(orderDetail.getAmount());
				zypxBmMapper.updateById(zypxBm);
			}
			else if (orderDetail.getFeeType() == FeeType.COURSE_BM_FEE) {//课程报名费
				StudentBmCourse studentBmCourse = studentBmCourseMapper.selectById(orderDetail.getYwId());
				studentBmCourse.setIsPayed(Constants.YES);
				studentBmCourseMapper.updateById(studentBmCourse);
			}
		}
	}
	
	/**
	 * 根据订单编号查询订单详情
	 */
	public Map<String, Object> getOrderDetailByOrderNo(String orderNo){
		CommOrder commOrder = commOrderMapper.selectById(orderNo);
		EntityWrapper<CommOrderDetail> wrapper = new EntityWrapper();
		wrapper.eq("order_id", orderNo);
		List<CommOrderDetail> commOrderDetails = commOrderDetailMapper.selectList(wrapper);
		List<Map<String, Object>> details = new ArrayList<Map<String,Object>>();
		for (CommOrderDetail orderDetail : commOrderDetails) {
			String studentId = null;
			if (orderDetail.getFeeType() == FeeType.ZYJDZC) {// 职业鉴定注册费
				ZyjdBm zyjdBm = zyjdBmMapper.selectById(orderDetail.getYwId());
				studentId = zyjdBm.getStudentId();
			} else if (orderDetail.getFeeType() == FeeType.HOTEL_FEE) {// 住宿费用
				ZypxRear zypxRear = rearMapper.selectById(orderDetail.getYwId());
				studentId = zypxRear.getStudentId();
			} else if (orderDetail.getFeeType() == FeeType.XM_BM_FEE) {//项目报名费
				ZypxBm zypxBm = zypxBmMapper.selectById(orderDetail.getYwId());
				if (Objects.nonNull(zypxBm)) {
					studentId = zypxBm.getStudentId();
				}
			} else if (orderDetail.getFeeType() == FeeType.COURSE_BM_FEE) {//课程报名费
				StudentBmCourse studentBmCourse = studentBmCourseMapper.selectById(orderDetail.getYwId());
				studentId = studentBmCourse.getStudentId();
			}
			if (StringUtils.isNotEmpty(studentId)) {
				StudentInfo studentInfo = studentInfoService.getByStudentId(studentId);
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("name", studentInfo.getName());
				map.put("sfzh", studentInfo.getSfzh());
				map.put("feeType", orderDetail.getFeeType().getName());
				map.put("amount", orderDetail.getAmount());
				details.add(map);
			}
		}
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("order", commOrder);
		result.put("details", details);
		return result;
	
	}

	/**
	 * 标记退费
	 */
	public void refund(String id, String remark, User user) {
		CommOrder commOrder = commOrderMapper.selectById(id);
		commOrder.setStatus(OrderStatus.YTF);
		commOrder.setRefundRemark(user.getName() + " " + DateUtils.format(new Date()) + " " + remark);
		commOrderMapper.updateById(commOrder);
	}
}
