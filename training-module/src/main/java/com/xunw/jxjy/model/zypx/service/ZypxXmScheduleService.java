package com.xunw.jxjy.model.zypx.service;

import java.io.File;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.enums.ScheduleSign;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.entity.ZypxXmSchedule;
import com.xunw.jxjy.model.zypx.mapper.ZypxXmScheduleMapper;
import com.xunw.jxjy.model.zypx.params.ZypxXmCommonQueryParams;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

@Service
public class ZypxXmScheduleService extends BaseCRUDService<ZypxXmScheduleMapper, ZypxXmSchedule> {

	@Autowired
	private AttConfig attConfig;

	@Transactional
	public Map<String, Object> toImport(String xmId, MultipartFile file, String userId) throws Exception {
		if (BaseUtil.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "course_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target, new String[] { "SCHEDULE_TIME", "ARRANGE" });
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}

		int row = 0;
		int cwCou = 0;
		Date atDate = new Date();
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			String scheduleTimeStr = StringUtils.trimToNull(map.get("SCHEDULE_TIME"));
			if (StringUtils.isEmpty(scheduleTimeStr)) {
				log.append("<br>");
				String msg = "第" + row + "行日程时间为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			Date scheduleTime = DateUtils.parse(scheduleTimeStr);
			if (scheduleTime == null) {
				log.append("<br>");
				String msg = "第" + row + "行日程时间不合法。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}

			String arrange = StringUtils.trimToNull(map.get("ARRANGE"));
			if (StringUtils.isEmpty(arrange)) {
				log.append("<br>");
				String msg = "第" + row + "行日程安排为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			mapper.insert(new ZypxXmSchedule(BaseUtil.generateId(), xmId, scheduleTime, arrange, userId, atDate));
		}
		int sucCount = (list.size() - 1) - cwCou;
		// 提示总共多少条，导入成功多少条，重复数据多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (list.size() - 1) + "条，导入成功" + sucCount + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	public List<ZypxXmSchedule> list(ZypxXmCommonQueryParams params) {
		EntityWrapper<ZypxXmSchedule> wrapper = new EntityWrapper<>();
		if (BaseUtil.isNotEmpty(params.getXmId())) {
			wrapper.eq("xm_id", params.getXmId());
		}
		wrapper.orderBy("schedule_time", true);
		return mapper.selectList(wrapper);
	}

	public Map<String, List<ZypxXmSchedule>> groupList(ZypxXmCommonQueryParams params) {
		// 按照年月日分组
		List<ZypxXmSchedule> schedules = this.list(params);
		schedules.forEach(x -> {
			Date scheduleTime = x.getScheduleTime();
			x.setScheduleYMD(DateUtils.format(scheduleTime, "yyyy-MM-dd"));
			x.setScheduleHM(DateUtils.format(scheduleTime, "HH:mm"));
			// 在当前时间之前：已结束，业务层计算当前
			x.setScheduleSign(scheduleTime.before(new Date()) ? ScheduleSign.DONE : ScheduleSign.NOT_START);
		});
		schedules.stream().filter(x -> x.getScheduleSign() == ScheduleSign.DONE).reduce((first, second) -> second)
				.ifPresent(y -> y.setScheduleSign(ScheduleSign.AT));
		return schedules.stream().collect(Collectors.groupingBy(ZypxXmSchedule::getScheduleYMD));
	}

	public void toExport(ZypxXmCommonQueryParams params, OutputStream os) throws Exception {
		Map<String, List<ZypxXmSchedule>> map = this.groupList(params);
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("项目日程安排表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "日期", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 15);
			ws.addCell(new Label(i, row, "日程安排", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 50);
		}
		row = 1;
		for (String scheduleYMD : map.keySet()) {
			int startRow = row;
			for (ZypxXmSchedule zypxXmSchedule : map.get(scheduleYMD)) {
				int col = 0;
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(scheduleYMD),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(zypxXmSchedule.getScheduleHM()),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(zypxXmSchedule.getArrange()),
						OfficeToolExcel.getNormolCell()));
				row++;
			}
			// 跨行
			ws.mergeCells(0, startRow, 0, row);
		}
		wwb.write();
		wwb.close();
		os.flush();
	}

}