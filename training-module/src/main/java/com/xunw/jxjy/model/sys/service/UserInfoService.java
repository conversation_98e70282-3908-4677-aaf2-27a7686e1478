package com.xunw.jxjy.model.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.jxjy.common.config.AttConfig;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.common.utils.DateUtils;
import com.xunw.jxjy.common.utils.JSONUtils;
import com.xunw.jxjy.model.enums.*;
import com.xunw.jxjy.model.inf.entity.ZyjdProfession;
import com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper;
import com.xunw.jxjy.model.inf.params.TeacherQueryParams;
import com.xunw.jxjy.model.sys.entity.User;
import com.xunw.jxjy.model.sys.entity.User2Role;
import com.xunw.jxjy.model.sys.mapper.User2RoleMapper;
import com.xunw.jxjy.model.sys.mapper.UserMapper;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import jxl.read.biff.BiffException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.sys.entity.UserInfo;
import com.xunw.jxjy.model.sys.mapper.UserInfoMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserInfoService extends BaseCRUDService<UserInfoMapper, UserInfo>{

	@Autowired
	private UserMapper userMapper;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private User2RoleMapper user2RoleMapper;
	@Autowired
	private ZyjdProfessionMapper zyjdProfessionMapper;

	public UserInfo getByUserId(String userId) {
		return	mapper.getByUserId(userId);
	}

	public Object findTeachers(TeacherQueryParams params) {
		List<Map<String, Object>> list = mapper.findTeachers(params.getCondition(), params);
		// 可评审工种显示
		for (Map<String, Object> map : list) {
			Object profession_ids = map.get("professionIds");
			if (profession_ids != null) {
				String profession_ids_str = profession_ids.toString();
				String[] profession_ids_arr = profession_ids_str.split(",");
				if (profession_ids_arr.length > 0) {
					List<ZyjdProfession> zyjdProfessions = zyjdProfessionMapper.selectBatchIds(Arrays.asList(profession_ids_arr));
					String professionNames = zyjdProfessions.stream().map(ZyjdProfession::getName).collect(Collectors.joining(","));
					map.put("professionNames", professionNames);
				}
			}
		}
		params.setRecords(list);
		return params;

	}

	/**
	 * 添加师资信息
	 */
	@Transactional
	public Object addTeacher(TeacherQueryParams params,String creatorId,String hostOrgId) {
		if (StringUtils.isNotEmpty(params.getRecommend()) && Integer.parseInt(params.getRecommend()) > 5 || Integer.parseInt(params.getRecommend()) < 0){
			throw BizException.withMessage("推荐指数范围必须为0~5");
		}
		String userId = BaseUtil.generateId();
		User user = new User();
		user.setId(userId);
		user.setName(params.getName());
		user.setMobile(params.getMobile());
		TeacherQueryParams teacherQueryParams = new TeacherQueryParams();
		if (StringUtils.isNotEmpty(params.getMobile())){
			teacherQueryParams.setKeyword(params.getMobile());
			teacherQueryParams.setSize(Integer.MAX_VALUE);
			List<Map<String, Object>> teachers = mapper.findTeachers(teacherQueryParams.getCondition(), teacherQueryParams);
			if (CollectionUtils.isNotEmpty(teachers)){
				throw BizException.withMessage("用户已存在");
			}
			user.setUsername(params.getMobile());
		} else {
			user.setUsername("T"+System.currentTimeMillis()+BaseUtil.generateRandomEnglishLetterString(1));
		}
		user.setSfzh(params.getSfzh());
		user.setStatus(params.getStatus());
		user.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
		user.setCreateTime(DateUtils.now());
		user.setCreatorId(creatorId);
		user.setOrgId(hostOrgId);
		userMapper.insert(user);
		UserInfo userInfo = new UserInfo();
		userInfo.setBrief(params.getBrief());
		userInfo.setId(BaseUtil.generateId());
		userInfo.setUserId(userId);
		userInfo.setGender(params.getGender());
		userInfo.setCreateTime(DateUtils.now());
		userInfo.setCreatorId(creatorId);
		userInfo.setEducation(params.getEducation());
		userInfo.setZw(params.getZw());
		userInfo.setZc(params.getZc());
		userInfo.setIsOut(params.getIsOut());
		userInfo.setWorkUnit(params.getWorkUnit());
		userInfo.setEmail(params.getEmail());
		userInfo.setPhoto(params.getPhoto());
		userInfo.setOfficeTel(params.getOfficeTel());
		userInfo.setRecommend(params.getRecommend());
		userInfo.setCourses(params.getCourses());
		userInfo.setStudyDirection(params.getStudyDirection());
		userInfo.setZcPhoto(params.getZcPhoto());
		userInfo.setBankCardNo(params.getBankCardNo());
		userInfo.setBank(params.getBank());
		userInfo.setBankCardPhoto(params.getBankCardPhoto());
		userInfo.setTeacherTypeId(params.getTypeId());
		userInfo.setSpecialityType(params.getSpecialityType());
		userInfo.setProfessionIds(params.getProfessionIds());
		userInfo.setCategory(params.getCategory());
		mapper.insert(userInfo);
		User2Role user2Role = new User2Role();
		user2Role.setId(BaseUtil.generateId());
		user2Role.setUserId(userId);
		user2Role.setRole(Role.TEACHER);
		user2Role.setCreateTime(DateUtils.now());
		user2Role.setCreatorId(creatorId);
		user2RoleMapper.insert(user2Role);
		return true;
	}

	/**
	 * 修改师资信息
	 */
	@Transactional
	public Object editTeacher(TeacherQueryParams params, String loginUserId) {
		if (StringUtils.isNotEmpty(params.getRecommend()) && (Integer.parseInt(params.getRecommend()) > 5 || Integer.parseInt(params.getRecommend()) < 0)){
			throw BizException.withMessage("推荐指数范围必须为0~5");
		}
		User user = userMapper.selectById(params.getId());
		if (BaseUtil.isEmpty(user)){
			throw BizException.withMessage("用户不存在");
		}
		if(StringUtils.isNotEmpty(user.getMobile()) && !user.getMobile().equals(params.getMobile())){
			TeacherQueryParams teacherQueryParams = new TeacherQueryParams();
			teacherQueryParams.setKeyword(params.getMobile());
			teacherQueryParams.setSize(Integer.MAX_VALUE);
			List<Map<String, Object>> teachers = mapper.findTeachers(teacherQueryParams.getCondition(), teacherQueryParams);
			if (CollectionUtils.isNotEmpty(teachers)){
				throw BizException.withMessage("手机号已存在");
			}
		}
		user.setName(params.getName());
		user.setMobile(params.getMobile());
		user.setSfzh(params.getSfzh());
		user.setStatus(params.getStatus());
		user.setUpdateTime(DateUtils.now());
		user.setUpdatorId(loginUserId);
		userMapper.updateById(user);

		EntityWrapper<UserInfo> entityWrapper = new EntityWrapper<>();
		entityWrapper.eq("user_id",params.getId());
		List<UserInfo> userInfos = mapper.selectList(entityWrapper);
		if (CollectionUtils.isEmpty(userInfos)){
			UserInfo userInfo = new UserInfo();
			userInfo.setId(BaseUtil.generateId());
			userInfo.setUserId(params.getId());
			userInfo.setBrief(params.getBrief());
			userInfo.setGender(params.getGender());
			userInfo.setCreateTime(DateUtils.now());
			userInfo.setCreatorId(loginUserId);
			userInfo.setEducation(params.getEducation());
			userInfo.setZw(params.getZw());
			userInfo.setZc(params.getZc());
			userInfo.setIsOut(params.getIsOut());
			userInfo.setWorkUnit(params.getWorkUnit());
			userInfo.setEmail(params.getEmail());
			userInfo.setPhoto(params.getPhoto());
			userInfo.setOfficeTel(params.getOfficeTel());
			userInfo.setRecommend(params.getRecommend());
			userInfo.setCourses(params.getCourses());
			userInfo.setStudyDirection(params.getStudyDirection());
			userInfo.setZcPhoto(params.getZcPhoto());
			userInfo.setBankCardNo(params.getBankCardNo());
			userInfo.setBank(params.getBank());
			userInfo.setBankCardPhoto(params.getBankCardPhoto());
			userInfo.setIsShowPortal(params.getIsShowPortal());
			userInfo.setTeacherTypeId(params.getTypeId());
			userInfo.setSpecialityType(params.getSpecialityType());
			userInfo.setProfessionIds(params.getProfessionIds());
			userInfo.setCategory(params.getCategory());
			mapper.insert(userInfo);
		} else {
			UserInfo userInfo = userInfos.get(0);
			userInfo.setBrief(params.getBrief());
			userInfo.setGender(params.getGender());
			userInfo.setUpdateTime(DateUtils.now());
			userInfo.setUpdatorId(loginUserId);
			userInfo.setEducation(params.getEducation());
			userInfo.setZw(params.getZw());
			userInfo.setZc(params.getZc());
			userInfo.setIsOut(params.getIsOut());
			userInfo.setWorkUnit(params.getWorkUnit());
			userInfo.setEmail(params.getEmail());
			userInfo.setPhoto(params.getPhoto());
			userInfo.setOfficeTel(params.getOfficeTel());
			userInfo.setRecommend(params.getRecommend());
			userInfo.setCourses(params.getCourses());
			userInfo.setStudyDirection(params.getStudyDirection());
			userInfo.setZcPhoto(params.getZcPhoto());
			userInfo.setBankCardNo(params.getBankCardNo());
			userInfo.setBank(params.getBank());
			userInfo.setBankCardPhoto(params.getBankCardPhoto());
			userInfo.setIsShowPortal(params.getIsShowPortal());
			userInfo.setTeacherTypeId(params.getTypeId());
			userInfo.setSpecialityType(params.getSpecialityType());
			userInfo.setProfessionIds(params.getProfessionIds());
			userInfo.setCategory(params.getCategory());
			mapper.updateById(userInfo);
		}
		return true;
	}

	/**
	 * 师资详情
	 */
	public Object teacherDetail(TeacherQueryParams params) {
		User user = userMapper.selectById(params.getId());
		EntityWrapper<UserInfo> entityWrapper = new EntityWrapper<>();
		entityWrapper.eq("user_id",params.getId());
		List<UserInfo> userInfos = mapper.selectList(entityWrapper);
		if (CollectionUtils.isEmpty(userInfos)){
			return user;
		}else {
			HashMap<String, Object> result = new HashMap<>();
			UserInfo userInfo = userInfos.get(0);
			result.putAll(JSONUtils.convertBeanToMap(userInfo));
			result.putAll(JSONUtils.convertBeanToMap(user));
			if (StringUtils.isNotEmpty(userInfo.getProfessionIds())) {
				String[] profession_ids_arr = userInfo.getProfessionIds().split(",");
				if (profession_ids_arr.length > 0) {
					List<ZyjdProfession> zyjdProfessions = zyjdProfessionMapper.selectBatchIds(Arrays.asList(profession_ids_arr));
					String professionNames = zyjdProfessions.stream().map(ZyjdProfession::getName).collect(Collectors.joining(","));
					result.put("professionNames", professionNames);
				}
			}
			return result;
		}
	}

	//考评员导入
	@Transactional
	public Object batchImport(MultipartFile file,String typeId, String hostOrgId, String loginUserId) throws IOException, BiffException {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "teacher_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "NAME","SFZH","PROFESSION","MOBILE","WORKUNIT","BANK_CARD_NO", "BANK"});
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		List<String> sfzList = new ArrayList<String>();
		List<String> mobileList = new ArrayList<String>();

		int row = 0;
		int existsCou = 0;//与系统重复的数据
		int cfCou = 0;//Excel重复数据
		int cwCou = 0;//Excel错误数据
		int success = 0;//注册成功的数据
		for (Map<String, String> map : list) {
			row++;
			// 第一行是标题，放过
			if (row < 2) {
				continue;
			}
			// a、姓名为必填项。
			String name = StringUtils.trimToNull(map.get("NAME"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);

				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String sfzh = StringUtils.trimToNull(map.get("SFZH"));
			if (StringUtils.isNotEmpty(sfzh)) {
				TeacherQueryParams params = new TeacherQueryParams();
				params.setKeyword(sfzh);
				params.setSize(Integer.MAX_VALUE);
				List<Map<String, Object>> teachers = mapper.findTeachers(params.getCondition(), params);
				if (sfzList.contains(sfzh)) {
					String msg = "<span style=\"color:red\">第" + row + "行身份证在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				} else if (CollectionUtils.isNotEmpty(teachers)){
					String msg = "<span style=\"color:red\">第" + row + "行身份证在系统中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					existsCou++;
					continue;
				} else {
					sfzList.add(sfzh);
				}
			}
			String workunit = StringUtils.trimToNull(map.get("WORKUNIT"));
			String mobile = StringUtils.trimToNull(map.get("MOBILE"));
			if (StringUtils.isNotEmpty(mobile)) {
				if(!mobile.matches("^((13|14|15|16|17|18|19)[0-9]{1}\\d{8})$")){
					String msg = "<span style=\"color:red\">第" + row + "行手机号格式错误，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cwCou++;
					continue;
				}
				TeacherQueryParams params = new TeacherQueryParams();
				params.setKeyword(mobile);
				params.setSize(Integer.MAX_VALUE);
				List<Map<String, Object>> teachers = mapper.findTeachers(params.getCondition(), params);
				if (mobileList.contains(mobile)) {
					String msg = "<span style=\"color:red\">第" + row + "行手机号在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				} else if (CollectionUtils.isNotEmpty(teachers)){
					String msg = "<span style=\"color:red\">第" + row + "行手机号在系统中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					existsCou++;
					continue;
				}

				else {
					mobileList.add(mobile);
				}
			}
			String bankCardNo = StringUtils.trimToNull(map.get("BANK_CARD_NO"));
			String bank = StringUtils.trimToNull(map.get("BANK"));
			String profession = StringUtils.trimToNull(map.get("PROFESSION"));
			String professionIds = null;
			if (StringUtils.isNotEmpty(profession)) {
				String[] professionNames = profession.split(",");
				List<String> zyjdProfessionIds = zyjdProfessionMapper.selectIdsByNames(hostOrgId, Arrays.asList(professionNames));
				professionIds = String.join(",", zyjdProfessionIds);
			}
			//写入用户表
			User user = new User();
			String userId = BaseUtil.generateId2();
			user.setId(userId);
			user.setName(name);
			user.setSfzh(sfzh);
			user.setMobile(mobile);
			user.setStatus(Zt.OK);
			if (StringUtils.isEmpty(mobile)){
				user.setUsername("T"+System.currentTimeMillis()+BaseUtil.generateRandomEnglishLetterString(1));
			}else {
				user.setUsername(mobile);
			}
			user.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
			user.setCreateTime(DateUtils.now());
			user.setCreatorId(loginUserId);
			user.setOrgId(hostOrgId);
			userMapper.insert(user);
			//权限设置
			User2Role user2Role = new User2Role();
			user2Role.setId(BaseUtil.generateId());
			user2Role.setUserId(userId);
			user2Role.setRole(Role.TEACHER);
			user2Role.setCreateTime(DateUtils.now());
			user2Role.setCreatorId(loginUserId);
			user2RoleMapper.insert(user2Role);
			// 写入用户信息表
			UserInfo userInfo = new UserInfo();
			userInfo.setId(BaseUtil.generateId2());
			userInfo.setUserId(userId);
			userInfo.setCreateTime(DateUtils.now());
			userInfo.setCreatorId(loginUserId);
			userInfo.setWorkUnit(workunit);
			userInfo.setProfessionIds(professionIds);
			userInfo.setBankCardNo(bankCardNo);
			userInfo.setBank(bank);
			userInfo.setTeacherTypeId(typeId);
			mapper.insert(userInfo);
			success++;
		}
		StringBuffer message = new StringBuffer(
				"<div>总共" + (row - 1) + "条数据，批量注册成功" + success + "条，Excel重复数据" + cfCou + "条，错误数据" + cwCou + "条，系统中已经存在数据"+existsCou+"条</div>");
		message.append(log);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("code", 0);
		map.put("message", message);
		return map;
	}

	//师资导入
	@Transactional(rollbackFor = Exception.class)
	public Object teacherImport(MultipartFile file, String typeId, String hostOrgId, String loginUserId) throws IOException, BiffException {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "teacher_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "name","sfzh","mobile", "email", "gender", "education", "workUnit", "zw", "zc", "isOut", "officeTel", "courses" ,"studyDirection","specialityType", "brief", "category"});
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}
		List<String> sfzList = new ArrayList<String>();
		List<String> mobileList = new ArrayList<String>();

		int row = 0;
		int existsCou = 0;//与系统重复的数据
		int cfCou = 0;//Excel重复数据
		int cwCou = 0;//Excel错误数据
		int success = 0;//注册成功的数据
		for (Map<String, String> map : list) {
			row++;
			// 第一行是标题，放过
			if (row < 2) {
				continue;
			}
			// a、姓名为必填项。
			String name = StringUtils.trimToNull(map.get("name"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行姓名为空，忽略这条数据。";
				log.append(msg);

				// 标记错误数据+1
				cwCou++;
				continue;
			}
			String sfzh = StringUtils.trimToNull(map.get("sfzh"));
			if (StringUtils.isNotEmpty(sfzh)) {
				TeacherQueryParams params = new TeacherQueryParams();
				params.setKeyword(sfzh);
				params.setSize(Integer.MAX_VALUE);
				List<Map<String, Object>> teachers = mapper.findTeachers(params.getCondition(), params);
				if (sfzList.contains(sfzh)) {
					String msg = "<span style=\"color:red\">第" + row + "行身份证在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				} else if (CollectionUtils.isNotEmpty(teachers)){
					String msg = "<span style=\"color:red\">第" + row + "行身份证在系统中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					existsCou++;
					continue;
				} else {
					sfzList.add(sfzh);
				}
			}
			String mobile = StringUtils.trimToNull(map.get("mobile"));
			if (StringUtils.isNotEmpty(mobile)) {
				if(!mobile.matches("^((13|14|15|16|17|18|19)[0-9]{1}\\d{8})$")){
					String msg = "<span style=\"color:red\">第" + row + "行手机号格式错误，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cwCou++;
					continue;
				}
				TeacherQueryParams params = new TeacherQueryParams();
				params.setKeyword(mobile);
				params.setSize(Integer.MAX_VALUE);
				params.setHostOrgId(hostOrgId);
				List<Map<String, Object>> teachers = mapper.findTeachers(params.getCondition(), params);
				if (mobileList.contains(mobile)) {
					String msg = "<span style=\"color:red\">第" + row + "行手机号在Excel中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					cfCou++;
					continue;
				} else if (CollectionUtils.isNotEmpty(teachers)){
					String msg = "<span style=\"color:red\">第" + row + "行手机号在系统中重复，忽略这条数据。</span>";
					log.append("<br>");
					log.append(msg);
					existsCou++;
					continue;
				}

				else {
					mobileList.add(mobile);
				}
			}
			String email = StringUtils.trimToNull(map.get("email"));
			Gender gender = Gender.findByName(StringUtils.trimToNull(map.get("gender")));
			Education education = Education.findByName(StringUtils.trimToNull(map.get("education")));
			String workUnit = StringUtils.trimToNull(map.get("workUnit"));
			String zw = StringUtils.trimToNull(map.get("zw"));
			String zc = StringUtils.trimToNull(map.get("zc"));
			String isOut = StringUtils.trimToNull(map.get("isOut"));
			if ("是".equals(isOut)) {
				isOut = Constants.YES;
			} else if ("否".equals(isOut)) {
				isOut = Constants.NO;
			}
			String officeTel = StringUtils.trimToNull(map.get("officeTel"));
			String courses = StringUtils.trimToNull(map.get("courses"));
			String studyDirection = StringUtils.trimToNull(map.get("studyDirection"));
			String specialityType = StringUtils.trimToNull(map.get("specialityType"));
			String brief = StringUtils.trimToNull(map.get("brief"));
			String category = StringUtils.trimToNull(map.get("category"));
			if (Constants.YES.equals(isOut) && StringUtils.isNotEmpty(category) && UserResource.findByName(category) == null) {
				throw BizException.withMessage("导入文件数据中，第" + row + "行，师资类型格式错误，应填企业，高校，党政干部其中一项。");
			}
			//写入用户表
			User user = new User();
			String userId = BaseUtil.generateId2();
			user.setId(userId);
			user.setName(name);
			user.setSfzh(sfzh);
			user.setMobile(mobile);
			user.setStatus(Zt.OK);
			if (StringUtils.isEmpty(mobile)){
				user.setUsername("T"+System.currentTimeMillis()+BaseUtil.generateRandomEnglishLetterString(1));
			}else {
				user.setUsername(mobile);
			}
			user.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
			user.setCreateTime(DateUtils.now());
			user.setCreatorId(loginUserId);
			user.setOrgId(hostOrgId);
			userMapper.insert(user);
			//权限设置
			User2Role user2Role = new User2Role();
			user2Role.setId(BaseUtil.generateId());
			user2Role.setUserId(userId);
			user2Role.setRole(Role.TEACHER);
			user2Role.setCreateTime(DateUtils.now());
			user2Role.setCreatorId(loginUserId);
			user2RoleMapper.insert(user2Role);
			// 写入用户信息表
			UserInfo userInfo = new UserInfo();
			userInfo.setId(BaseUtil.generateId2());
			userInfo.setUserId(userId);
			userInfo.setCreateTime(DateUtils.now());
			userInfo.setCreatorId(loginUserId);
			userInfo.setWorkUnit(workUnit);
			userInfo.setGender(gender);
			userInfo.setEducation(education);
			userInfo.setZw(zw);
			userInfo.setSpecialityType(specialityType);
			userInfo.setStudyDirection(studyDirection);
			userInfo.setZc(zc);
			userInfo.setBrief(brief);
			userInfo.setCategory(UserResource.findByName(category).name());
			userInfo.setCourses(courses);
			userInfo.setOfficeTel(officeTel);
			userInfo.setEmail(email);
			userInfo.setIsOut(isOut);
			userInfo.setTeacherTypeId(typeId);
			mapper.insert(userInfo);
			success++;
		}
		StringBuffer message = new StringBuffer(
				"<div>总共" + (row - 1) + "条数据，批量注册成功" + success + "条，Excel重复数据" + cfCou + "条，错误数据" + cwCou + "条，系统中已经存在数据"+existsCou+"条</div>");
		message.append(log);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("code", 0);
		map.put("message", message);
		return map;

	}
}
