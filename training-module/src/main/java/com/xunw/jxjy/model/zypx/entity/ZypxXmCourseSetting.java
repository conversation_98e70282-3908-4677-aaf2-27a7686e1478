package com.xunw.jxjy.model.zypx.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.jxjy.model.enums.TechLevel;

/**
 * 职业培训-项目课程设置表
 */
@TableName("biz_xm_course_setting")
public class ZypxXmCourseSetting implements Serializable {

	private static final long serialVersionUID = 2910823032910550664L;

	@TableId(type = IdType.INPUT)
	@TableField("id")
	private String id;

	@TableField("xm_id")
	private String xmId;

	// 课程id
	@TableField("course_id")
	private String courseId;

	// 是否开启直播
	@TableField("is_live")
	private String isLive;

	// 是否开启课件学习
	@TableField("is_courseware")
	private String isCourseware;

	// 是否开启面授
	@TableField("is_ms")
	private String isMs;

	// 课程分类
	@TableField("category")
	private String category;

	// 讲师ID 课程设置中的讲师ID
	@TableField("teacher_id")
	private String teacherId;

	// 讲师名称 课程设置中的讲师名称
	@TableField("teacher_name")
	private String teacherName;

	// 面授章节内容大字段
	@TableField("ms_content")
	private String msContent;

	// 上课开始时间
	@TableField("start_time")
	private Date startTime;

	// 上课结束时间
	@TableField("end_time")
	private Date endTime;

	// 上课地点
	@TableField("address")
	private String address;

	//行业id
  	@TableField("industry_id")
  	private String industryId;

  	//职业id
  	@TableField("profession_id")
  	private String professionId;

  	//等级
  	@TableField("tech_level")
  	private TechLevel techLevel;

	// 创建用户id
	@TableField("creator_id")
	private String creatorId;

	// 创建时间
	@TableField("create_time")
	private Date createTime;

	// 修改用户id
	@TableField("updator_id")
	private String updatorId;

	// 修改时间
	@TableField("update_time")
	private Date updateTime;

	@TableField("VENUE_ROOM_ID")
	private String roomId;

	//是否是别的直播课共享过来   Y：是  N：否
	@TableField("is_shared")
	private String isShared;

	@TableField("amount")
	private String amount;

	//是否开启课程评价 1是 0否
	@TableField("is_open_course_target")
	private String isOpenCourseTarget;

	public String getRoomId() {
		return roomId;
	}

	public void setRoomId(String roomId) {
		this.roomId = roomId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getXmId() {
		return xmId;
	}

	public void setXmId(String xmId) {
		this.xmId = xmId;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getIsLive() {
		return isLive;
	}

	public void setIsLive(String isLive) {
		this.isLive = isLive;
	}

	public String getIsCourseware() {
		return isCourseware;
	}

	public void setIsCourseware(String isCourseware) {
		this.isCourseware = isCourseware;
	}

	public String getIsMs() {
		return isMs;
	}

	public void setIsMs(String isMs) {
		this.isMs = isMs;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getTeacherId() {
		return teacherId;
	}

	public void setTeacherId(String teacherId) {
		this.teacherId = teacherId;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getMsContent() {
		return msContent;
	}

	public void setMsContent(String msContent) {
		this.msContent = msContent;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getIndustryId() {
		return industryId;
	}

	public void setIndustryId(String industryId) {
		this.industryId = industryId;
	}

	public String getProfessionId() {
		return professionId;
	}

	public void setProfessionId(String professionId) {
		this.professionId = professionId;
	}

	public TechLevel getTechLevel() {
		return techLevel;
	}

	public void setTechLevel(TechLevel techLevel) {
		this.techLevel = techLevel;
	}

	public String getIsShared() {
		return isShared;
	}

	public void setIsShared(String isShared) {
		this.isShared = isShared;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getIsOpenCourseTarget() {
		return isOpenCourseTarget;
	}

	public void setIsOpenCourseTarget(String isOpenCourseTarget) {
		this.isOpenCourseTarget = isOpenCourseTarget;
	}
}
