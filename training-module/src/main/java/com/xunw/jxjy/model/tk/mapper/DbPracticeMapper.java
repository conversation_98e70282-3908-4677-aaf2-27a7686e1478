package com.xunw.jxjy.model.tk.mapper;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.model.enums.TechLevel;
import com.xunw.jxjy.model.tk.entity.DbPractice;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【BIZ_DB_PRACTICE】的数据库操作Mapper
* @createDate 2023-11-06 09:51:12
* @Entity generator.entity.DbPractice
*/
public interface DbPracticeMapper extends BaseMapper<DbPractice> {

    List<Map<String, Object>> getDbPractice(@Param("xmId") String xmId, @Param("studentId") String studentId);

    List<Map<String, Object>> getPracticeQuestion(@Param("dbPracticeId") String dbPracticeId);

    List<Map<String, Object>> progress(@Param("xmId") String xmId, @Param("keyword") String keyword, @Param("hostOrgId") String hostOrgId, Page<?> page);

    List<Map<String, Object>> dbList(@Param("xmId") String xmId, @Param("professionId") String professionId, @Param("techLevel") TechLevel techLevel);
}




