package com.xunw.jxjy.model.zypx.service;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.exception.BizException;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.core.BaseCRUDService;
import com.xunw.jxjy.model.core.TomSystemQueue;
import com.xunw.jxjy.model.enums.ExamDataStatus;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.exam.entity.ExamAnswerCard;
import com.xunw.jxjy.model.exam.entity.ExamData;
import com.xunw.jxjy.model.utils.OfficeToolExcel;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamAnswerCardMapper;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamDataMapper;
import com.xunw.jxjy.model.zypx.params.ZypxExamDataQueryParams;
import com.xunw.jxjy.paper.model.Paper;
import com.xunw.jxjy.paper.utils.ModelHelper;

import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import net.sf.json.JSONObject;

@Service
public class ZypxExamDataService extends BaseCRUDService<ZypxExamDataMapper, ExamData>{
	
	@Autowired
	private ZypxExamPaperService examPaperService;
	@Autowired
	private ZypxExamAnswerCardMapper examAnswerCardMapper;
    
    public Page pageQuery(ZypxExamDataQueryParams params) throws SQLException, IOException {
        List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    
    public void exportDoneRecord(List<Map<String, Object>> bizZypxZxktSjList, OutputStream os) throws SQLException, IOException, RowsExceededException, WriteException {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("职业培训考生培训成绩查询表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 35);
			ws.addCell(new Label(i, row, "学员单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "课程", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "试卷名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 35);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "班级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "考试时间", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 28);
			ws.addCell(new Label(i, row, "耗时（分钟）", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 12);
			ws.addCell(new Label(i, row, "批改状态", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 12);
			ws.addCell(new Label(i, row, "得分", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
		}
		row = 1;
		for (Map<String, Object> map : bizZypxZxktSjList) {
			if (map==null){
				continue;
			}else {
				int col = 0;
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("serialNumber")!=null ?map.get("serialNumber"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("title")!=null ?map.get("title"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("orgName")!=null ?map.get("orgName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("courseName")!=null ?map.get("courseName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("paperName")!=null ?map.get("paperName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName")!=null ?map.get("studentName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("className")!=null ?map.get("className"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(
						map.get("startTime") != null
								? map.get("startTime").toString().substring(0, 16) + "至"
										+ (map.get("endTime") != null ? map.get("endTime").toString().substring(0, 16)
												: "")
								: ""),
						OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("kshs")!=null ? map.get("kshs"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("status")!=null ? ExamDataStatus.valueOf((String)map.get("status")).getName():""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("score")!=null ? map.get("score"):""), OfficeToolExcel.getNormolCell()));
				row++;
			}
		}
		wwb.write();
		wwb.close();
		os.flush();
    }
    
    
    public Page notPageQuery(ZypxExamDataQueryParams params) throws SQLException, IOException {
        List<Map<String, Object>> bizZypxZxktSjList = mapper.notList(params.getCondition(), params);
        params.setRecords(bizZypxZxktSjList);
        return params;
    }
    
    
    public void exportUnDoneRecord(List<Map<String, Object>> bizZypxZxktSjList, OutputStream os) throws SQLException, IOException, RowsExceededException, WriteException {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("职业培训考生培训成绩查询表", 0);
		int row = 0;
		{
			int i = 0;
			ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "学员单位", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "课程", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "试卷名称", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 20);
			ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
			ws.addCell(new Label(i, row, "班级", OfficeToolExcel.getTitle()));
			ws.setColumnView(i++, 10);
		}
		row = 1;
		for (Map<String, Object> map : bizZypxZxktSjList) {
			if (map==null){
				continue;
			}else {
				int col = 0;
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("serialNumber")!=null ?map.get("serialNumber"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("title")!=null ?map.get("title"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("orgName")!=null ?map.get("orgName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("courseName")!=null ?map.get("courseName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("paperName")!=null ?map.get("paperName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName")!=null ?map.get("studentName"):""), OfficeToolExcel.getNormolCell()));
				ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("className")!=null ?map.get("className"):""), OfficeToolExcel.getNormolCell()));
				row++;
			}
		}
		wwb.write();
		wwb.close();
		os.flush();
    }
    
    
	@Transactional
	public void batchDelete(String ids) {
		for(String id : StringUtils.split(ids,",")) {
			//删除考试记录
			mapper.deleteById(id);
			//删除答题卡
			EntityWrapper<ExamAnswerCard> bizZypxDtkWrapper = new EntityWrapper<ExamAnswerCard>();
			bizZypxDtkWrapper.eq("examdata_id", id);
			examAnswerCardMapper.delete(bizZypxDtkWrapper);
		}
	}
	
	public Map<String, Object> getDetails(String paperId, String studentId, String dataId) throws SQLException, IOException {
		Map<String, Object> modelMap = new HashMap<String, Object>();
		Map<String,Object> condition = new HashMap<String, Object>();
		condition.put("dataId", dataId);
		condition.put("studentId", studentId);
		Map<String, Object> examdataDetail = mapper.getHistoryDetail(condition);
		Paper paper = examPaperService.getPaper(paperId);
		modelMap.put("paper", paper);
		EntityWrapper<ExamAnswerCard> cardWrapper = new EntityWrapper<ExamAnswerCard>();
		cardWrapper.eq("examdata_id", dataId);
		cardWrapper.orderBy("create_time",true);
		List<ExamAnswerCard> cardList = examAnswerCardMapper.selectList(cardWrapper);
		modelMap.put("cardList", cardList);
		String data = BaseUtil.isNotEmpty(examdataDetail.get("data")) ? String.valueOf(examdataDetail.get("data")) : null;
		String scoreDetail = BaseUtil.isNotEmpty(examdataDetail.get("scoreDetail")) ? String.valueOf(examdataDetail.get("scoreDetail")) : null;
		examdataDetail.put("data", JSONObject.fromObject(data));
		examdataDetail.put("score_detail", JSONObject.fromObject(scoreDetail));
		modelMap.put("examdataDetail", examdataDetail);
		return modelMap;
	}

	
	public int checkQues(String teacherId, String dataId, String questionId, String score) throws SQLException, IOException {
		Map<String,Object> condition = new HashMap<String, Object>();
		condition.put("dataId", dataId);
		Map<String, Object> map = this.mapper.getHistoryDetail(condition);
		if (map == null){
			throw BizException.withMessage("考试记录不存在");
		}
		int total = 0;
		String scoreDetail = (String)map.get("scoreDetail");
		JSONObject json = JSONObject.fromObject(scoreDetail);
		json.put("Q-"+questionId, score);

		// 重新计算总分
		for (Object ky : json.keySet()) {
			String key = String.valueOf(ky);
			int val = json.getInt(key);
			total = total + val;
		}

		ExamData examData = new ExamData();
		examData.setId(dataId);
		examData.setMarkTeacherId(teacherId);
		examData.setMarkTime(new Date());
		examData.setScoreDetail(json.toString());
		examData.setScore(total);
		examData.setStatus(ExamDataStatus.YSDPG);
		EntityWrapper<ExamData> examdatWrapper = new EntityWrapper<ExamData>();
		examdatWrapper.eq("id", dataId);
		mapper.update(examData, examdatWrapper);
		return total;
	}
	
	/**
	 * 重新批阅
	 */
	public boolean reCheck(String id, String paperId) throws SQLException, IOException {
		//通过主键id获取对象
		Map<String, Object> condition = new HashMap<String, Object>();
		condition.put("dataId", id);
		Map<String, Object> examData = this.mapper.getHistoryDetail(condition);
		String data = (String)examData.get("data");
		//考生用户id
		String studentId = (String) examData.get("studentId");
		//放入队列的map
		Map<String, Object> queueMap = ModelHelper.SimpleJSON2Map(JSONObject.fromObject(data));
		queueMap.put("studentId", studentId);
		queueMap.put("paperId",paperId);
		//放入批改队列
		TomSystemQueue.addPaper(queueMap);
		//通过id获取对象
		ExamData bizZypxKsjl = mapper.selectById(id);
		bizZypxKsjl.setStatus(ExamDataStatus.YJJDPG);
		mapper.updateById(bizZypxKsjl);
		return true;
	}
	
	public  Map<String, Object> nextPaper(String dataId, String teacherId, String paperId) throws Exception {
		Map<String,Object> condition = new HashMap<String, Object>();
		//通过试卷id 和 答题状态查询需要已经自动批改的试卷 tianjun
		condition.put("paperId", paperId);
		condition.put("dataId", dataId);
		if (BaseUtil.isNotEmpty(teacherId)){
			condition.put("teacherId", teacherId);
		}
		List<Map<String, Object>> nextlist = this.mapper.nextPaper(condition);
		if (nextlist.size() == 0 || nextlist == null){
			throw BizException.withMessage("已经是最后一张批阅试卷了");
		}
        Map<String, Object> modelMap = nextlist.get(0);
        return modelMap;
	}
}
