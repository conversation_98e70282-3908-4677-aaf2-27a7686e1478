package com.xunw.jxjy.model.zypx.service;


import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.model.enums.PaperCategory;
import com.xunw.jxjy.model.inf.entity.StudentInfo;
import com.xunw.jxjy.model.inf.service.StudentInfoService;
import com.xunw.jxjy.model.personal.params.StudentBmQueryParams;
import com.xunw.jxjy.model.personal.service.ZypxStudentBmService;
import com.xunw.jxjy.model.personal.service.ZypxStudentCertiService;
import com.xunw.jxjy.model.personal.service.ZypxStudentLearningService;
import com.xunw.jxjy.model.sys.entity.Org;
import com.xunw.jxjy.model.sys.entity.StudentUser;
import com.xunw.jxjy.model.sys.mapper.StudentUserMapper;
import com.xunw.jxjy.model.sys.service.OrgService;
import com.xunw.jxjy.model.sys.service.StudentUserService;
import com.xunw.jxjy.model.zypx.entity.ZypxStudentCerti;
import com.xunw.jxjy.model.zypx.entity.ZypxXm;
import com.xunw.jxjy.model.zypx.mapper.ZypxExamPaperMapper;
import com.xunw.jxjy.model.zypx.params.ZypxStudentFilesQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ZypxStudentFilesService {

    @Autowired
    private StudentUserMapper studentUserMapper;
    @Autowired
    private ZypxStudentBmService studentBmService;
    @Autowired
    private ZypxStudentLearningService learningService;
    @Autowired
    private ZypxStudentCertiService certiService;
    @Autowired
    private StudentInfoService infoService;
    @Autowired
    private StudentUserService userService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private ZypxXmService xmService;
    @Autowired
    private ZypxExamPaperMapper paperMapper;

    public Page pageQuery(ZypxStudentFilesQueryParams params) {
        List<Map<String, Object>> list = studentUserMapper.list(params.getCondition(), params);
        List<Map<String,Object>> result=new ArrayList<>();
        for(Map<String,Object> map:list){
            Map<String,Object> resultMap=new HashMap<>();
            String studentId= BaseUtil.getStringValueFromMap(map,"id");
            //查询证书数量
            Integer certiCount = certiService.getCertiByStudentId(studentId);
            //培训项目
            StudentBmQueryParams studentBmQueryParams = new StudentBmQueryParams();
            studentBmQueryParams.setStudentId(studentId);
            List<Map<String,Object>> xmList=studentBmService.getStudentBmXmList(studentBmQueryParams);
            int courseCount = 0;
            Double totalFinishedHours = 0.0;
            if(xmList.size() > 0){
                for(Map<String,Object> xmMap:xmList){
                    String xmId= BaseUtil.getStringValueFromMap(xmMap,"xmId");
                    //查询每个项目学习总课时
                    Map<String, Object> trainingScore = learningService.getTrainingScore(xmId,studentId);
                    if(trainingScore != null) {
                        totalFinishedHours += Double.parseDouble(BaseUtil.getStringValueFromMap(trainingScore, "totalFinishedHours"));
                    }
                    //查询学生报名的课程
                    List<Map<String,Object>> courseList=studentBmService.getStudentBmCourseList(xmId,studentId);
                    courseCount += courseList.size();
                }
            }
            resultMap.put("id",studentId);
            resultMap.put("studentName",BaseUtil.getStringValueFromMap(map,"name"));
            resultMap.put("sfzh",BaseUtil.getStringValueFromMap(map,"sfzh"));
            resultMap.put("mobile",BaseUtil.getStringValueFromMap(map,"mobile"));
            resultMap.put("orgName",BaseUtil.getStringValueFromMap(map,"orgName"));
            resultMap.put("certiCount",certiCount);
            resultMap.put("xmCount",xmList.size());
            resultMap.put("totalFinishdeHours",totalFinishedHours);
            resultMap.put("courseCount",courseCount);
            result.add(resultMap);
        }
        params.setRecords(result);
        return params;
    }

    public Object getArchives(String studentId,String hostOrgId){
        //个人信息
        StudentInfo studentInfo = infoService.getByStudentId(studentId);
        StudentUser studentUser = userService.selectById(studentId);
        //查询单位所属单位
        Org org=orgService.selectById(studentUser.getOrgId());
        //培训项目
        StudentBmQueryParams studentBmQueryParams = new StudentBmQueryParams();
        studentBmQueryParams.setStudentId(studentId);
        List<Map<String,Object>> xmList=studentBmService.getStudentBmXmList(studentBmQueryParams);
        List<Map<String,Object>> result=new ArrayList<>();
        if(CollectionUtils.isNotEmpty(xmList)) {
            for (Map<String, Object> xmMap : xmList) {
                String xmId = BaseUtil.getStringValueFromMap(xmMap, "xmId");
                ZypxStudentCerti zypxStudentCerti = certiService.getStudentCerti(xmId,studentId);
                ZypxXm zypxXm = xmService.selectById(xmId);
                List<Map<String, Object>> courseList = studentBmService.getCourseByXmId(xmId);
                //项目的总课时
                Double totalHours = 0.0;
                for (Map<String, Object> course : courseList) {
                    if (BaseUtil.isNotEmpty(course.get("hours"))) {
                        totalHours += Double.parseDouble(course.get("hours").toString());
                    }
                }
                //查询已报名的课程
                List<Map<String, Object>> bmCourseList = studentBmService.getStudentBmCourseList(xmId, studentId);
                List<Map<String,Object>> resultCourse = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(bmCourseList)) {
                    for (Map<String, Object> courseMap : bmCourseList) {
                        String courseId = BaseUtil.getStringValueFromMap(courseMap, "courseId");
                        Map<String,Object> course = new HashMap<>();
                        //课程学习成绩
                        Map<String, Object> bmCourse = learningService.getLearningCourseScore(xmId, studentId, courseId);
                        course.put("courseName",BaseUtil.getStringValueFromMap(courseMap,"courseName"));
                        course.put("hours", BaseUtil.getStringValueFromMap(bmCourse, "hours"));
                        course.put("finishedHours", BaseUtil.getStringValueFromMap(bmCourse, "finishedHours"));
                        resultCourse.add(course);
                    }
                }
                //查询学习总课时
                Map<String, Object> trainingScore = learningService.getTrainingScore(xmId, studentId);
                String totalFinishedHours = BaseUtil.getStringValueFromMap(trainingScore, "totalFinishedHours");
                List<Map<String, Object>> practice = paperMapper.getPaperByXmIdAndstudentId(studentId, PaperCategory.WDLX,xmId);
                List<Map<String, Object>> assess = paperMapper.getPaperByXmIdAndstudentId(studentId, PaperCategory.ZJKH,xmId);
                Map<String, Object> map = new HashMap<>();
                map.put("practice",practice);
                map.put("assess",assess);
                map.put("studentName", studentInfo.getName());
                map.put("sfzh", studentInfo.getSfzh());
                map.put("orgName", org!=null ? StringUtils.isNotEmpty(org.getName()) ? org.getName() : null : null);
                map.put("zc", studentInfo.getZc());
                map.put("zw", studentInfo.getZw());
                map.put("serialNumber", zypxXm.getSerialNumber());
                map.put("title", zypxXm.getTitle());
                map.put("startTime", zypxXm.getStartTime());
                map.put("endTime", zypxXm.getEndTime());
                map.put("courseCount", courseList.size());
                map.put("bmCourseList", resultCourse);
                map.put("totalHours", totalHours);
                map.put("totalFinishedHours", totalFinishedHours);
                map.put("certiHours", zypxXm.getCertiHours());
                map.put("certi",zypxStudentCerti!=null ?  StringUtils.isNotEmpty(zypxStudentCerti.getCertiImgUrl()) ? zypxStudentCerti.getCertiImgUrl() : null : null);
                map.put("certiXmName",zypxXm.getCertiXmName());
                result.add(map);
            }
        }
        return result;
    }
}
