package com.xunw.jxjy.paper.utils;

import java.util.HashMap;
import java.util.Map;

import com.xunw.jxjy.common.utils.BaseUtil;
import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.paper.model.QBlank;
import com.xunw.jxjy.paper.model.QuestionBlankFill;


/**
 * 试卷批改辅助类
 * <AUTHOR>
 *
 */
public class PaperServiceHelper {

	/**
	 * 辅助批改填空题,返回的是得分，因为试题已经包含分数
	 * @param question
	 * @param userkey
	 * @return
	 */
	public static int BlankFillChecker(QuestionBlankFill question, String userkey){
		if(BaseUtil.isEmpty(userkey) || question == null)
			return 0;
		int score = question.getScore();
		int total = 0;
		if(question.isComplex()){
			Map<String, String> _map = new HashMap<String, String>();
			for(QBlank blank : question.getBlanks()){
				_map.put(BaseUtil.trimBlank(BaseUtil.HTMLDecode(blank.getValue())),"1");
			}
			
			//混杂
			for(String ukey : userkey.split(Constants.TM_SPLITER)){
				ukey = BaseUtil.trimBlank(ukey);
				if(_map.containsKey(ukey)){
					total++;
					_map.remove(ukey);
				}
			}
			
		}else{
			//顺序
			String[] ukeys = userkey.split(Constants.TM_SPLITER);
			for (int i = 0; i < ukeys.length; i++) {
				String _uk = BaseUtil.trimBlank(ukeys[i]);
				
				try{
					QBlank blank = question.getBlanks().get(i);
					String _key = BaseUtil.trimBlank(BaseUtil.HTMLDecode(blank.getValue()));
					if(_uk.equals(_key)){
						total++;
					}
				}catch(Exception e){
					System.out.println("批改填空题发生异常：" + e.getMessage());
					e.printStackTrace();
				}
				
			}
		}
		
		float f_total = total * 1f;
		float f_total_qs = question.getBlanks().size() * 1f;
		float rate = f_total / f_total_qs;
		int getScore = (int) (score * rate);
		
		return getScore;
	}
	
}
