package com.xunw.jxjy.paper.model;

import java.io.Serializable;

import net.sf.json.JSONObject;

/**
 * 试卷批改结果对象，存储批改结果
 * <AUTHOR>
 *
 */
public class PaperCheckResult implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2017941506092636130L;

	private boolean success;
	
	private String paperId;
	private String studentId;
	private int score;
	private JSONObject examData;
	private JSONObject scoreDetail;
	
	public PaperCheckResult(){
		this.success = false;
	}

	public PaperCheckResult(boolean success, String paperId, String studentId, int score,
			JSONObject examData, JSONObject scoreDetail) {
		this.success = success;
		this.paperId = paperId;
		this.studentId = studentId;
		this.score = score;
		this.examData = examData;
		this.scoreDetail = scoreDetail;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}
	
	public String getPaperId() {
		return paperId;
	}

	public void setPaperId(String paperId) {
		this.paperId = paperId;
	}

	public String getStudentId() {
		return studentId;
	}

	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}

	public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}

	public JSONObject getExamData() {
		return examData;
	}

	public void setExamData(JSONObject examData) {
		this.examData = examData;
	}

	public JSONObject getScoreDetail() {
		return scoreDetail;
	}

	public void setScoreDetail(JSONObject scoreDetail) {
		this.scoreDetail = scoreDetail;
	}

	@Override
	public String toString() {
		return "PaperCheckdfxq [success=" + success + ", paperId=" + paperId
				+ ", studentId=" + studentId + ", score=" + score + ", examData="
				+ examData + ", scoreDetail=" + scoreDetail + "]";
	}
	
}
