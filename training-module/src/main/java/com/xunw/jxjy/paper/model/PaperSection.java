package com.xunw.jxjy.paper.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 试卷章节及大题
 * <AUTHOR>
 *
 */
public class PaperSection implements Serializable {

	private static final long serialVersionUID = -2334945831419064408L;
	
	// 章节名称
	private String name;
	// 章节说明
	private String remark;
	// 章节编号
	private String id;
	//每题分值
	private int rscore;  
	// 章节试题
	private List<Question> questions;

	
	/**
	 * ================================================
	 * 以下字段仅对随机试卷有效，普通试卷下无效
	 * ================================================
	 */
	private int rnum; //试题数量
	private int rtype; //试题类型
	private int rlevel;//试题难度
	private String rdbid; //所属题库
	

	/**
	 * ================================================
	 * 以下字段仅对试卷模板有效，其他类型试卷下无效 田军
	 * ================================================
	 */
	private String trealType; //试题类型
	private String tlevel;//试题难度,多个值用_隔开
	private String tdbid; //所属题库,多个值用_隔开
	private Integer tnum; //试题数量
	private Integer tscore;  //每题分值
	

	public PaperSection() {

	}
	
	public PaperSection(String id, String name, String remark) {
		this.id = id;
		this.name = name;
		this.remark = remark;
	}

	public PaperSection(String name, String remark, List<Question> questions) {
		this.name = name;
		this.remark = remark;
		this.questions = questions;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<Question> getQuestions() {
		return questions;
	}

	public void setQuestions(List<Question> questions) {
		this.questions = questions;
	}

	public void addQuestion(Question question) {
		if (this.questions == null) {
			this.questions = new ArrayList<Question>();
		}
		this.questions.add(question);
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	
	/**
	 * ================================================
	 * 以下字段仅对随机试卷有效，普通试卷下无效
	 * ================================================
	 */

	public int getRnum() {
		return rnum;
	}

	public void setRnum(int rnum) {
		this.rnum = rnum;
	}

	public int getRtype() {
		return rtype;
	}

	public void setRtype(int rtype) {
		this.rtype = rtype;
	}

	public int getRlevel() {
		return rlevel;
	}

	public void setRlevel(int rlevel) {
		this.rlevel = rlevel;
	}

	public String getRdbid() {
		return rdbid;
	}

	public void setRdbid(String rdbid) {
		this.rdbid = rdbid;
	}

	public int getRscore() {
		return rscore;
	}

	public void setRscore(int rscore) {
		this.rscore = rscore;
	}


    /**
     * ================================================
     * 以下字段仅对试卷模板有效，其他类型试卷下无效
     * ================================================
     */
    public String getTrealType() {
        return trealType;
    }

    public void setTrealType(String trealType) {
        this.trealType = trealType;
    }

    public String getTlevel() {
        return tlevel;
    }

    public void setTlevel(String tlevel) {
        this.tlevel = tlevel;
    }

    public String getTdbid() {
        return tdbid;
    }

    public void setTdbid(String tdbid) {
        this.tdbid = tdbid;
    }

    public Integer getTnum() {
        return tnum;
    }

    public void setTnum(Integer tnum) {
        this.tnum = tnum;
    }

    public Integer getTscore() {
        return tscore;
    }

    public void setTscore(Integer tscore) {
        this.tscore = tscore;
    }
}
