package com.xunw.jxjy.paper.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.xunw.jxjy.common.utils.Constants;
import com.xunw.jxjy.model.enums.*;

/**
 * 试卷对象
 */
public class Paper implements Serializable {

	private static final long serialVersionUID = 2032544620148528758L;
	// 试卷id
	private String id;
	// 试卷名称
	private String name;
	// 试卷状态
	private Sjzt status;
	// 开考时间
    private Date startTime;
	// 结束时间
    private Date endTime;
	// 考试时长
    private Integer duration;
	// 公布答案的时间
    private Date scoreTime;
	// 卷面总分
    private Integer totalScore;
	// 及格分
    private Integer passedScore;
	// 试题排列方式
    private Stplsx quesSortType;
	// 试卷类型
	private Sjlx paperType;
	// 试卷说明
	private String remark;
	// 章节列表
	private List<PaperSection> sections;
	// 课程编号
	private String courseId;
	//是否显示答案
	private String isShowAnswer = Constants.YES;
	//试卷显示类型，1整卷，2单题
	private Sjxslx paperShowType = Sjxslx.ZJZS;
	//是否开启摄像头
	private String isOpenCamera = Constants.YES;
    //抓拍间隔时间(秒)
    private Integer photoCatchInterval;
	//是否允许手机
	private String isAllowMobile = Constants.YES;
	//考试验证码
	private String validateCode;
	//考试模式
	private ExamModel examModel;

	public Paper() {

	}

	public Paper(String id, String name, Sjzt status, Date startTime, Date endTime, Integer duration, Date scoreTime, Integer totalScore,
			Integer passedScore, Stplsx quesSortType, Sjlx paperType, String remark, List<PaperSection> sections, String courseId, String isShowAnswer,
			Sjxslx paperShowType) {
		super();
		this.id = id;
		this.name = name;
		this.status = status;
		this.startTime = startTime;
		this.endTime = endTime;
		this.duration = duration;
		this.scoreTime = scoreTime;
		this.totalScore = totalScore;
		this.passedScore = passedScore;
		this.quesSortType = quesSortType;
		this.paperType = paperType;
		this.remark= remark;
		this.sections = sections;
		this.courseId = courseId;
		this.isShowAnswer = isShowAnswer;
		this.paperShowType = paperShowType;
	}

	public Paper(Paper pp){
		this.setId(pp.getId());
		this.setName(pp.getName());
		this.setStatus(pp.getStatus());
		this.setStartTime(pp.getStartTime());
		this.setEndTime(pp.getEndTime());
		this.setDuration(pp.getDuration());
		this.setScoreTime(pp.getScoreTime());
		this.setTotalScore(pp.getTotalScore());
		this.setPassedScore(pp.getPassedScore());
		this.setQuesSortType(pp.getQuesSortType());
		this.setPaperType(pp.getPaperType());
		this.setRemark(pp.getRemark());
		this.setIsShowAnswer(pp.getIsShowAnswer());
		this.setPaperShowType(pp.getPaperShowType());

		//设置章节
		if (pp.getSections() != null) {
			for (PaperSection sec : pp.getSections()) {
				PaperSection section = new PaperSection(sec.getId(), sec.getName(), sec.getRemark());
				section.setRnum(sec.getRnum());
				section.setRtype(sec.getRtype());
				section.setRlevel(sec.getRlevel());
				section.setRdbid(sec.getRdbid());
				section.setRscore(sec.getRscore());
				//引用试题
				if (sec.getQuestions() != null) {
					for (Question ques : sec.getQuestions()) {
						section.addQuestion(ques);
					}
				}
				this.addSection(section);
			}
		}

	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Sjzt getStatus() {
		return status;
	}

	public void setStatus(Sjzt status) {
		this.status = status;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getDuration() {
		return duration;
	}

	public void setDuration(Integer duration) {
		this.duration = duration;
	}

	public Date getScoreTime() {
		return scoreTime;
	}

	public void setScoreTime(Date scoreTime) {
		this.scoreTime = scoreTime;
	}

	public Integer getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}

	public Integer getPassedScore() {
		return passedScore;
	}

	public void setPassedScore(Integer passedScore) {
		this.passedScore = passedScore;
	}

	public Stplsx getQuesSortType() {
		return quesSortType;
	}

	public void setQuesSortType(Stplsx quesSortType) {
		this.quesSortType = quesSortType;
	}

	public Sjlx getPaperType() {
		return paperType;
	}

	public void setPaperType(Sjlx paperType) {
		this.paperType = paperType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getIsShowAnswer() {
		return isShowAnswer;
	}

	public void setIsShowAnswer(String isShowAnswer) {
		this.isShowAnswer = isShowAnswer;
	}

	public Sjxslx getPaperShowType() {
		return paperShowType;
	}

	public void setPaperShowType(Sjxslx paperShowType) {
		this.paperShowType = paperShowType;
	}

	public String getIsOpenCamera() {
		return isOpenCamera;
	}

	public void setIsOpenCamera(String isOpenCamera) {
		this.isOpenCamera = isOpenCamera;
	}

	public Integer getPhotoCatchInterval() {
		return photoCatchInterval;
	}

	public void setPhotoCatchInterval(Integer photoCatchInterval) {
		this.photoCatchInterval = photoCatchInterval;
	}

	public String getIsAllowMobile() {
		return isAllowMobile;
	}

	public void setIsAllowMobile(String isAllowMobile) {
		this.isAllowMobile = isAllowMobile;
	}

	public String getValidateCode() {
		return validateCode;
	}

	public void setValidateCode(String validateCode) {
		this.validateCode = validateCode;
	}

	public List<PaperSection> getSections() {
		return sections;
	}

	public void setSections(List<PaperSection> sections) {
		this.sections = sections;
	}

	public void addSection(PaperSection section) {
		if (this.sections == null) {
			this.sections = new ArrayList<PaperSection>();
		}
		this.sections.add(section);
	}

	public ExamModel getExamModel() {
		return examModel;
	}

	public void setExamModel(ExamModel examModel) {
		this.examModel = examModel;
	}
}
