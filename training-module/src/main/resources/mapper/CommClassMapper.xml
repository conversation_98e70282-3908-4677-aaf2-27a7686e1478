<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.common.mapper.CommClassMapper">
	
	<select id="pageQuery" parameterType="Map" resultType="HumpSQLResultMap">
		select
		     c.*,
		     s.name headermaster_name,
		     s.username headermaster_username,
		     s.sfzh headermaster_sfzh,
		     s.mobile,
		     nvl(tbl.count,0) count,
			xm.title,
			ks.name student_name
		from
		  comm_class c
		left join sys_user s on s.id = c.headermaster_id
		left join biz_xm xm on xm.id = c.xm_id
		left join sys_student_info ks on ks.student_id = c.student_id
		left join (select bm.class_id, count(1) count from biz_bm bm group by bm.class_id) tbl on tbl.class_id=c.id
		<where>
			<if test="keyword!=null and keyword!=''">
				(c.name like '%'||#{keyword}||'%' or s.name like '%'||#{keyword}||'%' or s.username like '%'||#{keyword}||'%')
			</if>
			<if test="hostOrgId!=null and hostOrgId!=''">
				and c.host_org_id = #{hostOrgId}
			</if>
			<if test="headermasterId!=null and headermasterId!=''">
				and c.headermaster_id = #{headermasterId}
			</if>
			<if test="xmId != null and xmId != ''">
				and c.xm_id = #{xmId}
			</if>
		</where>
		order by c.create_time desc
    </select>
	<select id="courseStudentSelect" resultType="HumpSQLResultMap">
		select bm.xm_id,
			   xm.title,
			   ks.student_id,
			   ks.name student_name
		from biz_bm bm
				 left join biz_xm xm on xm.id = bm.xm_id
				 left join sys_student_info ks on ks.student_id = bm.student_id
		where class_id = #{classId}
	</select>

	<select id="getGroupLeader" resultType="java.util.Map">
		select
		    c.name class_name,
			si.name,
			si.mobile
		from
		    comm_class c
		inner join sys_student_info si on si.student_id = c.student_id
		<where>
			<if test="id!=null and id!=''">
				and c.xm_id = #{id}
			</if>
			<if test="keyword!=null and keyword!=''">
				and (si.name like '%'||#{keyword}||'%' or si.mobile like '%'||#{keyword}||'%')
			</if>
		</where>
		order by c.sort_no
    </select>
</mapper>