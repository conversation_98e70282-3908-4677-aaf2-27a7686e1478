<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.sys.mapper.UserMapper">
    
    <select id="query" parameterType="Map" resultType="HumpSQLResultMap">
		select 
		    u.id,
		    u.username,
		    u.name,
		    u.sfzh,
		    u.mobile,
		    u.org_id,
		    g.code org_code,
		    g.name org_name,
		    g.org_type,
		    g.is_parent,
		    ur.role,
		    u.status,
		    u.avatar,
		    f.gw,
		    f.zw,
		    f.zc,
		    f.brief,
		    f.is_out,
		    f.education,
		    f.photo,
		    f.work_unit,
		    u.creator_id,
		    u.create_time,
		    u.updator_id,
		    u.update_time,
		    u.is_crp_user
		from
		    sys_user u
		left join sys_user_info f on f.user_id = u.id
		left join sys_org g on g.id = u.org_id
		left join (
		     select u2r.user_id,wm_concat(to_char(u2r.role)) role from sys_user_2_role u2r group by u2r.user_id
		) ur on ur.user_id = u.id
		<where>
			u.status!='DELETED'
			<if test='isTeacher == "0"'>
				and ur.role != 'TEACHER'
			</if>
			<!-- 递归查询下级部门的用户 田军-->
			<if test="orgId != null and orgId !=''">
				and exists (select r.id from sys_org r where r.id=u.org_id start with r.id = #{orgId} connect by prior r.id = r.parent_id)
			</if>
			<if test="nature != null and nature !=''">
				and g.nature = #{nature} 
			</if>
			<!-- 根据主办单位递归查询培训机构、委托单位及其子部门的用户 -->
			<if test="hostOrgId !=null and hostOrgId != ''">
				and exists (select 1 from sys_org r where r.id = u.org_id start with exists(
					select 1 from sys_org_relation sr where sr.host_org_id=#{hostOrgId} and sr.org_id = r.id)
					connect by prior r.id = r.parent_id)
		    </if>
			<if test="orgType != null and orgType!=''">
				and g.org_type=#{orgType}
			</if>
			<if test="keyword != null and keyword!=''">
				and (u.username like '%'||#{keyword}||'%' or u.name like '%'||#{keyword}||'%' or u.mobile like '%'||#{keyword}||'%' or u.sfzh like '%'||#{keyword}||'%')
			</if>
			<if test="role != null and role!=''">
				and exists (select 1 from sys_user_2_role u2r where u2r.user_id = u.id and u2r.role=#{role})
			</if>
		</where>
		order by u.create_time desc
    </select>
    
    <select id="getUserByRoleAndSfzh" resultType="User">
    	select
    		u.*
    	from
    		sys_user u
    	where
    		u.sfzh=#{sfzh} and  exists (select 1 from sys_user_2_role u2r where u2r.user_id = u.id and u2r.role=#{role})
    </select>
    
    <select id="getById" resultType="HumpSQLResultMap">
		select 
		    u.id,
		    u.username,
		    u.name,
		    u.sfzh,
		    u.mobile,
		    u.org_id,
		    g.code org_code,
		    g.name org_name,
		    g.org_type,
		    g.is_parent,
		    ur.role,
		    u.status,
		    u.avatar,
		    f.gw,
		    f.zw,
		    f.zc,
		    f.brief,
		    f.education,
		    f.photo,
		    f.is_out,
		    f.work_unit,
		    u.creator_id,
		    u.create_time,
		    u.updator_id,
		    u.update_time
		from
		    sys_user u
		left join sys_user_info f on f.user_id = u.id
		left join sys_org g on g.id = u.org_id
		left join (
		     select u2r.user_id,wm_concat(to_char(u2r.role)) role from sys_user_2_role u2r group by u2r.user_id
		) ur on ur.user_id = u.id
		where
			u.id=#{id}
    </select>
    
    <select id="getRoleByUserId" resultType="String">
    	select u2r.role from sys_user_2_role u2r where u2r.user_id=#{userId} 
    </select>

	<select id="getUserByOrgIdAndRole" resultType="User">
    	select
    		u.*
    	from
    		sys_user u
    	left join sys_org g on g.id=u.org_id
    	left join sys_user_2_role u2r on u2r.user_id = u.id
    	<where>
    		g.id=#{orgId}
    		<if test="role != null">
				and u2r.role=#{role}
			</if>
    	</where>
    </select>
    
    <select id="countUserByHostOrgId" resultType="Integer">
    	select 
    		count(1) 
    	from sys_user u 
    	where u.org_id in(
			select id from sys_org g start with g.id = #{hostOrgId} connect by prior g.id = g.parent_id
		)
    </select>

</mapper>