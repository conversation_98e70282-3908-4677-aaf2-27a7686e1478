<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zyjd.mapper.ZyjdBmScopeMapper">

   <select id="list" parameterType="map" resultType="HumpSQLResultMap">
     	select
			bsc.id,
			bsc.bmbatch_id,
			b.name bmbatch_name,
	   		y.id industry_id,
			y.code industry_code,
			y.name industry_name,
			p.id profession_id,
			p.code profession_code,
	   		nvl2(p.direction, p.name||'('||p.direction||')', p.name) profession_name,
			bsc.tech_level,
			bsc.create_time,
	   		bsc.status,
	   		bsc.category,
	   		bsc.max_person_num
		from
			biz_zyjd_bmscope bsc
		inner join biz_zyjd_bmbatch b on b.id = bsc.bmbatch_id
		inner join inf_industry y on y.id = bsc.industry_id
		inner join inf_profession p on p.id = bsc.profession_id
		left join sys_user u on u.id = b.user_id
		<where>
			<if test="bmbatchId != null and bmbatchId != ''">
                and bsc.bmbatch_id = #{bmbatchId}
            </if>
			<if test="industryId != null and industryId != ''">
                and bsc.industry_id = #{industryId}
            </if>
			<if test="professionId != null and professionId != ''">
                and bsc.profession_id = #{professionId}
            </if>
			<if test="hostOrgId != null and hostOrgId != ''">
                and b.host_org_id = #{hostOrgId}
            </if>
			<if test="userId != null and userId != ''">
                and b.user_id = #{userId}
            </if>
			<if test="batchType != null and batchType != ''">
                and b.type = #{batchType}
            </if>
            <if test="notType != null and notType != ''">
                and b.type != #{notType}
            </if>
			<if test="techLevel != null and techLevel != ''">
                and bsc.tech_level = #{techLevel}
            </if>
			<if test="category != null and category != ''">
                and bsc.category = #{category}
            </if>
        </where>
		order by bsc.create_time desc
    </select>
    
    <select id="checkLevel" resultType="java.lang.Integer">
		select
			count(1) count
		from inf_profession_assign pa
		inner join inf_profession_assign_detail pad on pa.id = pad.assign_id
		<where>
			<if test="hostOrgId != null and hostOrgId != ''">
				pa.host_org_id = #{hostOrgId}
			</if>
			<if test="professionId != null and professionId != ''">
				and pa.profession_id = #{professionId}
			</if>
			<if test="techLevels != null">
				and pad.tech_level in
				<foreach collection="techLevels" item="techLevel" open="(" close=")" separator=",">
					#{techLevel}
				</foreach>
			</if>
		</where>
	</select>

   <select id="getDetailById" resultType="HumpSQLResultMap">
	   select
		    bs.*,
		    bb.name bmbatch_name,
		    p.name profession_name,
			p.code profession_code
	   from
		   biz_zyjd_bmscope bs
	   inner join inf_profession p on p.id = bs.profession_id
	   inner join biz_zyjd_bmbatch bb on bb.id = bs.bmbatch_id
	   <where>
		   	bs.id = #{id}
	   </where>
	</select>
</mapper>