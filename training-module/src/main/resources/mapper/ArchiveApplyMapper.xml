<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxArchiveApplyMapper">
    <select id="selectApplyXmList" resultType="HumpSQLResultMap">
        select
            xm.id xmId,
            xm.serial_number,
            xm.title,
            xm.trainees,
            aa.apply_time,
            xm.archive_time,
            u.name,
            nvl(aa.status,0) status,
            aa.id apply_id
        from biz_xm xm
        left join biz_archive_apply aa on xm.id = aa.xm_id
        left join sys_user u on u.id = xm.archive_user_id
        left join biz_plan p on p.id = xm.plan_id
        <where>
            exists(select 1 from biz_plan_detail pd where pd.plan_id = p.id and pd.serial_number = xm.serial_number and pd.is_execute = '1')
            and xm.host_org_id = #{hostOrgId}
            <if test="name != null and name != ''">
                and xm.title like '%' || #{name} || '%'
            </if>
            <if test="status != null and status != ''">
                and aa.status = #{status}
            </if>
            <if test='status == null or status == ""'>
                and (aa.status != '1' or aa.status is null)
            </if>
            <if test="planId != null and planId != ''">
                and xm.plan_id = #{planId}
            </if>
        </where>
        order by aa.status asc
    </select>
    <select id="applyApproveList" resultType="HumpSQLResultMap">
        SELECT
            aa.id,
            aa.xm_id,
            xm.title,
            xm.type_id,
            aa.apply_user_id,
            u.name,
            ty.name type_name,
            o.name receive_org_name,
            aa.approve_item_id,
            decode(ai.status,'END','已办结',nvl2(t.approve_user_name, '待'||replace(t.approve_user_name,',','/')||'审批...','')) approve_user_name,
            aa.apply_time,
            ai.status,
            l.result
        FROM comm_approve_item ai
        INNER JOIN biz_archive_apply aa ON ai.id = aa.approve_item_id
        INNER JOIN biz_xm xm ON aa.xm_id = xm.id
        INNER JOIN sys_user u ON u.id = aa.apply_user_id
        INNER JOIN sys_org o ON o.id = u.org_id
        LEFT JOIN inf_type ty ON xm.type_id = ty.id
        left join comm_approve_log l on l.item_id = ai.id AND l.user_id = #{currUserId}
        left join (select au.step_id,wm_concat(to_char(u.name)) approve_user_name from comm_approve_user au inner join sys_user u on au.user_id = u.id group by au.step_id) t on ai.next_step_id = t.step_id
        <where>
            (EXISTS( SELECT 1 FROM comm_approve_user au WHERE au.step_id = ai.next_step_id AND au.user_id = #{currUserId} )
            OR EXISTS( SELECT 1 FROM comm_approve_log log WHERE log.item_id = ai.id AND log.user_id = #{currUserId} )
            )
                and xm.host_org_id = #{hostOrgId} and ai.status != 'END' and l.result is null
            <if test="name != null and name != ''">
                and (xm.title like '%' || #{name} || '%' or xm.serial_number like '%' || #{name} || '%')
            </if>
            <if test="startTime != null and startTime != ''">
                and aa.apply_time > to_date(#{startTime},'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''">
                and to_date(#{endTime},'yyyy-MM-dd') > aa.apply_time
            </if>
        </where>
    </select>



    <select id="applyApproveSteps" resultType="HumpSQLResultMap">
        select
        aa.xm_id,
        u.name,
        au.name user_name,
        i.status,
        l.result,
        l.reason,
        s.step_num,
        s.step_name,
        aa.apply_time as time
        from
        biz_archive_apply aa
        inner join biz_xm xm on aa.xm_id = xm.id
        inner join sys_user u on u.id = aa.apply_user_id
        inner join comm_approve_item ai on aa.approve_item_id = ai.id
        inner join comm_approve_step s on s.approve_id = ai.approve_id
        inner join (select au.step_id,u.name,u.id from comm_approve_user au inner join sys_user u on au.user_id = u.id) au on au.step_id = s.id
        inner join comm_approve_log l on ai.id = l.item_id and s.step_num = l.step_num and s.id = l.step_id and l.user_id=au.id
        left join (select i.id, i.status from comm_approve_item i inner join sys_user u on i.creator_id = u.id) i on i.id = aa.approve_item_id
        left join inf_type t on xm.type_id = t.id

        <where>
            <if test="xmId != null and xmId != ''">
                and xm.id = #{xmId}
            </if>
            order by s.step_num
        </where>
    </select>




</mapper>
