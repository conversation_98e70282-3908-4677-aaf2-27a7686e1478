<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.portal.mapper.IndexMapper">

	<select id="getNoticeCategory" resultType="HumpSQLResultMap">
		select
			f.id,
			f.name,
			f.sort,
			f.remark,
			f.creator_id,
			f.create_time,
			f.updator_id,
			f.update_time,
			f.parent_id
		from
			sys_notice_category f
		where
			f.parent_id is null and f.host_org_id=#{hostOrgId}
		order by f.sort asc
	</select>

   <select id="getNotice" parameterType="Map" resultType="HumpSQLResultMap">
   		select
   			xx.id,
   			xx.title,
   			xx.category_id,
   			xx.is_to_top,
   			xx.writer,
   			xx.status,
   			xx.source,
   			xx.content,
   			xx.url,
   			xx.creator_id,
   			xx.create_time,
   			xx.publish_time,
   			xx.updator_id,
   			xx.update_time,
   			xx.count,
   			xx.is_link,
   			xx.link_url,
   			row_number() over(partition by xx.category_id order by xx.is_to_top desc,xx.publish_time desc nulls last) sort_num
		from
			sys_notice xx
		<where>
			xx.status='OK'
			<if test="categoryId != null and categoryId!=''">
				and xx.category_id in (select c.id from sys_notice_category c start with id = #{categoryId} connect by prior id = parent_id)
			</if>
			<if test="receiver != null">
				and (xx.receiver = #{receiver} or xx.receiver = 'ALL')
			</if>
			<if test="categoryName != null and categoryName!=''">
				and exists(select 1 from sys_notice_category c where c.id = xx.category_id and c.name = #{categoryName})
			</if>
		</where>
	   <choose>
		   <when test="sortType != null and sortType == 2">
			   order by xx.create_time desc
		   </when>
		   <otherwise>
			   order by xx.category_id asc, sort_num asc
		   </otherwise>
	   </choose>
   </select>

   <select id="getTopType" resultType="HumpSQLResultMap">
		select
			t.id,
			t.name,
			t.code,
			t.parent_id,
			t.create_time,
			t.creator_id,
			t.update_time,
			t.updator_id,
			t.host_org_id,
			t.sort
		from
			inf_type t
		<where>
			t.parent_id is null and t.status = 'OK'
			<if test="hostOrgId != null and hostOrgId != ''">
				and t.host_org_id = #{hostOrgId}
			</if>
			<if test="category != null">
				and t.category = #{category}
			</if>
		</where>
		order by t.sort asc
   </select>

   <select id="getChildType" resultType="HumpSQLResultMap">
		select
			t.id,
			t.name,
			t.code,
			t.parent_id,
			t.create_time,
			t.creator_id,
			t.update_time,
			t.updator_id,
			t.host_org_id,
			t.sort
		from
			inf_type t
		where
			t.parent_id = #{parentId} and t.status = 'OK'
		order by t.sort asc
   </select>

    <select id="getSecondLevelType" resultType="HumpSQLResultMap">
		select
			level,
			t.id,
			t.name,
			t.code,
			t.parent_id,
			t.create_time,
			t.creator_id,
			t.update_time,
			t.updator_id,
			t.host_org_id,
			t.sort
	    from
	      	inf_type t
	    <where>
			level=2 and t.status = 'OK'
			<if test="hostOrgId != null and hostOrgId != ''">
				and t.host_org_id=#{hostOrgId}
			</if>
			<if test="category != null">
				and t.category = #{category}
			</if>
			start with t.parent_id is null connect by prior t.id = t.parent_id
		</where>
	    order by t.sort asc
   </select>

  <select id="getXmList" parameterType="Map" resultType="HumpSQLResultMap">
  		select
  			xm.id,
        	xm.serial_number,
        	xm.title,
        	xm.type_id,
        	xm.trainees,
        	xm.start_time,
        	xm.end_time,
        	xm.jtbm_start_time,
        	xm.jtbm_end_time,
        	xm.grbm_start_time,
        	xm.grbm_end_time,
        	xm.is_allow_grbm,
        	xm.study_end_time,
        	xm.is_pop_ques,
        	xm.amount,
        	xm.status,
        	xm.creator_id,
        	xm.create_time,
        	xm.updator_id,
        	xm.update_time,
        	xm.xct,
        	xm.notice,
        	xm.is_open_camera,
        	xm.photo_catch_interval,
        	xm.is_open_verify,
        	xm.verify_threshold
  		from
  			biz_xm xm
  		inner join biz_plan p on p.id = xm.plan_id
	    inner join inf_type t on t.id = xm.type_id and t.status = 'OK'
  		<where>
  			xm.status='OK' and xm.is_allow_grbm = 1 and xm.host_org_id=#{hostOrgId}
			<if test="typeId != null and typeId!=''">
				and xm.type_id in (select p.id from inf_type p start with id=#{typeId} connect by prior p.id = p.parent_id)
			</if>
			<if test="keyword != null and keyword!=''">
				and xm.title like '%'||#{keyword}||'%'
			</if>
  			<if test='isSkill == "0"'>
				and (p.is_skill = #{isSkill} or p.is_skill is null)
			</if>
		</where>
		order by xm.start_time desc
  </select>

  <select id="getCourse" parameterType="Map" resultType="HumpSQLResultMap">
		select
			c.id,
			c.code,
			c.name,
			c.creator_id,
			c.create_time,
			c.updator_id,
			c.update_time,
			c.status,
			c.type_id,
			c.logo,
			c.xct,
			c.kcxz,
			c.hours,
			c.amount
		from
			inf_course c
		left join inf_type t on t.id = c.type_id and t.status = 'OK'
		<where>
			 c.host_org_id=#{hostOrgId}
			<!-- 网上课件 -->
			<if test='type == "KJ"'>
				and exists(select 1 from inf_kj kj where kj.course_id=c.id and kj.status='OK' and c.is_open_kj='1')
			</if>
			<!-- 在线课堂 -->
			<if test='type == "ZB"'>
				and exists(select 1 from biz_zb zb inner join inf_course_live cl on cl.course_id=zb.kc_id where zb.kc_id=c.id and c.is_open_zb='1')
			</if>
			<!-- 在线题库 -->
			<if test='type == "TK"'>
				and exists(select 1 from biz_question_db k inner join biz_qdb_2_course q2c on q2c.db_id=k.id where q2c.course_id = c.id and k.status='OK' and c.is_open_tk='1')
			</if>
			<if test="typeId!=null and typeId!=''">
				 and t.id in (select p.id from inf_type p start with id=#{typeId} connect by prior p.id = p.parent_id)
			</if>
			<if test="keyword != null and keyword!=''">
				and c.name like '%'||#{keyword}||'%'
			</if>
		</where>
		order by c.create_time desc
  </select>



  <!-- 获取培训项目的课程设置信息 -->
  <select id="getXmCourseSetting" parameterType="String" resultType="HumpSQLResultMap">
		select
			xcs.id,
			xcs.xm_id,
			xcs.course_id,
			xcs.is_live,
			xcs.is_courseware,
			xcs.create_time,
			xcs.creator_id,
			xcs.update_time,
			xcs.updator_id,
			xcs.is_ms,
			c.code course_code,
			c.name course_name,
			c.hours
		from
			biz_xm_course_setting xcs
		left join inf_course c on c.id = xcs.course_id
		where xcs.xm_id=#{xmId}
  </select>

	<!-- 获取门户的讲师信息-->
	<select id="getPortalTeacher" parameterType="Map" resultType="HumpSQLResultMap">
		select
			u.id,
			u.name,
			i.photo,
			i.recommend,
			i.zw,
			u.status,
			i.brief introduce
		from sys_user u
		inner join sys_user_info i on i.user_id = u.id
		<where>
			u.status='OK' and i.is_show_portal = 1 and u.org_id = #{hostOrgId}
			and exists(select 1 from sys_user_2_role u2r where u2r.role='TEACHER' and u2r.user_id = u.id)
			<if test="id != null and id != ''">
				and u.id = #{id}
			</if>
		</where>
		order by i.recommend nulls last,u.create_time desc
	</select>

  <select id="getCoursewareMaterialByXmId" resultType="HumpSQLResultMap">
		select
			m.id,
			m.kj_id,
			m.chapter_id,
			m.lesson_id,
			m.name,
			m.file_size,
			m.url,
			m.creator_id,
			m.create_time
		from
			biz_xm xm
		inner join biz_xm_course_setting xcs on xcs.xm_id = xm.id
		inner join inf_course c on c.id = xcs.course_id
		inner join inf_kj kj on kj.course_id = c.id
		inner join inf_kj_material m on m.kj_id = kj.id
		where
			kj.status = 'OK' and xm.id=#{xmId}
  </select>

	<!-- 获取所有开放的轮播图 -->
	<select id="getBanner" parameterType="Map" resultType="HumpSQLResultMap">
		select
		  	id,
		  	pb.url,
		  	pb.link
		from
			sys_portal_banner pb
		where
			status ='OK' and terminal ='PC' and pb.host_org_id=#{hostOrgId}
		order by create_time desc
	</select>

	<select id="getNoticeByNew"  resultType="HumpSQLResultMap">
		select
		xx.id,
		xx.title,
		xx.category_id,
		xx.is_to_top,
		xx.writer,
		xx.status,
		xx.source,
		xx.content,
		xx.url,
		xx.creator_id,
		xx.create_time,
		xx.updator_id,
		xx.update_time,
		xx.count,
		xx.is_link,
		xx.link_url,
		row_number() over(partition by xx.category_id order by xx.is_to_top desc,xx.create_time desc) sort_num
		from
		sys_notice xx
		<where>
			and xx.status='OK'
			and xx.category_id in (select c.id from sys_notice_category c start with id = 'ZXXW' connect by prior id = parent_id)
		</where>

	</select>
	<select id="getThirdLevelType" resultType="HumpSQLResultMap">
		select
			level,
			t.id,
			t.name,
			t.code,
			t.parent_id,
			t.create_time,
			t.creator_id,
			t.update_time,
			t.updator_id,
			t.host_org_id,
			t.sort
		from
			inf_type t
		<where>
			level=3 and t.status = 'OK'
			<if test="hostOrgId != null and hostOrgId != ''">
				and t.host_org_id=#{hostOrgId}
			</if>
			<if test="category != null">
				and t.category = #{category}
			</if>
			start with t.parent_id is null connect by prior t.id = t.parent_id
		</where>
		order by t.sort asc
	</select>

</mapper>
