<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zyjd.mapper.ZyjdBmMapper">

    <select id="list" parameterType="map" resultType="HumpSQLResultMap">
       	select
			 bm.id,
			 bm.bmbatch_id,
			 bm.ksly,
	         bm.invoice_title,
	         bm.taxpayer_number,
	         bm.work_time,
		     bm.new_certificate,
	         bm.old_tech_level,
	         bm.old_tech_level_time,
	         bm.old_tech_certi_num,
	         bm.industry_id,
	         bm.profession_id,
	         bm.direction_id,
	         bm.apply_tech_level,
	         bm.work_years,
	         bm.old_certi_photo,
	         bm.job_years,
		  	 bm.profession_years,
		  	 bm.current_job_time,
		  	 bm.mark_payd_user_id,
		  	 bm.healthy_code,
		  	 bm.trip_card,
		  	 bm.test_way,
		  	 bm.profession,
		  	 bm.current_job,
		  	 bm.full_education,
		  	 bm.full_specialty,
			 bm.submit_time,
			 bm.commitment,
		  	 to_char(bm.full_edu_time,'yyyy-MM-dd') full_edu_time,
		  	 bm.job_education,
		  	 bm.job_specialty,
		  	 to_char(bm.job_edu_time,'yyyy-MM-dd') job_edu_time,
		  	 bm.article_tital,
		  	 bm.is_article_publish,
		  	 bm.ddzz,
		  	 bm.fbqk,
			 bm.amount as bm_amount,
		  	 bm.nd,
		  	 bm.qh,
		  	 bm.status,
		  	 bm.pay_status,
		  	 bm.zkz,
		  	 bm.article_photo_url,
		  	 bm.zyjsrzzgzs,
		  	 bm.yqrbmb,
		  	 bm.xrzyjszwpw,
		  	 bm.old_certi_name,
		  	 bm.approve_advice,
			 bm.gznxzm,
			 bm.mark_refund_user_id,
			 bm.refund_amount,
			 bm.is_rec,
       	     bm.rec_code,
			 bm.receipt,
          	 b.name as bmbatch_name,
          	 b.type as bmbatch_type,
          	 y.name industry_name,
          	 y.code industry_code,
			 nvl2(p.direction, p.name||'('||p.direction||')', p.name) profession_name,
          	 p.code profession_code,
          	 d.name profession_direction_name,
          	 d.code profession_direction_code,
          	 f.resume,
          	 f.name student_name,
          	 f.gender,
       	     f.gw,
          	 f.education,
          	 f.sfzh,
          	 f.address,
          	 f.mobile,
          	 f.nation,
          	 f.post_code,
          	 f.student_photo,
          	 f.certi_type,
          	 f.phone,
          	 f.email,
          	 f.zc,
          	 f.qq,
          	 f.wxh,
          	 f.student_num,
          	 f.classz,
			 f.college,
			 f.edu_certi_photo,
			 f.sfzzm,
			 f.sfzfm,
			 f.instructor,
			 f.student_ticket,
			 f.political_type,
          	 b.zkz_end_time,
          	 b.zkz_start_time,
          	 b.years,
          	 f.edu_certi_number,
          	 f.certi_address,
          	 f.specialty,
          	 f.company_province_code,
          	 f.company_city_code,
          	 f.graduate_school,
			 f.birthday,
			 f.student_id,
          	 bm.certi_post,
          	 bm.old_certi_profession,
          	 bm.condition_id,
          	 pcd.condition_seq,
          	 pcd.condition,
          	 bm.approve_user_id,
          	 au.name approve_user_name,
          	 m.amount,
          	 bm.pay_type,
			 m.order_number,
			 m.union_order_no,
			 m.pay_time,
          	 s.company,
			 bm.regionalism_code,
			 bm.xb,
			 bm.certi_category,
			 bm.xl,
			 bm.mz,
			 bm.xw,
			 bm.social_credit_code,
			 bm.unit_nature,
			 bm.is_provide_jxjy,
			 bm.obtain_certi_category,
			 bm.zc_series,
			 bm.zy_qualification_name,
			 bm.zc_level,
			 bm.zy_qualification_level,
			 bm.is_reevaluation,
			 bm.is_exceptional,
			 bm.remark,
			 b.is_one_trial
        from
          	biz_zyjd_bm bm
        left join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
        left join sys_student_info f on f.student_id=bm.student_id
        left join sys_student s on s.id = bm.student_id
        left join inf_profession p on bm.profession_id=p.id
        left join inf_industry y on p.industry_id=y.id
        left join inf_profession_condition pcd on pcd.id = bm.condition_id
        left join sys_user u on u.id = b.user_id
        left join sys_user au on au.id = bm.approve_user_id
        left join inf_profession_direction d on bm.direction_id=d.id
        left join (select r.id order_number,r.amount,r.union_order_no,r.pay_time,d.yw_id,r.pay_method from comm_order_detail d inner join comm_order r on r.id = d.order_id where r.status='YZF') m on m.yw_id = bm.id
        <where>
            <if test="status != null and status != ''">
               bm.status=#{status}
            </if>
			<if test="years != null and years != ''">
			  and b.years=#{years}
			</if>
            <if test="studentId != null and studentId != ''">
               and bm.student_id=#{ksId}
            </if>
            <if test="bmbatchId != null and bmbatchId != ''">
               and bm.bmbatch_id=#{bmbatchId}
            </if>
            <if test="type != null and type != '' and type != 'ZYJD'">
               and b.type=#{type}
            </if>
            <if test='type != null and type=="ZYJD"'>
               and (b.type = 'FOREST' or b.type = 'SWFZ' or b.type = 'ZYJD')
            </if>
            <if test="industryId != null and industryId != ''">
                and y.id=#{industryId}
            </if>
            <if test="professionId != null and  professionId != ''">
                and p.id=#{professionId}
            </if>
			<if test="professionIds!=null and professionIds.size()>0">
				and p.id in
				<foreach collection="professionIds" item="professionId"
						 index="index" open="(" close=")" separator=",">
					#{professionId}
				</foreach>
			</if>
            <if test="applyTechLevel != null and applyTechLevel != ''">
                and bm.apply_tech_level=#{applyTechLevel}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and bm.pay_status=#{payStatus}
            </if>
            <if test="keyword != null and keyword != ''">
                and (f.name like '%' || #{keyword} || '%' or f.sfzh like '%' || #{keyword} || '%')
            </if>
            <if test='zkzIsNull != null and zkzIsNull=="1"'>
               and (bm.zkz is null or bm.zkz='')
            </if>
            <if test='zkzIsNull != null and zkzIsNull=="0"'>
               and bm.zkz is not null and bm.zkz!=''
            </if>
            <if test="hostOrgId !=null and hostOrgId !=''">
				and b.host_org_id = #{hostOrgId}
			</if>
			<if test='queryType == "0"'>
				and (bm.status = 'YTJ' or bm.status = 'TRIALTG')
				<if test="userId !=null and userId !=''">
					<!--未开启一审，且当前用户是批次申报人 或者 开启了一审，且在技能认定审核表中存在and未在审核记录中审核-->
					and (((b.is_one_trial = 0 or b.is_one_trial is null) and b.user_id = #{userId})
					or (b.is_one_trial = 1
					and exists(select 1 from BIZ_ZYJD_BM_TRIAL bt where bt.BMBATCH_ID = b.id and bt.USER_ID = #{userId} and bt.user_role = #{currentRole})
					and not exists (select 1 from BIZ_BM_AUDIT_LOG bal where bal.bm_id = bm.id and bal.USER_ID = #{userId} and bal.is_finish = 1)
					))
				</if>
				<if test='userId == null or userId == ""'>
					and ((b.is_one_trial = 1 and bm.status = 'TRIALTG') or (b.is_one_trial = 0 or b.is_one_trial is null))
				</if>
			</if>
			<if test='queryType == "1"'>
				and bm.status != 'YTJ'
				<if test="userId !=null and userId !=''">
					and b.user_id = #{userId}
				</if>
			</if>
			<if test='queryType == null or queryType == ""'>
				<if test="userId !=null and userId !=''">
					and b.user_id = #{userId}
				</if>
			</if>
			<if test="departmentId != null and departmentId != ''">
				and exists(
					select m.id from sys_org m where m.id = u.org_id  start with m.id=#{departmentId} connect by prior m.id = m.parent_id
				)
			</if>
            <if test="recommendCode !=null and recommendCode !=''">
				and bm.recommend_code = #{recommendCode}
			</if>
			<if test='isRecommend!=null and isRecommend=="1"'>
				and bm.recommend_code is not null
			</if>
			<if test='isRecommend!=null and isRecommend=="0"'>
				and bm.recommend_code is null
			</if>
            <if test='uploadedNewCerti != null and uploadedNewCerti=="1"'>
               and bm.new_certificate is not null
            </if>
            <if test="company !=null and company !=''">
                and s.company like '%' || #{company} || '%'
			</if>
			<if test="ids != null and ids != ''">
				and bm.id in
				<foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
					#{id}
				</foreach>
			</if>
			<if test="isRec !=null and isRec != ''">
				<if test="isRec == '-1'">
					and bm.is_rec is null
				</if>
				<if test="isRec != '-1'">
					and bm.is_rec = #{isRec}
				</if>
			</if>
			<if test="recCode !=null and recCode != ''">
				and bm.rec_code = #{recCode}
			</if>
			<if test="isRecNotNull !=null and isRecNotNull != ''">
				and bm.is_rec is not null
			</if>
			<if test="startTime!=null and startTime!=''">
				and bm.submit_time >= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if test="endTime!=null and endTime!=''">
				and to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss') >= bm.submit_time
			</if>
			<if test="payMethod!=null and payMethod!=''">
				and m.pay_method like #{payMethod} || '%'
			</if>
			<if test="category != null and category != ''">
				and p.category = #{category}
			</if>
			<if test='isGrouped == "0"'>
				and not exists(select 1 from BIZ_GROUP_STUDENT gs inner join BIZ_GROUP g on g.id = gs.group_id where g.bmbatch_id = #{bmbatchId} and gs.student_id = s.id)
			</if>
        </where>
        order by bm.save_time desc
    </select>

    <select id="getDetailsById" resultType="HumpSQLResultMap">
		   select
	         bm.id,
	         bm.ksly,
	         bm.performance,
	         bm.new_certificate,
		   	 bm.bmbatch_id,
		   	 b.name bmbatch_name,
		   	 b.type bmbatch_type,
		   	 b.status bmbatch_status,
	         bm.work_time,
	         bm.profession,
	         bm.old_tech_level,
	         bm.old_tech_level_time,
	         bm.old_tech_certi_num,
	         bm.old_certi_profession,
	         bm.old_certi_photo,
	         bm.old_certi_name,
	         bm.industry_id,
	         bm.profession_id,
	         bm.direction_id,
	         bm.apply_tech_level,
	         bm.work_years,
	         bm.job_years,
		  	 bm.profession_years,
		  	 bm.condition_id,
		  	 pc.condition_seq,
		  	 pc.condition,
		  	 bm.current_job_time,
		  	 bm.test_way,
		  	 bm.profession,
		  	 bm.current_job,
		  	 bm.full_education,
		  	 bm.full_specialty,
		  	 bm.bmlx,
		  	 to_char(bm.full_edu_time,'yyyy-MM-dd') full_edu_time,
		  	 bm.job_education,
		  	 bm.job_specialty,
		  	 to_char(bm.job_edu_time,'yyyy-MM-dd') job_edu_time,
		  	 bm.article_tital,
		  	 bm.is_article_publish,
		  	 bm.ddzz,
		  	 bm.fbqk,
		  	 bm.nd,
		  	 bm.qh,
		  	 bm.status,
		  	 bm.pay_status,
		  	 bm.zkz,
		  	 bm.article_photo_url,
		  	 bm.zyjsrzzgzs,
		  	 bm.yqrbmb,
		  	 bm.xrzyjszwpw,
		  	 bm.approve_advice,
		  	 bm.approve_time,
		  	 bm.certi_post,
		  	 bm.training_class,
		  	 bm.gznxzm,
		  	 bm.commitment,
		  	 bm.recommend_code,
		  	 bm.healthy_code,
		  	 bm.trip_card,
		     bm.apply_tech_level,
			 bm.receipt,
			 bm.invoice_url,
			 bm.is_reevaluation,
			 bm.is_exceptional,
	         f.student_id,
	         f.sfzh,
	         f.name,
	         f.zc,
	         f.gender,
	         f.nation,
	         f.education,
	         f.mobile,
	         f.address,
	         f.certi_address,
	         f.post_code,
	         f.sfzzm,
	         f.sfzfm,
	         f.student_photo,
	         f.specialty,
	         f.graduate_time,
	         f.qq,
	         f.political_type,
	         f.email,
	         f.wxh,
	         f.phone,
	         f.birthday,
	         f.college,
	         f.classz,
	         f.gw,
	         f.student_num,
	         f.edu_certi_photo,
	         f.edu_certi_number,
	         f.student_ticket,
	         f.graduate_school,
	         f.company_province_code,
	         f.company_city_code,
	         f.company_district_code,
	         nvl(province.dict_name, '')||nvl(city.dict_name, '')||nvl(district.dict_name,'') company_address,
	         f.resume,
		     f.certi_type,
	         y.code industry_code,
	         y.name industry_name,
	         p.code profession_code,
			 nvl2(p.direction, p.name||'('||p.direction||')', p.name) profession_name,
		     p.id profession_id,
	         d.code profession_direction_code,
	         d.name profession_direction_name,
	         s.company,
	         b.zkz_start_time,
	         b.zkz_end_time,
	         b.audit_mode,
		     pc.condition,
			 bm.regionalism_code,
			 bm.xb,
			 bm.certi_category,
			 bm.xl,
			 bm.mz,
			 bm.xw,
			 bm.social_credit_code,
			 bm.unit_nature,
			 bm.is_provide_jxjy,
			 bm.obtain_certi_category,
			 bm.zc_series,
			 bm.zy_qualification_name,
			 bm.zc_level,
			 bm.zy_qualification_level,
			 bm.*
	    from
	    	biz_zyjd_bm bm
	    inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
	    inner join sys_student_info f on f.student_id = bm.student_id
	    inner join sys_student s on s.id = bm.student_id
	    left join inf_profession p on p.id = bm.profession_id
	    left join inf_industry y on y.id = p.industry_id
	    left join inf_profession_direction d on d.id = bm.direction_id
	    left join inf_profession_condition pc on pc.id = bm.condition_id
	    left join sys_dict province on province.dict_value = f.company_province_code
	    left join sys_dict city on city.dict_value = f.company_city_code
	    left join sys_dict district on district.dict_value = f.company_district_code
		where
			bm.id = #{id}
    </select>

    <!--报名信息统计-->
	<select id="analysis" parameterType="map" resultType="HumpSQLResultMap">
		select
			b.id bmbatch_id,
			b.name as bmbatch_name,
			b.create_time,
			b.years,
			count(1) people
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
		left join inf_profession p on bm.profession_id = p.id
		left join inf_industry y on p.industry_id = y.id
		left join inf_profession_direction d on bm.direction_id = d.id
		left join sys_user u on u.id = b.user_id
		<where>
			<if test="status != null and status != ''">
               bm.status=#{status}
            </if>
            <if test="studentId != null and studentId != ''">
               and bm.student_id=#{studentId}
            </if>
            <if test="bmbatchId != null and bmbatchId != ''">
               and bm.bmbatch_id=#{bmbatchId}
            </if>
            <if test="years != null and years != ''">
               and b.years=#{years}
            </if>
            <if test='monthArea!=null and monthArea=="3"'>
				and to_char(b.create_time,'mm') in('01','02','03','04','05','06')
			</if>
			<if test='monthArea!=null and monthArea=="6"'>
				and to_char(b.create_time,'mm') in('07','08','09','10','11','12')
			</if>
            <if test="industryId != null and industryId != ''">
                and y.id=#{industryId}
            </if>
            <if test="professionId != null and  professionId != ''">
                and p.id=#{professionId}
            </if>
            <if test="applyTechLevel != null and applyTechLevel != ''">
                and bm.apply_tech_level=#{applyTechLevel}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and bm.pay_status=#{payStatus}
            </if>
             <if test="type != null and type != ''">
               and b.type=#{type}
            </if>
            <if test='zkzIsNull != null and zkzIsNull=="1"'>
               and (bm.zkz is null or bm.zkz='')
            </if>
            <if test='zkzIsNull != null and zkzIsNull=="0"'>
               and bm.zkz is not null and bm.zkz!=''
            </if>
            <if test="type != null and type!=''">
               and b.type = #{type}
            </if>
            <if test="hostOrgId !=null and hostOrgId !=''">
				and b.host_org_id = #{hostOrgId}
			</if>
            <if test="userId !=null and userId !=''">
				and b.user_id = #{userId}
			</if>
			<if test="recommendCode !=null and recommendCode !=''">
				and bm.recommend_code = #{recommendCode}
			</if>
			<if test='isRecommend!=null and isRecommend=="1"'>
				and bm.recommend_code is not null
			</if>
			<if test='isRecommend!=null and isRecommend=="0"'>
				and bm.recommend_code is null
			</if>
			<if test="departmentId != null and departmentId != ''">
				and exists(
					select m.id from sys_org m where m.id = u.org_id start with m.id=#{departmentId} connect by prior m.id = m.parent_id
				)
			</if>
		</where>
		group by b.id, b.name,b.create_time, b.years
		order by b.create_time desc
	</select>

	<!--准考证(考试通知单)-->
	<select id="getZkzById" resultType="HumpSQLResultMap">
   		select
	         bm.id,
	         bm.profession,
	         bm.apply_tech_level,
		  	 bm.zkz,
		  	 bm.exam_room,
		  	 bm.seat_no,
			 bm.BUS_LINE,
			 bm.EXAM_TIME,
			 bm.EXAM_POINT,
	         f.sfzh,
	         f.name student_name,
	         f.gender,
	         f.student_photo,
			 nvl2(p.direction, p.name||'('||p.direction||')', p.name) profession_name
	    from
	    	biz_zyjd_bm bm
	    inner join sys_student_info f on f.student_id = bm.student_id
	    inner join inf_profession p on p.id = bm.profession_id
		where
			bm.id=#{id}
   	</select>

   	<select id="getOpenedProfession" resultType="ZyjdProfession">
   		select
   			p.*,
			nvl2(p.direction, p.name||'('||p.direction||')', p.name) name
   		from
   			inf_profession p
   		inner join biz_zyjd_bmscope bsc on bsc.industry_id=p.industry_id and bsc.profession_id=p.id
   		where
   			bsc.bmbatch_id = #{bmbatchId} and bsc.industry_id = #{industryId}
   		order by p.code asc
   	</select>

	<select id="getProfessionBmCountList" resultType="HumpSQLResultMap">
		select
			bm.bmbatch_id,bm.profession_id,bm.apply_tech_level,count(1) count
		from
			biz_zyjd_bm bm
		where
			bm.bmbatch_id = #{bmbatchId} and bm.pay_status='YJ' group by bm.bmbatch_id,bm.profession_id,bm.apply_tech_level
	</select>

	<select id="getIndustryByBmbatchId" resultType="ZyjdIndustry">
   		select
   			y.*
   		from
   			inf_industry y
   		where y.id in(
			select distinct sz.industry_id from biz_zyjd_bmscope sz where sz.bmbatch_id=#{bmbatchId}
		)
   	</select>

	<select id="getAnalysisInfo" parameterType="map" resultType="HumpSQLResultMap">
		select
			p.id,
			nvl2(p.direction, p.name||'('||p.direction||')', p.name) name,
			bm.apply_tech_level,
			count(*) count
		from
			biz_zyjd_bmbatch b
		inner join biz_zyjd_bm bm on bm.bmbatch_id = b.id
		inner join inf_profession p on p.id = bm.profession_id
		where
			bm.bmbatch_id=#{bmbatchId}
		group by p.id, p.name, p.direction, bm.apply_tech_level
	</select>

	<select id="statistcCount" resultType="HumpSQLResultMap">
		select
			b.years,
			bm.industry_id,
			ind.name as industry_name,
			bm.profession_id,
			<if test='isReceiveOrgShow!=null and isReceiveOrgShow'>
				o.id as receive_org_id,
				o.name as receive_org_name,
			</if>
			nvl2(pro.direction, pro.name||'('||pro.direction||')', pro.name) as profession_name,
			count(1) as count
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch b on b.id = bm.bmbatch_id
		inner join inf_profession pro on pro.id = bm.profession_id
		inner join inf_industry ind on ind.id = pro.industry_id
		inner join inf_profession_assign pa on pa.profession_id = bm.profession_id
		<if test='isReceiveOrgShow!=null and isReceiveOrgShow'>
			left join sys_org o on o.id = pa.receive_org_id
		</if>
		<where>
			<if test="years!=null and years!=''">
				b.years = #{years}
			</if>
			<if test="industryId!=null and industryId!=''">
				and bm.industry_id = #{industryId}
			</if>
			<if test="professionId!=null and professionId!=''">
				and bm.profession_id = #{professionId}
			</if>
			<if test="applyTechLevel!=null and applyTechLevel!=''">
				and bm.apply_tech_level = #{applyTechLevel}
			</if>
			<if test="receiveOrgId!=null and receiveOrgId!=''">
				and pa.receive_org_id = #{receiveOrgId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and pa.host_org_id = #{hostOrgId}
			</if>
		</where>
		group by
			b.years,
			bm.industry_id,
			ind.name,
			bm.profession_id,
			<if test='isReceiveOrgShow!=null and isReceiveOrgShow'>
				o.id,o.name,
			</if>
			pro.name,
			pro.direction
	</select>

	<update id="updateRec" parameterType="map">
		update
		biz_zyjd_bm bzbm
		set bzbm.is_rec = 0,bzbm.rec_code=#{recCode}

		where bzbm.id in(

		select
		bm.id
		from
		biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
		inner join sys_student_info f on f.student_id=bm.student_id
		inner join sys_student s on s.id = bm.student_id
		inner join inf_profession p on bm.profession_id=p.id
		inner join inf_industry y on p.industry_id=y.id
		left join inf_profession_condition pcd on pcd.id = bm.condition_id
		left join sys_user u on u.id = b.user_id
		left join sys_user au on au.id = bm.approve_user_id
		left join inf_profession_direction d on bm.direction_id=d.id
		left join (select r.id order_number,r.amount,r.union_order_no,r.pay_time,d.yw_id from comm_order_detail d inner join comm_order r on r.id = d.order_id where r.status='YZF') m on m.yw_id = bm.id

		<where>
			<if test="status != null and status != ''">
				bm.status=#{status}
			</if>
			<if test="studentId != null and studentId != ''">
				and bm.student_id=#{ksId}
			</if>
			<if test="bmbatchId != null and bmbatchId != ''">
				and bm.bmbatch_id=#{bmbatchId}
			</if>
			<if test="type != null and type != '' and type != 'ZYJD'">
				and b.type=#{type}
			</if>
			<if test='type != null and type=="ZYJD"'>
				and (b.type = 'FOREST' or b.type = 'SWFZ' or b.type = 'ZYJD')
			</if>
			<if test="industryId != null and industryId != ''">
				and y.id=#{industryId}
			</if>
			<if test="professionId != null and  professionId != ''">
				and p.id=#{professionId}
			</if>
			<if test="applyTechLevel != null and applyTechLevel != ''">
				and bm.apply_tech_level=#{applyTechLevel}
			</if>
			<if test="payStatus != null and payStatus != ''">
				and bm.pay_status=#{payStatus}
			</if>
			<if test="keyword != null and keyword != ''">
				and (f.name like '%' || #{keyword} || '%' or f.sfzh like '%' || #{keyword} || '%')
			</if>
			<if test='zkzIsNull != null and zkzIsNull=="1"'>
				and (bm.zkz is null or bm.zkz='')
			</if>
			<if test='zkzIsNull != null and zkzIsNull=="0"'>
				and bm.zkz is not null and bm.zkz!=''
			</if>
			<if test="hostOrgId !=null and hostOrgId !=''">
				and b.host_org_id = #{hostOrgId}
			</if>
			<if test="userId !=null and userId !=''">
				and b.user_id = #{userId}
			</if>
			<if test="departmentId != null and departmentId != ''">
				and exists(
				select m.id from sys_org m where m.id = u.org_id  start with m.id=#{departmentId} connect by prior m.id = m.parent_id
				)
			</if>
			<if test="recommendCode !=null and recommendCode !=''">
				and bm.recommend_code = #{recommendCode}
			</if>
			<if test='isRecommend!=null and isRecommend=="1"'>
				and bm.recommend_code is not null
			</if>
			<if test='isRecommend!=null and isRecommend=="0"'>
				and bm.recommend_code is null
			</if>
			<if test='uploadedNewCerti != null and uploadedNewCerti=="1"'>
				and bm.new_certificate is not null
			</if>
			<if test="company !=null and company !=''">
				and s.company like '%' || #{company} || '%'
			</if>
			<if test="ids !=null and ids != ''">
				and bm.id in (#{ids})
			</if>
			<if test="isRec !=null and isRec != ''">
				<if test="isRec == '-1'">
					and bm.is_rec is null
				</if>
				<if test="isRec != '-1'">
					and bm.is_rec = #{isRec}
				</if>
			</if>
			<if test="startTime!=null and startTime!=''">
				and bm.submit_time >= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if test="endTime!=null and endTime!=''">
				and to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss') >= bm.submit_time
			</if>
		</where>
		)
	</update>

	<update id="updateRecByIds" parameterType="map">
		update
		biz_zyjd_bm
		set is_rec = 0,rec_code=#{recCode}
		where id in
		<foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
			#{id}
		</foreach>

	</update>

	<update id="updateRecByCode" parameterType="map">
		update
		biz_zyjd_bm
		set is_rec = null,rec_code = null
		where rec_code =#{recCode}
	</update>

	<update id="signRec" parameterType="map">
		update
			biz_zyjd_bm
		set is_rec = #{isRec}
		where rec_code =#{recCode}
	</update>

	<select  id="sumAmount" parameterType="map" resultType="Double">
		select
			sum(m.amount) as amount
		from
			biz_zyjd_bm bm
		left join (select r.id order_number,r.amount,r.union_order_no,r.pay_time,d.yw_id,r.pay_method from comm_order_detail d inner join comm_order r on r.id = d.order_id where r.status='YZF') m on m.yw_id = bm.id
		<where>
			<if test="recCode !=null and recCode != ''">
				and bm.rec_code = #{recCode}
			</if>
		</where>
	</select>

    <select id="bmListCurYear" resultType="com.xunw.jxjy.model.zypx.entity.ZypxBm">
		select
			bm.*
		from
			biz_bm bm
		inner join biz_xm xm on xm.id = bm.xm_id
		<where>
			bm.status = 'BMCG'
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id = #{hostOrgId}
			</if>
			<if test="year != null and year != ''">
				and xm.years = #{year}
			</if>
		</where>
    </select>

    <select id="xmIncomeStatistical" resultType="HumpSQLResultMap">
		select
			xm.years,
			sum(nvl(pd.CONTRACT_AMOUNT, 0)) amount
		from
			biz_xm xm
		inner join BIZ_PLAN_DETAIL pd on xm.id = pd.id
		<where>
				xm.status != 'OK'
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id = #{hostOrgId}
			</if>
			<if test="yearList != null and yearList.size() > 0">
				and xm.years in
				<foreach collection="yearList" index="index" item="year" separator="," open="(" close=")">
					#{year}
				</foreach>
			</if>
		</where>
		group by xm.years
		order by xm.years
	</select>

    <select id="zcpsScorePage" resultType="HumpSQLResultMap">
		select
			f.name,
			f.sfzh,
			bm.id,
			bm.industry_id,
			bm.profession_id,
			p.name profession_name,
			bm.apply_tech_level,
			bm.exam_score,
			bm.skill_score,
			bm.job_score,
			bm.potential_score,
			bm.synthesize_score,
			bm.thesis_score,
			listagg(rm.score, ',') within group(order by rm.score desc) score,
			REPLACE(listagg(rm.grade, '') within group(order by rm.grade), CHR(0), '') grade,
			bm.passed
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
		inner join sys_student_info f on f.student_id=bm.student_id
		inner join sys_student s on s.id = bm.student_id
		inner join biz_group g on g.bmbatch_id = b.id and g.TYPE = '2'
		inner join biz_group_student gs on gs.student_id = s.id and gs.group_id = g.id
		inner join inf_industry ind on bm.industry_id=ind.id
		inner join inf_profession p on bm.profession_id=p.id and p.industry_id=ind.id
		left join biz_review_mark rm on rm.bm_id = bm.id
		<where>
			<if test="hostOrgId !=null and hostOrgId != ''">
				b.host_org_id = #{hostOrgId}
			</if>
			<if test="bmbatchId != null and bmbatchId != ''">
				and bm.bmbatch_id=#{bmbatchId}
			</if>
			<if test="type != null and type != ''">
				and b.type = #{type}
			</if>
			<if test="industryId != null and industryId != ''">
				and bm.industry_id=#{industryId}
			</if>
			<if test="professionId != null and  professionId != ''">
				and p.id=#{professionId}
			</if>
			<if test="techLevel != null">
				and bm.apply_tech_level = #{techLevel}
			</if>
			<if test="keyword != null and keyword != ''">
				and (f.name like '%' || #{keyword} || '%' or f.sfzh like '%' || #{keyword} || '%')
			</if>
			<if test="passed != null and passed != ''">
				and bm.passed = #{passed}
			</if>
			<if test="loginUserId != null and loginUserId != ''">
				and exists(select 1 from biz_group_user gu where gu.group_id = g.id and gu.user_id = #{loginUserId})
			</if>
			<if test="groupId != null and groupId != ''">
				and g.id = #{groupId}
			</if>
		</where>
		group by f.name,f.sfzh,bm.id,bm.industry_id,bm.profession_id,p.name,bm.apply_tech_level,bm.exam_score,bm.skill_score,bm.job_score,bm.potential_score,bm.synthesize_score,bm.thesis_score,bm.passed
	</select>

    <select id="getBmBySfzh" resultType="com.xunw.jxjy.model.zyjd.entity.ZyjdBm">
		select
			bm.*
		from
			biz_zyjd_bm bm
		inner join sys_student_info si on si.student_id = bm.student_id
		where
			bm.bmbatch_id = #{bmbatchId}
			and si.sfzh = #{sfzh}
	</select>

    <select id="getDetailsByStudentId" resultType="com.xunw.jxjy.model.zyjd.entity.ZyjdBm">
		select
			bm.*
		from
			biz_zyjd_bm bm
		inner join sys_student_info si on si.student_id = bm.student_id
		where
			si.student_id = #{studentId}
		order by bm.submit_time desc
    </select>

    <select id="getOpenedProfessionIn" resultType="HumpSQLResultMap">
		select
			p.*,
			nvl2(p.direction, p.name||'('||p.direction||')', p.name) name,
			bsc.tech_level
		from
			inf_profession p
		inner join biz_zyjd_bmscope bsc on bsc.industry_id=p.industry_id and bsc.profession_id=p.id and bsc.status = 'OK'
		inner join biz_zyjd_bmbatch b on b.id = bsc.bmbatch_id and b.status = 'OK'
		where
			bsc.bmbatch_id = #{bmbatchId}
			and bsc.industry_id in
			<foreach collection="industryIds" index="index" item="industryId" open="(" separator="," close=")">
					#{industryId}
			</foreach>
			order by p.code asc
	</select>

    <select id="psList" resultType="java.util.Map">
		select
			f.name,
			f.sfzh,
			bm.id,
			bm.industry_id,
			bm.profession_id,
			p.name profession_name,
			bm.apply_tech_level,
			bm.exam_score,
			bm.skill_score,
			bm.job_score,
			bm.potential_score,
			bm.synthesize_score,
			bm.thesis_score,
			listagg(rm.score, ',') within group(order by rm.score desc) score,
			REPLACE(listagg(rm.grade, '') within group(order by rm.grade), CHR(0), '') grade,
			bm.passed
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
		inner join sys_student_info f on f.student_id=bm.student_id
		inner join sys_student s on s.id = bm.student_id
		inner join biz_group g on g.bmbatch_id = b.id
		inner join biz_group_student gs on gs.student_id = s.id and gs.group_id = g.id
		inner join inf_industry ind on bm.industry_id=ind.id
		inner join inf_profession p on bm.profession_id=p.id and p.industry_id=ind.id
		left join biz_review_mark rm on rm.bm_id = bm.id
		<where>
			<if test="hostOrgId !=null and hostOrgId != ''">
				b.host_org_id = #{hostOrgId}
			</if>
			<if test="bmbatchId != null and bmbatchId != ''">
				and bm.bmbatch_id=#{bmbatchId}
			</if>
			<if test="professionIds != null and professionIds.size > 0">
				and bm.profession_id in
				<foreach collection="professionIds" item="item" index="index" open="(" close=")" separator=",">
					 #{item}
				</foreach>
			</if>
			<if test="type != null and type != ''">
				and b.type = #{type}
			</if>
			<if test="professionId != null and  professionId != ''">
				and p.id=#{professionId}
			</if>
			<if test="category != null and category != ''">
				and p.category=#{category}
			</if>
			<if test="techLevel != null">
				and bm.apply_tech_level = #{techLevel}
			</if>
			<if test="keyword != null and keyword != ''">
				and (f.name like '%' || #{keyword} || '%' or f.sfzh like '%' || #{keyword} || '%')
			</if>
			<if test="passed != null and passed != ''">
				and bm.passed = #{passed}
			</if>
			<if test="loginUserId != null and loginUserId != ''">
				and exists(select 1 from biz_group_user gu where gu.group_id = g.id and gu.user_id = #{loginUserId})
			</if>
			<if test="groupId != null and groupId != ''">
				and g.id = #{groupId}
			</if>
			<if test="groupType != null and groupType != ''">
				and g.type = #{groupType}
			</if>
		</where>
		group by f.name,f.sfzh,bm.id,bm.industry_id,bm.profession_id,p.name,bm.apply_tech_level,bm.exam_score,bm.skill_score,bm.job_score,bm.potential_score,bm.synthesize_score,bm.thesis_score,bm.passed
	</select>

	<select id="zyList" resultType="java.util.Map">
		select * from
		(
			select
			f.name,
			f.sfzh,
			bm.id,
			bm.industry_id,
			bm.profession_id,
			p.name profession_name,
			bm.apply_tech_level,
			bm.exam_score,
			bm.skill_score,
			bm.job_score,
			bm.potential_score,
			bm.synthesize_score,
			bm.thesis_score,
			listagg(rm.score, ',') within group(order by rm.score desc) score,
			REPLACE(listagg(rm.grade, '') within group(order by rm.grade), CHR(0), '') grade,
			bm.passed,
			gu.user_id leader_id
			from
			biz_zyjd_bm bm
			inner join biz_zyjd_bmbatch b on b.id=bm.bmbatch_id
			inner join sys_student_info f on f.student_id=bm.student_id
			inner join sys_student s on s.id = bm.student_id
			inner join biz_group g on g.bmbatch_id = b.id
			inner join biz_group_student gs on gs.student_id = s.id and gs.group_id = g.id
			inner join biz_group_user gu on gu.group_id=gs.group_id and gu.IS_LEADER = 1
			inner join inf_industry ind on bm.industry_id=ind.id
			inner join inf_profession p on bm.profession_id=p.id and p.industry_id=ind.id
			left join biz_review_mark rm on rm.bm_id = bm.id
			<where>
				<if test="hostOrgId !=null and hostOrgId != ''">
					b.host_org_id = #{hostOrgId}
				</if>
				<if test="bmbatchId != null and bmbatchId != ''">
					and bm.bmbatch_id=#{bmbatchId}
				</if>
				<if test="type != null and type != ''">
					and b.type = #{type}
				</if>
				<if test="professionId != null and  professionId != ''">
					and p.id=#{professionId}
				</if>
				<if test="category != null and category != ''">
					and p.category=#{category}
				</if>
				<if test="techLevel != null">
					and bm.apply_tech_level = #{techLevel}
				</if>
				<if test="keyword != null and keyword != ''">
					and (f.name like '%' || #{keyword} || '%' or f.sfzh like '%' || #{keyword} || '%')
				</if>
				<if test="passed != null and passed != ''">
					and bm.passed = #{passed}
				</if>
				<if test="loginUserId != null and loginUserId != ''">
					and exists(select 1 from biz_group_user gu where gu.group_id = g.id and gu.user_id = #{loginUserId})
				</if>
				<if test="groupId != null and groupId != ''">
					and g.id = #{groupId}
				</if>
				<if test="groupType != null and groupType != ''">
					and g.type = #{groupType}
				</if>
			</where>
			group by
			f.name,f.sfzh,bm.id,bm.industry_id,bm.profession_id,p.name,bm.apply_tech_level,bm.exam_score,bm.skill_score,bm.job_score,bm.potential_score,bm.synthesize_score,bm.thesis_score,bm.passed,gu.user_id
			HAVING count(rm.grade) > 1
		) temp
		where
			(grade LIKE '%A%' AND grade LIKE '%C%') or (grade LIKE '%B%' AND grade LIKE '%C%')
	</select>

    <select id="studentPage" resultType="java.util.Map">
		select
			gs.id,
			bm.industry_id,
			p.name profession_name,
			bm.apply_tech_level,
			si.name student_name,
			si.mobile,
			s.company,
			gs.num,
			gs.status,
			gs.start_db_time,
			gs.remark,
			g.name group_name
		from
			biz_group_student gs
		inner join biz_group g on g.type = '1' and gs.group_id = g.id
		inner join biz_zyjd_bmbatch zb on zb.id = g.BMBATCH_ID
		inner join biz_zyjd_bm bm on bm.BMBATCH_ID = zb.id and bm.student_id = gs.student_id
		inner join sys_student s on s.id = gs.student_id
		inner join sys_student_info si on si.student_id = s.id
		inner join inf_profession p on p.id = bm.profession_id
		<where>
			zb.host_org_id = #{hostOrgId}
			<if test="bmbatchId != null and bmbatchId != ''">
				and bm.bmbatch_id = #{bmbatchId}
			</if>
			<if test="groupId != null and groupId != ''">
				and g.id = #{groupId}
			</if>
			<if test="industryId != null and industryId != ''">
				and bm.industry_id = #{industryId}
			</if>
			<if test="professionId != null and professionId != ''">
				and bm.profession_id = #{professionId}
			</if>
			<if test="techLevel != null">
				and bm.apply_tech_level = #{techLevel}
			</if>
			<if test="status != null and status != ''">
				and gs.status = #{status}
			</if>
			<if test="keyword != null and keyword != ''">
				and (si.name like '%' || #{keyword} || '%' or si.sfzh like '%' || #{keyword} || '%')
			</if>
		</where>
    </select>

    <select id="scoreDetail" resultType="java.util.Map">
		select
			bm.id,
			si.name student_name,
			s.company,
			bm.apply_tech_level,
			bm.industry_id,
			bm.unit_name,
			p.name profession_name
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch zb on zb.id = bm.bmbatch_id
		inner join inf_profession p on p.id = bm.profession_id
		inner join sys_student s on s.id = bm.student_id
		inner join sys_student_info si on si.student_id = s.id
		<where>
			exists (select 1 from biz_group_student gs inner join biz_group g on g.id = gs.group_id and g.type = '2' where gs.student_id = s.id and g.bmbatch_id = bm.bmbatch_id)
			<if test="bmbatchId != null and bmbatchId != ''">
				and zb.id = #{bmbatchId}
			</if>
			<if test="keyword != null and keyword != ''">
				and (si.name like '%' || #{keyword} || '%' or si.sfzh like '%' || #{keyword} || '%')
			</if>
		</where>
	</select>
	
	<select id="getDetailsByStudentIdAndType" resultType="com.xunw.jxjy.model.zyjd.entity.ZyjdBm">
		select
			bm.*
		from
			biz_zyjd_bm bm
		inner join sys_student_info si on si.student_id = bm.student_id
		where
			si.student_id = #{studentId}
			and exists (select 1 from biz_zyjd_bmbatch zb where zb.id = bm.bmbatch_id and zb.type = #{type})
		order by bm.submit_time desc
    </select>
    
</mapper>