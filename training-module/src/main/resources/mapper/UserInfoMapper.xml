<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.sys.mapper.UserInfoMapper">

	<select id="getByUserId" resultType="UserInfo">
		select
			u.*
		from
			sys_user_info u
		where
			u.user_id=#{userId}
	</select>
	<select id="findTeachers" resultType="HumpSQLResultMap">
		select
			u.id,
			u.name,
		    u.username,
			u.mobile,
			i.work_unit,
			i.zw,
			i.zc,
			i.is_out,
			i.study_direction,
			i.courses,
			u.status,
		    i.teacher_type_id,
			i.speciality_type,
			i.profession_ids
		from sys_user u
		left join sys_user_info i on u.id = i.user_id
		<where>
			exists(select 1 from sys_user_2_role u2r where u2r.user_id = u.id and u2r.role = 'TEACHER') and u.status != 'DELETED'
			<if test="keyword != null and keyword != ''">
				and (u.name like '%'||#{keyword}||'%' or u.mobile like '%'||#{keyword}||'%' or u.sfzh like '%'||#{keyword}||'%')
			</if>
			<if test="professionId != null and professionId != ''">
				and i.profession_ids like '%'||#{professionId}||'%'
			</if>
			<if test="isOut != null and isOut != ''">
				and i.is_out = #{isOut}
			</if>
			<if test="status != null and status != ''">
				and u.status = #{status}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and u.org_id = #{hostOrgId}
			</if>
			<if test="typeId != null and typeId != ''">
				and exists(select 1 from inf_type t where t.id = i.teacher_type_id and t.id = #{typeId})
			</if>
		</where>
		order by u.create_time desc,u.id
	</select>

	<select id="listByUserIds" resultType="HumpSQLResultMap">
		select
			ui.*,
			u.name,
			u.mobile
		from
			sys_user_info ui
		inner join sys_user u on u.id = ui.user_id
		where
			ui.user_id in
		<foreach collection="userIds" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</select>
</mapper>