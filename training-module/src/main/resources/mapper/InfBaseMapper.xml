<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.InfBaseMapper">
    <select id="pageQuery" resultType="map">
        select
            b.*
        from
            inf_base b
        <where>
            <if test="hostOrgId != null and hostOrgId != ''">
                and b.host_org_id = #{hostOrgId}
            </if>
            <if test="name != null and name != ''">
                and b.name like '%'||#{name}||'%'
            </if>
            <if test="type != null and type != ''">
                and b.type = #{type}
            </if>
        </where>
    </select>
</mapper>
