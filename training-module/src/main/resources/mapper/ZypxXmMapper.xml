<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxXmMapper">

	<select id="getXmInfo" parameterType="map" resultType="HumpSQLResultMap">
		select
			xm.*,
			lx.name type_name,
			p.name plan_name,
			n.count needs_count,
			n.content needs_content,
			n.fee needs_fee,
			n.trainees needs_trainees,
			nvl(p.is_skill,'0') as is_skill,
			h_org.name host_org_name,
			a.student_count
		from
			biz_xm xm
		left join inf_type lx on lx.id = xm.type_id
		left join biz_plan p on p.id=xm.plan_id
		left join biz_needs n on n.id = p.needs_id
		left join sys_org h_org on h_org.id = p.host_org_id
		left join (select x.id,count( bm.student_id ) student_count from biz_xm x inner join biz_bm bm on bm.xm_id = x.id group by x.id
			) a on a.id = xm.id
		<where>
			<if test="xmId != null and xmId != ''">
				and xm.id=#{xmId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id=#{hostOrgId}
			</if>
		</where>
	</select>

    <select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
        select
        	xm.id,
			xm.serial_number,
			xm.title,
			xm.type_id,
			xm.trainees,
			xm.start_time,
			xm.end_time,
			xm.jtbm_start_time,
			xm.jtbm_end_time,
			xm.grbm_start_time,
			xm.grbm_end_time,
			xm.is_allow_grbm,
			xm.study_end_time,
			xm.is_pop_ques,
			xm.status,
			xm.amount,
			xm.logo,
			xm.xct,
			xm.is_open_camera,
			xm.photo_catch_interval,
			xm.is_open_verify,
			xm.verify_threshold,
			xm.years,
			xm.plan_id,
			xm.is_allow_choose_course,
			xm.is_hot,
			xm.certi_hours,
			xm.leader_id,
			xm.limit_count,
			xm.is_need_approve,
			xm.create_time,
        	t.name type_name,
        	p.name plan_name,
		    nvl(p.is_skill,'0') as is_skill,
		    xm.learning_score_zb,
		    xm.practice_score_zb,
		    xm.final_exam_score_zb
        from
        	biz_xm xm
        inner join biz_plan p on p.id = xm.plan_id
        left join biz_plan_detail pd on pd.id = xm.id
        left join inf_type t on t.id = xm.type_id
        <where>
            <if test="keyword != null and keyword != ''">
                and (xm.title like '%' || #{keyword} || '%' or xm.serial_number like '%' || #{keyword} || '%')
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and xm.serial_number like '%' || #{serialNumber} || '%'
            </if>
            <if test="typeId != null and typeId != ''">
                and t.id in (select t.id from inf_type t start with t.id=#{typeId} connect by prior t.id = t.parent_id)
            </if>
            <if test="planId != null and planId != ''">
                and xm.plan_id = #{planId}
            </if>
            <if test="leaderId != null and leaderId != ''">
                and xm.leader_id = #{leaderId}
            </if>
            <if test="status != null and status != ''">
                and xm.status=#{status}
            </if>
            <if test="status == null or status == ''">
                and (xm.status != 'APPLY' and xm.status != 'ARCHIVE')
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and xm.host_org_id=#{hostOrgId}
            </if>
			<if test="year != null and year != ''">
				and xm.years=#{year}
			</if>
            <if test="xmId != null and xmId != ''">
                and xm.id=#{xmId}
            </if>
            <if test="entrustOrgId != null and entrustOrgId != ''">
				and pd.entrust_org_id=#{entrustOrgId}
			</if>
			<if test="classzType != null and classzType != ''">
				and exists(select 1 from biz_plan_detail pd where pd.id = xm.id and pd.classz_type=#{classzType})
			</if>
			<if test="industryCategory != null and industryCategory != ''">
				and exists(select 1 from biz_plan_detail pd where pd.id = xm.id and pd.industry_category=#{industryCategory})
			</if>
			<if test="baseId != null and baseId != ''">
				and exists(select 1 from biz_plan_detail pd where pd.id = xm.id and pd.base_id=#{baseId})
			</if>
        </where>
       	order by xm.create_time desc
    </select>

	<select id="getPlanWithXm" resultType="com.xunw.jxjy.model.zypx.vo.ZypxPlanXmVo">
		select
			p.id plan_id,
			p.name plan_name,
			p.type_id,
			t.name type_name,
			p.is_skill,
			p.years,
			xm.id xm_id,
		  	xm.title xm_name,
		  	xm.status,
		  	b.name batch_name
		from
			biz_plan p
		inner join biz_xm xm on xm.plan_id = p.id
		left join biz_plan_detail pd on pd.id = xm.id
		left join inf_type t on t.id = p.type_id
		left join biz_zyjd_bmbatch b on b.id = p.bmbatch_id
		<where>
			<if test="hostOrgId != null and hostOrgId != ''">
     			xm.host_org_id = #{hostOrgId}
     		</if>
     		<if test="leaderId != null and leaderId != ''">
     			and xm.leader_id = #{leaderId}
     		</if>
     		<if test="entrustOrgId != null and entrustOrgId != ''">
				and pd.entrust_org_id = #{entrustOrgId}
			</if>
     		<if test="searchWord != null and searchWord != ''">
				and (p.name like '%' || #{searchWord} || '%' or xm.title like '%' || #{searchWord} || '%')
			</if>
			<if test="status != null and status != ''">
                and xm.status=#{status}
            </if>
            <if test="status == null or status == ''">
                 and (xm.status != 'APPLY' and xm.status != 'ARCHIVE')
            </if>
		</where>
		order by xm.years desc, p.create_time desc,xm.create_time desc
	</select>

	<select id="listApproment" resultType="HumpSQLResultMap">
		select
			f.student_id,
       		f.sfzh,
       		f.name,
       		ls.total_hours,
       		ls.total_finished_hours,
       		ls.score learning_score,
       		ps.score pszy_score,
       		zs.score final_exam_score,
       		bm.id bm_id,
       		bm.certi_approve_result,
       		bm.certi_approve_reason,
       		bm.certi_approve_user_id
	    from
	    	biz_bm bm
	    inner join biz_xm xm on xm.id = bm.xm_id
	    inner join sys_student_info f on f.student_id = bm.student_id
	    left join view_pszy_score ps on ps.xm_id = xm.id and ps.student_id=bm.student_id
	    left join view_learning_score ls  on ls.xm_id = xm.id and ls.student_id=bm.student_id
	    left join view_zjkh_score zs on zs.xm_id = xm.id and zs.student_id=bm.student_id
		<where>
			<if test="xmId!=null and xmId!=''">
				and xm.id=#{xmId}
			</if>
			<if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="lsStart!=null and lsStart!=''">
				and ls.score >= #{lsStart}
			</if>
			<if test="lsEnd != null and lsEnd != ''">
				<![CDATA[ and ls.score <= #{lsEnd}]]>
			</if>
			<if test="psStart!=null and psStart!=''">
				and ps.score >= #{psStart}
			</if>
			<if test="psEnd != null and psEnd != ''">
				<![CDATA[ and ps.score <= #{psEnd}]]>
			</if>
			<if test="zsStart!=null and zsStart!=''">
				and zs.score >= #{zsStart}
			</if>
			<if test="zsEnd != null and zsEnd != ''">
				<![CDATA[ and zs.score <= #{zsEnd}]]>
			</if>
			<if test="keyword!=null and keyword!=''">
				and (f.sfzh like '%' || #{keyword} || '%' or f.name like '%' || #{keyword} || '%')
			</if>
			<if test='approveType != null and approveType=="1"'>
				and bm.certi_approve_result is null
			</if>
			<if test='approveType != null and approveType=="2"'>
				and bm.certi_approve_result='PASS'
			</if>
			<if test='approveType != null and approveType=="3"'>
				and bm.certi_approve_result='NOT_PASS'
			</if>
			<if test="hostOrgId !=null and hostOrgId !=''">
				and xm.host_org_id = #{hostOrgId}
			</if>
		</where>
		order by ls.score desc nulls last,ps.score desc nulls last, zs.score desc nulls last
	</select>

	<select id="getHotXm" resultType="HumpSQLResultMap">
		select * from(
			select
				xm.*,
				p.name type_name,
				nvl(xm.is_hot,0) hot,
				case when bm.id is not null then 1 else 0 end as is_bmed,
				bm.id bm_id,
				bb2f.form_id
			from
				biz_xm xm
			left join biz_bm bm on bm.xm_id = xm.id and bm.student_id=#{studentId}
			left join inf_type p on p.id = xm.type_id
			left join biz_xm_2_form bb2f on bb2f.xm_id = xm.id
			where
				xm.host_org_id=#{hostOrgId} and xm.status = 'OK' and p.status = 'OK' 
				and sysdate between xm.start_time and xm.end_time and xm.is_allow_grbm = 1 
			order by nvl(xm.is_hot,0) desc, xm.create_time desc
		) tbl where <![CDATA[ rownum <= 4]]>
	</select>

	<select id="statisticByXm" resultType="HumpSQLResultMap">
		select
			xm.id,
			xm.serial_number,
			xm.title,
			t.name type_name,
			h.total_hours,
			so.name receive_org_name,
			pd.receive_org_id,
			xm.years,
			nvl(xm.off_line_bm_count,0) + nvl(n.bm_count,0)  off_line_bm_count,
			nvl(xm.online_bm_count,0) + nvl(t1.bm_count,0) on_line_bm_count,
			nvl(xm.off_line_bm_count,0) + nvl(xm.online_bm_count,0) + nvl(n.bm_count,0)+nvl(t1.bm_count,0) total_count,
			nvl(vls.progress, 0) || '%' progress
	    from
	      	biz_xm xm
	    left join biz_plan_detail pd on pd.id = xm.id
		left join sys_org so on so.id = pd.receive_org_id
	    left join inf_type t on t.id = xm.type_id
	    left join(
			select a.xm_id, sum(c.hours) total_hours from biz_xm_course_setting a
			inner join inf_course c on c.id = a.course_id
			group by a.xm_id) h on h.xm_id = xm.id
		left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline=1 group by bm.xm_id) n on n.xm_id = xm.id
	    left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline is null or bm.is_offline!=1 group by bm.xm_id) t1 on t1.xm_id = xm.id
		left join (select case when sum(ls.total_hours) > 0 then round(sum(ls.total_finished_hours) / sum(ls.total_hours) * 100, 2) else null end progress, ls.xm_id from view_learning_score ls group by ls.xm_id) vls on vls.xm_id = xm.id
		<where>
			xm.status != 'APPLY'
			<if test="xmId !=null and xmId!=''">
				and xm.id=#{xmId}
			</if>
			<if test="typeId !=null and typeId!=''">
				and xm.type_id in (select t.id from inf_type t start with t.id = #{typeId} connect by prior t.id = t.parent_id)
			</if>
			<if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id=#{hostOrgId}
			</if>
			<if test="year!=null and year!=''">
				and xm.years=#{year}
			</if>
			<if test='monthArea!=null and monthArea=="3"'>
				and to_char(xm.create_time,'mm') in('01','02','03','04','05','06')
			</if>
			<if test='monthArea!=null and monthArea=="6"'>
				and to_char(xm.create_time,'mm') in('07','08','09','10','11','12')
			</if>
			<if test="receiveOrgId!=null and receiveOrgId!=''">
				and pd.receive_org_id=#{receiveOrgId}
			</if>
		</where>
		order by xm.create_time desc
	</select>

	<select id="statisticCostByXm" resultType="HumpSQLResultMap">
		select
			xm.id,
			xm.years,
			xm.serial_number,
			xm.title,
			t.name type_name,
			nvl(n.bm_count, 0) as total_count,
			decode(pd.is_org_pay, '1', nvl( pd.amount, 0) * r.bm_count, nvl(r.sum_amount, 0)) as total_amount,
			nvl(pd.total_cost, 0) as total_cost,
			decode(pd.is_org_pay, '1', nvl(pd.amount, 0) * r.bm_count, nvl(r.sum_amount, 0)) - nvl(pd.total_cost, 0) as total_left
		from
			biz_xm xm
		inner join biz_plan_detail pd on pd.id = xm.id
		left join inf_type t on t.id = xm.type_id
		left join (select bm.xm_id, count( 1 ) bm_count from biz_bm bm group by bm.xm_id) n on n.xm_id = xm.id
		left join (select bm.xm_id, count( 1 ) bm_count,sum(bm.payed_amount) sum_amount from biz_bm bm where bm.is_payed = 1 group by bm.xm_id) r on r.xm_id = xm.id
		<where>
			xm.status != 'APPLY'
			<if test="xmId !=null and xmId!=''">
				and xm.id = #{xmId}
			</if>
			<if test="typeId !=null and typeId!=''">
				and xm.type_id = #{typeId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id = #{hostOrgId}
			</if>
			<if test="year!=null and year!=''">
				and xm.years = #{year}
			</if>
			<if test='monthArea!=null and monthArea=="3"'>
				and to_char(xm.create_time,'mm') in('01','02','03','04','05','06')
			</if>
			<if test='monthArea!=null and monthArea=="6"'>
				and to_char(xm.create_time,'mm') in('07','08','09','10','11','12')
			</if>
			<if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="receiveOrgId!=null and receiveOrgId!=''">
				and pd.receive_org_id = #{receiveOrgId}
			</if>
		</where>
	</select>

	<select id="getPlanTime" parameterType="map" resultType="HumpSQLResultMap">
		select
			to_char(MIN(xm.start_time),'yyyy-MM-dd') start_time,
			to_char(MAX(xm.end_time),'yyyy-MM-dd')end_time
		from
			biz_xm xm
		where
			xm.plan_id=#{planId}
	</select>

	<select id="getFormField" parameterType="map" resultType="HumpSQLResultMap">
		select
			bfd.*,
			fft.name type_name
		from
			biz_xm xm
		inner join biz_xm_2_form bb2f on bb2f.xm_id = xm.id
		inner join biz_form bf on bf.id = bb2f.form_id
		left join biz_field bfd on bfd.form_id=bf.id
		left join biz_form_field_type fft on fft.id = bfd.type_id and fft.form_id = bf.id
		where
			xm.id=#{xmId} and bf.id=#{formId}
		order by fft.sort,sort_number asc
	</select>

	<select id="getNoPersonalFormField" parameterType="map" resultType="HumpSQLResultMap">
		select
			bfd.*
		from
			biz_xm xm
		inner join biz_xm_2_form bb2f on bb2f.xm_id = xm.id
		inner join biz_form bf on bf.id = bb2f.form_id
		left join biz_field bfd on bfd.form_id=bf.id
		where
			bfd.is_constant = '0' and xm.id=#{xmId} and bf.id=#{formId}
		order by sort_number asc
	</select>

	<select id="getByXmId" parameterType="map" resultType="HumpSQLResultMap">
		select
			xm.id xm_id,
			xm.title,
			xm.serial_number,
			pd.teachers,
			decode(pd.is_free_public, null, '0', pd.is_free_public) is_free_public,
			decode(pd.is_gov_subside, null, '0', pd.is_gov_subside) is_gov_subside,
			pd.receive_org_id,
			o.name receive_org_name,
			pd.entrust_org_nature,
			pd.contract_amount,
			pd.contract_sign_time,
			pd.contract_finish_time,
			pd.gov_amount,
			pd.not_gov_amount,
			pd.online_hours,
			pd.off_line_hours,
			pd.dict_industry_id,
			pd.count,
			pd.count_data,
			pd.school_teacher_count,
			pd.school_teacher_name,
			pd.outer_teacher_count,
			pd.outer_teacher_name,
			pd.is_typical,
			pd.typical_remark,
			pd.total_cost,
			pd.surplus
		from
			biz_xm xm
        left join biz_plan_detail pd on pd.id = xm.id
        left join sys_org o on o.id = pd.receive_org_id
		where xm.id=#{xmId}
	</select>

	<select id="getProfessionByXmId" parameterType="map" resultType="HumpSQLResultMap">
		select
			p.id,
			p.code,
		    nvl2(p.direction, p.name||'('||p.direction||')', p.name) name,
			t.tech_level
		from
			(select distinct zx.xm_id,zx.industry_id,zx.profession_id,zx.tech_level from biz_zyjd_xmscope zx) t
		inner join biz_xm xm on xm.id = t.xm_id
		inner join inf_profession p on p.id = t.profession_id
		where
			xm.id=#{xmId}
	</select>
	
	 <!-- 查询培训项目已经发布的课程 -->
    <select id="getCourseByXmId" parameterType="map" resultType="HumpSQLResultMap">
    	select
    		xcs.id course_setting_id,
    		xcs.course_id,
    		c.code course_code,
    		c.name course_name,
    		c.hours,
    		c.logo,
    		c.xct,
    		xcs.is_courseware,
    		xcs.is_live,
    		xcs.is_ms
    	from
    		biz_xm xm
    	inner join biz_xm_course_setting xcs on xcs.xm_id = xm.id
    	inner join inf_course c on c.id = xcs.course_id
    	where
    		xm.is_course_publish=1 and xm.id = #{xmId}
    </select>

	<select id="xmStudyDetailStatistic" resultType="HumpSQLResultMap">
		select
			sum(s.hours)total_hours,
			sum(s.finished_hours) total_finished_hours,
			si.name,
			si.sfzh,
			si.mobile,
			si.student_id,
			round((sum(s.finished_hours)/sum(s.hours))*100,2) progress
		from
			view_learning_course_score s
		inner join sys_student_info si on si.student_id = s.student_id
		inner join biz_xm xm on xm.id = s.xm_id
		<where>
			<if test="id !=null and id!=''">
				s.xm_id = #{id}
			</if>
			<if test="keyword != null and keyword != ''">
				and (si.name like '%'||#{keyword}||'%' or si.sfzh like '%'||#{keyword}||'%' or si.mobile like '%'||#{keyword}||'%')
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id = #{hostOrgId}
			</if>
		</where>
		group by si.name,si.sfzh,si.mobile,si.student_id
	</select>

	<select id="getFromXmByXmId" resultType="HumpSQLResultMap">
		select
			xm.id,
			xm.start_time,
			xm.end_time,
			xm.grbm_start_time,
			xm.grbm_end_time,
			xm.title,
			count(bm.id) bm_count,
			o.name org_name
		from
			biz_xm xm
		left join biz_bm bm on bm.xm_id = xm.id
		left join sys_org o on o.id = xm.host_org_id
		<where>
			<if test="xmId != null and xmId != ''">
				xm.id = #{xmId}
			</if>
		</where>
		group by xm.id,xm.start_time, xm.end_time, xm.grbm_start_time, xm.grbm_end_time, xm.title, o.name
	</select>

	<select id="getXmCountByTopTypeId" resultType="java.lang.Integer">
		select
			count(1) count
		from
			biz_xm xm
		where
			xm.type_id in (select t.id from inf_type t start with t.parent_id = #{id} connect by prior t.id = t.parent_id)
	</select>
	
	<select id="projectStat" resultType="HumpSQLResultMap">
        select
			t.id type_id,
		  	t.name type_name,
			sum(nvl(xm.off_line_bm_count, 0) + nvl(xm.online_bm_count, 0) + nvl(n.bm_count, 0)+nvl(t1.bm_count, 0)) train_num,
			sum(nvl(xm.online_bm_count, 0) + nvl(t1.bm_count, 0) ) train_online_num,
			sum(nvl(xm.off_line_bm_count, 0) + nvl(n.bm_count, 0)) train_offline_num,
			count(xm.id) project_num,
			sum(nvl(h.total_hours, 0)) class_hour
	    from
	      	biz_xm xm
	    inner join inf_type t on t.id = xm.type_id
	    left join(
			select a.xm_id, sum(c.hours) total_hours from biz_xm_course_setting a
			inner join inf_course c on c.id = a.course_id
			group by a.xm_id) h on h.xm_id = xm.id
		left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline=1 group by bm.xm_id) n on n.xm_id = xm.id
	    left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline is null or bm.is_offline!=1 group by bm.xm_id) t1 on t1.xm_id = xm.id
		<where>
			xm.status != 'APPLY'
			<if test="keyword !=null and keyword != ''">
				and t.name like '%' || #{keyword} || '%'
			</if>
			<if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id=#{hostOrgId}
			</if>
		  	<!--
		  	1近一周 
			2近一月 
			3近一季 
			4近半年 
			5近一年
			-->
			<if test='dateType!=null and dateType=="1"'>
				and to_date(to_char(sysdate-7, 'yyyy-MM-dd hh24:mi:ss'), 'yyyy-MM-dd hh24:mi:ss') <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="2"'>
			 	and ADD_MONTHS(sysdate, -1) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="3"'>
				and ADD_MONTHS(sysdate, -3) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="4"'>
				and ADD_MONTHS(sysdate, -6) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="5"'>
				and ADD_MONTHS(sysdate, -12) <![CDATA[ <= ]]> xm.create_time
			</if>
		</where>
		group by t.id, t.name
    </select>
    
    <select id="orgStat" resultType="HumpSQLResultMap">
        select
			so.id org_id,
			so.name org_name,
			sum(nvl(xm.off_line_bm_count, 0) + nvl(xm.online_bm_count, 0) + nvl(n.bm_count, 0)+nvl(t1.bm_count, 0)) train_num,
			sum(nvl(xm.online_bm_count, 0) + nvl(t1.bm_count, 0) ) train_online_num,
			sum(nvl(xm.off_line_bm_count, 0) + nvl(n.bm_count, 0)) train_offline_num,
			count(xm.id) project_num,
			sum(nvl(h.total_hours, 0)) class_hour
	    from
	      	biz_xm xm
	    inner join biz_plan_detail pd on pd.id = xm.id
		inner join sys_org so on so.id = pd.receive_org_id
	    left join(
			select a.xm_id, sum(c.hours) total_hours from biz_xm_course_setting a
			inner join inf_course c on c.id = a.course_id
			group by a.xm_id) h on h.xm_id = xm.id
		left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline=1 group by bm.xm_id) n on n.xm_id = xm.id
	    left join (select bm.xm_id,count(1) bm_count from biz_bm bm where bm.is_offline is null or bm.is_offline!=1 group by bm.xm_id) t1 on t1.xm_id = xm.id
		<where>
			xm.status != 'APPLY'
			<if test="keyword !=null and keyword != ''">
				and so.name like '%' || #{keyword} || '%'
			</if>
			<if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id=#{hostOrgId}
			</if>
		  	<!--
		  	1近一周 
			2近一月 
			3近一季 
			4近半年 
			5近一年
			-->
			<if test='dateType!=null and dateType=="1"'>
				and to_date(to_char(sysdate-7, 'yyyy-MM-dd hh24:mi:ss'), 'yyyy-MM-dd hh24:mi:ss') <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="2"'>
			 	and ADD_MONTHS(sysdate, -1) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="3"'>
				and ADD_MONTHS(sysdate, -3) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="4"'>
				and ADD_MONTHS(sysdate, -6) <![CDATA[ <= ]]> xm.create_time
			</if>
			<if test='dateType!=null and dateType=="5"'>
				and ADD_MONTHS(sysdate, -12) <![CDATA[ <= ]]> xm.create_time
			</if>
		</where>
		group by so.id, so.name
    </select>
    
</mapper>