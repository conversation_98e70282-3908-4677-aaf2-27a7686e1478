<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.TargetResultMapper">
	<select id="getAvgTargetResult" resultType="String">
		select
			round(avg( score ), 0) score
		from (
			select
				round(avg( star_count ), 0) score
			from
				biz_target_result btr, biz_target_setting bts
			where btr.setting_id = bts.id
			<if test="xmId != null and xmId != ''">
				and bts.xm_id = #{xmId}
			</if>
			<if test="settingId != null and settingId != ''">
				and btr.setting_id = #{settingId}
			</if>
			group by btr.setting_id
		)
	</select>

	<select id="getXmRank" resultType="HumpSQLResultMap">
		select
			xm.title, round(avg( score ), 2) score
		from (
			select
				bts.xm_id xmid, round(avg( star_count ), 2) score
			from
				biz_target_result btr, biz_target_setting bts
			where btr.setting_id = bts.id
			group by bts.xm_id, btr.setting_id
			)t1 join biz_xm xm on t1.xmid = xm.id
			<where>
				<if test="hostOrgId != null and hostOrgId != ''">
					xm.host_org_id = #{hostOrgId}
				</if>
			</where>
		group by xm.title
		order by score desc
	</select>

	<select id="getKjRank" resultType="HumpSQLResultMap">
		select
			iktr.star_count score,
			v.name
		from
			biz_kj_target_result iktr
		left join inf_kj kj on iktr.kj_id =kj.id
		left join inf_kj_video v on v.id = kj.video_id
		<where>
			<if test="hostOrgId != null and hostOrgId != ''">
				v.host_org_id = #{hostOrgId}
			</if>
		</where>
		order by score desc
	</select>

	<select id="getTeacherRank" resultType="HumpSQLResultMap">
		select
	    	max(bttr.star_count) score,
			s.name
	    from
		    biz_teacher_target_result bttr
	   	left join sys_user s on bttr.teacher_id=s.id
		<where>
			<if test="hostOrgId != null and hostOrgId != ''">
				s.ORG_ID = #{hostOrgId}
			</if>
		</where>
		group by s.name
		order by score desc
	</select>
	<select id="getTargetByXmId" resultType="HumpSQLResultMap">
		select
			xm.id,
		    xm.title,
		    xm.logo,
			t.id target_id,
		    t.name target_name,
			tr.star_count
		from biz_target t
		left join biz_target_setting ts on ts.target_id = t.id
		left join biz_xm xm on ts.xm_id = xm.id
		left join biz_target_result tr on ts.id = tr.setting_id and tr.student_id=#{studentId}
		where
			xm.id=#{xmId} and ts.type = '0'
	</select>

	<select id="getCourseTargetResult" resultType="java.util.Map">
		select
			c.id course_id,
			c.name course_name,
			xcs.teacher_name,
			si.student_id,
			si.name student_name,
			si.mobile
		from
			biz_target_result tr
		inner join biz_target_setting ts on ts.id = tr.setting_id
		inner join biz_xm xm on xm.id = ts.xm_id
		inner join biz_target t on t.id = ts.target_id
		inner join biz_xm_course_setting xcs on xcs.id = tr.course_setting_id
		inner join inf_course c on c.id = xcs.course_id
		inner join sys_student_info si on si.student_id = tr.student_id
		<where>
			<if test="xmId != null and xmId != ''">
				xcs.xm_id = #{xmId}
			</if>
			<if test="courseId != null and courseId != ''">
				and xcs.course_id = #{courseId}
			</if>
			<if test="keyword != null and keyword != ''">
				and (
					si.name like '%'||#{keyword}||'%'
					or si.mobile like '%'||#{keyword}||'%'
				)
			</if>
		</where>
		group by c.id, c.name, xcs.teacher_name, si.student_id, si.name, si.mobile
		order by si.name
    </select>

	<select id="getCourseTargetResultByStudent" resultType="java.util.Map">
		select
			t.name,
			tr.star_count,
			tr.remark
		from
			biz_target_result tr
		inner join biz_target_setting ts on ts.id = tr.setting_id
		inner join biz_target t on t.id = ts.target_id
		inner join biz_xm_course_setting xcs on xcs.id = tr.course_setting_id
		inner join inf_course c on c.id = xcs.course_id
		<where>
			<if test="xmId != null and xmId != ''">
				xcs.xm_id = #{xmId}
			</if>
			<if test="courseId != null and courseId != ''">
				and xcs.course_id = #{courseId}
			</if>
			<if test="studentId != null and studentId != ''">
				and tr.student_id = #{studentId}
			</if>
		</where>
		order by tr.time desc
	</select>

	<select id="exportCourseTargetResultByStudent" resultType="java.util.Map">
		select
			xm.title,
			c.id course_id,
			c.name course_name,
			xcs.teacher_name,
			si.student_id,
			si.name student_name,
			si.mobile,
			t.name target_name,
			tr.star_count,
			tr.remark,
			count(t.id) count
		from
			biz_target_result tr
		inner join biz_target_setting ts on ts.id = tr.setting_id
		inner join biz_xm xm on xm.id = ts.xm_id
		inner join biz_target t on t.id = ts.target_id
		inner join biz_xm_course_setting xcs on xcs.id = tr.course_setting_id
		inner join inf_course c on c.id = xcs.course_id
		inner join sys_student_info si on si.student_id = tr.student_id
		<where>
			<if test="xmId != null and xmId != ''">
				xcs.xm_id = #{xmId}
			</if>
			<if test="courseId != null and courseId != ''">
				and xcs.course_id = #{courseId}
			</if>
		</where>
		group by xm.title, c.id, c.name, xcs.teacher_name, si.student_id, si.name, si.mobile, t.name, tr.star_count, tr.remark
		order by si.name
	</select>

	<select id="checkReDo" resultType="integer">
		select
			count(1)
		from
			biz_target_result tr
		inner join biz_target_setting ts on ts.id = tr.SETTING_ID
		inner join BIZ_TARGET t on t.id = ts.TARGET_ID
		inner join BIZ_TARGET_TYPE tt on tt.id = t.TYPE_ID
		<where>
			<if test="id != null and id != ''">
				tt.id = #{id}
			</if>
			<if test="xmId != null and xmId != ''">
				and ts.xm_id = #{xmId}
			</if>
			<if test="studentId != null and studentId != ''">
				and tr.student_id = #{studentId}
			</if>
			<if test="courseSettingId != null and courseSettingId != ''">
				and tr.course_setting_id = #{courseSettingId}
			</if>
		</where>
    </select>
</mapper>