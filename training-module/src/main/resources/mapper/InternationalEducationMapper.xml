<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.InternationalEducationMapper">
    <select id="list" resultType="HumpSQLResultMap">
        select
            ie.*
        from
            inf_international_education ie
        <where>
            ie.host_org_id = #{hostOrgId}
            <if test="countryName != null and countryName != ''">
                and ie.country_name like '%'||#{countryName}||'%'
            </if>
            <if test="cooperativeUnits != null and cooperativeUnits != ''">
                and ie.cooperative_units like '%'||#{cooperativeUnits}||'%'
            </if>
        </where>
    </select>
</mapper>
