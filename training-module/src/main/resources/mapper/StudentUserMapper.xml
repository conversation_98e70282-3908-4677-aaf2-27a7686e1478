<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.sys.mapper.StudentUserMapper">
	
	<select id="list" resultType="HumpSQLResultMap">
     	select
     		s.id,
     		s.status,
     		s.student_type,
     		s.company,
     		f.sfzh, 
     		f.name, 
     		f.gender, 
     		f.nation, 
     		f.political_type, 
     		f.family_type, 
     		f.mobile, 
     		f.certi_address, 
     		f.certi_province,   
     		f.certi_city, 
     		f.address, 
     		f.post_code, 
     		f.profession, 
     		f.education, 
     		f.is_graduated, 
     		f.graduate_num, 
     		f.graduate_school, 
     		f.school_time, 
     		f.sfzzm, 
     		f.sfzfm, 
     		f.student_num, 
     		f.student_photo, 
     		f.create_time, 
     		f.zw, 
     		f.zc,
     		f.college,
     		f.classz,
     		f.specialty, 
     		f.graduate_time, 
     		f.ksly, 
     		f.phone, 
     		f.email, 
     		f.qq, 
     		f.wxh, 
     		f.birthday, 
     		f.current_tech_level, 
     		f.gw, 
     		f.gz, 
     		f.xtxz, 
     		f.dzrq, 
     		f.pymb, 
     		f.remark, 
     		f.teacher_name, 
     		f.teacher_gw, 
     		f.teacher_certino,
     		g.code org_code,
     		g.name org_name,
     		org.name reg_host_org_name
     	from
     		sys_student s
     	inner join sys_student_info f on f.student_id = s.id
     	left join sys_org g on g.id = s.org_id
     	left join sys_org org on org.id = s.reg_host_org_id
     	<where>
			(f.sfzh is not null or f.mobile is not null)
     		<if test="orgId != null and orgId != ''">
     			and s.org_id = #{orgId}
     		</if>
     		<if test="regHostOrgId!=null and regHostOrgId != ''">
		    	and s.reg_host_org_id = #{regHostOrgId}
		    </if>
     		<if test="status != null and status != ''">
     			and s.status = #{status}
     		</if>
     		<if test="ksly != null and ksly != ''">
     			and f.ksly = #{ksly}
     		</if>
     		<if test="keyword != null and keyword != ''">
     			and (f.sfzh like '%' || #{keyword} || '%' or f.name like '%' || #{keyword} || '%' or f.mobile like '%' || #{keyword} || '%')
     		</if>
     		<if test="company != null and company != ''">
     			and s.company like '%' || #{company} || '%'
     		</if>
     		<if test="studentType != null and studentType != ''">
     			and s.student_type = #{studentType}
     		</if>
     	</where>
     	order by f.create_time desc,s.id asc
    </select>
    
    <select id="getBySfzhOrMobile" resultType="StudentUser">
    	select
    		s.*
    	from
     		sys_student s
     	where
     		s.reg_host_org_id=#{hostOrgId}
     		and exists (select 1 from sys_student_info f where f.student_id = s.id and (f.sfzh = upper(#{account}) or f.mobile = #{account}))
    </select>
    
    <select id="getByMobile" resultType="StudentUser">
    	select
    		s.*
    	from
     		sys_student s
     	inner join sys_student_info f on f.student_id = s.id
     	where
     		f.mobile = #{mobile} and s.reg_host_org_id=#{hostOrgId}
    </select>
    
     <select id="getBySfzh" resultType="StudentUser">
    	select
    		s.*
    	from
     		sys_student s
     	inner join sys_student_info f on f.student_id = s.id
     	where
     		upper(f.sfzh) = upper(#{sfzh}) and s.reg_host_org_id=#{hostOrgId}
    </select>
    
    <update id="mergeStudentAccount" statementType="CALLABLE">
   		CALL proc_student_user_merge(#{oldId}, #{newId})
    </update>

</mapper>