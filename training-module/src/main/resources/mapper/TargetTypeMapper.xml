<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.TargetTypeMapper">
	<select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
		SELECT
			ta.*,
			y.name user_name
		FROM
			BIZ_TARGET_TYPE ta
		LEFT JOIN SYS_USER y ON ta.creator_id = y.ID
		<where>
			<if test="id != null and id != ''">
				ta.parent_id = #{id}
			</if>
		</where>
		ORDER BY ta.code ASC
	</select>

	<select id="getAllChildrens" resultType="com.xunw.jxjy.model.zypx.entity.TargetType">
		SELECT
			ta.*
		FROM
			biz_target_type ta
		start with ta.ID = #{id} connect by prior ta.id = ta.parent_id
    </select>
</mapper>