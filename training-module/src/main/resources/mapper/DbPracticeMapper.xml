<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.tk.mapper.DbPracticeMapper">

    <select id="getDbPractice" resultType="HumpSQLResultMap">
        select
            p.id db_practice_id,
            nvl(round(t.count / count(q.id) * 100 , 0), 0) progress,
            d.*
        from
            biz_db_practice p
        inner join biz_xm xm on xm.id = p.xm_id and sysdate between xm.start_time and xm.end_time and xm.status = 'OK'
        inner join biz_plan pl on pl.id = xm.plan_id
        inner join biz_bm bm on bm.xm_id = xm.id and bm.student_id = #{studentId}
        inner join biz_question_db d on d.id = p.db_id and d.status = 'OK'
        left join biz_question q on q.db_id = p.db_id and q.status = 'OK'
        left join (select count(1) count, pd.db_practice_id, pd.student_id from biz_db_practice_detail pd group by pd.db_practice_id,pd.student_id) t on t.db_practice_id = p.id and t.student_id = bm.student_id
        <where>
        	(pl.IS_SKILL = '1' and exists(select 1 from BIZ_ZYJD_BM zb where zb.student_id = #{studentId} and zb.profession_id = p.profession_id and zb.APPLY_TECH_LEVEL = p.tech_level and zb.STATUS = 'SHTG')) or pl.IS_SKILL = '0'
        	<if test="xmId!= null and xmId!= ''">
                and xm.id = #{xmId}
            </if>
        </where>
        group by p.id, t.count, d.id, d.name,d.status,d.creator_id,d.update_time,d.updator_id,d.create_time,d.remark,d.logo,d.host_org_id
    </select>
    <select id="getPracticeQuestion" resultType="HumpSQLResultMap">
        select
            q.*
        from
            biz_question q
        inner join biz_db_practice p on p.db_id = q.db_id and p.id = #{dbPracticeId}
        where q.type != 'TT' and q.status = 'OK'
    </select>

    <select id="progress" resultType="HumpSQLResultMap">
        select
            xm.title xm_name,
            si.name student_name,
            si.sfzh,
            count(pd.id)||'/'||count(q.id) progress,
            max(pd.create_time) time
        from
            biz_db_practice p
        inner join biz_xm xm on xm.id = p.xm_id
        inner join biz_question_db qd on qd.id = p.db_id and qd.status = 'OK'
        inner join biz_question q on q.db_id = qd.id and q.status = 'OK'
        inner join biz_db_practice_detail pd on pd.db_practice_id = p.id and pd.question_id = q.id
        inner join sys_student_info si on si.student_id = pd.student_id
        <where>
            <if test="keyword != null and keyword != ''">
                (si.name like '%'||#{keyword}||'%' or si.sfzh like '%'||#{keyword}||'%')
            </if>
            <if test="xmId!= null and xmId!= ''">
                and xm.id = #{xmId}
            </if>
            <if test="hostOrgId!= null and hostOrgId!= ''">
                and xm.host_org_id = #{hostOrgId}
            </if>
        </where>
        group by xm.title,si.name,si.sfzh
    </select>

    <select id="dbList" resultType="HumpSQLResultMap">
        select
            db.*
        from
            biz_question_db db
        inner join biz_db_practice p on p.db_id = db.id
        <where>
            db.status = 'OK'
            <if test="xmId != null and xmId != ''">
                and p.xm_id = #{xmId}
            </if>
            <if test="professionId != null and professionId != ''">
                and p.profession_id = #{professionId}
            </if>
            <if test="techLevel != null">
                and p.tech_level = #{techLevel}
            </if>
        </where>
    </select>
</mapper>
