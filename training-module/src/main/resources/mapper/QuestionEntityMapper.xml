<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.tk.mapper.QuestionEntityMapper">

   <select id="list" parameterType="map" resultType="HumpSQLResultMap">
     	select 
     		q.id, 
     		q.db_id, 
     		q.type, 
     		q.status, 
     		q.creator_id, 
     		q.create_time, 
     		q.real_type, 
     		q.content,
	   		q.parent_id,
     	    q.tags,
     	    q.is_corrected,
     	    q.wrong_reason,
     		c.code course_code,
     		c.name course_name,
     		g.code org_code,
     		g.name org_name,
     		db.name db_name, 
     		u.username creator_name
     	from
     		biz_question q
     	left join biz_question_db db on db.id=q.db_id
	   	left join biz_qdb_2_course q2c on q2c.db_id=db.id
     	left join inf_course c on c.id=q2c.course_id
     	left join sys_org g on g.id=db.host_org_id
     	left join sys_user u on u.id=q.creator_id
     	<where>
     		<if test="parentId != null and parentId != ''">
     			and q.parent_id = #{parentId}
     		</if>
     		<if test="parentId == null or parentId == ''">
     			and q.parent_id is null
     			<if test="hostOrgId != null and hostOrgId != ''">
     			and (c.host_org_id = #{hostOrgId}
					or exists(select 1 from inf_material_question mq
					inner join inf_material m on m.id = mq.material_id
					where mq.question_id = q.id and m.host_org_id = #{hostOrgId}))
     			</if>
     		</if>
     		<if test="keyword != null and keyword != ''">
     			and q.content like '%' || #{keyword} || '%' 
     		</if>
     		<if test="courseId != null and courseId != ''">
     			and q2c.course_id = #{courseId}
     		</if>
     		<if test="dbId != null and dbId != ''">
     			and db.id = #{dbId}  
     		</if>
     		<if test="type != null and type != ''">
     			and q.type = #{type}  
     		</if>
     		<if test="realType != null and realType != ''">
     			and q.real_type = #{realType}  
     		</if>
     		<if test="difficulty != null and difficulty != ''">
     			and q.difficulty = #{difficulty} 
     		</if>
     		<if test="status != null and status != ''">
     			and q.status=#{status}
     		</if>
     		<if test="isCorrected != null and isCorrected != ''">
     			and q.is_corrected = #{isCorrected}  
     		</if>
     		<if test="wrongReason != null and wrongReason != ''">
     			and q.wrong_reason like '%' || #{wrongReason} || '%' 
     		</if>
     		<if test="sign != null and sign != ''">
     			and q.type in ('SINGLECHOICE','MULTIPLECHOICE','JUDGMENT')
     		</if>
			<if test="tags != null and tags != ''">
				and q.tags like '%' || #{tags} || '%'
			</if>
			<if test="isOpen != null and isOpen != ''">
				and (q.status = 'OK' and (db.status = 'OK' or db.status is null))
			</if>
     	</where>
     	<if test="parentId != null and parentId != ''">
   			 order by q.seq_num asc
   		</if>
   		<if test="parentId == null or parentId == ''">
   			 order by q.create_time desc,q.rowid asc
   		</if>
    </select>
    
    <select id="listByAdmin" parameterType="map" resultType="HumpSQLResultMap">
     	select 
     		q.id, 
     		q.db_id, 
     		q.type, 
     		q.status, 
     		q.creator_id, 
     		q.create_time, 
     		q.real_type, 
     		q.content,
			q.parent_id,
     	    q.tags,
     	    q.is_corrected,
     	    q.wrong_reason,
     		g.code org_code,
     		g.name org_name,
     		db.name db_name, 
     		u.username creator_name
     	from
     		biz_question q
     	left join biz_question_db db on db.id=q.db_id
     	left join sys_org g on g.id=db.host_org_id
     	left join sys_user u on u.id=q.creator_id
     	<where>
     		<if test="parentId != null and parentId != ''">
     			and q.parent_id = #{parentId}
     		</if>
			<if test="parentId == null or parentId == ''">
				and q.parent_id is null
			</if>
     		<if test="keyword != null and keyword != ''">
     			and q.content like '%' || #{keyword} || '%' 
     		</if>
     		<if test="dbId != null and dbId != ''">
     			and db.id = #{dbId}  
     		</if>
     		<if test="type != null and type != ''">
     			and q.type = #{type}  
     		</if>
     		<if test="realType != null and realType != ''">
     			and q.real_type = #{realType}  
     		</if>
     		<if test="difficulty != null and difficulty != ''">
     			and q.difficulty = #{difficulty} 
     		</if>
     		<if test="status != null and status != ''">
     			and q.status=#{status}
     		</if>
     		<if test="isCorrected != null and isCorrected != ''">
     			and q.is_corrected = #{isCorrected}  
     		</if>
     		<if test="wrongReason != null and wrongReason != ''">
     			and q.wrong_reason like '%' || #{wrongReason} || '%' 
     		</if>
     		<if test="sign != null and sign != ''">
     			and q.type in ('SINGLECHOICE','MULTIPLECHOICE','JUDGMENT')
     		</if>
     	</where>
     	<if test="parentId != null and parentId != ''">
   			 order by q.seq_num asc
   		</if>
   		<if test="parentId == null or parentId == ''">
   			 order by q.create_time desc,q.rowid asc
   		</if>
    </select>
    
    <select id="getAllRealType" resultType="HumpSQLResultMap">
       select distinct real_type real_type from biz_question
    </select>
    
    <select id="getQuesDetailsById" parameterType="String" resultType="HumpSQLResultMap">
     	select
     		 q.id,
       		 q.db_id, 
       		 q.type, 
       		 q.difficulty, 
       		 q.source, 
       		 q.status, 
       		 q.content, 
       		 q.answer, 
       		 q.resolve, 
       		 q.creator_id, 
       		 q.create_time, 
       		 q.updator_id, 
       		 q.update_time, 
       		 q.data, 
       		 q.real_type, 
       		 q.parent_id, 
       		 q.seq_num, 
       		 q.is_corrected, 
       		 q.wrong_reason, 
       		 q.wrong_found_time, 
       		 q.wrong_found_by, 
       		 q.corrected_time, 
       		 q.corrected_by,
     		 db.name db_name
     	from
     		biz_question q
       	left join biz_question_db db on db.id=q.db_id
        where
       		q.id=#{id}
    </select>

	<select id="getQuestionsByRandom" parameterType="map" resultType="HumpSQLResultMap">
        select * from
        (select q.*, rownum rn from
	        (select
	        	 t.id,
	       		 t.db_id, 
	       		 t.type, 
	       		 t.difficulty, 
	       		 t.source, 
	       		 t.status, 
	       		 t.content, 
	       		 t.answer, 
	       		 t.resolve, 
	       		 t.creator_id, 
	       		 t.create_time, 
	       		 t.updator_id, 
	       		 t.update_time, 
	       		 t.data, 
	       		 t.real_type, 
	       		 t.parent_id, 
	       		 t.seq_num, 
	       		 t.is_corrected, 
	       		 t.wrong_reason, 
	       		 t.wrong_found_time, 
	       		 t.wrong_found_by, 
	       		 t.corrected_time, 
	       		 t.corrected_by
	         from
	         	biz_question t
	        <where>
	        	<if test="difficulty != null and difficulty != ''" >
					and t.difficulty = #{difficulty}
				</if>
				<if test="type != null and type != ''" >
					and t.type = #{type}
				</if>
				<if test="dbId != null and dbId != ''" >
					and t.db_id = #{dbId}
				</if>
	        </where>
			order by dbms_random.value()
			) q
        )  where <![CDATA[rn <= #{sum}]]>
	</select>
</mapper>