<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.CockpitMapper">
    <select id="survey" parameterType="string" resultType="java.util.Map">
        select
        1 is_finished,
        nvl(count(1), 0) xm_count,
        nvl(sum(pd.COUNT), 0) count
        from
        biz_xm xm
        left join BIZ_PLAN_DETAIL pd on pd.id=xm.id and pd.IS_EXECUTE = '1'
        where xm.status != 'APPLY' and xm.host_org_id = #{hostOrgId} and sysdate > xm.END_TIME
        union
        select
        0 is_finished,
        nvl(count(1), 0) xm_count,
        nvl(sum(pd.COUNT), 0) count
        from
        biz_xm xm
        left join BIZ_PLAN_DETAIL pd on pd.id=xm.id and pd.IS_EXECUTE = '1'
        where xm.status != 'APPLY' and xm.host_org_id = #{hostOrgId} and xm.END_TIME >=sysdate
    </select>

    <select id="base" parameterType="string" resultType="java.util.Map">
        select b.org, b.name, b.approval_time
        from INF_BASE b
        where b.HOST_ORG_ID = #{hostOrgId}
    </select>

    <select id="baseXm" parameterType="string" resultType="java.util.Map">
        select
            b.ORG,
            xm.title,
            to_char(xm.start_time, 'yyyy-mm-dd') start_time,
            nvl(pd.SUPPORT_AMOUNT, 0) SUPPORT_AMOUNT,
            nvl(pd.COUNT, 0) count
        from
            biz_xm xm
                left join BIZ_PLAN_DETAIL pd on pd.ID = xm.ID
                left join INF_BASE b on b.id = pd.BASE_ID
        where b.org is not null and xm.host_org_id = #{hostOrgId}
    </select>

    <select id="classz" parameterType="string" resultType="java.util.Map">
        select pd.name, nvl(pd.count, 0) count, nvl(pd.CONTRACT_AMOUNT, 0) CONTRACT_AMOUNT, nvl(t.avg, 100) p
        from BIZ_PLAN_DETAIL pd
                 left join (select round(avg(tr.STAR_COUNT) / 5 * 20, 0) avg, ts.XM_ID
                            from BIZ_TARGET_RESULT tr
                                     inner join BIZ_TARGET_SETTING ts on ts.id = tr.SETTING_ID
                            where exists(select 1
                                         from BIZ_TARGET t
                                         where t.id = ts.TARGET_ID
                                           and t.NAME = '您对本次培训整体的满意程度如何？')
                            group by ts.XM_ID) t on t.XM_ID = pd.ID
        where exists(select 1
                     from biz_xm xm
                     where xm.id = pd.id
                       and xm.status != 'APPLY'
                       and xm.host_org_id = #{hostOrgId})
        order by pd.EXECUTE_TIME desc
    </select>

    <select id="xmType" parameterType="string" resultType="java.util.Map">
        select d.DICT_NAME  name,
               count(xm.id) count
        from biz_xm xm
                 inner join biz_plan_detail pd on pd.id = xm.id
                 left join sys_dict d on d.ID = pd.CLASSZ_TYPE and d.DICT_CODE = 'classzType'
        where d.id is not null
          and xm.STATUS != 'APPLY'
          and xm.HOST_ORG_ID = #{hostOrgId}
        group by d.DICT_NAME
    </select>

    <select id="certi" parameterType="string" resultType="java.util.Map">
        select year, sum(count) count
        from (select to_char(sc.CREATE_TIME, 'yyyy') year, count(1) count
              from BIZ_STUDENT_CERTI sc
                       left join biz_xm xm on xm.id = sc.XM_ID
              where xm.STATUS != 'APPLY'
                and xm.HOST_ORG_ID = #{hostOrgId}
              group by to_char(sc.CREATE_TIME, 'yyyy')
              union
              select to_char(ssc.CREATE_TIME, 'yyyy') year, count(1) count
              from BIZ_STUDENT_SKILL_CERTI ssc
              where ssc.HOST_ORG_ID = #{hostOrgId}
              group by to_char(ssc.CREATE_TIME, 'yyyy'))
        group by year
        order by year desc
    </select>

    <select id="teacher" parameterType="string" resultType="java.util.Map">
        select case when ui.IS_OUT = '1' then '校外' when ui.IS_OUT = '0' then '校内' end type,
               count(ui.id)                                                               count
        from SYS_USER u
                 left join SYS_USER_INFO ui on ui.USER_ID = u.ID
        where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
          and ui.IS_OUT is not null
          and u.ORG_ID = #{hostOrgId}
          and u.STATUS != 'DELETED'
        group by ui.IS_OUT
    </select>

    <select id="outTeacher" parameterType="string" resultType="java.util.Map">
        select case
                   when ui.CATEGORY = 'QY' then '企业'
                   when ui.CATEGORY = 'GX' then '高校'
                   when ui.CATEGORY = 'DZGB' then '党政干部' end type,
               count(ui.id)                                      count
        from SYS_USER U
                 left join SYS_USER_INFO UI on UI.USER_ID = U.ID
        where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
          and ui.IS_OUT = 1
          and ui.category is not null
          and u.ORG_ID = #{hostOrgId}
          and u.STATUS != 'DELETED'
        group by ui.category
    </select>

    <select id="inTeacher" parameterType="string" resultType="java.util.Map">
        select ui.CATEGORY  type,
               count(ui.id) count
        from SYS_USER U
                 left join SYS_USER_INFO UI on UI.USER_ID = U.ID
        where exists(select 1 from SYS_USER_2_ROLE ur where ur.USER_ID = u.ID and ur.role = 'TEACHER')
          and ui.IS_OUT = 0
          and ui.category is not null
          and u.ORG_ID = #{hostOrgId}
          and u.STATUS != 'DELETED'
        group by ui.category
    </select>

    <select id="income" parameterType="string" resultType="java.util.Map">
        select xm.YEARS, nvl(sum(xm.INCOME_AMOUNT), 0) amount, NVL(sum(pd.CONTRACT_AMOUNT), 0) total_amount
        from biz_xm xm
                 inner JOIN biz_plan_detail pd on pd.id = xm.id
        where xm.STATUS != 'APPLY'
          and xm.HOST_ORG_ID = #{hostOrgId}
        group by xm.YEARS
        order by xm.YEARS desc
    </select>

    <select id="education" parameterType="string" resultType="java.util.Map">
        select COUNTRY_NAME, COOPERATIVE_UNITS, RANK, PROJECT_CATEGORY, SPECIALITY, APPLY_TIME
        from INF_INTERNATIONAL_EDUCATION
        where host_org_id = #{hostOrgId}
    </select>

    <select id="xmSource" parameterType="string" resultType="java.util.Map">
        select nvl(t.type, '其他') type, count
        from (select d.DICT_NAME  type,
                     count(xm.ID) count
              from BIZ_XM xm
                       inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
                       left join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY and d.DICT_CODE = 'industryCategory'
              where xm.STATUS != 'APPLY'
                and xm.HOST_ORG_ID = #{hostOrgId}
                and (d.DICT_NAME = '建材建工' or
                     d.DICT_NAME = '汽车' or
                     d.DICT_NAME = '交通')
              group by d.DICT_NAME
              union
              select null         type,
                     count(xm.ID) count
              from BIZ_XM xm
                       inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
                       left join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY and d.DICT_CODE = 'industryCategory'
              where xm.STATUS != 'APPLY'
                and xm.HOST_ORG_ID = #{hostOrgId}
                and ((d.DICT_NAME != '建材建工' and
                      d.DICT_NAME != '汽车' and
                      d.DICT_NAME != '交通') or d.DICT_NAME is null)) t
    </select>

    <select id="expend" parameterType="string" resultType="java.util.Map">
        select d.DICT_NAME    type,
               sum(xe.AMOUNT) amount
        from BIZ_XM_EXPEND xe
                 left join SYS_DICT d on d.id = xe.CONTENT
                 left join biz_xm xm on xm.id = xe.XM_ID
        where xm.STATUS != 'APPLY'
          and xm.HOST_ORG_ID = #{hostOrgId}
          and xm.YEARS = to_char(add_months(sysdate, -12), 'yyyy')
        group by d.DICT_NAME
    </select>

    <select id="featureXm" parameterType="string" resultType="java.util.Map">
        select d.DICT_NAME CATEGORY, xm.title, nvl(pd.COUNT, 0) count, nvl(pd.CONTRACT_AMOUNT, 0) CONTRACT_AMOUNT
        from biz_xm xm
                 inner join BIZ_PLAN_DETAIL pd on pd.id = xm.id
                 inner join SYS_DICT d on d.id = pd.INDUSTRY_CATEGORY
        where pd.INDUSTRY_CATEGORY is not null
          and xm.STATUS != 'APPLY'
          and xm.HOST_ORG_ID = #{hostOrgId}
    </select>
</mapper>