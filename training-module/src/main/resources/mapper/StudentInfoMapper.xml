<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.StudentInfoMapper">

    <select id="getByStudentId" resultType="com.xunw.jxjy.model.inf.entity.StudentInfo">
		select
			u.*
		from
			sys_student_info u
		where
			u.student_id=#{studentId}
    </select>

	<select id="findBysfzh" resultType="com.xunw.jxjy.model.inf.entity.StudentInfo">
    	select
    		f.*
    	from
    		sys_student_info f
     	where
     		upper(f.sfzh) = upper(#{sfzh}) and f.reg_host_org_id = #{hostOrgId}
    </select>

    <select id="getZcpsBmList" resultType="HumpSQLResultMap">
		select
			si.student_id,
			si.name,
			bm.id bm_id,
			bm.industry_id,
			p.name profession_name,
			bm.apply_tech_level,
			si.mobile,
			s.company,
			gs.num,
			gs.status,
			gs.start_db_time
		from
			sys_student s
		inner join sys_student_info si on si.student_id = s.id
		inner join biz_group_student gs on gs.student_id = s.id
		inner join biz_group g on g.id = gs.group_id
		inner join biz_zyjd_bmbatch zb on zb.id = g.bmbatch_id
		inner join biz_zyjd_bm bm on bm.bmbatch_id = zb.id and bm.student_id = s.id
		left join inf_profession p on p.id = bm.profession_id
		<where>
			bm.apply_tech_level = 'ONE' and bm.status = 'SHTG'
			<if test="groupId != null and groupId != ''">
				and g.id = #{groupId}
			</if>
		</where>
		order by gs.num
    </select>

    <select id="getFinishedStudent" resultType="HumpSQLResultMap">
		select
			si.student_id,
			si.name,
			bm.id bm_id,
			bm.industry_id,
			p.name profession_name,
			bm.apply_tech_level,
			si.mobile,
			s.company,
			gs.num,
			gs.status,
			gs.start_db_time
		from
			sys_student s
		inner join sys_student_info si on si.student_id = s.id
		inner join biz_group_student gs on gs.student_id = s.id
		inner join biz_group g on g.id = gs.group_id
		inner join biz_zyjd_bmbatch zb on zb.id = g.bmbatch_id
		inner join biz_zyjd_bm bm on bm.bmbatch_id = zb.id and bm.student_id = s.id
		left join inf_profession p on p.id = bm.profession_id
		<where>
			gs.status != '0'
			<if test="groupId != null and groupId != ''">
				and g.id = #{groupId}
			</if>
		</where>
		order by gs.num
	</select>

    <select id="queryOrderByStudentIds" resultType="java.util.Map">
		SELECT
			stu.id,
			info.name,
			DECODE( SIGN( COUNT( o.id ) - 0 ), 1, 1, 0 ) has_order
		FROM
			SYS_STUDENT stu
		LEFT JOIN SYS_STUDENT_INFO info ON stu.id = info.student_id
		LEFT JOIN COMM_ORDER o ON stu.id = o.PAY_USER_ID
		WHERE
			stu.id IN
			<foreach collection="list" item="studentId" index="index" open="(" close=")" separator=",">
				#{studentId}
			</foreach>
		GROUP BY
			stu.id,
			info.name
	</select>
    
    <select id="getStudent" resultType="com.xunw.jxjy.model.inf.entity.StudentInfo">
		SELECT *
		FROM SYS_STUDENT_INFO info
		WHERE info.STUDENT_ID IN (SELECT b.STUDENT_ID FROM biz_bm b where b.XM_ID = #{xmId})
		  and info.REG_HOST_ORG_ID = #{orgId}
    </select>

    <select id="getByStudentIds" resultType="com.xunw.jxjy.model.inf.entity.StudentInfo">
		SELECT
			*
		FROM
			SYS_STUDENT_INFO si
		where si.student_id in
		<foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
			#{studentId}
		</foreach>
    </select>
</mapper>
