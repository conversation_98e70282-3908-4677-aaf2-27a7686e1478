<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zyjd.mapper.ZcGroupStudentMapper">

	<select id="list" resultType="com.xunw.jxjy.model.zyjd.entity.ZcGroupStudent">
		select
			gs.*
		from
			biz_group_student gs
		inner join biz_group g on g.id = gs.group_id
		<where>
			<if test="type != null and type != ''">
				g.type = #{type}
			</if>
			<if test="bmbatchId != null and bmbatchId != ''">
				and g.id = #{bmbatchId}
			</if>
		</where>
		order by g.num, g.id
	</select>

	<select id="notGroupStudent" resultType="com.xunw.jxjy.model.inf.entity.StudentInfo">
		select
			si.*
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch zb on zb.id = bm.bmbatch_id and zb.type = 'ZCPS'
		inner join sys_student_info si on si.student_id = bm.student_id
		where
			bm.bmbatch_id = #{bmbatchId} and bm.apply_tech_level = 'ONE' and bm.status = 'SHTG'
			and not exists(select 1 from biz_group_student gs
							inner join biz_group g on g.id = gs.group_id
							where g.type = #{type} and g.bmbatch_id = zb.id
							and gs.student_id = si.student_id)
			<if test='type == "2"'>
				and exists(select 1 from biz_group_student gs
							inner join biz_group g on g.id = gs.group_id
							where gs.student_id = si.student_id and g.type = '1'
							and g.bmbatch_id = zb.id
							and gs.status = '2')
			</if>
	</select>

	<select id="getGroupStudentCount" resultType="java.lang.Integer">
		select
			count(gs.student_id) count
		from
			biz_zyjd_bm bm
		inner join biz_zyjd_bmbatch zb on zb.id = bm.bmbatch_id and zb.type = 'ZCPS'
		inner join biz_group g on g.bmbatch_id = zb.id and g.type = #{type}
		inner join biz_group_student gs on gs.group_id = g.id and gs.student_id = bm.student_id
		where
			bm.bmbatch_id = #{bmbatchId} and bm.apply_tech_level = 'ONE' and bm.status = 'SHTG'
    </select>
</mapper>