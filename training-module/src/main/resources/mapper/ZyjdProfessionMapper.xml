<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.ZyjdProfessionMapper">

    <select id="list" parameterType="map" resultType="HumpSQLResultMap">
        select
        	p.id,
        	p.industry_id,
        	p.code,
            nvl2(p.direction, p.name||'('||p.direction||')', p.name) name,
        	d.code industry_code,
        	d.name industry_name,
        	p.creator_id,
        	p.create_time,
        	p.updator_id,
        	p.update_time,
        	p.status,
        	p.short_image,
        	t.org_name
        from
        	inf_profession p
        inner join inf_industry d on d.id = p.industry_id
        left join (select pa.profession_id,wm_concat(to_char(g.name)) org_name from inf_profession_assign pa inner join sys_org g on g.id = pa.host_org_id group by pa.profession_id) t on t.profession_id = p.id 
        <where>
            <if test="industryWord != null and industryWord != ''">
               	(d.name like '%' || #{industryWord} || '%' or d.code like '%' || #{industryWord} || '%')
            </if>
            <if test="industryId != null and industryId != ''">
               	and d.id=#{industryId}
            </if>
            <if test="professionWord != null and professionWord != ''">
               	and (p.name like '%' || #{professionWord} || '%' or p.code like '%' || #{professionWord} || '%')
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and exists(select 1 from inf_profession_assign asg where asg.host_org_id=#{hostOrgId} and asg.profession_id=p.id)
            </if>
            <if test="typeId != null and typeId != ''">
                and p.type_id in (select p.id from inf_type p start with id=#{typeId} connect by prior p.id = p.parent_id)
            </if>
            <if test="keyword != null and keyword != ''">
               	and (p.name like '%' || #{keyword} || '%' or p.code like '%' || #{keyword} || '%')
            </if>
            <if test="type != null and type != '' and type == 'ZCPS'">
               	and d.id in ('DL', 'TY')
            </if>
        </where>
        order by p.create_time desc
    </select>

    <select id="getHostOrgProfessionList" parameterType="map" resultType="HumpSQLResultMap">
        select
            p.id,
            p.industry_id,
            p.code,
            nvl2(p.direction, p.name||'('||p.direction||')', p.name) name,
            d.code industry_code,
            d.name industry_name,
            asg.receive_org_id,
            s.name receive_org_name,
            p.status
        from
            inf_profession p
        inner join inf_industry d on d.id = p.industry_id
        inner join inf_profession_assign asg on asg.profession_id = p.id
        left join sys_org s on s.id = asg.receive_org_id
        <where>
            <if test="hostOrgId != null and hostOrgId !=''">
                 and asg.host_org_id = #{hostOrgId}
            </if>
            <if test="industryId != null and industryId != ''">
                and d.id = #{industryId}
            </if>
            <if test="professionId != null and professionId != ''">
                and p.id = #{professionId}
            </if>
            <if test="receiveOrgId != null and receiveOrgId != ''">
            	and asg.receive_org_id =  #{receiveOrgId}
            </if>
        </where>
    </select>
    
    <select id="getConditionByProfessionId" resultType="com.xunw.jxjy.model.inf.entity.ZyjdProfessionCondition">
    	select 
    		y.* 
    	from 
    		inf_profession_condition y 
    	<where>
    		 <if test="professionId != null and professionId != ''">
            	y.profession_id =  #{professionId}
            </if>
    	</where>
    	order by y.status desc,y.tech_level,y.condition_seq asc
    </select>

    <select id="selectIdsByNames" resultType="java.lang.String">
        SELECT
            p.id
        FROM
            inf_profession p
        LEFT JOIN INF_PROFESSION_ASSIGN pa ON p.id = pa.profession_id
        WHERE
            pa.host_org_id = #{hostOrgId}
            <if test="list != null and list.size > 0">
                and p.name in
                <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
    </select>

</mapper>