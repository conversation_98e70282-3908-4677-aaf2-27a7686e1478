<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxStudentCertiMapper">

    <select id="getCertiByStudent" parameterType="map" resultType="HumpSQLResultMap">
        select
            c.id,
            c.xm_id,
            c.student_id,
            c.certi_num,
            c.certi_url,
            c.certi_img_url,
            c.certi_time,
            c.is_print,
            c.create_time,
            c.is_publish,
            c.publish_time,
            t.id tid,
            nvl(xm.certi_xm_name,xm.title) name,
            t.demo,
            si.name student_name,
            si.sfzh,
            si.gender,
            s.company
        from
            biz_student_certi c
        inner join biz_xm xm on xm.id = c.xm_id
        inner join biz_certi_tpl t on t.id = xm.certi_tpl_id
        left join sys_student_info si on si.student_id = c.student_id
        left join sys_student s on s.id = c.student_id
        <where>
        	c.certi_num is not null
            <if test="studentId != null and studentId != ''">
                and c.student_id = #{studentId}
            </if>
            <if test="name != null and name != ''">
                and si.name = #{name}
            </if>
            <if test="sfzh != null and sfzh != ''">
                and si.sfzh = #{sfzh}
            </if>
            <if test="certiNum != null and certiNum != ''">
                and c.certi_num = #{certiNum}
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and xm.host_org_id = #{hostOrgId}
            </if>
            <if test="xmId != null and xmId != ''">
                and c.xm_id = #{xmId}
            </if>
        </where>
        order by c.create_time desc
    </select>

    <select id="list" resultType="HumpSQLResultMap">
		select
			f.name student_name,
			f.sfzh student_sfzh,
			c.id,
			c.xm_id,
			c.student_id,
			c.certi_num,
			c.certi_time,
      		c.certi_img_url,
			c.certi_url,
			xm.title,
			xm.serial_number
		from
			biz_student_certi c 
		inner join biz_xm xm on xm.id = c.xm_id
		inner join sys_student_info f on f.student_id = c.student_id
		<where>
			<if test="xmId != null and xmId != ''">
                c.xm_id = #{xmId}
            </if>
            <if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
			<if test="keyword != null and keyword != ''">
     			and (f.sfzh like '%' || #{keyword} || '%' or f.name like '%' || #{keyword} || '%')
     		</if>
     		<if test='isOnlyNoCertiNum != null and isOnlyNoCertiNum=="1"'>
				and c.certi_num is null
			</if>
			<if test='isOnlyCertiNum != null and isOnlyCertiNum=="1"'>
				and c.certi_num is not null
			</if>
			<if test="hostOrgId !=null and hostOrgId !=''">
				and xm.host_org_id = #{hostOrgId}
			</if>
		</where>
		order by c.certi_num asc,f.sfzh asc
    </select>

</mapper>