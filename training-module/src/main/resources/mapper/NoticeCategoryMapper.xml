<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.sys.mapper.NoticeCategoryMapper">

	<select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
		SELECT
			c.id,
			c.name,
			c.sort,
			c.remark,
		    c.status,
			count(n.category_id) count
		FROM
			sys_notice_category c
		left join sys_notice n on n.category_id = c.id
		<where>
			<if test="parentId != null and parentId != ''">
				c.parent_id=#{parentId}
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and c.host_org_id=#{hostOrgId}
			</if>
			<if test="status != null and status != ''">
				and c.status=#{status}
			</if>
			<if test="id != null and id != ''">
				and c.id=#{id}
			</if>
			<if test="name != null and name != ''">
				and c.name=#{name}
			</if>
		</where>
		group by c.id,c.name,c.sort,c.remark,c.status
		order by c.sort
    </select>


</mapper>
