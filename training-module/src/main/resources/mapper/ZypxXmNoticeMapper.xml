<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxXmNoticeMapper">

    <select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
        SELECT
            xn.*,
            t.is_read
        FROM
            BIZ_XM_NOTICE xn
        INNER JOIN (
            SELECT
                xn.ID,
                CASE WHEN count( xnr.ID ) > 0 THEN 1 ELSE 0 END is_read
            FROM
                BIZ_XM_NOTICE xn
            LEFT JOIN biz_xm_notice_record xnr ON xn.id = xnr.xm_notice_id AND xn.XM_ID = xnr.XM_ID
            <where>
                <if test="xmId != null and xmId != ''">
                    and xn.XM_ID = #{xmId}
                </if>
                <if test="status != null and status != ''">
                    and xn.STATUS = #{status}
                </if>
                <if test="content != null and content != ''">
                    and xn.content like '%'||#{title}||'%'
                </if>
                <if test="classId != null and classId != ''">
                    and (xn.CLASS_IDS = 'ALL' or xn.CLASS_IDS like '%'||#{classId}||'%')
                </if>
            </where>
            GROUP BY xn.ID) t on t.id = xn.id
        ORDER BY xn.CREATE_TIME DESC
    </select>
</mapper>
