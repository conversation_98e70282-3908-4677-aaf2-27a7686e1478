<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.CourseLiveMapper">
	
	<!-- 返回课程表全部字段 -->
	<select id="list" parameterType="Map" resultType="HumpSQLResultMap">
		select
			cl.id, 
			cl.course_id,
			cl.content,
			cl.teacher_ids,
			c.code course_code,
			c.name course_name,
		    c.hours,
			t.id type_id,
			t.name type_name,
			cl.creator_id,
			cl.create_time, 
			cl.updator_id,
			cl.update_time
		from
			inf_course_live cl
		inner join inf_course c on c.id = cl.course_id
		left join inf_type t on t.id = c.type_id
		<where>
			<if test="courseId!=null and courseId!=''">
				cl.course_id = #{courseId}
			</if>
			<if test='isPublic != null and isPublic=="1"'>
				and cl.is_public = 1
			</if>
			<if test='isPublic != null and isPublic=="0"'>
				and (cl.is_public is null or cl.is_public = 0 )
			</if>
			<if test="hostOrgId !=null and hostOrgId !=''">
				and c.host_org_id = #{hostOrgId}
			</if>
			<if test="teacherId!=null and teacherId!=''">
				and cl.teacher_ids like '%' || #{teacherId} || '%'
			</if>
			<if test="keyword!=null and keyword!=''">
				and (c.code like '%'||#{keyword}||'%' or c.name like '%'||#{keyword}||'%')
			</if>
		</where>
		order by cl.create_time desc
    </select>
    
    <select id="getLearningCountByLiveId" resultType="Integer">
    	select count(*) from watch_record w where w.liveid=#{liveId}
    </select>
    
    <select id="getLatestCourseLive" resultType="HumpSQLResultMap">
	    <![CDATA[
	    select t.* from(
	    	select
	    		cl.is_public,
	    		cl.id course_live_id,
	    		c.name course_name,
	    		case when zb.zbzt='PLAYING' then 1 else 0 end  is_living,
	    		row_number() over (partition by cl.id order by decode(zb.zbzt,'PLAYING',1,0) desc,zb.kssj asc) rn,
	    		zb.id live_id,
	    		zb.zbbt title,
	    		zb.js_id teacher_id,
	    		u.name teacher_name,
	    		zb.kssj start_time,
	    		zb.jssj end_time,
	    		nvl(xm.is_open_camera,0) is_open_camera,
    			nvl(xm.is_open_verify,0) is_open_verify,
    			xm.id xm_id,
	    	    xm.is_alllow_study_before_pay,
	    	    xm.is_allow_mobile_study,
	    	    xm.amount,
	    	    b.is_payed
	    	from
	    		inf_course_live cl
	    	inner join inf_course c on c.id = cl.course_id
			inner join biz_zb zb on zb.course_live_id = cl.id
	    	left join biz_xm_course_setting xcs on xcs.course_id = c.id and xcs.is_live = 1
	    	left join biz_xm xm on xm.id = xcs.xm_id
	    	left join sys_user u on u.id = zb.js_id
			left join biz_bm_course bc on bc.course_setting_id = xcs.id
			left join biz_bm b on bc.bm_id = b.id
	    	where
	    		(cl.is_public = 1 or b.student_id=#{studentId})
	    		and (zb.zbzt='PLAYING' or (zb.zbzt='READY' and zb.kssj > sysdate and zb.kssj < sysdate + 5))
	    		and c.host_org_id = #{hostOrgId}
	    	) t where t.rn=1 order by start_time asc
	    ]]>
    </select>
    
    <select id="getLiveViewRecord" resultType="HumpSQLResultMap">
    	select
    		f.name,
    		f.sfzh,
    		f.mobile,
			s.company,
    		r.watch_live_duration,
    		r.watch_replay_duration,
    		r.sum_watch_duration
    	from
    		view_watch_record r
    	inner join sys_student_info f on f.student_id = r.student_id
		inner join sys_student s on f.student_id = s.id
    	<where>
    		<if test="liveId!=null and liveId!=''">
    			r.live_id = #{liveId}
			</if>
    		<if test="keyword!=null and keyword!=''">
				and (f.name like '%'||#{keyword}||'%' or f.sfzh like '%'||#{keyword}||'%' or f.mobile like '%'||#{keyword}||'%')
			</if>
    	</where>
    	order by r.latest_update_time desc
    </select>
    
    <select id="getViewedStudentId" resultType="String">
    	select
    		 distinct v.student_id
    	from
    		view_watch_record v 
    	 inner join sys_student s on s.id = v.student_id
    	where 
    		v.course_live_id = #{courseLiveId}
    </select>
</mapper>