<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.TargetMapper">
	<select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
		select
			ta.*
		from
			biz_target ta
		<where>
			<if test="id != null and id != ''">
				ta.type_id = #{id}
			</if>
		</where>
		order by ta.create_time desc
	</select>

	<select id="getAllTargetByTypeIds" resultType="java.util.Map">
		select
			ta.*,
			bs.id setting_id
		from
			biz_target ta
		left join biz_target_setting bs on bs.target_id = ta.id
		<where>
			ta.type_id in (<foreach collection="typeIds" item="id" separator=",">#{id}</foreach>)
			<if test="xmId != null and xmId != ''">
				and bs.xm_id = #{xmId}
			</if>
		</where>
	</select>
</mapper>