<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.sys.mapper.NoticeMapper">
	
	<select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
		select
			xx.id,
			xx.title,
			xx.create_time,
			xx.update_time,
			xx.status,
			xx.is_to_top,
			xx.publish_time,
			DECODE( xx.count, NULL, '0', xx.count ) count,
			c.name category_name
		from
			sys_notice xx
		left join sys_notice_category c on xx.category_id=c.id
		<where>
			<if test="categoryId != null and categoryId != ''">
				xx.category_id in (select a.id from sys_notice_category a start with a.id=#{categoryId} connect by prior a.id = a.parent_id)
			</if>
			<if test="status != null and status != ''">
				and  xx.status=#{status}
			</if>
			<if test="wzWord != null and wzWord != ''">
				and  (xx.title like '%' || #{wzWord} || '%' or xx.content like '%' || #{wzWord} || '%')
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and c.host_org_id = #{hostOrgId}
			</if>
			<if test="receivers != null and receivers.length >0 ">
				and xx.receiver in
				<foreach collection="receivers" index="index" item="receiver" separator="," close=")" open="(">
		            #{receiver}
		        </foreach>
			</if>
		</where>
		order by xx.create_time desc
    </select>

	<select id="getInfoById" parameterType="String" resultType="HumpSQLResultMap">
		select
			xx.id, 
			xx.title, 
			xx.category_id, 
			xx.is_to_top, 
			xx.is_link,
			xx.link_url,
			xx.writer, 
			xx.status, 
			xx.source, 
			xx.content, 
			xx.url, 
			xx.creator_id,
			xx.publish_time,
			xx.receiver,
			xx.create_time,
			xx.updator_id, 
			xx.update_time, 
			xx.count,
		    u.name,
			fl.name category_name
		from
			sys_notice xx
		left join sys_notice_category fl on xx.category_id=fl.id
		left join sys_user u on xx.creator_id = u.id
		<where>
			xx.id=#{id}
		</where>
	</select>

	<select id="getByCategoryName" resultType="com.xunw.jxjy.model.sys.entity.Notice">
		select
			n.*,
			row_number() over(partition by n.category_id order by n.is_to_top desc,n.publish_time desc nulls last) sort_num
		from
			sys_notice n
		where
			category_id in (select id from sys_notice_category where name=#{name} and host_org_id=#{hostOrgId})
		order by sort_num
    </select>
</mapper>