<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.portal.mapper.ZkMapper">

	<select id="getSubjects" resultType="HumpSQLResultMap">
		select
			s_id,
			s_code,
			s_name,
			s_remark,
			s_create_time
		from
			sebms_study.tm_subject
		order by s_code
	</select>
	
	<select id="getSpecialtys" parameterType="Map" resultType="HumpSQLResultMap">
		select
			s_id,
			s_code,
			s_name,
			s_edulevel,
			s_subject_id,
			s_img_url,
			s_rounds,
			s_name_str<!-- 专业名称 -->
		from
			sebms_study.tm_specialty
		<where>
			<if test="keyword != null and keyword != ''">
				and (
					s_code like '%'||#{keyword}||'%'
					or s_name_str like '%'||#{keyword}||'%'
				)
			</if>
			<if test="s_edulevel != null and s_edulevel != ''">
				and s_edulevel=#{s_edulevel}
			</if>
			<if test="s_subject_id != null and s_subject_id != ''">
				and s_subject_id=#{s_subject_id}
			</if>
		</where>
	</select>
	
	<select id="getSpecialty" resultType="HumpSQLResultMap">
		select
			s_id,
			s_code,
			s_name,
			s_edulevel,
			s_require,
			s_explain,
			s_subject_id,
			s_introduction,
			s_img_url,
			s_rounds,
			s_name_str
		from
			sebms_study.tm_specialty
		where 
			s_id=#{s_id}
	</select>
	
	<select id="getCourseType" resultType="HumpSQLResultMap">
		select
			spct_id,
			s_id,
			type_name,
			spct_min_score,
			spct_sum_score,
			spct_remark,
			spct_status,
			spct_explain,
			spct_sort
		from
			sebms_study.tm_sp_course_type
		where 
			s_id=#{s_id}
		order by 
			spct_sort
	</select>
	
	<select id="getCourseChildType" resultType="HumpSQLResultMap">
		select
			spcct_id,
			spct_id,
			spcct_remark,
			spcct_sort,
			spcct_status,
			spcct_min_score,
			spcct_min_course
		from
			sebms_study.tm_sp_course_child_type
		where 
			spct_id=#{spct_id}
		order by 
			spcct_sort
	</select>
	
	<select id="getSpecialtyCourse" resultType="HumpSQLResultMap">
		select
			spc_id,
			spct_id,
			spcct_id,
			spc_no,
			spc_remark,
			c_id,
			spc_sort,
			spc_is_degree
		from
			sebms_study.tm_specialty_course
		where 
			spct_id=#{spct_id} and spcct_id=#{spcct_id}
		order by 
			spc_sort
	</select>
	
	<select id="getCourse" resultType="HumpSQLResultMap">
		select
			c_id,
			c_code,
			c_name,
			c_logo,
			c_score
		from
			sebms_study.tm_course
		where 
			c_id=#{c_id}
	</select>
	
	<select id="getCateogrys" resultType="HumpSQLResultMap">
		select
			c_id id,
			c_name name,
			c_remark remark,
			1 is_zk
		from
			sebms_study.tm_news_cateogry
		where
			c_type='自学考试'
		order by 
			c_createdate desc
	</select>
	
	<select id="getCateogry" resultType="HumpSQLResultMap">
		select
			c_id id,
			c_name name,
			c_remark remark,
			1 is_zk
		from
			sebms_study.tm_news_cateogry
		where
			c_id=#{c_id}
	</select>
	
	<select id="getNews" parameterType="Map" resultType="HumpSQLResultMap">
		select
			n.n_id id,
			n.n_title title,
			n.n_classid category_id,
			n.n_totop is_to_top,
			n.n_author writer,
			n.n_status status,
			n.n_newsfrom source,
			n.n_content content,
			'' url,
			0 is_link,
			'' link_url,
			n.n_createdate create_time,
			n.n_modifydate update_time,
			n.n_createdate publish_time,
			n.n_visit count,
			nc.c_name category_name,
   			row_number() over(partition by n.n_classid order by n.n_totop desc, n.n_createdate desc nulls last) sort_num
		from
			sebms_study.tm_news n
			left join tm_news_cateogry nc on c.c_id = n.n_classid
		<where>
			n.n_status = 1
			<if test="categoryId != null and categoryId != ''">
				and n.n_classid=#{categoryId}
			</if>
		</where>
		order by 
			n.n_totop desc
	</select>
	
	<select id="getNew" resultType="HumpSQLResultMap">
		select
			n.n_id id,
			n.n_title title,
			n.n_classid category_id,
			n.n_totop is_to_top,
			n.n_author writer,
			n.n_status status,
			n.n_newsfrom source,
			n.n_content content,
			'' url,
			0 is_link,
			'' link_url,
			n.n_createdate create_time,
			n.n_modifydate update_time,
			n.n_createdate publish_time,
			n.n_visit count,
			nc.c_name category_name,
   			row_number() over(partition by n.n_classid order by n.n_totop desc, n.n_createdate desc nulls last) sort_num
		from
			sebms_study.tm_news n
			left join tm_news_cateogry nc on c.c_id = n.n_classid
		where 
			n.n_id=#{n_id}
		order by
			n.n_totop desc
	</select>
	
</mapper>