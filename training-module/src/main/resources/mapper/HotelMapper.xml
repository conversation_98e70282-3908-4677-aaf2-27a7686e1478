<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.inf.mapper.HotelMapper">

    <select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
        	select
                h.id,
                h.host_org_id,
                h.address,
                h.map_position,
                h.lc_count,
                h.name,
                h.people,
                h.phone,
                h.total_count,
                h.serial_number,
                org.code,
                org.name org_name
        	from
            	inf_hotel h
            left join sys_org org on org.id = h.host_org_id
            <where>
                <if test="hostOrgId != null and hostOrgId != ''">
                    h.host_org_id = #{hostOrgId}
                </if>
                <if test="keyword != null and keyword != ''">
                     and (h.serial_number like '%'||#{keyword}||'%' or h.name like '%'||#{keyword}||'%' or h.people like '%'||#{keyword}||'%')
                </if>
            </where>
        	order by h.create_time desc
    </select>

    <select id="getByXmId" resultType="HumpSQLResultMap">
        select
            h.*,
            x2h.start_time,
            x2h.end_time
        from
            inf_hotel h
        inner join biz_xm_2_hotel x2h on x2h.hotel_id = h.id
        inner join biz_xm xm on xm.id = x2h.xm_id
        <where>
            <if test="id != null and id != ''">
                and xm.id = #{id}
            </if>
        </where>
        order by x2h.start_time
    </select>
</mapper>