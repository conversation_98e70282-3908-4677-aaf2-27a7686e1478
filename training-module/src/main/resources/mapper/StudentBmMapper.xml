<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.personal.mapper.StudentBmMapper">

   	<!--  查询职业培训可报名的项目 -->
    <select id="getChooseXmList" parameterType="map" resultType="HumpSQLResultMap">
		select
			xm.*,
			tbl.total_hours,
			type.name type_name,
			nvl(p.is_skill,'0') is_skill,
			bb2f.form_id,
		   	case when xm.status='OK' and xm.is_allow_grbm=1 and sysdate>xm.grbm_start_time and xm.grbm_end_time>sysdate then 1 else 0 end as is_bm_open,
		    bm.id bm_id
		from
		  	biz_xm xm
		inner join biz_plan p on p.id = xm.plan_id
		left join (select xcs.xm_id,sum(c.hours) total_hours from biz_xm_course_setting xcs
				inner join inf_course c on c.id =  xcs.course_id group by xcs.xm_id) tbl on tbl.xm_id = xm.id
		left join inf_type type on xm.type_id=type.id
		left join biz_xm_2_form bb2f on bb2f.xm_id=xm.id
		left join biz_bm bm on bm.xm_id = xm.id and bm.student_id = #{studentId}
		<where>
			xm.status='OK' and type.status='OK' and xm.is_allow_grbm = 1
			<if test="xmId!=null and xmId!=''">
				and	xm.id =#{xmId}
			</if>
			<if test="hostOrgId !=null and hostOrgId!=''">
				and	p.host_org_id =#{hostOrgId}
			</if>
			<if test="typeId!=null and typeId !=''">
				and xm.type_id in (select p.id from inf_type p start with p.id=#{typeId} connect by prior p.id = p.parent_id)
			</if>
			<if test="planId!=null and planId != ''">
				and p.id=#{planId}
			</if>
		  </where>
		order by xm.create_time desc,xm.title desc
    </select>

    <!-- 查询培训项目下可以报名的课程 -->
    <select id="getXmCourseList" parameterType="map" resultType="HumpSQLResultMap">
    	select
    		xcs.id course_setting_id,
    		xcs.course_id,
    		c.code course_code,
    		c.name course_name,
    		c.hours,
    		c.logo,
    		c.xct,
    		c.remark,
    		xcs.is_courseware,
    		xcs.is_live,
    		xcs.is_ms,
    		xcs.amount course_amount
    	from
    		biz_xm xm
    	inner join biz_xm_course_setting xcs on xcs.xm_id = xm.id
    	inner join inf_course c on c.id = xcs.course_id
    	where
    		xm.is_course_publish=1 and xm.id = #{xmId}
    </select>

    <!-- 查询学员已经报名的培训项目 -->
    <select id="getStudentBmXmList" parameterType="map" resultType="HumpSQLResultMap">
    	select
    		bm.id bm_id,
    		bm.xm_id,
    		bm.student_id,
    		bm.is_jtbm,
    		bm.jtbm_user_id,
    		bm.is_payed,
    		bm.jtbm_org_id,
    		bm.status,
    		bm.time,
    		bm.approve_advice,
    		bm.invoice_path,
			xm.buy_type,
			bc.amount total_course_amount,
			bm.invoice_path,
    		xm.trainees,
    		xm.serial_number,
    		xm.title,
    		xm.grbm_start_time,
    		xm.grbm_end_time,
    		xm.start_time,
    		xm.end_time,
    		xm.amount,
    		xm.status xm_status,
    		xm.study_end_time,
    		xm.is_open_camera,
    		xm.is_open_verify,
    		xm.is_pop_ques,
    		xm.is_allow_mobile_study,
    		xm.is_alllow_study_before_pay,
    		xm.is_alllow_exam_before_pay,
    	    nvl(arr.poiaddress,null) poiaddress,
    	    nvl(arr.poilatlng,null) poilatlng,
    	    nvl(arr.arrive_time,null) arrive_time,
    	    nvl(arr.poiname,null) poiname,
    	    nvl(xm.learning_score_zb,0) learning_score_zb,
    	    nvl(xm.practice_score_zb,0) practice_score_zb,
    	    nvl(xm.final_exam_score_zb,0) final_exam_score_zb,
    	    xm.poiaddress curpoiaddress,
    	    xm.poilatlng curpoilatlng,
    		xm.arrive_range,
    		xm.xct,
    	    xm.is_allow_choose_course,
    	    xm.is_allow_mobile_study,
    	    xm.is_alllow_study_before_pay,
    	    xm.is_alllow_exam_before_pay,
    	    xm.is_need_approve,
    	    xm.is_open_study,
    	    xm.open_study_url,
			xm.open_study_type,
    	    si.mobile,
    	    form.form_id,
    		type.name type_name,
    		p.id plan_id,
    		p.name plan_name,
    		m.qa_id,
    		case when xm.status='OK' and xm.end_time>sysdate then 1 else 0 end as isunderway,
    		case when xm.status='OK' and xm.is_allow_grbm=1 and sysdate>xm.grbm_start_time and xm.grbm_end_time>sysdate then 1 else 0 end as isbming,
    		case when xm.status='OK' and (xm.study_end_time>sysdate or xm.study_end_time is null) then 1 else 0 end as isvideostudy
    	from
    		biz_bm bm
    	inner join biz_xm xm on xm.id = bm.xm_id
    	inner join biz_plan p on p.id = xm.plan_id
		left join (select sum(nvl(xcs.amount, 0)) amount, bc.bm_id from biz_bm_course bc inner join biz_xm_course_setting xcs on bc.course_setting_id = xcs.id group by bc.bm_id) bc on bc.bm_id = bm.ID
		left join sys_student_info si on si.student_id = bm.student_id
    	left join inf_type type on xm.type_id=type.id
    	left join biz_xm_2_form form on xm.id=form.xm_id
    	left join biz_xm_arrive arr on arr.xm_id = xm.id and arr.student_id = #{studentId}
    	left join (select x2q.xm_id,x2q.qa_id,q.create_time from biz_xm_2_qa x2q inner join biz_questionnaire q on q.id = x2q.qa_id
				where q.status = 'SENT_DWON') m on m.xm_id = xm.id
		<where>
			<if test='isOnlyPass !=null and isOnlyPass == "1"'>
				bm.status='BMCG'
			</if>
			<if test='isunderway !=null and isunderway == "1"'>
				and xm.status='OK' and xm.end_time>sysdate
			</if>
			<if test='isvideostudy !=null and isvideostudy == "1"'>
				and xm.status='OK' and (xm.study_end_time>sysdate or xm.study_end_time is null) and xm.end_time>sysdate
			</if>
    		<if test="studentId!=null and studentId!=''">
				and bm.student_id =#{studentId}
	   		</if>
	   		<if test="xmId!=null and xmId!=''">
				and	bm.xm_id =#{xmId}
	   		</if>
    	</where>
    	order by isunderway desc, xm.create_time desc
    </select>

    <!-- 查询学生的报名的课程信息 -->
    <select id="getStudentBmCourseList" parameterType="map" resultType="HumpSQLResultMap">
    	select
	   		bm.id bm_id,
	        bm.xm_id,
	        nvl(bm.is_payed,0) is_payed,
	        xm.arrive_range,
    	    xm.title xm_name,
    	    nvl(xm.is_open_camera,0) is_open_camera,
    		nvl(xm.is_open_verify,0) is_open_verify,
    	    xm.amount,
			xm.buy_type,
    		xm.is_allow_mobile_study,
    		nvl(xm.is_alllow_study_before_pay,0) is_alllow_study_before_pay,
    		nvl(xm.is_alllow_exam_before_pay,0) is_alllow_exam_before_pay,
    	    case when xm.status='OK' and xm.end_time>sysdate then 1 else 0 end as isunderway,
    		case when xm.status='OK' and (xm.study_end_time>sysdate or xm.study_end_time is null) and xm.end_time>sysdate then 1 else 0 end as isvideostudy,
    		p.id plan_id,
    		p.name plan_name,
	        bmc.course_setting_id,
	        xcs.is_live,
	        xcs.is_courseware,
	        xcs.is_ms,
	        xcs.ms_content,
	        xcs.teacher_name,
	        xcs.start_time,
	        xcs.end_time,
	        bmc.bm_time,
	        c.id course_id,
	        c.code course_code,
	        c.name course_name,
	        c.xct,
	        c.logo,
	        cl.id course_live_id,
	        cl.content live_content,
	        kj.id kj_id,
	        kj.content kj_content,
	        lcs.hours,
			lcs.finished_hours,
			lcs.progress
    	from
    		biz_bm bm
    	inner join biz_bm_course bmc on bm.id = bmc.bm_id
    	inner join biz_xm xm on xm.id = bm.xm_id
    	inner join biz_plan p on p.id = xm.plan_id
    	left join biz_xm_course_setting xcs on xcs.id = bmc.course_setting_id
    	left join inf_course c on c.id = xcs.course_id
    	left join view_learning_course_score lcs on lcs.xm_id = bm.xm_id and lcs.course_id = c.id and lcs.student_id = bm.student_id
    	left join inf_course_live cl on cl.course_id = c.id
    	left join inf_kj kj on kj.course_id = c.id and kj.status='OK'
    	left join sys_user u on u.id = xcs.teacher_id
    	<where>
	    	<if test="studentId!=null and studentId!=''">
				bm.student_id =#{studentId}
	   		</if>
	   		<if test="xmId!=null and xmId!=''">
				and	bm.xm_id =#{xmId}
	   		</if>
    	</where>
    	order by xcs.start_time asc, xcs.end_time asc
    </select>

    <select id="getCoursewareStudyLatestEndTime" resultType="java.util.Date">
    	select
			max(p.end_time) end_time
		from
			biz_kj_progress p
		inner join inf_kj kj on kj.id = p.kj_id and kj.status='OK'
		inner join inf_course c on c.id = kj.course_id
		where
		     p.xm_id = #{xmId} and kj.course_id = #{courseId} and p.student_id=#{studentId}
    </select>

    <select id="getLiveStudyLatestEndTime" resultType="java.util.Date">
    	select
    		r.latest_update_time updatetime
		from
			view_zbxx_record r
		where
			r.xm_id=#{xmId} and r.student_id=#{studentId} and r.course_id = #{courseId}
    </select>

    <select id="getPublicCourseLiveStudyedByStudentId"  resultType="HumpSQLResultMap">
    	select
    		c.id course_id,
    		c.name course_name,
    		cl.id course_live_id,
    		m.updatetime study_time,
    		m.live_id,
    		m.js_id teacher_id,
    		u.name teacher_name
		from
			inf_course c
		inner join inf_course_live cl on cl.course_id = c.id
		inner join(
			select live_id,js_id,course_live_id,updatetime from(
				select zb.id live_id,zb.js_id,zb.course_live_id,r.updatetime,row_number() over(partition by zb.course_live_id order by r.updatetime desc) rn
				from watch_record r
				inner join biz_zb zb on zb.id = r.liveid where r.userid = #{studentId}) t where t.rn = 1
		) m on m.course_live_id = cl.id
		left join sys_user u on u.id = m.js_id
		where
			cl.is_public = 1
		 order by m.updatetime desc
    </select>

    <select id="getOpenCourseForLearningByStudentId" resultType="HumpSQLResultMap">
    	select
			c.id course_id,
		    c.name course_name,
			kj.id kj_id,
			c.amount,
			c.logo,
		    c.hours
		from
			inf_course c
		inner join inf_kj kj on kj.course_id = c.id and kj.status='OK'
		where
			c.host_org_id = #{hostOrgId} and c.is_open_kj='1'
			and (
				exists(select 1 from biz_student_bm_course sbc where sbc.student_id = #{studentId} and sbc.course_id = c.id and sbc.is_payed = '1')
				or exists(select 1 from biz_kj_progress p where p.xm_id='_personal' and p.kj_id=kj.id and p.student_id = #{studentId} and (c.amount = 0 or c.amount is null))
			)
    </select>

    <select id="getBmOpenCourseByStudentId" resultType="HumpSQLResultMap">
    	select
    		sbc.id bm_id,
			c.id course_id,
		    c.name course_name,
			kj.id kj_id,
			c.amount,
			nvl2(sbc.is_payed,sbc.is_payed,0) is_payed,
			sbc.student_id,
			c.logo,
		    c.hours
		from
			inf_course c
		inner join inf_kj kj on kj.course_id = c.id and kj.status='OK'
		inner join biz_student_bm_course sbc on sbc.course_id = c.id
		where
			c.host_org_id = #{hostOrgId} and c.is_open_kj='1' and sbc.student_id = #{studentId}
		order by sbc.create_time desc
    </select>

   <!--  获取课程报名的数量，包含项目下报名的课程和 个人报名的课程 -->
    <select id="getBmCountByCourseId" resultType="java.lang.Integer">
    	select
    		sum(nvl(a.count,0)+nvl(b.count,0)) as count
    	from
    		inf_course c
    	left join (select count(1) count,sbc.course_id from biz_student_bm_course sbc group by sbc.course_id) a on a.course_id = c.id
    	left join (select count(1) count,xcs.course_id from biz_bm_course bc inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id group by xcs.course_id) b on b.course_id = c.id
    	where
    		c.id = #{courseId}
    	group by c.id,c.name
    </select>
    
    <select id="getStudentByXmId" parameterType="map" resultType="HumpSQLResultMap">
		select
			c.name class_name,
			u.name headermaster_name,
			ui.gender headermaster_gender,
			u.mobile headermaster_mobile,
			ui.zw headermaster_zw,
			si.student_id,
			si.name,
			si.gender,
			si.mobile,
			si.zw
		from
		  	biz_bm bm
		left join comm_class c on c.id = bm.class_id
		left join sys_user u on u.id = c.headermaster_id
		left join sys_user_info ui on ui.user_id = c.headermaster_id
		left join sys_student_info si on si.student_id = bm.student_id
		<where>
			bm.status='BMCG'
			and (bm.shift_duty != 0 or bm.shift_duty is null)
			<if test="xmId!=null and xmId!=''">
				and	bm.xm_id =#{xmId}
			</if>
		  </where>
		order by c.sort_no, bm.time desc
    </select>
    
</mapper>