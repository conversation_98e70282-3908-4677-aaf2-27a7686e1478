<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.PlanDetailMapper">

	<select id="pageQuery" parameterType="map" resultType="HumpSQLResultMap">
		select
			pd.id,
			pd.plan_id,
			pd.name,
			pd.serial_number,
			pl.type_id,
			pd.status,
			pd.save_time,
			pd.submit_time,
			pd.approve_item_id,
			pd.is_execute,
			pd.execute_time,
			pd.contract,
			pd.contract_status,
			pl.needs_id,
			pd.pyfa,
			pd.xm_sources,
			pd.entrust_org_id,
			pd.training_form,
			pd.is_issue_certificate,
			pd.teachers,
			pd.enroll_way,
			pd.units,
			pd.leader_id,
			pd.address,
			pd.hours,
			pd.amount,
			pd.count,
			pd.start_time,
			pd.end_time,
			pd.paper,
			pd.approve_status,
			pd.approval_time,
			pd.approve_user_id,
			i.name type_name,
			pl.host_org_id,
			pl.is_skill,
			horg.code as host_org_code,
			horg.name as host_org_name,
			corg.code as cooper_code,
			corg.name as cooper_name,
			corg.contact,
			corg.telephone,
			pd.receive_org_id,
			a.name organizer_name,
			yh1.name create_user_name,
			decode(ai.status,'END','已办结',nvl2(t.approve_user_name, '待'||replace(t.approve_user_name,',','/')||'审批...',nvl2(yh3.name, '待'||replace(yh3.name,',','/')||'审批...', ''))) approve_user_name
		from
			biz_plan_detail pd
		inner join biz_plan pl on pl.id = pd.plan_id
		left join comm_approve_item ai on ai.id = pd.approve_item_id
		left join (select au.step_id,wm_concat(to_char(u.name)) approve_user_name from comm_approve_user au
			inner join sys_user u on au.user_id = u.id group by au.step_id) t on ai.next_step_id = t.step_id
		left join sys_org corg on corg.id = pd.entrust_org_id
		left join sys_org horg on horg.id = pl.host_org_id
		left join sys_org a on a.id = pd.receive_org_id
		left join inf_type i on i.id = pl.type_id
		LEFT JOIN sys_user yh1 ON yh1.id = pd.user_id
		left join sys_user yh2 on yh2.id = pd.leader_id
		left join sys_user yh3 on yh3.id = ai.pre_user_id
		<where>
			<if test="planId != null and planId != ''">
				pl.id = #{planId}
			</if>
			<if test="typeId != null and typeId != ''">
				pl.type_id = #{typeId} and (pl.is_skill = 0 or pl.is_skill is null)
			</if>
			<if test="bmbatchId != null and bmbatchId != ''">
				pl.bmbatch_id = #{bmbatchId}
			</if>
			<if test="hostOrgId != null and  hostOrgId != ''">
				and pl.host_org_id = #{hostOrgId}
			</if>
			<if test="year != null and  year != ''">
				and pl.years = #{year}
			</if>
			<if test="keyword != null and keyword != ''">
				and (pd.name like '%' || #{keyword} || '%' or  pd.serial_number like '%' || #{keyword} || '%')
			</if>
			<if test="status != null and status != ''">
				and  pd.status = #{status}
			</if>
			<if test="userId != null and userId != ''">
				and  pd.user_id = #{userId}
			</if>
			<if test="startTime!=null and startTime!=''">
			    <![CDATA[AND pd.save_time >=  to_date(#{startTime}, 'yyyy-mm-dd')]]>
			</if>
			<if test="endTime!=null and endTime!=''">
				<![CDATA[AND pd.save_time <=  to_date(#{endTime}, 'yyyy-mm-dd')]]>
			</if>
			<if test='isExecute == "1"'>
				and  pd.is_execute = #{isExecute}
			</if>
			<if test='isExecute == "0"'>
				and  (pd.is_execute = #{isExecute} or pd.is_execute is null)
			</if>
		</where>
		order by pd.save_time desc
	</select>

	<select id="approveItemPage" parameterType="Map" resultType="HumpSQLResultMap">
		SELECT
			pd.id,
			pd.name,
			pd.serial_number,
			pl.type_id,
			t.name type_name,
			pd.status,
			pd.save_time,
			pd.submit_time,
			pd.approve_item_id,
			pd.is_execute,
			pd.execute_time,
			pd.contract,
			pd.pyfa,
			pd.paper,
			pl.needs_id,
			pd.user_id,
			g.name receive_org_name,
			yh1.name create_user_name,
			pl.years,
			case when instr(t.approve_user_id, #{approveUserId}) > 0 and 'HOST_ORG' = #{curRole} then 1
				when instr(item.pre_user_id, #{approveUserId}) > 0 and item.pre_status = '2' and 'HOST_ORG_CHILD' = #{curRole} then 1
				else 0 end as is_current_user_approve,
			decode(item.status, 'END', '已办结', nvl2(t.approve_user_name, '待'||replace(t.approve_user_name,',','/')||'审批...', nvl2(yh2.name, '待'||replace(yh2.name,',','/')||'审批...', ''))) approve_user_name
		from
			comm_approve_item item
		inner join biz_plan_detail pd on item.id = pd.approve_item_id
		inner join biz_plan pl on pl.id = pd.plan_id
		left join inf_type t on t.id = pl.type_id
		left join sys_user yh1 on yh1.id = pd.user_id
		left join sys_user yh2 on yh2.id = item.pre_user_id
		left join sys_org g on g.id = pd.receive_org_id
		left join (select au.step_id,wm_concat(to_char(u.name)) approve_user_name,wm_concat(to_char(u.id)) approve_user_id from comm_approve_user au inner join sys_user u on au.user_id = u.id group by au.step_id) t on item.next_step_id = t.step_id
		<where>
			pd.status != 'DRAFT'
			and (exists(select 1 from comm_approve_user au where au.step_id = item.next_step_id and au.user_id = #{approveUserId})
					or exists(select 1 from comm_approve_log log where log.item_id =item.id and log.user_id = #{approveUserId})
					or item.pre_user_id = #{approveUserId})
			<if test="planId != null and planId != ''">
				and pl.id=#{planId}
			</if>
			<if test="year != null and year != ''">
				and pl.years=#{year}
			</if>
			<if test="keyword != null and keyword != ''">
				and (pd.name like '%' || #{keyword} || '%' or  pd.serial_number like '%' || #{keyword} || '%')
			</if>
			<if test="status != null and status != ''">
				and pd.status=#{status}
			</if>
			<if test="result != null and result != ''">
				and exists(select 1 from comm_approve_log log where log.item_id =item.id and log.user_id = #{approveUserId} and log.result=#{result})
			</if>
			<if test="startTime!=null and startTime!=''">
				<![CDATA[AND  pd.submit_time >=  to_date(#{startTime}, 'yyyy-mm-dd')]]>
			</if>
			<if test="endTime!=null and endTime!=''">
				<![CDATA[AND pd.submit_time <=  to_date(#{endTime}, 'yyyy-mm-dd')]]>
			</if>
		</where>
		order by pd.submit_time desc
	</select>

	<select id="getPlanApproveSteps" parameterType="string" resultType="HumpSQLResultMap">
		select
			step.id,
			step.step_num,
			step.step_name
		from
			comm_approve_step step
		inner join comm_approve app on app.id = step.approve_id
		inner join comm_approve_item item on item.approve_id = app.id
		inner join biz_plan_detail pd on pd.approve_item_id = item.id
		where
			pd.id = #{planDetailId}
		order by step.step_num
	</select>

	<select id="getPlanApproveLogs" parameterType="string" resultType="HumpSQLResultMap">
		select
			step.id,
			step.step_num,
			step.step_name,
			log.user_id,
			su.name user_name,
			log.result,
			log.reason,
			log.time
		from
			comm_approve_log log
		inner join comm_approve_item item on item.id = log.item_id
		inner join biz_plan_detail pd on pd.approve_item_id = log.item_id
		left join comm_approve_step step on step.id = log.step_id
		left join sys_user su on su.id = log.user_id
		where
			pd.id = #{planDetailId}
	</select>

	<select id = "getApprovePaperPage" parameterType="map" resultType="HumpSQLResultMap">
		select
			p.id,
			p.serial_number,
			p.name,
			p.paper,
			p.approve_user_id,
			p.approve_status,
			p.approval_time,
			p.approve_advice,
			u.name user_name,
			t.name type_name
		from
			biz_plan_detail p
		inner join biz_plan pl on  pl.id = p.plan_id
		left join inf_type t on t.id = pl.type_id
		left join sys_user u on u.id = p.approve_user_id
		<where>
			<if test="hostOrgId != null and  hostOrgId != ''">
				and pl.host_org_id = #{hostOrgId}
			</if>
			<if test="userId != null and userId != ''">
				and pl.user_id = #{userId}
			</if>
			<if test="planId != null and planId != ''">
				and pl.id = #{planId}
			</if>
			<if test="year != null and year != ''">
				and pl.years = #{year}
			</if>
			<if test="approveStatus != null and approveStatus != ''">
				and NVL(p.approve_status, 'WAIT') = #{approveStatus}
			</if>
			<if test='flag != null and flag == "0"'>
				and p.paper is not null
			</if>
			<if test="keyword != null and keyword != ''">
				and (p.name like '%' || #{keyword} || '%')
			</if>
		</where>
		order by pl.create_time desc
	</select>


	<select id = "getApproveContracPage" parameterType="map" resultType="HumpSQLResultMap">
		select
			p.id,
			p.serial_number,
			p.name,
			p.contract,
			p.contract_user_id,
			p.contract_status,
			p.contract_time,
			p.contract_advice,
			t.name type_name,
			u.name user_name
		from
			biz_plan_detail p
		inner join biz_plan pl on  pl.id = p.plan_id
		left join inf_type t on t.id = pl.type_id
		left join sys_user u on u.id = p.contract_user_id
		<where>
			<if test="hostOrgId != null and  hostOrgId != ''">
				and pl.host_org_id = #{hostOrgId}
			</if>
			<if test="userId != null and userId != ''">
				and pl.user_id = #{userId}
			</if>
			<if test="planId != null and planId != ''">
				and pl.id = #{planId}
			</if>
			<if test="year != null and year != ''">
				and pl.years = #{year}
			</if>
			<if test="contractStatus != null and contractStatus != ''">
				and NVL(p.contract_status,'WAIT') = #{contractStatus}
			</if>
			<if test='flag != null and flag == "0"'>
				and p.contract is not null
			</if>
			<if test="keyword != null and keyword != ''">
				and (p.name like '%' || #{keyword} || '%')
			</if>
		</where>
		order by pl.create_time desc
	</select>
	
	<select id="getMaxSerialNumber" resultType="String">
		select
			#{prefix} || max(TO_NUMBER(REPLACE(pd.serial_number, #{prefix}, '')))
		from
			biz_plan_detail pd
		inner join biz_plan p on p.id = pd.plan_id
		where 
			pd.serial_number like #{prefix}||'%' and p.host_org_id = #{hostOrgId}
	</select>
	
	<select id="getXmApplyByProfession" resultType="String">
		select
			d.id
		from 
			biz_plan p
		inner join biz_plan_detail d on d.plan_id = p.id
		inner join biz_xm_2_skill x2s on x2s.xm_id = d.id
		where
			p.id = #{planId} and x2s.profession_id = #{professionId} and x2s.tech_level = #{techLevel}
	</select>

	<select id="list" resultType="HumpSQLResultMap">
		select
			pd.id,
			pd.serial_number,
			pd.name batch_name,
			to_char(pd.start_time, 'yyyy-mm-dd') start_time,
			to_char(pd.end_time, 'yyyy-mm-dd') end_time,
			pd.submit_time,
			pd.status,
			count(bm.student_id) enroll_num
		from
			biz_plan_detail pd
		inner join biz_xm xm on xm.id = pd.id
		left join biz_bm bm on bm.xm_id = xm.id and bm.status = 'BMCG'
		<where>
			pd.status != 'DRAFT' and (xm.status != 'APPLY' and xm.status != 'ARCHIVE')
			<if test="year != null and year != ''">
				and xm.years = #{year}
			</if>
			<if test="status != null">
				and pd.status = #{status}
			</if>
			<if test="keyword != null and keyword != ''">
				and (pd.name like '%' || #{keyword} || '%' or  pd.serial_number like '%' || #{keyword} || '%')
			</if>
			<if test="hostOrgId != null and hostOrgId != ''">
				and xm.host_org_id = #{hostOrgId}
			</if>
			<if test="userId != null and userId != ''">
				and xm.leader_id = #{userId}
			</if>
		</where>
		group by pd.id,pd.serial_number,pd.name,pd.start_time,pd.end_time,pd.submit_time,pd.status
    </select>

	<select id="getYears" resultType="java.lang.String">
		select distinct years from biz_xm where years is not null order by years desc
	</select>
</mapper>
