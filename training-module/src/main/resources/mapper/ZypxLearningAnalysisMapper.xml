<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxLearningAnalysisMapper">

	<select id="pageQueryRecord" resultType="HumpSQLResultMap">
		select
			bm.xm_id,
			xm.title,
			xm.serial_number,
			f.name student_name,
			f.sfzh,
			f.mobile,
			s.company,
			bm.student_id,
			c.id as course_id,
			c.name course_name,
			nvl(a.progress, 0) kj_progress,
			a.update_time kj_latest_study_time,
			nvl(b.progress,0) zb_progress,
			b.update_time zb_latest_study_time,
			nvl(t.count, 0) as ms_count,
			t.time as ms_latest_arrive_time,
			kj.id kj_id,
			cl.id course_live_id,
			xcs.is_live,
			xcs.is_courseware,
			xcs.is_ms,
			t.count,
			it.name type_name,
			o.name receive_org_name
		from
			biz_bm bm
		inner join biz_xm xm on xm.id = bm.xm_id
		inner join inf_type it on it.id = xm.type_id and it.category = 'XM'
		inner join sys_student s on s.id = bm.student_id
		inner join sys_student_info f on f.student_id = bm.student_id
		inner join biz_bm_course bc on bc.bm_id = bm.id
		inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id
		inner join inf_course c on c.id = xcs.course_id
		left join biz_plan_detail pd on pd.id = xm.id
		left join SYS_ORG o on o.id = pd.receive_org_id
		left join biz_student_learning_score a on a.xm_id=bm.xm_id and a.student_id=bm.student_id and a.course_id=c.id and a.learning_type='KJ'
		left join view_zbxx_score b on b.xm_id = bm.xm_id and b.student_id=bm.student_id and b.course_id=c.id and b.learning_type='ZB'
		left join (select m.xm_id,m.student_id,m.course_id,count(1) count,max(m.time) as time from biz_ms_arrive m group by m.xm_id,m.student_id,m.course_id) t on t.xm_id = bm.xm_id and t.student_id = bm.student_id and t.course_id = c.id
		left join inf_kj kj on kj.course_id = c.id and kj.status = 'OK'
		left join inf_course_live cl on cl.course_id = c.id
		<where>
			<if test="xmId != null and xmId != ''">
                xm.id=#{xmId}
            </if>
            <if test="courseId != null and courseId != ''">
                and c.id=#{courseId}
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and xm.host_org_id=#{hostOrgId}
            </if>
            <if test="orgId != null and orgId != ''">
                and s.org_id=#{orgId}
            </if>
			<if test="entrustOrgId != null and entrustOrgId != ''">
				and pd.entrust_org_id=#{entrustOrgId}
			</if>
            <if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
            <if test="keyword != null and keyword != ''">
                and (f.name like '%'||#{keyword}||'%' or f.sfzh like '%'||#{keyword}||'%' or f.mobile like '%'||#{keyword}||'%')
            </if>
            <if test="headermasterId !=null and headermasterId!=''">
            	and exists (select 1 from comm_class cls where cls.id = bm.class_id and cls.headermaster_id = #{headermasterId})
		    </if>
			<if test="year!=null and year!=''">
				and xm.years=#{year}
			</if>
			<if test="typeId != null and typeId != ''">
				and xm.type_id=#{typeId}
			</if>
			<if test="receiveOrgId != null and receiveOrgId != ''">
				and pd.receive_org_id=#{receiveOrgId}
			</if>
			<if test="startTime != null and startTime != ''">
				and xm.start_time >= to_date(#{startTime}, 'yyyy-MM-dd HH24:mi:ss')
			</if>
			<if test="endtime != null and endTime != ''">
				and to_date(#{endTime}, 'yyyy-MM-dd HH24:mi:ss') >= xm.end_time
			</if>
			<if test='learningType != null and learningType=="KJ"'>
				and xcs.is_courseware = 1
			</if>
			<if test='learningType != null and learningType=="ZB"'>
				and xcs.is_live = 1
			</if>
			<if test='learningType != null and learningType=="MS"'>
				and xcs.is_ms = 1
			</if>
		</where>
		<if test='learningType != null and learningType=="KJ"'>
			<if test='sort == null or sort == ""'>
				order by a.update_time desc nulls last, bm.rowid desc
			</if>
			<if test='sort == "0"'>
				order by nvl(xcs.is_courseware,0) desc,a.progress asc nulls first, bm.rowid desc
			</if>
			<if test='sort == "1"'>
				order by nvl(xcs.is_courseware,0) desc,a.progress desc nulls last, bm.rowid desc
			</if>
		</if>
		<if test='learningType != null and learningType=="ZB"'>
			<if test='sort == null or sort == ""'>
				order by b.update_time desc nulls last, bm.rowid desc
			</if>
			<if test='sort == "2"'>
				order by nvl(xcs.is_live,0) desc,b.progress asc nulls first, bm.rowid desc
			</if>
			<if test='sort == "3"'>
				order by nvl(xcs.is_live,0) desc,b.progress desc nulls last, bm.rowid desc
			</if>
		</if>
	</select>
   
    <!-- 学习进度统计按科次 by 田军-->
    <select id="pageQueryProgress" parameterType="map" resultType="HumpSQLResultMap">
		select
			a.xm_id,
			xm.serial_number,
			xm.leader_id,
			pd.entrust_org_id,
			xm.host_org_id,
			xm.years,
			xm.title,
			xcs.is_live,
			xcs.is_courseware,
			xcs.is_ms,
			a.course_id,
			c.code course_code,
			c.name course_name,
			a.count,
			it.name type_name,
			o.name receive_org_name,
			nvl(round(b.avg_progress,2),0) kj_avg_progress,
			nvl(round(b.sum_progress/a.count,2),0) kj_sum_progress,
			nvl(round(k.avg_progress,2),0) zb_avg_progress,
			nvl(round(k.sum_progress/a.count,2),0) zb_sum_progress
		from
			(select xm.id xm_id,xcs.course_id,count(*) count from biz_bm bm inner join biz_xm xm on xm.id = bm.xm_id
				inner join biz_bm_course bc on bc.bm_id = bm.id inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id
				where xcs.is_courseware = 1 or xcs.is_live = 1 group by xm.id,xcs.course_id) a
		inner join biz_xm xm on xm.id = a.xm_id
		inner join inf_type it on it.id = xm.type_id
		inner join biz_xm_course_setting xcs on xcs.xm_id = xm.id and xcs.course_id = a.course_id
		left join (select s.xm_id,s.course_id,avg(s.progress) avg_progress,sum(s.progress) sum_progress from biz_student_learning_score s where s.learning_type = 'KJ' and exists(select 1 from biz_bm bm where bm.xm_id = s.xm_id and bm.student_id = s.student_id) group by s.xm_id,s.course_id) b on a.xm_id = b.xm_id and a.course_id = b.course_id
		left join (select s.xm_id,s.course_id,avg(s.progress) avg_progress,sum(s.progress) sum_progress from biz_student_learning_score s where s.learning_type = 'ZB' and exists(select 1 from biz_bm bm where bm.xm_id = s.xm_id and bm.student_id = s.student_id) group by s.xm_id,s.course_id) k on a.xm_id = k.xm_id and a.course_id = k.course_id
		left join inf_course c on c.id = a.course_id
		left join biz_plan_detail pd on pd.id = xm.id
		left join sys_org o on o.id = pd.receive_org_id
		<where>
			 <if test="xmId != null and xmId != ''">
                xm.id=#{xmId}
            </if>
            <if test="leaderId!=null and leaderId!=''">
				and xm.leader_id = #{leaderId}
			</if>
            <if test="courseId != null and courseId != ''">
                and a.course_id=#{courseId}
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and xm.host_org_id=#{hostOrgId}
            </if>
			<if test="entrustOrgId != null and entrustOrgId != ''">
				and pd.entrust_org_id=#{entrustOrgId}
			</if>
			<if test="year!=null and year!=''">
				and xm.years=#{year}
			</if>
			<if test="typeId != null and typeId != ''">
				and xm.type_id=#{typeId}
			</if>
			<if test="receiveOrgId != null and receiveOrgId != ''">
				and pd.receive_org_id=#{receiveOrgId}
			</if>
			<if test="startTime != null and startTime != ''">
				and xm.start_time >= to_date(#{startTime}, 'yyyy-MM-dd HH24:mi:ss')
			</if>
			<if test="endtime != null and endTime != ''">
				and to_date(#{endTime}, 'yyyy-MM-dd HH24:mi:ss') >= xm.end_time
			</if>
		</where>
    </select>
    
    <!-- 按照单位统计学习进度 by 田军-->
    <select id="pageQueryProgressDetails" parameterType="map" resultType="HumpSQLResultMap">
		select
			a.xm_id,
			xm.serial_number,
			xm.leader_id,
			pd.entrust_org_id,
			xm.host_org_id,
			xm.years,
			xm.title,
			xcs.is_live,
			xcs.is_courseware,
			xcs.is_ms,
			a.course_id,
			c.code course_code,
			c.name course_name,
			a.company,
			a.count,
			nvl(round(b.avg_progress,2),0) kj_avg_progress,
			nvl(round(b.sum_progress/a.count,2),0) kj_sum_progress,
			nvl(round(k.avg_progress,2),0) zb_avg_progress,
			nvl(round(k.sum_progress/a.count,2),0) zb_sum_progress
		from
			(select xm.id xm_id,xcs.course_id,s.company,count(*) count from biz_bm bm inner join biz_xm xm on xm.id = bm.xm_id
				inner join biz_bm_course bc on bc.bm_id = bm.id inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id
				inner join sys_student s on s.id = bm.student_id where xcs.is_courseware = 1 or xcs.is_live = 1 group by xm.id,xcs.course_id,s.company) a
		inner join biz_xm xm on xm.id = a.xm_id
		inner join biz_xm_course_setting xcs on xcs.xm_id = xm.id and xcs.course_id = a.course_id
		left join (select s.xm_id,s.course_id,std.company,avg(s.progress) avg_progress,sum(s.progress) sum_progress from biz_student_learning_score s inner join sys_student std on std.id = s.student_id where s.learning_type = 'KJ' and exists(select 1 from biz_bm bm where bm.xm_id = s.xm_id and bm.student_id = s.student_id) group by s.xm_id,s.course_id,std.company) b on a.xm_id = b.xm_id and a.course_id = b.course_id and a.company = b.company
		left join (select s.xm_id,s.course_id,std.company,avg(s.progress) avg_progress,sum(s.progress) sum_progress from biz_student_learning_score s inner join sys_student std on std.id = s.student_id where s.learning_type = 'ZB' and exists(select 1 from biz_bm bm where bm.xm_id = s.xm_id and bm.student_id = s.student_id) group by s.xm_id,s.course_id,std.company) k on a.xm_id = k.xm_id and a.course_id = k.course_id and a.company = k.company
		left join inf_course c on c.id = a.course_id
		left join biz_plan_detail pd on pd.id = xm.id
		<where>
			xm.id=#{xmId} and a.course_id=#{courseId}
			 <if test="company != null and company != ''">
                and a.company like '%'||#{company}||'%'
            </if>
        </where>
    </select>
    
</mapper>