<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxXmCourseSettingMapper">

    <select id="query" parameterType="map" resultType="com.xunw.jxjy.model.zypx.dto.XmCourseSettingItem">
		select
			xcs.id,
			c.id course_id,
			c.code course_code,
			c.name course_name,
			xcs.teacher_id,
			case
				when xcs.is_courseware = 1 then kj.teacher_name
				else nvl2(xcs.teacher_id, u.name, xcs.teacher_name)
			end as teacher_name,
			nvl(xcs.is_ms,0) is_ms,
			nvl(xcs.is_live,0) is_live,
			nvl(xcs.is_courseware,0) is_courseware,
			xcs.start_time,
			xcs.end_time,
			xcs.address,
			xcs.industry_id,
			xcs.profession_id,
			xcs.tech_level,
			xcs.is_shared,
			nvl2(p.direction, p.name||'('||p.direction||')', p.name) profession_name,
			xcs.amount,
			p.name profession_name,
			c.hours,
			kj.content kj_content,
			cl.content live_content,
			xcs.ms_content ms_content,
			xcs.is_open_course_target
		from
			biz_xm_course_setting xcs
		left join inf_course c on c.id = xcs.course_id
		left join inf_kj kj on kj.course_id = c.id and kj.status='OK'
		left join inf_course_live cl on cl.course_id = c.id
		left join sys_user u on u.id = xcs.teacher_id
		left join inf_profession p on p.id = xcs.profession_id
        <where>
            <if test="xmId != null and xmId != ''">
                xcs.xm_id=#{xmId}
            </if>
            <if test="courseId != null and courseId != ''">
                and c.id=#{courseId}
            </if>
        </where>
        order by xcs.start_time asc, xcs.end_time asc
    </select>

    <!-- 查询项目下的所有课程 -->
    <select id="getAllCourseByXmId" resultType="HumpSQLResultMap">
		select
			xcs.id,
			xcs.category,
			xcs.teacher_id,
			case
				when xcs.is_courseware = 1 then kj.teacher_name
				else nvl2(xcs.teacher_id,u.name,xcs.teacher_name)
			end as teacher_name,
			u.mobile teacher_phone,
			xcs.is_ms,
			xcs.is_live,
			xcs.is_courseware,
			xcs.start_time,
			xcs.end_time,
			xcs.address,
			c.id course_id,
			c.code course_code,
			c.name course_name,
			c.hours,
			xcs.industry_id,
			xcs.profession_id,
			xcs.tech_level,
			xcs.amount,
			case
				when xcs.is_courseware = 1 then kj.content
				when xcs.is_live = 1 then cl.content
				else xcs.ms_content
			end as content
		from
			biz_xm_course_setting xcs
		left join inf_course c on c.id = xcs.course_id
		left join inf_kj kj on kj.course_id = c.id and kj.status='OK'
		left join inf_course_live cl on cl.course_id = c.id
		left join sys_user u on u.id = xcs.teacher_id
		where
			xcs.xm_id = #{xmId}
		order by xcs.start_time asc, xcs.end_time asc
    </select>

    <select id="getCourseByCourseSettingId" resultType="Course">
    	select
    		c.*
    	from
    		biz_xm_course_setting xcs
    	inner join inf_course c on c.id = xcs.course_id
    	where
    		 xcs.id=#{courseSettingId}
    </select>

    <select id="getLearnContentById" parameterType="string" resultType="HumpSQLResultMap">
        select
       		xcs.ms_content,
       		cl.content live_content,
        	c.code course_code,
		    c.name course_name,
		    u.name teacher_name,
		    x.title
        from
      		biz_xm_course_setting xcs
    	left join inf_course c on c.id = xcs.course_id
    	left join inf_course_live cl on cl.course_id = c.id
    	left join sys_user u on u.id = xcs.teacher_id
    	left join biz_xm x on x.id = xcs.xm_id
        <where>
           	xcs.id=#{id}
        </where>
    </select>

    <select id="doTargetCourseList" resultType="HumpSQLResultMap">
		select
			xcs.id setting_id,
			xcs.teacher_name,
			xcs.start_time,
			xcs.end_time,
			c.name course_name,
			xcs.is_open_course_target,
			case when count(tr.id) > 0 then 1 else 0 end as is_target_finished
		from
			biz_xm_course_setting xcs
		inner join biz_xm xm on xm.id = xcs.xm_id
		inner join biz_bm bm on bm.xm_id = xcs.xm_id
		inner join inf_course c on c.id = xcs.course_id
		inner join biz_target_setting ts on ts.xm_id = xm.id
		left join biz_target_result tr on tr.setting_id = ts.id and tr.course_setting_id = xcs.id and tr.student_id = bm.student_id
		where
		  xcs.is_ms = '1' and xm.id = #{xmId}
		  and bm.student_id = #{studentId}
		  and exists(select 1 from biz_bm_course bc where bc.course_setting_id = xcs.id and bc.bm_id = bm.id)
		group by xcs.id,xcs.teacher_name,xcs.start_time,xcs.end_time,c.name,xcs.is_open_course_target
		order by xcs.start_time
    </select>

    <select id="getXmLinkmanByXmId" resultType="HumpSQLResultMap">
		select
			xl.name,
			xl.PHONE mobile,
			xl.REMARK
		from
			BIZ_XM_LINKMAN xl
		<where>
			<if test="id != null and id != ''">
				xl.xm_id = #{id}
			</if>
			<if test="keyword != null and keyword != ''">
			 	and (xl.name like '%'||#{keyword}||'%' or xl.PHONE like '%'||#{keyword}||'%')
			</if>
		</where>
	</select>
</mapper>
