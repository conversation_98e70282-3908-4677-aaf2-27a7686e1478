<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.ZypxXmBmCardNumberMapper">
    <select id="findByBmIdAndCardNumber" resultType="com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber">
		select
			*
		from
			BIZ_XM_BM_CARD_NUMBER
		where
			BM_ID = #{bmId}
			and CARD_NUMBER = #{cardNumber}
	</select>

    <select id="findByBmId" resultType="com.xunw.jxjy.model.zypx.entity.ZypxXmBmCardNumber">
		select
			*
		from
			BIZ_XM_BM_CARD_NUMBER
		where
			BM_ID = #{bmId}
	</select>
</mapper>
