<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.model.zypx.mapper.TargetSettingMapper">

    <select id="selectTargetResultByStudentId" resultType="HumpSQLResultMap">
		select
            bts.xm_id,
            bts.id setting_id,
            btt.id type_id,
            btt.name type_name,
            bt.name target_name,
            btr.star_count
        from
            biz_target_setting bts
        left join biz_target bt on bts.target_id = bt.id
        left join biz_target_type btt on btt.id = bt.type_id
        left join biz_target_result btr on btr.setting_id = bts.id
            <if test="studentId != null and studentId != ''">
                and btr.student_id = #{studentId}
            </if>
        <where>
            bts.type = '0'
            <if test="xmId != null and xmId != ''">
                and bts.xm_id = #{xmId}
            </if>
        </where>
	</select>

    <select id="list" resultType="com.xunw.jxjy.model.zypx.entity.TargetType">
        SELECT
            tt.*
        FROM
            biz_target_type tt
        left join biz_target t on t.TYPE_ID = tt.id
        LEFT JOIN BIZ_TARGET_SETTING ts ON ts.TARGET_ID = t.id
        where
            ts.XM_ID = #{xmId}
        group by tt.id,tt.code,tt.name,tt.parent_id,tt.sort,tt.is_course_target,tt.create_time,tt.creator_id
        ORDER BY tt.SORT
    </select>
</mapper>
