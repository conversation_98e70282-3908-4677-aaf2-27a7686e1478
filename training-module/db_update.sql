--创建错题收藏表 田军
create table BIZ_ERROR_QUESTION_COLLECT
(
   id                   NVARCHAR2(64)        not null,
   student_id           NVARCHAR2(64),
   paper_id             NVARCHAR2(64),
   question_id          NVARCHAR2(64),
   answer               NVARCHAR2(200),
   time                 DATE
);

comment on column BIZ_ERROR_QUESTION_COLLECT.id is
'主键';

comment on column BIZ_ERROR_QUESTION_COLLECT.student_id is
'学员用户id';

comment on column BIZ_ERROR_QUESTION_COLLECT.paper_id is
'试卷id';

comment on column BIZ_ERROR_QUESTION_COLLECT.question_id is
'试题id';

comment on column BIZ_ERROR_QUESTION_COLLECT.answer is
'学员作答答案';

comment on column BIZ_ERROR_QUESTION_COLLECT.time is
'收藏时间';

alter table BIZ_ERROR_QUESTION_COLLECT
   add constraint PK_BIZ_ERROR_QUESTION_COLLECT primary key (id);


-- 学员用户表新增字段  wnz 2021-5-20
ALTER TABLE sys_student ADD dept_id NVARCHAR2(64);
comment on column sys_student.dept_id is '部门ID';

ALTER TABLE sys_student ADD child_dept_id NVARCHAR2(64);
comment on column sys_student.child_dept_id is '子部门ID';


-- 课件笔记表 wnz 2021-5-25
create table BIZ_KJ_NOTE
(
   id                 NVARCHAR2(64)        not null,
   kj_id              NVARCHAR2(64),
   chapter_id         NVARCHAR2(64),
   lesson_id          NVARCHAR2(64),
   content            NCLOB,
   creator_id         NVARCHAR2(64),
   create_time        DATE
);

comment on column BIZ_KJ_NOTE.id is
'主键id';

comment on column BIZ_KJ_NOTE.kj_id is
'课件id';

comment on column BIZ_KJ_NOTE.chapter_id is
'章节id';

comment on column BIZ_KJ_NOTE.lesson_id is
'课时id';

comment on column BIZ_KJ_NOTE.content is
'笔记内容';

comment on column BIZ_KJ_NOTE.creator_id is
'创建用户id';

comment on column BIZ_KJ_NOTE.create_time is
'创建时间';

alter table BIZ_KJ_NOTE
   add constraint PK_BIZ_KJ_NOTE primary key (id);

alter table BIZ_KJ_NOTE
   add constraint FK_INF_KJ_N_REFERENCE_INF_KJ foreign key (kj_id)
      references INF_KJ (id);

--修改短信记录表中的手机号为大字段
alter table comm_sms_log rename column mobile to mobilea;
alter table comm_sms_log add mobile NCLOB;
comment on column comm_sms_log.mobile is '手机号';
update comm_sms_log s set s.mobile = mobilea;
alter table comm_sms_log drop column mobilea;


--修改主办单位ID相关的字段名称 田军 2021-06-04
alter table comm_class rename column org_id to host_org_id;
alter table comm_approve rename column org_id to host_org_id;
alter table inf_hotel rename column org_id to host_org_id;
alter table inf_venue rename column org_id to host_org_id;
alter table sys_app_version rename column org_id to host_org_id;
alter table biz_plan rename column org_id to host_org_id;
alter table biz_needs rename column org_id to host_org_id;
alter table biz_fee_tpl rename column org_id to host_org_id;

alter table inf_venue rename column address_link to map_position;
comment on column inf_venue.map_position is '地图经纬度坐标';
alter table inf_hotel rename column address_link to map_position;
comment on column inf_hotel.map_position is '地图经纬度坐标';

-- 学员后勤安排信息表 增加字段签到表url 李习平 2021-6-8
alter table biz_rear add arrive_url nvarchar2(128);
comment on column biz_rear.arrive_url is '签到表url';

---以上内容已经更新到线上 2021-06-08 田军发布到线上----

-- 学员后勤安排信息表 增加餐票信息表 李习平 2021-6-8
create table BIZ_MEAL_TICKET
(
   id                       NVARCHAR2(64)        not null,
   student_id               NVARCHAR2(64),
   xm_id                    NVARCHAR2(64),
   restaurant_name          NVARCHAR2(64),
   breakfast_ticket_num     NUMBER(3),
   breakfast_ticket_price   NUMBER(10,2),
   dinner_ticket_num        NUMBER(3),
   dinner_ticket_price      NUMBER(10,2),
   remark                   NVARCHAR2(255)
);
comment on column biz_meal_ticket.id is '主键id';
comment on column biz_meal_ticket.student_id is '学生id';
comment on column biz_meal_ticket.xm_id is '项目id';
comment on column biz_meal_ticket.restaurant_name is '餐厅名称';
comment on column biz_meal_ticket.breakfast_ticket_num is '早餐券张数';
comment on column biz_meal_ticket.breakfast_ticket_price is '早餐价格';
comment on column biz_meal_ticket.dinner_ticket_num is '晚餐券张数';
comment on column biz_meal_ticket.dinner_ticket_price is '晚餐价格';
comment on column biz_meal_ticket.remark is '说明';

--学员后勤安排信息表 增加签到信息表 李习平 2021-6-10
create table biz_xm_arrive
(
   id                 nvarchar2(64),
   xm_id              nvarchar2(64),
   student_id         nvarchar2(64),
   arrive_time        date,
   arrive_way         nvarchar2(64),
   poiaddress         nvarchar2(64),
   poiname            nvarchar2(64),
   poilatlng          nvarchar2(64),
   sign_img_url       nvarchar2(1000)
);
comment on column biz_xm_arrive.id is '主键id';
comment on column biz_xm_arrive.xm_id is '培训项目id';
comment on column biz_xm_arrive.student_id is '学员用户id';
comment on column biz_xm_arrive.arrive_time is '报到时间';
comment on column biz_xm_arrive.arrive_way is '枚举  ：线上扫码、线下导入';
comment on column BIZ_XM_ARRIVE.poiaddress is '高德地图-报到地点 ';
comment on column BIZ_XM_ARRIVE.poiname is '高德地图-报到地标';
comment on column BIZ_XM_ARRIVE.poilatlng is'签到GPS坐标';
comment on column biz_xm_arrive.sign_img_url is '手写签名图片url';

--删除后勤表签到信息 只存储住宿信息
alter table biz_rear drop (arrive_time, arrive_way, arrive_url);

--问卷调查表 增加是否是培训项目的问卷 1 是0 否' 李习平 2021-6-17
alter table biz_questionnaire add (is_for_xm nvarchar2(1));
comment on column biz_questionnaire.is_for_xm is '是否是培训项目的问卷 1  是   0 否';

--问卷调查表 增加主办单位ID 1 是0 否' 田军 2021-6-18
alter table biz_questionnaire add (host_org_id nvarchar2(64));
comment on column biz_questionnaire.host_org_id is '主办单位ID';

--修改问卷作答详情表的答案字段类型
ALTER TABLE biz_qa_answer_detail ADD a NVARCHAR2(2000);
update biz_qa_answer_detail set a = answer;
alter TABLE biz_qa_answer_detail drop column answer;
alter TABLE biz_qa_answer_detail rename column a to answer;
comment on column biz_qa_answer_detail.answer is '小题答案';

--增加用户登录记录表
CREATE TABLE sys_student_login_log (
  id NVARCHAR2(64) NOT NULL,
  student_id NVARCHAR2(64),
  last_login_time DATE,
  client NVARCHAR2(64),
  PRIMARY KEY (id)
);
COMMENT ON COLUMN sys_student_login_log.id IS '主键id';
COMMENT ON COLUMN sys_student_login_log.student_id IS '学员id';
COMMENT ON COLUMN sys_student_login_log.last_login_time IS '最后登录时间';
COMMENT ON COLUMN sys_student_login_log.client IS '客户端';

--增加项目资料上传表
CREATE TABLE biz_xm_material (
  id NVARCHAR2(64) NOT NULL,
  xm_id NVARCHAR2(64),
  material_type NVARCHAR2(64),
  material_name NVARCHAR2(64),
  material_size NVARCHAR2(64),
  is_open_for_student NVARCHAR2(1),
  material_url NVARCHAR2(255),
  uploader NVARCHAR2(64),
  upload_time DATE,
  PRIMARY KEY (id)
);
COMMENT ON COLUMN biz_xm_material.id IS '主键id';
COMMENT ON COLUMN biz_xm_material.xm_id IS '项目id';
COMMENT ON COLUMN biz_xm_material.material_type IS '资料类型 文档 视频';
COMMENT ON COLUMN biz_xm_material.material_name IS '资料名称';
COMMENT ON COLUMN biz_xm_material.material_size IS '资料大小';
COMMENT ON COLUMN biz_xm_material.is_open_for_student IS '资料状态 1启用 0停用';
COMMENT ON COLUMN biz_xm_material.material_url IS '资料url';
COMMENT ON COLUMN biz_xm_material.uploader IS '上传人';
COMMENT ON COLUMN biz_xm_material.upload_time IS '上传时间';

--修改字段名称  田军 2020-06-23
alter table biz_xm_material rename column is_open_for_student to material_status;
COMMENT ON COLUMN biz_xm_material.material_status IS '状态';

--增加数据字典表
CREATE TABLE sys_dict (
  id NVARCHAR2(64) NOT NULL,
  dict_code NVARCHAR2(100),
  dict_name NVARCHAR2(200),
  dict_value NVARCHAR2(200),
  dict_desc NVARCHAR2(100),
  dict_status NVARCHAR2(20),
  dict_sort number,
  is_default NVARCHAR2(1),
  creator_id NVARCHAR2(64),
  create_time DATE,
  updator_id NVARCHAR2(64),
  update_time DATE,
  PRIMARY KEY (id)
);
COMMENT ON COLUMN sys_dict.id IS '主键id';
COMMENT ON COLUMN sys_dict.dict_code IS '字典编码';
COMMENT ON COLUMN sys_dict.dict_name IS '字典名称';
COMMENT ON COLUMN sys_dict.dict_value IS '字典值';
COMMENT ON COLUMN sys_dict.dict_desc IS '字典描述';
COMMENT ON COLUMN sys_dict.dict_status IS '字典状态 OK启用 BLOCK停用';
COMMENT ON COLUMN sys_dict.dict_sort IS '排序';
COMMENT ON COLUMN sys_dict.is_default IS '是否默认 1默认 0否';
COMMENT ON COLUMN sys_dict.creator_id IS '创建人id';
COMMENT ON COLUMN sys_dict.create_time IS '创建时间';
COMMENT ON COLUMN sys_dict.updator_id IS '更新人id';
COMMENT ON COLUMN sys_dict.update_time IS '更新时间';

--删除BIZ_KJ_NOTE外键引用
ALTER TABLE BIZ_KJ_NOTE DROP CONSTRAINT FK_INF_KJ_N_REFERENCE_INF_KJ;

--试卷作答历史记录表  田军 2021-06-26
create table BIZ_EXAMDATA_HISTORY
(
  id              NVARCHAR2(64) not null,
  paper_id        NVARCHAR2(64),
  student_id      NVARCHAR2(64),
  start_time      DATE,
  end_time        DATE,
  ip              NVARCHAR2(50),
  score           INTEGER,
  status          NVARCHAR2(50),
  data            NCLOB,
  score_detail    NCLOB,
  client          NVARCHAR2(64),
  mark_teacher_id NVARCHAR2(64),
  mark_time       DATE,
  effect_time     DATE,
  effect_user_id  NVARCHAR2(64),
  save_time       DATE
);
-- Add comments to the table
comment on table BIZ_EXAMDATA_HISTORY
  is '用于存放练习（作业）作答结果的历史记录';
-- Add comments to the columns
comment on column BIZ_EXAMDATA_HISTORY.id
  is '主键id';
comment on column BIZ_EXAMDATA_HISTORY.paper_id
  is '试卷id';
comment on column BIZ_EXAMDATA_HISTORY.student_id
  is '考生用户id';
comment on column BIZ_EXAMDATA_HISTORY.start_time
  is '开始时间';
comment on column BIZ_EXAMDATA_HISTORY.end_time
  is '结束时间';
comment on column BIZ_EXAMDATA_HISTORY.ip
  is 'ip地址';
comment on column BIZ_EXAMDATA_HISTORY.score
  is '分数';
comment on column BIZ_EXAMDATA_HISTORY.status
  is '枚举:0未交卷，1已交卷待批改，2已自动批改，3已手动批改 -1批改失败';
comment on column BIZ_EXAMDATA_HISTORY.data
  is '答卷内容';
comment on column BIZ_EXAMDATA_HISTORY.score_detail
  is '交卷客户端';
comment on column BIZ_EXAMDATA_HISTORY.client
  is '交卷客户端';
comment on column BIZ_EXAMDATA_HISTORY.mark_teacher_id
  is '评卷老师id';
comment on column BIZ_EXAMDATA_HISTORY.mark_time
  is '评卷时间';
comment on column BIZ_EXAMDATA_HISTORY.effect_time
  is '生效时间';
comment on column BIZ_EXAMDATA_HISTORY.effect_user_id
  is '生效操作用户id';
comment on column BIZ_EXAMDATA_HISTORY.save_time
  is '试卷最后一次答题保存时间';
alter table BIZ_EXAMDATA_HISTORY
  add constraint PK_BIZ_EXAMDATA_HISTORY primary key (ID);

--试卷最高得分记录表 田军 2021-06-26
create table BIZ_EXAM_PAPER_MAX_SCORE
(
  id          NVARCHAR2(64) not null,
  paper_id    NVARCHAR2(64),
  student_id  NVARCHAR2(64),
  max_score   INTEGER,
  examdata_id NVARCHAR2(64),
  time        DATE
);
comment on column BIZ_EXAM_PAPER_MAX_SCORE.id
  is '主键';
comment on column BIZ_EXAM_PAPER_MAX_SCORE.paper_id
  is '试卷id';
comment on column BIZ_EXAM_PAPER_MAX_SCORE.student_id
  is '考生用户id';
comment on column BIZ_EXAM_PAPER_MAX_SCORE.max_score
  is '最高分';
comment on column BIZ_EXAM_PAPER_MAX_SCORE.examdata_id
  is '最高分对应的作答记录ID';
comment on column BIZ_EXAM_PAPER_MAX_SCORE.time
  is '创建时间';
alter table BIZ_EXAM_PAPER_MAX_SCORE
  add constraint PK_BIZ_EXAM_PAPER_MAX_SCORE primary key (ID);

---使用学员的当前作业成绩初始化学员试卷最高得分表， 田军 2021-06-26
--初始化学员试卷最高得分表
insert into biz_exam_paper_max_score
select sys_guid() as id, d.paper_id,d.student_id,d.score as max_score,d.id as examdata_id,sysdate time from biz_examdata d
inner join biz_exam_paper p on p.id = d.paper_id
where p.category='PSZY' and d.score >= 0;

--删除学员用户表中的部门ID，子部门ID 田军
alter table sys_student drop column dept_id;
alter table sys_student drop column child_dept_id;

--学员用户表增加学员类型 田军
alter table sys_student add student_type NVARCHAR2(100);
comment on column sys_student.student_type is '学员类型枚举，在校学生、社会人士';
--学员用户表增加字段培训机构名称
alter table sys_student add other_org_name NVARCHAR2(200);
comment on column sys_student.other_org_name is '学员自己输入的培训机构名称，此机构在系统内不存在，即org_id为null';
--学员用户表增加字段注册主办单位ID
alter table sys_student add reg_host_org_id NVARCHAR2(64);
comment on column sys_student.reg_host_org_id is '注册主办单位id,即通过哪一个主办单位注册的';


--新建职业培训-培训项目合作宾馆关系表， 李习平 2021-06-29
create table biz_xm_2_hotel
(
   id                 NVARCHAR2(64),
   xm_id              NVARCHAR2(64),
   hotel_id           NVARCHAR2(64),
   start_time         DATE,
   end_time           DATE,
   create_time        DATE,
   creator_id         NVARCHAR2(64)
);

comment on table biz_xm_2_hotel is '存储培训项目与合作宾馆的关联关系';
comment on column biz_xm_2_hotel.id is '主键';
comment on column biz_xm_2_hotel.xm_id is '项目id';
comment on column biz_xm_2_hotel.hotel_id is '合作宾馆id';
comment on column biz_xm_2_hotel.start_time is '预定开始时间';
comment on column biz_xm_2_hotel.end_time is '预定结束时间';
comment on column biz_xm_2_hotel.create_time is '创建时间';
comment on column biz_xm_2_hotel.creator_id is '创建用户id';

--住宿表增加字段 李习平 2021-6-29
alter table biz_rear add (amount number);
comment on column biz_rear.amount is '住宿费用';
alter table biz_rear add (is_payed nvarchar2(64));
comment on column biz_rear.is_Payed is '住宿费是否缴纳 1是 0 否';
alter table biz_rear add (rear_type nvarchar2(64));
comment on column biz_rear.rear_type is '预定方式,枚举：线上、线下';

--住宿表增加字段 李习平 2021-6-29
alter table inf_hotel add (img_url nvarchar2(255));
comment on column inf_hotel.img_url is '图片链接';
alter table inf_hotel add (room_types NCLOB);
comment on column inf_hotel.room_types is '房间类型';

--删除宾馆表 单间数和双人间数 李习平 2021-6-29
alter table inf_hotel drop (single_room_count, double_room_count);

--高中（中专）及以下 李习平 2021-7-1
update sys_student_info set education = 'GAOZHONG'
where education in ('XIAOXUE','CHUZHONG','GAOZHONG','JIXIAO','GAOJI','ZHIGAO','ZHONGZHUNAN');
--专科 李习平 2021-7-1
update sys_student_info set education = 'DAZHUNAN'
where education in ('DAZHUNAN','DAXUE');
--本科 李习平 2021-7-1
update sys_student_info set education = 'BENKE'
where education in ('BENKE');
--研究生及以上 李习平 2021-7-1
update sys_student_info set education = 'YANJIUSHENG'
where education in ('BOSHI','SHUOSHI');

--通知公告分类数据处理 田军 2021-07-03
delete from sys_notice_category y where y.parent_id is null;
update sys_notice_category set parent_id = null;

--项目表增加字段 李习平 2021-7-5
alter table biz_xm add (poiaddress nvarchar2(64));
comment on column biz_xm.poiaddress is '报到地点';
alter table biz_xm add (poilatlng nvarchar2(64));
comment on column biz_xm.poilatlng is '报到gps坐标';
alter table biz_xm add (arrive_range nvarchar2(64));
comment on column biz_xm.arrive_range is '报到坐标范围';

--存储平台的文档  田军 2021-07-06
create table SYS_DOCUMENT
(
   id                   NVARCHAR2(64)        not null,
   name                 NVARCHAR2(200),
   file_size            INTEGER,
   url                  NVARCHAR2(200),
   status               NVARCHAR2(64),
   create_time          DATE,
   creator_id           NVARCHAR2(64)
);

comment on table SYS_DOCUMENT is
'存储平台的文档，操作手册等等一些内容';

comment on column SYS_DOCUMENT.id is
'主键id';

comment on column SYS_DOCUMENT.name is
'文档名称';

comment on column SYS_DOCUMENT.file_size is
'文档大小,单位字节';

comment on column SYS_DOCUMENT.url is
'文档下载地址';

comment on column SYS_DOCUMENT.status is
'状态枚举，OK ，BLOCK';

comment on column SYS_DOCUMENT.create_time is
'创建时间';

comment on column SYS_DOCUMENT.creator_id is
'创建用户id';

alter table SYS_DOCUMENT
   add constraint PK_SYS_DOCUMENT primary key (id);

--机构表增加主办单位门户是否是定制化门户
alter table sys_org add (is_portal_customized nvarchar2(1));
comment on column sys_org.is_portal_customized is '主办单位门户是否是定制化门户';

--删除职业鉴定报名表中的1.0遗留的无用字段 tianjun  2021-07-07
alter table biz_zyjd_bm drop column bmd_id;
alter table biz_zyjd_bm drop column zbmd_id;
alter table biz_zyjd_bm drop column class_id;

--职业培训报名表中增加缴费相关字段   tianjun 2021-07-07
alter table biz_bm add is_payed nvarchar2(1);
comment on column biz_bm.is_payed is '是否已缴费 1 已缴  0 未缴';

alter table biz_bm add pay_type nvarchar2(1);
comment on column biz_bm.pay_type is '缴费方式 1 线上 0 线下';

alter table biz_bm add mark_payed_user_id nvarchar2(64);
comment on column biz_bm.mark_payed_user_id is '标记缴费用户id';

alter table biz_bm add mark_payed_reason nvarchar2(200);
comment on column biz_bm.mark_payed_reason is '标记缴费备注';

alter table biz_bm add payed_amount NUMBER;
comment on column biz_bm.payed_amount is '缴费金额';

alter table biz_xm add limit_count INTEGER;
comment on column biz_xm.limit_count is '报名学员数量限制';

--将职业鉴定同步过去的报名数据中待确认的数据都删除，删除之前必须先备份
--备份
create table biz_bm_0707 as select * from biz_bm;
--删除
delete from biz_bm bm where bm.xm_id in (
select s.xm_id  from biz_zyjd_xmscope s
) and bm.status = 'DQR';

--直播笔记表名 字段 李习平 2021年7月9日
create table biz_live_note
(
    id            nvarchar2(64)   not null,
    live_id       nvarchar2(64),
    text          nvarchar2(200),
    time_point    nvarchar2(64),
    create_time   date,
    user_id       nvarchar2(64)
);
comment on column biz_live_note.id is '主键';
comment on column biz_live_note.live_id is '直播id';
comment on column biz_live_note.text is '笔记内容';
comment on column biz_live_note.time_point is '时间节点';
comment on column biz_live_note.create_time is '创建时间';
comment on column biz_live_note.user_id is '用户id';

--课件评价表
create table biz_kj_target_result
(
   id                 nvarchar2(64),
   kj_id              nvarchar2(64),
   star_count         integer,
   student_id         nvarchar2(64),
   time               date
);
comment on table biz_kj_target_result is '存储学员用户对课件的评价';
comment on column biz_kj_target_result.id is '主键id';
comment on column biz_kj_target_result.kj_id is '课件id';
comment on column biz_kj_target_result.star_count is '几颗星';
comment on column biz_kj_target_result.student_id is '评价学员用户id';
comment on column biz_kj_target_result.time is '评价时间';

--直播讲师评价表
create table biz_teacher_target_result
(
   id                 nvarchar2(64),
   teacher_id         nvarchar2(64),
   star_count         integer,
   student_id         nvarchar2(64),
   time               date
);
comment on table biz_teacher_target_result is '存储直播讲师的评价信息';
comment on column biz_teacher_target_result.id is '主键id';
comment on column biz_teacher_target_result.teacher_id is '讲师用户id';
comment on column biz_teacher_target_result.star_count is '几颗星';
comment on column biz_teacher_target_result.student_id is '评价学员用户id';
comment on column biz_teacher_target_result.time is '评价时间';

--创建报名历史记录表
create table biz_bm_history
(
  id                 NVARCHAR2(64) not null,
  xm_id              NVARCHAR2(64),
  student_id         NVARCHAR2(64),
  is_jtbm            NVARCHAR2(1),
  jtbm_user_id       NVARCHAR2(64),
  jtbm_org_id        NVARCHAR2(64),
  status             NVARCHAR2(50),
  time               DATE,
  update_time        DATE,
  updator_id         NVARCHAR2(64),
  class_id           NVARCHAR2(64),
  approve_result     NVARCHAR2(200),
  approve_reason     NVARCHAR2(200),
  approve_user_id    NVARCHAR2(200),
  is_payed           NVARCHAR2(1),
  pay_type           NVARCHAR2(1),
  mark_payed_user_id NVARCHAR2(64),
  mark_payed_reason  NVARCHAR2(200),
  payed_amount       NUMBER
);
-- Add comments to the table
comment on table biz_bm_history
  is '本表记录职业培训的报名信息';
-- Add comments to the columns
comment on column biz_bm_history.xm_id
  is '项目ID';
comment on column biz_bm_history.student_id
  is '考生id';
comment on column biz_bm_history.is_jtbm
  is '是否集体报名  1是  0否';
comment on column biz_bm_history.jtbm_user_id
  is '集体报名用户id';
comment on column biz_bm_history.jtbm_org_id
  is '集体报名机构id';
comment on column biz_bm_history.status
  is '报名状态 枚举：待确认、报名成功   ';
comment on column biz_bm_history.time
  is '报名时间';
comment on column biz_bm_history.update_time
  is '修改时间';
comment on column biz_bm_history.updator_id
  is '修改用户id';
comment on column biz_bm_history.class_id
  is '班级ID';
comment on column biz_bm_history.approve_result
  is '证书审核结果';
comment on column biz_bm_history.approve_reason
  is '证书审核意见';
comment on column biz_bm_history.approve_user_id
  is '审核操作用户ID';
comment on column biz_bm_history.is_payed
  is '是否已缴费 1 已缴  0 未缴';
comment on column biz_bm_history.pay_type
  is '缴费方式 1 线上 0 线下';
comment on column biz_bm_history.mark_payed_user_id
  is '标记缴费用户id';
comment on column biz_bm_history.mark_payed_reason
  is '标记缴费备注';
comment on column biz_bm_history.payed_amount
  is '缴费金额';
alter table biz_bm_history
  add constraint pk_biz_bm_history primary key (ID);

--创建报名课程历史记录表
create table biz_bm_course_history
(
  id                NVARCHAR2(64) not null,
  bm_id             NVARCHAR2(64),
  course_setting_id NVARCHAR2(64),
  bm_time           DATE
);
-- Add comments to the table
comment on table biz_bm_course_history
  is '学员报名课程表 ，此表用于存储学员的报名课程信息。';
-- Add comments to the columns
comment on column biz_bm_course_history.bm_id
  is '报名表id';
comment on column biz_bm_course_history.course_setting_id
  is '课程设置id';
comment on column biz_bm_course_history.bm_time
  is '报名时间';
alter table biz_bm_course_history
  add constraint pk_biz_bm_course_history primary key (ID);

--设置系统现有学员数据的reg_host_org_id，此处以国土为例
update sys_student set reg_host_org_id='hbgtzyzyxy';

--发票字段 李习平 2021年7月9日 已发布国土生产
alter table sys_student_info add invoice_title nvarchar2(100);
comment on column sys_student_info.invoice_title is '发票抬头';
alter table sys_student_info add invoice_code nvarchar2(100);
comment on column sys_student_info.invoice_code is '发票代码';
--培训机构ID字段 李习平 2021年7月22日 已发布国土生产
alter table biz_plan add org_id nvarchar2(64);
comment on column biz_plan.org_id is '培训机构ID';

--修改证书模板表 田军   2021-07-26
--删除证书模板内容字段
alter table biz_certi_tpl drop column content;
alter table biz_certi_tpl rename column core_path to template_path;
comment on column biz_certi_tpl.template_path is '证书模板URL地址';
alter table biz_certi_tpl add host_org_id nvarchar2(64);
comment on column biz_certi_tpl.host_org_id is '主办单位ID';
--注意此处需要手动设置host_org_id的取值
--添加字段证芯日期
alter table biz_xm add certi_date DATE;
comment on column biz_xm.certi_date is '证芯日期';
--结业证书项目名称
alter table biz_xm add certi_xm_name nvarchar2(500);
comment on column biz_xm.certi_xm_name is '结业证书项目名称';

--删除培训项目表中的无用的字段
alter table biz_xm drop column XXCJZB;
alter table biz_xm drop column PSZYCJZB;
alter table biz_xm drop column ZHCYCJZB;
alter table biz_xm drop column SXXXCJZB;
alter table biz_xm drop column SXPSZYCJZB;
alter table biz_xm drop column SXZHCYCJZB;

--biz_xm表增加字段 是否允许先学习后缴费，是否允许先考试后缴费
alter table biz_xm add is_alllow_study_before_pay nvarchar2(1);
comment on column biz_xm.is_alllow_study_before_pay is '是否允许先学习后缴费';
alter table biz_xm add is_alllow_exam_before_pay nvarchar2(1);
comment on column biz_xm.is_alllow_exam_before_pay is '是否允许先考试后缴费';
update biz_xm set is_alllow_study_before_pay = 1;
update biz_xm set is_alllow_exam_before_pay = 1;


--创建机构org code sequence 田军
create sequence SEQ_ORG_CODE
minvalue 1  --增长最小值
maxvalue 9999999999999999  --增长最大值,也可以设置NOMAXvalue -- 不设置最大值
start with 1  --从101开始计数
increment by 1  --自增步长为1
NOCACHE;


----更改考试验证码的字段长度
alter table biz_exam_paper modify validate_code nvarchar2(40);

--只有理工大资讯分类表固定5个默认类型
INSERT INTO SYS_NOTICE_CATEGORY VALUES ('BJFC', '班级风采', '5');
INSERT INTO SYS_NOTICE_CATEGORY VALUES ('XMJS', '项目介绍', '3');
INSERT INTO SYS_NOTICE_CATEGORY VALUES ('ZCYD', '政策研读', '4');
INSERT INTO SYS_NOTICE_CATEGORY VALUES ('ZXXW', '中心新闻', '1');
INSERT INTO SYS_NOTICE_CATEGORY VALUES ('TZGG', '通知公告', '2');

--创建直播评论表 刘仕贤  2021/8/25
CREATE TABLE BIZ_LIVE_COMMENT
 (
      ID                NVARCHAR2(64)      NOT NULL,
      LIVE_ID           NVARCHAR2(64),
      CONTENT           NVARCHAR2(2000),
      CREATE_TIME       DATE,
      CREATOR_ID        NVARCHAR2(64)
);
COMMENT ON COLUMN  BIZ_LIVE_COMMENT.ID IS '主键';
COMMENT ON COLUMN  BIZ_LIVE_COMMENT.LIVE_ID IS '直播id';
COMMENT ON COLUMN  BIZ_LIVE_COMMENT.CONTENT IS '评论类容';
COMMENT ON COLUMN  BIZ_LIVE_COMMENT.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN  BIZ_LIVE_COMMENT.CREATOR_ID IS '用户id';

ALTER TABLE BIZ_LIVE_COMMENT ADD CONSTRAINT PK_BIZ_LIVE_COMMENT PRIMARY KEY (ID);



--修改直播笔记表的字段名称  刘仕贤 2021/8/25
alter table BIZ_LIVE_NOTE rename column USER_ID to  CREATOR_ID;
alter table BIZ_LIVE_NOTE rename column TEXT to  CONTENT;

--修改课件评论表的字段名称  刘仕贤 2021年8月25日10:02:43
alter table INF_KJPL rename column zj_id to  chapter_id;
alter table INF_KJPL rename column keshi_id to  lesson_id;
alter table INF_KJPL rename column nr to  content;
alter table INF_KJPL rename column cjyh_id to  creator_id;
alter table INF_KJPL rename column cjsj to  create_time;

--咨询分类表增加主办单位Id字段  数据需要处理
alter table sys_notice_category add host_org_id NVARCHAR2(64);
comment on column sys_notice_category.host_org_id is '主办单位id';

--课程表增加主办单位id字段 数据需要处理
alter table inf_course add host_org_id NVARCHAR2(64);
comment on column inf_course.host_org_id is '主办单位id';

--机构表增加 管理端域名字段
alter table sys_org add admin_domain NVARCHAR2(1000);
comment on column sys_org.admin_domain is '主办单位管理端域名';
comment on column sys_org.portal_domain is '主办单位门户域名';

--职业鉴定费用设置表增加主办单位ID字段，国土老数据需要手动设置这个字段
alter table inf_fee_setting  add host_org_id NVARCHAR2(64);
comment on column inf_fee_setting.host_org_id is '主办单位id';

--修改订单表的原有的学校ID 为主办单位ID
alter table comm_order rename column school_id to host_org_id;
comment on column comm_order.host_org_id is '主办单位id';
--手动设置订单的主办单位，此处以生态为例
update comm_order r set r.host_org_id = 'hbstgczyjsxy';

--职业鉴定批次表增加主办单位ID，国土老数据需要手动设置这个字段
alter table biz_zyjd_bmbatch add host_org_id NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.host_org_id is '主办单位id';

--门户banner表增加主办单位ID，老数据需要手动设置
alter table sys_portal_banner add host_org_id NVARCHAR2(64);
comment on column sys_portal_banner.host_org_id is '主办单位id';

--课件表增加讲师的图片
alter table inf_kj add teacher_photo NVARCHAR2(1000);
comment on column inf_kj.teacher_photo is '讲师的照片';

--分类表增加主办单位ID
alter table inf_type add host_org_id NVARCHAR2(64);
comment on column inf_type.host_org_id is '主办单位id';

--更改课件评论表名称
ALTER TABLE inf_kjpl RENAME TO biz_kj_comment;

--课程表增加 LOGO字段
alter table inf_course add logo NVARCHAR2(2000);
comment on column inf_course.logo is '课程LOGO';

--课程表将学分更改为学时
alter table inf_course rename column score to hours;
comment on column inf_course.hours is '学时';

--培训项目表增加最低学时
alter table biz_xm add certi_hours NUMBER;
comment on column biz_xm.certi_hours is '最低学时要求';


--使用课程设置表中的学时字段更新课程表中的学时字段
--备份
create table inf_course_bak_0921 as select * from inf_course;
--更新数据库
update inf_course c set c.hours = (select max(xcs.hours) from biz_xm_course_setting xcs where xcs.course_id=c.id)
where exists (select 1 from biz_xm_course_setting xcs where xcs.course_id=c.id);
--删除项目课程设置表中的hours字段
alter table biz_xm_course_setting drop column hours;
alter table biz_xm_course_setting drop column zbcjbl;
alter table biz_xm_course_setting drop column kjxxcjbl;
alter table biz_xm_course_setting drop column mscjbl;

--课程表增加字段是否是公开课
alter table inf_course add is_public NVARCHAR2(1);
comment on column inf_course.is_public is '是否是公开课 1 是 0 否';
--课程表增加公开课授课开始时间
alter table inf_course add teach_start_time DATE;
comment on column inf_course.teach_start_time is '公开课授课开始时间';
--课程表增加公开课授课结束时间
alter table inf_course add teach_end_time DATE;
comment on column inf_course.teach_end_time is '公开课授课结束时间';
--课程表增加公开课授课形式
alter table inf_course add teach_type NVARCHAR2(1);
comment on column inf_course.teach_type is '公开课授课形式 1 直播  2课件';

--项目表增加培训项目的LOGO
alter table biz_xm add logo NVARCHAR2(1000);
comment on column biz_xm.logo is '项目图标';

--增加直播表
CREATE TABLE INF_COURSE_LIVE
 (
      id                nvarchar2(64)      not null,
      course_id         nvarchar2(64),
      introduce			nvarchar2(500),
      content           nclob,
      create_time       date,
      creator_id        nvarchar2(64),
      update_time		date,
      updator_id		nvarchar2(64)
);
COMMENT ON COLUMN  INF_COURSE_LIVE.ID IS '主键';
COMMENT ON COLUMN  INF_COURSE_LIVE.course_id IS '课程id';
COMMENT ON COLUMN  INF_COURSE_LIVE.CONTENT IS '直播类容大字段';
COMMENT ON COLUMN  INF_COURSE_LIVE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN  INF_COURSE_LIVE.CREATOR_ID IS '用户id';

ALTER TABLE INF_COURSE_LIVE ADD CONSTRAINT PK_BIZ_INF_COURSE_LIVE PRIMARY KEY (ID);

--直播表增加讲师ID集合
alter table inf_course_live add teacher_ids NVARCHAR2(1000);
comment on column inf_course_live.teacher_ids is '直播中所有讲师ID集合，用逗号拼接';

--修改课程设置表，删除直播章节大字段,删除之前需要备份课程设置表
create table biz_xm_course_setting_live0921 as select * from biz_xm_course_setting;

--!!!向直播表中插入以前的直播数据
insert into inf_course_live(id,introduce,course_id,content,teacher_ids,creator_id,create_time,updator_id,update_time)
select sys_guid() as id ,null,tbl.course_id, tbl.live_content as content,tbl.teacher_ids, null as creator_id,tbl.create_time,null as updator_id,null as update_time from(
select xcs.course_id,xcs.live_content, xcs.teacher_ids,xcs.create_time,row_number() over (partition by xcs.course_id order by xcs.create_time desc) as rn from biz_xm_course_setting xcs
where xcs.is_live = 1 and xcs.live_content is not null order by xcs.course_id
) tbl where tbl.rn=1;

--修改课程设置表，删除直播章节大字段
alter table biz_xm_course_setting drop column live_content;
alter table biz_xm_course_setting drop column teacher_ids;

--报名表中添加是否已经申请发票字段
alter table biz_bm add is_applyed_invoice NVARCHAR2(64);
comment on column biz_bm.is_applyed_invoice is '是否已经申请发票';

--报明表中添加 发票申请时间字段
alter table biz_bm add invoice_applyed_time Date;
comment on column biz_bm.invoice_applyed_time is ' 发票申请时间';


--学生表中添加发票-开户行字段
alter table SYS_STUDENT_INFO add invoice_bank NVARCHAR2(64);
comment on column SYS_STUDENT_INFO.invoice_bank is ' 发票-开户行';

--学生表中添加发票-开户行字段
alter table SYS_STUDENT_INFO add invoice_bank_account NVARCHAR2(64);
comment on column SYS_STUDENT_INFO.invoice_bank_account is ' 发票-银行账号';

--学生表中添加发票-单位地址
alter table SYS_STUDENT_INFO add invoice_org_address NVARCHAR2(64);
comment on column SYS_STUDENT_INFO.invoice_org_address is ' 发票-单位地址';

--学生表中添加发票-单位电话
alter table SYS_STUDENT_INFO add invoice_org_phone NVARCHAR2(64);
comment on column SYS_STUDENT_INFO.invoice_org_phone is ' 发票-单位电话';

--培训项目表中增加字段 练习成绩占比
alter table biz_xm add practice_score_zb NUMBER;
comment on column biz_xm.practice_score_zb is '练习成绩占比';


--创建个人报名课程表
CREATE TABLE BIZ_STUDENT_BM_COURSE
 (
      id                nvarchar2(64)      not null,
      course_id         nvarchar2(64),
      student_id		nvarchar2(64),
      create_time       date,
      creator_id        nvarchar2(64)
);
COMMENT ON COLUMN  BIZ_STUDENT_BM_COURSE.ID IS '主键';
COMMENT ON COLUMN  BIZ_STUDENT_BM_COURSE.course_id IS '课程id';
COMMENT ON COLUMN  BIZ_STUDENT_BM_COURSE.student_id IS '学员ID';

ALTER TABLE BIZ_STUDENT_BM_COURSE ADD CONSTRAINT PK_BIZ_STUDENT_BM_COURSE PRIMARY KEY (ID);

--培训项目增加字段是否是热门项目
alter table biz_xm add is_hot NVARCHAR2(1);
comment on column biz_xm.is_hot is '是否是热门项目';

--理工大门户新增培训案例表
CREATE TABLE SYS_TRAINING_CASE
 (
        id                   nvarchar2(64) not null,
        training_img         nvarchar2(1000),
        content              nvarchar2(500),
        create_time          date,
        host_org_id          nvarchar2(64),
        update_time          date
);
COMMENT ON COLUMN SYS_TRAINING_CASE.id IS '主键';
COMMENT ON COLUMN SYS_TRAINING_CASE.training_img IS '案例图片';
COMMENT ON COLUMN SYS_TRAINING_CASE.content IS '内容描述';
COMMENT ON COLUMN SYS_TRAINING_CASE.create_time IS '创建时间';
COMMENT ON COLUMN SYS_TRAINING_CASE.host_org_id IS '主办单位id';
COMMENT ON COLUMN SYS_TRAINING_CASE.update_time IS '创建时间';

ALTER TABLE SYS_TRAINING_CASE ADD CONSTRAINT  PK_SYS_TRAINING_CASE PRIMARY KEY (ID);

--删除废弃的视图
drop view view_zypx_xxcj;

--需要手动更新项目类型表的主办单位信息，否则查询出来的数据为空，此处以修改生态的数据库为例，
--其他学校请注意修改主办单位
update inf_type t set t.host_org_id = 'hbstgczyjsxy';

--添加七大员行业
insert into inf_industry(id,code,name,status) values('qdyhyid','99','七大员行业','OK');

--添加七大员职业
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('SGY','qdyhyid','00101','施工员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('ZHLY','qdyhyid','00102','质量员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('CLY','qdyhyid','00103','材料员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('ZLY','qdyhyid','00104','资料员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('BZY','qdyhyid','00105','标准员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('JXY','qdyhyid','00106','机械员','OK');
insert into inf_profession(id,INDUSTRY_ID,code,name,status) values('LWY','qdyhyid','00107','劳务员','OK');

--为个人学习的课程插入一个项目
insert into biz_xm(id,serial_number,title)values('_personal','_personal','个人学习项目');

--删除字段
alter table biz_xm drop column face_verify_interval;

--------以上内容已经与2021-09-22同步到所有线上环境

--新增课程费用字段
alter table inf_course add amount number;
comment on column inf_course.amount is '课程费用';


--创建门户项目表
create table SYS_PORTAL_XM
(
   id                   NVARCHAR2(64)        not null,
   name                 NVARCHAR2(200),
   type_id              NVARCHAR2(64),
   logo                 NVARCHAR2(1000),
   start_time           DATE,
   end_time             DATE,
   introdcue            NVARCHAR2(1000),
   status               NVARCHAR2(20),
   content              NCLOB,
   create_time          DATE,
   creator_id           NVARCHAR2(64)
);

comment on table SYS_PORTAL_XM is
'门户项目介绍表';

comment on column SYS_PORTAL_XM.name is
'项目名称';

comment on column SYS_PORTAL_XM.type_id is
'分类id';

comment on column SYS_PORTAL_XM.logo is
'图标';

comment on column SYS_PORTAL_XM.start_time is
'培训时间';

comment on column SYS_PORTAL_XM.end_time is
'培训结束时间';

comment on column SYS_PORTAL_XM.introdcue is
'简介';

comment on column SYS_PORTAL_XM.status is
'状态 枚举';

comment on column SYS_PORTAL_XM.content is
'详细内容';

comment on column SYS_PORTAL_XM.create_time is
'创建时间';

comment on column SYS_PORTAL_XM.creator_id is
'创建用户id';

alter table SYS_PORTAL_XM add constraint PK_SYS_PORTAL_XM primary key (id);

--创建门户课件资源表
create table SYS_PORTAL_KJ
(
   id                   NVARCHAR2(64)        not null,
   type                 NVARCHAR2(200),
   kj_id                NVARCHAR2(64),
   create_time          DATE,
   creator_id           NVARCHAR2(64)
);

comment on column SYS_PORTAL_KJ.id is
'主键';

comment on column SYS_PORTAL_KJ.type is
'分类';

comment on column SYS_PORTAL_KJ.kj_id is
'课件资源表id ';

comment on column SYS_PORTAL_KJ.create_time is
'创建时间';

comment on column SYS_PORTAL_KJ.creator_id is
'创建用户id';

alter table SYS_PORTAL_KJ add constraint PK_SYS_PORTAL_KJ primary key (id);

--资讯表添加字段是否是外链
alter table sys_notice add is_link NVARCHAR2(1);
comment on column sys_notice.is_link is '是否是外链 1 是 0 否';
--将现在的老的数据设置一下全部不是外链
update sys_notice t set t.is_link = 0 where t.is_link is null;

--资讯表添加链接字段
alter table sys_notice add link_url NVARCHAR2(200);
comment on column sys_notice.link_url is '链接';

--理工大添加三个父类型
INSERT INTO SYS_NOTICE_CATEGORY(id,name,sort) VALUES ('NOTICE', '通知公告大类', '0');
INSERT INTO SYS_NOTICE_CATEGORY(id,name,sort) VALUES ('XYKX', '校园快讯', '1');
INSERT INTO SYS_NOTICE_CATEGORY(id,name,sort) VALUES ('MTLG', '媒体理工', '2');

--理工大添加子类型
INSERT INTO SYS_NOTICE_CATEGORY(id,name,sort,parent_id) VALUES ('SPLX', '审批立项', '2','NOTICE');

--理工大子类添加父类Id
update SYS_NOTICE_CATEGORY set PARENT_ID='NOTICE' where id='TZGG';
update SYS_NOTICE_CATEGORY set PARENT_ID='NOTICE' where id='ZXXW';
update SYS_NOTICE_CATEGORY set PARENT_ID='NOTICE' where id='ZCYD';

--门户项目表增加  主办单位ID
alter table sys_portal_xm add host_org_id NVARCHAR2(64);
comment on column sys_portal_xm.host_org_id is '主办单位ID';

--创建门户师资表---田军，将讲师用户与门户师资分开
create table SYS_PORTAL_TEACHER
(
   id                   NVARCHAR2(64)        not null,
   name                 NVARCHAR2(100),
   photo                NVARCHAR2(500),
   host_org_id			NVARCHAR2(64),
   sort_number			NUMBER,
   zw                   NVARCHAR2(100),
   status				NVARCHAR2(64),
   introduce            NCLOB,
   create_time          DATE,
   creator_id           NVARCHAR2(64)
);

comment on column SYS_PORTAL_TEACHER.name is
'姓名';

comment on column SYS_PORTAL_TEACHER.photo is
'照片';
comment on column SYS_PORTAL_TEACHER.sort_number is
'排序值，值越小越靠前';

comment on column SYS_PORTAL_TEACHER.zw is
'职务';

comment on column SYS_PORTAL_TEACHER.introduce is
'人物介绍';

comment on column SYS_PORTAL_TEACHER.create_time is
'创建时间';

comment on column SYS_PORTAL_TEACHER.creator_id is
'创建用户id';

alter table SYS_PORTAL_TEACHER
   add constraint PK_SYS_PORTAL_TEACHER primary key (id);

--新增关联字段parent_id
alter table sys_dict add parent_id NVARCHAR2(64);
comment on column sys_dict.parent_id is '关联id';

--课程表添加字段is_open_kj
alter table inf_course add is_open_kj NVARCHAR2(1);
comment on column inf_course.is_open_kj is '是否开放课件 1开放 0关闭';

--课程表增加字段is_open_zb
alter table inf_course add is_open_zb NVARCHAR2(1);
comment on column inf_course.is_open_zb is '是否开放直播回放 1开放 0关闭';

--课程表现有的课件和直播回放为开放
update inf_course set is_open_kj='1',is_open_zb='1';

--主办单位增加定制化系统名称 田军  2021-10-18
alter table sys_org add admin_sys_name NVARCHAR2(500);
comment on column sys_org.admin_sys_name is '管理端定制化系统名称';
alter table sys_org add portal_sys_name NVARCHAR2(500);
comment on column sys_org.portal_sys_name is '门户定制化系统名称';

--学员信息表中增加所在院系  所在班级
alter table sys_student_info add college NVARCHAR2(500);
comment on column sys_student_info.college is '所在院系';
alter table sys_student_info add classz NVARCHAR2(100);
comment on column sys_student_info.classz is '所在班级';

--删除理工大课件管理表
truncate table sys_portal_kj;
drop table sys_portal_kj;


--机构表增加地区字段 --叶从文 2021-10-18

--省级编码
-- Add/modify columns
alter table SYS_ORG add province_code NVARCHAR2(10);
-- Add comments to the columns
comment on column SYS_ORG.province_code
  is '省编码';

  --城市编码
-- Add/modify columns
alter table SYS_ORG add city_code NVARCHAR2(10);
-- Add comments to the columns
comment on column SYS_ORG.city_code
  is '市编码';

    --区编码
-- Add/modify columns
alter table SYS_ORG add district_code NVARCHAR2(10);
-- Add comments to the columns
comment on column SYS_ORG.district_code
  is '区县编码';

--培训项目地区人数限制表 --ycw 2021-10-21
create table biz_bm_count_limit
(
  id             NVARCHAR2(64) not null,
  xm_id          NVARCHAR2(64),
  province_code  NVARCHAR2(10),
  city_code      NVARCHAR2(10),
  district_code  NVARCHAR2(10),
  limit_bm_count INTEGER,
  creator_id     NVARCHAR2(64),
  create_time    DATE,
  updator_id     NVARCHAR2(64),
  update_time    DATE
);
-- Add comments to the columns
comment on column biz_bm_count_limit.xm_id
  is '培训项目id';
comment on column biz_bm_count_limit.province_code
  is '省份编码';
comment on column biz_bm_count_limit.limit_bm_count
  is '限制报名人数';
comment on column biz_bm_count_limit.creator_id
  is '创建用户id';
comment on column biz_bm_count_limit.create_time
  is '创建时间';
comment on column biz_bm_count_limit.updator_id
  is '修改用户id';
comment on column biz_bm_count_limit.update_time
  is '修改时间';
comment on column biz_bm_count_limit.city_code
  is '城市编码';
comment on column biz_bm_count_limit.district_code
  is '区县编码';
-- Create/Recreate primary, unique and foreign key constraints
alter table biz_bm_count_limit add constraint PK_biz_bm_count_limit primary key (ID);

--培训项目后台支持自动短信的配置 --ycw 2020-10-21
-- Add/modify columns
alter table BIZ_XM add is_auto_send_sms NVARCHAR2(1);
alter table BIZ_XM add sms_template NVARCHAR2(2000);
-- Add comments to the columns
comment on column BIZ_XM.is_auto_send_sms
  is '是否自动发送短信 1 是 0否';
comment on column BIZ_XM.sms_template
  is '自动发送短信模板内容';

--培训项目后台支持报名口令的配置 --ycw 2020-10-26
-- Add/modify columns
alter table BIZ_XM add bm_invite_code NVARCHAR2(1000);
-- Add comments to the columns
comment on column BIZ_XM.bm_invite_code
  is '报名报名口令 如果填写了报名时需要填写 ';

  --培训项目地区限制区域的最小区域编码--ycw 2020-10-27
-- Add/modify columns
alter table BIZ_BM_COUNT_LIMIT rename column limit_bm_count to LIMIT_COUNT;
alter table BIZ_BM_COUNT_LIMIT add limit_area_code NVARCHAR2(10);
comment on column BIZ_BM_COUNT_LIMIT.limit_area_code
  is '限制区域的最小区域编码';

-- 删除学员用户表的 other_org_name
--备份
create table sys_student1028 as select * from sys_student;
alter table sys_student drop column other_org_name;

-- 课件视频资源表
create table INF_KJ_VIDEO
(
  id            NVARCHAR2(64) not null,
  name     		NVARCHAR2(300),
  teacher_name  NVARCHAR2(64),
  introduce     NVARCHAR2(2000),
  content       NCLOB,
  host_org_id	NVARCHAR2(64),
  kjdw          NVARCHAR2(64),
  create_time   DATE,
  creator_id    NVARCHAR2(64),
  update_time   DATE,
  updator_id    NVARCHAR2(64),
  teacher_brief NVARCHAR2(1000),
  teacher_photo NVARCHAR2(1000)
);
-- Add comments to the table
comment on table INF_KJ_VIDEO
  is '课件视频资源表';
-- Add comments to the columns
comment on column INF_KJ_VIDEO.name
  is '视频名称';
comment on column INF_KJ_VIDEO.teacher_name
  is '教师姓名';
comment on column INF_KJ_VIDEO.introduce
  is '视频介绍';
comment on column INF_KJ_VIDEO.content
  is '详细内容';
comment on column INF_KJ_VIDEO.kjdw
  is '课件单位id';
comment on column INF_KJ_VIDEO.create_time
  is '创建时间';
comment on column INF_KJ_VIDEO.creator_id
  is '创建用户id';
comment on column INF_KJ_VIDEO.update_time
  is '修改时间';
comment on column INF_KJ_VIDEO.updator_id
  is '修改用户id';
comment on column INF_KJ_VIDEO.teacher_brief
  is '讲师简介';
comment on column INF_KJ_VIDEO.teacher_photo
  is '讲师的照片';
-- Create/Recreate primary, unique and foreign key constraints
alter table INF_KJ_VIDEO
  add constraint PK_INF_KJ_VIDEO primary key (ID);

--职业培训项目资料表添加资料分类字段
alter table biz_xm_material add  is_archive  NVARCHAR2(10);
comment on column biz_xm_material.is_archive  is '是否是归档资料 0不是  1是';

--给原有的项目资料表中添加资料分类的值
update biz_xm_material set is_archive='0' where is_archive is null;

--删除学习记录表中直播学习进度为0的数据
create table biz_student_leanring_score1105 as select * from biz_student_leanring_score;
delete from biz_student_leanring_score s where s.learning_type = 'ZB' and s.progress = 0;

--报名表中新增标记是否已开票字段
alter table biz_bm add is_mark_invoice NVARCHAR2(10);
comment on column biz_bm.is_mark_invoice is '标记是否已开票 0未开票 1 已开票';

--删除面授签到表中的 bm_course_id，并增加字段，先要做数据迁移
--
create table biz_ms_arrive_1110 as select * from biz_ms_arrive;
--

alter table BIZ_MS_ARRIVE  add xm_id  NVARCHAR2(64);
comment on column BIZ_MS_ARRIVE.xm_id is '项目ID';
alter table BIZ_MS_ARRIVE  add student_id  NVARCHAR2(64);
comment on column BIZ_MS_ARRIVE.student_id is '学员ID';
alter table BIZ_MS_ARRIVE  add course_id  NVARCHAR2(64);
comment on column BIZ_MS_ARRIVE.course_id is '课程ID';
--
update biz_ms_arrive ma set (ma.xm_id,ma.student_id,ma.course_id) = (select bm.xm_id,bm.student_id,xcs.course_id from biz_bm bm inner join biz_bm_course bc on bc.bm_id = bm.id
inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id where bc.id = ma.bm_course_id) where exists(select 1 from biz_bm bm inner join biz_bm_course bc on bc.bm_id = bm.id
inner join biz_xm_course_setting xcs on xcs.id = bc.course_setting_id where bc.id = ma.bm_course_id);

--
alter table BIZ_MS_ARRIVE drop column bm_course_id;

--

---支付模块代码重构 田军
-- 创建平台收款方表
create table INF_BANK
(
  id            NVARCHAR2(64) not null,
  name     		NVARCHAR2(300),
  host_org_id	NVARCHAR2(64) not null,
  is_default	NVARCHAR2(1)
);
alter table INF_BANK
  add constraint PK_INF_BANK primary key (ID);

alter table inf_bank_config rename column bank to bank_id;
alter table inf_bank_config drop column host_org_id;
alter table inf_bank_config drop column is_default;

alter table comm_order rename column bank to bank_id;
alter table comm_order drop column pay_plat;
--修改支付配置的表名
alter table inf_bank rename to sys_bank;
alter table inf_bank_config rename to sys_bank_config;
----------k
alter table sys_bank add status NVARCHAR2(64);
comment on column sys_bank.status
  is '状态，OK启用、BLOCK禁用';

alter table sys_bank add is_default NVARCHAR2(1);
comment on column sys_bank.is_default
  is '是否是默认的收款方';

--支付配置表中增加状态字段
alter table sys_bank_config add status NVARCHAR2(20);
comment on column sys_bank_config.status
  is '状态 启用OK 、禁用BLOCK';


--增加聚合支付订单编号,并添加唯一性索引,注意添加唯一性索引
alter table comm_order add union_order_no NVARCHAR2(100);
comment on column comm_order.union_order_no
  is '聚合支付订单编号,仅聚合支付时有值';
--课程表增加字段is_open_tk
alter table inf_course add is_open_tk NVARCHAR2(1);
comment on column inf_course.is_open_tk is '是否开放题库 1开放 0关闭';

--课程表增加字段is_open_person_bm
alter table inf_course add is_open_person_bm NVARCHAR2(1);
comment on column inf_course.is_open_person_bm is '是否开放个人报名 1开放 0关闭';

--报名表中增加导师用户id
alter table biz_bm add tutor_id NVARCHAR2(64);
comment on column biz_bm.tutor_id is '导师用户id';

alter table biz_bm add mark_refund_user_id NVARCHAR2(64);
comment on column biz_bm.mark_refund_user_id is '标记退费用户ID';

alter table biz_bm add mark_refund_reason NVARCHAR2(64);
comment on column biz_bm.mark_refund_reason is '标记退费备注';

alter table biz_bm add refund_amount NUMBER;
comment on column biz_bm.refund_amount is '退费金额';

------CRP 数据同步
---创建CRP教职工表
create table CRP_TEACHER
(
  id              NVARCHAR2(64),
  ticket_number   NVARCHAR2(100),
  name            NVARCHAR2(100),
  gender          NVARCHAR2(6),
  department_name NVARCHAR2(200),
  department_id   NVARCHAR2(64),
  status          NVARCHAR2(64),
  sync_time		  DATE,
  host_org_id     NVARCHAR2(64)
);
alter table CRP_TEACHER
  add constraint PK_CRP_TEACHER primary key (ID);

--创建CRP部门表
create table CRP_DEPARTMENT
(
  id              NVARCHAR2(64),
  code   		  NVARCHAR2(100),
  name            NVARCHAR2(100),
  property_id     NVARCHAR2(6),
  property_name   NVARCHAR2(200),
  parent_id       NVARCHAR2(64),
  status          NVARCHAR2(64),
  sync_time		  DATE,
  host_org_id     NVARCHAR2(64)
);
alter table CRP_DEPARTMENT
  add constraint PK_CRP_DEPARTMENT primary key (ID);



alter table sys_user add is_crp_user NVARCHAR2(1);
comment on column sys_user.is_crp_user is '是否是CRP用户';


alter table sys_user_info add gender NVARCHAR2(10);
comment on column sys_user_info.gender is '性别';

-- 学员用户表新增单位字段 company
ALTER TABLE sys_student ADD company NVARCHAR2(1000);
comment on column sys_student.company is '学员单位';

--学员信息表增加单位所在地
ALTER TABLE sys_student_info ADD company_province_code NVARCHAR2(50);
comment on column sys_student_info.company_province_code is '单位所在地-省份';
ALTER TABLE sys_student_info ADD company_city_code NVARCHAR2(50);
comment on column sys_student_info.company_city_code is '单位所在地-市';
ALTER TABLE sys_student_info ADD company_district_code NVARCHAR2(50);
comment on column sys_student_info.company_district_code is '单位所在地-市';

--更新学员的单位信息
update sys_student s set s.company = (select g.name from sys_org g where g.id = s.org_id)
where s.student_type = 'SOCIAL' and s.company is null and exists(select 1 from sys_org a where a.id = s.org_id);

--更新学员的单位所在地
update sys_student_info f set (f.company_province_code,f.company_city_code,f.company_district_code) = (
select g.province_code,g.city_code,g.district_code from sys_student s inner join sys_org g on s.org_id = g.id where
s.id = f.student_id and s.student_type = 'SOCIAL' and s.org_id is not null and g.province_code is not null
) where exists(
  select 1 from sys_student a inner join sys_org b on a.org_id = b.id where a.id = f.student_id and a.student_type = 'SOCIAL' and a.org_id is not null
   and b.province_code is not null
);
--修正培训机构设置
update sys_student s set s.org_id = null where  s.student_type = 'SOCIAL' and s.org_id is not null;

--然后需要检查培训机构的数据是否正确，然后修改

--增加是否线下培训
ALTER TABLE biz_bm ADD is_offline NVARCHAR2(1);
comment on column biz_bm.is_offline is '是否线下培训';

--线下报名人数
ALTER TABLE biz_xm ADD OFF_LINE_BM_COUNT NUMBER;
comment on column biz_xm.OFF_LINE_BM_COUNT is '线下报名人数';

--在线学习人数
ALTER TABLE biz_xm ADD ONLINE_STUDY_COUNT NUMBER;
comment on column biz_xm.ONLINE_STUDY_COUNT is '在线学习人数';

--修改项目资料表,
alter table biz_xm_material rename column is_archive to category;
comment on column biz_xm_material.category is '0 学员学习资料 1 项目归档资料 2、培训佐证材料';

--报名前是否必须完善个人信息
ALTER TABLE biz_xm ADD is_must_fill_personal_info NVARCHAR2(1);
comment on column biz_xm.is_must_fill_personal_info is '报名前是否必须完善个人信息';

--直播抓拍表
create table BIZ_LIVE_PHOTO_LOG
(
  id             NVARCHAR2(64),
  xm_id          NVARCHAR2(64),
  student_id		NVARCHAR2(64),
  course_live_id NVARCHAR2(64),
  chapter_id     NVARCHAR2(64),
  lesson_id      NVARCHAR2(64),
  url            NVARCHAR2(1000),
  create_time    DATE
);
alter table BIZ_LIVE_PHOTO_LOG add constraint pk_biz_live_photo_log primary key (id);


create table BIZ_LIVE_FACE_VERIFY_LOG
(
  id         NVARCHAR2(64) not null,
  xm_id      NVARCHAR2(64),
  courselive_id      NVARCHAR2(64),
  student_id NVARCHAR2(64),
  url        NVARCHAR2(2000),
  result     NUMBER(20,3),
  is_pass    NVARCHAR2(1),
  log_time   DATE
);
alter table biz_live_face_verify_log add constraint pk_biz_live_face_verify_log primary key (id);

--培训项目增加是否允许手机端学习
ALTER TABLE biz_xm ADD is_allow_mobile_study NVARCHAR2(1);
comment on column biz_xm.is_allow_mobile_study is '是否允许手机端学习';

update biz_xm m set m.is_allow_mobile_study = 1 where m.is_allow_mobile_study is null;

--机构表增加字段 是否已集成CRP
ALTER TABLE sys_org ADD is_crp_integrate NVARCHAR2(1);
comment on column sys_org.is_crp_integrate is '是否已集成crp 1是 0 否';
--如果主办单位是国土的话 要把这个值设置为1
update sys_org g set g.is_crp_integrate=1 where g.id='hbgtzyzyxy';

--用户信息表增加字段
alter table sys_user_info add work_unit NVARCHAR2(64);
comment on column sys_user_info.work_unit is '工作单位';

alter table sys_user_info add study_direction NVARCHAR2(200);
comment on column sys_user_info.study_direction is '研究方向';

alter table sys_user_info add courses NVARCHAR2(500);
comment on column sys_user_info.courses is '主讲课程';

alter table sys_user_info add office_tel NVARCHAR2(64);
comment on column sys_user_info.office_tel is '办公电话';

alter table sys_user_info add remark NVARCHAR2(200);
comment on column sys_user_info.remark is '备注';

--学员用户信息表增加学历证书图片字段
alter table sys_student_info add edu_certi_photo NVARCHAR2(200);
comment on column sys_student_info.edu_certi_photo is '学历证书图片';

--职业鉴定费用设置表 增加
alter table INF_FEE_SETTING add upgrade_amount NUMBER;
comment on column INF_FEE_SETTING.upgrade_amount is '提高班收费标准';

alter table INF_FEE_SETTING add strong_amount NUMBER;
comment on column INF_FEE_SETTING.strong_amount is '强化班收费标准';

--创建审批用户表
create table COMM_APPROVE_USER
(
   id                   NVARCHAR2(64)        not null,
   step_id              NVARCHAR2(64),
   user_id              NVARCHAR2(64)
);

comment on column COMM_APPROVE_USER.step_id is
'审批流程详情表id';

comment on column COMM_APPROVE_USER.user_id is
'审批用户id';

alter table COMM_APPROVE_USER
   add constraint PK_COMM_APPROVE_USER primary key (id);

--针对已经有的审批流程，设置审批用户ID 即：老数据处理
insert into comm_approve_user
select sys_guid() as id, a.step_id,a.user_id  from(
select distinct step_id,user_id from comm_approve_log) a;

--删除comm_approve_step 表中的org字段，删除之前记得做数据备份
alter table comm_approve_step drop column org_id;

--培训计划详情增加字段
alter table biz_plan add xm_sources NVARCHAR2(64);
comment on column biz_plan.xm_sources is '项目来源';

alter table biz_plan add cooper_org_id NVARCHAR2(64);
comment on column biz_plan.cooper_org_id is '委托单位';

alter table biz_plan add organizer NVARCHAR2(64);
comment on column biz_plan.organizer is '承办单位';

alter table biz_plan add training_form NVARCHAR2(64);
comment on column biz_plan.training_form is '培训形式';

alter table biz_plan add is_issue_certificate NVARCHAR2(1);
comment on column biz_plan.is_issue_certificate is '是否发放证书1是；0否';

alter table biz_plan add teachers NVARCHAR2(64);
comment on column biz_plan.teachers is '教师团队';

alter table biz_plan add enroll_way NVARCHAR2(64);
comment on column biz_plan.enroll_way is '招生方式';

alter table biz_plan add units NVARCHAR2(64);
comment on column biz_plan.units is '费用单位';

alter table biz_plan add leader_id NVARCHAR2(64);
comment on column biz_plan.leader_id is '项目负责人';

alter table biz_plan add address NVARCHAR2(200);
comment on column biz_plan.address is '培训地点';

alter table biz_plan add hours number;
comment on column biz_plan.hours is '计划课时';

alter table biz_plan add amount number;
comment on column biz_plan.amount is '金额';

alter table biz_plan add count number;
comment on column biz_plan.count is '学习人数';

alter table biz_plan add start_time DATE;
comment on column biz_plan.start_time is '开始时间';

alter table biz_plan add end_time DATE;
comment on column biz_plan.end_time is '结束时间';

alter table biz_plan add paper NVARCHAR2(200);
comment on column biz_plan.paper is '立项审批表';

--机构表增加单位字段
alter table sys_org add contact NVARCHAR2(64);
comment on column sys_org.contact is '联系人';

alter table sys_org add telephone NVARCHAR2(64);
comment on column sys_org.telephone is '电话';


--ycw modify 系统设置表增加主办单位 (未执行)
-- Add/modify columns
alter table SYS_SETTING add org_id NVARCHAR2(64);
-- Add comments to the columns
comment on column SYS_SETTING.org_id
  is '主办单位id';

--ycw modify 微信扫码绑定的微信openid (未执行)
-- Add/modify columns
alter table SYS_STUDENT add open_id NVARCHAR2(100);
-- Add comments to the columns
comment on column SYS_STUDENT.open_id
  is '微信扫码绑定的微信openid';

--ycw modify 消息相关表脚本  (未执行)
-- Create table 记录消息 的已读用户
create table SYS_MESSAGE_RECEIVED
(
  id         NVARCHAR2(64) not null,
  message_id NVARCHAR2(64),
  user_id    NVARCHAR2(64),
  read_time  DATE
);
-- Add comments to the table

comment on table SYS_MESSAGE_RECEIVED
  is '记录消息 的已读用户';
-- Add comments to the columns
comment on column SYS_MESSAGE_RECEIVED.message_id
  is '消息id';
comment on column SYS_MESSAGE_RECEIVED.user_id
  is '管理用户id';
comment on column SYS_MESSAGE_RECEIVED.read_time
  is '读取时间';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_MESSAGE_RECEIVED
  add constraint PK_SYS_MESSAGE_RECEIVED primary key (ID);


-- Create table  记录消息  与接受用户的关系，此处是管理用户
create table SYS_MESSAGE_2_USER
(
  id          NVARCHAR2(64) not null,
  message_id  NVARCHAR2(64),
  user_id     NVARCHAR2(64),
  create_time DATE
);
-- Add comments to the table
comment on table SYS_MESSAGE_2_USER
  is '记录消息  与接受用户的关系，此处是管理用户';
-- Add comments to the columns
comment on column SYS_MESSAGE_2_USER.message_id
  is '消息id';
comment on column SYS_MESSAGE_2_USER.user_id
  is '管理用户id';
comment on column SYS_MESSAGE_2_USER.create_time
  is '创建时间';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_MESSAGE_2_USER
  add constraint PK_SYS_MESSAGE_2_USER primary key (ID);


-- Create table 记录消息  与接受角色的 关联关系
create table SYS_MESSAGE_2_ROLE
(
  id          NVARCHAR2(64) not null,
  message_id  NVARCHAR2(64),
  role        NVARCHAR2(64),
  create_time DATE
);
-- Add comments to the table
comment on table SYS_MESSAGE_2_ROLE
  is '记录消息  与接受角色的 关联关系';
-- Add comments to the columns
comment on column SYS_MESSAGE_2_ROLE.message_id
  is '消息id';
comment on column SYS_MESSAGE_2_ROLE.role
  is '角色枚举';
comment on column SYS_MESSAGE_2_ROLE.create_time
  is '创建时间';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_MESSAGE_2_ROLE
  add constraint PK_SYS_MESSAGE_2_ROLE primary key (ID);


-- Create table 记录消息  与接受机构的关系
create table SYS_MESSAGE_2_ORG
(
  id          NVARCHAR2(64) not null,
  message_id  NVARCHAR2(64),
  org_id      NVARCHAR2(64),
  create_time DATE
);
-- Add comments to the table
comment on table SYS_MESSAGE_2_ORG
  is '记录消息  与接受机构的关系';
-- Add comments to the columns
comment on column SYS_MESSAGE_2_ORG.message_id
  is '消息id';
comment on column SYS_MESSAGE_2_ORG.org_id
  is '机构id';
comment on column SYS_MESSAGE_2_ORG.create_time
  is '创建时间';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_MESSAGE_2_ORG
  add constraint PK_SYS_MESSAGE_2_ORG primary key (ID);


-- Create table   系统消息
create table SYS_MESSAGE
(
  id               NVARCHAR2(64) not null,
  title            NVARCHAR2(100),
  content          NVARCHAR2(2000),
  is_must_received NVARCHAR2(1),
  create_time      DATE,
  creator_id       NVARCHAR2(64),
  is_sys_created   NVARCHAR2(1),
  host_org_id      NVARCHAR2(64),
  type             NVARCHAR2(100)
);
-- Add comments to the table
comment on table SYS_MESSAGE
  is '系统消息';
-- Add comments to the columns
comment on column SYS_MESSAGE.title
  is '标题';
comment on column SYS_MESSAGE.content
  is '内容';
comment on column SYS_MESSAGE.is_must_received
  is '是否必读 1 是  0 否';
comment on column SYS_MESSAGE.create_time
  is '创建时间';
comment on column SYS_MESSAGE.creator_id
  is '创建用户id';
comment on column SYS_MESSAGE.is_sys_created
  is '是否系统自动生成 1 是  0 否';
comment on column SYS_MESSAGE.host_org_id
  is '所属主办单位id';
comment on column SYS_MESSAGE.type
  is '消息类型';
-- Create/Recreate primary, unique and foreign key constraints
alter table SYS_MESSAGE
  add constraint PK_SYS_MESSAGE primary key (ID);

--项目表中增加审核字段
alter table biz_xm add is_need_approve NVARCHAR2(1);
comment on column biz_xm.is_need_approve is '报名是否需要审核 0 否；1是';

--项目课程设置表增加字段
alter table biz_xm_course_setting add teacher_name NVARCHAR2(50);
comment on column biz_xm_course_setting.teacher_name is '讲师名称';

alter table biz_xm_course_setting add course_date NVARCHAR2(20);
comment on column biz_xm_course_setting.course_date is '上课日期';

alter table biz_xm_course_setting add course_time NVARCHAR2(20);
comment on column biz_xm_course_setting.course_time is '上课时间';

alter table biz_xm_course_setting add venue_room_id NVARCHAR2(64);
comment on column biz_xm_course_setting.venue_room_id is '培训室ID';

--机构表增加 机构性质 字段
alter table sys_org add nature NVARCHAR2(10);
comment on column sys_org.nature is '机构性质:1 政府单位 2、企业 3、其他';

--课件表增加video_id
alter table inf_kj add video_id NVARCHAR2(64);
comment on column inf_kj.video_id is '视频ID 即：课件资源表ID';

alter table biz_zyjd_bm add training_class NVARCHAR2(64);
comment on column biz_zyjd_bm.training_class is '培训班分类';

--修改字段名
alter table sys_setting rename column org_id to host_org_id;


--创建课件视频授权表
create table INF_KJ_ASSIGN
(
   id                   NVARCHAR2(64)        not null,
   video_id             NVARCHAR2(64),
   host_org_id          NVARCHAR2(64),
   time                 DATE
);

comment on table INF_KJ_ASSIGN is
'课件资源授权表';

comment on column INF_KJ_ASSIGN.id is
'主键id';

comment on column INF_KJ_ASSIGN.video_id is
'视频资源id';

comment on column INF_KJ_ASSIGN.host_org_id is
'主办单位id';

comment on column INF_KJ_ASSIGN.time is
'分配时间';

alter table INF_KJ_ASSIGN
   add constraint PK_INF_KJ_ASSIGN primary key (id);

--初始化课件资源表（inf_kj_video）的数据
insert into inf_kj_video(id,name,teacher_name,teacher_photo,introduce,content,host_org_id,kjdw,teacher_brief)
select kj.id ,c.name,kj.teacher_name,kj.teacher_photo,kj.introduce,kj.content,c.host_org_id,kj.kjdw,kj.teacher_brief
from inf_kj kj
inner join inf_course c on kj.course_id = c.id
--更新课件表的视频ID字段
update inf_kj kj set kj.video_id = kj.id;


-- 课件视频资源授权表
create table INF_KJ_ASSIGN
(
   id             NVARCHAR2(64)        not null,
   video_id       NVARCHAR2(64),
   host_org_id    NVARCHAR2(64),
   time           DATE
);
comment on table INF_KJ_ASSIGN is '课件授权表';
comment on column INF_KJ_ASSIGN.id is '主键id';
comment on column INF_KJ_ASSIGN.video_id is '视频资源id';
comment on column INF_KJ_ASSIGN.host_org_id is '主办单位id';
comment on column INF_KJ_ASSIGN.time is '分配时间';
alter table INF_KJ_ASSIGN add constraint PK_INF_KJ_ASSIGN primary key (ID);

-- 题库授权表
create table BIZ_QDB_ASSIGN
(
   id             NVARCHAR2(64)        not null,
   db_id          NVARCHAR2(64),
   host_org_id    NVARCHAR2(64),
   time           DATE
);
comment on table BIZ_QDB_ASSIGN is '题库授权表';
comment on column BIZ_QDB_ASSIGN.id is '主键id';
comment on column BIZ_QDB_ASSIGN.db_id is '题库资源id';
comment on column BIZ_QDB_ASSIGN.host_org_id is '主办单位id';
comment on column BIZ_QDB_ASSIGN.time is '分配时间';
alter table BIZ_QDB_ASSIGN add constraint PK_IBIZ_QDB_ASSIGN primary key (ID);

--课程题库关系表
create table BIZ_QDB_2_COURSE
(
  id               NVARCHAR2(64) not null,
  db_id            NVARCHAR2(64),
  course_id        NVARCHAR2(64)
);
-- Add comments to the table
comment on table BIZ_QDB_2_COURSE
  is '课程题库关系表';
-- Add comments to the columns
comment on column BIZ_QDB_2_COURSE.id
  is '主键id';
comment on column BIZ_QDB_2_COURSE.db_id
  is '题库id';
comment on column BIZ_QDB_2_COURSE.course_id
  is '课程id';
-- Create/Recreate primary, unique and foreign key constraints
alter table BIZ_QDB_2_COURSE
  add constraint PK_BIZ_QDB_2_COURSE primary key (ID);

--生成课程题库关系表
insert into biz_qdb_2_course(id,course_id,db_id) select sys_guid() id,db.course_id,db.id db_id from biz_question_db db;

--删除字段
alter table biz_question_db drop column course_id;

--修改字段
alter table biz_question_db rename column org_id to host_org_id;

--修改项目表
alter table biz_xm drop column arrive_qrcode_url;

alter table sys_student_info add student_ticket NVARCHAR2(500);
comment on column sys_student_info.student_ticket is '学生证URL';

--职业技能报名表增加 推荐代码
alter table biz_zyjd_bm add recommend_code NVARCHAR2(50);
comment on column biz_zyjd_bm.recommend_code is '推荐代码';

---以上内容已经与2022年3月7号更新到线上服务
--更改视频表的课件单位ID字段名称
alter table inf_kj_video rename column kjdw to kjdw_id;
alter table inf_kj drop column kjdw;

--课件视频表增加分类ID
alter table inf_kj_video add type_id NVARCHAR2(64);
comment on column inf_kj_video.type_id is '分类ID';

--职业鉴定报名表增加承诺书
alter table biz_zyjd_bm add commitment NVARCHAR2(500);
comment on column biz_zyjd_bm.commitment is '承诺书';

--职业鉴定报名表增加是否是社会类的培训评价
alter table biz_zyjd_bmbatch add is_social NVARCHAR2(1);
comment on column biz_zyjd_bmbatch.is_social is '是否是社会类的培训评价';


alter table INF_FEE_SETTING add batch_id NVARCHAR2(64);
comment on column INF_FEE_SETTING.batch_id is '批次ID';

---自定义表单版块----2021-12月合并 by tianjun
-- 创建自定义表单表
create table BIZ_FORM
(
   id                 NVARCHAR2(64)        not null,
   name               NVARCHAR2(200),
   host_org_id        NVARCHAR2(64),
   creator_id         NVARCHAR2(64),
   create_time        DATE,
   updator_id         NVARCHAR2(64),
   update_time        DATE
);

comment on column BIZ_FORM.id is
'主键';

comment on column BIZ_FORM.name is
'名称';

comment on column BIZ_FORM.host_org_id is
'主办单位ID';

comment on column BIZ_FORM.creator_id is
'创建用户id';

comment on column BIZ_FORM.create_time is
'创建时间';

comment on column BIZ_FORM.updator_id is
'修改用户id';

comment on column BIZ_FORM.update_time is
'修改时间';

alter table BIZ_FORM
   add constraint PK_BIZ_FORM primary key (id);


---创建表单字段表
create table BIZ_FIELD
(
   id                 NVARCHAR2(64)        not null,
   name               NVARCHAR2(200),
   type               NVARCHAR2(100),
   is_required        NVARCHAR2(1),
   datasource_type    NVARCHAR2(64),
   datasource_value   NCLOB,
   form_id            NVARCHAR2(64),
   sort_number        NUMBER,
   create_time        DATE,
   creator_id         NVARCHAR2(64)
);

comment on column BIZ_FIELD.id is
'主键';

comment on column BIZ_FIELD.name is
'字段名称';

comment on column BIZ_FIELD.type is
'字段展示类型枚举:文本、数字、日期选择、日期时间选择、单选、多选、图片上传、文档上传';

comment on column BIZ_FIELD.is_required is
'是否是必填项    1    是  0 否';

comment on column BIZ_FIELD.datasource_type is
'数据源枚举：1、接口  2、自定义，此字段仅对单选、多效';

comment on column BIZ_FIELD.datasource_value is
'数据源不同则此字段取值不同，1、接口地址  2、JSON大字段';

comment on column BIZ_FIELD.form_id is
'所属表单ID';

comment on column BIZ_FIELD.create_time is
'创建时间';

comment on column BIZ_FIELD.creator_id is
'创建人';

alter table BIZ_FIELD
   add constraint PK_BIZ_FIELD primary key (id);

----表单作答结果表
create table BIZ_FORM_DATA
(
   id                 NVARCHAR2(64),
   xm_id              NVARCHAR2(64),
   student_id         NVARCHAR2(64),
   form_id            NVARCHAR2(64),
   field_id           NVARCHAR2(64),
   field_value        NVARCHAR2(500),
   field_text         NVARCHAR2(500)
);

comment on column BIZ_FORM_DATA.id is
'主键';

comment on column BIZ_FORM_DATA.xm_id is
'项目id';

comment on column BIZ_FORM_DATA.student_id is
'学员id';

comment on column BIZ_FORM_DATA.form_id is
'表单id';

comment on column BIZ_FORM_DATA.field_id is
'字段id';

comment on column BIZ_FORM_DATA.field_value is
'学员作答填写的值';

comment on column BIZ_FORM_DATA.field_text is
'填写值对应的文本,冗余字段,便于导出';

alter table BIZ_FORM_DATA
   add constraint PK_BIZ_FORM_DATA primary key (id);

--创建项目技能关系表
create table BIZ_XM_2_SKILL
(
   id                  NVARCHAR2(64) not null,
   xm_Id               NVARCHAR2(64),
   industry_id         NVARCHAR2(64),
   profession_id       NVARCHAR2(64),
   tech_level          NVARCHAR2(200)
);

comment on column BIZ_XM_2_SKILL.id is
'主键';

comment on column BIZ_XM_2_SKILL.xm_Id is
'项目id';

comment on column BIZ_XM_2_SKILL.industry_id is
'行业id';

comment on column BIZ_XM_2_SKILL.profession_id is
'职业id';

comment on column BIZ_XM_2_SKILL.tech_level is
'等级 枚举';

alter table BIZ_XM_2_SKILL
   add constraint PK_BIZ_XM_2_SKILL primary key (id);

--创建项目表单关系表
create table BIZ_BATCH_2_FORM
(
   id                 NVARCHAR2(64)        not null,
   xm_id           NVARCHAR2(64),
   form_id         		NVARCHAR2(64),
   status             NVARCHAR2(64)
);

comment on column BIZ_BATCH_2_FORM.id is
'主键ID';

comment on column BIZ_BATCH_2_FORM.xm_id is
'项目id';

comment on column BIZ_BATCH_2_FORM.form_id is
'表单ID';

comment on column BIZ_BATCH_2_FORM.status is
'枚举 OK 、BLOCK';

alter table BIZ_BATCH_2_FORM
   add constraint PK_BIZ_BATCH_2_FORM primary key (id);

--计划表增加是否是职业节能类的培训字段
alter table biz_plan add is_skill NVARCHAR2(1);
comment on column biz_plan.is_skill is '是否是职业技能类的培训 1是 0不是';

--表单字段表增加是否是个人信息相关字段
alter table biz_field add submit_name nvarchar2(50);
comment on column biz_field.submit_name is '表单提交时使用的字段名称';

alter table biz_field add is_personal nvarchar2(1);
comment on column biz_field.is_personal is '是否是个人信息 1 是   0 否';

--增加字段用于控制自定义表单字段所针对的学员类型
    alter table biz_field add required_student_type nvarchar2(20);
comment on column biz_field.required_student_type is '限制必填该字段的学员类型';


--表名重命名
rename biz_batch_2_form to biz_xm_2_form;
--删除项目表单关系表中的状态字段
alter table biz_xm_2_form drop column status;

--更改字段名
alter table biz_field rename column required_student_type to required_type;
comment on column biz_field.required_type is '必填类型枚举：NO:非必填  ALL：全部必填  SCHOOL：学生必填  SOCIAL：在职人员必填';

alter table biz_field drop column is_required;

--视频表增加字段是 是否是直播转换
alter table inf_kj_video add is_live INTEGER;
comment on column inf_kj_video.is_live is '是否是直播回放转换 1是 0 否';

--删除订单表中请求第三方平台生成订单的时间
alter table comm_order drop column xd_time;

--报名表增加审核结果
alter table biz_bm add approve_advice nvarchar2(200);
comment on column biz_bm.approve_advice is '审核意见';

--删除默认收款方的判断
create table sys_bank_0406 as select * from sys_bank;

delete from sys_bank b where b.is_default!=1;

alter table sys_bank drop column is_default;

create table biz_zyjd_bmbatch_0406 as select * from biz_zyjd_bmbatch;

alter table biz_zyjd_bmbatch drop column bank;


--crp学员用户
create table crp_student
(
    id                          NVARCHAR2(64)        not null,
     student_num                NVARCHAR2(64),
     name            			NVARCHAR2(64),
     gender          			NVARCHAR2(64),
     college                    NVARCHAR2(64),
     grade				        NVARCHAR2(64),
     sfzh				        NVARCHAR2(64),
     administration_class       NVARCHAR2(64),
	 specialty				    NVARCHAR2(64),
	 mobile					    NVARCHAR2(64),
	 status         		    NVARCHAR2(64),
	 sync_time		  		    DATE,
	 host_org_id     		    NVARCHAR2(64)
);
comment on column crp_student.id is
'主键';
comment on column crp_student.student_num is
'学号';
comment on column crp_student.name is
'姓名';
comment on column crp_student.gender is
'性别';
comment on column crp_student.college is
'院系';
comment on column crp_student.grade is
'年级';
comment on column crp_student.sfzh is
'身份证';
comment on column crp_student.administration_class is
'行政班';
comment on column crp_student.specialty is
'专业';
comment on column crp_student.mobile is
'手机号';
alter table crp_student
  add constraint PK_CRP_STUDENT primary key (ID);



alter table BIZ_ZYJD_BM add new_certificate NVARCHAR2(200);
comment on column BIZ_ZYJD_BM.new_certificate is '新资格证书';

---以下改动是针对2022-4-14国土会议提出的改动变更
--管理用户信息表增加是否外聘
alter table sys_user_info add is_out nvarchar2(1);
comment on column sys_user_info.is_out is '是否外聘，1是 0 否';

--将原来的培训计划中的相关字段迁移到计划详情表

--培训计划详情表增加承办单位ID
alter table biz_plan_detail add organizer_id NVARCHAR2(64);
comment on column biz_plan_detail.organizer_id is '承办单位ID';

--培训计划详情表增加最近保存时间
alter table biz_plan_detail add save_time DATE;
comment on column biz_plan_detail.save_time is '最近保存时间';

--培训计划详情表增加上报时间
alter table biz_plan_detail add submit_time DATE;
comment on column biz_plan_detail.submit_time is '上报时间';

--培训计划详情表增加关联审批事项ID
alter table biz_plan_detail add approve_item_id NVARCHAR2(64);
comment on column biz_plan_detail.approve_item_id is '审批事项ID';

--培训计划详情表增加是否已经执行
alter table biz_plan_detail add is_execute NVARCHAR2(1);
comment on column biz_plan_detail.is_execute is '1 是 0 否';

--培训计划详情表增加执行时间
alter table biz_plan_detail add execute_time DATE;
comment on column biz_plan_detail.execute_time is '增加执行时间';

--合同
alter table biz_plan_detail add contract NVARCHAR2(500);
comment on column biz_plan_detail.contract is '合同下载地址';

--增加项目来源
alter table biz_plan_detail add xm_sources NVARCHAR2(100);
comment on column biz_plan_detail.xm_sources is '项目来源';

--委托单位
alter table biz_plan_detail add cooper_org_id NVARCHAR2(64);
comment on column biz_plan_detail.cooper_org_id is '委托单位';

--承办单位id
alter table biz_plan_detail add organizer_id NVARCHAR2(64);
comment on column biz_plan_detail.organizer_id is '承办单位ID';

--培训形式
alter table biz_plan_detail add training_form NVARCHAR2(64);
comment on column biz_plan_detail.training_form is '培训形式';

--是否颁发结业证书
alter table biz_plan_detail add is_issue_certificate NVARCHAR2(64);
comment on column biz_plan_detail.is_issue_certificate is '是否颁发结业证书';

--教师团队
alter table biz_plan_detail add teachers NVARCHAR2(64);
comment on column biz_plan_detail.teachers is '教师团队';

--招生方式
alter table biz_plan_detail add enroll_way NVARCHAR2(64);
comment on column biz_plan_detail.enroll_way is '招生方式';

-- 项目负责人ID
alter table biz_plan_detail add leader_id NVARCHAR2(64);
comment on column biz_plan_detail.leader_id is '项目负责人';

--培训地点
alter table biz_plan_detail add address NVARCHAR2(64);
comment on column biz_plan_detail.address is '培训地点';

--计划课时
alter table biz_plan_detail add hours NUMBER;
comment on column biz_plan_detail.hours is '计划课时';

--培养人数
alter table biz_plan_detail add count NUMBER;
comment on column biz_plan_detail.count is '培养人数';

--收费标准
alter table biz_plan_detail add amount NUMBER;
comment on column biz_plan_detail.amount is '收费标准';

--收费标准对应的单位
alter table biz_plan_detail add units NVARCHAR2(64);
comment on column biz_plan_detail.units is '收费标准对应的单位';

--是否单位统付
alter table biz_plan_detail add is_org_pay NVARCHAR2(1);
comment on column biz_plan_detail.is_org_pay is '是否单位统付';

--立项审批表
alter table biz_plan_detail add paper NVARCHAR2(500);
comment on column biz_plan_detail.paper is '立项审批表地址';

--状态
alter table biz_plan_detail add status NVARCHAR2(64);
comment on column biz_plan_detail.status is '状态';

--项目编号
alter table biz_plan_detail add SERIAL_NUMBER NVARCHAR2(100);
comment on column biz_plan_detail.SERIAL_NUMBER is '项目编号';

--数据备份
create table biz_plan_0419 as select * from biz_plan;
create table biz_plan_detail_0419 as select * from biz_plan_detail;
create table comm_approve_item_0419 as select * from comm_approve_item;
create table comm_approve_log_0419 as select * from comm_approve_log;
create table biz_xm_0419 as select * from biz_xm;

--执行下面操作前  请一定确定相关数据迁移完毕！！！
--删除培训计划表中不需要的字段
alter table biz_plan drop column organizer_id;
alter table biz_plan rename column save_time to create_time;
alter table biz_plan drop column submit_time;
alter table biz_plan drop column approve_item_id;
alter table biz_plan drop column is_execute;
alter table biz_plan drop column execute_time;
alter table biz_plan drop column contract;
alter table biz_plan drop column xm_sources;
alter table biz_plan drop column cooper_org_id;
alter table biz_plan drop column org_id;
alter table biz_plan drop column organizer_id;
alter table biz_plan drop column training_form;
alter table biz_plan drop column is_issue_certificate;
alter table biz_plan drop column teachers;
alter table biz_plan drop column enroll_way;
alter table biz_plan drop column leader_id;
alter table biz_plan drop column address;
alter table biz_plan drop column hours;
alter table biz_plan drop column count;
alter table biz_plan drop column amount;
alter table biz_plan drop column units;
alter table biz_plan drop column is_org_pay;
alter table biz_plan drop column paper;
alter table biz_plan drop column is_plan_publish;
alter table biz_plan drop column plan_publish_time;
alter table biz_plan drop column plan_publish_userid;
alter table biz_plan drop column is_course_publish;
alter table biz_plan drop column course_publish_time;
alter table biz_plan drop column course_publish_userid;
alter table biz_plan drop column status;
alter table biz_plan drop column file_name;
alter table biz_plan drop column file_url;
alter table biz_plan drop column start_time;
alter table biz_plan drop column end_time;
alter table biz_plan drop column is_changed;
alter table biz_plan drop column organizer;


alter table biz_plan_detail drop column class_count;
alter table biz_plan_detail drop column fee_tpl_id;
alter table biz_plan_detail drop column fee;
alter table biz_plan_detail drop column is_classed;
alter table biz_plan_detail drop column target;


alter table biz_xm add is_course_publish NVARCHAR2(1);
comment on column biz_xm.is_course_publish is '课程设置是否已经发布 1 是  0 否';
alter table biz_xm add course_publish_time DATE;
comment on column biz_xm.course_publish_time is '课程设置发布时间';
alter table biz_xm add course_publish_userid NVARCHAR2(64);
comment on column biz_xm.course_publish_userid is '课程设置发布用户ID';

--培训计划详情增加行业、工种、等级
alter table biz_plan_detail add industry_id NVARCHAR2(64);
comment on column biz_plan_detail.industry_id is '行业';

alter table biz_plan_detail add profession_id NVARCHAR2(64);
comment on column biz_plan_detail.profession_id is '工种';

alter table biz_plan_detail add tech_level NVARCHAR2(64);
comment on column biz_plan_detail.tech_level is '等级';


alter table crp_teacher add mobile NVARCHAR2(20);
comment on column crp_teacher.mobile is '移动电话';

alter table biz_bm add zyjd_bm_id NVARCHAR2(64);
comment on column biz_bm.zyjd_bm_id is '职业鉴定报名表ID';


alter table sys_student add is_crp_user NVARCHAR2(1);
comment on column sys_student.is_crp_user is '是否是CRP用户';

alter table sys_student_info add certi_type NVARCHAR2(64);
comment on column sys_student_info.certi_type is '证件类型';

alter table sys_student_info add edu_certi_number NVARCHAR2(100);
comment on column sys_student_info.edu_certi_number is '学历证书编号';

alter table sys_student_info add certi_address NVARCHAR2(300);
comment on column sys_student_info.certi_address is '籍贯地址';


update biz_zyjd_bmbatch b set b.is_social = 'social' where b.is_social = 1;
alter table biz_zyjd_bmbatch rename column is_social to form_type;
comment on column biz_zyjd_bmbatch.form_type is '采集信息表类型';


alter table biz_zyjd_bm add certi_post NVARCHAR2(64);
comment on column biz_zyjd_bm.certi_post is '证书领取方式 1 自取  2 快递到付';

alter table biz_zyjd_bm add old_certi_profession NVARCHAR2(500);
comment on column biz_zyjd_bm.old_certi_profession is '原证书职业';

alter table biz_zyjd_bm add condition_id NVARCHAR2(64);
comment on column biz_zyjd_bm.condition_id is '满足的报考条件';

-----批次表增加字段
alter table biz_zyjd_bmbatch add approve_status NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.approve_status is '批次的审核的状态';


alter table biz_zyjd_bmbatch add approve_user_id NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.approve_user_id is '审核操作用户ID';

alter table biz_zyjd_bmbatch add approve_time DATE;
comment on column biz_zyjd_bmbatch.approve_time is '审核时间';

alter table biz_zyjd_bmbatch add user_id NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.user_id is '上报用户ID';

alter table biz_zyjd_bmbatch add approve_advice NVARCHAR2(1000);
comment on column biz_zyjd_bmbatch.approve_advice is '审核意见';

--老的批次数据的处理
update biz_zyjd_bmbatch b set b.user_id = b.creator_id,b.approve_status = 'PASS';





alter table biz_zyjd_bmbatch add bank_id NVARCHAR2(300);
comment on column biz_zyjd_bmbatch.bank_id is '指定收款方';


alter table sys_bank add is_default NVARCHAR2(1);
comment on column sys_bank.is_default is '是否默认';

update sys_bank b set b.is_default = 1;

alter table biz_zyjd_bmbatch add years NVARCHAR2(10);
comment on column biz_zyjd_bmbatch.years is '年度';

alter table biz_plan_detail add dict_industry_id NVARCHAR2(10);
comment on column biz_plan_detail.dict_industry_id is '面向行业';

alter table biz_zyjd_bmbatch add plan_id NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.plan_id is '计划ID';


alter table biz_plan_detail add approve_user_id NVARCHAR2(64);
comment on column biz_plan_detail.approve_user_id is '立项表审核人';
alter table biz_plan_detail add approve_status NVARCHAR2(10);
comment on column biz_plan_detail.approve_status is '立项表审批结果';
alter table biz_plan_detail add approval_time Date;
comment on column biz_plan_detail.approval_time is '立项表审核时间';

--工种授权表
-- Create table
create table inf_profession_assign
(
  id            nvarchar2(64) not null,
  profession_id nvarchar2(64),
  host_org_id   nvarchar2(64)
)

-- add comments to the table
comment on table inf_profession_assign
  is '工种授权表';
-- add comments to the columns
comment on column inf_profession_assign.id
  is '主键';
comment on column inf_profession_assign.profession_id
  is '职业id';
comment on column inf_profession_assign.host_org_id
  is '主办单位id';

  alter table inf_profession_assign
  add constraint pk_inf_profession_assign primary key (id);


alter table sys_org add is_college NVARCHAR2(1);
comment on column sys_org.is_college is '是否是二级学院 1 是 0 否';

alter table inf_profession_assign add college_id NVARCHAR2(64);
comment on column inf_profession_assign.college_id is '学院id';

---修改培训计划详情表中的字段的名称
alter table biz_plan_detail rename column cooper_org_id to entrust_org_id;
alter table biz_plan_detail rename column organizer_id to receive_org_id;

---修改委托单位的名称
update sys_org g set g.org_type='ENTRUST_ORG' where g.org_type='COOPER_ORG';
update sys_user_2_role r set r.role='ENTRUST_ORG'  where r.role='COOPER_ORG';

---修改批次表
alter table biz_zyjd_bmbatch drop column bm_qrcode;
alter table biz_zyjd_bmbatch add college_id NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.college_id is '申报该批次的二级学院ID';

--国土老的数据的处理，将目前已经申报的批次的统一改为继续教育学院
update biz_zyjd_bmbatch b set b.college_id='hbgtzyzyxy10749' where b.host_org_id='hbgtzyzyxy';


alter table biz_plan_detail add applyed_profession NCLOB;
comment on column biz_plan_detail.applyed_profession is '申报职业信息';

alter table biz_plan_detail add course_setting NCLOB;
comment on column biz_plan_detail.course_setting is '课程设置信息';


alter table biz_plan_detail add eval_paper NVARCHAR2(500);
comment on column biz_plan_detail.eval_paper is '测评模板';

--培训计划表增加二级学院ID
alter table biz_plan add college_id NVARCHAR2(64);
comment on column biz_plan.college_id is '二级学院ID';

alter table biz_plan_detail add user_id NVARCHAR2(64);
comment on column biz_plan_detail.user_id is '申报用户ID';


alter table biz_xm_course_setting add industry_id NVARCHAR2(64);
comment on column biz_xm_course_setting.industry_id is '申报行业ID';
alter table biz_xm_course_setting add profession_id NVARCHAR2(64);
comment on column biz_xm_course_setting.profession_id is '申报工种ID';
alter table biz_xm_course_setting add tech_level NVARCHAR2(64);
comment on column biz_xm_course_setting.tech_level is '申报等级';


alter table biz_plan_detail add approve_advice NVARCHAR2(200);
comment on column biz_plan_detail.approve_advice is '立项表审批结果';

------删除职业鉴定报名表中的单位、学员、班级,删除之前记得备份
create table biz_zyjd_bm_0601 as select * from biz_zyjd_bm;
alter table biz_zyjd_bm drop column dw;
alter table biz_zyjd_bm drop column COLLEGE;
alter table biz_zyjd_bm drop column CLASSZ;
alter table biz_zyjd_bm rename column pat_status to pay_status;
alter table biz_zyjd_bm rename column reg_status to status;

----以下脚本是2022-06-02号的
alter table biz_exam_paper add profession_id NVARCHAR2(64);
comment on column biz_exam_paper.profession_id is '职业ID';

alter table biz_exam_paper add tech_level NVARCHAR2(64);
comment on column biz_exam_paper.tech_level is '申报等级';

---删除错题集中的试卷ID
alter table biz_error_question_collect drop column paper_id;

--职业鉴定报名表中增加业绩证明
alter table biz_zyjd_bm add performance NVARCHAR2(2000);
comment on column biz_zyjd_bm.performance is '业绩证明，可以传多张图片';
=======
--试卷表增加课时id
alter table BIZ_EXAM_PAPER add LESSON_ID varchar2(100);
comment on column BIZ_EXAM_PAPER.LESSON_ID is '课时id';

--项目课程设置表增加上课开始时间、上课结束时间、上课地点字段
alter table BIZ_XM_COURSE_SETTING add start_time date;
comment on column BIZ_XM_COURSE_SETTING.start_time is '上课开始时间';

alter table BIZ_XM_COURSE_SETTING add end_time date;
comment on column BIZ_XM_COURSE_SETTING.end_time is '上课结束时间';

alter table BIZ_XM_COURSE_SETTING add address NVARCHAR2(500);
comment on column BIZ_XM_COURSE_SETTING.address is '上课地点';


---创建触发器--删除学员
CREATE OR REPLACE TRIGGER trigger_del_sys_student
   BEFORE DELETE --指定触发时机为删除操作前触发
   ON sys_student
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
   --将删除前数据插入到日志记录表,以供监督使用。
  insert into del_sys_student
  (id, password, status, is_bind_mobile, avatar, org_id, student_type, reg_host_org_id, company, open_id, is_crp_user)
values
  (:old.id, :old.password, :old.status, :old.is_bind_mobile, :old.avatar, :old.org_id, :old.student_type, :old.reg_host_org_id, :old.company, :old.open_id, :old.is_crp_user);
END;

---创建触发器--删除学员信息
CREATE OR REPLACE TRIGGER trigger_del_sys_student_info
   BEFORE DELETE --指定触发时机为删除操作前触发
   ON sys_student_info
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
   --将删除前数据插入到日志记录表,以供监督使用。
   insert into del_sys_student_info
  (id, student_id, sfzh, name, gender, nation, political_type, family_type, mobile, certi_address, certi_province, certi_city, address, post_code, profession, education, is_graduated, graduate_num, graduate_school, school_time, sfzzm, sfzfm, student_num, student_photo, create_time, zw, zc, specialty, graduate_time, ksly, phone, email, qq, wxh, birthday, current_tech_level, gw, gz, xtxz, dzrq, pymb, remark, teacher_name, teacher_gw, teacher_certino, invoice_title, invoice_code, invoice_bank, invoice_bank_account, invoice_org_address, invoice_org_phone, college, classz, company_province_code, company_city_code, company_district_code, edu_certi_photo, student_ticket, certi_type, edu_certi_number)
values
  (:old.id, :old.student_id, :old.sfzh, :old.name, :old.gender, :old.nation, :old.political_type, :old.family_type, :old.mobile, :old.certi_address, :old.certi_province, :old.certi_city, :old.address, :old.post_code, :old.profession, :old.education, :old.is_graduated, :old.graduate_num, :old.graduate_school, :old.school_time, :old.sfzzm, :old.sfzfm, :old.student_num, :old.student_photo, :old.create_time, :old.zw, :old.zc, :old.specialty, :old.graduate_time, :old.ksly, :old.phone, :old.email, :old.qq, :old.wxh, :old.birthday, :old.current_tech_level, :old.gw, :old.gz, :old.xtxz, :old.dzrq, :old.pymb, :old.remark, :old.teacher_name, :old.teacher_gw, :old.teacher_certino, :old.invoice_title, :old.invoice_code, :old.invoice_bank, :old.invoice_bank_account, :old.invoice_org_address, :old.invoice_org_phone, :old.college, :old.classz, :old.company_province_code, :old.company_city_code, :old.company_district_code, :old.edu_certi_photo, :old.student_ticket, :old.certi_type, :old.edu_certi_number);
END;

---创建触发器--删除职业鉴定报名信息
CREATE OR REPLACE TRIGGER trigger_del_biz_zyjd_bm
   BEFORE DELETE --指定触发时机为删除操作前触发
   ON biz_zyjd_bm
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
	insert into del_biz_zyjd_bm(id, ndm, student_id, ksly, work_time, profession, old_tech_level, old_tech_level_time, old_tech_certi_num, industry_id, profession_id, direction_id, apply_tech_level, work_years, save_time, submit_time, status, pay_status, approve_time, approve_advice, approve_user_id, pay_type, bmbatch_id, old_certi_photo, amount, mark_payd_user_id, remark, job_years, profession_years, current_job_time, test_way, old_certi_name, zkz, zkzno_create_time, current_job, full_education, full_specialty, full_edu_time, job_education, job_specialty, job_edu_time, article_tital, is_article_publish, ddzz, fbqk, nd, qh, article_photo_url, zyjsrzzgzs, yqrbmb, xrzyjszwpw, bmlx, pxfs, gznxzm, invoice_title, taxpayer_number, training_class, recommend_code, commitment, new_certificate, certi_post, old_certi_profession, condition_id)
values
  (:old.id, :old.ndm, :old.student_id, :old.ksly, :old.work_time, :old.profession, :old.old_tech_level, :old.old_tech_level_time, :old.old_tech_certi_num, :old.industry_id, :old.profession_id, :old.direction_id, :old.apply_tech_level, :old.work_years, :old.save_time, :old.submit_time, :old.status, :old.pay_status, :old.approve_time, :old.approve_advice, :old.approve_user_id, :old.pay_type, :old.bmbatch_id, :old.old_certi_photo, :old.amount, :old.mark_payd_user_id, :old.remark, :old.job_years, :old.profession_years, :old.current_job_time, :old.test_way, :old.old_certi_name, :old.zkz, :old.zkzno_create_time, :old.current_job, :old.full_education, :old.full_specialty, :old.full_edu_time, :old.job_education, :old.job_specialty, :old.job_edu_time, :old.article_tital, :old.is_article_publish, :old.ddzz, :old.fbqk, :old.nd, :old.qh, :old.article_photo_url, :old.zyjsrzzgzs, :old.yqrbmb, :old.xrzyjszwpw, :old.bmlx, :old.pxfs, :old.gznxzm, :old.invoice_title, :old.taxpayer_number, :old.training_class, :old.recommend_code, :old.commitment, :old.new_certificate, :old.certi_post, :old.old_certi_profession, :old.condition_id);
END;

--删除项目课程设置表中的上课日期、上课时间 董小康
alter table biz_xm_course_setting drop column course_date;
alter table biz_xm_course_setting drop column course_time;

--表BIZ_PLAN_DETAIL增加字段 董小康2022-6-9
alter table BIZ_PLAN_DETAIL add is_free_public NVARCHAR2(1);
comment on column BIZ_PLAN_DETAIL.is_free_public is '是否为免费公益性项目';

alter table BIZ_PLAN_DETAIL add is_gov_subside NVARCHAR2(1);
comment on column BIZ_PLAN_DETAIL.is_gov_subside is '是否为政府补贴性项目';

alter table BIZ_PLAN_DETAIL add entrust_org_nature NVARCHAR2(100);
comment on column BIZ_PLAN_DETAIL.entrust_org_nature is '甲方类型';

alter table BIZ_PLAN_DETAIL add contract_amount NUMBER(10,2);
comment on column BIZ_PLAN_DETAIL.contract_amount is '合同金额';

alter table BIZ_PLAN_DETAIL add contract_sign_time DATE;
comment on column BIZ_PLAN_DETAIL.contract_sign_time is '合同签署时间';

alter table BIZ_PLAN_DETAIL add contract_finish_time DATE;
comment on column BIZ_PLAN_DETAIL.contract_finish_time is '合同完成时间';

alter table BIZ_PLAN_DETAIL add gov_amount NUMBER(10,2);
comment on column BIZ_PLAN_DETAIL.gov_amount is '财政资金金额';

alter table BIZ_PLAN_DETAIL add not_gov_amount NUMBER(10,2);
comment on column BIZ_PLAN_DETAIL.not_gov_amount is '非财政资金金额';

alter table BIZ_PLAN_DETAIL add online_hours NUMBER;
comment on column BIZ_PLAN_DETAIL.online_hours is '线上学时';

alter table BIZ_PLAN_DETAIL add off_line_hours NUMBER;
comment on column BIZ_PLAN_DETAIL.off_line_hours is '线下学时';

alter table BIZ_PLAN_DETAIL add count_data NCLOB;
comment on column BIZ_PLAN_DETAIL.count_data is '培训对象人数';

alter table BIZ_PLAN_DETAIL add school_teacher_count NUMBER;
comment on column BIZ_PLAN_DETAIL.school_teacher_count is '校内教师人数';

alter table BIZ_PLAN_DETAIL add school_teacher_name NVARCHAR2(100);
comment on column BIZ_PLAN_DETAIL.school_teacher_name is '校内教师姓名';

alter table BIZ_PLAN_DETAIL add outer_teacher_count NUMBER;
comment on column BIZ_PLAN_DETAIL.outer_teacher_count is '外聘教师人数';

alter table BIZ_PLAN_DETAIL add outer_teacher_name NVARCHAR2(100);
comment on column BIZ_PLAN_DETAIL.outer_teacher_name is '外聘教师姓名';

alter table BIZ_PLAN_DETAIL add is_typical NVARCHAR2(1);
comment on column BIZ_PLAN_DETAIL.is_typical is '是否为典型特色项目';

alter table BIZ_PLAN_DETAIL add typical_remark NVARCHAR2(100);
comment on column BIZ_PLAN_DETAIL.typical_remark is '典型特色项目备注';

alter table BIZ_PLAN_DETAIL add total_cost NUMBER(10,2);
comment on column BIZ_PLAN_DETAIL.total_cost is '总支出';

alter table INF_TYPE add category NVARCHAR2(100);
comment on column INF_TYPE.category is '分类';

---项目库、课程库的数据库脚
create table INF_XM
(
  id               NVARCHAR2(64),
  name             NVARCHAR2(64),
  target           NVARCHAR2(500),
  training_form    NVARCHAR2(100),
  trainees         NVARCHAR2(100),
  pyfa             NVARCHAR2(500),
  hours            NUMBER,
  leader_id        NVARCHAR2(64),
  receive_org_id   NVARCHAR2(64),
  creator_id       NVARCHAR2(64),
  create_time      DATE,
  receive_org_name VARCHAR2(255),
  leader_name      VARCHAR2(255),
  host_org_id      NVARCHAR2(64)
);
-- Add comments to the columns
comment on column INF_XM.id
  is '主键id';
comment on column INF_XM.name
  is '项目名称';
comment on column INF_XM.target
  is '培训目标';
comment on column INF_XM.training_form
  is '培训方式';
comment on column INF_XM.trainees
  is '培训对象';
comment on column INF_XM.pyfa
  is '培训实施方案';
comment on column INF_XM.hours
  is '学时';
comment on column INF_XM.leader_id
  is '项目负责人id';
comment on column INF_XM.receive_org_id
  is '承办单位id';
comment on column INF_XM.creator_id
  is '创建人';
comment on column INF_XM.create_time
  is '创建时间';
comment on column INF_XM.receive_org_name
  is '负责人名称';
comment on column INF_XM.leader_name
  is '承办单位名称';
comment on column INF_XM.host_org_id
  is '主办单位Id';

 alter table INF_XM
 add constraint pk_inf_xm primary key (id);

  create table INF_XM_2_TYPE
  (
    id      NVARCHAR2(64) not null,
    xm_id   NVARCHAR2(64),
    type_id NVARCHAR2(500)
  );
  comment on column INF_XM_2_TYPE.id
    is '主键id';
  comment on column INF_XM_2_TYPE.xm_id
    is '项目类的id';
  comment on column INF_XM_2_TYPE.type_id
    is '分类id';
  alter table INF_XM_2_TYPE
  add constraint pk_inf_xm_2_type primary key (id);

create table INF_XM_2_COURSE
(
  id          NVARCHAR2(64) not null,
  xm_id       NVARCHAR2(64),
  course_id   NVARCHAR2(64),
  creator_id  NVARCHAR2(64),
  create_time DATE
);
-- Add comments to the columns
comment on column INF_XM_2_COURSE.id
  is '主键id';
comment on column INF_XM_2_COURSE.xm_id
  is '项目id';
comment on column INF_XM_2_COURSE.course_id
  is '课程id';
comment on column INF_XM_2_COURSE.creator_id
  is '创建用户id';
comment on column INF_XM_2_COURSE.create_time
  is '创建时间';
  alter table INF_XM_2_COURSE
  add constraint pk_inf_xm_2_course primary key (id);

 create table INF_PACKAGE
 (
   id          NVARCHAR2(64) not null,
   name        NVARCHAR2(100),
   remark      NVARCHAR2(100),
   tags        NVARCHAR2(100),
   creator_id  NVARCHAR2(64),
   create_time DATE,
   host_org_id NVARCHAR2(64)
 );
 comment on column INF_PACKAGE.id
   is '主键id';
 comment on column INF_PACKAGE.name
   is '资源包名称';
 comment on column INF_PACKAGE.remark
   is '简介';
 comment on column INF_PACKAGE.tags
   is '标签,多个标签用,隔开';
 comment on column INF_PACKAGE.creator_id
   is '创建人';
 comment on column INF_PACKAGE.create_time
   is '创建时间';
 comment on column INF_PACKAGE.host_org_id
   is '主办单位Id';

  alter table INF_PACKAGE
  add constraint pk_inf_package primary key (id);

  create table INF_MATERIAL
   (
     id          NVARCHAR2(64) not null,
     name        NVARCHAR2(64),
     type        NVARCHAR2(64),
     url         NVARCHAR2(200),
     duration    NUMBER,
     tags        NVARCHAR2(200),
     remark      NVARCHAR2(200),
     creator_id  NVARCHAR2(64),
     create_time DATE,
     host_org_id NVARCHAR2(64)
   );
   -- Add comments to the columns
   comment on column INF_MATERIAL.id
     is '主键id';
   comment on column INF_MATERIAL.name
     is '知识点';
   comment on column INF_MATERIAL.type
     is '类型';
   comment on column INF_MATERIAL.url
     is '资源地址';
   comment on column INF_MATERIAL.duration
     is '时长 单位分';
   comment on column INF_MATERIAL.tags
     is '标签';
   comment on column INF_MATERIAL.remark
     is '备注';
   comment on column INF_MATERIAL.creator_id
     is '创建人';
   comment on column INF_MATERIAL.create_time
     is '创建时间';
   comment on column INF_MATERIAL.host_org_id
     is '主办单位Id';
	alter table INF_MATERIAL
	add constraint pk_inf_material primary key (id);



  create table INF_MATERIAL_2_PACKAGE
  (
    id          NVARCHAR2(64) not null,
    package_id  NVARCHAR2(64),
    material_id NVARCHAR2(500)
  );
  comment on column INF_MATERIAL_2_PACKAGE.id
    is '主键id';
  comment on column INF_MATERIAL_2_PACKAGE.package_id
    is '资源包id';
  comment on column INF_MATERIAL_2_PACKAGE.material_id
    is '素材id';
	alter table INF_MATERIAL_2_PACKAGE
	add constraint PK_INF_MATERIAL_2_PACKAGE primary key (id);

------创建卷库表
create table biz_paper_repo
(
  id              NVARCHAR2(50) primary key not null,
  name            NVARCHAR2(100),
  total_score     NUMBER(11),
  passed_score    NUMBER(11),
  data            NCLOB,
  category        NVARCHAR2(64),
  db_id           NVARCHAR2(50),
  host_org_id	  NVARCHAR2(64),
  creator_id	  NVARCHAR2(64),
  create_time	  DATE
);
comment on table biz_paper_repo
  is '卷库表';
comment on column biz_paper_repo.id
  is '主键id';
comment on column biz_paper_repo.name
  is '名称';
comment on column biz_paper_repo.total_score
  is '总分';
comment on column biz_paper_repo.passed_score
  is '及格分';
comment on column biz_paper_repo.data
  is '试卷内容xml';
comment on column biz_paper_repo.category
  is '类型：PSZY(平时练习)、ZHCY（阶段测验）、ZJKH（终极考核）';
comment on column biz_paper_repo.db_id
  is '题库id';
comment on column biz_paper_repo.host_org_id
  is '主办单位id';
comment on column biz_paper_repo.creator_id
  is '创建用户id';
comment on column biz_paper_repo.create_time
  is '创建时间';

 --删除机构表中的是否是二级学院的字段
 alter table sys_org drop column is_college;
 --删除培训计划表中的二级学院id 字段
 alter table biz_plan drop column college_id;
 --删除报名批次表的二级学院id 字段
 alter table biz_plan drop column college_id;
 --更改技能授权表中的二级学院ID为部门id
 alter table inf_profession_assign rename column college_id to receive_org_id;
 comment on column inf_profession_assign.college_id
   is '院系(部门)ID';

 update sys_user_2_role r set r.role = 'DEPARTMENT' where r.role='COLLEGE';
 update sys_role_2_privilege r2p set r2p.role = 'DEPARTMENT' where r2p.role='COLLEGE';

 --2022-07-22
 alter table biz_bm rename column mark_payed_reason to remark;
  comment on column biz_bm.remark
   is '备注';
 alter table biz_bm_history rename column mark_payed_reason to remark;
   comment on column biz_bm_history.remark
   is '备注';
 alter table biz_bm drop column mark_refund_reason;
 alter table biz_bm_history add mark_refund_user_id NVARCHAR2(64);
 alter table biz_bm_history add refund_amount NUMBER;

----2022 -07 -03
alter table biz_zyjd_bm add healthy_code NVARCHAR2(500);
comment on column biz_zyjd_bm.healthy_code is '健康码';


alter table biz_zyjd_bm add trip_card NVARCHAR2(500);
comment on column biz_zyjd_bm.trip_card is '行程卡';


create table biz_zyjd_bm_0725 as select * from biz_zyjd_bm;
alter table biz_zyjd_bm drop column GED;


create table biz_zb_0725 as select * from biz_zb;
alter table biz_zb drop column ljgkrc;


--把 inf_course 表中的 is_public 字段移到 inf_course_live 中   赵小飞 2022-8-18
alter table inf_course drop column is_public;

-------公开课优化
--数据处理
update inf_course_live cl set cl.is_public = 1 where cl.course_id in(
select c.id from inf_course c where c.is_public = 1
);
alter table inf_course_live add is_public NVARCHAR2(1);
comment on column inf_course_live.is_public is '是否是公开课 1 是 0 否';
--删除 inf_course 表中的 公开课授课开始时间,公开课授课结束时间,公开课授课形式 1 直播  2课件  字段 赵小飞 2022-8-18
--公开课授课开始时间
alter table inf_course drop column teach_start_time;
--公开课授课结束时间
alter table inf_course drop column teach_end_time;
--公开课授课形式 1 直播  2课件
alter table inf_course drop column teach_type;

--删除消息跟机构的关系表,消息跟角色的关系表
drop table sys_message_2_org;
drop table sys_message_2_role;
drop table sys_message_received;
alter table sys_message_2_user add read_time DATE;
comment on column sys_message_2_user.read_time is '读取时间';

alter table sys_message drop column is_sys_created;
----------------------以上脚本已经更新到线上环境--------------------------------


--在系统公告表中添加接收对象receiver字段 2022-8-23 赵小飞
alter table sys_notice add receiver NVARCHAR2(20);
comment on column sys_notice.receiver is '接收对象("ALL"不限 "ADMIN"仅管理员 "STUDENT"仅学员)';

alter table biz_zb add course_live_id NVARCHAR2(64);
comment on column biz_zb.course_live_id is '所属直播课ID';

update biz_zb zb set zb.course_live_id = (select cl.id from inf_course_live cl where cl.course_id = zb.kc_id)
where exists(select 1 from inf_course_live cl where cl.course_id = zb.kc_id)
----------------------以上脚本已经更新到线上环境--------------------------------


alter table biz_xm add host_org_id NVARCHAR2(64);
comment on column biz_xm.host_org_id is '主办单位id';
update biz_xm xm set xm.host_org_id = (select host_org_id from biz_plan p where p.plan_id=xm.plan_id)
where exists(select host_org_id from biz_plan p where p.plan_id=xm.plan_id);

-------------------------------------------------------------------------------
alter table biz_plan add bmbatch_id NVARCHAR2(64);
comment on column biz_plan.bmbatch_id is '职业技能报名批次ID';


update biz_plan p set p.bmbatch_id = (select id from biz_zyjd_bmbatch b where b.plan_id = p.id)
where exists(select id from biz_zyjd_bmbatch b where b.plan_id = p.id) and p.is_skill = 1;


alter table biz_zyjd_bmbatch drop column plan_id;
----------已更新-------

alter table sys_student_info add resume NCLOB;
comment on column sys_student_info.resume is '工作经历';

alter table inf_profession add category NVARCHAR2(64);
comment on column inf_profession.category is '所属职业'


alter table biz_plan_detail drop column applyed_profession;
alter table biz_plan_detail drop column course_setting;


-- 技能证书表 赵小飞 2022-09-27
create table BIZ_STUDENT_SKILL_CERTI
(
    id                 NVARCHAR2(64)    not null,
    student_name       NVARCHAR2(64),
    sfzh               NVARCHAR2(64),
    company            NVARCHAR2(64),
    gender             NVARCHAR2(1),
    mobile             NVARCHAR2(64),
    certi_no           NVARCHAR2(64),
    certi_name         NVARCHAR2(64),
    certi_effect_date  NVARCHAR2(64),
    profession_id      NVARCHAR2(64),
    tech_level         NVARCHAR2(64),
    create_time        DATE,
    ceator_id          NVARCHAR2(64)
);

comment on column BIZ_STUDENT_SKILL_CERTI.id is
'主键';
comment on column BIZ_STUDENT_SKILL_CERTI.student_name is
'姓名';
comment on column BIZ_STUDENT_SKILL_CERTI.company is
'工作单位';
comment on column BIZ_STUDENT_SKILL_CERTI.mobile is
'手机号';
comment on column BIZ_STUDENT_SKILL_CERTI.sfzh is
'身份证号';
comment on column BIZ_STUDENT_SKILL_CERTI.gender is
'性别';
comment on column BIZ_STUDENT_SKILL_CERTI.certi_no is
'证书编号';
comment on column BIZ_STUDENT_SKILL_CERTI.certi_effect_date is
'证书有效期';
comment on column BIZ_STUDENT_SKILL_CERTI.certi_name is
'证书名称';
comment on column BIZ_STUDENT_SKILL_CERTI.profession_id is
'职业(工种)ID';
comment on column BIZ_STUDENT_SKILL_CERTI.tech_level is
'技能等级';
comment on column BIZ_STUDENT_SKILL_CERTI.create_time is
'创建时间';
comment on column BIZ_STUDENT_SKILL_CERTI.ceator_id is
'创建人';
-----------------以上脚本已经更新到线上服务器-------
alter table biz_student_bm_course add is_payed NVARCHAR2(1);
comment on column biz_student_bm_course.is_payed is '是否已经支付，1是 0 否';


alter table biz_student_skill_certi add host_org_id NVARCHAR2(64);
comment on column biz_student_skill_certi.host_org_id is '所属机构';


--报名咨询表 刘喜 sql
CREATE TABLE BIZ_XM_BM_ADVICE (
  ID NVARCHAR2(64) NOT NULL,
  XM_ID NVARCHAR2(64),
  NAME NVARCHAR2(64),
  MOBILE NVARCHAR2(64),
  CONTENT NVARCHAR2(2000),
  TIME DATE
);
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.NAME IS '报名咨询申请人姓名';
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.MOBILE IS '报名咨询申请人电话号码';
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.CONTENT IS '报名咨询内容';
COMMENT ON COLUMN BIZ_XM_BM_ADVICE.TIME IS '创建时间';
COMMENT ON TABLE BIZ_XM_BM_ADVICE IS '报名咨询表';


alter table sys_notice add third_party_id NVARCHAR2(100);

-------------以上脚本已经更新发布到线上服务器-------------

--2022.11.16 直播表增加是否是试播课程字段  赵小飞
alter table inf_course_live add is_test NVARCHAR2(1);
comment on column inf_course_live.is_test is '是否是试播课程';

--删除是否开放个人报名的字段
alter table inf_course drop column is_open_person_bm;


--加入合同URL字段 李焱兵
--alter table biz_plan_detail add contract_url NVARCHAR2(500);
--comment on column biz_plan_detail.contract_url is '合同url';

alter table biz_plan_detail add contract_user_id NVARCHAR2(64);
comment on column biz_plan_detail.contract_user_id is '合同审核人';

alter table biz_plan_detail add contract_status NVARCHAR2(10);
comment on column biz_plan_detail.contract_status is '合同审批结果';

alter table biz_plan_detail add contract_time DATE;
comment on column biz_plan_detail.contract_time is '合同审核时间';

alter table biz_plan_detail add contract_advice NVARCHAR2(200);
comment on column biz_plan_detail.contract_advice is '合同审核意见';


alter table biz_zyjd_bm add mark_refund_user_id NVARCHAR2(64);
comment on column biz_zyjd_bm.mark_refund_user_id is '标记退费用户ID';

alter table biz_zyjd_bm add refund_amount NVARCHAR2(64);
comment on column biz_zyjd_bm.refund_amount is '退费金额';


alter table biz_exam_paper add  choose_tag NVARCHAR2(64);
comment on column biz_exam_paper.choose_tag is '选考标记，选考标记相同的试卷考生随机抽取一份作答';

---以上脚本已经发布到线上环境------

alter table biz_exam_paper drop column type;

alter table biz_exam_paper add db_id NVARCHAR2(64);
comment on column biz_exam_paper.db_id is '题库ID';

update biz_exam_paper p set p.category = 'PSZY' where p.category = 'ZHCY';

alter table sys_student_info add instructor NVARCHAR2(100);
comment on column sys_student_info.instructor is '辅导员';

---以上脚本已经发布到线上环境------

--userinfo表新增字段 用于师资功能 2022-12-13 赵小飞
alter table sys_user_info add recommend NVARCHAR2(64);
comment on column sys_user_info.recommend is '推荐指数';

alter table sys_user_info add custom_one NVARCHAR2(500);
comment on column sys_user_info.custom_one is '自定义字段1';

alter table sys_user_info add custom_two NVARCHAR2(500);
comment on column sys_user_info.custom_two is '自定义字段2';

alter table sys_user_info add custom_three NVARCHAR2(500);
comment on column sys_user_info.custom_three is '自定义字段3';

alter table sys_user_info add is_show_portal NVARCHAR2(1);
comment on column sys_user_info.is_show_portal is '是否在门户上展示';

alter table sys_user_info add email NVARCHAR2(100);
comment on column sys_user_info.email is '邮箱';

alter table sys_user_info add zc_photo NVARCHAR2(200);
comment on column sys_user_info.zc_photo is '职称证书扫描件';

alter table sys_user_info add bank_card_photo NVARCHAR2(200);
comment on column sys_user_info.bank_card_photo is '银行卡扫描件';

alter table sys_user add type_id NVARCHAR2(64);
comment on column sys_user.type_id is '类型id';


alter table inf_course_live add is_public_form NVARCHAR2(1);
comment on column inf_course_live.is_public_form is '公开课是否需要采集学员信息 1是  0 否';

update inf_course_live cl set cl.is_public_form = 1;

--修改字段类型 赵小飞 2022-12-16
alter table sys_user_info modify recommend integer;

--修改字段名称和位置
alter table sys_user drop column type_id;

alter table sys_user_info add teacher_type_id NVARCHAR2(64);
comment on column sys_user.type_id is '类型id';

--归档审批表 2022-11-4 赵小飞
create table BIZ_ARCHIVE_APPLY
(
    id                 NVARCHAR2(64)    not null,
    xm_id              NVARCHAR2(64),
    apply_user_id      NVARCHAR2(64),
    apply_time         DATE,
    status             NVARCHAR2(64),
    approve_item_id    NVARCHAR2(64)
);
comment on column BIZ_ARCHIVE_APPLY.id is
'主键';
comment on column BIZ_ARCHIVE_APPLY.xm_id is
'归档项目id';
comment on column BIZ_ARCHIVE_APPLY.apply_user_id is
'申请用户id';
comment on column BIZ_ARCHIVE_APPLY.apply_time is
'申请归档时间';
comment on column BIZ_ARCHIVE_APPLY.status is
'项目归档状态';
comment on column BIZ_ARCHIVE_APPLY.approve_item_id is
'审批事项id';

-- 修改日志表增加主办单位ID
alter table sys_log add host_org_id NVARCHAR2(64);
comment on column sys_log.host_org_id is '主办单位ID';


--归档表字段长度更改
alter table biz_plan_detail modify typical_remark NVARCHAR2(500);

--项目详情增加项目结余字段
alter table biz_plan_detail add surplus NUMBER(10,2);
comment on column biz_plan_detail.surplus is '项目结余';


--试题表增加标签字段
alter table BIZ_QUESTION add TAGS NVARCHAR2(200);
comment on column BIZ_QUESTION.TAGS is '标签';

--素材表增加字段
alter table INF_MATERIAL add language_points NVARCHAR2(1000);
comment on column INF_MATERIAL.language_points is '识点介绍';

alter table INF_MATERIAL add explain_incisively INTEGER;
comment on column INF_MATERIAL.explain_incisively is '是否精讲';

alter table INF_MATERIAL add teacher NVARCHAR2(200);
comment on column INF_MATERIAL.teacher is '老师';


--素材与试题表
create table INF_MATERIAL_QUESTION
(
    ID                   NVARCHAR2(64)        not null,
    MATERIAL_ID          NVARCHAR2(64)        not null,
    QUESTION_ID          NVARCHAR2(64)        not null
);

alter table INF_MATERIAL_QUESTION
    add constraint PK_INF_MATERIAL_QUESTION primary key (ID, MATERIAL_ID, QUESTION_ID);


--课时与试题表
create table INF_LESSON_QUESTION
(
    id                 NVARCHAR2(64)        not null,
    kj_id              NVARCHAR2(64)        not null,
    lesson_id          NVARCHAR2(64)        not null,
    question_id        NVARCHAR2(64)        not null
);

alter table INF_LESSON_QUESTION
    add constraint PK_INF_LESSON_QUESTION primary key (id, kj_id, lesson_id, question_id);

alter table inf_material_question add source NVARCHAR2(64);
comment on column inf_material_question.source is '素材试题来源';

alter table INF_LESSON_QUESTION add source NVARCHAR2(64);
comment on column INF_LESSON_QUESTION.source is '课件试题来源';


alter table sys_student_info add org_address NVARCHAR2(200);
comment on column sys_student_info.org_address is '单位地址';


alter table biz_bm add invoice_amount NUMBER(10,2);
comment on column biz_bm.invoice_amount is '开票金额';

--saas服务优化，机构表增加字段  赵小飞 2023-02-06
alter table sys_org add admin_logo NVARCHAR2(200);
comment on column sys_org.admin_logo is '管理端logo';

alter table sys_org add portal_logo NVARCHAR2(200);
comment on column sys_org.portal_logo is '门户logo';

alter table sys_org add ios_appid NVARCHAR2(200);
comment on column sys_org.ios_appid is '苹果appid';

--更正表名 赵小飞 2023-02-07
alter table biz_student_leanring_score rename to biz_student_learning_score;
alter table biz_examdata rename to biz_exam_data;

------以上代码已经发布到线上环境 2023-02-13 -------

---问卷作答表增加项目ID
alter table biz_qa_answer add xm_id nvarchar2(64);
comment on column BIZ_QA_ANSWER.xm_id is '培训项目ID';

--更正表名 赵小飞 2023-02-14
alter table biz_examdata_history rename to biz_exam_data_history
------以上脚本已经发布到线上数据库 2023-02-14 田军 发布------


alter table biz_zyjd_bmbatch add is_auto_next_batch nvarchar2(1);
comment on column biz_zyjd_bmbatch.is_auto_next_batch is '当前批次关闭时，是否智能匹配同类型下的其他开放的批次,1是 0 否';
------以上脚本已经发布到线上环境------


alter table biz_bm add certi_approve_user_id nvarchar2(64);
comment on column biz_bm.certi_approve_user_id is '证书审核操作用户ID';

alter table biz_bm rename column approve_result to certi_approve_result;
comment on column biz_bm.certi_approve_result is '证书审核结果';

alter table biz_bm rename column approve_reason to certi_approve_reason;
comment on column biz_bm.certi_approve_reason is '证书审核意见';
------以上脚本已经发布到线上环境------


alter table SYS_PORTAL_BANNER add terminal NVARCHAR2(50);
comment on column SYS_PORTAL_BANNER.terminal is '适配终端';

alter table SYS_PORTAL_BANNER add notice_id NVARCHAR2(64);
comment on column SYS_PORTAL_BANNER.notice_id is '资讯id';


alter table INF_TYPE add applet_sort INTEGER;
comment on column INF_TYPE.applet_sort is '小程序排序';


alter table SYS_NOTICE_CATEGORY add status NVARCHAR2(50);
comment on column SYS_NOTICE_CATEGORY.status is '状态(启用/禁用)';
------以上脚本已经发布到线上环境------

alter table biz_xm_course_setting add is_shared NVARCHAR2(5);
comment on column biz_xm_course_setting.is_shared is '是否是共享的直播课';




alter table BIZ_ZYJD_BM add is_rec NVARCHAR2(1);
comment on column BIZ_ZYJD_BM.is_rec is'是否已入账1 是 0 否';

alter table BIZ_ZYJD_BM add rec_code NVARCHAR2(64);
comment on column BIZ_ZYJD_BM.rec_code is '对账单编号';

--职业鉴定报名对账表
create table BIZ_ZYJD_BMREC
(
    id                 NVARCHAR2(64),
    code               NVARCHAR2(64),
    regist_num         INTEGER,
    total_amount       NUMBER,
    status             NVARCHAR2(20),
    time_quantum       NVARCHAR2(200),
    remark             NVARCHAR2(2000),
    create_time        DATE,
    creator_id         NVARCHAR2(64),
    update_time        DATE,
    host_org_Id        NVARCHAR2(64),
    updator_id         NVARCHAR2(64)

);

comment on column BIZ_ZYJD_BMREC.code is
'对账单编号';

comment on column BIZ_ZYJD_BMREC.regist_num is
'报名数';

comment on column BIZ_ZYJD_BMREC.total_amount is
'总金额';

comment on column BIZ_ZYJD_BMREC.status is
'状态';

comment on column BIZ_ZYJD_BMREC.time_quantum is
'对账时间段';

comment on column BIZ_ZYJD_BMREC.create_time is
'创建时间';

comment on column BIZ_ZYJD_BMREC.creator_id is
'创建用户id';

comment on column BIZ_ZYJD_BMREC.update_time is
'修改时间';

comment on column BIZ_ZYJD_BMREC.updator_id is
'修改用户id';

comment on column BIZ_ZYJD_BMREC.remark is
'备注';

alter table biz_zyjd_bmbatch add is_allow_multi_profession NVARCHAR2(1);
comment on column biz_zyjd_bmbatch.is_allow_multi_profession is '是否允许同考生同批次报名多个职业';

--增加微信小程序二维码字段
alter table sys_org add applet_qr_code NVARCHAR2(200);
comment on column sys_org.applet_qr_code is '微信小程序二维码';

--更改自定义表单属性
alter table biz_field rename column is_personal to is_constant;
comment on column biz_field.is_constant is '是否是系统预设';


alter table biz_exam_answer_card add question_id NVARCHAR2(64);
comment on column biz_exam_answer_card.question_id is '试题ID';


alter table biz_exam_answer_card add name NVARCHAR2(500);
comment on column biz_exam_answer_card.name is '附件的名称';

alter table inf_kj_video add label NVARCHAR2(500);
comment on column inf_kj_video.label is '标签';


alter table comm_sms_log modify (mobile NVARCHAR2(2000));

alter table sys_student_info modify certi_type NVARCHAR2(64) default 'SFZ';

update sys_student_info f set f.certi_type = 'SFZ' where f.certi_type = '0';

alter table biz_xm add is_open_study NVARCHAR2(1);
comment on column biz_xm.is_open_study  is '是否使用第三方学习平台 1是 0否';

alter table biz_xm add open_study_url NVARCHAR2(500);
comment on column biz_xm.open_study_url  is '第三方平台学习链接';



alter table biz_exam_paper add exam_model NVARCHAR2(20);
comment on column biz_exam_paper.exam_model  is '考试模式';

update biz_exam_paper set exam_model= 'SDSK';

alter table biz_exam_answer_card modify ip NVARCHAR2(100);
-----以上代码已经发布到线上 田军 2023-06-13-------


alter table sys_student_info add reg_host_org_id NVARCHAR2(64);
comment on column sys_student_info.reg_host_org_id is '主办单位ID';

alter table trigger_del_sys_student_info add reg_host_org_id NVARCHAR2(64);
comment on column trigger_del_sys_student_info.reg_host_org_id is '主办单位ID';

---更新触发器--删除学员信息
CREATE OR REPLACE TRIGGER trigger_del_sys_student_info
   BEFORE DELETE --指定触发时机为删除操作前触发
   ON sys_student_info
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
   insert into trigger_del_sys_student_info
  (id, student_id, sfzh, name, gender, nation, political_type, family_type, mobile, certi_address, certi_province, certi_city, address, post_code, profession, education, is_graduated, graduate_num, graduate_school, school_time, sfzzm, sfzfm, student_num, student_photo, create_time, zw, zc, specialty, graduate_time, ksly, phone, email, qq, wxh, birthday, current_tech_level, gw, gz, xtxz, dzrq, pymb, remark, teacher_name, teacher_gw, teacher_certino, invoice_title, invoice_code, invoice_bank, invoice_bank_account, invoice_org_address, invoice_org_phone, college, classz, company_province_code, company_city_code, company_district_code, edu_certi_photo, student_ticket, certi_type, edu_certi_number,reg_host_org_id)
values
  (:old.id, :old.student_id, :old.sfzh, :old.name, :old.gender, :old.nation, :old.political_type, :old.family_type, :old.mobile, :old.certi_address, :old.certi_province, :old.certi_city, :old.address, :old.post_code, :old.profession, :old.education, :old.is_graduated, :old.graduate_num, :old.graduate_school, :old.school_time, :old.sfzzm, :old.sfzfm, :old.student_num, :old.student_photo, :old.create_time, :old.zw, :old.zc, :old.specialty, :old.graduate_time, :old.ksly, :old.phone, :old.email, :old.qq, :old.wxh, :old.birthday, :old.current_tech_level, :old.gw, :old.gz, :old.xtxz, :old.dzrq, :old.pymb, :old.remark, :old.teacher_name, :old.teacher_gw, :old.teacher_certino, :old.invoice_title, :old.invoice_code, :old.invoice_bank, :old.invoice_bank_account, :old.invoice_org_address, :old.invoice_org_phone, :old.college, :old.classz, :old.company_province_code, :old.company_city_code, :old.company_district_code, :old.edu_certi_photo, :old.student_ticket, :old.certi_type, :old.edu_certi_number,:old.reg_host_org_id);
END;

---创建唯一性索引
create unique index MOBILE_UQ_SAME_HOST on sys_student_info (CASE  WHEN (MOBILE IS NOT NULL AND REG_HOST_ORG_ID IS NOT NULL) THEN MOBILE||REG_HOST_ORG_ID ELSE NULL END);
create unique index SFZH_UQ_SAME_HOST on sys_student_info (CASE  WHEN (SFZH IS NOT NULL AND REG_HOST_ORG_ID IS NOT NULL) THEN SFZH||REG_HOST_ORG_ID ELSE NULL END);

update sys_student_info f set f.reg_host_org_id = (select reg_host_org_id from sys_student s where s.id = f.student_id )
where exists (select 1 from sys_student s where s.id = f.student_id );
------以上代码已经 发布到线上环境-----

--课件表增加直播课id
alter table inf_kj add course_live_id NVARCHAR2(100);
comment on column inf_kj.course_live_id is '直播课id，可以用来判断是哪个直播课转成的课件';

alter table inf_kj_video add course_live_id NVARCHAR2(100);
comment on column inf_kj_video.course_live_id is '直播课id，可以用来判断是哪个直播课转成的课件';

--更改表名
ALTER TABLE inf_kj_comment RENAME TO biz_kj_comment;
ALTER TABLE inf_kj_note RENAME TO biz_kj_note;
ALTER TABLE inf_kj_target_result RENAME TO biz_kj_target_result;


--工种等级授权表
create table inf_profession_assign_detail
(
    id                 NVARCHAR2(64),
    assign_id          NVARCHAR2(64),
    tech_level         NVARCHAR2(64)
);
comment on column inf_profession_assign_detail.id is
'主键';
comment on column inf_profession_assign_detail.assign_id is
'工种授权表id';
comment on column inf_profession_assign_detail.tech_level is
'等级 枚举 ONE,TWO,THREE...';

--更新门户项目的查询视图
create or replace view v_xm as
select p.id||'-'||pad.tech_level||'-'||a.host_org_id as id,
       p.id profession_id,
       p.industry_id,
       pad.tech_level,
       g.name||'-'||p.name||'-'||decode(pad.tech_level,'ONE','一级','TWO','二级','THREE','三级','FOUR','四级','FIVE','五级','') title,
       null as grbm_start_time,
       0 amount,
       null as logo,
       null as notice,
       g.code||'-'||g.name as host_org_name,
       a.host_org_id,
       g.portal_domain,
       g.admin_domain
from inf_profession p
inner join inf_profession_assign a on a.profession_id = p.id
inner join inf_profession_assign_detail pad on pad.assign_id = a.id
inner join sys_org g on g.id = a.host_org_id
where pad.tech_level in('ONE','TWO','THREE','FOUR','FIVE')
order by a.host_org_id,p.id,decode(pad.tech_level,'ONE',1,'TWO',2,'THREE',3,'FOUR',4,'FIVE',5,0);

--素材表的资源地址长度更改
alter table INF_MATERIAL modify url NVARCHAR2(1000);

drop view view_xm_course_setting;
alter table biz_xm_course_setting drop column plan_id;
alter table biz_bm drop column tutor_id;
alter table trigger_del_biz_bm drop column tutor_id;
CREATE OR REPLACE TRIGGER trigger_del_biz_bm
  BEFORE DELETE --指定触发时机为删除操作前触发
 ON biz_bm
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
insert into trigger_del_biz_bm
  (id, xm_id, student_id, is_jtbm, jtbm_user_id, jtbm_org_id, status, time, update_time, updator_id, class_id, certi_approve_result, certi_approve_reason, approve_user_id, is_payed, pay_type, mark_payed_user_id, remark, payed_amount, is_applyed_invoice, invoice_applyed_time, is_mark_invoice, mark_refund_user_id, refund_amount, is_offline, approve_advice, zyjd_bm_id, invoice_amount, certi_approve_user_id)
values
  (:old.id, :old.xm_id, :old.student_id, :old.is_jtbm, :old.jtbm_user_id, :old.jtbm_org_id, :old.status, :old.time, :old.update_time, :old.updator_id, :old.class_id, :old.certi_approve_result, :old.certi_approve_reason, :old.approve_user_id, :old.is_payed, :old.pay_type, :old.mark_payed_user_id, :old.remark, :old.payed_amount, :old.is_applyed_invoice, :old.invoice_applyed_time, :old.is_mark_invoice, :old.mark_refund_user_id, :old.refund_amount, :old.is_offline, :old.approve_advice, :old.zyjd_bm_id, :old.invoice_amount, :old.certi_approve_user_id);
END;
---以上脚本已经发布

---删除鉴定费设置表的STRONG_AMOUNT
alter table inf_fee_setting drop column STRONG_AMOUNT;

--报名批次表增加培训费单独设置开关
alter table biz_zyjd_bmbatch add is_fee_separate NVARCHAR2(1);
comment on column biz_zyjd_bmbatch.is_fee_separate is '培训费、报名费是否分开缴纳';

-- --------2023年11月1日更新
alter table biz_exam_paper drop column db_id;


--题库练习表
create table BIZ_DB_PRACTICE
(
    id                   NVARCHAR2(64)        not null,
    xm_id                NVARCHAR2(64),
    db_id                NVARCHAR2(64)
);

comment on column BIZ_DB_PRACTICE.id is
'主键';

comment on column BIZ_DB_PRACTICE.xm_id is
'项目id';

comment on column BIZ_DB_PRACTICE.db_id is
'题库id';

alter table BIZ_DB_PRACTICE
    add constraint PK_BIZ_DB_PRACTICE primary key (id);

comment on table BIZ_DB_PRACTICE is '题库练习表'

--题库练习详情表
create table BIZ_DB_PRACTICE_DETAIL
(
    id                   NVARCHAR2(64)        not null,
    db_practice_id       NVARCHAR2(64),
    student_id           NVARCHAR2(64),
    question_id          NVARCHAR2(64),
    answer               NVARCHAR2(2000),
    create_time          DATE
);

comment on column BIZ_DB_PRACTICE_DETAIL.id is
'主键';

comment on column BIZ_DB_PRACTICE_DETAIL.db_practice_id is
'题库练习id';

comment on column BIZ_DB_PRACTICE_DETAIL.student_id is
'学员id';

comment on column BIZ_DB_PRACTICE_DETAIL.question_id is
'小题id';

comment on column BIZ_DB_PRACTICE_DETAIL.answer is
'学员答案';

comment on column BIZ_DB_PRACTICE_DETAIL.create_time is
'作答时间';

alter table BIZ_DB_PRACTICE_DETAIL
    add constraint PK_BIZ_DB_PRACTICE_DETAIL primary key (id);

comment on table BIZ_DB_PRACTICE_DETAIL is '题库练习详情表';

--视图名称规范化调整
--需要同步更改门户的视图，主要是线上的
--更新门户项目的查询视图 2023-11-22
create or replace view v_portal_xm as
select p.id||'-'||pad.tech_level||'-'||a.host_org_id as id,
       p.id profession_id,
       p.industry_id,
       pad.tech_level,
       g.name||'-'||p.name||'-'||decode(pad.tech_level,'ONE','一级','TWO','二级','THREE','三级','FOUR','四级','FIVE','五级','') title,
       null as grbm_start_time,
       0 amount,
       null as logo,
       null as notice,
       g.code||'-'||g.name as host_org_name,
       a.host_org_id,
       g.portal_domain,
       g.admin_domain
from inf_profession p
         inner join inf_profession_assign a on a.profession_id = p.id
         inner join inf_profession_assign_detail pad on pad.assign_id = a.id
         inner join sys_org g on g.id = a.host_org_id
where pad.tech_level in('ONE','TWO','THREE','FOUR','FIVE')
order by a.host_org_id,p.id,decode(pad.tech_level,'ONE',1,'TWO',2,'THREE',3,'FOUR',4,'FIVE',5,0);

create or replace view v_portal_type as
select
    id,
    name,
    parent_id,
    sort,
    create_time
from
    inf_type
where
    CATEGORY='XM' and host_org_id is null
order by sort asc;

create or replace view v_portal_student as
select
    t.id,
    ssi.sfzh,
    ssi.name,
    ssi.mobile,
    t.password,
    t.status
from sys_student t
left join sys_student_info ssi on t.id = ssi.student_id;

create view v_portal_qa_xm_data as
select
    qaa.id,
    qaa.qa_id,
    qaa.student_id,
    stuif.sfzh sfzh,
    stuif.name user_name,
    xm.id xm_id,
    xm.title,
    stu.org_id,
    org.code org_code,
    org.name org_name,
    org.org_type,
    qaa.status,
    qaa.content,
    nvl(qaa.submit_time, qaa.save_time) submit_time
from
    biz_qa_answer qaa
inner join sys_student stu on stu.id = qaa.student_id
inner join sys_student_info stuif on stu.id = stuif.student_id
inner join biz_bm bm on stu.id  = bm.student_id and bm.status = 'BMCG'
inner join biz_xm xm on xm.id  = bm.xm_id
inner join biz_xm_2_qa x2q on x2q.xm_id = xm.id and x2q.qa_id = qaa.qa_id
left join sys_org org on org.id = stu.org_id;

drop view v_xm;
drop view v_type;
drop view v_student;
drop view v_qa_xm_data;

alter table BIZ_DB_PRACTICE add profession_id NVARCHAR2(64);
comment on column BIZ_DB_PRACTICE.profession_id is '专业id';

alter table BIZ_DB_PRACTICE add tech_level NVARCHAR2(20);
comment on column BIZ_DB_PRACTICE.tech_level is '等级';
---以上脚本已经发布
alter table INF_PROFESSION_CONDITION add status NVARCHAR2(1);
comment on column INF_PROFESSION_CONDITION.status is '状态 0不可用，1可用';


--在线报名人数
ALTER TABLE biz_xm ADD ONLINE_BM_COUNT NUMBER;
comment on column biz_xm.ONLINE_BM_COUNT is '在线报名人数';

--课件表，增加是否为精品课件
alter table inf_kj_video add is_boutique NVARCHAR2(1);
comment on column inf_kj_video.is_boutique is '是否是精品课 1是 0否';
---以上脚本已经发布

--分类表增加状态字段 2024-01-25
alter table inf_type add status NVARCHAR2(20);
comment on column inf_type.status is '状态';

--报考条件增加是否需要学历证书字段 2024-0307
alter table inf_profession_condition add is_need_edu_certi NVARCHAR2(1);
comment on column inf_profession_condition.is_need_edu_certi is '是否需要学历证书 1是 0否';

--类型与二级管理员关系表 用于分类型审核项目申报信息
create table BIZ_TYPE_2_USER
(
    id                   NVARCHAR2(64)        not null,
    user_id              NVARCHAR2(64),
    type_id              NVARCHAR2(64)
);

comment on column BIZ_TYPE_2_USER.id is
    '主键';

comment on column BIZ_TYPE_2_USER.user_id is
    '二级管理员id';

comment on column BIZ_TYPE_2_USER.type_id is
    '类型id';

alter table BIZ_TYPE_2_USER
    add constraint PK_BIZ_TYPE_2_USER primary key (id);

comment on table BIZ_TYPE_2_USER is '类型与二级管理员关系表 用于分类型审核项目申报信息';

---技能认定批次表增加formId字段
ALTER TABLE biz_zyjd_bmbatch ADD FORM_ID NVARCHAR2(64);
comment on column biz_zyjd_bmbatch.FORM_ID is '信息采集表单ID';
--初始化formId,处理老的数据
update biz_zyjd_bmbatch b set b.form_id = 'standard' where b.form_type = 'standard';
update biz_zyjd_bmbatch b set b.form_id = (select g.code from sys_org g where g.id = b.host_org_id)||'_'|| b.form_type
where b.form_type != 'standard' and exists(select 1 from sys_org g where g.id = b.host_org_id) and b.form_type is not null;

--机构表增加字段是否是二级学院
alter table sys_org add is_college NVARCHAR2(1);
comment on column sys_org.is_college is '是否是二级学院  1是 0 否';
--2024-03-24 已上线

--工种表增加简介，缩略图，证书样表字段
alter table inf_profession add type_id NVARCHAR2(64);
comment on column inf_profession.type_id is '分类id';
alter table biz_zyjd_bmbatch drop column form_type;

alter table inf_profession add introduce NCLOB;
comment on column inf_profession.introduce is '职业介绍';

alter table inf_profession add sample NVARCHAR2(500);
comment on column inf_profession.sample is '职业证书样表';

alter table inf_profession add short_image NVARCHAR2(500);
comment on column inf_profession.short_image is '缩略图';

alter table inf_profession add bm_material NCLOB;
comment on column inf_profession.bm_material is '报名材料';

--项目评论表
create table BIZ_XM_EVALUATE
(
    id          NVARCHAR2(64) not null,
    xm_id       NVARCHAR2(64),
    user_id     NVARCHAR2(64),
    create_time DATE,
    content     NCLOB
);

comment on column BIZ_XM_EVALUATE.id is
    '主键';

comment on column BIZ_XM_EVALUATE.xm_id is
    '项目id';

comment on column BIZ_XM_EVALUATE.user_id is
    '用户id';

comment on column BIZ_XM_EVALUATE.create_time is
    '创建时间';

comment on column BIZ_XM_EVALUATE.content is
    '评论';
alter table BIZ_XM_EVALUATE add constraint PK_BIZ_XM_EVALUATE primary key (id);

comment on table BIZ_XM_EVALUATE is '项目评论表';

--公告表增加发布时间字段
alter table sys_notice add publish_time DATE;
comment on column sys_notice.publish_time is '发布时间';

--存储学员用户对直播课的评分
create table BIZ_COURSE_LIVE_TARGET_RESULT
(
    ID                  NVARCHAR2(64),
    COURSE_LIVE_ID      NVARCHAR2(64),
    STAR_COUNT          NUMBER,
    STUDENT_ID          NVARCHAR2(64),
    TIME                DATE
);
comment on column BIZ_COURSE_LIVE_TARGET_RESULT.ID is '主键id';

comment on column BIZ_COURSE_LIVE_TARGET_RESULT.COURSE_LIVE_ID is '直播课id';

comment on column BIZ_COURSE_LIVE_TARGET_RESULT.STAR_COUNT is '几颗星';

comment on column BIZ_COURSE_LIVE_TARGET_RESULT.STUDENT_ID is '评价学员用户id';

comment on column BIZ_COURSE_LIVE_TARGET_RESULT.TIME is '评价时间';

comment on table BIZ_COURSE_LIVE_TARGET_RESULT is '存储学员用户对直播课的评分';

--更改素材表，素材名称的长度64->200
alter table INF_MATERIAL modify name NVARCHAR2(200);
--2024-03-25 已上线

--审批事项表增加前置审批相关字段
alter table COMM_APPROVE_ITEM add pre_user_id NVARCHAR2(64);
comment on column COMM_APPROVE_ITEM.pre_user_id is '前置审批人id';

alter table COMM_APPROVE_ITEM add pre_status NVARCHAR2(1);
comment on column COMM_APPROVE_ITEM.pre_status is '前置审批状态 0不通过 1通过 2待审';


-- 七牛切片临时表
create table BIZ_KJ_VIDEO_URL
(
    id            NVARCHAR2(64) not null,
    video_id      NVARCHAR2(64),
    old_url       NVARCHAR2(500),
    new_url       NVARCHAR2(500),
    qny_sid       NVARCHAR2(64),
    response_txt  NVARCHAR2(2000),
    response_time DATE,
    ts_time       DATE,
    is_replace    INTEGER
);
-- Add comments to the table
comment on table BIZ_KJ_VIDEO_URL
    is '课件单位视频URL记录表';
-- Add comments to the columns
comment on column BIZ_KJ_VIDEO_URL.id
    is '主键ID';
comment on column BIZ_KJ_VIDEO_URL.video_id
    is '视频资源id';
comment on column BIZ_KJ_VIDEO_URL.old_url
    is '原视频URL';
comment on column BIZ_KJ_VIDEO_URL.new_url
    is '新视频URL';
comment on column BIZ_KJ_VIDEO_URL.qny_sid
    is '七牛云执行任务ID';
comment on column BIZ_KJ_VIDEO_URL.response_txt
    is '七牛云执行完返回过来的报文';
comment on column BIZ_KJ_VIDEO_URL.response_time
    is '七牛云执行完的时间';
comment on column BIZ_KJ_VIDEO_URL.ts_time
    is '调用七牛云执行的时间';
comment on column BIZ_KJ_VIDEO_URL.is_replace
    is '是否已替换（是、否）';

--2024-03-29已上线

-- 添加项目购买类型字段
alter table BIZ_PLAN_DETAIL add buy_type NVARCHAR2(200);
comment on column BIZ_PLAN_DETAIL.buy_type is '购买类型 course 按课程购买 xm 按项目购买';

alter table BIZ_XM add buy_type NVARCHAR2(200);
comment on column BIZ_XM.buy_type is '购买类型 course 按课程购买 xm 按项目购买';

alter table BIZ_XM_COURSE_SETTING add amount number(10,2);
comment on column BIZ_XM_COURSE_SETTING.amount is '课程金额';
--2024-03-29已上线

--更改用于门户的分类试图查询
create or replace view V_PORTAL_TYPE as
select
    id,
    name,
    parent_id,
    sort,
    create_time
from
    inf_type
where
    host_org_id is null
order by sort asc;

alter table BIZ_KJ_VIDEO_URL modify old_url nvarchar2(1000);
--2024-04-01已上线

--职业表增加职业方向字段
alter table inf_profession add direction NVARCHAR2(500);
comment on column inf_profession.direction is '职业方向';
--2024-04-10已上线

alter table biz_zyjd_bm add regionalism_code NVARCHAR2(64);
comment on column biz_zyjd_bm.regionalism_code is '行政区划代码';

alter table biz_zyjd_bm add xb NVARCHAR2(64);
comment on column biz_zyjd_bm.xb is '性别';

alter table biz_zyjd_bm add certi_category NVARCHAR2(64);
comment on column biz_zyjd_bm.certi_category is '证件类型';

alter table biz_zyjd_bm add xl NVARCHAR2(64);
comment on column biz_zyjd_bm.xl is '学历';

alter table biz_zyjd_bm add xw NVARCHAR2(64);
comment on column biz_zyjd_bm.xw is '学位';

alter table biz_zyjd_bm add mz NVARCHAR2(64);
comment on column biz_zyjd_bm.mz is '民族';

alter table biz_zyjd_bm add social_credit_code NVARCHAR2(64);
comment on column biz_zyjd_bm.social_credit_code is '所在单位统一社会信用代码';

alter table biz_zyjd_bm add unit_nature NVARCHAR2(64);
comment on column biz_zyjd_bm.unit_nature is '所在单位性质';

alter table biz_zyjd_bm add is_provide_jxjy NVARCHAR2(64);
comment on column biz_zyjd_bm.is_provide_jxjy is '是否由所在单位提供继续教育培训';

alter table biz_zyjd_bm add obtain_certi_category NVARCHAR2(64);
comment on column biz_zyjd_bm.obtain_certi_category is '取得证书类型';

alter table biz_zyjd_bm add zc_series NVARCHAR2(64);
comment on column biz_zyjd_bm.zc_series is '职称系列';

alter table biz_zyjd_bm add zy_qualification_name NVARCHAR2(64);
comment on column biz_zyjd_bm.zy_qualification_name is '职业资格名称';

alter table biz_zyjd_bm add zc_level NVARCHAR2(64);
comment on column biz_zyjd_bm.zc_level is '职称级别';

alter table biz_zyjd_bm add zy_qualification_level NVARCHAR2(64);
comment on column biz_zyjd_bm.zy_qualification_level is '职业资格等级';
--2024-04-15已上线
alter table inf_profession_condition add qualification_type NVARCHAR2(64);
comment on column inf_profession_condition.qualification_type is '职称或资格类型 枚举';

alter table biz_plan_detail add remark nvarchar2(2000);
comment on column biz_plan_detail.remark is '备注';
--2024-04-18已上线

create table BIZ_FORM_FIELD_TYPE (
    ID  nvarchar2(64) not null,
    NAME nvarchar2(500),
    SORT integer,
    form_id nvarchar2(64)
);
comment on table BIZ_FORM_FIELD_TYPE is '表单组件分类表';
comment on column BIZ_FORM_FIELD_TYPE.ID is '主键';
comment on column BIZ_FORM_FIELD_TYPE.NAME is '名称';
comment on column BIZ_FORM_FIELD_TYPE.SORT is '排序';
comment on column BIZ_FORM_FIELD_TYPE.form_id is '自定义表单id';

alter table BIZ_FORM_FIELD_TYPE
    add constraint PK_BIZ_FORM_FIELD_TYPE primary key (id);

alter table BIZ_FIELD add type_id nvarchar2(64);
comment on column BIZ_FIELD.type_id is '组件分类id';

---田军  2024-04-30
alter table BIZ_XM drop column sms_template;
comment on column BIZ_XM.is_auto_send_sms is '报名审核结果是否发送短信通知 1是 0 否';

--设置技能认定报名表，行业，职业，等级都可以为空
ALTER TABLE BIZ_ZYJD_BM MODIFY INDUSTRY_ID NVARCHAR2(64) NULL;
ALTER TABLE BIZ_ZYJD_BM MODIFY PROFESSION_ID NVARCHAR2(64) NULL;
ALTER TABLE BIZ_ZYJD_BM MODIFY APPLY_TECH_LEVEL NVARCHAR2(50) NULL;
--2024-05-07已上线
alter table biz_zyjd_bm add receipt NVARCHAR2(1000);
comment on column biz_zyjd_bm.receipt is '报名回执';

--处理退费数据
update biz_bm set is_payed = '2' where mark_refund_user_id is not null;
update biz_zyjd_bm set pay_status='REFUND' where mark_refund_user_id is not null;

-- 2024-6-5表COMM_SMS_LOG董小康增加字段biz_id
alter table COMM_SMS_LOG add biz_id nvarchar2(64);
comment on column COMM_SMS_LOG.biz_id is '业务id（报名id）';

alter table biz_zyjd_bm add invoice_url NVARCHAR2(1000);
comment on column biz_zyjd_bm.invoice_url is '发票url地址，用于土地地质测绘学员缴费';

create table BIZ_ATTACHMENT (
    ID    nvarchar2(64) not null,
    NAME  nvarchar2(500),
    URL   nvarchar2(1000),
    YW_ID nvarchar2(64)
);
comment on table BIZ_ATTACHMENT is '附件表';
comment on column BIZ_ATTACHMENT.ID is '主键';
comment on column BIZ_ATTACHMENT.NAME is '附件名称';
comment on column BIZ_ATTACHMENT.URL is '附件地址';
comment on column BIZ_ATTACHMENT.YW_ID is '业务id';

alter table BIZ_ZYJD_BMBATCH add is_one_trial NVARCHAR2(20);
comment on column BIZ_ZYJD_BMBATCH.is_one_trial is '是否开启初审';

create table BIZ_ZYJD_BM_TRIAL (
    ID          nvarchar2(64) not null,
    BMBATCH_ID  nvarchar2(64),
    USER_ID     nvarchar2(64),
    USER_ROLE   nvarchar2(20),
    NUM         nvarchar2(20)
);
comment on table BIZ_ZYJD_BM_TRIAL is '职业鉴定报名数据审核员表';
comment on column BIZ_ZYJD_BM_TRIAL.ID is '主键';
comment on column BIZ_ZYJD_BM_TRIAL.BMBATCH_ID is '批次id';
comment on column BIZ_ZYJD_BM_TRIAL.USER_ID is '用户id';
comment on column BIZ_ZYJD_BM_TRIAL.USER_ROLE is '用户角色';
comment on column BIZ_ZYJD_BM_TRIAL.NUM is '审核级别 1 初审';
--调查问卷增加是否是满意度调查字段
alter table BIZ_QUESTIONNAIRE add scope NVARCHAR2(10);
comment on column BIZ_QUESTIONNAIRE.scope is '调查对象 ADMIN 管理员 STUDENT 学员';

--2024-06-11已发布上线

--答辩评审
alter table biz_zyjd_bmbatch add REPLY_START_TIME DATE;
comment on column biz_zyjd_bmbatch.REPLY_START_TIME is '答辩开始时间';

alter table biz_zyjd_bmbatch add REPLY_END_TIME DATE;
comment on column biz_zyjd_bmbatch.REPLY_END_TIME is '答辩结束时间';

alter table biz_zyjd_bmbatch add REVIEW_START_TIME DATE;
comment on column biz_zyjd_bmbatch.REVIEW_START_TIME is '评审开始时间';

alter table biz_zyjd_bmbatch add REVIEW_END_TIME DATE;
comment on column biz_zyjd_bmbatch.REVIEW_END_TIME is '评审结束时间';

alter table biz_zyjd_bmbatch add DIRECTOR nvarchar2(64);
comment on column biz_zyjd_bmbatch.DIRECTOR is '主任';

alter table biz_zyjd_bmbatch add SUPERVISOR nvarchar2(200);
comment on column biz_zyjd_bmbatch.SUPERVISOR is '督导员';

--副主任表
CREATE TABLE BIZ_ZC_BATCH_DIRECTOR(
      ID NVARCHAR2(64) NOT NULL,
      BATCH_ID NVARCHAR2(64),
      USER_ID NVARCHAR2(64),
      PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_ZC_BATCH_DIRECTOR IS '副主任表';
COMMENT ON COLUMN BIZ_ZC_BATCH_DIRECTOR.BATCH_ID IS '答辩评审批次id';
COMMENT ON COLUMN BIZ_ZC_BATCH_DIRECTOR.USER_ID IS '教师id';

alter table BIZ_ZYJD_BMSCOPE add MAX_PERSON_NUM int;
comment on column BIZ_ZYJD_BMSCOPE.MAX_PERSON_NUM is '人数上限';

alter table BIZ_ZYJD_BMSCOPE add STATUS nvarchar2(20);
comment on column BIZ_ZYJD_BMSCOPE.STATUS is '状态';

alter table BIZ_ZYJD_BMSCOPE add CATEGORY nvarchar2(20);
comment on column BIZ_ZYJD_BMSCOPE.CATEGORY is '工种类型';

alter table sys_user_info add speciality_type nvarchar2(500);
comment on column sys_user_info.speciality_type is '专业类别';

alter table biz_zyjd_bm add exam_score number(10,2);
comment on column biz_zyjd_bm.exam_score is '理论考试得分';

alter table biz_zyjd_bm add skill_score number(10,2);
comment on column biz_zyjd_bm.skill_score is '操作技能得分';

alter table biz_zyjd_bm add job_score number(10,2);
comment on column biz_zyjd_bm.job_score is '工作业绩得分';

alter table biz_zyjd_bm add potential_score number(10,2);
comment on column biz_zyjd_bm.potential_score is '潜在能力得分';

alter table biz_zyjd_bm add synthesize_score number(10,2);
comment on column biz_zyjd_bm.synthesize_score is '综合评定';

alter table biz_zyjd_bm add thesis_score nvarchar2(20);
comment on column biz_zyjd_bm.thesis_score is '论文评分';

alter table biz_zyjd_bm add score nvarchar2(20);
comment on column biz_zyjd_bm.score is '评审得分';

alter table biz_zyjd_bm add passed nvarchar2(20);
comment on column biz_zyjd_bm.passed is '评审结果 0 不通过 1 通过';

alter table biz_zyjd_bm add reward nclob;
comment on column biz_zyjd_bm.reward is '奖励';

alter table biz_zyjd_bm add achievement nclob;
comment on column biz_zyjd_bm.achievement is '业绩';

alter table biz_zyjd_bm add work_experience nclob;
comment on column biz_zyjd_bm.work_experience is '主要学历和工作经历(包括技术配合、进修)';

alter table biz_zyjd_bm add work_experience_dl nclob;
comment on column biz_zyjd_bm.work_experience_dl is '工作经历';

alter table biz_zyjd_bm add training_experience nclob;
comment on column biz_zyjd_bm.training_experience is '培训经历';

alter table biz_zyjd_bm add js_work_achievement nclob;
comment on column biz_zyjd_bm.js_work_achievement is '取得技师后的工作业绩';

alter table biz_zyjd_bm add skill_speciality nclob;
comment on column biz_zyjd_bm.skill_speciality is '主要技术特长、贡献及成果';

alter table biz_zyjd_bm add xm nclob;
comment on column biz_zyjd_bm.xm is '技术革新、技术改造、科技成果转化、关键问题处理';

alter table biz_zyjd_bm add opus nclob;
comment on column biz_zyjd_bm.opus is '编写操作规程、规范、标准、教案及发表论文、著作等情况';

alter table biz_zyjd_bm add competition nclob;
comment on column biz_zyjd_bm.competition is '参加技能竞赛获奖情况';

alter table biz_zyjd_bm add bear_content nclob;
comment on column biz_zyjd_bm.bear_content is '承担技艺传授、技能培训工作情况';

alter table biz_zyjd_bm add honor nclob;
comment on column biz_zyjd_bm.honor is '获得荣誉称号';

alter table biz_zyjd_bm add technical_summary nclob;
comment on column biz_zyjd_bm.technical_summary is '技术总结';

alter table biz_zyjd_bm add thesis nclob;
comment on column biz_zyjd_bm.thesis is '论文';

alter table biz_zyjd_bm add js_time date;
comment on column biz_zyjd_bm.js_time is '取得技师资格等级时间';

alter table biz_zyjd_bm add speciality nvarchar2(200);
comment on column biz_zyjd_bm.speciality is '特长';

--答辩评审论文评分表
CREATE TABLE BIZ_ZC_THESIS_SCORE(
    ID NVARCHAR2(64) NOT NULL,
    BM_ID NVARCHAR2(64),
    INNOVATE_SCORE number(10,2),
    UTILITY_SCORE number(10,2),
    CORRECTNESS_SCORE number(10,2),
    ACTUAL_EFFECT_SCORE number(10,2),
    WRITING_EXPRESSION_SCORE number(10,2),
    ANSWER_CORRECTNESS_SCORE number(10,2),
    ANSWER_LOGIC_SCORE number(10,2),
    LANGUAGE_EXPRESSION_SCORE number(10,2),
    CREATED_BY NVARCHAR2(64),
    CREATED_TIME DATE,
    UPDATED_BY NVARCHAR2(64),
    UPDATED_TIME DATE,
    PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_ZC_THESIS_SCORE IS '答辩评审论文评分表;';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.ID IS '主键';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.BM_ID IS '答辩评审报名id';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.INNOVATE_SCORE IS '论文创新性评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.UTILITY_SCORE IS '论文实用性评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.CORRECTNESS_SCORE IS '论文正确性评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.ACTUAL_EFFECT_SCORE IS '论文实际效果评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.WRITING_EXPRESSION_SCORE IS '写作表达水平评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.ANSWER_CORRECTNESS_SCORE IS '回答正确性评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.ANSWER_LOGIC_SCORE IS '回答逻辑性评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.LANGUAGE_EXPRESSION_SCORE IS '语言表达能力评分';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.CREATED_BY IS '创建人';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.UPDATED_BY IS '更新人';
COMMENT ON COLUMN BIZ_ZC_THESIS_SCORE.UPDATED_TIME IS '更新时间';

--答辩评审资料表
CREATE TABLE BIZ_ZC_FILE(
    ID NVARCHAR2(64) NOT NULL,
    BM_ID NVARCHAR2(64),
    TYPE NVARCHAR2(50),
    NAME NVARCHAR2(500),
    FILE_SIZE number(10,2),
    URL NVARCHAR2(500),
    CREATED_BY NVARCHAR2(64),
    CREATED_TIME DATE,
    UPDATED_BY NVARCHAR2(64),
    UPDATED_TIME DATE,
    PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_ZC_FILE IS '答辩评审资料表';
COMMENT ON COLUMN BIZ_ZC_FILE.ID IS '主键';
COMMENT ON COLUMN BIZ_ZC_FILE.BM_ID IS '答辩评审报名id';
COMMENT ON COLUMN BIZ_ZC_FILE.TYPE IS '文件类型';
COMMENT ON COLUMN BIZ_ZC_FILE.NAME IS '文件名称';
COMMENT ON COLUMN BIZ_ZC_FILE.FILE_SIZE IS '文件大小 单位M';
COMMENT ON COLUMN BIZ_ZC_FILE.URL IS '文件路径';
COMMENT ON COLUMN BIZ_ZC_FILE.CREATED_BY IS '创建人';
COMMENT ON COLUMN BIZ_ZC_FILE.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_ZC_FILE.UPDATED_BY IS '更新人';
COMMENT ON COLUMN BIZ_ZC_FILE.UPDATED_TIME IS '更新时间';


-- 分组表
CREATE TABLE BIZ_GROUP(
      ID NVARCHAR2(64) NOT NULL,
      BMBATCH_ID NVARCHAR2(64),
      NUM INT,
      NAME NVARCHAR2(90),
      STATUS NVARCHAR2(20),
      URL NVARCHAR2(500),
      PLAYBACK_URL NVARCHAR2(500),
      TYPE NVARCHAR2(1),
      TASK_ID NVARCHAR2(200),
      ROOM_ID NVARCHAR2(64),
      CREATED_BY NVARCHAR2(64),
      CREATED_TIME DATE,
      UPDATED_BY NVARCHAR2(64),
      UPDATED_TIME DATE,
      PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_GROUP IS '分组表;';
COMMENT ON COLUMN BIZ_GROUP.ID IS '主键';
COMMENT ON COLUMN BIZ_GROUP.BMBATCH_ID IS '答辩评审批次id';
COMMENT ON COLUMN BIZ_GROUP.NUM IS '分组序号';
COMMENT ON COLUMN BIZ_GROUP.NAME IS '分组名称';
COMMENT ON COLUMN BIZ_GROUP.STATUS IS '状态 枚举 答辩中，等待中，完成';
COMMENT ON COLUMN BIZ_GROUP.URL IS '答辩地址';
COMMENT ON COLUMN BIZ_GROUP.PLAYBACK_URL IS '回放地址';
COMMENT ON COLUMN BIZ_GROUP.TYPE IS '分组类型 1答辩分组 2评审分组';
COMMENT ON COLUMN BIZ_GROUP.TASK_ID IS '录制任务id';
COMMENT ON COLUMN BIZ_GROUP.ROOM_ID IS '房间id';
COMMENT ON COLUMN BIZ_GROUP.CREATED_BY IS '创建人';
COMMENT ON COLUMN BIZ_GROUP.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_GROUP.UPDATED_BY IS '更新人';
COMMENT ON COLUMN BIZ_GROUP.UPDATED_TIME IS '更新时间';

--分组学员表
CREATE TABLE BIZ_GROUP_STUDENT(
      ID NVARCHAR2(64) NOT NULL,
      GROUP_ID NVARCHAR2(64),
      STUDENT_ID NVARCHAR2(64),
      NUM INT,
      STATUS INT,
      START_DB_TIME DATE,
      PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_GROUP_STUDENT IS '分组学员关系表;';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.ID IS '主键';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.GROUP_ID IS '分组id';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.STUDENT_ID IS '学员id';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.NUM IS '答辩序号';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.START_DB_TIME IS '进入答辩时间';
COMMENT ON COLUMN BIZ_GROUP_STUDENT.STATUS IS '答辩状态 0 未开始 1进行中 2已结束';

-- 分组老师表
CREATE TABLE BIZ_GROUP_USER(
       ID NVARCHAR2(64) NOT NULL,
       GROUP_ID NVARCHAR2(64),
       USER_ID NVARCHAR2(64),
       IS_LEADER NVARCHAR2(1),
       PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_GROUP_USER IS '分组老师关系表;';
COMMENT ON COLUMN BIZ_GROUP_USER.ID IS '主键';
COMMENT ON COLUMN BIZ_GROUP_USER.GROUP_ID IS '分组id';
COMMENT ON COLUMN BIZ_GROUP_USER.USER_ID IS '老师id';
COMMENT ON COLUMN BIZ_GROUP_USER.IS_LEADER IS '是否是组长 1是 0否';

-- 评审打分表
CREATE TABLE BIZ_REVIEW_MARK(
       id NVARCHAR2(64) NOT NULL,
       bm_id NVARCHAR2(64),
       user_id NVARCHAR2(64),
       score number(10,2),
       PRIMARY KEY (ID)
);

COMMENT ON TABLE BIZ_REVIEW_MARK IS '评审打分表;';
COMMENT ON COLUMN BIZ_REVIEW_MARK.ID IS '主键';
COMMENT ON COLUMN BIZ_REVIEW_MARK.bm_id IS '报名id';
COMMENT ON COLUMN BIZ_REVIEW_MARK.user_id IS '老师id';
COMMENT ON COLUMN BIZ_REVIEW_MARK.score IS '分数';

alter table BIZ_GROUP_STUDENT add sign_user nvarchar2(64);
comment on column BIZ_GROUP_STUDENT.sign_user is '标记人';

alter table BIZ_GROUP_STUDENT add sign_time date;
comment on column BIZ_GROUP_STUDENT.sign_time is '标记时间';

alter table BIZ_GROUP_STUDENT add remark nvarchar2(200);
comment on column BIZ_GROUP_STUDENT.remark is '备注';

alter table biz_zyjd_bm add is_reevaluation nvarchar2(20);
comment on column biz_zyjd_bm.is_reevaluation is '是否转评';

alter table biz_zyjd_bm add is_exceptional nvarchar2(20);
comment on column biz_zyjd_bm.is_exceptional is '是否破格';


---创建删除订单的触发器
create table trigger_del_comm_order as select * from comm_order  where 2 < 1;

CREATE OR REPLACE TRIGGER trigger_del_comm_order
   BEFORE DELETE --指定触发时机为删除操作前触发
   ON comm_order
   FOR EACH ROW   --说明创建的是行级触发器
BEGIN
  insert into trigger_del_comm_order
  (id, category, user_type, pay_user_id, pay_org_id, host_org_id, pay_name, amount, product, pay_method, status, trans_number, pay_time, notify_time, remark, pay_account, create_time, get_result_by, bank_id, union_order_no)
values
  (:old.id, :old.category, :old.user_type, :old.pay_user_id, :old.pay_org_id, :old.host_org_id, :old.pay_name, :old.amount, :old.product, :old.pay_method, :old.status, :old.trans_number, :old.pay_time, :old.notify_time, :old.remark, :old.pay_account, :old.create_time, :old.get_result_by, :old.bank_id, :old.union_order_no);
END;

--学员表增加工号处理
alter table sys_student add employee_num NVARCHAR2(100);
comment on column sys_student.employee_num is '工号';

--sys_user表增加工号处理
alter table sys_user add employee_num NVARCHAR2(100);
comment on column sys_user.employee_num is '工号';

--2024-6-21 董小康 表单字段表增加表单填写提示字段
alter table biz_field add remark nvarchar2(500);
comment on column biz_field.remark is '表单填写提示';

--2024-6-24 邹贤良 bm表添加退费原因字段
ALTER TABLE BIZ_BM ADD REFUND_REMARK NVARCHAR2(200);
COMMENT ON COLUMN BIZ_BM.REFUND_REMARK IS '退费备注 （原因 + 人 + 时间）';

--2024-6-24 邹贤良 bm表添加退费原因字段
ALTER TABLE COMM_ORDER ADD REFUND_REMARK NVARCHAR2(200);
COMMENT ON COLUMN COMM_ORDER.REFUND_REMARK IS '退费备注 （原因 + 人 + 时间）';

--2024-6-24 邹贤良 BIZ_ZYJD_BMBATCH表添加缴费截止时间字段
ALTER TABLE BIZ_ZYJD_BMBATCH ADD JF_END_TIME DATE;
COMMENT ON COLUMN BIZ_ZYJD_BMBATCH.JF_END_TIME IS '缴费截止时间';

--2024-7-8 董小康 BIZ_ZYJD_BM表添加字段考场、座位号
ALTER TABLE BIZ_ZYJD_BM ADD EXAM_ROOM NVARCHAR2(200);
COMMENT ON COLUMN BIZ_ZYJD_BM.EXAM_ROOM IS '考场号';
ALTER TABLE BIZ_ZYJD_BM ADD SEAT_NO NVARCHAR2(100);
COMMENT ON COLUMN BIZ_ZYJD_BM.SEAT_NO IS '座位号';
        
        
--2024-07-10 杜斌 biz_exam_paper 添加字段
ALTER TABLE BIZ_EXAM_PAPER ADD IS_REEXAMINATION NUMBER default 0;
COMMENT ON COLUMN BIZ_EXAM_PAPER.IS_REEXAMINATION IS '是否允许重考 0 否 1 是';       

        
--2024-07-11 杜斌 biz_bm 添加字段
ALTER TABLE biz_bm ADD invoice_path NVARCHAR2(500); 
COMMENT ON COLUMN biz_bm.invoice_path IS '发票地址';

        
--2024-07-15 杜斌 biz_zyjd_bm 添加字段
ALTER TABLE biz_zyjd_bm ADD EXAM_POINT NVARCHAR2(500);
COMMENT ON COLUMN biz_zyjd_bm.EXAM_POINT IS '考试地点';

ALTER TABLE biz_zyjd_bm ADD EXAM_TIME NVARCHAR2(20);
COMMENT ON COLUMN biz_zyjd_bm.EXAM_TIME IS '考试时间';

ALTER TABLE biz_zyjd_bm ADD BUS_LINE NVARCHAR2(500);
COMMENT ON COLUMN biz_zyjd_bm.BUS_LINE IS '乘车路线'; 

-- 2024-07-15 董小康增加表 INF_PROFESSION_AD_COURSE
CREATE TABLE INF_PROFESSION_AD_COURSE (
  ID NVARCHAR2(64) NOT NULL,
  COURSE_ID NVARCHAR2(64),
  IS_LIVE NVARCHAR2(1),
  IS_COURSEWARE NVARCHAR2(1),
  CREATE_TIME DATE,
  CREATOR_ID NVARCHAR2(64),
  UPDATE_TIME DATE,
  UPDATOR_ID NVARCHAR2(64),
  IS_MS NVARCHAR2(1),
  CATEGORY NVARCHAR2(64),
  MS_CONTENT NCLOB,
  TEACHER_ID NVARCHAR2(64),
  LIVE_CONTENT VARCHAR2(64 BYTE),
  TEACHER_NAME NVARCHAR2(50),
  VENUE_ROOM_ID NVARCHAR2(64),
  START_TIME DATE,
  END_TIME DATE,
  ADDRESS VARCHAR2(255 BYTE),
  INDUSTRY_ID NVARCHAR2(64),
  PROFESSION_ID NVARCHAR2(64),
  TECH_LEVEL NVARCHAR2(64),
  IS_SHARED NVARCHAR2(5),
  AMOUNT NUMBER(10,2),
  HOST_ORG_ID NVARCHAR2(64),
  PRIMARY KEY (ID)
);
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.TEACHER_NAME IS '讲师名称';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.VENUE_ROOM_ID IS '培训室ID';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.START_TIME IS '上课开始时间';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.END_TIME IS '上课结束时间';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.ADDRESS IS '上课地点';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.INDUSTRY_ID IS '申报行业ID';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.PROFESSION_ID IS '申报工种ID';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.TECH_LEVEL IS '申报等级';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.IS_SHARED IS '是否从别的直播课共享过来(是/否)';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.AMOUNT IS '课程金额';
COMMENT ON COLUMN INF_PROFESSION_AD_COURSE.HOST_ORG_ID IS '主办单位id';

--2024-07-22 董小康 sys_role_2_privilege 添加字段
ALTER TABLE sys_role_2_privilege ADD HOST_ORG_ID NVARCHAR2(64);
COMMENT ON COLUMN sys_role_2_privilege.HOST_ORG_ID IS '主办单位id，非空时为主办单位独立权限配置，反之默认配置';

--2024-07-31 邹贤良 增加直属单位管理
CREATE TABLE "INF_UNIT" (
"ID" NVARCHAR2(100) NOT NULL,
"NAME" NVARCHAR2(100),
"STATUS" NVARCHAR2(20),
"HOST_ORG_ID" NVARCHAR2(64),
"CREATE_TIME" DATE,
"CREATOR_ID" NVARCHAR2(64),
"UPDATE_TIME" DATE,
"UPDATOR_ID" NVARCHAR2(64),
PRIMARY KEY ("ID")
);
COMMENT ON COLUMN "INF_UNIT"."ID" IS '主键id';
COMMENT ON COLUMN "INF_UNIT"."NAME" IS '名称';
COMMENT ON COLUMN "INF_UNIT"."STATUS" IS '状态';
COMMENT ON COLUMN "INF_UNIT"."HOST_ORG_ID" IS '主办单位id';
COMMENT ON COLUMN "INF_UNIT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "INF_UNIT"."CREATOR_ID" IS '创建用户id';
COMMENT ON COLUMN "INF_UNIT"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "INF_UNIT"."UPDATOR_ID" IS '修改用户id';

--2024-07-31 邹贤良 职称评审报名增加直属单位名称
ALTER TABLE "BIZ_ZYJD_BM" ADD ("UNIT_NAME" NVARCHAR2(200));
COMMENT ON COLUMN "BIZ_ZYJD_BM"."UNIT_NAME" IS '直属单位'

--2024-07-31 邹贤良 用户信息增加可评审工种
ALTER TABLE "SYS_USER_INFO" ADD ("PROFESSION_IDS" NVARCHAR2(1000));
COMMENT ON COLUMN "SYS_USER_INFO"."PROFESSION_IDS" IS '可评审工种'

--2024-08-08 邹贤良 职称评审增加logo
ALTER TABLE "BIZ_ZYJD_BMBATCH" ADD ("LOGO" NVARCHAR2(255));
COMMENT ON COLUMN "BIZ_ZYJD_BMBATCH"."LOGO" IS '门户展示的LOGO'

--2024-08-08 邹贤良 职称评审考评员增加银行卡号和开户行
ALTER TABLE "SYS_USER_INFO" ADD ("BANK_CARD_NO" NVARCHAR2(100)) ADD ("BANK" NVARCHAR2(100));
COMMENT ON COLUMN "SYS_USER_INFO"."BANK_CARD_NO" IS '银行卡号';
COMMENT ON COLUMN "SYS_USER_INFO"."BANK" IS '开户行';

ALTER TABLE "BIZ_ZYJD_BMBATCH" ADD ("AUDIT_MODE" NVARCHAR2(200));
COMMENT ON COLUMN "BIZ_ZYJD_BMBATCH"."AUDIT_MODE" IS '枚举：或签OR、会签AND'

--2024-07-31 邹贤良 增加审核日志
CREATE TABLE "BIZ_BM_AUDIT_LOG" (
    "ID" NVARCHAR2(64) NOT NULL,
    "BM_ID" NVARCHAR2(64) NOT NULL,
    "BM_TYPE" NVARCHAR2(20),
    "STATUS" NVARCHAR2(10),
    "USER_ID" NVARCHAR2(64),
    "ADVICE" NVARCHAR2(200),
    "TIME" DATE,
    "IS_FINISH" NUMBER,
    "COUNT" NUMBER,
    PRIMARY KEY ("ID")
);
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."ID" IS '主键id';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."BM_ID" IS '报名id：BIZ_ZYJD_BM、BIZ_BM主键';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."BM_TYPE" IS '报名类型：项目-XM、职业鉴定-ZYJD，目前只考虑职业鉴定';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."STATUS" IS '审核状态：0-初审驳回、1-初审通过、2-终审通过、3-终审驳回';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."USER_ID" IS '审核用户id';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."ADVICE" IS '审核意见';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."TIME" IS '审核时间';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."IS_FINISH" IS '是否审核完成：1是、0否，当学生重新提交该值置0、当审核结束时该值置0';
COMMENT ON COLUMN "BIZ_BM_AUDIT_LOG"."COUNT" IS '审核轮次：用于详情查看审核记录';

--2024-08-22 邹贤良 职称评审报名
ALTER TABLE "BIZ_ZYJD_BM"
ADD ("APPRAISAL_FORMS" NVARCHAR2(1000))
ADD ("TECHNICAL_SUMMARY_FORMS" NVARCHAR2(1000))
ADD ("THESIS_FORMS" NVARCHAR2(1000))
ADD ("JS_WORK_ACHIEVEMENT_FORMS" NVARCHAR2(1000))
ADD ("LATENT_ABILITY_FORMS" NVARCHAR2(1000))
ADD ("CERTIFICATES_FORMS" NVARCHAR2(1000));

COMMENT ON COLUMN "BIZ_ZYJD_BM"."APPRAISAL_FORMS" IS '技师鉴定考评申报表';
COMMENT ON COLUMN "BIZ_ZYJD_BM"."TECHNICAL_SUMMARY_FORMS" IS '技术总结材料';
COMMENT ON COLUMN "BIZ_ZYJD_BM"."THESIS_FORMS" IS '论文材料';
COMMENT ON COLUMN "BIZ_ZYJD_BM"."JS_WORK_ACHIEVEMENT_FORMS" IS '工作业绩评定材料';
COMMENT ON COLUMN "BIZ_ZYJD_BM"."LATENT_ABILITY_FORMS" IS '潜在能力考核材料';
COMMENT ON COLUMN "BIZ_ZYJD_BM"."CERTIFICATES_FORMS" IS '获奖证书及成果证明材料';

--2024-08-23 董小康 评审打分表增加字段
ALTER TABLE BIZ_REVIEW_MARK ADD GRADE NVARCHAR2(10);
COMMENT ON COLUMN BIZ_REVIEW_MARK.GRADE IS '成绩：A、B、C';    
ALTER TABLE BIZ_REVIEW_MARK ADD REMARK NVARCHAR2(500);
COMMENT ON COLUMN BIZ_REVIEW_MARK.REMARK IS '备注';    

--2024-08-23 董小康 增加评审打分投票表
CREATE TABLE BIZ_REVIEW_VOTE (
	ID NVARCHAR2(64) NOT NULL,
	BM_ID NVARCHAR2(64),
	USER_ID NVARCHAR2(64),
	GRADE NVARCHAR2(10),
	PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_REVIEW_VOTE IS '评审打分投票表';
COMMENT ON COLUMN BIZ_REVIEW_VOTE.ID IS '主键id';
COMMENT ON COLUMN BIZ_REVIEW_VOTE.BM_ID IS '报名id';
COMMENT ON COLUMN BIZ_REVIEW_VOTE.USER_ID IS '投票老师id';
COMMENT ON COLUMN BIZ_REVIEW_VOTE.GRADE IS '成绩：A、B、C';

--2024-09-03 董小康培训项目表增加字段IS_EXEMPT_REGISTER
ALTER TABLE BIZ_XM ADD IS_EXEMPT_REGISTER INTEGER DEFAULT 0;
COMMENT ON COLUMN BIZ_XM.IS_EXEMPT_REGISTER IS '是否免注册：1-是，0-否';

--2024-09-12 董小康培训项目表增加字段STUDENT_NOTICE
ALTER TABLE BIZ_XM ADD STUDENT_NOTICE NCLOB;
COMMENT ON COLUMN BIZ_XM.STUDENT_NOTICE IS '学员须知';

--2024-09-12 董小康 创建项目通知表
CREATE TABLE BIZ_XM_NOTICE (
	ID NVARCHAR2(64) NOT NULL,
	XM_ID NVARCHAR2(64),
	CONTENT NCLOB,
	CLASS_IDS NVARCHAR2(1000),
	STATUS NVARCHAR2(20),
	CREATOR_ID NVARCHAR2(64),
	CREATE_TIME DATE,
	UPDATOR_ID NVARCHAR2(64),
	UPDATE_TIME DATE,
	PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_XM_NOTICE IS '项目通知表';
COMMENT ON COLUMN BIZ_XM_NOTICE.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_NOTICE.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_NOTICE.CONTENT IS '内容';
COMMENT ON COLUMN BIZ_XM_NOTICE.CLASS_IDS IS '通知班级范围：班级id，多个英文逗号拼接，为空-全员';
COMMENT ON COLUMN BIZ_XM_NOTICE.STATUS IS '状态 DRAFT-草稿、PUBLISH-发布、BLOCK-禁用';
COMMENT ON COLUMN BIZ_XM_NOTICE.CREATOR_ID IS '创建人ID';
COMMENT ON COLUMN BIZ_XM_NOTICE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_XM_NOTICE.UPDATOR_ID IS '更新人ID';
COMMENT ON COLUMN BIZ_XM_NOTICE.UPDATE_TIME IS '更新时间';

--2024-09-12 董小康 创建项目日程表
CREATE TABLE BIZ_XM_SCHEDULE (
	ID NVARCHAR2(64) NOT NULL,
	XM_ID NVARCHAR2(64),
	SCHEDULE_TIME DATE,
	ARRANGE NVARCHAR2(500),
	CREATOR_ID NVARCHAR2(64),
	CREATE_TIME DATE,
	PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_XM_SCHEDULE IS '项目日程表';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.SCHEDULE_TIME IS '日程时间：年月日 时分';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.ARRANGE IS '日程安排';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.CREATOR_ID IS '创建人ID';
COMMENT ON COLUMN BIZ_XM_SCHEDULE.CREATE_TIME IS '创建时间';

--2024-09-12 董小康 创建项目实践区域表
CREATE TABLE BIZ_XM_PRACTICE_AREA (
	ID NVARCHAR2(64) NOT NULL,
	XM_ID NVARCHAR2(64),
	ADDR NVARCHAR2(200),
	ADDR_COORD NVARCHAR2(100),
	INTRODUCE NVARCHAR2(1000),
	SORT_NO	INTEGER,
	CREATOR_ID NVARCHAR2(64),
	CREATE_TIME DATE,
	UPDATOR_ID NVARCHAR2(64),
	UPDATE_TIME DATE,
	PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_XM_PRACTICE_AREA IS '项目实践区域表';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.ADDR IS '实践地址';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.ADDR_COORD IS '实践地址坐标';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.INTRODUCE IS '实践区域介绍';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.SORT_NO IS '排序';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.CREATOR_ID IS '创建人ID';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.UPDATOR_ID IS '更新人ID';
COMMENT ON COLUMN BIZ_XM_PRACTICE_AREA.UPDATE_TIME IS '更新时间';

--2024-10-08 赵小飞 增加指标设置类型
ALTER TABLE BIZ_TARGET_SETTING ADD TYPE INTEGER;
COMMENT ON COLUMN BIZ_TARGET_SETTING.TYPE IS '指标设置类型 0项目评价指标 1课程评价指标';

--指标设置数据处理
update BIZ_TARGET_SETTING set type = 0 where type is null;

--2024-10-08 赵小飞 增加指标评价备注
ALTER TABLE BIZ_TARGET_RESULT ADD REMARK NVARCHAR2(500);
COMMENT ON COLUMN BIZ_TARGET_RESULT.REMARK IS '备注';
--2024-10-09 赵小飞 增加指标评价课程设置id
ALTER TABLE BIZ_TARGET_RESULT ADD COURSE_SETTING_ID NVARCHAR2(64);
COMMENT ON COLUMN BIZ_TARGET_RESULT.COURSE_SETTING_ID IS '课程设置id';

--2024-10-12 赵小飞 评分指标增加作答类型
ALTER TABLE BIZ_TARGET ADD ANSWER_TYPE NVARCHAR2(20);
COMMENT ON COLUMN BIZ_TARGET.ANSWER_TYPE IS '作答类型';
--2024-10-12 赵小飞 评分指标增加最大分值
ALTER TABLE BIZ_TARGET ADD max_score INTEGER;
COMMENT ON COLUMN BIZ_TARGET.max_score IS '最大分值';

--处理指标表的老数据
update biz_target set answer_type='STAR',max_score=5 where answer_type is null or max_score is null;

--2024-10-12 赵小飞 项目表增加报道简介
ALTER TABLE BIZ_XM ADD REPORT_INTRODUCTION NCLOB;
COMMENT ON COLUMN BIZ_XM.REPORT_INTRODUCTION IS '报道简介';
--2024-10-12 赵小飞 项目表增加师资简介
ALTER TABLE BIZ_XM ADD TEACHER_INTRODUCTION NCLOB;
COMMENT ON COLUMN BIZ_XM.TEACHER_INTRODUCTION IS '师资简介';
--2024-10-12 赵小飞 项目表增加管理团队及联系方式
ALTER TABLE BIZ_XM ADD MANAGEMENT_TEAM NVARCHAR2(2000);
COMMENT ON COLUMN BIZ_XM.MANAGEMENT_TEAM IS '管理团队及联系方式';

--2024-10-14 赵小飞 项目表增加班委职责
ALTER TABLE BIZ_XM ADD CLASS_COMMITTEE_WORK NVARCHAR2(2000);
COMMENT ON COLUMN BIZ_XM.CLASS_COMMITTEE_WORK IS '班委职责';

--2024-10-14 赵小飞 项目表增加 小组工作内容 包含组长职责和值日工作内容
ALTER TABLE BIZ_XM ADD GROUP_WORK NVARCHAR2(2000);
COMMENT ON COLUMN BIZ_XM.GROUP_WORK IS '小组工作内容 包含组长职责和值日工作内容';

--2024-10-14 赵小飞 项目表增加 住宿安排
ALTER TABLE BIZ_XM ADD STAY_PLAN NVARCHAR2(2000);
COMMENT ON COLUMN BIZ_XM.STAY_PLAN IS '住宿安排';

--2024-10-14 赵小飞 项目表增加 用餐安排
ALTER TABLE BIZ_XM ADD EAT_PLAN NVARCHAR2(2000);
COMMENT ON COLUMN BIZ_XM.EAT_PLAN IS '用餐安排';

--2024-10-14 赵小飞 宾馆表增加 宾馆简介
ALTER TABLE INF_HOTEL ADD REMARK NVARCHAR2(2000);
COMMENT ON COLUMN INF_HOTEL.REMARK IS '宾馆简介';


-- 2024-10-17 胡杰 班级表添加项目、组长、组长任务
alter table COMM_CLASS add XM_ID NVARCHAR2(64);
comment on column COMM_CLASS.XM_ID is '项目ID';

alter table COMM_CLASS
    add STUDENT_ID NVARCHAR2(64);
comment on column COMM_CLASS.STUDENT_ID is '组长id';

alter table COMM_CLASS
    add GROUP_WORK NVARCHAR2(2000);
comment on column COMM_CLASS.GROUP_WORK is '小组工作任务 包含组长职责和值日工作内容';

--2024-10-18 赵小飞 项目课程设置表增加是否开启课程评价
ALTER TABLE BIZ_XM_COURSE_SETTING ADD IS_OPEN_COURSE_TARGET NVARCHAR2(1);
COMMENT ON COLUMN BIZ_XM_COURSE_SETTING.IS_OPEN_COURSE_TARGET IS '是否开启课程评价 1是 0否';

--2024-10-21 董小康增加字段、表
ALTER TABLE BIZ_BM ADD SHIFT_DUTY INTEGER;
COMMENT ON COLUMN BIZ_BM.SHIFT_DUTY IS '班内职责：0负责人，1-班长，2-副班长';

--2024-10-21 赵小飞 项目表增加用餐地点坐标
ALTER TABLE biz_xm ADD eat_point NVARCHAR2(20);
COMMENT ON COLUMN biz_xm.eat_point IS '用餐地点坐标';


ALTER TABLE COMM_CLASS ADD SORT_NO INTEGER;
COMMENT ON COLUMN COMM_CLASS.SORT_NO IS '班级排序值';

CREATE TABLE BIZ_XM_LINKMAN (
	ID 			NVARCHAR2(64) NOT NULL,
	XM_ID 		NVARCHAR2(64),
	NAME		NVARCHAR2(200),
	PHONE		NVARCHAR2(100),
	REMARK		NVARCHAR2(1000)
);
COMMENT ON TABLE BIZ_XM_LINKMAN IS '项目培训联系人';
COMMENT ON COLUMN BIZ_XM_LINKMAN.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_LINKMAN.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_LINKMAN.NAME IS '姓名';
COMMENT ON COLUMN BIZ_XM_LINKMAN.PHONE IS '电话';
COMMENT ON COLUMN BIZ_XM_LINKMAN.REMARK IS '备注';

--基地表
CREATE TABLE INF_BASE (
    ID 			NVARCHAR2(64) NOT NULL,
    NAME 		NVARCHAR2(500),
    ORG		    NVARCHAR2(500),
    TYPE        NVARCHAR2(1),
    CREATE_TIME DATE,
    CREATOR_ID  NVARCHAR2(64),
    UPDATE_TIME DATE,
    UPDATOR_ID  NVARCHAR2(64),
    HOST_ORG_ID NVARCHAR2(64),
    PRIMARY KEY (ID)
);
COMMENT ON TABLE INF_BASE IS '培训基地表';
COMMENT ON COLUMN INF_BASE.ID IS '主键ID';
COMMENT ON COLUMN INF_BASE.NAME IS '基地名称';
COMMENT ON COLUMN INF_BASE.ORG IS '主管单位';
COMMENT ON COLUMN INF_BASE.TYPE IS '基地类型';
COMMENT ON COLUMN INF_BASE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN INF_BASE.CREATOR_ID IS '创建人';
COMMENT ON COLUMN INF_BASE.UPDATE_TIME IS '修改时间';
COMMENT ON COLUMN INF_BASE.UPDATOR_ID IS '修改人';
COMMENT ON COLUMN INF_BASE.HOST_ORG_ID IS '主办单位id';

--IP白名单表
CREATE TABLE INF_IP_WHITE (
      ID 		  NVARCHAR2(64) NOT NULL,
      IP 		  NVARCHAR2(20),
      CREATE_TIME DATE,
      CREATOR_ID  NVARCHAR2(64),
      UPDATE_TIME DATE,
      UPDATOR_ID  NVARCHAR2(64),
      PRIMARY KEY (ID)
);
COMMENT ON TABLE INF_IP_WHITE IS 'IP白名单表';
COMMENT ON COLUMN INF_IP_WHITE.ID IS '主键ID';
COMMENT ON COLUMN INF_IP_WHITE.IP IS 'IP地址';
COMMENT ON COLUMN INF_IP_WHITE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN INF_IP_WHITE.CREATOR_ID IS '创建人';
COMMENT ON COLUMN INF_IP_WHITE.UPDATE_TIME IS '修改时间';
COMMENT ON COLUMN INF_IP_WHITE.UPDATOR_ID IS '修改人';

ALTER TABLE BIZ_XM_NOTICE ADD TITLE NVARCHAR2(500);
COMMENT ON COLUMN BIZ_XM_NOTICE.TITLE IS '标题';

--项目通知阅读记录表
CREATE TABLE BIZ_XM_NOTICE_RECORD (
      ID 		   NVARCHAR2(64) NOT NULL,
      XM_ID 	   NVARCHAR2(64),
      XM_NOTICE_ID NVARCHAR2(64),
      STUDENT_ID   NVARCHAR2(64),
      TIME         DATE,
      PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_XM_NOTICE_RECORD IS '项目通知阅读记录表';
COMMENT ON COLUMN BIZ_XM_NOTICE_RECORD.ID IS '主键id';
COMMENT ON COLUMN BIZ_XM_NOTICE_RECORD.XM_ID IS '项目id';
COMMENT ON COLUMN BIZ_XM_NOTICE_RECORD.XM_NOTICE_ID IS '项目通知公告id';
COMMENT ON COLUMN BIZ_XM_NOTICE_RECORD.STUDENT_ID IS '学生id';
COMMENT ON COLUMN BIZ_XM_NOTICE_RECORD.TIME IS '阅读时间';
--2024-11-06理工大已发布上线

ALTER TABLE BIZ_TARGET_TYPE ADD parent_id NVARCHAR2(64);
COMMENT ON COLUMN BIZ_TARGET_TYPE.parent_id IS '父id';

ALTER TABLE BIZ_TARGET_TYPE ADD sort NVARCHAR2(20);
COMMENT ON COLUMN BIZ_TARGET_TYPE.sort IS '排序';

ALTER TABLE BIZ_TARGET_TYPE ADD is_course_target NVARCHAR2(20);
COMMENT ON COLUMN BIZ_TARGET_TYPE.is_course_target IS '是否是课程评价';

ALTER TABLE INF_BASE ADD APPROVAL_TIME nvarchar2(20);
comment on column INF_BASE.APPROVAL_TIME is '基地获批时间';

ALTER TABLE SYS_USER_INFO ADD category nvarchar2(200);
comment on column SYS_USER_INFO.category is '校内：所属学院，校外：企业，高校，党政干部';

--国际化教育表
CREATE TABLE inf_international_education
(
    ID                NVARCHAR2(64) NOT NULL,
    country_name      NVARCHAR2(2000),
    cooperative_units NVARCHAR2(2000),
    rank              NVARCHAR2(2000),
    project_category  nvarchar2(2000),
    speciality        nvarchar2(2000),
    apply_time        nvarchar2(2000),
    host_org_id       nvarchar2(64),
    creator_id        nvarchar2(64),
    create_time       date,
    updator_id        nvarchar2(64),
    update_time       date,
    PRIMARY KEY (ID)
);
COMMENT ON TABLE inf_international_education IS '国际化教育表';
COMMENT ON COLUMN inf_international_education.ID IS '主键id';
COMMENT ON COLUMN inf_international_education.country_name IS '国别';
COMMENT ON COLUMN inf_international_education.cooperative_units IS '外方合作单位名称';
COMMENT ON COLUMN inf_international_education.rank IS '世界排名';
COMMENT ON COLUMN inf_international_education.project_category IS '合作项目类别';
COMMENT ON COLUMN inf_international_education.speciality IS '合作专业';
COMMENT ON COLUMN inf_international_education.apply_time IS '计划实施期';
COMMENT ON COLUMN inf_international_education.host_org_id IS '主办单位id';
COMMENT ON COLUMN inf_international_education.creator_id IS '创建人';
COMMENT ON COLUMN inf_international_education.create_time IS '创建时间';
COMMENT ON COLUMN inf_international_education.updator_id IS '修改人';
COMMENT ON COLUMN inf_international_education.update_time IS '修改时间';

--项目支出表
CREATE TABLE BIZ_XM_EXPEND
(
    ID              NVARCHAR2(64) NOT NULL,
    XM_ID           NVARCHAR2(64),
    CONTENT  NVARCHAR2(2000),
    AMOUNT   NUMBER,
    TIME            DATE,
    OPERATOR        NVARCHAR2(64),
    PRIMARY KEY (ID)
);
COMMENT ON TABLE BIZ_XM_EXPEND IS '项目收支表';
COMMENT ON COLUMN BIZ_XM_EXPEND.ID IS '主键ID';
COMMENT ON COLUMN BIZ_XM_EXPEND.XM_ID IS '项目ID';
COMMENT ON COLUMN BIZ_XM_EXPEND.CONTENT IS '支出项';
COMMENT ON COLUMN BIZ_XM_EXPEND.AMOUNT IS '支出金额';
COMMENT ON COLUMN BIZ_XM_EXPEND.TIME IS '时间';
COMMENT ON COLUMN BIZ_XM_EXPEND.OPERATOR IS '经办人';

ALTER TABLE BIZ_XM ADD SOURCE NUMBER;
COMMENT ON COLUMN BIZ_XM.SOURCE IS '项目来源 1建材建工 2汽车 3交通 4其他';

ALTER TABLE BIZ_XM ADD INDUSTRY_CATEGORY NVARCHAR2(64);
COMMENT ON COLUMN BIZ_XM.INDUSTRY_CATEGORY IS '行业特色 字典';

ALTER TABLE BIZ_XM ADD INCOME_AMOUNT NUMBER;
COMMENT ON COLUMN BIZ_XM.INCOME_AMOUNT IS '收入金额';

ALTER TABLE BIZ_XM ADD SUPPORT_AMOUNT NUMBER;
COMMENT ON COLUMN BIZ_XM.SUPPORT_AMOUNT IS '资助金额';

ALTER TABLE BIZ_XM ADD BASE_ID NVARCHAR2(64);
COMMENT ON COLUMN BIZ_XM.BASE_ID IS '所属基地';

ALTER TABLE BIZ_PLAN_DETAIL ADD CLASSZ_TYPE NVARCHAR2(64);
COMMENT ON COLUMN BIZ_PLAN_DETAIL.CLASSZ_TYPE IS '培训班类别 字典 classzType';

ALTER TABLE BIZ_PLAN_DETAIL ADD INDUSTRY_CATEGORY NVARCHAR2(64);
COMMENT ON COLUMN BIZ_PLAN_DETAIL.INDUSTRY_CATEGORY IS '行业特色';

ALTER TABLE BIZ_PLAN_DETAIL ADD SUPPORT_AMOUNT NVARCHAR2(64);
COMMENT ON COLUMN BIZ_PLAN_DETAIL.SUPPORT_AMOUNT IS '资助金额';

ALTER TABLE BIZ_PLAN_DETAIL ADD BASE_ID NVARCHAR2(64);
COMMENT ON COLUMN BIZ_PLAN_DETAIL.BASE_ID IS '所属基地';

-- 删除字段
ALTER TABLE BIZ_XM DROP COLUMN SOURCE;
ALTER TABLE BIZ_XM DROP COLUMN INDUSTRY_CATEGORY;
ALTER TABLE BIZ_XM DROP COLUMN SUPPORT_AMOUNT;
ALTER TABLE BIZ_XM DROP COLUMN BASE_ID;

alter table SYS_USER_INFO modify BRIEF NVARCHAR2(2000);

alter table SYS_USER_INFO modify courses NVARCHAR2(2000);

ALTER TABLE INF_COURSE ADD REMARK NVARCHAR2(2000);
COMMENT ON COLUMN INF_COURSE.REMARK IS '课程简介';

create table BIZ_XM_BM_CARD_NUMBER
(
    ID           NVARCHAR2(64) not null primary key,
    BM_ID        NVARCHAR2(64),
    CARD_NUMBER  NVARCHAR2(200),
    PRODUCT_NAME NVARCHAR2(200),
    PRODUCT_ID   NVARCHAR2(200)
);
comment on table BIZ_XM_BM_CARD_NUMBER is '项目报名和学习卡绑定关联表';
comment on column BIZ_XM_BM_CARD_NUMBER.ID is '主键';
comment on column BIZ_XM_BM_CARD_NUMBER.BM_ID is '报名id';
comment on column BIZ_XM_BM_CARD_NUMBER.CARD_NUMBER is '学习卡号';
comment on column BIZ_XM_BM_CARD_NUMBER.PRODUCT_NAME is '产品名称';
comment on column BIZ_XM_BM_CARD_NUMBER.PRODUCT_ID is '产品id';
create unique index BM_ID_CARD_NUMBER_UINDEX on BIZ_XM_BM_CARD_NUMBER (BM_ID, CARD_NUMBER);

ALTER TABLE BIZ_XM ADD OPEN_STUDY_TYPE NVARCHAR2(20);
COMMENT ON COLUMN BIZ_XM.OPEN_STUDY_TYPE IS '第三方学习平台类型';

ALTER TABLE BIZ_XM ADD IS_ALLOW_MULTI_CHOOSE_COURSE INTEGER;
COMMENT ON COLUMN BIZ_XM.IS_ALLOW_MULTI_CHOOSE_COURSE IS '是否允许报名多门课程';

update BIZ_XM set IS_ALLOW_MULTI_CHOOSE_COURSE = 1;